<?php
/**
 * Class representing logic of which Web Hosting links should be shown.
 * 
 * @package LegacyCodebase
 * <AUTHOR> <<EMAIL>>
 */

/**
 * Class representing logic of which Web Hosting links should be shown.
 *
 * @package LegacyCodebase
 * <AUTHOR> <<EMAIL>>
 */
class ShowHostingLinks
{
    private $advancedWebstatsEnabled;
    private $myWebspaceEnabled;
    private $plusnetHostingEnabled;
    private $tableIndex;
     
    private static $rulesTable = array(
        array(0,0,0,0,1,1,1,1,0,0,0,0,1,1,1,1),
        array(0,0,1,1,0,0,1,1,0,0,1,1,0,0,1,1),
        array(0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1),
    );
     
    private static $actionsTable = array(
        array(0,0,0,0,0,0,1,1,0,0,0,0,0,0,1,1),
        array(0,0,1,1,0,0,1,1,0,0,1,1,0,0,1,1),
        array(0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1),
        array(0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1)
    );

    /**
     * Constructor
     * 
     * @param boolean $advancedWebstatsEnabled Advanced Webstats is enabled
     * @param boolean $myWebspaceEnabled       My webspace is enabled 
     * @param boolean $plusnetHostingEnabled   Plusnet hosting is enabled
     * 
     * @return void
     */
    public function __construct($advancedWebstatsEnabled, $myWebspaceEnabled, $plusnetHostingEnabled)
    {
        $this->advancedWebstatsEnabled = $advancedWebstatsEnabled;
        $this->myWebspaceEnabled = $myWebspaceEnabled;
        $this->plusnetHostingEnabled = $plusnetHostingEnabled;
        $this->setTableIndex();
    }

    /**
     * Determines which index of the rules/actions tables to use
     *
     * @return void
     */
    private function setTableIndex()
    {

        // echo "Inputs: $this->advancedWebstatsEnabled , $this->myWebspaceEnabled, $this->plusnetHostingEnabled";
        for ($i = 0; $i < 16; $i++) {

            if (self::$rulesTable[0][$i] == $this->advancedWebstatsEnabled
            && self::$rulesTable[1][$i] == $this->myWebspaceEnabled
            && self::$rulesTable[2][$i] == $this->plusnetHostingEnabled) {
                break;
            }
             
        }
        $this->tableIndex = $i;
    }

    /**
     * Should we show advanced web stats
     *
     * @return boolean
     */
    public function showAdvancedWebStats()
    {
        return self::$actionsTable[0][$this->tableIndex];
    }

    /**
     * Should we show my webspace
     *
     * @return boolean
     */
    public function showMyWebspace()
    {
        return self::$actionsTable[1][$this->tableIndex];
    }
     
    /**
     * Should we show plusnet hosting
     *
     * @return boolean
     */
    public function showPlusnetHosting()
    {
        return self::$actionsTable[2][$this->tableIndex];
    }

    /**
     * Should we show website settings
     *
     * @return boolean
     */
    public function showWebsiteSettings()
    {
        return self::$actionsTable[3][$this->tableIndex];
    }
}
