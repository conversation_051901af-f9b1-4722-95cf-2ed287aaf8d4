<?php

/**
 * FILE HEADER
 *
 * @package LegacyCodebase
 * <AUTHOR> <<EMAIL>>
 */

/**
 * Validates a parameter against a regular expression dies if no match
 *
 * @param str $function_name the name of the entity which called this function
 * @param str $variable      the value to be validated
 * @param str $expression    the regular expression used to validate the value
 * @param str $variable_name the name of the variable being validated
 *
 * @return bool
 */

function split_pt_validate ($function_name, $variable, $expression, $variable_name = '')
{
    if (!preg_match("/$expression/", $variable)) {
        $e = new Exception("Regex failure");

        error_log(
            "FATAL: $function_name(): parameter [$variable_name] failed validation." .
            " The regex used was /$expression/ and the parameter value was [$variable]." .
            " Stack trace: " . $e->getTraceAsString()
        );

        die("Exiting due to validation failure.  See the error log for more details");
    }

    return true;
}
