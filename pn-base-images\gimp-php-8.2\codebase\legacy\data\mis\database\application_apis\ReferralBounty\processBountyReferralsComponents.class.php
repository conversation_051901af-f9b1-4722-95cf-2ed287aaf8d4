<?php

	/**
	 * processBountyReferralsComponents
	 *
	 * @uses processBountyReferrals
	 * <AUTHOR> <<EMAIL>>
	 * @most functions reused from previous Bounty Referral Script created by <PERSON><PERSON> <m<PERSON><PERSON><PERSON><PERSON>@plus.net>
	 * @version $id$
	 */

	require_once '/local/data/mis/database/application_apis/ReferralBounty/processBountyReferrals.class.php';
	
	class processBountyReferralsComponents extends processBountyReferrals
	{
		var $intServiceComponentId;	

		function __construct($intSpecialOfferId=0, $intServiceId=0, $intServiceComponentId=0) 
		{
			parent::__construct($intSpecialOfferId, $intServiceId);
			$this->intServiceComponentId = $intServiceComponentId;
		}

		/**
		 * findReferralsToProcess
		 * gets details of a non-processed referred customers active and sigend up with the given serviceId
		 * 
		 * @access public
		 * @return array of referral to process 
		 */

		function findReferralsToProcess()
		{
			$dbhConnection = get_named_connection_with_db('userdata_reporting');
			$intSpecialOfferId = mysql_real_escape_string($this->intSpecialOfferId, $dbhConnection['handle']);
			$intServiceId = mysql_real_escape_string($this->intServiceId, $dbhConnection['handle']);

			$strQuery = "SELECT br.intBountyReferralId AS referral_uid,
			                    br.intReferrerServiceId AS referral_from_sid,
			                    br.intReferredServiceId AS referral_signedup_sid
			               FROM userdata.tblBountyReferrals br
			               LEFT JOIN userdata.tblReferralsProcessed trp
			                 ON br.intBountyReferralId = trp.intBountyReferralId AND intSpecialOfferId = '$intSpecialOfferId' 
			              INNER JOIN userdata.services ser
			                 ON br.intReferredServiceId = ser.service_id
			              INNER JOIN userdata.services ser1
			                 ON br.intReferrerServiceId = ser1.service_id
			              INNER JOIN products.visp_config vc
			                 ON vc.isp = ser.isp
			              WHERE trp.intBountyReferralId is NULL
			                 AND ser.status = 'active'
			                 AND vc.short_name IN ('PlusNet', 'Madasafish')
			                 AND ser.service_id = '$intServiceId'";

			$resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection, 'query failed on');
			
			$arrReferrals = PrimitivesResultsAsArrayGet($resResult);

			return $arrReferrals;
		} // end function 

		/**
		 * isComponentActiveForServiceId
		 * checks if the given component is an active component of the gived service 
		 *
		 * $param int $intServiceId
		 * $param int $intComponentId
		 * @access public
		 * @return bool
		 * true if it is an active component false if it's not
		 */
		function isComponentActiveForServiceId($intServiceId, $intComponentId)
		{	
		
			if($intServiceId > 0 && $intComponentId > 0)
			{	
				$dbhConnection = get_named_connection_with_db('product_reporting');
				$intServiceId = mysql_real_escape_string($intServiceId, $dbhConnection['handle']);
				$intComponentId = mysql_real_escape_string($intComponentId, $dbhConnection['handle']);
				$strQuery = '	SELECT component_id ' .
				            "		FROM userdata.components where service_id = '$intServiceId' " .
				            "			AND component_type_id = '$intComponentId' " .
				            "			AND status = 'active' LIMIT 1";
				$resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection, 'query failed on', false);
				$intNumber = PrimitivesNumRowsGet($resResult);
				if(1 === $intNumber)
				{
					return true;
				} else
				{
					return false;
				}
			} else 
			{
			return false;
			}	
		}

		/**
		 * findRefundValueForSid 
		 * It is getting all components which are represented in the offer, checking if they are active for the service id. If they are, value is added
		 * 
		 * @param int $intServiceId 
		 * @access private
		 * @return mixed - FALSE = error, array = details of referrer/referred refund payments
		 */
		protected function findRefundValueForSid($intServiceId) 
		{
			
			$dbhConnection = get_named_connection_with_db('product_reporting');
			$intServiceId = mysql_real_escape_string($intServiceId, $dbhConnection['handle']);
			$intServiceComponentId = mysql_real_escape_string($this->intServiceComponentId, $dbhConnection['handle']);
			$intSpecialOfferId = mysql_real_escape_string($this->intSpecialOfferId, $dbhConnection['handle']);
			$strQuery = "SELECT
			                IFNULL(rcb.decValue, 0) AS decReferralValue,
			                IFNULL(ref.decValue, 0) AS decReferredValue
			             FROM
			                products.tblReferralComponentBounty rcb
			             INNER JOIN products.tblSpecialOffer sp
			                ON sp.intSpecialOfferId = rcb.intSpecialOfferId
			             INNER JOIN userdata.components c
			                ON c.component_type_id = rcb.intServiceComponentId
			             INNER JOIN userdata.services s
			                ON s.service_id = c.service_id
			             LEFT JOIN products.tblReferredComponentBounty AS ref
			                ON ref.intReferralComponentBountyId = rcb.intReferralComponentBountyId
			             WHERE s.service_id = '{$intServiceId}'
			                AND c.status = 'active'
			                AND sp.intSpecialOfferId = '{$intSpecialOfferId}'
			                AND rcb.intServiceComponentId = '{$intServiceComponentId}'";

			$resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection, 'query failed on ' . __METHOD__, false);
				 
			if(false === $resResult) 
			{
				return false;
			}


			$arrReturn = PrimitivesResultsAsArrayGet($resResult);

			if (empty($arrReturn)) {

				// not an error; there's just no refunds available for this component

				return array(
					'decReferralValue' => 0,
					'decReferredValue' => 0
				);
			}


			return $arrReturn[0];
		}

	}

