<?php
	/*
	+----------------------+-----------------------------+-----------------------------------+-----------+--------------+--------+---------------------+----------+
	| service_component_id | name                        | description                       | available | date_created | db_src | time_stamp          | isp      |
	+----------------------+-----------------------------+-----------------------------------+-----------+--------------+--------+---------------------+----------+
	|             00000572 | Waitrose Webspace           | Waitrose Webspace 100MB           | Yes       | 2008-04-14   | CBla   | 2008-04-14 11:43:36 | waitrose |
	|             00000574 | Waitrose Webmail            | Waitrose Webmail                  | Yes       | 2008-04-14   | CBla   | 2008-04-14 11:43:36 | waitrose |
	|             00000573 | Greenbee Webspace           | Greenbee Webspace 100MB           | Yes       | 2008-04-14   | CBla   | 2008-04-14 11:43:36 | greenbee |
	|             00000575 | Greenbee Webmail            | Greenbee Webmail                  | Yes       | 2008-04-14   | CBla   | 2008-04-14 11:43:36 | greenbee |
	+----------------------+-----------------------------+-----------------------------------+-----------+--------------+--------+---------------------+----------+
	*/

	configDefineIfMissing('COMPONENT_WAITROSE_WEBMAIL',  574);
	configDefineIfMissing('COMPONENT_GREENBEE_WEBMAIL',  575);
	configDefineIfMissing('COMPONENT_WAITROSE_WEBSPACE', 572);
	configDefineIfMissing('COMPONENT_GREENBEE_WEBSPACE', 573);

	$global_component_configurators[COMPONENT_WAITROSE_WEBSPACE] = "config_bv_webspace_configurator";
	$global_component_configurators[COMPONENT_GREENBEE_WEBSPACE] = "config_bv_webspace_configurator";
	$global_component_configurators[COMPONENT_WAITROSE_WEBMAIL]  = 'config_bv_webmail_configurator';
	$global_component_configurators[COMPONENT_GREENBEE_WEBMAIL]  = 'config_bv_webmail_configurator';

	require_once '/local/data/mis/database/class_libraries/Brightview/BVDB.class.php';
	require_once '/local/data/mis/database/database_libraries/components/MAAF/Webspace.class.php';
	require_once '/local/data/mis/database/database_libraries/components/MAAF/Webmail.class.php';
	require_once '/local/data/mis/database/database_libraries/components/Exception/InvalidSoulstoneEntryException.class.php';

	/**
	 * Legacy configurator wrapper for webmail
	 *
	 * <AUTHOR> Kurylowicz <<EMAIL>>
	 *
	 * @param integer $intComponentID
	 * @param string $strAction
	 *
	 * @return bool
	 */
	function config_bv_webmail_configurator($intComponentID, $strSignal)
	{
		try {

			$objConfigurator = new Config_bv_soulstone_configurator($intComponentID);
			$objConfigurator->handleWebmail($strSignal);
		}
		catch (Exception $objException) {

			error_log(basename(__FILE__)  . ':' . $objException->getMessage());
			return false;
		}

		return true;
	}

	/**
	 * Legacy configurator wrapper for webspace
	 *
	 * <AUTHOR> Kurylowicz <<EMAIL>>
	 *
	 * @param integer $intComponentID
	 * @param string $strAction
	 *
	 * @return bool
	 */
	function config_bv_webspace_configurator($intComponentID, $strSignal)
	{
		try {

			$objConfigurator = new Config_bv_soulstone_configurator($intComponentID);
			$objConfigurator->handleWebspace($strSignal);
		}
		catch (Exception $objException) {

			error_log(basename(__FILE__)  . ':' . $objException->getMessage());
			return false;
		}

		return true;
	}

	/**
	 * Main configurator class
	 *
	 * <AUTHOR> Kurylowicz <<EMAIL>>
	 */
	class Config_bv_soulstone_configurator
	{
		private $intComponentId = 0;

		private $arrComponentDetails = array();

		private $arrUserdata = array();

		private $strRealm = '';

		private $strUsername = '';

		private $objMailBox;

		private $objWebspace;

		public static function getForService($intServiceId)
		{
			$intServiceId = (int)$intServiceId;
			$arrConfigurators = array();

			if (true == is_numeric($intServiceId) && 0 <> $intServiceId) {

				$arrCriteria = array('service_id' => $intServiceId,
			                  	   'type'       => array(COMPONENT_WAITROSE_WEBMAIL, COMPONENT_GREENBEE_WEBMAIL, COMPONENT_WAITROSE_WEBSPACE, COMPONENT_GREENBEE_WEBSPACE),
			                  	   'status'     => array('unconfigured', 'queued-activate', 'active')
			                 		   );

				$arrComponents = userdata_component_find($arrCriteria, '', '');

				foreach ($arrComponents as $arrComponent)
				{

					try {

						$arrConfigurators[] =  new Config_bv_soulstone_configurator($arrComponent['component_id']);
					}
					catch (Exception $objException) {

						// Do nothing
						error_log(__METHOD__ . ': ' . $objException->getMessage());
					}
				}
			}

			return $arrConfigurators;
		}

		/**
		 * Constructor
		 *
		 * @param integer $intComponentId
		 */
		public function __construct($intComponentId = 0)
		{
			$this->intComponentId = $intComponentId;
			$this->arrComponentDetails = userdata_component_get($intComponentId);

			$this->arrUserdata = userdata_service_get($this->arrComponentDetails['service_id'], true);

			// If the service status is 'queued-destroy', then the business actor entries
			// will be set as inactive, so we will not be able to fetch the realm.
			$bolIsActive = $this->arrUserdata['status'] == 'queued-destroy' ? false : true;

			$this->strRealm = userdataGetFrameworkAuthRealm($this->arrComponentDetails['service_id'], $this->arrUserdata['username'], $bolIsActive);
			$this->strUsername = $this->arrUserdata['username'];

			// Validate data
			if (!$this->strUsername) {

				throw new Exception('Could not retrive username for service id:'.$this->arrComponentDetails['service_id'].' and username: '.$this->arrUserdata['username']);
			}

			if(!$this->strRealm) {

				throw new Exception('Could not retrive Realm for service id:'.$this->arrComponentDetails['service_id'].' and username: '.$this->arrUserdata['username']);
			}
		}

		public function handle($strSignal)
		{
			$intComponentType = $this->arrComponentDetails['component_type_id'];
			if (COMPONENT_WAITROSE_WEBMAIL == $intComponentType || COMPONENT_GREENBEE_WEBMAIL == $intComponentType) {

				return $this->handleWebmail($strSignal);
			}
			elseif(COMPONENT_WAITROSE_WEBSPACE == $intComponentType || COMPONENT_GREENBEE_WEBSPACE == $intComponentType) {

				return $this->handleWebspace($strSignal);
			}

			return false;
		}

		/**
		 * Return current mailbox
		 *
		 * @return BrightViewMail
		 */
		private function getMailBox()
		{
			if (true == is_null($this->objMailBox)) {

				$this->objMailBox = BrightViewMail::getMail($this->arrUserdata['username'], $this->strRealm);

				if (FALSE === $this->objMailBox instanceof BrightViewMail) {

					throw new Exception("There is no mailbox for {$this->arrUserdata['username']} {$this->strRealm}");
				}
			}

			return $this->objMailBox;
		}

		/**
		 * Validate current mailbox
		 *
		 */
		private function validateSoulstoneEntry()
		{
			$objCurrentAccount = Brightview_Account::findByServiceId($this->arrUserdata['service_id']);

			if (false == $objCurrentAccount) {

				if (false == empty($this->intComponentId)) {

					userdata_component_set_status($this->intComponentId, 'queued-activate');
				}

				throw new InvalidSoulstoneEntryException('No entry in Soulstone database found (master mailbox), can\'t configure component.');
			}
		}

		/**
		 * Handle configurator signals for soulstone webmail
		 *
		 * @param string $strAction
		 */
		public function handleWebmail($strSignal)
		{
			try {
				$this->validateSoulstoneEntry();
			} catch (InvalidSoulstoneEntryException $e) {
				if (! ($strSignal == 'auto_destroy' && $this->arrUserdata['status'] == 'queued-destroy')) {
					// Problem: 63885 If the service is 'queued-destroy', and we are trying to destroy the webmail
					// component, then we do not need a valid Soulstone entry. We can set the component status to
					// destroyed, which is done using the configure method
					throw $e;
				}
			}

			switch($strSignal)
			{
				case "auto_refresh":
				case "auto_configure":

					if ('queued-destroy' == $this->arrComponentDetails['status']) {

						MAAF_CProduct_Webmail::configure($this->intComponentId, 'auto_destroy');
						return true;
					}

					if('queued-deactivate' == $this->arrComponentDetails['status'] || 'queued-deconfigure' == $this->arrComponentDetails['status']) {

						MAAF_CProduct_Webmail::configure($this->intComponentId, 'auto_disable');
						return true;
					}

					if ('active' <> $this->arrComponentDetails['status'] && 'active' == $this->arrUserdata['status']) {

						MAAF_CProduct_Webmail::configure($this->intComponentId, 'auto_enable');
						return true;
					}

					if ('active' == $this->arrComponentDetails['status'] && 'destroyed' == $this->arrUserdata['status']) {

						MAAF_CProduct_Webmail::configure($this->intComponentId, 'auto_destroy');
						return true;
					}

					// Don't touch active or destroyed component
					if ('active' == $this->arrComponentDetails['status'] ) {

						MAAF_CProduct_Webmail::configure($this->intComponentId, 'auto_refresh');
						return true;
					}

					if('destroyed' == $this->arrComponentDetails['status']) {

						return true;
					}

					// By default set to queued-activate
					userdata_component_set_status($this->intComponentId, 'queued-activate');

					return true;

					break;

				case "auto_disable":

						MAAF_CProduct_Webmail::configure($this->intComponentId, 'auto_disable');

						return true;

					break;

		    		case "auto_enable":
					MAAF_CProduct_Webmail::configure($this->intComponentId, 'auto_enable');
		    			return true;

					break;

		    		case "auto_destroy":

		    			MAAF_CProduct_Webmail::configure($this->intComponentId, 'auto_destroy');

		    			return true;

					break;
			}

			return false;
		}

		private function getWebQuota()
		{
			// Default is 100M
			return 100;
		}

		/**
		 * Enable FTP soulstone flag
		 *
		 * <AUTHOR> Sourkova <<EMAIL>>
		 */
		public function enableFtp()
		{
			$objPDOStatement = BVDB::db()->prepare('UPDATE passwd SET status="active", web_quota = ' . $this->getWebQuota() . ', options=options | ? WHERE username = ? and realm = ?');
			$objPDOStatement->execute(array(MAAF_Component_Webspace::FTP_FLAG, $this->strUsername, $this->strRealm));
		}

		/**
		 * Disable FTP soulstone flag
		 *
		 * <AUTHOR> Sourkova <<EMAIL>>
		 */
		public function disableFtp()
		{
			$objPDOStatement = BVDB::db()->prepare('UPDATE passwd SET options=options & ~? WHERE username = ? and realm = ?');
			$objPDOStatement->execute(array(MAAF_Component_Webspace::FTP_FLAG, $this->strUsername, $this->strRealm));
		}

		/**
		 * Handle configurator signals for soulstone webmail
		 *
		 * @param string $strAction
		 */
		public function handleWebspace($strSignal)
		{
			$this->validateSoulstoneEntry();

			switch($strSignal)
			{
				case "auto_refresh":
				case "auto_configure":

					if ('queued-destroy' == $this->arrComponentDetails['status']) {

						return $this->handleWebspace('auto_destroy');
					}

					if('queued-deactivate' == $this->arrComponentDetails['status'] || 'queued-deconfigure' == $this->arrComponentDetails['status']) {

						return $this->handleWebspace('auto_disable');
					}

					if ('active' <> $this->arrComponentDetails['status'] && 'active' == $this->arrUserdata['status']) {

						return $this->handleWebspace('auto_enable');
					}

					if ('active' == $this->arrComponentDetails['status'] && 'destroyed' == $this->arrUserdata['status']) {

						return $this->handleWebspace('auto_destroy');
					}

					// Don't touch active or destroyed component
					if ('active' == $this->arrComponentDetails['status'] || 'destroyed' == $this->arrComponentDetails['status']) {

						return true;
					}

					// By default set to queued-activate
					userdata_component_set_status($this->intComponentId, 'queued-activate');

					return true;

					break;

		    		case "auto_disable":

		    			$this->disableFtp();
		    			userdata_component_set_status($this->intComponentId, 'deactive');

		    			return true;

					break;

		    		case "auto_enable":

		    			$this->enableFtp();
		    			userdata_component_set_status($this->intComponentId, 'active');

		    			return true;

					break;

		    		case "auto_destroy":

		    			$this->disableFtp();
		    			userdata_component_set_status($this->intComponentId, 'destroyed');

		    			return true;

					break;
			}

			return false;
		}


	}
