<?php
/**
 * Address base class
 * 
 * @package    Core
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <a<PERSON><PERSON><PERSON><PERSON>@plus.net>
 * 
 * @copyright  2008 PlusNet
 * @version    CVS: $Id: Address.class.php,v 1.2 2008-04-08 04:43:14 a<PERSON><PERSON>owicz Exp $
 * @since      File available since 18/02/2007
 */

/**
 * User base class
 * 
 * @package    Core
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <aku<PERSON>@plus.net>
 * @copyright  2008 PlusNet
 */
class Core_Address
{
	/**
	 * @access private
	 * @var Core_AddressDao
	 */
	private $objDao = null;

	const STATUS_ACTIVE   = 'active';
	const STATUS_INACTIVE = 'inactive';

	const TYPE_DELIVERY   = 'DELIVERY';

	/**
	 * Default constructor
	 * 
	 * When <i>$intAddressId</i> provided it initializes 
	 * {@link Core_Address::$objDao} DAO
	 * 
	 * @access public
	 * <AUTHOR> <<EMAIL>>
	 * <AUTHOR> <aku<PERSON><PERSON><PERSON>@plus.net>
	 * 
	 * @uses Core_AddressDao::get()
	 * 
	 * @param int $intAddressId Address ID
	 * @param string $strTransactionName
	 */
	public function __construct($intAddressId = null, $strTransactionName = Db_Manager::DEFAULT_TRANSACTION)
	{
		if (isset($intAddressId))
		{
			$this->setDao(Core_AddressDao::get($intAddressId, $strTransactionName));
		}
	}

	/**
	 * Takes care of all calls to setters and getters of DAO object
	 *  
	 * @access public
	 *  
	 * @uses Core_Address::$objDao
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * <AUTHOR> Borodaenko" <<EMAIL>>
	 * 
	 * @param string    $strMethod Method name
	 * @param array     $arrParams Params passed to method called
	 * 
	 * @throws Exception
	 */	
	public function __call($strMethod, $arrParams)
	{
		if(preg_match('/^(get|set)/', $strMethod)) {

			return call_user_func_array(array($this->objDao, $strMethod), $arrParams);
		}

		throw new Exception("Method does not exist: ".get_class($this).'::'.$strMethod);
	}

	/**
	 * Calls write method of DAO object
	 *  
	 * @access public
	 *  
	 * @uses Core_Address::$objDao
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 */		
	public function write()
	{
		return $this->objDao->write();
	}

	/**
	 * Sets {@link Core_Address::$objDao} DAO
	 * 
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @param Core_IAddressDao $objCoreAddressDao
	 */
	public function setDao(Core_IAddressDao $objCoreAddressDao)
	{
		$this->objDao = $objCoreAddressDao;
	}

	/**
	 * Checks if address is a delivery address
	 * 
	 * @access public
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @uses Core_AddressDao::getTypeHandle()
	 * @uses Core_Address::TYPE_DELIVERY
	 * 
	 * @return bool
	 */
	public function isDeliveryAddress() {

		$strAddressType = $this->getTypeHandle(); 

		return ($strAddressType == self::TYPE_DELIVERY);
	}
}
