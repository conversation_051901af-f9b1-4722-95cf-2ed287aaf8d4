<?php

///////////////////////////////////////////////////////////////////////////
// ADSL Provisioning System Library
//
///////////////////////////////////////////////////////////////////////////
//
//  Misc Functions
//  ---------
//  ADSLStripSlashes
//  ADSLPerformVispCheck
//  ADSLSendActivationEmail
//
//  Read Functions
//  ---------
//  ADSLGetSettingHistory
//  ADSLGetConfigurationValues
//  ADSLProvisionInstallDiaryStatusGet
//  ADSLProvisionInstallDiaryStatusGetByTag
//  ADSLProvisionMasterStatusGet
//  ADSLProvisionStatusGet
//  ADSLProvisionStatusGetByMasterStatus
//  ADSLProvisionSubStatusGet
//  ADSLProvisionSubStatusGetByStatus
//  ADSLProvisionSubStatusGetByTag
//  ADSLProvisionSubStatusGetByErrorText
//  ADSLProvisionProcessGet
//  ADSLProvisionSubStatusGetForArray
//  ADSLProvisionAccountLastTouched
//  ADSLProvisionOrderTypeSubStatusGetByKey
//  ADSLProvisionXMLErrorGetByKey
//  ADSLProvisionXMLErrorGetByCode
//  ADSLProvisionToolGroupGet
//  ADSLProvisionToolGet
//  ADSLProvisionToolGetWithGroups
//  ADSLProvisionToolGetByGroup
//  ADSLProvisionToolGetByWorkplaceURL
//  ADSLProvisionToolGetByPortalURL
//  ADSLProvisionSubStatusToolsGet
//  ADSLProvisionHistoryGet
//  ADSLProvisionOutstandingActionButtonsGet
//  ADSLProvisionInstallDiaryButtonsGet
//  ADSLProvisionOutstandingActionsGet
//  ADSLProvisionAvailableActionsGet
//  ADSLProvisionIsActionOutstanding
//  ADSLProvisionStatusesGetAffected
//  ADSLIsISDNConversion
//  ADSLIsLowSpeedProduct
//  ADSLWorkLogIsAccountBeingWorkedOn
//  ADSLWorkLogNumberOfTimesAccountHasBeenWorkedOn
//  ADSLGetLowerSpeedProduct
//  ADSLProvisionGetStatusesAndGroup
//  ADSLGetTimeSlots
//  adslGetAllHardwareComponents
//  ADSLGetCeaseType
//  ADSLGetCeaseLog
//  ADSLGetLatestCeaseLog
//  ADSLGetCeaseReport
//  ADSLGetCeaseReportAccountTypes
//  ADSLIsUserMigratingISP
//
//  Write Functions
//  ---------
//  ADSLProvisionInstallDiaryStatusSet
//  ADSLProvisionMasterStatusSet
//  ADSLProvisionStatusSet
//  ADSLProvisionSubStatusSet
//  ADSLAutoChangeStatus
//  ADSLProvisionXMLErrorSet
//  ADSLProvisionToolGroupSet
//  ADSLProvisionToolSet
//  ADSLProvisionActionAdd
//  ADSLProvisionActionDealtWith
//  ADSLProvisionActionDealtWithByTool
//  ADSLGetOpenSubStatuses
//  ADSLClearActions
//  ADSLProvisionHistoryAdd
//  ADSLProvisionSubStatusToolsSet
//  ADSLUpdateConfigurationValue
//  ADSLlogPreRegDetails
//  ADSLPreRegPaymentDetails
//  ADSLPreRegHardwareDetails
//  ADSLPreRegDetailsUpdate
//  ADSLWorkLogUpdate
//  ADSLWorkLogTidyUp
//  ADSLWorkLogStopWorkByUser
//  adslHardwareStockLevelAlter
//  ADSLPreregRecordUserDecision
//  ADSLAddCeaseLog
//  ADSLCancelCeaseLog
//
///////////////////////////////////////////////////////////////////////////

require_once(SQL_PRIMITIVES_LIBRARY);
require_once(ADSL_ACCESS_LIBRARY);
require_once(USERDATA_ACCESS_LIBRARY);
require_once(TICKETS_ACCESS_LIBRARY);

// Addition for Migration Promotion Project (PIT Project 600) Sept 2005

if(!function_exists('remoteinet_ADSLSendActivationEmail'))
{
    require_once "/local/data/mis/database/database_libraries/migration-promotion-access.inc";
}

$database_array = array('PN' => array('userdata' => 'userdata',
                                    'adsl'     => 'adsl'),
                        'AF' => array('userdata' => 'affinity',
                                    'adsl'     => 'affinity'),
                        'CD' => array('userdata' => 'charitydays',
                                    'adsl'     => 'charitydays'));


function ADSLStripSlashes($objMixed)
{
    if(is_array($objMixed)===true)
    {
        if(count($objMixed) > 0)
        {
            foreach($objMixed as $unkKey => $unkValue)
            {
                if(is_array($unkValue) === true)
                {
                    $unkValue           = ADSLStripSlashes($unkValue);
                    $objMixed[$unkKey] = $unkValue;
                }
                else
                {
                    $objMixed[$unkKey] = stripslashes($unkValue);
                }
            }
        }
    }
    else
    {
        $objMixed = stripslashes($objMixed);
    }
    return $objMixed;
}



///////////////////////////////////////////////////////////////////////////
//  Function   :  ADSLPerformVispCheck
//  Purpose    :  Do some checking to find service_id, etc for an account off of the ParB report_error
//  Arguements :  $strTelephone
//                $strCustomer
//                $usiServiceId
//                $strProblemText
//  Returns    :  $arrADSLConfigDetails
///////////////////////////////////////////////////////////////////////////
function ADSLPerformVispCheck(&$strTelephone, $strCustomer, $usiServiceId, &$strProblemText)
{
    global $database_array;

    $strCustomer = addslashes($strCustomer);
    $strTelephone     = addslashes($strTelephone);
    $usiServiceId    = addslashes($usiServiceId);

    $arrCustomer = explode(' ' , $strCustomer);

    // if there is no 0 at the beginning of the phone number, add one
    if(substr($strTelephone, 0, 1) != '0')
    {
        $strTelephone = '0' . $strTelephone;
    }

    // if there is a MR* or a MS* or a DR* in the first element of the array, then has a MR or MRS in front of the name
    if(stristr($arrCustomer[0], 'MR') || stristr($arrCustomer[0], 'MS') || stristr($arrCustomer[0], 'DR'))
    {
        $forename = $arrCustomer[1];

        unset($arrCustomer[1]);
        unset($arrCustomer[0]);
        $surname = implode(' ', $arrCustomer);
    }
    else // there is no MR or MRS
    {
        $forename = $arrCustomer[0];

        unset($arrCustomer[0]);
        $surname = implode(' ', $arrCustomer);

    }

    // If the returned service ID has one of the database strings in front of it
    // (i.e. AF000000 = an affinity SID) then connect to the correct database straight away
    if(substr($usiServiceId, 0, 2) == 'PN')
    {
        // service id is everything but the first 2 characters
        $usiServiceId   = substr($usiServiceId, 2, strlen($usiServiceId));

    }

    $conn_id = get_named_connection('userdata');

    $forename = strtolower($forename);
    $surname = strtolower($surname);

    $query = "SELECT s.service_id, s.cli_number as telephone, u.forenames, u.surname
                FROM services s
          INNER JOIN users u
                  ON s.user_id = u.user_id
               WHERE s.service_id = '$usiServiceId'
                 AND s.cli_number = '$strTelephone'";

    $result = mysql_query($query, $conn_id)
        or report_error(__FILE__, __LINE__, mysql_error($conn_id));

    if(mysql_num_rows($result) == 1)
    {
        $details_array = mysql_fetch_assoc($result);
    }
    else
    {
        //The CLI number may be in service_cli_numbers
        $query = "SELECT s.service_id, s.cli_number as telephone, u.forenames, u.surname
                    FROM services s
              INNER JOIN users u
                      ON s.user_id = u.user_id
              INNER JOIN service_cli_numbers scn
                      ON s.service_id = scn.service_id
                   WHERE s.service_id = '$usiServiceId'
                     AND scn.cli_number = '$strTelephone'";

        $result = mysql_query($query, $conn_id)
                or report_error(__FILE__, __LINE__, mysql_error($conn_id));

        if(mysql_num_rows($result) == 1)
        {
            $details_array = mysql_fetch_assoc($result);
        }
    }

    // if no results returned from plusnet DB
    if(!isset($details_array))
    {
        return 'not_in_plusnet_database';
    }
    else
    {
        return ADSLStripSlashes($details_array);
    }
}



///////////////////////////////////////////////////////////////////////////
//  Function   :  ADSLGetSettingHistory
//  Purpose    :  returns the history of changes for a setting
//  Arguements :  $intADSLConfigurationSettingId - The setting we want the history for
//                $intCount                      - How much history we want
//  Returns    :  $arrHistory
///////////////////////////////////////////////////////////////////////////

/**
 * Log a regrade event
 *
 * @param int     $intServiceId           Service Id
 * @param int     $intADSLRegradeID       Regrade Id
 * @param string  $strRegradeStatusHandle Regrade Status
 * @param boolean $bolCompleted           Completed
 * @param string  $strComment             Comment
 *
 * @return boolean Update Status
 */
function AdslRegradeLogEvent(
    $intServiceId,
    $intADSLRegradeID,
    $strRegradeStatusHandle,
    $bolCompleted = false,
    $strComment = ''
) {
    // Get ID for Handle
    $intRegradeStatusID = AdslRegradeStatusHandleToID($strRegradeStatusHandle);

    if (!$intRegradeStatusID) {

        return false;
    }

    $arrConnection = get_named_connection_with_db('adsl');

    $strDatePerformedClause = '';
    if ($bolCompleted) {
        $strDatePerformedClause = ', dtmPerformed = if(dtmPerformed is null,now(),dtmPerformed)';
    }


    // Update current status
    $strQuery = 'UPDATE tblADSLRegrade set intRegradeStatusID = %d '
              . $strDatePerformedClause
              . ' WHERE intServiceID = %d AND intADSLRegradeID = %d ';

    $strQuery = sprintf(
        $strQuery,
        mysql_real_escape_string($intRegradeStatusID, $arrConnection['handle']),
        mysql_real_escape_string($intServiceId, $arrConnection['handle']),
        mysql_real_escape_string($intADSLRegradeID, $arrConnection['handle'])
    );

    $resResult = PrimitivesQueryOrExit($strQuery, $arrConnection, 'get outstanding batches');


    $intADSLRegradeBatchEntryID = 0;
    if ($strRegradeStatusHandle != 'AWAITING_ORDER_RESUBMISSION') {

        // Get active BT batch
        $strQuery = "SELECT intADSLRegradeBatchEntryID FROM tblRegradeBatchEntry ".
                    "WHERE intADSLRegradeID = %d ".
                    "ORDER BY intADSLRegradeBatchEntryID DESC ".
                    "LIMIT 1";

        $strQuery = sprintf(
            $strQuery,
            mysql_real_escape_string($intADSLRegradeID, $arrConnection['handle'])
        );

        $resResult = PrimitivesQueryOrExit($strQuery, $arrConnection, 'get outstanding batches');
        $intADSLRegradeBatchEntryID = PrimitivesResultGet($resResult, 'intADSLRegradeBatchEntryID');
    }

    $strBatchID = '';

    if ($intADSLRegradeBatchEntryID > 0) {

        $strBatchID = sprintf(
            ', intADSLRegradeBatchEntryID = %d ',
            mysql_real_escape_string($intADSLRegradeBatchEntryID, $arrConnection['handle'])
        );
    }

    // Write row to log
    $strQuery = 'INSERT INTO tblADSLRegradeLog '
              .        ' SET intADSLRegradeID = %d, '
              .            ' intRegradeStatusID = %d, '
              .            " vchComment = '%s', "
              .            ' dtmAdded = now() '
              .            $strBatchID;

    $strQuery = sprintf(
        $strQuery,
        mysql_real_escape_string($intADSLRegradeID, $arrConnection['handle']),
        mysql_real_escape_string($intRegradeStatusID, $arrConnection['handle']),
        mysql_real_escape_string($strComment, $arrConnection['handle'])
    );

    $resResult = PrimitivesQueryOrExit($strQuery, $arrConnection, 'get outstanding batches');

    //If there is no commit date stored then store the current date as the regrade is complete
    if ($bolCompleted && defined('PARTNER_ID') && PARTNER_ID == 5) {

        $strQuery ="INSERT IGNORE INTO dbRemoteInternet.tblCommitDate VALUES (%d, DATE(NOW()),  NOW())";
        $strQuery = sprintf($strQuery, mysql_real_escape_string($intServiceId, $arrConnection['handle']));
        PrimitivesQueryOrExit($strQuery, $arrConnection);
    }

    return true;

} // func : AdslRegradeLogEvent


///////////////////////////////////////////////////////////////////////////
//  Function   :  ADSLGetSettingHistory
//  Purpose    :  returns the history of changes for a setting
//  Arguements :  $intADSLConfigurationSettingId - The setting we want the history for
//                $intCount                      - How much history we want
//  Returns    :  $arrHistory
///////////////////////////////////////////////////////////////////////////
function ADSLGetSettingHistory($intADSLConfigurationSettingId, $intCount='10')
{
    $intADSLConfigurationSettingId = addslashes($intADSLConfigurationSettingId);
    $intCount                      = addslashes($intCount);

    $dbhConnection = get_named_connection_with_db('common_adsl');

    $strQuery = '  SELECT * '
            .'    FROM tblADSLConfigurationChangeLog '
            ."   WHERE intADSLConfigurationSettingId=$intADSLConfigurationSettingId "
            .'ORDER BY utxTimestamp DESC '
            ."   LIMIT $intCount ";

    $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

    $arrHistory = PrimitivesResultsAsArrayGet($resResult);

    return ADSLStripSlashes($arrHistory);

}  //  END function ADSLGetSettingHistory

/**
 * Returns adsl configuration values for a given key.  Data is cached (27 entries)
 *  - an integer which maps to intADSLConfigurationSettingId
 *  - a string which maps to vchADSLConfigurationKey
 *
 * NOTE: if a config "key" is not passed to the script, all values will be returned in an array,
 * indexed by intADSLConfigurationSettingId
 * Returns an empty array if the key cannot be found
 *
 * @param str  $vchADSLConfigurationKey the configuration key
 * @param bool $bolValueOnly            indicates that the 'vchADSLConfigurationValue' value only should be returned
 *
 * @return mix
 */
function ADSLGetConfigurationValues($vchADSLConfigurationKey = '', $bolValueOnly = false)
{
    static $dataCache = array();

    if (empty($dataCache)) {
        // ITW and PN both store ADSL config in the dbCommonADSL database
        $query = 'SELECT * FROM dbCommonADSL.tblADSLConfigurationSetting';

        $conn      = get_named_connection_with_db('unprivileged_reporting');
        $resResult = PrimitivesQueryOrExit($query, $conn);

        // Note that (as per above) the dataCache is indexed by intADSLConfigurationSettingId
        $dataCache = PrimitivesResultsAsArrayGet($resResult, 'intADSLConfigurationSettingId');
    }

    if (empty($vchADSLConfigurationKey)) {
        // No filtering required in this case - note that we ignore the $bolValueOnly flag in this scenario
        return $dataCache;
    }

    // The "key" passed to the function can be either the intADSLConfigurationSettingId or vchADSLConfigurationKey value
    // As the cache is indexed by intADSLConfigurationSettingId, we can do a "key" lookup before iterating through the
    // cache to check vchADSLConfigurationKey.
    $retData = false;
    if (is_numeric($vchADSLConfigurationKey) && array_key_exists($vchADSLConfigurationKey, $dataCache)) {
        $retData = $dataCache[$vchADSLConfigurationKey];
    } else {
        foreach ($dataCache as $config) {
            if ($config['vchADSLConfigurationKey'] == $vchADSLConfigurationKey) {
                $retData = $config;
                break;
            }
        }
    }

    // We need to extract and sanitise the value if $bolValueOnly is set
    if ($retData && $bolValueOnly == true) {
        $retData = ADSLStripSlashes($retData['vchADSLConfigurationValue']);
    }

    // The original code returns an empty array if the key was not found
    if (!$retData) {
        $retData = array();
    }
    return $retData;
}

//////////////////////////////////////////////////////////
// Function      ADSLProvisionInstallDiaryStatusGet
// Purpose       Get array of Install Diary Statuses
// Argument      [OPTIONAL] usiADSLInstallDiaryStatusId   Specify Install Diary Status to return
//               [OPTIONAL] strOrderBy                    Order results by field
// Returns       Array of Install Diary Statuses
//////////////////////////////////////////////////////////
function ADSLProvisionInstallDiaryStatusGet($usiADSLInstallDiaryStatusId=0, $strOrderBy = '')
{
    $dbhConnection  = get_named_connection_with_db('common_adsl_reporting');

    $strQuery       = '
SELECT usiADSLInstallDiaryStatusId,
   vchADSLInstallDiaryStatusName,
   vchADSLInstallDiaryStatusTag
FROM tblADSLInstallDiaryStatus';

    if($usiADSLInstallDiaryStatusId > 0)
    {
        $strQuery .= ' WHERE usiADSLInstallDiaryStatusId="'.$usiADSLInstallDiaryStatusId.'"';
    }
    if($strOrderBy != '')
    {
        $strQuery .= ' ORDER BY '.$strOrderBy;
    }

    $resResult      = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Getting install diary status list');
    $arrStatus      = array();
    if($resResult)
    {
        $arrStatus  = ADSLStripSlashes(PrimitivesResultsAsArrayGet($resResult));
    }
    return $arrStatus;
}



//////////////////////////////////////////////////////////
// Function      ADSLProvisionInstallDiaryStatusGetByTag
// Purpose       Get Install Diary Status information by tag
// Argument      vchADSLInstallDiaryStatusTag   tag to search by
// Returns       Array of Install Diary Status
//////////////////////////////////////////////////////////
function ADSLProvisionInstallDiaryStatusGetByTag($vchADSLInstallDiaryStatusTag)
{
    $dbhConnection  = get_named_connection_with_db('common_adsl');
    $strQuery       = "
SELECT usiADSLInstallDiaryStatusId,
   vchADSLInstallDiaryStatusName,
   vchADSLInstallDiaryStatusTag
FROM tblADSLInstallDiaryStatus
WHERE vchADSLInstallDiaryStatusTag = '{$vchADSLInstallDiaryStatusTag}'";

    $resResult      = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Getting install diary status by tag');
    $arrStatus      = false;
    if($resResult)
    {
        $arrStatus  = PrimitivesResultGet($resResult);

        $strStatus  = $arrStatus['vchADSLInstallDiaryStatusName'];
    }
    return ADSLStripSlashes($arrStatus);
}

//////////////////////////////////////////////////////////
// Function      ADSLProvisionMasterStatusGet
// Purpose       Get array of Master Statuses
// Argument      [OPTIONAL] usiADSLProcessMasterStatusId  Specify Master status to return
//               [OPTIONAL] strOrderBy                    Order results by field
// Returns       Array of Master Statuses
//////////////////////////////////////////////////////////
function ADSLProvisionMasterStatusGet($usiADSLProcessMasterStatusId=0, $strOrderBy = '')
{
    $dbhConnection  = get_named_connection_with_db('common_adsl');
    $strQuery       = 'SELECT * FROM vblADSLProcessMasterStatus';
    if($usiADSLProcessMasterStatusId > 0)
    {
        $strQuery .= ' WHERE usiADSLProcessMasterStatusId="'.$usiADSLProcessMasterStatusId.'"';
    }
    if($strOrderBy != '')
    {
        $strQuery .= ' ORDER BY '.$strOrderBy;
    }

    $resResult      = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Getting specific master status');
    $arrMaster      = array();
    if($resResult)
    {
        $arrMaster      = PrimitivesResultsAsArrayGet($resResult);
    }
    return ADSLStripSlashes($arrMaster);
}



//////////////////////////////////////////////////////////
// Function      ADSLProvisionStatusGet
// Purpose       Get array of Statuses
// Argument      [OPTIONAL] usiADSLProcessStatusId  Specify status to return
//               [OPTIONAL] strOrderBy              Order results by field
// Returns       Array of Statuses
//////////////////////////////////////////////////////////
function ADSLProvisionStatusGet($usiADSLProcessStatusId=0, $strOrderBy = '')
{
    $dbhConnection  = get_named_connection_with_db('common_adsl');
    $strQuery       = 'SELECT *
                         FROM vblADSLProcessMasterStatus AS ms
                   INNER JOIN tblADSLProcessStatus AS s
                           ON ms.usiADSLProcessMasterStatusId = s.usiADSLProcessMasterStatusId';
    if ($usiADSLProcessStatusId > 0) {

        $strQuery .= ' WHERE usiADSLProcessStatusId="'.$usiADSLProcessStatusId.'"';
    }

    if ($strOrderBy != '') {

        $strQuery .= ' ORDER BY '.$strOrderBy;
    }

    $resResult      = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Getting specific status');
    $arrStatus      = array();
    if($resResult)
    {
        $arrStatus      = PrimitivesResultsAsArrayGet($resResult);
    }
    return ADSLStripSlashes($arrStatus);
}



//////////////////////////////////////////////////////////
// Function      ADSLProvisionSubStatusGet
// Purpose       Get array of Sub Statuses
// Argument      usiADSLProcessStatusId    Specify status to return
// Returns       Array of Sub Status
//////////////////////////////////////////////////////////
function ADSLProvisionStatusGetByMasterStatus($usiADSLProcessMasterStatusId)
{
    $dbhConnection  = get_named_connection_with_db('common_adsl');
    $strQuery       = 'SELECT *
                         FROM vblADSLProcessMasterStatus AS ms
                   INNER JOIN tblADSLProcessStatus AS s
                           ON ms.usiADSLProcessMasterStatusId = s.usiADSLProcessMasterStatusId
                        WHERE ms.usiADSLProcessMasterStatusId    ="'.$usiADSLProcessMasterStatusId.'"';

    $resResult      = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Getting specific sub-status');
    $arrStatus      = array();
    if($resResult)
    {
        $arrStatus      = PrimitivesResultsAsArrayGet($resResult);
    }
    return ADSLStripSlashes($arrStatus);
}



//////////////////////////////////////////////////////////
// Function      ADSLProvisionSubStatusGet
// Purpose       Get array of Sub Statuses
// Argument      [OPTIONAL] usiADSLProcessSubStatusId  Specify sub status to return
//               [OPTIONAL] strOrderBy                 Order results by field
// Returns       Array of Sub Statuses
//////////////////////////////////////////////////////////
function ADSLProvisionSubStatusGet($usiADSLProcessSubStatusId=0, $strOrderBy = '')
{
    $dbhConnection  = get_named_connection_with_db('common_adsl');
    $strQuery       = 'SELECT *
                         FROM vblADSLProcessMasterStatus AS ms
                   INNER JOIN tblADSLProcessStatus AS s
                           ON ms.usiADSLProcessMasterStatusId = s.usiADSLProcessMasterStatusId
                   INNER JOIN tblSubStatus AS ss
                           ON ss.usiADSLProcessStatusId       = s.usiADSLProcessStatusId';

    if ($usiADSLProcessSubStatusId > 0) {

        $strQuery .= ' WHERE ss.usiADSLProcessSubStatusId="'.$usiADSLProcessSubStatusId.'"';
    }

    if ($strOrderBy != '') {

        $strQuery .= ' ORDER BY '.$strOrderBy;
    }

    $resResult      = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Getting specific sub-status');
    $arrSubStatus   = array();

    if ($resResult) {

        $arrSubStatus = PrimitivesResultsAsArrayGet($resResult);
    }
    return ADSLStripSlashes($arrSubStatus);
}



//////////////////////////////////////////////////////////
// Function      ADSLProvisionSubStatusGet
// Purpose       Get array of Sub Statuses
// Argument      usiADSLProcessStatusId    Specify status to return
// Returns       Array of Sub Status
//////////////////////////////////////////////////////////
function ADSLProvisionSubStatusGetByStatus($usiADSLProcessStatusId, $strOrderBy='')
{
    $dbhConnection  = get_named_connection_with_db('common_adsl');
    $strQuery       = 'SELECT ms.*, s.*, ss.* , dot.vchHandle
                         FROM dbCommonADSL.vblADSLProcessMasterStatus AS ms
                   INNER JOIN dbCommonADSL.tblADSLProcessStatus s
                           ON ms.usiADSLProcessMasterStatusId = s.usiADSLProcessMasterStatusId
                   INNER JOIN dbCommonADSL.tblSubStatus ss
                           ON ss.usiADSLProcessStatusId = s.usiADSLProcessStatusId
                    LEFT JOIN dbCommonADSL.tblDslOrderType AS dot
                           ON dot.intDslOrderTypeId = ss.intDslOrderTypeId
                        WHERE ss.usiADSLProcessStatusId = "'.$usiADSLProcessStatusId.'"';
    if($strOrderBy != '')
    {
        $strQuery .= ' ORDER BY '.$strOrderBy;
    }

    $resResult      = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Getting specific sub-status');
    $arrSubStatus   = array();
    if($resResult)
    {
        $arrSubStatus = PrimitivesResultsAsArrayGet($resResult);
    }
    return ADSLStripSlashes($arrSubStatus);
}



//////////////////////////////////////////////////////////
// Function      ADSLProvisionSubStatusGetByTag
// Purpose       Get array of Sub Status
// Argument      strADSLProcessSubStatusTag  Tag to search on
// Returns       Array of Sub Status
//////////////////////////////////////////////////////////
function ADSLProvisionSubStatusGetByTag($strADSLProcessSubStatusTag)
{
    $dbhConnection  = get_named_connection_with_db('common_adsl');

    $strADSLProcessSubStatusTag = addslashes($strADSLProcessSubStatusTag);

    $strQuery       = 'SELECT *
                         FROM vblADSLProcessMasterStatus AS ms
                   INNER JOIN tblADSLProcessStatus AS s
                           ON ms.usiADSLProcessMasterStatusId = s.usiADSLProcessMasterStatusId
                   INNER JOIN tblSubStatus AS ss
                           ON ss.usiADSLProcessStatusId       = s.usiADSLProcessStatusId
                        WHERE vchADSLProcessSubStatusTag      ="'.$strADSLProcessSubStatusTag.'"';

    $resResult      = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Getting specific sub-status by tag');
    $arrSubStatus   = array();

    if ($resResult) {

        $arrSubStatus = PrimitivesResultGet($resResult);
    }
    return ADSLStripSlashes($arrSubStatus);
}



//////////////////////////////////////////////////////////
// Function      ADSLProvisionSubStatusGetByErrorText
// Purpose       Get array of Sub Status
// Argument      strErrorMessage  Message to search on
// Returns       Array of Sub Status
//////////////////////////////////////////////////////////
function ADSLProvisionSubStatusGetByErrorText($strErrorMessage)
{
    $dbhConnection  = get_named_connection_with_db('common_adsl');

    $strErrorMessage = addslashes($strErrorMessage);

    $strQuery       = 'SELECT *
                        FROM tblSubStatus
                        WHERE vchADSLProcessSubStatusName ="'.$strErrorMessage.'"';

    $resResult      = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Getting specific sub-status by error text');
    $arrSubStatus   = array();
    if($resResult)
    {
        $arrSubStatus = PrimitivesResultGet($resResult);
    }
    return ADSLStripSlashes($arrSubStatus);
}


function ADSLProvisionProcessGet($usiServiceId)
{
    $dbhConnection  = get_named_connection_with_db('common_adsl');
    $usiServiceId   = addslashes($usiServiceId);
    $strQuery       = 'SELECT *
                        FROM tblADSLProcess
                        WHERE usiServiceId  = "'.$usiServiceId.'"
                        AND usiPartnerId  = "'.PARTNER_ID.'"';
    $resResult      = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Getting adsl process for specific service id');
    $arrProcess     = false;
    if($resResult)
    {
        $arrProcess = PrimitivesResultGet($resResult);
        if(count($arrProcess) == 0)
        {
            $arrProcess = false;
        }
    }
    return ADSLStripSlashes($arrProcess);
}



function ADSLProvisionSubStatusGetForArray(&$arrServices, $strSignupType)
{
    $dbhConnection          = get_named_connection_with_db('adsl');
    $dbhConnectionUserdata  = get_named_connection_with_db('userdata');

    $arrNewServices = array();
    $arrReturn      = array();

    // Create a temporary array of install diary statuses for conversion, to reduce the possibility of
    // severly hammering the database.
    $arrInstallDiaryStatus = ADSLProvisionInstallDiaryStatusGet();
    foreach($arrInstallDiaryStatus as $intIndex => $arrStatus)
    {
        $arrInstallDiaryConversion[$arrStatus['vchADSLInstallDiaryStatusTag']] = $arrStatus['usiADSLInstallDiaryStatusId'];
    }
    unset($arrInstallDiaryStatus);

    if (is_array($arrServices))
    {
    foreach($arrServices as $arrService)
    {
        $intServiceId = $arrService['service_id'];
        if(!isset($arrService['service_id']))
        {
            $intServiceId = $arrService['intServiceId'];
        }

        $intOriginalServiceId = $intServiceId;

        $intStatusId           = 0;

        // Firstly, do we have an entry under the new ADSL Process?
        $arrProcess  = ADSLProvisionProcessGet($intServiceId);

        //Removed for problem 11840
/*            if($strSignupType == 'upgrade')
        {
            $strQuery = 'SELECT status
                        FROM install_diary
                        WHERE service_id = "'.ADSLGetInstallDiaryServiceId($intServiceId).'"
                            AND status NOT IN ("preregistered", "waiting_for_cheque")';
        }
        else
        {*/
            $strQuery = 'SELECT status
                        FROM install_diary
                        WHERE service_id = "'.ADSLGetInstallDiaryServiceId($intServiceId).'"';
        //}

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Getting install diary entry');
        $strStatus = PrimitivesResultGet($resResult, 'status');

        if($strStatus != '')
        {
            if(isset($arrInstallDiaryConversion[$strStatus]))
            {
                $intStatusId = $arrInstallDiaryConversion[$strStatus];
            }
            else
            {
                error_log("DEBUG: We don't have an install diary status to convert to for $strStatus, ".$arrService['service_id']." using $intStatusId");
            }

            if(isset($arrReturn[$intStatusId]))
            {
                ++$arrReturn[$intStatusId];
            }
            else
            {
                $arrReturn[$intStatusId] = 1;
            }

            // As per the new requirements, dtmEvent needs to be date since the account was last touched, rather
            // than the date it actually entered this whole process
            $arrNewServices[] = array('intServiceId'   => $intOriginalServiceId,
                                        'dtmEvent'       => ADSLProvisionAccountLastTouched($intOriginalServiceId),
                                        'intStatusId'    => $intStatusId);

            if(isset($arrReturn['total']))
            {
                ++$arrReturn['total'];
            }
            else
            {
                $arrReturn['total'] = 1;
            }
        }
    }
    }
    $arrServices = $arrNewServices;
    return ADSLStripSlashes($arrReturn);
}


//////////////////////////////////////////////////////////
// Function      ADSLProvisionAccountLastTouched
// Purpose       Get time account was last touched
// Argument      usiServiceId   Service Id to search for
// Returns       str of date and time ('yyyy-mm-dd hh:ii:ss')
//////////////////////////////////////////////////////////
function ADSLProvisionAccountLastTouched($usiServiceId)
{
    $dbhConnection = get_named_connection_with_db('common_adsl');
    $usiServiceId  = addslashes($usiServiceId);

    $strQuery       = 'SELECT max(dtmDateTime) AS dtmDateTime
                        FROM tblADSLProcess
                        WHERE usiServiceId = "'.$usiServiceId.'"
                        AND usiPartnerId = "'.PARTNER_ID.'"';
    $resResult      = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Getting date/time account last touched');
    if($resResult)
    {
        $strDateTime = PrimitivesResultGet($resResult, 'dtmDateTime');
    }
    if($strDateTime == '')
    {
        $strDateTime = '0000-00-00 00:00:00';
    }
    return $strDateTime;
}


//////////////////////////////////////////////////////////
// Function      ADSLProvisionOrderTypeSubStatusGetByKey
// Purpose       Get array of response codes
// Argument      [OPTIONAL] intDslOrderTypeSubStatusId  Specify intDslOrderTypeSubStatusId (by unique ID) to return
//               [OPTIONAL] strOrderBy          Order results by field
// Returns       Array of provisioning Code(s)
//////////////////////////////////////////////////////////
function ADSLProvisionOrderTypeSubStatusGetByKey($intDslOrderTypeSubStatusId=0, $strOrderBy = '')
{
    $dbhConnection  = get_named_connection_with_db('common_adsl_reporting');
    $strQuery       = 'SELECT  brcss.intBtResponseCodeSubStatusId, brcss.intSubStatusId, ss.intDslOrderTypeId, brcss.intBtResponseCode, '
            . 'dot.vchDescription as strOrderType, ss.vchADSLProcessSubStatusName '
            . 'FROM tblBtResponseCodeSubStatus AS brcss '
            . 'INNER JOIN tblSubStatus AS ss ON ss.usiADSLProcessSubStatusId = brcss.intSubStatusId '
            . 'LEFT JOIN tblDslOrderType as dot ON dot.intDslOrderTypeId = ss.intDslOrderTypeId  ';

    if($intDslOrderTypeSubStatusId> 0)
    {
        $strQuery .= ' WHERE intBTResponseCodeSubStatusId = "'.$intDslOrderTypeSubStatusId.'"';
    }
    if($strOrderBy != '')
    {
        $strQuery .= ' ORDER BY '.$strOrderBy;
    }

    $resResult      = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Getting XML Error Code');
    $arrErrorCode   = array();
    if($resResult)
    {
        $arrErrorCode = PrimitivesResultsAsArrayGet($resResult);
    }
    return ADSLStripSlashes($arrErrorCode);
}


//////////////////////////////////////////////////////////
// Function      ADSLProvisionXMLErrorGetByKey
// Purpose       Get array of XML Error Codes
// Argument      [OPTIONAL] usiADSLErrorCodeId  Specify error code (by unique ID) to return
//               [OPTIONAL] strOrderBy          Order results by field
// Returns       Array of Error Code(s)
//////////////////////////////////////////////////////////
function ADSLProvisionXMLErrorGetByKey($usiADSLErrorCodeId=0, $strOrderBy = '')
{
    $dbhConnection  = get_named_connection_with_db('common_adsl');
    $strQuery       = 'SELECT * FROM tblADSLErrorCode';

    if($usiADSLErrorCodeId > 0)
    {
        $strQuery .= ' WHERE usiADSLErrorCodeId="'.$usiADSLErrorCodeId.'"';
    }
    if($strOrderBy != '')
    {
        $strQuery .= ' ORDER BY '.$strOrderBy;
    }

    $resResult      = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Getting XML Error Code');
    $arrErrorCode   = array();
    if($resResult)
    {
        $arrErrorCode = PrimitivesResultsAsArrayGet($resResult);
    }
    return ADSLStripSlashes($arrErrorCode);
}



//////////////////////////////////////////////////////////
// Function      ADSLProvisionXMLErrorGetByCode
// Purpose       Get array of XML Error Code
// Argument      vchADSLErrorCode   Specify error code to return
// Returns       Array of Error Code
//////////////////////////////////////////////////////////
function ADSLProvisionXMLErrorGetByCode($vchADSLErrorCode)
{
    $dbhConnection  = get_named_connection_with_db('common_adsl');
    $strQuery       = 'SELECT * FROM tblADSLErrorCode
                        WHERE vchADSLErrorCode = "'.$vchADSLErrorCode.'"';

    $resResult      = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Getting XML Error Code');
    $arrErrorCode   = array();
    if($resResult)
    {
        $arrErrorCode = PrimitivesResultsAsArrayGet($resResult);
    }
    return ADSLStripSlashes($arrErrorCode);
}



//////////////////////////////////////////////////////////
// Function      ADSLProvisionToolGroupGet
// Purpose       Get array of provision tool group
// Argument      usiADSLActionButtonGroupId   Specify group to return
//               strOrderBy                   Order results
// Returns       Array of Tool Group
//////////////////////////////////////////////////////////
function ADSLProvisionToolGroupGet($usiADSLActionButtonGroupId=0, $strOrderBy = '')
{
    $dbhConnection  = get_named_connection_with_db('common_adsl');
    $strQuery       = 'SELECT * FROM vblADSLActionButtonGroup';

    if($usiADSLActionButtonGroupId > 0)
    {
        $strQuery .= ' WHERE usiADSLActionButtonGroupId="'.$usiADSLActionButtonGroupId.'"';
    }
    if($strOrderBy != '')
    {
        $strQuery .= ' ORDER BY '.$strOrderBy;
    }

    $resResult  = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Getting Tool Group');
    $arrGroup   = array();
    if($resResult)
    {
        $arrGroup = PrimitivesResultsAsArrayGet($resResult);
    }
    return ADSLStripSlashes($arrGroup);
}



//////////////////////////////////////////////////////////
// Function      ADSLProvisionToolGet
// Purpose       Get array of provision tool
// Argument      usiADSLActionButtonId   Specify tool to return
//               strOrderBy              Order results
// Returns       Array of Tool(s)
//////////////////////////////////////////////////////////
function ADSLProvisionToolGet($usiADSLActionButtonId=0, $strOrderBy = '')
{
    $dbhConnection  = get_named_connection_with_db('common_adsl');
    $strQuery       = 'SELECT * FROM tblADSLActionButton';

    if($usiADSLActionButtonId > 0)
    {
        $strQuery .= ' WHERE usiADSLActionButtonId="'.$usiADSLActionButtonId.'"';
    }
    if($strOrderBy != '')
    {
        $strQuery .= ' ORDER BY '.$strOrderBy;
    }

    $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Getting Tool');
    $arrToll   = array();
    if($resResult)
    {
        $arrTool = PrimitivesResultsAsArrayGet($resResult);
    }
    return ADSLStripSlashes($arrTool);
}



//////////////////////////////////////////////////////////
// Function      ADSLProvisionToolGetWithGroups
// Purpose       Get array of provision tool, including their group information
// Argument      usiADSLActionButtonId   Specify tool to return
//               strOrderBy              Order results
// Returns       Array of Tool(s)
//////////////////////////////////////////////////////////
function ADSLProvisionToolGetWithGroups($usiADSLActionButtonId=0, $strOrderBy = '')
{
    $dbhConnection  = get_named_connection_with_db('common_adsl');
    $strQuery       = 'SELECT *
                         FROM tblADSLActionButton AS aab
                   INNER JOIN vblADSLActionButtonGroup AS aabg
                           ON aab.usiADSLActionButtonGroupId = aabg.usiADSLActionButtonGroupId';

    if ($usiADSLActionButtonId > 0) {

        $strQuery .= ' WHERE usiADSLActionButtonId="'.$usiADSLActionButtonId.'"';
    }

    if ($strOrderBy != '') {

        $strQuery .= ' ORDER BY '.$strOrderBy;
    }

    $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Getting Tool');
    $arrToll   = array();

    if ($resResult) {

        $arrTool = PrimitivesResultsAsArrayGet($resResult);
    }
    return ADSLStripSlashes($arrTool);
}



//////////////////////////////////////////////////////////
// Function      ADSLProvisionToolGetByGroup
// Purpose       Get array of provision tool, including their group information
// Argument      usiADSLActionButtonId   Specify tool to return
//               strOrderBy              Order results
// Returns       Array of Tool(s)
//////////////////////////////////////////////////////////
function ADSLProvisionToolGetByGroup($usiADSLActionButtonGroupId=0, $strOrderBy = '')
{
    $dbhConnection  = get_named_connection_with_db('common_adsl');
    $strQuery       = 'SELECT *
                         FROM tblADSLActionButton AS aab
                   INNER JOIN vblADSLActionButtonGroup AS aabg
                           ON aab.usiADSLActionButtonGroupId = aabg.usiADSLActionButtonGroupId';

    if ($usiADSLActionButtonGroupId > 0) {

        $strQuery .= ' WHERE aabg.usiADSLActionButtonGroupId="'.$usiADSLActionButtonGroupId.'"';
    }

    if ($strOrderBy != '') {

        $strQuery .= ' ORDER BY '.$strOrderBy;
    }

    $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Getting Tool');
    $arrTool   = array();

    if ($resResult) {

        $arrTool = PrimitivesResultsAsArrayGet($resResult);
    }
    return ADSLStripSlashes($arrTool);
}



//////////////////////////////////////////////////////////
// Function      ADSLProvisionToolGetByWorkplaceURL
// Purpose       Get array of tool, based on its Action Button URL
// Argument      vchWorkplaceURL
// Returns       Array of Tool
//////////////////////////////////////////////////////////
function ADSLProvisionToolGetByWorkplaceURL($vchWorkplaceURL)
{
    $dbhConnection   = get_named_connection_with_db('common_adsl');

    $vchWorkplaceURL = addslashes($vchWorkplaceURL);

    $strQuery        = 'SELECT *
                        FROM tblADSLActionButton
                        WHERE vchADSLActionButtonURL = "'.$vchWorkplaceURL.'"';
    $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Getting Tool Id by Workplace URL');
    $arrTool   = array();
    if($resResult)
    {
        $arrTempTool = PrimitivesResultsAsArrayGet($resResult);
        $arrTool     = $arrTempTool[0];
    }
    return ADSLStripSlashes($arrTool);
}



//////////////////////////////////////////////////////////
// Function      ADSLProvisionToolGetByWorkplaceURL
// Purpose       Get array of tool, based on its Action Button URL
// Argument      vchPortalURL
// Returns       Array of Tool
//////////////////////////////////////////////////////////
function ADSLProvisionToolGetByPortalURL($vchPortalURL)
{
    $dbhConnection   = get_named_connection_with_db('common_adsl');

    $vchPortalURL    = addslashes($vchPortalURL);

    $strQuery        = 'SELECT *
                        FROM tblADSLActionButton
                        WHERE vchADSLActionButtonPortalURL = "'.$vchPortalURL.'"';
    $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Getting Tool Id by Portal URL');
    $arrTool   = array();
    if($resResult)
    {
        $arrTool = PrimitivesResultsAsArrayGet($resResult);
        $arrTool = $arrTool[0];
    }
    return ADSLStripSlashes($arrTool);
}



//////////////////////////////////////////////////////////
// Function      ADSLProvisionSubStatusToolsGet
// Purpose       Get array of tools that are allowed for a sub status
// Argument      usiADSLProcessSubStatusId
// Returns       Array of Tool Id(s)
//////////////////////////////////////////////////////////
function ADSLProvisionSubStatusToolsGet($usiADSLProcessSubStatusId)
{
    $dbhConnection  = get_named_connection_with_db('common_adsl');

    $usiADSLProcessSubStatusId = addslashes($usiADSLProcessSubStatusId);

    $strQuery       = 'SELECT *
                        FROM tblSubStatusButtonLink
                        WHERE usiADSLProcessSubStatusId = "'.$usiADSLProcessSubStatusId.'"';

    $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Getting Tools for Process Sub Status');
    $arrTool   = array();
    if($resResult)
    {
        $arrTool = PrimitivesResultsAsArrayGet($resResult, 'usiADSLActionButtonId');
    }
    return ADSLStripSlashes($arrTool);
}



//////////////////////////////////////////////////////////
// Function      ADSLProvisionSubStatusToolsGet
// Purpose       Get array of tools that are allowed for a sub status
// Argument      usiADSLProcessSubStatusId
// Returns       Array of Tool Id(s)
//////////////////////////////////////////////////////////
function ADSLProvisionHistoryGet($usiServiceId)
{
    $dbhConnection  = get_named_connection_with_db('common_adsl');

    $usiServiceId   = addslashes($usiServiceId);

    $strQuery       = 'SELECT *
                    FROM tblADSLProcess
                    WHERE usiServiceId = "'.$usiServiceId.'"
                    AND usiPartnerId = "'.PARTNER_ID.'"
                ORDER BY dtmDateTime DESC, usiADSLProcessId DESC';

    $resResult  = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Getting Process History');
    $arrHistory = array();
    if($resResult)
    {
        $arrHistory = PrimitivesResultsAsArrayGet($resResult, '');
    }
    return ADSLStripSlashes($arrHistory);
}



//////////////////////////////////////////////////////////
// Function      ADSLProvisionOutstandingActionButtonsGet
// Purpose       Get array of tools that are allowed for the current adsl account
// Argument      usiServiceId
// Returns       Array of Tool Id(s)
//////////////////////////////////////////////////////////
function ADSLProvisionOutstandingActionButtonsGet($usiServiceId)
{
    global $perm;

    $dbhConnection  = get_named_connection_with_db('common_adsl');

    $usiServiceId   = addslashes($usiServiceId);

    $strQuery       = 'SELECT DISTINCT abg.vchADSLActionButtonGroup, ab.*, pss.vchADSLProcessSubStatusName,
                              pa.usiADSLProcessActionId
                         FROM tblSubStatusButtonLink AS pssbl
                   INNER JOIN tblSubStatus AS pss
                           ON pss.usiADSLProcessSubStatusId = pssbl.usiADSLProcessSubStatusId
                   INNER JOIN tblADSLActionButton AS ab
                           ON ab.usiADSLActionButtonId      = pssbl.usiADSLActionButtonId
                   INNER JOIN vblADSLActionButtonGroup AS abg
                           ON ab.usiADSLActionButtonGroupId = abg.usiADSLActionButtonGroupId
                   INNER JOIN tblADSLProcessAction AS pa
                           ON pa.usiADSLProcessSubStatusId  = pssbl.usiADSLProcessSubStatusId
                        WHERE pa.usiServiceId               = "'.$usiServiceId.'"
                          AND pa.usiPartnerId               = "' . PARTNER_ID . '"
                          AND pa.dtmCompleted IS NULL';

    // Limit buttons if no networks permission
    if (!$perm->have_perm('networks')) {

        $strQuery .= ' AND abg.vchADSLActionButtonTag != "developer"';
    }

    // Also limit the buttons based on the type of adsl account
    // We filter at this stage to avoid any problms later based on counting the array
    $arrADSL = get_adsl_application_details(ADSLGetInstallDiaryServiceId($usiServiceId));

    if ($arrADSL['is_isdn_conversion'] == 'yes' AND 5 != PARTNER_ID) {

        $strQuery .= ' AND ab.bolADSLActionButtonNotForISDNUse != 1';
    }


    // Also limit the buttons based on the type of adsl account
    // We filter at this stage to avoid any problms later based on counting the array
    $arrService = userdata_service_get($usiServiceId);
    $arrADSL    = adsl_get_product_details($arrService['type']);

    if ($arrADSL['speed'] == '500') {

        $strQuery .= ' AND ab.bolADSLActionButtonNotForLowSpeed != 1';
    }

    $strQuery .= '   ORDER BY usiADSLActionButtonGroupId';
    $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Getting Outstanding Tools for ADSL Service');
    $arrTool   = array();

    if ($resResult) {

        $arrTool = PrimitivesResultsAsArrayGet($resResult);
    }

    return ADSLStripSlashes($arrTool);
}



//////////////////////////////////////////////////////////
// Function      ADSLProvisionInstallDiaryButtonsGet
// Purpose       Get array of tools that are allowed for the current adsl account
//               BASED ON INSTALL DIARY STATUS
// Argument      usiServiceId
// Returns       Array of Tool Id(s)
//////////////////////////////////////////////////////////
function ADSLProvisionInstallDiaryButtonsGet($usiServiceId)
{
    global $my_id, $perm;

    $dbhConnection  = get_named_connection_with_db('adsl');

    $usiServiceId   = addslashes($usiServiceId);

    $bolHouseMove = CHouseMove::getActiveHouseMoveFromServiceId($usiServiceId);

    if ($bolHouseMove) {

        $strStatus = 'house_move';
    } else {

        $strQuery = sprintf('SELECT status FROM install_diary WHERE service_id=%d', $usiServiceId);
        $resResult
            = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': ADSLProvisionInstallDiaryButtonsGet()');
        $strStatus = PrimitivesResultGet($resResult, 'status');
    }
    if($strStatus == null) {
        return array();
    }

    $strQuery = sprintf("SELECT DISTINCT abg.vchADSLActionButtonGroup, ab.*, pss.vchADSLProcessSubStatusName
                           FROM dbCommonADSL.tblSubStatusButtonLink AS pssbl
                     INNER JOIN dbCommonADSL.tblSubStatus AS pss
                             ON pss.usiADSLProcessSubStatusId = pssbl.usiADSLProcessSubStatusId
                     INNER JOIN dbCommonADSL.tblADSLActionButton AS ab
                             ON ab.usiADSLActionButtonId      = pssbl.usiADSLActionButtonId
                     INNER JOIN dbCommonADSL.vblADSLActionButtonGroup AS abg
                             ON ab.usiADSLActionButtonGroupId = abg.usiADSLActionButtonGroupId
                          WHERE pss.vchADSLProcessSubStatusTag = '%s'",
                        $strStatus);

    // Limit buttons if no networks permission
    if (!$perm->have_perm('networks')) {

        $strQuery .= ' AND abg.vchADSLActionButtonTag != "developer"';
    }

    // Also limit the buttons based on the type of adsl account
    // We filter at this stage to avoid any problms later based on counting the array
    $arrADSL = get_adsl_application_details($usiServiceId);

    if ($arrADSL['is_isdn_conversion'] == 'yes' AND 5 != PARTNER_ID) {

        $strQuery .= ' AND ab.bolADSLActionButtonNotForISDNUse != 1';
    }

    // Also limit the buttons based on the type of adsl account
    // We filter at this stage to avoid any problms later based on counting the array
    $arrService = userdata_service_get($usiServiceId);
    $arrADSL    = adsl_get_product_details($arrService['type']);

    if ($arrADSL['speed'] == '500') {

        $strQuery .= ' AND ab.bolADSLActionButtonNotForLowSpeed != 1';
    }

    $strQuery .= '   ORDER BY usiADSLActionButtonGroupId';

    $resResult
        = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Getting Outstanding Tools for ADSL Service');
    $arrTool   = array();

    if ($resResult) {

        $arrTool = PrimitivesResultsAsArrayGet($resResult);
    }

    return ADSLStripSlashes($arrTool);
}




//////////////////////////////////////////////////////////
// Function      ADSLProvisionOutstandingActionsGet
// Purpose       Get array of statuses for the current adsl account
// Argument      usiServiceId
// Returns       Array of statuses
//////////////////////////////////////////////////////////
function ADSLProvisionOutstandingActionsGet($usiServiceId)
{
    $dbhConnection  = get_named_connection_with_db('common_adsl');

    $usiServiceId   = addslashes($usiServiceId);

    $strQuery       =
        'SELECT pss.vchADSLProcessSubStatusName, pss.usiADSLProcessSubStatusId, pa.usiADSLProcessActionId
           FROM tblSubStatus AS pss
     INNER JOIN tblADSLProcessAction AS pa
             ON pss.usiADSLProcessSubStatusId = pa.usiADSLProcessSubStatusId
          WHERE pa.usiServiceId = "' . $usiServiceId . '"
            AND pa.usiPartnerId = "' . PARTNER_ID . '"
            AND pa.dtmCompleted IS NULL';

    $resResult
        = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Getting Outstanding Tools for ADSL Service');
    $arrAction = array();

    if ($resResult) {

        $arrAction = PrimitivesResultsAsArrayGet($resResult);
    }

    return ADSLStripSlashes($arrAction);
}



//////////////////////////////////////////////////////////
// Function      ADSLProvisionAvailableActionsGet
// Purpose       Get array of actions that are not currently on an account
// Argument      usiServiceId
// Returns       Array of Tool Id(s)
//////////////////////////////////////////////////////////
function ADSLProvisionAvailableActionsGet($usiServiceId)
{
    $dbhConnection  = get_named_connection_with_db('common_adsl');

    $usiServiceId   = addslashes($usiServiceId);

    $strQuery       = 'SELECT pss.vchADSLProcessSubStatusName, pss.usiADSLProcessSubStatusId, pa.usiADSLProcessActionId
                         FROM tblSubStatus AS pss
                    LEFT JOIN tblADSLProcessAction AS pa
                           ON pa.usiADSLProcessSubStatusId  = pss.usiADSLProcessSubStatusId
                        WHERE pa.usiADSLProcessSubStatusId IS NULL
                          AND pa.usiServiceId               = "'.$usiServiceId.'"
                          AND pa.usiPartnerId               = "' . PARTNER_ID . '"
                          AND pa.dtmCompleted IS NULL';

    $resResult
        = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Getting Outstanding Tools for ADSL Service');
    $arrAction   = array();

    if ($resResult) {

        $arrAction = PrimitivesResultsAsArrayGet($resResult);
    }

    return ADSLStripSlashes($arrAction);
}



function ADSLProvisionIsActionOutstanding($usiADSLProcessSubStatusId, $usiServiceId)
{
    $dbhConnection  = get_named_connection_with_db('common_adsl');

    $usiServiceId              = addslashes($usiServiceId);
    $usiADSLProcessSubStatusId = addslashes($usiADSLProcessSubStatusId);

    $strQuery       = 'SELECT usiADSLProcessActionId
                        FROM tblADSLProcessAction
                        WHERE usiADSLProcessSubStatusId  = "'.$usiADSLProcessSubStatusId.'"
                        AND usiServiceId               = "'.$usiServiceId.'"
                AND usiPartnerId               = "' . PARTNER_ID . '"
                        AND dtmCompleted IS NULL';

    $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Checking Outstanding Tools for ADSL Service');
    $arrAction = array();
    if($resResult)
    {
        $arrAction = PrimitivesResultsAsArrayGet($resResult);
    }

    $bolOutstanding = false;
    if(count($arrAction) > 0)
    {
        $bolOutstanding = true;
    }
    return $bolOutstanding;
}



function ADSLProvisionStatusesGetAffected($usiServiceId, $usiADSLActionButtonId)
{
    $dbhConnection  = get_named_connection_with_db('common_adsl');

    $usiServiceId          = addslashes($usiServiceId);
    $usiADSLActionButtonId = addslashes($usiADSLActionButtonId);

    $strQuery       = 'SELECT pss.*
                         FROM tblSubStatus AS pss
                   INNER JOIN tblADSLProcessAction AS pa
                           ON pa.usiADSLProcessSubStatusId   = pss.usiADSLProcessSubStatusId
                   INNER JOIN tblSubStatusButtonLink AS ssbl
                           ON ssbl.usiADSLProcessSubStatusId = pa.usiADSLProcessSubStatusId
                        WHERE pa.dtmCompleted                IS NULL
                          AND pa.usiServiceId                = "'.$usiServiceId.'"
                          AND pa.usiPartnerId                = "'.PARTNER_ID.'"
                          AND ssbl.usiADSLActionButtonId     = "'.$usiADSLActionButtonId.'"';

    $resResult
        = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Checking statuses affected by tool action');
    $arrAction = array();

    if ($resResult) {

        $arrAction = PrimitivesResultsAsArrayGet($resResult);
    }

    return ADSLStripSlashes($arrAction);
}



//////////////////////////////////////////////////////////
// Function      ADSLIsISDNConversion
// Purpose       Confirm if an account is an ISDN-ADSL Conversion or Not
// Argument      usiServiceId
// Returns       true or false
//////////////////////////////////////////////////////////
function ADSLIsISDNConversion($usiServiceId)
{
    $dbhConnection  = get_named_connection_with_db('adsl');

    $usiServiceId   = addslashes($usiServiceId);

    $strQuery       = 'SELECT is_isdn_conversion AS ISDN
                        FROM install_diary
                        WHERE service_id = "'.$usiServiceId.'"';

    $resResult      = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Checking for ISDN Conversion');

    $bolISDN        = false;
    if($resResult)
    {
        $arrMaster  = PrimitivesResultsAsArrayGet($resResult);
        if($arrMaster[0]['ISDN'] == 'yes')
        {
            $bolISDN = true;
        }
    }
    return $bolISDN;
}

//////////////////////////////////////////////////////////
// Function      ADSLIsUpgradeInProgress
// Purpose       Confirm if an account is an Upgrade account or not
// Argument      usiServiceId (of the main account)
// Returns        the temporary service id or boolean false
//////////////////////////////////////////////////////////
function ADSLIsUpgradeInProgress($usiServiceId)
{
    $usiServiceId = (int) $usiServiceId;

    $query = "SELECT holding_adsl_service_id FROM adsl_upgrades" .
        " WHERE upgrade_status = 'in_progress' AND service_id = $usiServiceId";

    $conn = get_named_connection_with_db('userdata');
    $resResult = PrimitivesQueryOrExit($query, $conn, __FILE__.': Checking for upgrade account');
    $strTempAccountServiceId = PrimitivesResultGet($resResult, 'holding_adsl_service_id');

    return (!empty($strTempAccountServiceId) ? $strTempAccountServiceId : false);
}


//////////////////////////////////////////////////////////
// Function      ADSLGetMainAccountServiceId
// Purpose       to get the Service ID main account
// Argument      usiServiceId (of the install diary entry)
// Returns       strMainAccountServiceId or false
//////////////////////////////////////////////////////////
function ADSLMapTempToMain ($usiServiceId)
{
    $usiServiceId = (int) $usiServiceId;

    $query = "SELECT service_id FROM adsl_upgrades" .
        " WHERE upgrade_status = 'in_progress' AND holding_adsl_service_id = $usiServiceId";

    $conn = get_named_connection_with_db('userdata');
    $resResult = PrimitivesQueryOrExit($query, $conn, __FILE__.': Checking for Main account');
    $strMainAccountServiceId = PrimitivesResultGet($resResult, 'service_id');

    return (!empty($strMainAccountServiceId) ? $strMainAccountServiceId : false);
}

/**
 * Confirm if an account is a low speed adsl product
 * Data is cached on a per-request basis
 *
 * @param int $intServiceDefinitionId the ADSL service definition ID
 *
 * @return bool
 */
function ADSLIsLowSpeedProduct($intServiceDefinitionId)
{
    static $dataCache = array();
    $intServiceDefinitionId = (int) $intServiceDefinitionId;

    if (!array_key_exists($intServiceDefinitionId, $dataCache)) {
        $query     = "SELECT speed FROM adsl_product WHERE service_definition_id = $intServiceDefinitionId";

        $conn      = get_named_connection_with_db('product_reporting');
        $resResult = PrimitivesQueryOrExit($query, $conn, __FUNCTION__);
        $arrSpeed  = PrimitivesResultsAsArrayGet($resResult);

        $dataCache[$intServiceDefinitionId] = (!empty($arrSpeed) && $arrSpeed[0]['speed'] == 500 ? true : false);
    }

    return $dataCache[$intServiceDefinitionId];
}

//////////////////////////////////////////////////////////
// Function      ADSLWorkLogIsAccountBeingWorkedOn
// Purpose       Determine if an account is being currently worked on?
// Argument      usiServiceId
// Returns       Array of user work log for this account or false
//////////////////////////////////////////////////////////
function ADSLWorkLogIsAccountBeingWorkedOn($usiServiceId)
{
    $dbhConnection  = get_named_connection_with_db('common_adsl');

    $usiServiceId   = addslashes($usiServiceId);

    $strQuery       = 'SELECT *
                        FROM tblUserWorkLog
                        WHERE usiServiceId = "'.$usiServiceId.'"
                        AND usiPartnerId = "'.PARTNER_ID.'"
                        AND dtmEnd       IS NULL';
    $resResult      = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Checking if ADSL account is being worked on');

    $arrIsAccountBeingWorkedOn = false;

    if($resResult)
    {
        $arrResult = PrimitivesResultsAsArrayGet($resResult);
        if(count($arrResult) > 0)
        {
            $arrIsAccountBeingWorkedOn = ADSLStripSlashes($arrResult[0]);
        }
    }
    return $arrIsAccountBeingWorkedOn;
}



//////////////////////////////////////////////////////////
// Function      ADSLWorkLogNumberOfTimesAccountHasBeenWorkedOn
// Purpose       Determine how many times an account has been worked on
// Argument      usiServiceId
// Returns       number of times account has been worked on
//////////////////////////////////////////////////////////
function ADSLWorkLogNumberOfTimesAccountHasBeenWorkedOn($usiServiceId)
{
    $dbhConnection  = get_named_connection_with_db('common_adsl');

    $usiServiceId   = addslashes($usiServiceId);

    $strQuery       = 'SELECT count(*) AS intTimesWorkedOn
                        FROM tblUserWorkLog
                        WHERE usiServiceId = "'.$usiServiceId.'"
                        AND usiPartnerId = "'.PARTNER_ID.'"';
    $resResult      = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Checking number of times ADSL account has been worked on');

    $intTimesWorkedOn = 0;
    if($resResult)
    {
        $intTimesWorkedOn = PrimitivesResultGet($resResult, 'intTimesWorkedOn');
    }
    return $intTimesWorkedOn;
}

//////////////////////////////////////////////////////////
// Function      ADSLGetLowerSpeedProduct
// Purpose       Work out what product is the next step down to the current product
// Argument      intServiceDefinitionId - product to look for lower speed version - service_definition_id
// Returns       intProductToMoveTo     - lower speed version of product - service_definition_id
//////////////////////////////////////////////////////////
function ADSLGetLowerSpeedProduct($intServiceDefinitionId)
{
    $dbhConnection  = get_named_connection_with_db('product');

    $intServiceDefinitionId = addslashes($intServiceDefinitionId);

    // Time for the most monsterous query of all time! (Ulp!)
    $strQuery = 'SELECT DISTINCT(sd2.service_definition_id) as intServiceDefinitionId, sd2.name
                   FROM service_definitions as sd1
             INNER JOIN service_definitions as sd2
                     ON sd1.initial_charge        = sd2.initial_charge
                    AND sd1.isp                   = sd2.isp
             INNER JOIN adsl_product as ap1
                     ON sd1.service_definition_id = ap1.service_definition_id
             INNER JOIN adsl_product as ap2
                     ON sd2.service_definition_id = ap2.service_definition_id
                  WHERE CAST(ap2.speed AS SIGNED) < CAST(ap1.speed AS SIGNED)
                    AND ap2.speed                 != ""
                    AND ap1.nat                   = ap2.nat
                    AND ap1.hardware_type         = ap2.hardware_type
                    AND ap1.install_type          = ap2.install_type
                    AND IF (sd1.name LIKE "%Annual Contract%", sd2.name LIKE "%Annual Contract%", 1)
                    AND IF (sd1.name LIKE "%Monthly Contract%", sd2.name LIKE "%Monthly Contract%", 1)
                    AND IF (ap1.bt_product_code LIKE "%Home%", ap2.bt_product_code LIKE "%Home%", 1)
                    AND IF (ap1.bt_product_code LIKE "%Office%", ap2.bt_product_code LIKE "%Office%", 1)
                    AND IF (ap1.bt_product_code LIKE "%IPStream S%", ap2.bt_product_code LIKE "%IPStream S%", 1)
                    AND IF (ap1.bt_product_code LIKE "%IPStream O%", ap2.bt_product_code LIKE "%IPStream O%", 1)
                    AND IF (sd1.name LIKE "%FORCE9%", sd2.name like "%FORCE9%", 1)
                    AND IF (sd1.name LIKE "%F9%", sd2.name like "%F9%", 1)
                    AND (sd2.end_date is null
                     OR sd2.end_date > now())
                    AND sd2.name NOT LIKE "%Staff%"
                    AND sd1.service_definition_id = "'.$intServiceDefinitionId . '" ';


    //Remote Internet - Check that the egress type is the same
    if (defined('PARTNER_ID') && PARTNER_ID == 5) {

        $strQuery .= ' AND sd1.intEgressTypeID = sd2.intEgressTypeID ';
    }

    //Order the query by speed and limit to the best fit product.
    $strQuery .= ' ORDER BY ap2.speed DESC LIMIT 1';

    // This line appeared to cause problems cross visp
    // 'AND IF(sd1.end_date IS NULL, sd2.end_date IS NULL, sd2.end_date IS NOT NULL) ' .
    $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Finding 500Kbps version of product');
    $intProductToMoveTo = 0;

    if ($resResult) {

        $arrResult = PrimitivesResultsAsArrayGet($resResult);
        $intProductToMoveTo = $arrResult[0]['intServiceDefinitionId'];
    }
    return $intProductToMoveTo;
}



//////////////////////////////////////////////////////////
// Function      ADSLProvisionGetStatusesAndGroup
// Purpose       Fetch last (intCount) statuses from process history for an account and group them
//               this is used for parsing failed error counting
// Argument      usiServiceId - Service to fetch history for
//               intCount     - Number of previous histories to get
// Returns       Array of substatuses
//////////////////////////////////////////////////////////
function ADSLProvisionGetStatusesAndGroup($usiServiceId, $intCount=0)
{
    $dbhConnection  = get_named_connection_with_db('common_adsl');

    $usiServiceId = addslashes($usiServiceId);
    $intCount      = addslashes($intCount);

    $strQuery      = 'SELECT usiADSLProcessSubStatusId
                        FROM tblADSLProcess
                        WHERE usiServiceId = "'.$usiServiceId.'"
                    ORDER BY dtmDateTime DESC';
    if($intCount > 0)
    {
        $strQuery .= ' LIMIT '.$intCount;
    }

    $resResult     = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Finding statuses and counting occurances');

    $arrSubStatus  = array();
    if($resResult)
    {
        $arrResult = PrimitivesResultsAsArrayGet($resResult);
        foreach($arrResult as $intIndex => $arrProcess)
        {
            if(!isset($arrSubStatus[$arrProcess['usiADSLProcessSubStatusId']]))
            {
                $arrSubStatus[$arrProcess['usiADSLProcessSubStatusId']] = 1;
            }
            else
            {
                ++ $arrSubStatus[$arrProcess['usiADSLProcessSubStatusId']];
            }
        }
    }
    return $arrSubStatus;
}



//////////////////////////////////////////////////////////
// Function      ADSLGetTimeSlots
// Purpose       Fetch time slots for installation of ADSL
// Argument      intADSLInstallTimeSlotId  if passed will return a specific time slot, otherwise returns all time slots
// Returns       Array of Timeslot (s)
//////////////////////////////////////////////////////////
function ADSLGetTimeSlots($intADSLInstallTimeSlotId = 0)
{
    $dbhConnection  = get_named_connection_with_db('common_adsl');

    $intADSLInstallTimeSlotId = addslashes($intADSLInstallTimeSlotId);

    $strQuery      = 'SELECT *
                        FROM vblADSLInstallTimeSlots';
    if($intADSLInstallTimeSlotId != 0)
    {
        $strQuery .= ' WHERE intADSLInstallTimeSlotId = '.$intTimeSlotId;
    }

    $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Fetching time slots');

    if ($intADSLInstallTimeSlotId == 0)
    {
        $arrTimeSlots = PrimitivesResultsAsArrayGet($resResult, 'intADSLInstallTimeSlotId');
        // Mumble grumble, mysql time format hh:mm:ss, seconds are irrelevant
        foreach($arrTimeSlots as $intIndex => $arrTimeSlot)
        {
            $arrTimeSlots[$intIndex]['timTimeSlotStart'] = substr($arrTimeSlot['timTimeSlotStart'], 0, 5);
            $arrTimeSlots[$intIndex]['timTimeSlotEnd']   = substr($arrTimeSlot['timTimeSlotEnd'], 0, 5);
        }
    }
    else
    {
        $arrTimeSlots = PrimitivesResultGet($resResult);
        // Mumble grumble, mysql time format hh:mm:ss, seconds are irrelevant
        $arrTimeSlots['timTimeSlotStart'] = substr($arrTimeSlots['timTimeSlotStart'], 0, 5);
        $arrTimeSlots['timTimeSlotEnd']   = substr($arrTimeSlots['timTimeSlotEnd'], 0, 5);
    }
    return ($arrTimeSlots);
}



function ADSLGetTimeSlotByStartTime($strTime)
{
    $dbhConnection  = get_named_connection_with_db('common_adsl');

    $strTime = addslashes($strTime);

    $strQuery      = 'SELECT *
                        FROM vblADSLInstallTimeSlots
                        WHERE timTimeSlotStart = "'.$strTime.'"';

    $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Fetching specific time slot');

    $arrTimeSlot = PrimitivesResultGet($resResult);
    // Mumble grumble, mysql time format hh:mm:ss, seconds are irrelevant
    $arrTimeSlots['timTimeSlotStart'] = substr($arrTimeSlots['timTimeSlotStart'], 0, 5);
    $arrTimeSlots['timTimeSlotEnd']   = substr($arrTimeSlots['timTimeSlotEnd'], 0, 5);
    return ($arrTimeSlots);
}


////////////////////////////////////////////////////////////////////////////////
//  Function   :  adslHardwareStockLevelAlter
//  Purpose    :  Works out what adsl hardware stock is attached
//                to this component and deducts/adds it from the stock level
//  Arguments  :  $intComponentTypeId - the component id
//                $strAction          - + or -
//  Returns    :  none
////////////////////////////////////////////////////////////////////////////////
function adslHardwareStockLevelAlter($intComponentTypeId, $strAction)
{
    $intComponentTypeId = addslashes($intComponentTypeId);

    $dbhConnection = get_named_connection_with_db('common_adsl');

    //  First get the hardware attached to this component modem/splitter/etc
    $strQuery = 'SELECT DISTINCT usiStockItemComponentId, usiStockItemId, usiServiceComponentId '
            .'  FROM tblADSLStockItemComponentMap '
            ." WHERE usiServiceComponentId='$intComponentTypeId' "
            .'   AND bolIsAMap=1 ';

    $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

    $arrItemsForCompnent = PrimitivesResultsAsArrayGet($resResult);

    //  Now lower our stock levels by the correct amount for each hardware item
    foreach($arrItemsForCompnent AS $arrOneItem)
    {
        $strUpdateQuery = 'UPDATE tblADSLStockItem '
                        ."   SET usiAmount = usiAmount $strAction usiAlterAmount "
                        ." WHERE usiStockItemId='".$arrOneItem['usiStockItemId']."' ";

        PrimitivesQueryOrExit($strUpdateQuery, $dbhConnection);
    }
}


////////////////////////////////////////////////////////////////////////////
//  Function  :  adslGetAllHardwareComponents
//  Purpose   :  Gets all hardware componentsd for a given partner_id
//  Arguments :  intPartnerId - the partner number
//  Returns   :  arrHardwareComponents - the components
////////////////////////////////////////////////////////////////////////////
function adslGetAllHardwareComponents($intPartnerId = 0)
{
    GLOBAL $partner_array;

    if($intPartnerId == 0)
    {
        $dbhConnection = get_named_connection_with_db('product');
    }
    else
    {
        $dbhConnection = get_named_connection_with_db($partner_array[$intPartnerId]['db']);
    }

    $strQuery = 'SELECT chbc.service_component_id, sc.name '
                 .'FROM component_hardware_bundle_config AS chbc '
           .'INNER JOIN service_components AS sc '
                   .'ON sc.service_component_id=chbc.service_component_id';

    $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

    $arrHardwareComponents = PrimitivesResultsAsArrayGet($resResult);

    return $arrHardwareComponents;
}



//////////////////////////////////////////////////////////
// Function      ADSLAppliedFeeFailedRetryBilling
// Purpose       To check to see if we should try to rebill the account for the inital payment when it's failed
// Argument      $intServiceId - the serviceId of the main account
//                 $intADSLProcessSubStatusId - the substatus for adsl process
// Returns       $arrRetryBilling - service id and bolRetryBilling
//////////////////////////////////////////////////////////
function ADSLAppliedFeeFailedRetryBilling ($intServiceId, $intADSLProcessSubStatusId)
{
    $intServiceId = addslashes($intServiceId);
    $intADSLProcessSubStatusId = addslashes($intADSLProcessSubStatusId);

    //check that the account has an entry in the install_diary
    $dbhConnection = get_named_connection_with_db('adsl');

    $intInstallDiaryServiceId = ADSLGetInstallDiaryServiceId ($intServiceId);

    $strQuery = 'SELECT COUNT(*) AS intTotal
                   FROM install_diary
                  WHERE service_id = '."'$intInstallDiaryServiceId'";

    $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

    $intTotalRows = PrimitivesResultGet($resResult, 'intTotal');

    if ($intTotalRows > 0)
    {
        $dbhConnection  = get_named_connection_with_db('common_adsl');

        $strQuery = 'SELECT DISTINCT usiServiceId,
                            IF (DATE_SUB(NOW(), INTERVAL 2 DAY) < dtmDateTime, 1, 0) AS bolRetryBilling
                       FROM tblADSLProcess
                      WHERE usiServiceId = '."'$intServiceId'".'
                        AND usiPartnerId = '.PARTNER_ID.'
                        AND usiADSLProcessSubStatusId = '."'$intADSLProcessSubStatusId'".'
                      ORDER BY dtmDateTime
                      LIMIT 1';

        $resResult = PrimitivesQueryOrExit(
            $strQuery,
            $dbhConnection,
            __FILE__.': Getting Applied Fee Failed Accounts check to see if we should try to bill again'
        );
        $arrRetryBilling = PrimitivesResultGet($resResult);

        $arrSubStatus = ADSLProvisionSubStatusGetByTag('awaiting_cancellation');
        $strQuery      = 'SELECT count(usiServiceId) AS intCount
                            FROM tblADSLProcess
                           WHERE usiServiceId              = "'.$intServiceId.'"
                             AND usiPartnerId              = '.PARTNER_ID.'
                             AND usiADSLProcessSubStatusId = "'.$arrSubStatus['usiADSLProcessSubStatusId'].'"';

        $resLogResult = PrimitivesQueryOrExit(
            $strQuery,
            $dbhConnection,
            __FILE__.': Checking ADSL log for an entry for cancellation email having been sent'
        );

        $arrResult     = PrimitivesResultGet($resLogResult);

        if ($arrResult['intCount'] == 0) {

            return $arrRetryBilling;
        }
    }
    return false;
}



/////////////////////////////////////////////////////
// Function   ADSLGetCeaseType
// Purpose    Gets information about a Cease Type(s)
// Arguments  $usiADSLCeaseTypeID - if 0 or blank will ignore param
//            $strADSLCeaseTypeTag - if '' will ignore param
// Returns    $arrResult - 0 or more rows in vblADSLCeaseType
/////////////////////////////////////////////////////
function ADSLGetCeaseType($usiADSLCeaseTypeID = 0,$strADSLCeaseTypeTag = '')
{
    //Get a db connection
    $dbhConnection = get_named_connection_with_db('common_adsl');

    $strQuery = "SELECT usiADSLCeaseTypeID, vchADSLCeaseType, vchADSLCeaseTypeTag
                FROM vblADSLCeaseType ";

    if($usiADSLCeaseTypeID != 0)
    {
        //If the user has specified an cease ID then select that
        $strQuery .= " WHERE usiADSLCeaseTypeID = $usiADSLCeaseTypeID";
    }
    elseif($strADSLCeaseTypeTag != '')
    {
        //If the use has only specified the tag then search on that
        $strQuery .= " WHERE vchADSLCeaseTypeTag = '$strADSLCeaseTypeTag'";
    }

    if($usiADSLCeaseTypeID != 0 && $strADSLCeaseTypeTag != '')
    {
        //If the user has specified both then search on that
        $strQuery .= " AND vchADSLCeaseTypeTag = '$strADSLCeaseTypeTag'";
    }

    $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

    $arrResult = PrimitivesResultsAsArrayGet($resResult);

    return $arrResult;

}//end of function: ADSLGetCeaseType()


////////////////////////////////////////////////////////
// Function   ADSLGetCeaseLog
// Purpose    Gets the information from a cease Log
// Arguments  $usiServiceID          - account
//            $usiADSLCeaseLogID     - primary key
//            $dtmDateCeaseRequested - to_days of dtmDateCeaseRequested
//
// Returns
///////////////////////////////////////////////////////
function ADSLGetCeaseLog($usiServiceID = 0, $usiADSLCeaseLogID = 0, $strDateCeaseRequested = 0)
{
    //Get a db connection
    $dbhConnection = get_named_connection_with_db('common_adsl');

    $strQuery = "SELECT usiADSLCeaseLogID,
                        usiServiceID,
                        usiADSLCeaseTypeID,
                        usiServiceDefinitionID,
                        dtmDateCeaseRequested,
                        dtmDateCeaseCancelled
                FROM tblADSLCeaseLog ";

    if($usiServiceID != 0)
    {
        $strQuery .= "WHERE usiServiceID = $usiServiceID ";
    }
    elseif($usiADSLCeaseLogID != 0)
    {
        $strQuery .= "WHERE usiADSLCeaseLogID = $usiADSLCeaseLogID ";
    }
    else if($intDateCeaseRequested != 0)
    {
        $strQuery .= "WHERE dtmDateCeaseRequested = '$strDateCeaseRequested' ";
    }

    if($usiServiceID != 0 && $usiADSLCeaseLogID != 0)
    {
        $strQuery .= "AND usiADSLCeaseLogID = $usiADSLCeaseLogID ";
    }

    if(($usiServiceID != 0 || $usiADSLCeaseLogID != 0) && $strDateCeaseRequested != 0)
    {
        $strQuery .= "AND dtmDateCeaseRequested = '$strDateCeaseRequested' ";
    }

    $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

    $arrResult = PrimitivesResultsAsArrayGet($resResult);

    return $arrResult;

}//end of function: ADSLGetCeaseLog()

////////////////////////////////////////////////////////
// Function   ADSLGetLatestCeaseLog
// Purpose    Gets the information from a cease Log
// Arguments  $usiServiceID
//
// Returns    $strLatestDateCeaseRequested
///////////////////////////////////////////////////////
function ADSLGetLatestCeaseLog($usiServiceID)
{
    //Get a db connection
    $dbhConnection = get_named_connection_with_db('common_adsl');

    $strQuery = "SELECT MAX(dtmDateCeaseRequested) as strLatestDateCeaseRequested
                FROM tblADSLCeaseLog
                WHERE usiServiceID = $usiServiceID";

    $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

    $arrResult = PrimitivesResultGet($resResult);

    $strLatestDateCeaseRequested = $arrResult['strLatestDateCeaseRequested'];

    return $strLatestDateCeaseRequested;

}//end of function: ADSLGetLatestCeaseLog()


///////////////////////////////////////////////////////////
// Function   ADSLGetCeaseReport
// Purpose    Gets the number of cease, and ceases cancelled, for a cease type
// Arguments  $usiADSLCeaseTypeID
//            $strStartDate
//            $strEndDate
// Returns    $arrCeaseReport => 'intCountCeases'
//                            => 'intCountCeasesCancelled'
///////////////////////////////////////////////////////////
function ADSLGetCeaseReport($usiADSLCeaseTypeID = 0, $strStartDate = '', $strEndDate = '')
{
    //Get a db connection
    $dbhConnection = get_named_connection_with_db('common_adsl');

    //Start the query list
    $arrWhere = array();

    //If we have any arguments add them to the query
    if($usiADSLCeaseTypeID != 0)
    {
        $arrWhere[] = "usiADSLCeaseTypeID = $usiADSLCeaseTypeID ";
    }
    if($strStartDate != '')
    {
        $arrWhere[] = "to_days(dtmDateCeaseRequested) >= to_days('$strStartDate') ";
    }

    if($strEndDate != '')
    {
        $arrWhere[] = "to_days(dtmDateCeaseRequested) <= to_days('$strEndDate') ";
    }

    //Select the number of cease that have been requested in WP.
    $strQuery = "SELECT COUNT(*)
                FROM tblADSLCeaseLog ";

    // Add the WHERE clause
    if(!empty($arrWhere))
    {
        $strQuery .= ' WHERE ' .implode(' AND ' , $arrWhere);
    }

    $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

    $arrResult = PrimitivesResultGet($resResult);

    $intCountCeases = $arrResult['COUNT(*)'];

    //SELECT the number of ceases that have been cancelled
    $strQuery = "SELECT COUNT(*)
                FROM tblADSLCeaseLog
                WHERE dtmDateCeaseCancelled != '0000-00-00 00:00:00' ";

    if($usiADSLCeaseTypeID != 0)
    {
        $strQuery .= "AND usiADSLCeaseTypeID = $usiADSLCeaseTypeID ";
    }
    if($strStartDate != '')
    {
        $strQuery .= "AND to_days(dtmDateCeaseRequested) >= to_days('$strStartDate') ";
    }
    if($strEndDate != '')
    {
        $strQuery .= "AND to_days(dtmDateCeaseRequested) <= to_days('$strEndDate') ";
    }

    $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

    $arrResult = PrimitivesResultGet($resResult);

    $intCountCeasesCancelled = $arrResult['COUNT(*)'];

    $arrCeaseReport = array('intCountCeases' => $intCountCeases,
                            'intCountCeasesCancelled' => $intCountCeasesCancelled);

    return $arrCeaseReport;
}//end of Function: ADSLGetCeaseReport()


///////////////////////////////////////////////////////////////////////
// Function     ADSLGetCeaseReportAccountTypes
// Purpose      Selects information from the tblADSLCeaseLog using the below arguments
// Arguments    $usiADSLCeaseTypeID
//              $strStartDate
//              $strEndDate
//              $strViewCancelled
//              $usiServiceDefinitionID
// Returns      $arrResults => 'usiADSLCeaseLogID'
//                          => 'usiServiceID'
//                          => 'usiADSLCeaseTypeID'
//                          => 'usiServiceDefinitionID'
//                          => 'dtmDateCeaseRequested'
//                          => 'dtmDateCeaseCancelled'
///////////////////////////////////////////////////////////////////////
function ADSLGetCeaseReportAccountTypes($usiADSLCeaseTypeID = 0, $strStartDate = '', $strEndDate = '',
                                        $strViewCancelled = 'cease',$usiServiceDefinitionID = 0)
{
    //Get a db connection
    $dbhConnection = get_named_connection_with_db('common_adsl');

    //Start the query list
    $arrWhere = array();

    if($usiADSLCeaseTypeID != 0)
    {
        $arrWhere[] = "usiADSLCeaseTypeID = $usiADSLCeaseTypeID ";
    }
    if($strStartDate != '')
    {
        $arrWhere[] = "to_days(dtmDateCeaseRequested) >= to_days('$strStartDate') ";
    }
    if($strEndDate != '')
    {
        $arrWhere[] = "to_days(dtmDateCeaseRequested) <= to_days('$strEndDate') ";
    }
    if($strViewCancelled != 'cease')
    {
        $arrWhere[] = "dtmDateCeaseCancelled != '0000-00-00 00:00:00' ";
    }
    if($usiServiceDefinitionID != 0)
    {
        $arrWhere[] = "usiServiceDefinitionID = $usiServiceDefinitionID ";
    }

    //Select the number of cease that have been requested in WP.
    $strQuery = "SELECT usiADSLCeaseLogID, usiServiceID, usiADSLCeaseTypeID, usiServiceDefinitionID,
                        dtmDateCeaseRequested, dtmDateCeaseCancelled
                FROM tblADSLCeaseLog";

    // Add the WHERE clause
    if(!empty($arrWhere))
    {
        $strQuery .= ' WHERE ' .implode(' AND ' , $arrWhere);
    }

    $strQuery .= "ORDER BY usiADSLCeaseTypeID, usiServiceDefinitionID ";

    $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

    $arrResult = PrimitivesResultsAsArrayGet($resResult);

    return $arrResult;
}//end of Function: ADSLGetCeaseReportAccountTypes()


///////////////////////////////////////////////////////////////////////////
//  Function   : ADSLIsUserMigratingISP
//  Purpose    : Discover whether an account is an ADSL migration
//  Arguments  : Service ID
//  Returns    : True or false
///////////////////////////////////////////////////////////////////////////

/**
 * Discover whether an account is an ADSL migration
 *
 * @param int $intServiceID Service Id
 *
 * @return bool
 */
function ADSLIsUserMigratingISP($intServiceID)
{
    if (!preg_match("/^[0-9]{1,20}$/", $intServiceID)) {

        return false;
    }

    $dbhConnection = get_named_connection_with_db('tickets');

    $strSQL = 'SELECT ticket_id '.
                'FROM tickets '.
            "WHERE service_id = '$intServiceID' ".
                "AND status IN ('Open', 'Closed', 'Assigned', 'Actioned') ".
                "AND team_id = 52";

    $resResult = PrimitivesQueryOrExit($strSQL, $dbhConnection);

    $arrRow = array();

    $arrRow = PrimitivesResultGet($resResult);

    if (count($arrRow) > 0) {

        return true;
    }

    return false;
}

//
//
//     End of Read Functions
//
///////////////////////////////////////////////////////////////////////////



///////////////////////////////////////////////////////////////////////////
//
//     Start of Write Functions
//
//


//////////////////////////////////////////
// Function    ADSLProvisionInstallDiaryStatusSet
// Purpose     Adding or updating an Install Diary Status
// Arguments   usiADSLInstallDiaryStatusId    - id of install diary status to change, or 0 to add
//             vchADSLInstallDiaryStatusName  - name of install diary status
//             vchADSLInstallDiaryStatusTag   - Reporting tag to use/Matches to enum install diary setting
//             txtTracker                     - Text to display on the ADSL tracker
// Returns     id of install diary status
//////////////////////////////////////////
function ADSLProvisionInstallDiaryStatusSet($usiADSLInstallDiaryStatusId, $vchADSLInstallDiaryStatusName, $vchADSLInstallDiaryStatusTag)
{
    $dbhConnection  = get_named_connection_with_db('common_adsl');

    $usiADSLInstallDiaryStatusId   = addslashes($usiADSLInstallDiaryStatusId);
    $vchADSLInstallDiaryStatusName = addslashes($vchADSLInstallDiaryStatusName);
    $vchADSLInstallDiaryStatusTag  = addslashes($vchADSLInstallDiaryStatusTag);

    if($usiADSLInstallDiaryStatusId == 0)
    {
        $strQuery = 'INSERT INTO tblADSLInstallDiaryStatus
                    (vchADSLInstallDiaryStatusName, vchADSLInstallDiaryStatusTag)
                    VALUES
                    ("'.$vchADSLInstallDiaryStatusName.'", "'.$vchADSLInstallDiaryStatusTag.'")';
    }
    else
    {
        $strQuery = 'UPDATE tblADSLInstallDiaryStatus
                        SET vchADSLInstallDiaryStatusName = "'.$vchADSLInstallDiaryStatusName.'",
                            vchADSLInstallDiaryStatusTag  = "'.$vchADSLInstallDiaryStatusTag.'"
                    WHERE usiADSLInstallDiaryStatusId   = "'.$usiADSLInstallDiaryStatusId.'"';
    }
    $resResult    = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Setting install diary status');

    if($usiADSLInstallDiaryStatusId == 0)
    {
        $usiADSLInstallDiaryStatusId = PrimitivesInsertIdGet($dbhConnection);
    }
    return $usiADSLInstallDiaryStatusId;
}



//////////////////////////////////////////
// Function    ADSLProvisionMasterStatusSet
// Purpose     Adding or updating a Master Status
// Arguments   usiADSLProcessMasterStatusId    - id of master status to change, or 0 to add
//             vchADSLProcessMasterStatusName  - name of master status
//             vchADSLProcessMasterStatusTag   - Reporting tag to use
// Returns     id of master status
//////////////////////////////////////////
function ADSLProvisionMasterStatusSet($usiADSLProcessMasterStatusId, $strADSLProcessMasterStatusName, $strADSLProcessMasterStatusTag)
{
    $dbhConnection  = get_named_connection_with_db('common_adsl');

    $usiADSLProcessMasterStatusId   = addslashes($usiADSLProcessMasterStatusId);
    $strADSLProcessMasterStatusName = addslashes($strADSLProcessMasterStatusName);
    $strADSLProcessMasterStatusTag  = addslashes($strADSLProcessMasterStatusTag);

    if($usiADSLProcessMasterStatusId == 0)
    {
        $strQuery       = 'INSERT INTO vblADSLProcessMasterStatus
                            (vchADSLProcessMasterStatusName, vchADSLProcessMasterStatusTag, vchDbSrc)
                        VALUES
                            ("'.$strADSLProcessMasterStatusName.'", "'.$strADSLProcessMasterStatusTag.'", "$Id: common-adsl-access.inc,v ******** 2009-07-15 12:33:03 rpvava Exp $")';
    }
    else
    {
        $strQuery       = 'UPDATE vblADSLProcessMasterStatus
                            SET vchADSLProcessMasterStatusName = "'.$strADSLProcessMasterStatusName.'",
                                vchADSLProcessMasterStatusTag  = "'.$strADSLProcessMasterStatusTag.'"
                            WHERE usiADSLProcessMasterStatusId   = "'.$usiADSLProcessMasterStatusId.'"';
    }
    $resResult      = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Setting master status');

    if($usiADSLProcessMasterStatusId == 0)
    {
        $usiADSLProcessMasterStatusId = PrimitivesInsertIdGet($dbhConnection);
    }
    return $usiADSLProcessMasterStatusId;
}



//////////////////////////////////////////
// Function    ADSLProvisionStatusSet
// Purpose     Adding or updating a Status
// Arguments   usiADSLProcessStatusId          - id of status to change, or 0 to add
//             usiADSLProcessMasterStatusId    - id of master status
//             vchADSLProcessStatusName        - name of status
//             vchInstallDiaryStatus           - Install Diary equivalent status
// Returns     id of status
//////////////////////////////////////////
function ADSLProvisionStatusSet($usiADSLProcessStatusId, $usiADSLProcessMasterStatusId, $strADSLProcessStatusName, $vchInstallDiaryStatus)
{
    global $my_id;

    $dbhConnection  = get_named_connection_with_db('common_adsl');

    $usiADSLProcessStatusId       = addslashes($usiADSLProcessStatusId);
    $usiADSLProcessMasterStatusId = addslashes($usiADSLProcessMasterStatusId);
    $strADSLProcessStatusName     = addslashes($strADSLProcessStatusName);
    $vchInstallDiaryStatus        = addslashes($vchInstallDiaryStatus);

    if($usiADSLProcessStatusId == 0)
    {
        $strQuery       = 'INSERT INTO tblADSLProcessStatus
                            (usiADSLProcessMasterStatusId, vchADSLProcessStatusName, vchInstallDiaryStatus, vchUserId)
                        VALUES
                            ("'.$usiADSLProcessMasterStatusId.'", "'.$strADSLProcessStatusName.'", "'.$vchInstallDiaryStatus.'", "'.$my_id.'")';
    }
    else
    {
        $strQuery       = 'UPDATE tblADSLProcessStatus
                            SET vchADSLProcessStatusName     = "'.$strADSLProcessStatusName.'",
                                vchInstallDiaryStatus        = "'.$vchInstallDiaryStatus.'",
                                usiADSLProcessMasterStatusId = "'.$usiADSLProcessMasterStatusId.'",
                                vchUserId                    = "'.$my_id.'"
                            WHERE usiADSLProcessStatusId       = "'.$usiADSLProcessStatusId.'"';
    }
    $resResult      = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Setting status');

    if($usiADSLProcessStatusId == 0)
    {
        $usiADSLProcessStatusId = PrimitivesInsertIdGet($dbhConnection);
    }
    return $usiADSLProcessStatusId;
}



//////////////////////////////////////////
// Function    ADSLProvisionSubStatusSet
// Purpose     Adding or updating a SubStatus
// Arguments   usiADSLProcessSubStatusId   - id of substatus to change, or 0 to add
//             usiADSLProcessStatusId      - id of status
//             vchADSLProcessSubStatusName - name of status
//             vchADSLProcessSubStatusTag  - tag of status, for 'hard-coding'
//             usiADSLInstallDiaryStatusId - Install Disary status to fail over to in  the event of ParB execution
//             strMailText                 - text to use as mail template
// Returns     id of sub status
//////////////////////////////////////////
function ADSLProvisionSubStatusSet($usiADSLProcessSubStatusId, $usiADSLProcessStatusId, $strADSLProcessSubStatusName, $strADSLProcessSubStatusTag, $strMailText, $usiADSLInstallDiaryStatusId)
{
    global $my_id;

    $dbhConnection  = get_named_connection_with_db('common_adsl');

    $usiADSLProcessSubStatusId   = addslashes($usiADSLProcessSubStatusId);
    $usiADSLProcessStatusId      = addslashes($usiADSLProcessStatusId);
    $strADSLProcessSubStatusName = addslashes($strADSLProcessSubStatusName);
    $strADSLProcessSubStatusTag  = addslashes($strADSLProcessSubStatusTag);
    $strMailText                   = addslashes($strMailText);

    if($usiADSLProcessSubStatusId == 0)
    {
        $strQuery       = 'INSERT INTO tblSubStatus
                            (usiADSLProcessStatusId, vchADSLProcessSubStatusName, vchADSLProcessSubStatusTag, txtMailText, usiADSLInstallDiaryStatusId, vchUserId)
                        VALUES
                            ("'.$usiADSLProcessStatusId.'", "'.$strADSLProcessSubStatusName.'", "'.$strADSLProcessSubStatusTag.'", "'.$strMailText.'", "'.$usiADSLInstallDiaryStatusId.'", "'.$my_id.'")';
    }
    else
    {
        $strQuery       = 'UPDATE tblSubStatus
                            SET vchADSLProcessSubStatusName = "'.$strADSLProcessSubStatusName.'",
                                vchADSLProcessSubStatusTag  = "'.$strADSLProcessSubStatusTag.'",
                                usiADSLProcessStatusId      = "'.$usiADSLProcessStatusId.'",
                                usiADSLProcessStatusId      = "'.$usiADSLProcessStatusId.'",
                                usiADSLInstallDiaryStatusId = "'.$usiADSLInstallDiaryStatusId.'",
                                txtMailText                 = "'.$strMailText.'",
                                vchUserId                   = "'.$my_id.'"
                            WHERE usiADSLProcessSubStatusId   = "'.$usiADSLProcessSubStatusId.'"';
    }
    $resResult      = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Setting substatus');

    if($usiADSLProcessSubStatusId == 0)
    {
        $usiADSLProcessSubStatusId = PrimitivesInsertIdGet($dbhConnection);
    }
    return $usiADSLProcessSubStatusId;
}


//////////////////////////////////////////
// Function    ADSLProvisionXMLErrorSet
// Purpose     Adding or updating an XML Error
// Arguments   usiADSLErrorCodeId             - id of error code to change, or 0 to add
//             vchADSLErrorCode               - error code
//             vchADSLErrorText               - error text
//             vchADSLErrorTextNotForCustomer - error text, not for customer
// Returns     id of sub status
//////////////////////////////////////////
function ADSLProvisionXMLErrorSet($usiADSLErrorCodeId, $vchADSLErrorCode, $vchADSLErrorText, $bolADSLErrorShowCustomer, $usiADSLProcessSubStatusId)
{
    global $my_id;

    $dbhConnection  = get_named_connection_with_db('common_adsl');

    $usiADSLErrorCodeId             = addslashes($usiADSLErrorCodeId);
    $vchADSLErrorCode               = addslashes($vchADSLErrorCode);
    $vchADSLErrorText               = addslashes($vchADSLErrorText);
    $bolADSLErrorShowCustomer = addslashes($bolADSLErrorShowCustomer);
    $usiADSLProcessSubStatusId      = addslashes($usiADSLProcessSubStatusId);

    if($usiADSLErrorCodeId == 0)
    {
        $strQuery       = 'INSERT INTO tblADSLErrorCode
                            (vchADSLErrorCode, vchADSLErrorText, bolADSLErrorShowCustomer, usiADSLProcessSubStatusId, vchUserId)
                        VALUES
                            ("'.$vchADSLErrorCode.'", "'.$vchADSLErrorText.'", "'.$vchADSLErrorTextNotForCustomer.'", "'.$usiADSLProcessSubStatusId.'", "'.$my_id.'")';
    }
    else
    {
        $strQuery       = 'UPDATE tblADSLErrorCode
                            SET vchADSLErrorCode          = "'.$vchADSLErrorCode.'",
                                vchADSLErrorText          = "'.$vchADSLErrorText.'",
                                bolADSLErrorShowCustomer  = "'.$bolADSLErrorShowCustomer.'",
                                usiADSLProcessSubStatusId = "'.$usiADSLProcessSubStatusId.'",
                                vchUserId                 = "'.$my_id.'"
                            WHERE usiADSLErrorCodeId        = "'.$usiADSLErrorCodeId.'"';
    }
    $resResult      = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Setting XML error code');

    if($usiADSLErrorCodeId == 0)
    {
        $usiADSLErrorCodeId = PrimitivesInsertIdGet($dbhConnection);
    }
    return $usiADSLErrorCodeId;
}



//////////////////////////////////////////
// Function    ADSLProvisionToolGroupSet
// Purpose     Adding or updating a provision tool group
// Arguments   usiADSLActionButtonGroupId - id of group to change, or 0 to add
//             vchADSLActionButtonGroup   - tool group name
// Returns     id of tool group
//////////////////////////////////////////
function ADSLProvisionToolGroupSet($usiADSLActionButtonGroupId, $vchADSLActionButtonGroup, $vchADSLActionButtonTag)
{
    global $my_id;

    $dbhConnection  = get_named_connection_with_db('common_adsl');

    $usiADSLActionButtonGroupId = addslashes($usiADSLActionButtonGroupId);
    $vchADSLActionButtonGroup   = addslashes($vchADSLActionButtonGroup);

    if($usiADSLActionButtonGroupId == 0)
    {
        $strQuery       = 'INSERT INTO vblADSLActionButtonGroup
                            (vchADSLActionButtonGroup, vchADSLActionButtonGroup, vchUserId)
                        VALUES
                            ("'.$vchADSLActionButtonGroup.'", "'.$vchADSLActionButtonTag.'", "'.$my_id.'")';
    }
    else
    {
        $strQuery       = 'UPDATE vblADSLActionButtonGroup
                            SET vchADSLActionButtonGroup   = "'.$vchADSLActionButtonGroup.'",
                                vchADSLActionButtonTag     = "'.$vchADSLActionButtonTag.'",
                                vchUserId                  = "'.$my_id.'"
                            WHERE usiADSLActionButtonGroupId = "'.$usiADSLActionButtonGroupId.'"';
    }
    $resResult      = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Setting Tool Group');

    if($usiADSLActionButtonGroupId == 0)
    {
        $usiADSLActionButtonGroupId = PrimitivesInsertIdGet($dbhConnection);
    }
    return $usiADSLActionButtonGroupId;
}



//////////////////////////////////////////
// Function    ADSLProvisionToolSet
// Purpose     Adding or updating a provision tool group
// Arguments   usiADSLActionButtonGroupId - id of group to change, or 0 to add
//             vchADSLActionButtonGroup   - tool group name
// Returns     id of tool group
//////////////////////////////////////////
function ADSLProvisionToolSet($usiADSLActionButtonId, $usiADSLActionButtonGroupId, $vchADSLActionButton, $vchADSLActionButtonURL, $vchADSLActionButtonPortalURL, $txtTracker, $bolADSLActionButtonNotForISDNUse, $bolADSLActionButtonNotForLowSpeed)
{
    global $my_id;

    $dbhConnection  = get_named_connection_with_db('common_adsl');

    $usiADSLActionButtonId         = addslashes($usiADSLActionButtonId);
    $usiADSLActionButtonGroupId   = addslashes($usiADSLActionButtonGroupId);
    $vchADSLActionButton           = addslashes($vchADSLActionButton);
    $vchADSLActionButtonURL        = addslashes($vchADSLActionButtonURL);
    $vchADSLActionButtonPortalURL = addslashes($vchADSLActionButtonPortalURL);
    $txtTracker                      = addslashes($txtTracker);

    if($usiADSLActionButtonId == 0)
    {
        $strQuery       = 'INSERT INTO tblADSLActionButton
                            (usiADSLActionButtonGroupId, vchADSLActionButton, vchADSLActionButtonURL, vchADSLActionButtonPortalURL, txtTracker, bolADSLActionButtonNotForISDNUse, bolADSLActionButtonNotForLowSpeed, vchUserId)
                        VALUES
                            ("'.$usiADSLActionButtonGroupId.'", "'.$vchADSLActionButton.'", "'.$vchADSLActionButtonURL.'", "'.$vchADSLActionButtonPortalURL.'", "'.$txtTracker.'", "'.$bolADSLActionButtonNotForISDNUse.'", "'.$bolADSLActionButtonNotForLowSpeed.'", "'.$my_id.'")';
    }
    else
    {
        $strQuery       = 'UPDATE tblADSLActionButton
                            SET usiADSLActionButtonGroupId        = "'.$usiADSLActionButtonGroupId.'",
                                vchADSLActionButton               = "'.$vchADSLActionButton.'",
                                vchADSLActionButtonURL            = "'.$vchADSLActionButtonURL.'",
                                vchADSLActionButtonPortalURL      = "'.$vchADSLActionButtonPortalURL.'",
                                txtTracker                        = "'.$txtTracker.'",
                                bolADSLActionButtonNotForISDNUse  = "'.$bolADSLActionButtonNotForISDNUse.'",
                                bolADSLActionButtonNotForLowSpeed = "'.$bolADSLActionButtonNotForLowSpeed.'",
                                vchUserId                         = "'.$my_id.'"
                            WHERE usiADSLActionButtonId             = "'.$usiADSLActionButtonId.'"';
    }
    $resResult      = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Setting Provision Tool');

    if($usiADSLActionButtonId == 0)
    {
        $usiADSLActionButtonId = PrimitivesInsertIdGet($dbhConnection);
    }
    return $usiADSLActionButtonId;
}



//////////////////////////////////////////
// Function    ADSLProvisionActionDealtWith
// Purpose     Adds an action to be dealt with
// Arguments   usiServiceId                Id of service to look for
//             usiADSLProcessSubStatusId   id of substatus to look for
//             strAdditionalNote           Additional note to add to Historical
// Returns     Nothing
//////////////////////////////////////////
function ADSLProvisionActionAdd($usiServiceId, $usiADSLProcessSubStatusId, $strAdditionalNote='')
{
    $dbhConnection  = get_named_connection_with_db('common_adsl');

    $usiServiceId              = addslashes($usiServiceId);
    $usiADSLProcessSubStatusId = addslashes($usiADSLProcessSubStatusId);

    $strQuery       = 'INSERT INTO tblADSLProcessAction
                        (usiServiceId, usiPartnerId, usiADSLProcessSubStatusId)
                    VALUES
                        ("'.$usiServiceId.'", "'.PARTNER_ID.'", "'.$usiADSLProcessSubStatusId.'")';
    $resResult      = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Adding ADSL process action');
    $arrAction = ADSLProvisionSubStatusGet($usiADSLProcessSubStatusId);
    ADSLProvisionHistoryAdd($usiServiceId, $usiADSLProcessSubStatusId, 'New Action <b>'.$arrAction[0]['vchADSLProcessSubStatusName']."</b> added\n$strAdditionalNote");
}



//////////////////////////////////////////
// Function    ADSLProvisionActionDealtWith
// Purpose     Sets an action to having been dealt with
// Arguments   usiServiceId                Id of service to look for
//             usiADSLProcessSubStatusId   id of substatus to look for
//             strAdditionalNote           Additional note to add to Historical
// Returns     Nothing
//////////////////////////////////////////
function ADSLProvisionActionDealtWith($usiServiceId, $usiADSLProcessSubStatusId, $strAdditionalNote='')
{
    global $my_id;

    $dbhConnection  = get_named_connection_with_db('common_adsl');

    $usiServiceId              = addslashes($usiServiceId);
    $my_id                     = addslashes($my_id);
    $usiADSLProcessSubStatusId = addslashes($usiADSLProcessSubStatusId);

    $strQuery       = 'UPDATE tblADSLProcessAction
                        SET dtmCompleted              = now(),
                            vchUserId                 = "'.$my_id.'"
                        WHERE usiServiceId              = "'.$usiServiceId.'"
                        AND usiPartnerId              = "'.PARTNER_ID.'"
                        AND usiADSLProcessSubStatusId = "'.$usiADSLProcessSubStatusId.'"
                        AND dtmCompleted              IS NULL';
    $resResult      = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Adding ADSL process history');
    $arrAction = ADSLProvisionSubStatusGet($usiADSLProcessSubStatusId);
    ADSLProvisionHistoryAdd($usiServiceId, $usiADSLProcessSubStatusId, 'Action <b>'.$arrAction[0]['vchADSLProcessSubStatusName']."</b> cleared\n$strAdditionalNote");
}



//////////////////////////////////////////
// Function     ADSLProvisionActionDealtWithByTool
// Purpose      Clears _all_ actions that are outstanding on a users account based on a tool being used
// Arguments    usiServiceId        - service id of account on which to work
//              arrTool             - id of tool that was used
//              strAdditionalNote   - text appended to standard service notice
// Returns      Nothing
//////////////////////////////////////////
function ADSLProvisionActionDealtWithByTool($usiServiceId, $mixTool, $strAdditionalNote='')
{
    global $my_id;
    $my_id = addslashes($my_id);

    if (is_array($mixTool))
    {
        $arrAction = ADSLGetOpenSubStatuses($usiServiceId, $mixTool);
        $strTool = $mixTool['vchADSLActionButton'];
    }
    else
    {
        $arrAction = ADSLGetOpenSubStatuses($usiServiceId);
        $strTool = $mixTool;
    }

    if(count($arrAction) > 0)
    {
        $arrName = ADSLClearActions($usiServiceId, $arrAction);

        if(count($arrName) > 0)
        {
            $strMessage = 'The following statuses have been cleared as a result of using the <b>'.$strTool."</b> tool\n<i>- ".implode("\n- ", $arrName)."</i>\n".$strAdditionalNote;
            ADSLProvisionHistoryAdd($usiServiceId, 0, $strMessage);
        }
    }
    elseif($strAdditionalNote != '')
    {
        ADSLProvisionHistoryAdd($usiServiceId, 0, '<b>'.$strTool."</b> has been used\n".$strAdditionalNote);
    }
}


//////////////////////////////////////////
// Function     ADSLGetOpenSubStatuses
// Purpose      Returns the open actions on an account optionally restricted by a tool
// Arguments    usiServiceId        - service id of account on which to work
//              arrTool             - optional id of tool by which to restrict actions
// Returns      Array of ADSLProcessSubStatusIds
//////////////////////////////////////////
function ADSLGetOpenSubStatuses($usiServiceId, $arrTool=NULL)
{
    $usiServiceId = addslashes($usiServiceId);
    if (isset($arrTool)) {
        $arrTool['usiADSLActionButtonId'] = addslashes($arrTool['usiADSLActionButtonId']);
    }

    // Firstly we need to fetch a list of sub statuses that are open
    $strQuery   = 'SELECT DISTINCT pa.usiADSLProcessSubStatusId
                    FROM tblADSLProcessAction   AS pa
                INNER JOIN tblSubStatusButtonLink  AS ssbl
                        ON ssbl.usiADSLProcessSubStatusId  = pa.usiADSLProcessSubStatusId
                INNER JOIN tblADSLActionButton     AS ab
                        ON ab.usiADSLActionButtonId          = ssbl.usiADSLActionButtonId
                INNER JOIN tblSubStatus            AS pss
                        ON pss.usiADSLProcessSubStatusId   = pa.usiADSLProcessSubStatusId ';

    $strWhere = '';
    // If specified, restrict actions to those that are a result of the the tool
    if (is_array($arrTool)) {
        $strWhere   .= ' WHERE ab.usiADSLActionButtonId        = "'.$arrTool['usiADSLActionButtonId'].'" ';
    }

    if (empty($strWhere)) {
        $strWhere = ' WHERE ';
    } else {
        $strWhere.= ' AND ';
    }

    $strQuery       .= $strWhere . ' pa.usiServiceId                 = "'.$usiServiceId.'"
                        AND pa.usiPartnerId                 = "' . PARTNER_ID . '"
                        AND pa.dtmCompleted                 IS NULL';
    $dbhConnection  = get_named_connection_with_db('common_adsl');
    $resResult  = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Retrieving open sub-statuses');
    return PrimitivesResultsAsArrayGet($resResult);
}


//////////////////////////////////////////
// Function     ADSLClearActions
// Purpose      Clears a list of actions
// Arguments    usiServiceId        - service id of account on which to work
//              arrAction        - array of ADSLProcessSubStatusIds to set as complete
// Returns      Array of names of actions cleared
//////////////////////////////////////////
function ADSLClearActions($usiServiceId, $arrAction)
{
    global $my_id;
    $my_id = addslashes($my_id);

    $arrName = array();

    foreach($arrAction as $intIndex => $arrSubStatus)
    {
        if(isset($arrSubStatus['usiADSLProcessSubStatusId']))
        {
            $arrStatus  = ADSLProvisionSubStatusGet($arrSubStatus['usiADSLProcessSubStatusId']);
            $arrName[]  = $arrStatus[0]['vchADSLProcessSubStatusName'];

            $strQuery   = 'UPDATE tblADSLProcessAction
                            SET dtmCompleted                    = now(),
                                vchUserId                       = "'.$my_id.'"
                            WHERE usiServiceId                  = "'.$usiServiceId.'"
                                AND usiPartnerId                = "'.PARTNER_ID.'"
                                AND usiADSLProcessSubStatusId   = "'.$arrSubStatus['usiADSLProcessSubStatusId'].'"
                                AND dtmCompleted                IS NULL';
            $dbhConnection  = get_named_connection_with_db('common_adsl');
            $resResult  = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Clearing sub-status');
        }
    }

    return $arrName;
}


/**
 * Adding an entry to the ADSL Order Process History for a specific account
 *
 * @param int    $usiServiceId              Service Id
 * @param int    $usiADSLProcessSubStatusId Sub Status Id
 * @param strin  $strProcessNotes           Process Notes
 * @param string $dtmDateTime               Date Time
 * @param string $vchUserId                 User Id
 * @param bool   $bolRaiseTicket            Raise Provisioning Ticket
 *
 * @return void
 */
function ADSLProvisionHistoryAdd(
    $usiServiceId, $usiADSLProcessSubStatusId, $strProcessNotes,
    $dtmDateTime = 'now()', $vchUserId='', $bolRaiseTicket = true
) {

    global $my_id;

    $strSource = 'Internal';
    if ($vchUserId == '') {

        if (!isset($my_id)) {

            // These must have been done by the customer
            $vchUserId = 0;
            $strSource = 'Portal';
        } else {

            $vchUserId = $my_id;
        }
    }

    if ($dtmDateTime != 'now()') {

        $dtmDateTime = '"'.$dtmDateTime.'"';
    }

    $usiServiceId                = addslashes($usiServiceId);
    $usiADSLProcessSubStatusId   = addslashes($usiADSLProcessSubStatusId);
    $strProcessNotes             = addslashes($strProcessNotes);

    // Get install diary status id for reporting purposes
    // we log the install diary status of the account every time we add a history item
    $dbhConnection  = get_named_connection_with_db('adsl');
    $strQuery = 'SELECT tAIDS.usiADSLInstallDiaryStatusId, tAIDS.vchADSLInstallDiaryStatusTag
                    FROM dbCommonADSL.tblADSLInstallDiaryStatus as tAIDS
            INNER JOIN install_diary AS id
                    ON id.status     = tAIDS.vchADSLInstallDiaryStatusTag
                WHERE id.service_id = "'.$usiServiceId.'"';

    $resResult                   = PrimitivesQueryOrExit(
        $strQuery, $dbhConnection, __FILE__.': Getting current install diary status id'
    );
    $arrResults                  = PrimitivesResultGet($resResult);
    $usiADSLInstallDiaryStatusId = $arrResults['usiADSLInstallDiaryStatusId'];

    $usiADSLInstallDiaryStatusId = addslashes($usiADSLInstallDiaryStatusId);

    // Now write the entry to the history table
    $dbhConnection  = get_named_connection_with_db('common_adsl');
    $strQuery       = 'INSERT INTO tblADSLProcess '
                    . ' (usiServiceId, usiPartnerId, usiADSLProcessSubStatusId,'
                    .  ' usiADSLInstallDiaryStatusId, txtProcessNotes, dtmDateTime, vchUserId) '
                    . ' VALUES '
                    .  ' ("'.$usiServiceId.'", "'.PARTNER_ID.'", "'.$usiADSLProcessSubStatusId
                    . '", "'.$usiADSLInstallDiaryStatusId.'", "'.$strProcessNotes.'", '
                    . $dtmDateTime.', "'.$vchUserId.'")';

    $resResult      = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Adding ADSL process history');

    if ($bolRaiseTicket === true) {

        // P 34235 - Single Provisioning Ticket
        // for now, only do this on ITW but would be trivial to enable this for PN
        if (defined('PARTNER_ID') && PARTNER_ID == 5) {

            $intTicketId = null;

            $intClassId = CClass::Retrieve('provisioning_tickets');
            $intTeamId = PHPLibTeamIdGetByName('BOT - New BB Order Placement');
            $vchADSLInstallDiaryStatusTag = $arrResults['vchADSLInstallDiaryStatusTag'];
            $arrEndStatus = array('active', 'cancelled');

            require_once '/local/data/mis/btri-database-access-common/database_libraries/regrade-access.inc';
            $arrRegrade = GetActiveRegradeDetails($usiServiceId);

            $intTicketId = empty($arrRegrade['intTicketID']) ? null : $arrRegrade['intTicketID'];

            if (empty($intTicketId)) {

                // see if there is an existing provisioning ticket
                $strQuery = 'SELECT tc.intTicketID, tc.intTicketClassID '.
                                'FROM tickets AS t '.
                                'INNER JOIN tblTicketClass AS tc '.
                                'ON t.ticket_id = tc.intTicketID '.
                                'WHERE t.service_id = "'.$usiServiceId.'" '.
                                'AND t.status = "Closed" '.
                                'AND bolCancelled = 0 '.
                                'AND tc.intClassID ="'.$intClassId.'" '.
                                'ORDER BY tc.intTicketID DESC';
                $dbhConnection  = get_named_connection_with_db('tickets');
                $resResult = PrimitivesQueryOrExit(
                    $strQuery, $dbhConnection, __FILE__.': Looking for provisioning ticket'
                );
                $arrResults = PrimitivesResultGet($resResult);

                $intTicketId = empty($arrResults['intTicketID']) ? null : $arrResults['intTicketID'];

            }
            // if there is no ticket add a new one
            if (empty($intTicketId)) {

                $intTicketId = tickets_ticket_add(
                    $strSource, $usiServiceId, 0, 0, 'Closed', $vchUserId,
                    "Provisioning of your Broadband Account Information\n".$strProcessNotes, 0, $intTeamId
                );
                // create the link to the ticket class
                $objTicketClass = CTicketClass::Create();
                $objTicketClass->SetAll(array('intClassID' => $intClassId, 'intTicketID'=> $intTicketId));
                $objTicketClass->Save();

            } else {

                // otherwise we add a contact to the existing ticket
                tickets_ticket_contacts_add(
                    $intTicketId, 0,
                    "Provisioning of your Broadband Account Information\n".$strProcessNotes,
                    0, 0, 0, '', $vchUserId, null, false
                );
            }
        } else {

            // Add a new service notice for every event
            tickets_ticket_add(
                $strSource, $usiServiceId, 0, 0, 'Closed', $vchUserId,
                "Provisioning of your ADSL Account Information\n".$strProcessNotes, 0
            );
        }
    }
}



//////////////////////////////////////////
// Function    ADSLProvisionSubStatusToolsSet
// Purpose     Adding or updating a provision tool group
// Arguments   usiADSLProcessSubStatusId - id of substatus to attach tools to
//             arrTool                   - array of tool ids to allow
// Returns     Nothing
//////////////////////////////////////////
function ADSLProvisionSubStatusToolsSet($usiADSLProcessSubStatusId, $arrTool)
{
    $dbhConnection  = get_named_connection_with_db('common_adsl');

    $usiADSLProcessSubStatusId = addslashes($usiADSLProcessSubStatusId);

    $strQuery       = 'DELETE FROM tblSubStatusButtonLink
                        WHERE usiADSLProcessSubStatusId = "'.$usiADSLProcessSubStatusId.'"';
    $resResult      = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Removing Tool links for Process Sub Status');
    if(count($arrTool) > 0)
    {
        foreach($arrTool as $intIndex => $usiADSLActionButtonId)
        {
            $usiADSLActionButtonId = addslashes($usiADSLActionButtonId);
            $strQuery       = 'INSERT INTO tblSubStatusButtonLink
                                (usiADSLActionButtonId, usiADSLProcessSubStatusId)
                            VALUES
                                ("'.$usiADSLActionButtonId.'", "'.$usiADSLProcessSubStatusId.'")';
            $resResult      = PrimitivesQueryOrExit($strQuery, $dbhConnection, __FILE__.': Adding Tool links for Process Sub Status');
        }
    }
}



///////////////////////////////////////////////////////////////////////////
//  Function   :  ADSLUpdateConfigurationValue
//  Purpose    :  To set an adsl configure value
//  Arguements :  $intADSLConfigToUpdate = Setting to be changed
//                $strNewValue           = The new value
//                $strPersonResponsible  = the auth_user.user_id
//  Returns    :  none
///////////////////////////////////////////////////////////////////////////
function ADSLUpdateConfigurationValue($intADSLConfigToUpdate, $strKeyName, $strValue, $strDescription='')
{
    global $my_id;

    $intADSLConfigToUpdate = addslashes($intADSLConfigToUpdate);
    $strKeyName            = addslashes($strKeyName);
    $strValue              = addslashes($strValue);
    $strPersonResponsible  = addslashes($my_id);
    $strDescription        = addslashes($strDescription);

    $dbhConnection = get_named_connection_with_db('common_adsl');

    //  Get the old value first
    $arrOldValue = ADSLGetConfigurationValues($intADSLConfigToUpdate);

    if(isset($arrOldValue['vchADSLConfigurationValue']))
    {
        $intADSLConfigId = $arrOldValue['intADSLConfigurationSettingId'];
        $strOldValue     = $arrOldValue['vchADSLConfigurationValue'];
        $strOldKey       = addslashes($arrOldValue['vchADSLConfigurationKey']);

        // Log the change if we have changed the value
        if($strOldValue != $strValue || $strOldKey != $strKeyName)
        {
            //  Log the change in the tblADSLConfigurationChangeLog
            $strQuery = 'INSERT into tblADSLConfigurationChangeLog '
                    .'       (intADSLConfigurationSettingId, vchChangedByUid, vchOldKeyName, vchOldValue, vchNewValue) '
                    .'VALUES '
                    ."       ('$intADSLConfigId', '$strPersonResponsible', '$strOldKey', '$strOldValue', '$strValue') ";

            PrimitivesQueryOrExit($strQuery ,$dbhConnection , 'Failed trying to log the change', 'true');
        }

        //  Actually make the change
        $strQueryUpdate = 'UPDATE tblADSLConfigurationSetting '
                        ."   SET vchADSLConfigurationKey         = '$strKeyName', "
                        ."       vchADSLConfigurationValue       = '$strValue', "
                        ."       txtADSLConfigurationDescription = '$strDescription', "
                        .'       vchDbSrc                        = "$Id: common-adsl-access.inc,v ******** 2009-07-15 12:33:03 rpvava Exp $"'
                        ." WHERE intADSLConfigurationSettingId   = '$intADSLConfigToUpdate' ";
    }
    else
    {
        $strQueryUpdate = 'INSERT INTO tblADSLConfigurationSetting
                            (vchADSLConfigurationKey, vchADSLConfigurationValue, txtADSLConfigurationDescription, vchDbSrc)
                        VALUES
                            ("'.$strKeyName.'", "'.$strValue.'", "'.$strDescription.'", "$Id: common-adsl-access.inc,v ******** 2009-07-15 12:33:03 rpvava Exp $")';
    }

    PrimitivesQueryOrExit($strQueryUpdate ,$dbhConnection , 'Failed trying to Update the value', 'true');

}  //  END ADSLUpdateConfigurationValue



///////////////////////////////////////////////////////////////////////////
//  function   :  ADSLlogPreRegDetails
//  purpose    :  to log the details from the prereg process
//  arguements :  $arrSignupDetails
//  returns    :  none
///////////////////////////////////////////////////////////////////////////
function ADSLlogPreRegDetails($arrSignupDetails)
{

    //  addslashes
    $intServiceId           = addslashes($arrSignupDetails['service_id']);
    $vchTitle               = addslashes($arrSignupDetails['salutation']);
    $vchForename            = addslashes($arrSignupDetails['forenames']);
    $vchSurname             = addslashes($arrSignupDetails['surname']);
    $vchHouse               = addslashes($arrSignupDetails['house']);
    $vchStreet              = addslashes($arrSignupDetails['street']);
    $vchTown                = addslashes($arrSignupDetails['town']);
    $vchCounty              = addslashes($arrSignupDetails['county']);
    $vchPostcode            = str_replace(' ', '', addslashes($arrSignupDetails['postcode']) );
    $vchCLI                 = str_replace(' ', '', addslashes($arrSignupDetails['cli_number']) );
    $vchPhone               = str_replace(' ', '', addslashes($arrSignupDetails['telephone']) );
    $intSdi                 = addslashes($arrSignupDetails['intPreRegADSLAccount']);
    $vchEmail               = addslashes($arrSignupDetails['email']);
    $strIsp                 = addslashes($arrSignupDetails['strIsp']);
    $intExchangeId          = addslashes($arrSignupDetails['intExchangeId']);
    $bolRegisteredWithBt    = addslashes($arrSignupDetails['bolRegisteredWithBt']);
    $bolPreRegSimpleProcess = addslashes($arrSignupDetails['bolPreRegSimpleProcess']);
    $bolOnePoundDomain      = isset($arrSignupDetails['domainoption']) && $arrSignupDetails['domainoption'] == 'yes' ? '1' : '0';
    $bolIsISDN              = isset($arrSignupDetails['isdn']) && $arrSignupDetails['isdn'] == 'yes' ? '1' : '0' ;

    if (isset($arrSignupDetails['deliver_to_home']) && $arrSignupDetails['deliver_to_home'] == '1')
    {

        $strHardwareHouse    = $vchHouse;
        $strHardwareStreet   = $vchStreet;
        $strHardwareTown     = $vchTown;
        $strHardwareCounty   = $vchCounty;
        $strHardwarePostcode = $vchPostcode;

    }
    else
    {

        if ($arrSignupDetails['delivery_house'] != ''   && $arrSignupDetails['delivery_street'] != ''
            && $arrSignupDetails['delivery_town'] != '' && $arrSignupDetails['delivery_county'] != ''
            && $arrSignupDetails['delivery_postcode'] != '')
        {

            $strHardwareHouse    = isset($arrSignupDetails['delivery_house'])    ? addslashes($arrSignupDetails['delivery_house'])    : '' ;
            $strHardwareStreet   = isset($arrSignupDetails['delivery_street'])   ? addslashes($arrSignupDetails['delivery_street'])   : '' ;
            $strHardwareTown     = isset($arrSignupDetails['delivery_town'])     ? addslashes($arrSignupDetails['delivery_town'])     : '' ;
            $strHardwareCounty   = isset($arrSignupDetails['delivery_county'])   ? addslashes($arrSignupDetails['delivery_county'])   : '' ;
            $strHardwarePostcode = isset($arrSignupDetails['delivery_postcode']) ? addslashes($arrSignupDetails['delivery_postcode']) : '' ;

        }

    }

    if (isset($arrSignupDetails['arrPaymentDetails']['strPaymentType']) == true )
    {
/*
        //  Get the payment options
        $dbhConnection = get_named_connection_with_db('common_adsl');

        $strQueryPaymentOptions = 'SELECT usiPaymentMethodId, vchPaymentMethod '
                                .'  FROM vblADSLPreRegPaymentMethod ';

        $resResult = PrimitivesQueryOrExit($strQueryPaymentOptions ,$dbhConnection, 'Failed trying to retrieve PaymentDetails', false);

        $arrPaymentMethods = PrimitivesResultsAsArrayGet($resResult, 'vchPaymentMethod');
*/
        $strPaymentType     = addslashes($arrSignupDetails['arrPaymentDetails']['strPaymentType']);
        $strInvoicePeriod   = addslashes($arrSignupDetails['arrPaymentDetails']['strInvoicePeriod']);

//            $strPaymentMethodId = $arrPaymentMethods[$strPaymentType]['usiPaymentMethodId'];

        if ($strPaymentType == 'cc' || $strPaymentType == 'combo')
        {

            $strCCPaymentMethod = addslashes($arrSignupDetails['arrPaymentDetails']['strCCPaymentMethod']);
            $strCCCardNumber    = addslashes($arrSignupDetails['arrPaymentDetails']['strCCCardNumber']);
            $strCCCardHolder    = addslashes($arrSignupDetails['arrPaymentDetails']['strCCCardHolder']);
            $strCCStartMonth    = str_pad(addslashes($arrSignupDetails['arrPaymentDetails']['strCCStartMonth']), 2, '0', STR_PAD_LEFT);
            $strCCStartYear     = addslashes($arrSignupDetails['arrPaymentDetails']['strCCStartYear']);
            $strCCEndMonth      = str_pad(addslashes($arrSignupDetails['arrPaymentDetails']['strCCEndMonth']), 2, '0', STR_PAD_LEFT);
            $strCCEndYear       = addslashes($arrSignupDetails['arrPaymentDetails']['strCCEndYear']);
            $strCCStartMonth    = addslashes($arrSignupDetails['arrPaymentDetails']['strCCStartMonth']);

            if ($strPaymentType == 'cc')
            {
                $strDDForename      = '';
                $strDDSurname       = '';
                $strDDSortCode      = '';
                $strDDAccNumber     = '';
            }

        }

        if ($strPaymentType == 'dd' || $strPaymentType == 'combo')
        {

            $strDDForename    = addslashes($arrSignupDetails['arrPaymentDetails']['strDDForename']);
            $strDDSurname     = addslashes($arrSignupDetails['arrPaymentDetails']['strDDSurname']);
            $strDDSortCode    = addslashes($arrSignupDetails['arrPaymentDetails']['strDDSortCode']);
            $strDDAccNumber   = addslashes($arrSignupDetails['arrPaymentDetails']['strDDAccNumber']);

            if ($strPaymentType == 'dd')
            {
                $strCCPaymentMethod = '';
                $strCCCardNumber    = '';
                $strCCCardHolder    = '';
                $strCCStartMonth    = '';
                $strCCStartYear     = '';
                $strCCEndMonth      = '';
                $strCCEndYear       = '';
                $strCCStartMonth    = '';
            }

        }

    }

    $dbhConnection = get_named_connection_with_db('common_adsl');

    $strQuery = 'INSERT INTO tblADSLPreRegInterest '
            .'            (intServiceId, vchTitle, vchForename, vchSurname, vchHouse, vchStreet, '
            .'             vchTown, vchCounty, vchPostcode, vchCLI, vchPhone, intSdi, vchEmail, '
            .'             strIsp, intExchangeId, bolRegisteredWithBt, bolPreRegSimpleProcess, '
            .'             bolIsISDN, bolOnePoundDomain, '
            .'             dtmDatePreRegistered,  vchDBSrc) '
            .' VALUES '
            ."            ('$intServiceId', '$vchTitle', '$vchForename', '$vchSurname', '$vchHouse', '$vchStreet', "
            ."             '$vchTown', '$vchCounty', '$vchPostcode', '$vchCLI', '$vchPhone', '$intSdi', '$vchEmail', "
            ."            '$strIsp', '$intExchangeId', '$bolRegisteredWithBt', '$bolPreRegSimpleProcess', "
            ."            '$bolIsISDN', '.$bolOnePoundDomain', "
            .'             now(), "$Id: common-adsl-access.inc,v ******** 2009-07-15 12:33:03 rpvava Exp $" ) ';

    $resResult = PrimitivesQueryOrExit($strQuery ,$dbhConnection, 'Failed trying to store details', false);

    $intInsertId = PrimitivesInsertIdGet($dbhConnection);

    //  Also store the payment details
    //  Payment details are not taken if it is a simple prereg
    if ($bolPreRegSimpleProcess == '0')
    {
        //  Get the numerical value for the payment Method
        $arrPaymentMethod = ADSLPreRegGetPaymentMethod();
        $strPaymentMethod = $arrSignupDetails['arrPaymentDetails']['strCCPaymentMethod'];
        $intPaymentMethodId = $arrPaymentMethod[strtolower($strPaymentMethod)]['usiPaymentMethodId'];

        //  Get the numerical value for the payment type
        $arrPaymentType = ADSLPreRegGetPaymentType();
        $intPaymentTypeId = $arrPaymentType[$strPaymentType]['usiPaymentTypeId'];

        $strQueryPayment = 'INSERT INTO tblADSLPreRegPaymentDetails '
                        .'            (intPreRegId, intServiceId, usiPaymentMethodId, usiPaymentTypeId, vchCCNumber, dteCCStart, '
                        .'             dteCCEnd, vchCCName, vchDDSortCode, vchDDAccNumber, '
                        .'             vchDDForename, vchDDSurname ) '
                        .'      VALUES '
                        ."            ('$intInsertId', '$intServiceId', '$intPaymentMethodId', '$intPaymentTypeId', '$strCCCardNumber', '$strCCStartYear-$strCCStartMonth-00', "
                        ."             '$strCCEndYear-$strCCEndMonth-00', '$strCCCardHolder', '$strDDSortCode', '$strDDAccNumber', "
                        ."             '$strDDForename', '$strDDSurname' ) ";

        PrimitivesQueryOrExit($strQueryPayment, $dbhConnection, 'Failed trying to store payment details', false);

    }

    //  Store hardware delivery address
    if (isset($strHardwareHouse) && $strHardwareHouse != '')
    {

        $strQueryHardware = 'INSERT INTO tblADSLPreRegHardwareAdress '
                        .'            (intPreRegId, vchHouse, vchStreet, '
                        .'             vchTown, vchCounty, vchPostcode )'
                        ."     VALUES ('$intInsertId', '$strHardwareHouse', '$strHardwareStreet', "
                        ."             '$strHardwareTown', '$strHardwareCounty', '$strHardwarePostcode') ";

        PrimitivesQueryOrExit($strQueryHardware, $dbhConnection, 'Failed trying to store the hardware address');

    }

}  //  END ADSLlogPreRegDetails

function ADSLPreRegGetPaymentMethod()
{
    $dbhConnection = get_named_connection_with_db('common_adsl');

    $strQuery = 'SELECT usiPaymentMethodId, vchPaymentMethod '
            .'  FROM vblADSLPreRegPaymentMethod ';

    $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

    $arrPaymentMethod = PrimitivesResultsAsArrayGet($resResult, 'vchPaymentMethod');

    return $arrPaymentMethod;
}  //  END func ADSLPreRegGetPaymentMethod

function ADSLPreRegGetPaymentType()
{
    $dbhConnection = get_named_connection_with_db('common_adsl');

    $strQuery = 'SELECT usiPaymentTypeId, vchPaymentType '
            .'  FROM vblADSLPreRegPaymentType ';

    $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

    $arrPaymentType = PrimitivesResultsAsArrayGet($resResult, 'vchPaymentType');

    return $arrPaymentType;
}  //  END func ADSLPreRegGetPaymentType

////////////////////////////////////////////////////////////////////////////
//  Function   :  ADSLPreRegPaymentDetails
//  Purpose    :
//  Arguements :  $intPreRegId
//  Returns    :  $arrPaymentDetails - the payment details
////////////////////////////////////////////////////////////////////////////
function ADSLPreRegPaymentDetails($intPreRegId)
{
    $intPreRegId = addslashes($intPreRegId);

    $dbhConnection = get_named_connection_with_db('common_adsl');

    $strQuery = 'SELECT usiPaymentDetailsId, intPreRegId, intServiceId, '
            .'          vchPaymentMethod, vchPaymentType, vchCCNumber, '
            .'          dteCCStart, dteCCEnd, vchCCName, '
            .'          usiCCIssueNumber, vchDDSortCode, vchDDAccNumber,'
            .'          vchDDForename, vchDDSurname, usiInvoicePeriodId '
            .'     FROM tblADSLPreRegPaymentDetails AS pd '
            .'    INNER JOIN vblADSLPreRegPaymentMethod AS pm '
            .'       ON pd.usiPaymentMethodId=pm.usiPaymentMethodId'
            .'    INNER JOIN vblADSLPreRegPaymentType AS pt '
            .'       ON pd.usiPaymentTypeId=pt.usiPaymentTypeId '
            ."    WHERE pd.intPreRegId='$intPreRegId' ";

    $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

    $arrPaymentDetails = PrimitivesResultsAsArrayGet($resResult);

    return $arrPaymentDetails[0];
}  //  END  func ADSLPreRegPaymentDetails

////////////////////////////////////////////////////////////////////////////
//  Function   :  ADSLPreRegHardwareDetails
//  Purpose    :
//  Arguements :  $intPreRegId
//  Returns    :  $arrHardwareDetails - thehardware details
////////////////////////////////////////////////////////////////////////////
function ADSLPreRegHardwareDetails($intPreRegId)
{
    $intPreRegId = addslashes($intPreRegId);

    $dbhConnection = get_named_connection_with_db('common_adsl');

    $strQuery = 'SELECT usiHardwareAddressId, intPreRegId, vchHouse,  '
            .'       vchStreet, vchTown, vchCounty, '
            .'       vchPostcode '
            .'FROM   tblADSLPreRegHardwareAdress AS ph '
            ."WHERE  ph.intPreRegId='$intPreRegId' ";

    $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

    $arrHardwareDetails = PrimitivesResultsAsArrayGet($resResult);

    return $arrHardwareDetails[0];
}  //  END  func ADSLPreRegHardwareDetails

////////////////////////////////////////////////////////////////////////////
//  function  :  ADSLPreRegDetailsUpdate
//  Purpose   :  to alter the account that the prereg wants
//  Arguments :  intNewUpgradeAccount - service_definition_id for new upgrade
//               intPreRegId          - pre reg details to change
//  Returns   :  bolUpdated           - true/false
////////////////////////////////////////////////////////////////////////////
function ADSLPreRegDetailsUpdate($intNewUpgradeAccount, $intPreRegId)
{
    $intNewUpgradeAccount = addslashes($intNewUpgradeAccount);
    $intPreRegId          = addslashes($intPreRegId);

    $bolUpdated = true;

    $dbhConnection = get_named_connection_with_db('common_adsl');

    $strQuery = 'UPDATE tblADSLPreRegInterest '
            ."SET    intSdi='$intNewUpgradeAccount' "
            ."WHERE  intPreRegId='$intPreRegId' ";

    if(PrimitivesQueryOrExit($strQuery, $dbhConnection) === false)
    {
        $bolUpdated = false;
    }

    return $bolUpdated;
}  //  END  func ADSLPreRegDetailsUpdate



////////////////////////////////////////////
// Function     ADSLUpdateWorkLog
// Purpose      Either adds an entry for the current service_id/partner_id combination or closes one
//              Also adds a history event
// Arguments    usiServiceId    Service id to add/close
// Return       nadda, zip, nowt
////////////////////////////////////////////
function ADSLWorkLogUpdate($usiServiceId)
{
    global $my_id;

    $dbhConnection = get_named_connection_with_db('common_adsl');
    $usiServiceId  = addslashes($usiServiceId);

    if(is_array(ADSLWorkLogIsAccountBeingWorkedOn($usiServiceId)) === true)
    {
        ADSLProvisionHistoryAdd($usiServiceId, 0, 'Finished work on account', 'now()', '', false);
        $strQuery      = 'UPDATE tblUserWorkLog
                            SET dtmEnd       = now(),
                                vchEndUserId = "'.$my_id.'"
                        WHERE usiServiceId = "'.$usiServiceId.'"
                            AND usiPartnerId = "'.PARTNER_ID.'"
                            AND dtmEnd       IS NULL';
    }
    else
    {
        ADSLProvisionHistoryAdd($usiServiceId, 0, 'Started work on account', 'now()', '', false);
        $strQuery      = 'INSERT INTO tblUserWorkLog
                            (usiServiceId, usiPartnerId, vchStartUserId, dtmStart)
                        VALUES
                            ("'.$usiServiceId.'", "'.PARTNER_ID.'", "'.$my_id.'", now())';

        // Clear session variables relating to house move product changes
        // in case someone else has changed the products in product change and we still have an old copy saved
        unset($_SESSION['HouseMove_ProductChange_'.$usiServiceId.'_arrSelectedProducts']);
    }
    PrimitivesQueryOrExit($strQuery, $dbhConnection, 'Updating/inserting into adsl work log');
}


////////////////////////////////////////////////////////////////////////////////
//  Function  :  adslGetAccountHardware
//  Purpose   :  Returns details about the hardware the account type has
//  Arguments :  $intServiceDefinitionId - the account type we are interested in
//  Return    :  $arrAccountHardware     - the hardware details
////////////////////////////////////////////////////////////////////////////////
function adslGetAccountHardware($intServiceDefinitionId, $bolSignUpCall = FALSE)
{
    $intServiceDefinitionId = addslashes($intServiceDefinitionId);

    $dbhConnection = get_named_connection_with_db('product');

    $strQuery = ' SELECT scc.service_component_id  '
            .'      FROM service_component_config AS scc '
            .'INNER JOIN component_hardware_bundle_config AS chbc '
            .'        ON scc.service_component_id=chbc.service_component_id '
            ."     WHERE scc.service_definition_id='$intServiceDefinitionId' ";

    if ($bolSignUpCall) {

        $strQuery = $strQuery. 'AND scc.default_quantity > 0';

    }
    $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

    $arrAccountHardware = PrimitivesResultsAsArrayGet($resResult);

    return $arrAccountHardware[0];

}  //  END func adslGetAccountHardware



function ADSLWorkLogTidyUp($intMinutesOfInactivity, $vchUserId)
{
    global $my_id;

    $dbhConnection            = get_named_connection_with_db('common_adsl');
    $intMinutesOfInactivity  = addslashes($intMinutesOfInactivity);
    $vchUserId                 = addslashes($vchUserId);

    // We need to set my_id so that HistoryAdd will worked, and hence is global at the start of this function
    $tmpmy_id                   = $my_id;
    $my_id                      = $vchUserId;

    // So that we can create reasonable Historys for the accounts, we need to perform this system by
    // SELECT, then by UPDATE
    $strQuery = 'SELECT usiServiceId, intUserWorkLogId
                    FROM tblUserWorkLog
                WHERE dtmEnd IS NULL
                    AND vchEndUserId IS NULL
                    AND dtmStart < date_sub(now(), interval "'.$intMinutesOfInactivity.'" minute)';

    $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection, 'Fetching outstanding work log entries');
    $arrResult = PrimitivesResultsAsArrayGet($resResult);

    foreach($arrResult as $intIndex => $arrWorkLog)
    {
        $strQuery = 'UPDATE tblUserWorkLog
                        SET dtmEnd = now(),
                            vchEndUserId = "'.$vchUserId.'"
                    WHERE intUserWorkLogId = "'.$arrWorkLog['intUserWorkLogId'].'"';
        PrimitivesQueryOrExit($strQuery, $dbhConnection, 'Tidying up outstanding work log entry '.$arrWorkLog['intUserWorkLogId']);
        ADSLProvisionHistoryAdd($arrWorkLog['usiServiceId'], 0, 'Finished work on account', 'now()', '', false);
    }
    // Put my_id back to what it should be
    $my_id = $tmpmy_id;
}

function ADSLWorkLogStopWorkByUser($intMinutesOfInactivity, $vchUserId)
{
    $dbhConnection            = get_named_connection_with_db('common_adsl');
    $intMinutesOfInactivity  = addslashes($intMinutesOfInactivity);
    $vchUserId                 = addslashes($vchUserId);

    // We need to set my_id so that HistoryAdd will worked
    $my_id                      = $vchUserId;

    // So that we can create reasonable Historys for the accounts, we need to perform this system by
    // SELECT, then by UPDATE
    $strQuery = 'SELECT usiServiceId, intUserWorkLogId
                    FROM tblUserWorkLog
                WHERE dtmEnd IS NULL
                    AND vchEndUserId IS NULL
                    AND dtmStart < date_sub(now(), interval "'.$intMinutesOfInactivity.'" minute)';
    $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection, 'Fetching outstanding work log entries');
    $arrResult = PrimitivesResultsAsArrayGet($resResult);

    foreach($arrResult as $intIndex => $arrWorkLog)
    {
        $strQuery = 'UPDATE tblUserWorkLog
                        SET dtmEnd = now(),
                            vchEndUserId = "'.$vchUserId.'"
                    WHERE intUserWorkLogId = "'.$arrWorkLog['intUserWorkLogId'].'"';
        PrimitivesQueryOrExit($strQuery, $dbhConnection, 'Tidying up outstanding work log entry '.$arrWorkLog['intUserWorkLogId']);
        ADSLProvisionHistoryAdd($arrWorkLog['usiServiceId'], 0, 'Finished work on account', 'now()', '', false);
    }
}


/**
 * Method to clear the appointment dates stored in the install diary
 *
 * Clears the date/time slots for the service Id in the install diary
 *
 * <AUTHOR> Walton, <<EMAIL>>
 * @param int $intServiceId Service Id
 *
 * @return bool - success or failure
 */
function ADSLInstallDiaryClearAppointmentDates($intServiceId)
{
    if (!preg_match('/^[0-9]+$/', $intServiceId)) {

        return false;
    }

    $dbhConnection = get_named_connection_with_db('adsl');

    $strQuery = 'UPDATE install_diary '.
                " SET date_1   = '0000-00-00', ".
                    " date_2   = '0000-00-00', ".
                    " date_3   = '0000-00-00', ".
                    ' intTimeSlot1 = NULL, '.
                    ' intTimeSlot2 = NULL, '.
                    ' intTimeSlot3 = NULL '.
                " WHERE service_id = $intServiceId ";


    return PrimitivesQueryOrExit($strQuery, $dbhConnection, "Clearing install_diary appointment dates for $intServiceId");
}//end of function: ADSLInstallDiaryClearAppointmentDates()




function ADSLInstallDiarySetAppointmentDates($usiServiceId, $intDate1, $intTime1, $intDate2, $intTime2, $intDate3, $intTime3)
{
    // Make the dates passed actual dates
    $strDate1 = date('Y-m-d', $intDate1 * 60 * 60 *24);
    $strDate2 = date('Y-m-d', $intDate2 * 60 * 60 *24);
    $strDate3 = date('Y-m-d', $intDate3 * 60 * 60 *24);

    $dbhConnection = get_named_connection_with_db('adsl');

    $strQuery = 'UPDATE install_diary
                    SET date_1   = "'.$strDate1.'",
                        date_2   = "'.$strDate2.'",
                        date_3   = "'.$strDate3.'",
                        intTimeSlot1 = "'.$intTime1.'",
                        intTimeSlot2 = "'.$intTime2.'",
                        intTimeSlot3 = "'.$intTime3.'"
                WHERE service_id = "'.$usiServiceId.'"';
    PrimitivesQueryOrExit($strQuery, $dbhConnection, 'Updating install_diary appointment dates');
}


function ADSLInstallDiarySetInstallDate($usiServiceId, $intDate, $intTime, $intIndex = 1)
{
    // Make the date passed an actual date
    $strDate = date('Y-m-d', $intDate * 60 * 60 *24);

    ADSLInstallDiarySetInstallDateTime($usiServiceId, new DateTime($strDate), $intTime, $intIndex);
}

/**
 * @param int      $usiServiceId
 * @param DateTime $date
 * @param int      $intTime      Timeslot: 1 or 2 for AM, 3 or more for PM
 * @param int      $intIndex
 */
function ADSLInstallDiarySetInstallDateTime($usiServiceId, DateTime $date, $intTime, $intIndex = 1)
{
    $dbhConnection = get_named_connection_with_db('adsl');

    $strQuery = "UPDATE install_diary
                    SET date_{$intIndex}       = '".$date->format("Y-m-d")."',
                        intTimeSlot{$intIndex} = '".$intTime."'
                    WHERE service_id = '".$usiServiceId."'";
    PrimitivesQueryOrExit($strQuery, $dbhConnection, 'Updating install_diary appointment dates');
}

/////////////////////////////////////////
// Function   ADSLSendActivationEmail
// Purpose    Email the customer that their adsl is now active
//            also adds a Question to the account
// Arguments  $service_id,
//            $visp,
//            $strDebug
// Returns    none
/////////////////////////////////////////
function ADSLSendActivationEmail($service_id, $visp, $strDebug='')
{
    global $visp_array;

    // if it's Remote Internet, then use the RI specific mailing system
    if(function_exists('remoteinet_ADSLSendActivationEmail'))
    {
        return remoteinet_ADSLSendActivationEmail($service_id, $visp_array, $strDebug);
    }

    // get information about users service and user
    $service_array = userdata_service_get($service_id);
    $arrTemplateVars = array(
                'intClientId'    => $service_id,
                'strPhoneNo'     => $service_array['cli_number'],
                'strDslUsername' => adslGetUserRealm($service_id));

    $arrProductDetails = userdataServiceProductGet($service_id);
    // get info about product
    $arrProduct = product_get_account_attributes($service_id);

    // Get the hardware bundle object for the passed Service ID
    $HardwareBundle = HardwareBundleControl::GetHardwareBundleByServiceID($service_id);

    if($HardwareBundle) {

        $bolTr069 = $HardwareBundle->isTr069Compatible();
    } else {

        $bolTr069 = false;
    }

    if (Core_Service::isJlpVisp($visp)) {
        $strEmailTemplate = UserdataUserMigratingISP($service_id) ? 'broadband_migration_complete' : 'broadband_gone_live';

        $arrTemplateVars = array(
                'intClientId'    => $service_id,
                'strPhoneNo'     => $service_array['cli_number'],
                'strDslUsername' => adslGetUserRealm($service_id),
                'bolTr069'       => $bolTr069
            );

        EmailHandler::sendEmail($service_id, $strEmailTemplate, $arrTemplateVars);
        return;
    }

    if (count($visp_array) == 0)
    {
        error_log('DIE! function ADSLSendActivationEmail - $visp_array isn\'t set . Cannot continue sending mail .');
        return;
    }

    $template = new Template('/local/data/mis/portal_modules/mailtemplates/adsl_activation');

    //  Check that we are using the origional service_id and not the upgrade one
    $service_id = ADSLGetMainAccountServiceId($service_id);

    $product_array = product_get_service($service_array['type']);
    $user_array    = userdata_user_get($service_array['user_id']);
    $product_attributes = product_get_account_attributes($service_array['type']);
    $bolMigratedUser = UserdataUserMigratingISP($service_id);
    $contractEnd = null;
    $component = CProduct::getProductByProductTypeHandle($service_id, 'INTERNET_CONNECTION');
    if (!empty($component)) {
        foreach($component->getProductComponents() as $product) {
            if($product instanceof CProductComponentSubscription) {
                $contractEnd = $product->getContractEnd();
            }
        }
        if ($contractEnd != null) {
            $strContractEndDate = date('d-m-y', $contractEnd);
        }
    }
    $isp           = $product_array['isp'];

    $arrVispConfig = product_visp_config_get($isp);

    $name          = ucfirst(strtolower($user_array['forenames'])).' '.ucfirst(strtolower($user_array['surname']));
    $account       = $product_array['name'];
    $username      = $service_array['username'];
    $brand         = $visp_array[$isp]['brand'];
    $period        = $service_array['invoice_period'];
    $portal        = $visp_array[$isp]['url'];
    $strapline     = $visp_array[$isp]['strapline'];
    //  You should not use this, you should be using $realm below as it is from the db
    $vchAdslLogin  = $visp_array[$isp]['adsl_login'];

    // get dialup details for service. Returned array contains details of all active dialup components
    $radius_array  = radius_get_dialup_number_details(array(),$service_id);

    $realm='';
    $strAdslUsername = '';

    //Try and get the realm using the components on the account
    $arrRealm = radius_get_realm_by_service_id($service_id);

    //If we were able to get a realm through the componets then set it
    if(isset($arrRealm['realm']))
    {
        if (strpos($arrRealm['realm'], '@') === false) {
            $realm = '@'.$arrRealm['realm'];
        } else {
            $realm = '-'.$arrRealm['realm'];
        }
    }

    // if the realm is not set, get it from the install diary
    // currently only applicable for two vISPs, but might be possible for
    // general use
    //
    // Added plus.net as there are issues with geting realm for business users,
    // this seems to work :)
    $arrIsps = array('madasafish', 'partner','plus.net');
    if ($realm == '' && in_array($isp, $arrIsps))
    {
        $realm = productGetAdslRealmFromIsp($service_array['isp']);
        if (strpos($realm, '@') === false) {
            $realm = '@'.$realm;
        } else {
            $realm = '-'.$realm;
        }
        $strAdslUsername = adslGetUserRealm($service_id);
    }

    if('partner' == $isp) {

        $arrADSLInstallDiary = get_adsl_application_details($service_id, true);
        $arrVispConfig['vchVispRealm'] = $arrADSLInstallDiary['strRealmProvisioned'];
    }

    // Problem 30628 - Metronet Realms. Changed the 'if' statement so that if it is a Metronet
    // product, it should try and determine the realm from the data returned from radius_get_dialup_number_details()

    // This is 99.9% identical to how the Metronet portal now aquires the realm information (see Problem 30606 about that)
    // so rather than copy-paste that solution here, I think it is far more elegant to just determine if we're
    // Metronet and reuse data that has just been collected.

    // It is assumed that this code will only be executed *after* the ADSL Dialup component has been configured
    // and activated.

    if($realm == '' || $isp == 'metronet')        // if all else fails, or if we're Metronet
    {
        foreach($radius_array as $each_group)
        {
            if( ( ($each_group['com_method']      == 'ADSL/RDSL') &&
                ($each_group['reason_included'] =='ActiveComponentOnAccount') )
            || ( ($each_group['com_method']      == 'ADSL/RDSL') &&
                    ($each_group['reason_included'] == 'DefaultComponent') ) )
            {
                $realm = '@'.$each_group['realm'];

                break;
            }
        }
    }

    if($realm=="")
    {
        // Only require the programme tool at this point, we only need one function from it.
        require_once(PROGRAMME_TOOL_ACCESS_LIBRARY);

        pt_raise_autoproblem('adsl_activation_realm_unknown', 'Unknown ADSL realm for automatically activated account',
                            "activate_accounts_plusnet.php was unable to find The realm of any active ADSL component on the account belonging to service_id '$service_id'\n The most likely cause is that RADIUS is unavailable or mis configuration existed between RADIUS or the service_components. This customer has not been mailed.");

        return;
    }

    //Figure out if this is a migration special offer.
    $resObj = MigrationPromotionIsValidForService($service_id);
    if (is_array($resObj) && isset($resObj['error']) && $resObj['error'] == 'true')
    {
        if ($resObj['code'] != 1702)
        {
            error_log("Error called from $PHP_SELF line number: ".__LINE__."\nError returned from object: ".$resObj['info']."\nError Code: ".$resObj['code']."\nError Type: ".$resObj['type']."\nError Message: ".$resObj['msg']."No Service ID is available as this error was caused during signup.");
            page_redirect("error.html");
        }
        else
        {
            $resObj = false;
        }
    }




    if (Core_Service::isPartnerVisp($isp) && ($product_attributes['vchProvisioningProfile'] == 'FTTC')) {

	   $strEmailTemplate  = 'partner_fibre_active';
        $arrTemplateVars = array(
            'cli' => $service_array['cli_number'],
            'adslrealm' => $arrVispConfig['vchVispRealm'],
            'productname' => $product_attributes['product_name'],
            'strDslUsername' => adslGetUserRealm($service_id)
        );
        EmailHandler::sendEmail($service_id, $strEmailTemplate, $arrTemplateVars);
        return;

    } elseif (Core_Service::isPartnerVisp($isp)) {
        $strEmailTemplate = 'active_adsl';

        $arrTemplateVars = array(
            'cli' => $service_array['cli_number'],
            'adslrealm' => $arrVispConfig['vchVispRealm'],
            'productname' => $product_attributes['name']
        );

        EmailHandler::sendEmail($service_id, $strEmailTemplate, $arrTemplateVars);
        return;

    } elseif ($resObj === true) {

        $template->set_file('main', 'selfinstall_activation.txt');
        /**
         * Addition for Migration Promotion Project (PIT Project 600) Sept 2005
         * Code to display promotion details in adsl activation email
         **/

        $template->set_block('main', 'migration_offer', 'migration_offer_block');
        $template->set_var('migration_offer_block',  '');

        $template->parse('migration_offer_block', 'migration_offer', true);
        //END Migration Promotion

    } elseif ($isp == 'metronet') {

        $html_template = new Template('/local/data/mis/portal_modules/mailtemplates/adsl_activation');
        $text_template = new Template('/local/data/mis/portal_modules/mailtemplates/adsl_activation');
        $html_template->set_file('html_mail', 'active_adsl_metronet.ihtml');
        $text_template->set_file('text_mail', 'active_adsl_metronet.txt');

    } elseif ($isp == 'madasafish') {

        $html_template = new Template('/local/data/mis/portal_modules/mailtemplates/adsl_activation');
        $text_template = new Template('/local/data/mis/portal_modules/mailtemplates/adsl_activation');
        $html_template->set_file('html_mail', 'active_adsl_res_eb004.ihtml');
        $text_template->set_file('text_mail', 'active_adsl_res_eb004.txt');

    } elseif (($product_array['type'] == 'business') && ($isp != 'metronet')) {
        // Plusnet Business
        if ($product_attributes['vchProvisioningProfile'] == 'FTTC') {
            $strEmailTemplate = 'account-activation-fibre-legacy-business';
        } else {
            $strEmailTemplate = 'account-activation-adsl-legacy-business';
        }

        $arrTemplateVars = array(
            'realm' => $arrVispConfig['vchVispRealm'],
        );

        EmailHandler::sendEmail($service_id, $strEmailTemplate, $arrTemplateVars);
        return;

    } elseif ($isp == 'johnlewis') {

        if ($product_attributes['vchProvisioningProfile'] == 'FTTC') {
            $strEmailTemplate = 'active_fibre_res';
        } else {
            $strEmailTemplate = 'active_adsl_res';
        }

        $arrTemplateVars = array(
            'cli' => $service_array['cli_number'],
            'adslrealm' => $arrVispConfig['vchVispRealm'],
            'productname' => $product_attributes['product_name']
        );

        EmailHandler::sendEmail($service_id, $strEmailTemplate, $arrTemplateVars);
        return;

    } else {
        // Plusnet Res
        if ($product_attributes['vchProvisioningProfile'] == 'FTTC') {
            $strEmailTemplate = 'account-activation-fibre-legacy';
        } else {
            $strEmailTemplate = 'account-activation-adsl-legacy';
        }

        $arrTemplateVars = array(
            'realm' => $arrVispConfig['vchVispRealm'],
            'plusnetRouter' => $bolTr069
        );

        EmailHandler::sendEmail($service_id, $strEmailTemplate, $arrTemplateVars);
        return;
    }

    if (! isset($html_template)) {

        $template->set_block('main', 'plusnet_visp', 'plusnet_visp_block');
        $template->set_block('main', 'plusnet_visp2', 'plusnet_visp2_block');
        $template->set_block('main', 'payment_details', 'payment_details_block');
                $template->set_block('main', 'TR069_BLOCK', 'tr069');
                $template->set_block('main', 'NO_TR069_BLOCK', 'no_tr069');

        $template->set_var(array('name' => $name,
                                'account' => $account,
                                'username' => $username,
                                'strUsername' => $username,
                                'strCli' => $service_array['cli_number'],
                                'realm' => $realm,
                                'vchAdslLogin' => $realm,
                                'brand' => $arrVispConfig['full_name'],
                                'period' => $period,
                                'portal' => $portal,
                                'strapline' => $strapline,
                                // set blocks to empty string
                                'plusnet_visp_block' => '',
                                'plusnet_visp2_block' => '',
                                'payment_details_block' => '',
                                'tr069' => '',
                                'strSupportBusinessNumber' => $arrVispConfig['vchBusinessPhoneNumber'],
                                'no_tr069' => ''));

                // Get the hardware bundle object for the passed Service ID
                    $HardwareBundle = HardwareBundleControl::GetHardwareBundleByServiceID($service_id);

                    if($HardwareBundle) {

                        if($HardwareBundle->isTr069Compatible()) {

                                $template->parse('tr069', 'TR069_BLOCK', true);
                        } else {

                                $template->parse('no_tr069', 'NO_TR069_BLOCK', true);
                            }
                    } else {

                        $template->parse('no_tr069', 'NO_TR069_BLOCK', true);
                    }

        if($visp == 'plusnet')
        {
            $template->parse('plusnet_visp_block', 'plusnet_visp', true);
            $template->parse('plusnet_visp2_block', 'plusnet_visp2', true);
        }

        //If the product is a no install fee product we don't need to tell the user about the payment
        if (!product_is_adsl_no_install_fee($product_array['service_definition_id']))
        {
            $template->parse('payment_details_block', 'payment_details', true);
        }

        $template->parse ('output','main');
    } //if (isset($template))
    else {

        //This code for the new HTML templates.
        $arrTemplVars = array('isp_brand_name'                 => $arrVispConfig['full_name'],
                    'isp_url'                     => $arrVispConfig['url_domain'],
                    'sig'                         => $arrVispConfig['tag_line'],
                    'real_name'                   => $name,
		    'strSurname' 		  => ucfirst(strtolower($user_array['surname'])),
                    'password'                    => $service_array['password'],
                    'broadband_username'          => ('' <> trim($strAdslUsername) ? $strAdslUsername : $username . $realm),
                    'secure_portal_url'           => $arrVispConfig['portal_main_page_url'],
                    'username'                    => $service_array['username'],
		    'strUsername'		  => $username,
                    'realm'                       => $arrVispConfig['vchVispRealm'],
                    'strCli'                      => $service_array['cli_number'],
                    'geographical_support_number' => $arrVispConfig['vchGeographicalSupportNumber'],
                    'support_phone_number'        => $arrVispConfig['support_phone_number'],
                    'support_url'              => $arrVispConfig['portal_main_page_url'],
                    'strSupportBusinessNumber'    => $arrVispConfig['vchBusinessPhoneNumber'],
                    'bolMigratedUser'             => $bolMigratedUser,
                    'strContractEndDate'          => $strContractEndDate);

        $html_template->set_var($arrTemplVars);
        $text_template->set_var($arrTemplVars);
        $html_template->parse('output', 'html_mail');
        $text_template->parse('output', 'text_mail');
    }


    //Set up the mail object.
    include_once('/local/data/mis/database/database_libraries/mail-class.inc');
    $objEmail = new mail_object();

    //Sets the from address.
    if(stristr($visp_array[$isp]['email'], 'support@') == true)
    {
        $objEmail->set_from($visp_array[$isp]['email']);
    }
    else
    {
        $objEmail->set_from('support@'.$visp_array[$isp]['email']);
    }

            //Sets the to address
            $to_address = '';
            $bolHasPNMailBox = false;
            $strDefaultMailboxAlias = userdataGetDefaultMailboxAlias($service_id);

            if (isset($strDefaultMailboxAlias) && strlen($strDefaultMailboxAlias) > 0) {
                $bolHasPNMailBox = true;
            }

            if ($strDebug == '') {
                if(stristr($visp_array[$isp]['email_x'], '@') == true)
                {
                    if ($service_array['isp'] != 'metronet' && $service_array['isp'] != 'madasafish' && $bolHasPNMailBox) {
                        $strEmail = substr(stristr($visp_array[$isp]['email_x'], '@'), 1);
                        $objEmail->add_to("postmaster@$username.$strEmail");
                        $to_address = "postmaster@$username.$strEmail";
                    }
                    elseif($service_array['isp'] == 'madasafish'){

                        $strEmail = substr(stristr($visp_array[$isp]['email_x'], '@'), 1);
                        $objEmail->add_to($username."@".$strEmail);
                        $to_address = $username."@".$strEmail;
                    }
                    else {
                        $strEmail = $user_array['email'];
                        $objEmail->add_to($strEmail);
                        $to_address = $strEmail;
                    }
                }
                else
                {
                    if (!in_array($service_array['isp'], array('metronet', 'madasafish', 'partner')) && $bolHasPNMailBox) {
                        $objEmail->add_to("postmaster@$username.".$visp_array[$isp]['email_x']);
                        $to_address = "postmaster@$username." . $visp_array[$isp]['email_x'];
                    }
                    elseif($service_array['isp'] == 'madasafish'){
                        $objEmail->add_to($username."@".$visp_array[$isp]['email_x']);
                        $to_address = $username."@".$visp_array[$isp]['email_x'];
                    }
                    else {
                        $objEmail->add_to($user_array['email']);
                        $to_address = $user_array['email'];
                    }
                }

                if (strtolower($to_address) != strtolower($user_array['email']))
                {
                    $objEmail->add_to($user_array['email']);
                    $to_address .= ",{$user_array['email']}";
                }
            }
            else {
                $objEmail->add_to($strDebug);
            }

            //Sets the subject
            if ($product_attributes['vchProvisioningProfile'] == 'FTTC') {
                $objEmail->set_subject('Plusnet broadband - order update');
            } else {
                $objEmail->set_subject('Your broadband is now active');
            }


    //Sets the content of the mail.
    if (isset($text_template)) {
        //HTML multi-part version.
        $objEmail->add_html($html_template->get('output'), $text_template->get('output'));
        $message = $text_template->get('output');
    } else {
        //Plain text version.
        $message = $template->get('output');
        $objEmail->set_body($message);
    }

    //Add the attachement.
    if ($isp == 'plus.net') {
        if ($product_array['type'] == 'business') {
            $strPdfPath = '/local/data/mis/doc_root/latest/support/pdf/welcome/Plusnet-Business-Welcome-Pack.pdf';
            $filename = 'Plusnet-Business-Welcome-Pack.pdf';
        } else {
            $strPdfPath = '/local/data/mis/doc_root/latest/support/pdf/welcome/Plusnet-Handbook.pdf';
            $filename = 'Plusnet-Handbook.pdf';
        }

        $strPdf = file_get_contents($strPdfPath);
        $objEmail->add_attachment($strPdf, $filename, 'application/pdf');
    }

    $objEmail->send();

    if ($strDebug == '')
    {
        $ticket_text = "Customer was mailed ($to_address) to inform them of their ADSL activation\n\n" . $message;
        tickets_ticket_add('Script', $service_id, 0, 0, 'Closed', 0, $ticket_text);
    }
}


///////////////////////////////////////////////////////////////////////////
//  function  : ADSLPreregRecordUserDecision
//  purpose   : When an exchange becomes active preregistered users get
//              to choose if they want to carry on with their upgrade
//  arugments : intPreRegId             : the users pre reg id
//              intPreregUserDecisionId : 1 = Confirmed
//                                        2 = Cancelled
//  returns   : none
///////////////////////////////////////////////////////////////////////////
function ADSLPreregRecordUserDecision($intPreRegId, $intPreregUserDecisionId)
{
    $intPreRegId             = addslashes($intPreRegId);
    $intPreregUserDecisionId = addslashes($intPreregUserDecisionId);

    $dbhConnection = get_named_connection_with_db('common_adsl');

    $strQuery = 'UPDATE tblADSLPreRegInterest '
            ."SET    intPreregUserDecisionId='$intPreregUserDecisionId' "
            ."WHERE  intPreRegId='$intPreRegId' ";

    PrimitivesQueryOrExit($strQuery, $dbhConnection);
}

///////////////////////////////////////////////////////////////////////////
// Function   ADSLAddCeaseLog
// Purpose    Keep a log of Ceases
// Arguments  usiServiceID
//            usiADSLCeaseTypeID
//            usiServiceDefinitionID
//            strDateCeaseRequested
// Returns
///////////////////////////////////////////////////////////////////////////
function ADSLAddCeaseLog($usiServiceID,$usiADSLCeaseTypeID,$usiServiceDefinitionID,$strDateCeaseRequested)
{
    $dbhConnection = get_named_connection_with_db('common_adsl');

    $strQuery = "INSERT INTO tblADSLCeaseLog
                            (usiServiceID,usiADSLCeaseTypeID,usiServiceDefinitionID,dtmDateCeaseRequested)
                    VALUES ($usiServiceID,$usiADSLCeaseTypeID,'$usiServiceDefinitionID','$strDateCeaseRequested')";

    PrimitivesQueryOrExit($strQuery, $dbhConnection,'Keeping a log of the ADSL Cease');

}//end of Function: ADSLAddCeaseLog()


////////////////////////////////////////////////////////////////////////////
// Function   ADSLCancelCeaseLog
// Purpose    Updates the cease setting the cease date as the current date
// Arguments  $usiADSLCeaseLogID
// Returns
///////////////////////////////////////////////////////////////////////////
function ADSLUpdateCeaseLogSetCancelled($usiADSLCeaseLogID)
{
    $dbhConnection = get_named_connection_with_db('common_adsl');

    $strQuery = "UPDATE tblADSLCeaseLog
                    SET dtmDateCeaseCancelled = NOW()
                WHERE usiADSLCeaseLogID = $usiADSLCeaseLogID";

    PrimitivesQueryOrExit($strQuery, $dbhConnection,'Set the ADSL Cease as cancelled in the log');

}//end of Function: ADSLUpdateCeaseLogSetCancelled()

////////////////////////////////////////////////////////////////////////////
// Function   ADSLHardwareSupplierChangeOverGetFromSupplierOrToSupplier
// Purpose    To choose if a harware order should be place the the current or new supplier
// Arguments  None
// Returns    'from' - the order should be placed with the supplier we are moving from
//            'to'   - the order should be placed with the supplier we are moving to
//
// Notes      Table tblADSLHardwareSupplierChangeOver should only contain 1 row
//            if you are going to update the percentage split do the following:
//            DELETE FROM tblADSLHardwareSupplierChangeOver;
//            INSERT INTO tblADSLHardwareSupplierChangeOver (usiSupplierFromCount, usiSupplierToCount, usiSupplierFromPercentage) VALUES (1,1,90);
//            The example above shows that 90% of orders should be placed with the 'from' supplier and 10% from the 'to' supplier
///////////////////////////////////////////////////////////////////////////
function ADSLHardwareSupplierChangeOverGetFromSupplierOrToSupplier(){

    $dbhConnection = get_named_connection_with_db('common_adsl');

    $strQuery = 'SELECT
                IF(ROUND((usiSupplierFromCount/(usiSupplierFromCount+usiSupplierToCount)*100),0)-usiSupplierFromPercentage>=0, "to", "from") as pc
            FROM tblADSLHardwareSupplierChangeOver';

    $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

    $arrReturn = PrimitivesResultsAsArrayGet($resResult);
    $strReturn = $arrReturn[0]['pc'];

    if($strReturn == 'from'){

        $strQuery = 'UPDATE tblADSLHardwareSupplierChangeOver SET usiSupplierFromCount=usiSupplierFromCount+1';
        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);
    }

    if($strReturn == 'to'){
        $strQuery = 'UPDATE tblADSLHardwareSupplierChangeOver SET usiSupplierToCount=usiSupplierToCount+1';
        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);
    }

    return $strReturn;

}
