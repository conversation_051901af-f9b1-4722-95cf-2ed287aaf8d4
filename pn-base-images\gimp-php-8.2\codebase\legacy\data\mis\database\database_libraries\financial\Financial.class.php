<?php
	class Lib_Financial extends Util_LibrarySplitter
	{
		protected static $objInstance;

		protected function __construct()
		{
			$this->myName = __CLASS__;
			$this->myFile = __FILE__;
		}

		public static function singleton()
		{
			if (!isset(self::$objInstance))
			{
				$strClass = __CLASS__;
				self::$objInstance = new $strClass;
			}

			return self::$objInstance;
		} // public static function singleton()

        public static function setInstance(Lib_Financial $instance)
        {
            self::$objInstance = $instance;
        }

        public static function resetInstance()
        {
            self::$objInstance = null;
        }

	} // class Lib_Financial extends Util_LibrarySplitter
?>
