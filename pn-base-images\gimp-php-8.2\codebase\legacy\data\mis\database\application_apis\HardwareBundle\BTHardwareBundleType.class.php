<?php
/**
* BTHardwareBundleType class
* Encapsulates a hardware bundle type
*
* @param m_strClassPrefix
* @param m_strSupplierTag
*
* @package    DatabaseAccessCommon
* @subpackage application_apis
* @access     public
* <AUTHOR>
*
*/

class BTHardwareBundleType extends HardwareBundleType
{
	//
	// PRIVATE Members
	//

	/**
	* The Class Prefix
	*
	* A String that is the prefix of this class
	*
	* @access private
	* @var    String
	*/
	var $m_strClassPrefix = 'BT';

	/**
	* The Supplier Tag
	*
	* A String that is this suppliers tag
	*
	* @access private
	* @var    String
	*/
	var $m_strSupplierTag = 'BT';

	//
	// Constructor
	//

	/**
	* BTHardwareBundleType constructor
	*
	* The constructor method for this class
	*
	*/
	function BTHardwareBundleType()
	{
		$this->HardwareBundleType();
	}

}//end of class:BTHardwareBundleType
?>