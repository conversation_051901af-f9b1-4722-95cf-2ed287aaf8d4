<?php

/**
* Access library for WLR Favourite Number component
* 
* @package    Core
* @subpackage WLR
* @access     public
* @version    $Id: CWlrFavouriteNumber.inc,v 1.4 2008-03-27 19:38:58 ssmith Exp $
*/

require_once('/local/data/mis/database/database_libraries/components/CWlrProductComponent.inc');

class CWlrFavouriteNumber extends CWlrProductComponent
{
	/**
	 * intDiscountPercent 
	 * 
	 * @var integer
	 * @access private
	 */
	private $intDiscountPercent = 0;
	
	/**
	 * intMaxQuantity at this moment the default quantity is 5
	 * 
	 * @var integer
	 * @access private
	 */
	private $intMaxQuantity = 5;
	
	/**
	 * User config ID from userdata.tblConfigWlrFavouriteNumber
	 * 
	 * @var integer
	 * @access private
	 */
	private $intUserConfigId = 0;
	
	/**
	 * arrFavouriteNumbers 
	 * 
	 * @var array
	 * @access private
	 */
	private $arrFavouriteNumbers = array();
	
	/**
	 * __construct 
	 * 
	 * @param integer $intProductComponentInstanceId 
	 * @access public
	 * @return void
	 */
	public function __construct($intProductComponentInstanceId)
	{
		$this->CProductComponent($intProductComponentInstanceId);
		$this->setFavouriteNumberConfig();
		$this->setFavouriteNumbers();
	}

	/**
	 * setFavouriteNumberConfig - retrives config data based on intProductComponentInstanceId
	 * 
	 * @access private
	 * @return void
	 */
	private function setFavouriteNumberConfig()
	{
		$arrDbConnection = get_named_connection_with_db('dbProductComponents');
		$intInstanceId = mysql_real_escape_string($this->getProductComponentInstanceId(), 
		                                          $arrDbConnection['handle']);
		
		$strQuery = 'SELECT fnc.intMaxQuantity, fnc.intDiscountPercent, cwfn.intConfigWlrFavouriteNumberId
		             FROM userdata.tblProductComponentInstance pci 
		             INNER JOIN dbProductComponents.tblProductComponent pc ON pc.intProductComponentID = pci.intProductComponentID 
		             INNER JOIN dbProductComponents.tblProductComponentConfig pcc ON pc.intProductComponentID = pcc.intProductComponentID 
		             INNER JOIN dbProductComponents.tblWlrFavouriteNumberConfig fnc ON pcc.intProductComponentConfigID = fnc.intProductComponentConfigId 
		             LEFT JOIN userdata.tblConfigWlrFavouriteNumber cwfn ON pci.intProductComponentInstanceID = cwfn.intProductComponentInstanceId AND cwfn.dtmEnd IS NULL '.
		            "WHERE pci.dtmEnd IS NULL AND pci.intProductComponentInstanceID = '$intInstanceId'";
		
		$arrData = PrimitivesResultGet( PrimitivesQueryOrExit($strQuery, $arrDbConnection) );
		
		if ( count($arrData) > 0 ) {
			$this->intDiscountPercent = (int) $arrData['intDiscountPercent'];
			$this->intMaxQuantity = (int) $arrData['intMaxQuantity'];
			
			if ( !empty($arrData['intConfigWlrFavouriteNumberId']) ) {
				$this->intUserConfigId = (int) $arrData['intConfigWlrFavouriteNumberId'];
			}
		}
	}
	
	/**
	 * setFavouriteNumbers - creates array of favourite numbers assigned to user 
	 * 
	 * @access private
	 * @return boolean if user config id does not exist
	 */
	private function setFavouriteNumbers()
	{
		if ( 0 == $this->intUserConfigId ) {
			return false;
		}
		
		$arrDbConnection = get_named_connection_with_db('userdata');
		$strQuery = 'SELECT intFavouriteNumberId, vchCli, intSortOrder
		             FROM tblFavouriteNumber '.
		            "WHERE intConfigWlrFavouriteNumberId = '$this->intUserConfigId' ". 
		            'AND dtmEnd IS NULL ORDER BY intSortOrder ASC';

		$arrResult = PrimitivesResultsAsArrayGet(PrimitivesQueryOrExit($strQuery, $arrDbConnection) );
		
		if ( is_array($arrResult) && count($arrResult) > 0 ) {
			foreach ( $arrResult as $arrNumber ) {
				$this->arrFavouriteNumbers[$arrNumber['intSortOrder']]['strCli'] = $arrNumber['vchCli'];
				$this->arrFavouriteNumbers[$arrNumber['intSortOrder']]['intFavouriteNumberId'] = $arrNumber['intFavouriteNumberId'];	
			}
		}
	}
	
	/**
	 * getDiscount 
	 * 
	 * @access public
	 * @return integer 
	 */
	public function getDiscount()
	{
		return (int) $this->intDiscountPercent; 
	}
	
	/**
	 * getMaxQuantity 
	 * 
	 * @access public
	 * @return integer
	 */
	public function getMaxQuantity()
	{
		return (int) $this->intMaxQuantity;
	}
	
	/**
	 * Return user config ID
	 *
	 * @access public
	 * @return integer
	 */
	public function getUserConfigId()
	{
		return (int) $this->intUserConfigId;
	}
	
	/**
	 * Returns active favourite numbers
	 * 
	 * @access public
	 * @return array
	 */
	public function getFavouriteNumbers()
	{
		return $this->arrFavouriteNumbers;
	}
	
	/**
	 * canAddNumber 
	 * 
	 * @access public
	 * @return boolean
	 */
	public function canAddNumber()
	{
		return ( count($this->arrFavouriteNumbers) < $this->intMaxQuantity );
	}
	
	/**
	 * onDestroy 
	 * 
	 * @param array $arrArgs 
	 * @access public
	 * @return void
	 */
	public function onDestroy($arrArgs = array()) 
	{
		$this->endAllNumbers($this->intUserConfigId);
		$this->endUserConfig($this->intUserConfigId);

		return TRUE;
	}

	/**
	 * End user config
	 *
	 * @param integer $intUserConfigId
	 * @return void
	 */
	private function endUserConfig($intUserConfigId) 
	{
		if ( !is_numeric($intUserConfigId) || 0 >= $intUserConfigId ) {
			return false;
		}
		
		$arrDbConnection = get_named_connection_with_db('userdata');
		$intUserConfigId = mysql_real_escape_string($intUserConfigId, $arrDbConnection['handle']);
		
		$strQuery = 'UPDATE tblConfigWlrFavouriteNumber SET dtmEnd = NOW() '.
		            "WHERE intConfigWlrFavouriteNumberId = '$intUserConfigId'";
		
		PrimitivesQueryOrExit($strQuery, $arrDbConnection);	
	}
	
	/**
	 * End all favourite numbers
	 *
	 * @param integer $intUserConfigId
	 * @return void
	 */
	private function endAllNumbers($intUserConfigId)
	{
		if ( !is_numeric($intUserConfigId) || 0 >= $intUserConfigId ) {
			return false;
		}
		
		$arrDbConnection = get_named_connection_with_db('userdata');
		$intUserConfigId = mysql_real_escape_string($intUserConfigId, $arrDbConnection['handle']);
		
		$strQuery = 'UPDATE tblFavouriteNumber SET dtmEnd = NOW() 
		             WHERE dtmEnd IS NULL  '.
		            "AND intConfigWlrFavouriteNumberId = '$intUserConfigId'";
		
		PrimitivesQueryOrExit($strQuery, $arrDbConnection);
	}
	
	/**
	 * createUserConfig 
	 * 
	 * @access public
	 * @return integer - id of a new config
	 */
	public function createUserConfig()
	{
		$arrDbConnection = get_named_connection_with_db('userdata');
		$intProductComponentInstanceId = mysql_real_escape_string($this->getProductComponentInstanceId(), 
		                                                          $arrDbConnection['handle']);
	
		$strQuery = 'INSERT INTO tblConfigWlrFavouriteNumber SET dtmStart = NOW(), '.
		            "intProductComponentInstanceId = '$intProductComponentInstanceId'";

		PrimitivesQueryOrExit($strQuery, $arrDbConnection);
		$this->intUserConfigId = PrimitivesInsertIdGet($arrDbConnection);
		return $this->intUserConfigId;
	}

	/**
	 * addNumber 
	 * 
	 * @param string $strCli 
	 * @param integer $intSortOrder 
	 * @access public
	 * @return boolean - true on succes 
	 * @throws InvalidArgumentException
	 */
	public function addNumber($strCli, $intSortOrder)
	{
		if ( empty($strCli) || !is_numeric($intSortOrder) || $intSortOrder <= 0 ) {
			throw new InvalidArgumentException('Invalid argument');
		}
		
		$arrDbConnection = get_named_connection_with_db('userdata');
		
		$strCli = mysql_real_escape_string($strCli, $arrDbConnection['handle']);
		$intSortOrder = mysql_real_escape_string($intSortOrder, $arrDbConnection['handle']);
		                                                          
		$strQuery = 'INSERT INTO tblFavouriteNumber SET dtmStart = NOW(), '.
		            "intConfigWlrFavouriteNumberId = '$this->intUserConfigId',
		             vchCli = '$strCli', intSortOrder = '$intSortOrder'";
		            
		return PrimitivesQueryOrExit($strQuery, $arrDbConnection);
	}
	
	/**
	 * endNumber 
	 * 
	 * @param integer $intFavouriteNumberId 
	 * @param integer $intSortOrder 
	 * @access public
	 * @return void
	 * @throws InvalidArgumentException
	 */
	public function endNumber($intFavouriteNumberId, $intSortOrder = null)
	{
		if ( !is_numeric($intFavouriteNumberId) || $intFavouriteNumberId <= 0 ) {
			throw new InvalidArgumentException('Invalid intFavouriteNumberId: '. $intFavouriteNumberId);
		}
		
		$arrDbConnection = get_named_connection_with_db('userdata');
		$intFavouriteNumberId = mysql_real_escape_string($intFavouriteNumberId, $arrDbConnection['handle']);

		$strQuery = 'UPDATE tblFavouriteNumber SET dtmEnd = NOW() '.
		            "WHERE intFavouriteNumberId = '$intFavouriteNumberId'";
		
		PrimitivesQueryOrExit($strQuery, $arrDbConnection);
		
		if ( !is_null($intSortOrder) ) {
			$intSortOrder = mysql_real_escape_string($intSortOrder, $arrDbConnection['handle']);
			
			$strQuery = 'UPDATE tblFavouriteNumber AS tleft 
			             INNER JOIN tblFavouriteNumber AS tright 
			               ON tleft.intConfigWlrFavouriteNumberId = tright.intConfigWlrFavouriteNumberId 
			             SET tleft.intSortOrder = tleft.intSortOrder - 1
			             WHERE tleft.dtmEnd IS NULL '.
			            "AND tleft.intSortOrder > '$intSortOrder'
			             AND tright.intFavouriteNumberId = '$intFavouriteNumberId'";
	
			PrimitivesQueryOrExit($strQuery, $arrDbConnection);
		}
	}
	
	/**
	 * isValidNumber method check whether CLI is valid landline, UK mobile 
	 * or international number
	 * 
	 * @param string $strCli 
	 * @static
	 * @access public
	 * @return boolean - true when cli is valid number
	 * @throws InvalidArgumentException
	 */
	public function isValidNumber($strCli) 
	{
		if ( empty($strCli) ) {
			throw new InvalidArgumentException('Invalid CLI: '.$strCli);
		}
		
		$arrPatterns = array('/^0(1|2)[0-9]{8,9}$/', // landline
		                     '/^07[0-9]{8,9}$/', // mobile
		                     '/^00[0-9]{8,14}$/'); // international
		
		foreach ( $arrPatterns as $strPattern ) {
			if ( preg_match($strPattern, $strCli) ) {
				return true;
			}
		}
		
		return false;
	}
	
	/**
	 * isFavourite - methods check whether user has got such CLI and is active 
	 *
	 * @param unknown_type $strCli
	 * @return boolean
	 * @thorws InvalidArgumentException
	 */
	public function isFavourite($strCli)
	{
		if ( empty($strCli)) {
			throw new InvalidArgumentException('Invalid CLI: '. $strCli);
		}
		
		$arrDbConnection = get_named_connection_with_db('userdata');
		$strCli = mysql_real_escape_string($strCli, $arrDbConnection['handle']);
		
		$strQuery = 'SELECT count(vchCli) AS intSummary FROM tblFavouriteNumber '. 
		            "WHERE dtmEnd IS NULL AND vchCli = '$strCli' 
		             AND intConfigWlrFavouriteNumberId = '$this->intUserConfigId'";
		
		$intSummary = PrimitivesResultGet( PrimitivesQueryOrExit($strQuery, $arrDbConnection), 
		                                   'intSummary');
		if ( $intSummary > 0 ) {
			return true;	
		}
		
		return false;
	}
	
	/**
	 * Method transfers all active favourite numbers from one user config to another
	 *
	 * @param integer $intFromConfigId
	 * @return boolean it returns false when user config id is invalid 
	 */
	public function transferUserConfig($intFromConfigId)
	{
		if ( empty($intFromConfigId) || !is_numeric($intFromConfigId) ) {
			return false;
		}
		
		$arrDbConnection = get_named_connection_with_db('userdata');
		$intFromConfigId = mysql_real_escape_string($intFromConfigId, $arrDbConnection['handle']);
		
		$strQuery = 'INSERT INTO tblFavouriteNumber 
		             (dtmStart, intConfigWlrFavouriteNumberId, vchCli, intSortOrder) '.
		            "SELECT NOW(), '$this->intUserConfigId', vchCli, intSortOrder  
		             FROM tblFavouriteNumber WHERE intConfigWlrFavouriteNumberId = '$intFromConfigId'"; 
		
		PrimitivesQueryOrExit($strQuery, $arrDbConnection);
		
		$this->endAllNumbers($intFromConfigId);
		$this->endUserConfig($intFromConfigId);
	}
}
