<?php

	/////////////////////////////////////////////////////////////////////
	// File:     config-cbc-extra-bandwidth-access.inc
	// Purpose:  Access mini-library for configuring the customers CBC ExtraBandwidth component
	/////////////////////////////////////////////////////////////////////

	/////////////////////////////////////////////////////////////////////
	// Functions
	//
	// Write functions
	// ---------------
	//
	// config_cbc_flex_restrict
	//
	// Read functions
	// --------------
	//
	// Modify Functions
	// ----------------
	//
	// Delete functions
	// ----------------
	//
	/////////////////////////////////////////////////////////////////////

	/////////////////////////////////////////////////////////////////////
	// Requires

	require_once('/local/data/mis/database/database_libraries/cbc-access.inc');
	require_once('/local/data/mis/database/database_libraries/view-my-broadband-usage-access.inc');
	require_once('/local/data/mis/database/database_libraries/programme-tool-access.inc');

	// Requires
	/////////////////////////////////////////////////////////////////////

	/////////////////////////////////////////////////////////////////////
	// Data

	$global_component_configurators['311'] = 'ConfigExtraBandwidthConfigurator';

	// Data
	/////////////////////////////////////////////////////////////////////
	
	// Hack to insert the component configurator array into PHP5's global scope
	// if it's not already there
	if(!isset($GLOBALS['global_component_configurators']))
	{
		$GLOBALS['global_component_configurators'] = $global_component_configurators;
	}
	else
	{
		foreach($global_component_configurators as $intIndex => $strConfigurator)
		{
			$GLOBALS['global_component_configurators'][$intIndex] = $strConfigurator;
		}
	}
	
	/////////////////////////////////////////////////////////////////////
	// Library functions

	/////////////////////////////////////////////////////////////
	// 
	// 
	function ConfigInsertCBCExtraBandwidthID($intComponentID, $intCBCExtraBandwidthID)
	{
		$dbhConnection = get_named_connection_with_db('userdata');

		$strQuery = 'INSERT INTO tblConfigCBCExtraBandwidth ' .
		            '           (intComponentID, ' .
		            '            intCBCExtraBandwidthID, ' .
		            '            vchDBSrc) ' .
		            "    VALUES ('$intComponentID', " .
		            "            '$intCBCExtraBandwidthID', " .
		            '            "CBl")';

		PrimitivesQueryOrExit($strQuery, $dbhConnection);

        $intConfigID = PrimitivesInsertIdGet($dbhConnection);

		// Update the component with the new config id
		$strQuery = 'UPDATE components ' .
		            "   SET config_id = '$intConfigID', " .
		            '       db_src = "CBl" ' .
		            " WHERE component_id = '$intComponentID'";

		PrimitivesQueryOrExit($strQuery, $dbhConnection);

		$intAffectedRows = PrimitivesAffectedRowsGet($dbhConnection);

		// Update the components description to reflect the included bandwidth
		ConfigExtraBandwidthUpdateDescription($intComponentID, $intCBCExtraBandwidthID);

		// Add a log entry for this
		$arrComponent = userdata_component_get($intComponentID);

		InsertCBCExtraBandwidthServiceLog($arrComponent['service_id'], 0, $intCBCExtraBandwidthID);

		return $intConfigID;

	} // function ConfigInsertCBCExtraBandwidthID

	function ConfigUpdateCBCExtraBandwidthID($intComponentID, $intFromCBCExtraBandwidthID, $intToCBCExtraBandwidthID)
	{
		$dbhConnection = get_named_connection_with_db('userdata');

		$strQuery = 'UPDATE tblConfigCBCExtraBandwidth ' .
		            "   SET intCBCExtraBandwidthID = '$intToCBCExtraBandwidthID', " .
		            '       vchDBSrc = "CBl" ' .
		            " WHERE intComponentID = '$intComponentID'";

		PrimitivesQueryOrExit($strQuery, $dbhConnection);

		$intAffectedRows = PrimitivesAffectedRowsGet($dbhConnection);

		// Update the components description to reflect the included bandwidth
		ConfigExtraBandwidthUpdateDescription($intComponentID, $intToCBCExtraBandwidthID);

		// Add a log entry for this
		$arrComponent = userdata_component_get($intComponentID);

		InsertCBCExtraBandwidthServiceLog($arrComponent['service_id'], $intFromCBCExtraBandwidthID, $intToCBCExtraBandwidthID);

		return $intAffectedRows;

	} // function ConfigUpdateCBCExtraBandwidthID

	function ConfigExtraBandwidthGetCBCExtraBandwidthID($intComponentID)
	{
		$dbhConnection = get_named_connection_with_db('userdata');

		$strQuery = 'SELECT intCBCExtraBandwidthID ' .
		            '  FROM tblConfigCBCExtraBandwidth ' .
		            " WHERE intComponentID = '$intComponentID'";

		$resCBCExtraBandwidthID = PrimitivesQueryOrExit($strQuery, $dbhConnection);

		$intCBCExtraBandwidthID = PrimitivesResultGet($resCBCExtraBandwidthID, 'intCBCExtraBandwidthID');

		return $intCBCExtraBandwidthID;

	} // function ConfigExtraBandwidthGetCBCExtraBandwidthID

	function ConfigExtraBandwidthUpdateDescription($intComponentID, $intCBCExtraBandwidthID)
	{
		$dbhConnection = get_named_connection_with_db('userdata');

		$arrCBCExtraBandwidth = GetCBCExtraBandwidthDetails($intCBCExtraBandwidthID);

		$intIncludedGB = round($arrCBCExtraBandwidth['intExtraBandwidthBytes'] / GIGABYTE);

		$strDescription = addslashes("$intIncludedGB" . 'GB Extra Bandwidth');

		$strQuery = 'UPDATE components ' .
		            "   SET description = '$strDescription', " .
		            '       db_src = "CBl" ' .
		            " WHERE component_id = '$intComponentID'";

		PrimitivesQueryOrExit($strQuery, $dbhConnection);

		$intAffectedRows = PrimitivesAffectedRowsGet($dbhConnection);

		return $intAffectedRows;

	} // function ConfigExtraBandwidthUpdateDescription

	function ConfigExtraBandwidthSetCBCExtraBandwidthID($intComponentID, $intCBCExtraBandwidthID)
	{
		// Check whether a record has been already added for this component
		$intFromCBCExtraBandwidthID = ConfigExtraBandwidthGetCBCExtraBandwidthID($intComponentID);

		if ($intCBCExtraBandwidthID == $intFromCBCExtraBandwidthID)
		{
			return true;
		}
		elseif ($intFromCBCExtraBandwidthID > 0)
		{
			$bolUpdated = ConfigUpdateCBCExtraBandwidthID($intComponentID, $intFromCBCExtraBandwidthID, $intCBCExtraBandwidthID);

			return $bolUpdated;
		}
		else
		{
			$intConfigID = ConfigInsertCBCExtraBandwidthID($intComponentID, $intCBCExtraBandwidthID);

			return $intConfigID;
		}

		return false;

	} // function ConfigExtraBandwidthSetCBCExtraBandwidthID

	function ConfigExtraBandwidthDelete($intComponentID)
	{
		$dbhConnection = get_named_connection_with_db('userdata');

		// Delete the config entry for this component
		$strQuery = 'DELETE FROM tblConfigCBCExtraBandwidth ' .
		            " WHERE intComponentID = '$intComponentID'";

		PrimitivesQueryOrExit($strQuery, $dbhConnection);

		$intAffectedRows = PrimitivesAffectedRowsGet($dbhConnection);

		// If row was removed then upadte the component details to reflect it
		if ($intAffectedRows > 0)
		{
			$strQuery = 'UPDATE components ' .
			            '   SET config_id = "-1", ' .
			            '       description = "" ' .
			            " WHERE component_id = '$intComponentID'";

			PrimitivesQueryOrExit($strQuery, $dbhConnection);

			$intAffectedRows = PrimitivesAffectedRowsGet($dbhConnection);
		}

		return $intAffectedRows;

	} // function ConfigExtraBandwidthDelete

	function ConfigExtraBandwidthActivate($intComponentID)
	{
		$arrComponent = userdata_component_get($intComponentID);

		switch ($arrComponent['status'])
		{
			case 'unconfigured' :

				// Get the CBC Flex component
				$intCBCFlexComponentID = GetCBCFlexActiveComponentID($arrComponent['service_id']);

				if ($intCBCFlexComponentID > 0)
				{
					$intCBCFlexID = ConfigFlexGetCBCFlexID($intCBCFlexComponentID);

					$intCBCExtraBandwidthID = GetCurrentCBCExtraBandwidthLowestID($intCBCFlexID);

					// Set the ExtraBandwidth option of the product
					$bolSetCBCExtraBandwidthID = ConfigExtraBandwidthSetCBCExtraBandwidthID($intComponentID, $intCBCExtraBandwidthID);

					// Activate the component
					if ($bolSetCBCExtraBandwidthID > 0)
					{
						userdata_component_set_status($intComponentID, 'active');

						return true;
					}
				}

				break;

			default :

				// Do nothing
				break;
		}

		return false;

	} // function ConfigExtraBandwidthActivate

	function ConfigExtraBandwidthValidateUsage($intServiceDefinitionID, $intBandwidth, $intNumDays=1)
	{
		$dbhConnection = get_named_connection_with_db('product_reporting');

		$strQuery = 'SELECT intMaximumDailyBandwidth ' .
		            '  FROM adsl_product ' .
		            " WHERE service_definition_id = '$intServiceDefinitionID'";

		$resMaximumDailyBandwidth = PrimitivesQueryOrExit($strQuery, $dbhConnection);

        $intMaximumDailyBandwidth = PrimitivesResultGet($resMaximumDailyBandwidth, 'intMaximumDailyBandwidth');

		if (isset($intMaximumDailyBandwidth) && $intMaximumDailyBandwidth > 0)
		{
			$intMaximumPeriodBandwidth = $intMaximumDailyBandwidth * $intNumDays;

			if ($intBandwidth <= $intMaximumPeriodBandwidth)
			{
				return true;
			}
			else
			{
				return false;
			}

		}

		return false;

	} // function ConfigExtraBandwidthValidateUsage

	function ConfigExtraBandwidthRefresh($intComponentID, $bolForceRefresh = false)
	{
		$arrComponent = userdata_component_get($intComponentID);

		// If customer has an outstanding log to reset account then do not refresh the component
		$intVMBUBillingResetLogID = getVMBUBillingResetLogID($arrComponent['service_id']);

		if (isset($intVMBUBillingResetLogID) && $intVMBUBillingResetLogID > 0)
		{
			return false;

		} // end if

		$arrService = userdata_service_get($arrComponent['service_id']);

		switch ($arrComponent['status'])
		{
			case 'active' :

				$intCBCFlexComponentID = GetCBCFlexActiveComponentID($arrComponent['service_id']);

				if ($intCBCFlexComponentID > 0)
				{
					$intCBCFlexID = ConfigFlexGetCBCFlexID($intCBCFlexComponentID);

					$intCurrentCBCExtraBandwidthID = ConfigExtraBandwidthGetCBCExtraBandwidthID($intComponentID);

					$arrCurrentCBCExtraBandwidth = GetCBCExtraBandwidthDetails($intCurrentCBCExtraBandwidthID);

					$bolCorrectForCBCFlexID = false;

					if ($intCBCFlexID == $arrCurrentCBCExtraBandwidth['intCBCFlexID'])
					{
						$bolCorrectForCBCFlexID = true;
					}

					// If component has been set and is on current invoice date, then do not update
					if ($intCurrentCBCExtraBandwidthID > 0 && date('Ymd') >= date('Ymd', strtotime($arrService['next_invoice'])) && $bolCorrectForCBCFlexID == true && $bolForceRefresh == false)
					{
						return false;
					}

					// Get the amount of bandwidth used this period
					$arrTotalBandwidthUsedBytesByType = false;

					$strAdslGroupsFormatted = GetAllADSLGroups();

					if ($strAdslGroupsFormatted != '')
					{
						$arrTotalBandwidthUsedBytesByType = getTotalBandwidthUsedByType($arrComponent['service_id'], $strAdslGroupsFormatted);
					}

					// If component has been set previously but false is returned when getting bandwidth then do not update
					// It may because server is away and we do not want to treat that as being the same as having no bandwidth
					if ($intCurrentCBCExtraBandwidthID > 0 && $arrTotalBandwidthUsedBytes === false && $bolCorrectForCBCFlexID == true)
					{
						return false;
					}

					$intChargedBandwidthBytes = 0;

					$intTotalBandwidthUsedBytes = $arrTotalBandwidthUsedBytesByType['intPeakUsageBytes'] +
					                              $arrTotalBandwidthUsedBytesByType['intOffPeakUsageBytes'];

					// Validate that the bandwidth being returned is correct for the month based on the product
					$bolValid = ConfigExtraBandwidthValidateUsage($arrService['type'], $intTotalBandwidthUsedBytes, 30);

					if ($bolValid === false)
					{
						// Raise a problem for this service id
						$intGB = number_format($intTotalBandwidthUsedBytes / GIGABYTE, 0);

						$strMessage = 'The function ConfigExtraBandwidthRefresh has found Service ID ' . $arrComponent['service_id'] .
						              ' to have more bandwidth in their running monthly usage than is expected on their product (' . $arrService['type'] . ').' .
						              " They are currently returning $intTotalBandwidthUsedBytes bytes ($intGB" . "GB) of bandwidth.\n\n" .
						              'Please check this customer and fix as required. If the customers bandwidth is correct and there are no problems with' .
						              ' their account, please update products.adsl_product.intMaximumDailyBandwidth to a more appropiate level for'.
						              ' service_definition_id ' . $arrService['type'] . '.';

						$strComment = ' Service ID ' . $arrComponent['service_id'] . ' (Service Definition ID ' . $arrService['type'] . ') is returning' .
						              " $intTotalBandwidthUsedBytes bytes ($intGB" . "GB) of bandwidth.";

						$intProblemID = pt_raise_autoproblem('VMBU Invalid Period Bandwidth',
						                                     'AUTO-PROBLEM: VMBU Invalid Period Bandwidth Usage',
						                                     $strMessage,
						                                     $strComment);

						// Send debug email
						mail('<EMAIL>', 'VMBU Invalid Period Bandwidth Usage', "Problem ID $intProblemID has been raised.");

						return false;
					}

					$arrCBCFlexDetails = GetCBCFlexDetails($intCBCFlexID);

					// Log how much usage has been used
					UpdateCBCBandwidthUsage($arrComponent['service_id'], $arrCBCFlexDetails['intIncludedBandwidthBytes'], $intTotalBandwidthUsedBytes);

					$intChargeableBandwidthBytes = $arrTotalBandwidthUsedBytesByType['intChargeableBytes'];

					if ($intChargeableBandwidthBytes > $arrCBCFlexDetails['intIncludedBandwidthBytes'])
					{
						$intExtraBandwidthUsedBytes = $intChargeableBandwidthBytes - $arrCBCFlexDetails['intIncludedBandwidthBytes'];
					}

					if ($intExtraBandwidthUsedBytes < 0)
					{
						$intExtraBandwidthUsedBytes = 0;
					}

					// Check whether the component should be updated
					$arrCurrentCBCExtraBandwidthIDs = GetCurrentCBCExtraBandwidthIDs($intCBCFlexID);

					$intRequiredCBCExtraBandwidthID = false;

					foreach ($arrCurrentCBCExtraBandwidthIDs as $intCBCExtraBandwidthID)
					{
						$arrCBCExtraBandwidth = GetCBCExtraBandwidthDetails($intCBCExtraBandwidthID);

						if ($intExtraBandwidthUsedBytes > ($arrCBCExtraBandwidth['intExtraBandwidthBytes'] - GIGABYTE))
						{
							$intRequiredCBCExtraBandwidthID = $intCBCExtraBandwidthID;
						}
					}

					if ($intRequiredCBCExtraBandwidthID > 0)
					{
						CheckWhetherToSendCBCEmail($arrComponent['service_id'], $intCurrentCBCExtraBandwidthID, $intRequiredCBCExtraBandwidthID);

						$bolSetCBCExtraBandwidthID = ConfigExtraBandwidthSetCBCExtraBandwidthID($intComponentID, $intRequiredCBCExtraBandwidthID);

						return $bolSetCBCExtraBandwidthID;
					}

				} // endif

				break;

			default :

				// Do nothing
				break;
		}

		return false;

	} // function ConfigExtraBandwidthRefresh

	function ConfigExtraBandwidthDestroy($intComponentID)
	{
		$arrComponent = userdata_component_get($intComponentID);

		switch ($arrComponent['status'])
		{
			case 'active' :

				// Remove component settings and clear component fields
				$bolDelete = ConfigExtraBandwidthDelete($intComponentID);

				if ($bolDelete > 0)
				{
					userdata_component_set_status($intComponentID, 'destroyed');

					return true;
				}

				break;

			case 'unconfigured' :

				userdata_component_set_status($intComponentID, 'destroyed');

				return true;

				break;

			default :

				// Do nothing
				break;
		}

		return false;

	} // function ConfigExtraBandwidthDestroy

	/////////////////////////////////////////////////////////////
	// Function:   ConfigExtraBandwidthConfigurator
	// Purpose:    Auto-matically performs actions
	//             Note that the auto-configure DOES NOT DO ANYTHING as the component requires the CBC ExtraBandwidth option setting
	// Arguments:  $intComponentID
	//             $strAction
	// Returns:    bool
	/////////////////////////////////////////////////////////////
	function ConfigExtraBandwidthConfigurator($intComponentID, $strAction)
	{
		$bolUpdated = false;

		switch ($strAction)
		{
			case 'auto_enable' :

				$bolUpdated = ConfigExtraBandwidthActivate($intComponentID);

				break;

			case 'auto_configure' :

				$arrComponent = userdata_component_get($intComponentID);

				if ($arrComponent['status'] == 'unconfigured')
				{
					$bolUpdated = ConfigExtraBandwidthActivate($intComponentID);
				}
				elseif ($arrComponent['status'] == 'active')
				{
					$bolUpdated = ConfigExtraBandwidthRefresh($intComponentID);
				}

				break;

			case 'auto_refresh' :

				$bolUpdated = ConfigExtraBandwidthRefresh($intComponentID);

				break;

			case 'auto_destroy' :

				$bolUpdated = ConfigExtraBandwidthDestroy($intComponentID);

				break;

			case 'auto_disable' :
			default :

				// Do nothing
				break;
		}

		return $bolUpdated;

    } // function ConfigExtraBandwidthConfigurator

	// Library functions
	/////////////////////////////////////////////////////////////////////

?>
