<?php

	/**
	* Access library for wifi
	*
	* @package    Core
	* @subpackage Wifi
	* @access     public
	* <AUTHOR>
	* @version    $Id: config-wifi-access.inc,v 1.24 2007-07-25 06:38:49 rjones Exp $
        * @filesource
	*/

	require_once('/local/data/mis/database/database_libraries/components/component-defines.inc');
	require_once('/local/data/mis/database/database_libraries/sql_primitives.inc');
	require_once('/local/data/mis/database/database_libraries/userdata-access.inc');
	require_once('/local/data/mis/database/database_libraries/referrals.inc');
	require_once('/local/data/mis/database/database_libraries/wifi-roaming-access.inc');
	require_once('/local/data/mis/database/database_libraries/components/component-defines.inc');
	require_once('/local/data/mis/database/database_libraries/CWifiScheduledPayment.inc');
	require_once('/local/data/mis/database/database_libraries/CWifiScheduledTariffChange.inc');
	require_once('/local/data/mis/database/database_libraries/CoreObjects/CError/CError.inc');
	require_once('/local/data/mis/database/database_libraries/CoreObjects/Radius/CRadiusUser.inc');
	require_once('/local/data/mis/database/database_libraries/CoreObjects/Radius/CRadiusSubscription.inc');
	require_once('/local/data/mis/database/database_libraries/CoreObjects/Radius/CRadiusTimeQuota.inc');
	require_once('/local/data/mis/database/application_apis/CPage/CCurrentUser.inc');
	require_once('/local/data/mis/database/database_libraries/components/CWifiComponent.inc');


	/////////////////////
	// Service Event IDs
	//////////////////////

	define('WIFI_COMPONENT_CREATED', 58);
	define('WIFI_COMPONENT_ACTIVATED',52);
	define('WIFI_COMPONENT_DISABLED',53);
	define('WIFI_SCHEDULED_CANCELLATION',54);
	define('WIFI_SCHEDULED_CANCELLATION_REVOKED',55);
	define('WIFI_SCHEDULED_COMPONENT_CANCELLATION',56);
	define('WIFI_IMMEDIATE_COMPONENT_CANCELLATION',57);
	define('WIFI_TARIFF_CHANGE',59);
	define('WIFI_COMPONENT_REPORTING_SIGNUP',75);
	define('WIFI_COMPONENT_REPORTING_UPGRADE',76);
	define('WIFI_COMPONENT_REPORTING_DOWNGRADE',77);
	define('WIFI_COMPONENT_REPORTING_CANCELLATION',78);

	define('WIFI_INITIAL_PROMOTION_ENDTIME', 1114901999); //select UNIX_TIMESTAMP('2005-04-30 23:59:59');

	define('PRIORITY_WIFI_SUBSCRIPTION', '1000');
	define('PRIORITY_WIFI_PROMOTION',    '2000');
	define('PRIORITY_WIFI_GIFT',         '3000');
	define('PRIORITY_WIFI_PREPAY',       '4000');
	define('PRIORITY_WIFI_CREDIT',       '5000');
	define('PRIORITY_WIFI_DEFAULT',      '3000');


	
	/////////////////////////////////////////////////////////////////////
	// Exported data

	/* Globally executed code, make configurator entries for all wifi configs */

	$connection = get_named_connection_with_db('unprivileged_reporting');

	$query = "SELECT intServiceComponentID
	            FROM products.tblComponentWIFIConfig";

	$result = PrimitivesQueryOrExit($query, $connection,'Make wifi configurator entries');

	$array_wifi_configurators = PrimitivesResultsAsArrayGet($result);

	foreach($array_wifi_configurators AS $configurator)
	{
		$configurator['intServiceComponentID'] = $configurator['intServiceComponentID'] * 1;
		$global_component_configurators[$configurator['intServiceComponentID']] =
		                               "config_wifi_configurator";
	}
	
	// Hack to insert the component configurator array into PHP5's global scope
	// if it's not already there
	if(!isset($GLOBALS['global_component_configurators']))
	{
		$GLOBALS['global_component_configurators'] = $global_component_configurators;
	}
	else
	{
		foreach($global_component_configurators as $intIndex => $strConfigurator)
		{
			$GLOBALS['global_component_configurators'][$intIndex] = $strConfigurator;
		}
	}

	unset($array_wifi_configurators);

	//////////////////////////////////////////////////////////////////
	// GLOBAL!  DO NOT REMOVE
	//
	// For all machines which do not have GetText compiled on them
	// This is a replacement which does nothing.
	/////////////////////////////////////////////////////////////////
	if(!function_exists('_'))
	{
		function _($strUnLocalized)
		{
			//Localisation stuff later...

			return $strUnLocalized;
		}
	}


	
	/**
	* Component configurator
	*
	* Follows standard component configurator patter.  Do not modify name or param list.
	*
	* @access public
	* @global
	* <AUTHOR>
	* @param integer component id
	* @param string action
	* @return integer -1 on failure, otherwise the result of the action performed.
	*/
	function config_wifi_configurator ($component_id, $action) 
	{
		// Retrieve wifi components

		$objWifi = new CWifiComponent($component_id); 

		if(!$objWifi)
		{
			return(-1);
		}
		
		switch ($action) 
		{

		    case "auto_configure":
				return $objWifi->EnableWifiComponent();
			break;

		    case "auto_disable":
				$objWifi->prvSetStatus('queued-deactivate');	
				return $objWifi->DisableWifiComponent();
			break;

		    case "auto_enable":
				return $objWifi->EnableWifiComponent();
			break;

		    case "auto_refresh":
		    	return $strState = $objWifi->RefreshCurrentState();
			break;

		    case "auto_destroy":
				return $objWifi->DestroyWifiComponent(1);
			break;

		    default:
			break;
		}
	}
?>
