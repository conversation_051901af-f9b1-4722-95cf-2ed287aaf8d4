<?php
require_once '/local/data/mis/database/application_apis/Financial/Encryption.php';
require_once '/local/data/mis/database/class_libraries/Customers/C_Core_Service.php';
require_once '/local/data/mis/database/application_apis/Referrals/ReferralToken.class.php';

/**
 * Referrals
 *
 * @package
 * @version   $id$
 * @copyright PlusNet
 * <AUTHOR> <<EMAIL>>
 * @license   PHP License Version 3.01 {@link http://www.php.net/license/3_01.txt}
 */
class Referrals
{
    /**
     * intServiceId
     *
     * @var integer
     * @access protected
     */
    protected $intServiceId = 0;

    /**
     * objService
     *
     * @var object
     * @access protected
     */
    protected $objService;

    /**
     * arrReferralToken
     *
     * @var array
     * @access protected
     */
    protected $arrReferralToken = array();

    /**
     * arrReferralBonus
     *
     * @var array
     * @access private
     */
    private $arrReferralBonus = array();

    /**
     * dbhConnectionWrite - So we can use transactions all the way down
     *
     * @var resource
     * @access private
     */
    private $dbhConnectionWrite = false;

    /**
     * dbhConnectionRead
     *
     * @var resource
     * @access private
     */
    private $dbhConnectionRead = false;

    /**
     * __construct
     *
     * @param integer $intServiceId The service ID of the REFERRER
     *
     * @access public
     * @return void
     */
    public function __construct($intServiceId = null)
    {
        if (is_numeric($intServiceId) && $intServiceId > 0) {
            $this->intServiceId = (integer) $intServiceId;

            $this->objService = new C_Core_Service($intServiceId);
            $this->objService->fetchAccountDetails();
        }

        $this->dbhConnectionWrite = get_named_connection_with_db('financial');
        $this->dbhConnectionRead  = get_named_connection_with_db('financial_reporting');
    }

    /**
     * addReferralToken
     *
     * @param ReferralToken $objReferralToken objReferralToken
     *
     * @access public
     * @return void
     */
    public function addReferralToken(ReferralToken $objReferralToken)
    {
        // Work out if there are any bonus-based adjustments to make
        if (false === $objReferralToken->getSpecialOfferReferralValueId()) {
            $arrReferralBonus = $this->getReferralBonus($objReferralToken);

            if (!empty($arrReferralBonus) &&
                    $this->arrReferralBonus[$arrReferralBonus['intReferralTypeId']]['intReferralBonusTokenCount'] <
                    $this->arrReferralBonus[$arrReferralBonus['intReferralTypeId']]['intReferralBonusTokenMax']) {
                $intReferralValuePence = $objReferralToken->getOriginalValuePence() +
                        $arrReferralBonus['intSpecialOfferReferralValuePence'];
                $objReferralToken->setReferralValuePence($intReferralValuePence);
                $objReferralToken->setSpecialOfferReferralValueId($arrReferralBonus['intSpecialOfferReferralValueId']);
                $this->arrReferralBonus[$arrReferralBonus['intReferralTypeId']]['intReferralBonusTokenCount']++;
            }
        }

        $this->arrReferralToken[] = $objReferralToken;
    } // public function addReferralToken(ReferralToken $objReferralToken)

    /**
     * createAllReferralToken - Creates all the referral tokens assigned into the arrReferralToken
     *    array. It will also check for bonus updates.
     *
     * <AUTHOR> Jones <<EMAIL>>
     * @access public
     * @return boolean
     */
    public function createReferralTokenAll()
    {
        // Set this flag in the script that calls setConnectionWrite to cascade transactions
        if (!isset($this->dbhConnectionWrite['bolTransaction']) ||
                false === $this->dbhConnectionWrite['bolTransaction']) {
            if (false === ($resource = PrimitivesQueryOrExit(
                'BEGIN',
                $this->dbhConnectionWrite,
                'ReferralToken::processReferralCreditTokens::BEGIN',
                false
            ))) {
                throw new Exception('ReferralToken::create - failed to begin transaction');
            }
        }

        foreach ($this->arrReferralToken as $intIdx => $objReferralToken) {
            // You never know what someone might have done
            // The token must be valid AND we don't want an ID since this indicates the
            // token is already created.
            if ($objReferralToken instanceof ReferralToken &&
                    $objReferralToken->isValid() && !($objReferralToken->getReferralTokenId() > 0)) {
                $intReferrerServiceId     = $objReferralToken->getReferrerServiceId();
                $intReferralServiceId     = $objReferralToken->getReferralServiceId();
                $dteMonthCreditEarned     =
                    PrimitivesRealEscapeString($objReferralToken->getMonthCreditEarned(), $this->dbhConnectionWrite);
                $strYearMonth             =
                    PrimitivesRealEscapeString($objReferralToken->getYearMonth(), $this->dbhConnectionWrite);
                // Product Type ID - because this could be a service definition id or a service component id
                $intProductTypeId         = $objReferralToken->getProductTypeId();
                $intReferralValuePence    = $objReferralToken->getOriginalValuePence();
                $strCreatingTransaction   =
                    PrimitivesRealEscapeString($objReferralToken->getCreatingTransaction(), $this->dbhConnectionWrite);
                $intCreatingTransactionId = $objReferralToken->getCreatingTransactionId();
                $strSource = (!is_null($objReferralToken->getSource())) ? $objReferralToken->getSource()
                           :  basename(__FILE__) . ':' . __LINE__ . ':$Id: Referrals.class.php,v 1.11 2009-07-17 15:48:43 rjones Exp $';

                $arrReferralTokenInsertRow[$intIdx] =
                    "({$intReferrerServiceId}, {$intReferralServiceId}, '{$dteMonthCreditEarned}', '{$strYearMonth}', \n" .
                    "   NOW(), {$intProductTypeId}, '{$intReferralValuePence}', \n" .
                    "   {$intReferralValuePence}, '{$strCreatingTransaction}', {$intCreatingTransactionId}, '{$strSource}')";
            } // $objReferralToken->isValid() && !($objReferralToken->getReferralTokenId() > 0))
        } // foreach ($this->arrReferralToken as $intIdx => $objReferralToken)

        // No tokens can be validly created
        if (!empty($arrReferralTokenInsertRow)) {
            $strQuery = "
INSERT INTO referral_tokens
  (referrer_service_id, referral_service_id, month_credit_earned, year_n_month,
   date_token_created, referrals_account_type_when_created, original_value_in_pence,
   unspent_credit_in_pence, creating_transaction, creating_transaction_id, db_src)
  VALUES
" . implode(",\n", $arrReferralTokenInsertRow);

            if (false === PrimitivesQueryOrExit(
                $strQuery,
                $this->dbhConnectionWrite,
                'Referrals::createReferralTokenAll',
                false
            )) {
                // Attempt to rollback the transaction. Doesn't matter if this fails because the
                // transaction should implicitly rollback after the script ends anyway.
                PrimitivesQueryOrExit(
                    'ROLLBACK',
                    $this->dbhConnectionWrite,
                    'Referrals::createReferralTokenAll::ROLLBACK_1',
                    false
                );

                // Indicate transaction ended
                $this->dbhConnectionWrite['bolTransaction'] = false;
                throw new Exception(
                    'Referrals::createReferralTokenAll - failed to create special offer referral token record'
                );
            }

            if (count($arrReferralTokenInsertRow) !== PrimitivesAffectedRowsGet($this->dbhConnectionWrite)) {
                // Attempt to rollback the transaction. Doesn't matter if this fails because the
                // transaction should implicitly rollback after the script ends anyway.
                PrimitivesQueryOrExit(
                    'ROLLBACK',
                    $this->dbhConnectionWrite,
                    'Referrals::createReferralTokenAll::ROLLBACK_2',
                    false
                );

                // Indicate transaction ended
                $this->dbhConnectionWrite['bolTransaction'] = false;
                // Parity Error
                throw new Exception(
                    'Referrals::createReferralTokenAll - The number of tokens created does not match the number ' .
                    'of tokens requested.'
                );
            }

            // Go through the created tokens and set their insert id
            $intReferralTokenId = PrimitivesInsertIdGet($this->dbhConnectionWrite);
            foreach ($arrReferralTokenInsertRow as $strReferralTokenInsertRow) {
                $this->arrReferralToken[$intIdx]->setReferralTokenId($intReferralTokenId);
                $intSpecialOfferReferralValueId = $this->arrReferralToken[$intIdx]->getSpecialOfferReferralValueId();
                if (false !== $intSpecialOfferReferralValueId) {
                    $arrReferralTokenSpecialOfferInsertRow[] =
                        "({$intReferralTokenId}, {$intSpecialOfferReferralValueId})";
                }

                $intReferralTokenId++; // Tokens should be sequential
            }

            if (!empty($arrReferralTokenSpecialOfferInsertRow)) {
                $strQuery = "
INSERT INTO tblReferralTokenSpecialOffer
  (intReferralTokenId, intSpecialOfferReferralValueId)
  VALUES
" . implode(",\n", $arrReferralTokenSpecialOfferInsertRow);

                if (false === PrimitivesQueryOrExit(
                    $strQuery,
                    $this->dbhConnectionWrite,
                    'Referrals::createReferralTokenAll',
                    false
                )) {
                    // Attempt to rollback the transaction. Doesn't matter if this fails because the
                    // transaction should implicitly rollback after the script ends anyway.
                    PrimitivesQueryOrExit(
                        'ROLLBACK',
                        $this->dbhConnectionWrite,
                        'Referrals::createReferralTokenAll::ROLLBACK_3',
                        false
                    );

                    // Indicate transaction ended
                    $this->dbhConnectionWrite['bolTransaction'] = false;

                    throw new Exception(
                        'Referrals::createReferralTokenAll - Unable to create special offer referral token(s)'
                    );
                }
            } // if (!empty($arrReferralTokenSpecialOfferInsertRow))
        } // if (!empty($arrReferralTokenInsertRow))

        // Only commit if this transaction is not set up in the calling script
        if (!isset($this->dbhConnectionWrite['bolTransaction']) ||
                false === $this->dbhConnectionWrite['bolTransaction']) {
            if (false === PrimitivesQueryOrExit(
                'COMMIT',
                $this->dbhConnectionWrite,
                'ReferralToken::processReferralCreditTokens::COMMIT',
                false
            )) {
                $this->dbhConnectionWrite['bolTransaction'] = false;
                throw new Exception(
                    'Referrals::createReferralTokenAll - Failed to commit transaction to create tokens'
                );
            }

            // Indicate transaction ended
            $this->dbhConnectionWrite['bolTransaction'] = false;
        }

        return true;
    } // public function createReferralTokenAll()

    /**
     * createReferralToken - Allows creation of a single referral token
     *
     * @param ReferralToken $objReferralToken objReferralToken
     *
     * @access public
     * @return boolean
     */
    public function createReferralToken(ReferralToken $objReferralToken)
    {
        $this->addReferralToken($objReferralToken);
        return $this->createReferralTokenAll();
    }

    /**
     * getCount - gets the count of referrals for the given Service ID, including
     *   pending referrals, but not separating WLR components.
     *
     * @param integer $intServiceId intServiceId
     *
     * @static
     * @access public
     * @return integer
     */
    public static function getCount($intServiceId)
    {
        if (!is_numeric($intServiceId) || intval($intServiceId) <= 0) {
            return false;
        }
        $intServiceId = (int) $intServiceId;

        $strQuery = "
  SELECT  count(1) AS intReferrals
    FROM  userdata.signup_referrals sr
   WHERE  sr.referral_from_sid = {$intServiceId}
     AND (sr.referral_signedup_sid > 0 OR sr.dtmReferredDate >= DATE_SUB(NOW(), INTERVAL 90 DAY))
     AND  sr.bolActive = 1
";

        $dbhConnection = get_named_connection_with_db('userdata_reporting');

        $resResult     = PrimitivesQueryOrExit($strQuery, $dbhConnection, 'Referrals::getCount', false);

        if (false === $resResult) {
            return false;
        }

        $intReferrals = PrimitivesResultGet($resResult, 'intReferrals');

        return $intReferrals;
    } // static public function getCount($intServiceId)

    /**
     * getReferralBonus - WARNING... this method throws exceptions which must be caught,
     *     or else billing could die!
     *
     * @param ReferralToken $objReferralToken $objReferralToken
     *
     * @access public
     * @return array
     */
    public function getReferralBonus(ReferralToken $objReferralToken)
    {
        $intReferredServiceId = $objReferralToken->getReferralServiceId();
        $intReferralValueId   = $objReferralToken->getReferralValueId();
        $intReferralTypeId    = $objReferralToken->getReferralTypeId();

        // Make sure it's a valid int
        if (!is_numeric($intReferredServiceId) || (int) $intReferredServiceId <= 0) {
            throw new Exception('Referred Service ID is invalid');
        }
        $intReferredServiceId = (int) $intReferredServiceId;


        // Again, this should be an int
        if (!is_numeric($intReferralValueId) || (int) $intReferralValueId <= 0) {
            throw new Exception('Referral Value ID is invalid');
        }
        $intReferralValueId = (int) $intReferralValueId;

        $dbhConnection = get_named_connection_with_db('product_reporting');

        if (2 == $intReferralTypeId) {
            // If we have to apply this to anything other than WLR this needs rethinking.
            // Yah. I know this query is ugly. But it's the only way we can reasonably
            // determine when a WLR component was signed up
            $strQuery = "
    SELECT rv.intReferralTypeId,
           sorv.intSpecialOfferReferralValueId,
           sorv.intSpecialOfferReferralValuePence,
           sorv.intReferralBonusTokenMax
      FROM userdata.signup_referrals sr
INNER JOIN userdata.services refd
        ON sr.referral_signedup_sid = refd.service_id
INNER JOIN (

            SELECT com.service_id,
                   IF (
                       MIN(cwlr.dtmStart) < MIN(com.creationdate),
                       MIN(cwlr.dtmStart),
                       MIN(com.creationdate)
                   ) AS startdate
              FROM userdata.components com
        INNER JOIN userdata.tblProductComponentInstance pci
                ON com.component_id = pci.intComponentId
        INNER JOIN userdata.tblConfigWlrLineRent cwlr
                ON pci.intProductComponentInstanceId = cwlr.intProductComponentInstanceId
        INNER JOIN userdata.components comst
                ON com.service_id = comst.service_id
        INNER JOIN userdata.tblConfigWlrLineRent cwlrst
                ON pci.intProductComponentInstanceId = cwlrst.intProductComponentInstanceId
             WHERE comst.status IN ('active', 'queued-deactivate', 'deactive', 'queued-reactivate')
               AND com.service_id = {$intReferredServiceId}
         GROUP BY com.service_id

  ) wlr ON refd.service_id = wlr.service_id
INNER JOIN products.tblSpecialOffer so
        ON wlr.startdate >= so.dtmOfferStartDate
       AND (wlr.startdate <= so.dtmOfferEndDate OR so.dtmOfferEndDate IS NULL)
INNER JOIN products.tblSpecialOfferType sot
        ON so.intSpecialOfferTypeId = sot.intSpecialOfferTypeId
       AND sot.vchSpecialOfferType = 'TokenBased'
INNER JOIN products.tblSpecialOfferReferralValue sorv
        ON so.intSpecialOfferId = sorv.intSpecialOfferId
INNER JOIN products.tblReferralValue rv
        ON sorv.intReferralValueId = rv.intReferralValueId
     WHERE refd.service_id           = {$intReferredServiceId}
       AND sorv.intReferralValueId   = {$intReferralValueId}";
        } else {
            // First query to see if the user is eligible for special offer
            $strQuery = "
    SELECT rv.intReferralTypeId,
           sorv.intSpecialOfferReferralValueId,
           sorv.intSpecialOfferReferralValuePence,
           sorv.intReferralBonusTokenMax
      FROM userdata.signup_referrals sr
INNER JOIN userdata.services refd
        ON sr.referral_signedup_sid = refd.service_id
INNER JOIN products.tblSpecialOffer so
        ON refd.startdate >= so.dtmOfferStartDate
       AND (refd.startdate <= so.dtmOfferEndDate OR so.dtmOfferEndDate IS NULL)
INNER JOIN products.tblSpecialOfferType sot
        ON so.intSpecialOfferTypeId = sot.intSpecialOfferTypeId
       AND sot.vchSpecialOfferType = 'TokenBased'
INNER JOIN products.tblSpecialOfferReferralValue sorv
        ON so.intSpecialOfferId = sorv.intSpecialOfferId
INNER JOIN products.tblReferralValue rv
        ON sorv.intReferralValueId = rv.intReferralValueId
     WHERE refd.service_id           = {$intReferredServiceId}
       AND sorv.intReferralValueId   = {$intReferralValueId}";
        } // if (1 == $intReferralTypeId)

        $resReferrals = PrimitivesQueryOrExit($strQuery, $dbhConnection, 'Referrals::getReferralBonus', false);
        if (false === $resReferrals) {
            // If the query is well formed why should this fail? Missing tables?
            throw new Exception('Failed to execute Referral Bonus query');
        }

        $intRows = PrimitivesNumRowsGet($resReferrals);
        if (0 === $intRows) {
            // No matches - no bonus applies
            return false;
        }

        $arrReferralBonus = PrimitivesResultGet($resReferrals);

        $intReferralBonusTokenMax = (int) $arrReferralBonus['intReferralBonusTokenMax'];

        // Second query. Make sure we haven't given the user all their allowed tokens already.
        $strQuery = "
    SELECT COUNT(rtso.intReferralTokenId) AS intReferralBonusTokenCount
      FROM financial.referral_tokens rt
INNER JOIN financial.tblReferralTokenSpecialOffer rtso
        ON rt.referral_token_id = rtso.intReferralTokenId
INNER JOIN products.tblSpecialOfferReferralValue sorv
        ON rtso.intSpecialOfferReferralValueId = sorv.intSpecialOfferReferralValueId
INNER JOIN products.tblReferralValue rv
        ON sorv.intReferralValueId = rv.intReferralValueId
     WHERE rt.referral_service_id     = {$intReferredServiceId}
       AND rv.intReferralTypeId       = {$intReferralTypeId}
    HAVING intReferralBonusTokenCount < {$intReferralBonusTokenMax}";

        $resReferralTokens = PrimitivesQueryOrExit($strQuery, $dbhConnection, 'Referrals::getReferralBonus', false);
        if (false === $resReferralTokens) {
            // If the query is well formed why should this fail? Missing tables?
            throw new Exception('Failed to execute Referral Bonus Token query');
        }

        $intRows = PrimitivesNumRowsGet($resReferralTokens);
        if (0 === $intRows) {
            // Nothing returned - no bonus applies
            return false;
        }

        $arrReferralBonus['intReferralBonusTokenCount'] = PrimitivesResultGet(
            $resReferralTokens,
            'intReferralBonusTokenCount'
        );

        // We keep a record of the bonus token count, so we can do a multi-insert and ensure
        // we don't apply too many credits for the same type.
        if (!isset($this->arrReferralBonus[$intReferralTypeId])) {
            $this->arrReferralBonus[$intReferralTypeId] = array(
                    'intReferralBonusTokenCount' => $arrReferralBonus['intReferralBonusTokenCount'],
                    'intReferralBonusTokenMax'   => $arrReferralBonus['intReferralBonusTokenMax']);
        }

        return $arrReferralBonus;
    } // public static function getReferralBonus($intReferredServiceId, $intReferralValueId, $strReferralType)



    //
    // Referral Credits - Code for handling referral values which exceed the customer's
    //                    monthly subscription fee.
    //

    /**
     * Add a referral credit entry to be picked up by the Referral Refund script
     *
     * @param integer $intInvoiceId    intInvoiceId
     * @param integer $intCreditValue  intCreditValue
     * @param boolean $bolRedeemTokens bolRedeemTokens
     *
     * @access public
     * @return boolean
     */
    public function addReferralCredit($intInvoiceId, $intCreditValue, $bolRedeemTokens = false)
    {
        // The only parameter that absolutely must have a proper numeric value
        if (!is_numeric($intCreditValue)) {
            return false;
        }

        if (false === PrimitivesQueryOrExit(
            'BEGIN',
            $this->dbhConnectionWrite,
            'Referrals::addReferralCredit::BEGIN',
            false
        )) {
            // Failed to start transaction
            return false;
        }

        // Flag to indicate we've started a transaction
        $this->dbhConnectionWrite['bolTransaction'] = true;

        if (empty($intInvoiceId) || !is_numeric($intInvoiceId)) {
            $intInvoiceId = 'NULL';
        }

        // Have to assign string from boolean to make it work with SQL.
        // (TRUE will return 1 and FALSE 0 in MySQL)
        $strRedeemTokens = ($bolRedeemTokens) ? 'TRUE' : 'FALSE';

        // Sanitize input
        $intInvoiceId    = PrimitivesRealEscapeString($intInvoiceId, $this->dbhConnectionWrite);
        $intCreditValue  = PrimitivesRealEscapeString($intCreditValue, $this->dbhConnectionWrite);
        $strRedeemTokens = PrimitivesRealEscapeString($strRedeemTokens, $this->dbhConnectionWrite);

        $strQuery = "INSERT INTO financial.tblReferralCredit
                        SET intServiceId           = {$this->intServiceId},
                            intInvoiceId           = {$intInvoiceId},
                            intReferralCreditValue = {$intCreditValue},
                            bolRedeemTokens        = {$strRedeemTokens}";

        if (false === PrimitivesQueryOrExit(
            $strQuery,
            $this->dbhConnectionWrite,
            'Referrals::addReferralCredit::tblReferralCredit',
            false
        )) {
            // Attempt to rollback the transaction. Doesn't matter if this fails because the
            // transaction should implicitly rollback after the script ends anyway.
            PrimitivesQueryOrExit(
                'ROLLBACK',
                $this->dbhConnectionWrite,
                'Referrals::addReferralCredit::ROLLBACK',
                false
            );
            return false;
        }

        if ($bolRedeemTokens) {
            if (false === $this->processReferralCreditTokens(
                array(
                    'intInvoiceId'           => $intInvoiceId,
                    'intReferralCreditValue' => $intCreditValue
                ),
                $intCreditValue
            )) {
                // Everything should have been rolled back already
                return false;
            };
        }

        if (false === PrimitivesQueryOrExit(
            'COMMIT',
            $this->dbhConnectionWrite,
            'Referrals::addReferralCredit::BEGIN',
            false
        )) {
            // Failed to commit transaction
            return false;
        }

        // Flag to indicate we've ended the current transaction
        $this->dbhConnectionWrite['bolTransaction'] = false;

        return true;
    } // public function addReferralCredit($intInvoiceId, $intCreditValue, $bolRedeemTokens = false)

    /**
     * Referral Credit has been redeemed in some fashion (probably via the Referral
     * Refund script
     *
     * @param array $arrReferralCredit arrReferralCredit
     *
     * @access public
     * @return boolean
     */
    public function redeemReferralCredit($arrReferralCredit)
    {
        // Compile list of IDs to update DB
        foreach ($arrReferralCredit as $recReferralCreditDetail) {
            if (!is_numeric($recReferralCreditDetail['intReferralCreditId'])) {
                // CRITICAL PROBLEM! The referral credit id is not of the proper type.
                // Return back to calling functionality, which should handle this return
                return false;
            }

            $arrReferralCreditId[] = $recReferralCreditDetail['intReferralCreditId'];
        }

        $strQuery = 'UPDATE financial.tblReferralCredit
                        SET dtmProcessed = NOW()
                      WHERE intReferralCreditId IN (' . implode(',', $arrReferralCreditId) . ')
                        AND dtmProcessed IS NULL';

        $dbhConnection = get_named_connection_with_db('financial');

        PrimitivesQueryOrExit(
            $strQuery,
            $dbhConnection,
            'ReferralRefundManager::markReferralCreditProcessed',
            false
        );

        return true;
    } // public function redeemReferralCredit($arrReferralCredit)

    /**
     * processReferralCreditTokens
     *
     * @param array   $arrReferralCredit arrReferralCredit
     * @param integer $intMaxRedeemPence intMaxRedeemPence
     *
     * @access public
     * @return boolean
     */
    public function processReferralCreditTokens($arrReferralCredit, $intMaxRedeemPence)
    {
        $intCreditNoteId = $this->createCreditNote($arrReferralCredit);
        $intInvoiceId = $arrReferralCredit['intInvoiceId'];
        $arrReferrals = financial_referral_amount_redeemable($this->intServiceId, 0);
        $strSource = addslashes(
            basename(__FILE__) .
            ':' .
            __LINE__ .
            ':$Id: Referrals.class.php,v 1.11 2009-07-17 15:48:43 rjones Exp $'
        );
        if (is_array($arrReferrals['assessed_referral_tokens'])) {
            foreach ($arrReferrals['assessed_referral_tokens'] as $arrToken) {
                $intRedeemTokenPence = ($arrToken['available_for_refund'] < $intMaxRedeemPence)
                                     ? $arrToken['available_for_refund']
                                     : $intMaxRedeemPence;
                if ($intRedeemTokenPence > 0) {
                    $objReferralToken = new ReferralToken();
                    $objReferralToken->setConnectionWrite($this->dbhConnectionWrite);
                    $objReferralToken->setSource($strSource);
                    $objReferralToken->setReferralTokenId($arrToken['referral_token_id']);
                    $objReferralToken->redeem($intCreditNoteId, $intInvoiceId, $intRedeemTokenPence);
                    $intMaxRedeemPence = $intMaxRedeemPence - $intRedeemTokenPence;
                }
            }
        }

        return true;
    } // function ProcessRefund()

    /**
     * createCreditNote
     *
     * @param array &$arrReferralCredit - Passed by reference in case we have to assign in a newly generated invoice ID
     *
     * @access public
     * @return integer
     */
    public function createCreditNote(&$arrReferralCredit)
    {
        require_once DATABASE_LIBRARY_ROOT.'CoreObjects/Financial/CFinancialHelper.inc';

        $floCreditValue = $arrReferralCredit['intReferralCreditValue'] / 100;
        $intAccountId   = $this->objService->getAccountID();
        $intInvoiceId   = $arrReferralCredit['intInvoiceId'];

        $strDescription = ($intInvoiceId == '')
                        ? 'Referral Bonus Payment'
                        : 'Referral Tokens Credit Payment';

        // Do we have a valid invoice to record this credit against?
        if (!is_numeric($intInvoiceId) || $intInvoiceId < 1) {
            // Create an invoice of zero pennies to assign the refund against
            $intInvoiceId = financial_sales_invoice_add(
                $intAccountId,
                0,
                VAT_NOW_IN_CFINANCIALHELPER,
                $strDescription,
                null,
                0,
                0
            );

            // Set the invoice status to fully refunded
            financial_sales_invoice_modify($intInvoiceId, 'fully_refunded');
            $arrReferralCredit['intInvoiceId'] = $intInvoiceId;


            // We've created a new zero-value invoice, and it won't have any line items
            // associated with it, therefore we'll want to add a 'placeholder' line item
            // a little later on.
            $bolNewInvoiceCreated = true;

        } else {

            // We didn't create a new invoice as we already had an invoice ID supplied to
            // us, therefore business as usual.
            $bolNewInvoiceCreated = false;
        }

        $arrOriginalInvoice = financial_sales_invoice_get($intInvoiceId);

        // Add credit note
        $intCreditNoteId = financial_sales_invoice_add(
            $intAccountId,
            -($floCreditValue),
            $arrOriginalInvoice['vat_applied'],
            $strDescription,
            null,
            $intInvoiceId
        );

        // Update the status of the credit note and associate the line item with
        // the credit note
        financial_sales_invoice_modify($intCreditNoteId, 'fully_paid');
        financial_sales_invoice_item_add($intCreditNoteId, $strDescription, 1, null, -($floCreditValue));

        // Set the type of credit note to other
        financialUpdateCreditTypeID($intCreditNoteId, 5);

        // Update the credit note's description
        financialUpdateCreditNoteOtherDescription($intCreditNoteId, $strDescription);

        // Add refund
        financial_sales_refund_add($intInvoiceId, $floCreditValue, null, 0, 0, null);


        // Last thing we do is the special-case if we actually created a new invoice.
        // We'll need to associate some sort of line item against it to make reference
        // to the credit note; otherwise it makes the invoice look a bit silly
        // without any line items.

        if ($bolNewInvoiceCreated) {

            $strLineItemDescription = "Notice of {$strDescription} credit";


            // We can't use the intCreditNoteId value directly, as that's an internal
            // database ID - we need to instead acquire the human-readable invoice
            // reference instead.

            $strInvoiceRef = Financial_InvoiceIssuer::mapSalesInvoiceIdToInvoiceRef($intCreditNoteId);

            if (false != $strInvoiceRef) {

                // add some extra information in the event that it didn't fail (not that it should!)

                $strLineItemDescription .= ", invoice {$strInvoiceRef}";
            }


            financial_sales_invoice_item_add($intInvoiceId, $strLineItemDescription, 1, null, 0);
        }


        return $intCreditNoteId;
    } // private function createCreditNote()

    /**
     * getAllAccountsRefundCredits
     *
     * @static
     * @access public
     * @return array
     */
    public static function getAllAccountsReferralCredits()
    {
        static $arrReferralCreditAccounts = array();

        if (empty($arrReferralCreditAccounts)) {
            $dbhConnection = get_named_connection_with_db('financial_reporting');
            $strQuery = "
    SELECT s.service_id as intServiceID,
           u.user_id as intUserID,
           s.username,
           concat(u.forenames, ' ', u.surname) as real_name,
           i.short_name as isp_brand_name,
           i.url_domain as isp_url,
           u.email as strEmail,
           CONCAT(
               'postmaster',
                substring(replace(default_customer_email_address,'username',username),
                locate('@',default_customer_email_address) )
           ) AS strEmailAddress,
           ddi.name AS strDdAccName,
           ddi.bank_sort_code AS strDdAccSortCode,
           ddi.bank_account_number AS strDdAccNumber,
           CONCAT(rbd.strAccForenames, ' ', rbd.strAccSurname) AS strRefAccName,
           rbd.strAccSortCode AS strRefAccSortCode,
           rbd.strAccNumber AS strRefAccNumber,
           rc.intReferralCreditId,
           rc.intInvoiceId,
           rc.intReferralCreditValue,
           rc.bolRedeemTokens
      FROM userdata.services s
INNER JOIN userdata.users u ON s.user_id = u.user_id
INNER JOIN products.service_definitions sd ON s.type = sd.service_definition_id
INNER JOIN products.visp_config i ON s.isp = i.isp
INNER JOIN financial.tblReferralCredit rc ON s.service_id = rc.intServiceId AND rc.dtmProcessed IS NULL
 LEFT JOIN transactions.direct_debit_instructions ddi ON s.service_id = ddi.service_id
       AND ddi.direct_debit_instruction_status_id = 5
 LEFT JOIN financial.tblReferralBankDetail rbd ON s.service_id = rbd.intServiceID
     WHERE sd.name NOT LIKE '%Staff%' AND sd.name NOT LIKE '%Plus.Net.Uk%'
       AND (ddi.bank_account_number IS NOT NULL || rbd.strAccNumber IS NOT NULL)
       AND s.status='active' AND s.username NOT LIKE 'ztest%'
";

            $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);
            $arrReferralCredits = PrimitivesResultsAsArrayGet($resResult);
            $arrReferralCreditAccounts = array();
            $arrProcessedReferralCredits = array();

            // Sort out the bank account details to use either DD or Ref details
            foreach ($arrReferralCredits as $recReferralCredit) {
                // Wait! We've already processed this particular referral credit! Don't do it again!
                if (in_array($recReferralCredit['intReferralCreditId'], $arrProcessedReferralCredits)) {
                    continue;
                }

                // A carry-over error would be very embarrassing... :$
                $arrBankDetails = array();

                // If we have referral bank details use those. Otherwise, use the DD details
                if ((trim($recReferralCredit['strRefAccNumber'])) != '') {
                    $arrBankDetails = array(
                        'strAccName'     => $recReferralCredit['strRefAccName'],
                        'strAccNumber'   => $recReferralCredit['strRefAccNumber'],
                        'strAccSortCode' => $recReferralCredit['strRefAccSortCode']
                    );
                } else {
                    $arrBankDetails = array(
                        'strAccName'     => $recReferralCredit['strDdAccName'],
                        'strAccNumber'   => $recReferralCredit['strDdAccNumber'],
                        'strAccSortCode' => $recReferralCredit['strDdAccSortCode']
                    );

                }

                // No bank details... were they removed? :(
                if (empty($arrBankDetails['strAccNumber'])) {
                    continue;
                }

                // Now overwrite current details with new / unecrypted details
                $recReferralCredit = array_merge($recReferralCredit, $arrBankDetails);

                $intServiceId = (integer) $recReferralCredit['intServiceID'];
                if (isset($arrReferralCreditAccounts[$intServiceId])) {
                    $arrReferralCreditAccounts[$intServiceId]['total_unspent'] += $recReferralCredit['intReferralCreditValue'];
                } else {
                    $arrReferralCreditAccounts[$intServiceId] = $recReferralCredit;
                    $arrReferralCreditAccounts[$intServiceId]['total_unspent'] = $recReferralCredit['intReferralCreditValue'];
                }
                $arrReferralCredit = array('intReferralCreditId'    => $recReferralCredit['intReferralCreditId'],
                                           'intInvoiceId'           => $recReferralCredit['intInvoiceId'],
                                           'bolRedeemTokens'        => $recReferralCredit['bolRedeemTokens'],
                                           'intReferralCreditValue' => $recReferralCredit['intReferralCreditValue']);
                // Keep a record of processed referral credits to avoid any accidental processing of
                // duplicate records. Shouldn't be possible, but just in case.
                $arrProcessedReferralCredits[] = $recReferralCredit['intReferralCreditId'];
                unset($recReferralCredit['intReferralCreditId']);
                unset($recReferralCredit['intInvoiceId']);
                unset($recReferralCredit['bolRedeemTokens']);
                unset($recReferralCredit['intReferralCreditValue']);
                $arrReferralCreditAccounts[$intServiceId]['arrReferralCredit'][] = $arrReferralCredit;
            } // foreach ($arrRefundCreditAccounts as $recRefundCreditAccount)
        } // if (empty($arrRefundCreditAccounts))

        return $arrReferralCreditAccounts;
    } // static function getAllAccountsRefundCredits()
}
