<?php
	
	require_once("/local/data/mis/database/database_libraries/sql_primitives.inc");

	///////////////////////////////////////////////////////////////////////////
	//
	// Configurator for use with all visps to configure Night Dial
	//
	///////////////////////////////////////////////////////////////////////////

        $connection = get_named_connection_with_db('unprivileged_reporting');

        $query = 'SELECT service_component_id
		            FROM products.component_nightdial_config';

        $result = PrimitivesQueryOrExit($query,$connection, 'Make nightdial entries');

        $array_nightdial_configurators = PrimitivesResultsAsArrayGet($result);

        foreach($array_nightdial_configurators as $configurator)
        {
                $global_component_configurators[$configurator['service_component_id']] = 'config_nightdial_configurator';
        }
	
	// Hack to insert the component configurator array into PHP5's global scope
	// if it's not already there
	if(!isset($GLOBALS['global_component_configurators']))
	{
		$GLOBALS['global_component_configurators'] = $global_component_configurators;
	}
	else
	{
		foreach($global_component_configurators as $intIndex => $strConfigurator)
		{
			$GLOBALS['global_component_configurators'][$intIndex] = $strConfigurator;
		}
	}
	
	function nightdial_auto_activate($component_id)
	{
		$component = userdata_component_get($component_id);

		switch($component['status'])
		{
			case 'destroyed':
			case 'queued-destroy':
				break;

			case 'unconfigured':
			case 'queued-activate':
				userdata_component_set_status($component_id, 'active');
				break;

			case 'active':
				break;
		}
	}

	function nightdial_auto_destroy($component_id)
	{
		$component = userdata_component_get($component_id);

		switch($component['status'])
		{
			case 'unconfigured':
			case 'destroyed':
			case 'queued-destroy':
			case 'queued-activate':
			case 'active':
				userdata_component_set_status($component_id, 'destroyed');
				break;
		}
	}


	//
	// Night Dial Component Configurator
	//

	function config_nightdial_configurator($component_id, $action)
	{
		switch($action)
		{
			case 'auto_configure':
				nightdial_auto_activate($component_id);
				break;

			case 'auto_disable':
				break;

			case 'auto_enable':
				break;

			case 'auto_destroy':
				nightdial_auto_destroy($component_id);
				break;

			case 'auto_refresh':
				break;

			default:
				break;
		}
	}
?>
