<?php
/**
 * This file contains the main RMA class used by the hardware bundle system.
 *
 * IMPORTANT:
 * The database has been designed to allow each item of the order to be
 * handled by a different supplier, thereby having its own status. However,
 * these classes have been written with the assumption that the supplier
 * and status for each item in an order are the same.
 *
 * Functions marked *** STATIC *** don't require an instansiated object to be used.
 * i.e. You can just call classname::functionname()
 *
 * Hungarian notation used:
 *
 * str : String
 * int : Integer
 * arr : Array of items (not a row from a database)
 * r   : Record (associative array, eg a row from the database)
 * bin : Boolean
 * h   : Handle (eg database connection or result, or a file handle)
 * m_  : Class member variable (shouldn't be touched outside the class)
 *
 * hardware-bundle-classes.inc
 * <PERSON>, October 2002
 * Revised <PERSON>, June 2004
 * Revised Andy <PERSON>, July 2005
 * $Id: rma-hardware-bundle-classes.inc,v 1.17 2007-06-08 13:15:56 kp<PERSON>ybyszewski Exp $*
 *
 * @package LegacyCodebase
 * <AUTHOR> <<EMAIL>>
 * @link    link
 */

require_once '/local/data/mis/database/application_apis/HardwareBundle/HardwareRmaProcess.class.php';
?>
