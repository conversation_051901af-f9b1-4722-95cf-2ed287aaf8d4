<?php
/**
 * YouView Access Configurator
 *
 * @package   LegacyCodebase
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Plusnet
 *
 */

require_once '/local/data/mis/database/database_libraries/components/component-defines.inc';
require_once '/local/data/mis/database/database_libraries/sql_primitives.inc';
require_once '/local/data/mis/database/database_libraries/components/CTvComponentDestroyer.php';

$dbConnection = get_named_connection_with_db('product');
$selectYouViewComponents = "SELECT scp.intServiceComponentID
	            FROM products.tblServiceComponentProduct scp
	        INNER JOIN products.tblServiceComponentProductType scpt
	            ON scp.intServiceComponentProductTypeID = scpt.intServiceComponentProductTypeID
	        WHERE scpt.vchHandle = 'YOUVIEW_TV'";

$queryResult = PrimitivesQueryOrExit(
    $selectYouViewComponents,
    $dbConnection,
    'Make YouView configurator entries'
);

foreach (PrimitivesResultsAsArrayGet($queryResult) as $serviceComponentProduct) {
    $global_component_configurators[(int)$serviceComponentProduct['intServiceComponentID']] =
        'config_youview_configurator';
}
$globalsGlobalComponentConfigurators = &$GLOBALS['global_component_configurators'];
if (empty($globalsGlobalComponentConfigurators)) {
    $globalsGlobalComponentConfigurators = $global_component_configurators;
} else {
    foreach ($global_component_configurators as $key => $configuratorName) {
        $globalsGlobalComponentConfigurators[$key] = $configuratorName;
    }
}

/**
 * Config YouView Configurator
 *
 * @param int    $componentId Component ID
 * @param string $action      Action
 *
 * @return void
 */
function config_youview_configurator($componentId, $action)
{
    if ($action == 'auto_destroy') {
        $cTvComponentDestroyer = new CTvComponentDestroyer($componentId);
        $cTvComponentDestroyer->destroyCComponent();
    }
}
