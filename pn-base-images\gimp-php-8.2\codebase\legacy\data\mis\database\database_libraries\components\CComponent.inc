<?php

/**
* Abstract component base class.
*
*
*
* @package    Core
* @access     public
* <AUTHOR> <<EMAIL>>
* @version    $Id: CComponent.inc,v 1.21.2.4 2009/07/17 09:07:37 ssmith Exp $
* @filesource
*/

require_once '/local/data/mis/database/database_libraries/components/component-defines.inc';
require_once '/local/data/mis/database/database_libraries/sql_primitives.inc';
require_once '/local/data/mis/database/database_libraries/userdata-access.inc';
require_once('/local/data/mis/database/database_libraries/CoreObjects/CObject/CObject.inc');
require_once('/local/data/mis/database/database_libraries/CoreObjects/Utility/CValidator.inc');
require_once('/local/data/mis/database/database_libraries/CoreObjects/EventLogging/CComponentEvent.inc');

// Service Component IDs
configDefineIfMissing('SCID_PLUSTALK', 398);
configDefineIfMissing('SCID_PLUSTALK_RES_PAYG', 403);
configDefineIfMissing('SCID_PLUSTALK_RES_EW', 404);
configDefineIfMissing('SCID_PLUSTALK_RES_ANYTIME', 405);
configDefineIfMissing('SCID_PLUSTALK_BUS_PAYG', 406);
configDefineIfMissing('SCID_PLUSTALK_BUS_EW', 407);
configDefineIfMissing('SCID_PLUSTALK_BUS_ANYTIME', 408);
configDefineIfMissing('SCID_PLUSTALK_STAFF', 409);
configDefineIfMissing('SCID_METRONET_EMAIL', 499);
configDefineIfMissing('SCID_PLUSTALK_120', 502);
configDefineIfMissing('SCID_PLUSTALK_240', 503);
configDefineIfMissing('SCID_PLUSTALK_360', 504);
configDefineIfMissing('SCID_PLUSTALK_480', 505);
configDefineIfMissing('SCID_ENHANCED_CARE', COMPONENT_ENHANCED_CARE);
configDefineIfMissing('SCID_WLR_ANYTIME', COMPONENT_WLR_ANYTIME);
configDefineIfMissing('SCID_WLR_ANYTIME_PLUS', COMPONENT_WLR_ANYTIME_PLUS);
configDefineIfMissing('SCID_WLR_EVENING_WEEKEND', COMPONENT_WLR_EVENING_WEEKEND);

configDefineIfMissing('SCID_WLR_PN_TALK_ANYTIME', COMPONENT_WLR_PN_TALK_ANYTIME);
configDefineIfMissing('SCID_WLR_PN_TALK_EW', COMPONENT_WLR_PN_TALK_EVENING_WEEKEND);

configDefineIfMissing('SCID_WLR_TALK', COMPONENT_WLR_TALK);
configDefineIfMissing('SCID_WLR_TALK_ANYTIME', COMPONENT_WLR_TALK_ANYTIME);

configDefineIfMissing('SCID_WLR_ESSENTIAL', COMPONENT_WLR_ESSENTIAL);

configDefineIfMissing('SCID_GREENBEE_PHONE', COMPONENT_WLR_GREENBEE_PHONE);
configDefineIfMissing('SCID_GREENBEE_BUNDLED_PHONE', COMPONENT_WLR_GREENBEE_PHONE_BUNDLE);
configDefineIfMissing('SCID__BROADBANDSCOPE_8_STATIC_IP', COMPONENT_BROADBANDSCOPE_8_STATIC_IP);

configDefineIfMissing('SCID_ADD_ON_PRODUCT_BUNDLE', 511);
configDefineIfMissing('SCID_SUBSCRIPTION', 1);

configDefineIfMissing('SCID_SECURITY_BULL_GUARD', 546);
configDefineIfMissing('SCID_SECURITY_BULL_GUARD_TRIAL', 547);

configDefineIfMissing('SCID_MAAF_WEBMAIL', 534);

configDefineIfMissing('SCID_INTERNET_CONNECTION_MADASAFISH_MAX_PREMIER', 530);
configDefineIfMissing('SCID_INTERNET_CONNECTION_MADASAFISH_MAX_PLUS', 529);
configDefineIfMissing('SCID_INTERNET_CONNECTION_MADASAFISH_MAX', 528);
configDefineIfMissing('SCID_INTERNET_CONNECTION_MADASAFISH_LEGACY_BROADBAND', 527);
configDefineIfMissing('SCID_PARENTAL_CONTROL', 560);

configDefineIfMissing('SCID_TV_YOUVIEW_PLUS', 1835);
configDefineIfMissing('SCID_TV_YOUVIEW', 1836);
configDefineIfMissing('SCID_BT_SPORT_APP', 2244);
configDefineIfMissing('SCID_BT_SPORT_STAFF_BTSPORT_APP', 2245);

/**
* Component
*
* Abstract component base class
*
* @access     public
* <AUTHOR> <<EMAIL>>
*/
class CComponent extends CObject
{
    const STATUS_UNCONFIGURED = 'unconfigured';

    const STATUS_QUEUED_ACTIVATE    = 'queued-activate';
    const STATUS_QUEUED_REACTIVATE  = 'queued-reactivate';
    const STATUS_QUEUED_DEACTIVATE  = 'queued-deactivate';
    const STATUS_QUEUED_DECONFIGURE = 'queued-deconfigure';
    const STATUS_QUEUED_DESTROY     = 'queued-destroy';

    const STATUS_ACTIVE    = 'active';
    const STATUS_DEACTIVE  = 'deactive';
    const STATUS_DESTROYED = 'destroyed';
    const STATUS_INVALID   = 'invalid';

    /**
    * Component ID (userdata.components.component_id)
    *
    * @var integer
    * @access private
    */
    var $m_intComponentID = 0;

    /**
    * Service ID (userdata.services.service_id)
    *
    * @var integer
    * @access private
    */
    var $m_intServiceID = 0;

    /**
    * Component Name (products.service_components.name)
    *
    * @var string
    * @access private
    */
    var $m_strComponentName = '';

    /**
    * Service Component ID (products.service_components.service_component_id)
    *
    * @var integer
    * @access private
    */
    var $m_intServiceComponentID = 0;

    /**
    * Component Status (userdata.components.status)
    *
    * @var string
    * @access private
    */
    var $m_strStatus = '';

    /**
    * Component Config ID (userdata.components.config_id)
    *
    * @var integer
    * @access private
    */
    var $m_intConfigID = 0;

    /**
    * Component Event Logging Object
    *
    * @var CComponentEventLog
    * @access private
    */
    var $m_objEventLogger = null;

    private static $selfInstance;

    /**
     * setter used to mock the class for testing purposes
     *
     * @param integer $instance Instance object to use.
     * @return void
     */
    public static function setInstance($instance)
    {
        self::$selfInstance = $instance;
    }

    /**
     * Getter used to mock class for testing purposes
     * @return CProductHelper CProductHelper instance
     */
    public static function getInstance()
    {
        if (self::$selfInstance == null) {
            self::$selfInstance = new CComponent();
        }
        return self::$selfInstance;
    }



    //
    // Constructor
    //

    /**
     * Contructor for the CPlusTalkComponent class
     * Load a component instance
     *
     * @param int $intComponentID The component ID
     *
     * @return boolean success
     *
     * @protected
     */
    function CComponent($intComponentID)
    {
        // Check Component exists and is of the correct type
        $dbhConn = get_named_connection_with_db('product');

        $strQuery="SELECT c.component_id as intComponentID,
                          c.service_id as intServiceID,
                          c.component_type_id as intServiceComponentID,
                          sc.name as strComponentName,
                          c.config_id as intConfigID,
                          c.status as strStatus
                     FROM userdata.components c
               INNER JOIN products.service_components sc
                       ON sc.service_component_id = c.component_type_id
                 WHERE c.component_id = '$intComponentID'";

        $refResult = PrimitivesQueryOrExit(
            $strQuery,
            $dbhConn,
            'Fetch Component Info for CComponent object construction'
        );
        $arrComponentDetails = PrimitivesResultGet($refResult);

        if ((!$arrComponentDetails) || count($arrComponentDetails) < 1) {
            return false;
        }

        if ((!isset($arrComponentDetails['intComponentID']) ) || $arrComponentDetails['intComponentID'] < 1) {
            return false;
        }

        $this->m_intComponentID = intval($arrComponentDetails['intComponentID']);

        if ((!isset($arrComponentDetails['intServiceID']) ) || $arrComponentDetails['intServiceID'] < 1) {
            return false;
        }

        $arrComponentDetails['intServiceID'] = intval($arrComponentDetails['intServiceID']);
        $this->m_intServiceID = $arrComponentDetails['intServiceID'];
        $this->m_strComponentName = $arrComponentDetails['strComponentName'];
        $this->m_intServiceComponentID = $arrComponentDetails['intServiceComponentID'];
        $this->m_strStatus = $arrComponentDetails['strStatus'];
        $this->m_intConfigID = $arrComponentDetails['intConfigID'];

        return true;
    } // function CComponent($intComponentID)



    //
    // Accessor Methods
    //

    /**
    * Returns the component ID
    *
    * @access public
    * @return integer component ID
    */
    function getComponentID()
    {
        if ($this->m_intComponentID < 1) {
            return false;
        }

        return $this->m_intComponentID;
    } // function getComponentID()

    /**
    * Returns the service ID
    *
    * @access public
    * @return integer service ID
    */
    function getServiceID()
    {
        if ($this->m_intServiceID < 1) {
            return false;
        }

        return $this->m_intServiceID;
    } // function getServiceID()

    /**
    * Returns the component type (ServiceComponentID)
    *
    * @access public
    * @return int component ID
    */
    function getComponentTypeID()
    {
        if ($this->m_intServiceComponentID < 1) {
            return false;
        }

        return $this->m_intServiceComponentID;
    } // function getComponentTypeID()

    /**
    * Returns the Service Component ID (ServiceComponentID) (compatibility)
    *
    * @access public
    * @return int component ID
    */
    function getServiceComponentID()
    {
        return $this->getComponentTypeID();
    } // function getServiceComponenID()

    /**
    * Returns the component status eg active, destroyed
    *
    * @access public
    * @return string Status string
    */
    function getStatus()
    {
        return $this->m_strStatus;
    }

    /**
    * Returns the component's config ID
    *
    * @access public
    * @return integer The component's Config ID
    */
    function getConfigID()
    {
        if ($this->m_intConfigID < 1) {
            return false;
        }

        return $this->m_intConfigID;
    } // function getConfigID()

    /**
    * Returns the component's name
    *
    * @access public
    * @return string the component's name
    */
    function getComponentName()
    {
        return $this->m_strComponentName;
    }



    //
    // Factory Methods
    //

    /**
     * Create a component instance
     *
     * Factory method, returns correct subclass based on the type of component
     *
     * @param  integer $intComponentID Component ID
     *
     * @return CComponent component subclass instance
     *
     * <AUTHOR>
     */
    public static function createInstance($intComponentID)
    {
        $objComponent = false; // Return by reference

        if ($intComponentID > 0) {
            //Get component type
            $intServiceComponentID = CComponent::getComponentType($intComponentID);

            //Create correct subclass of component object
            switch ($intServiceComponentID) {
                case SCID_PLUSTALK_RES_PAYG:
                case SCID_PLUSTALK_BUS_PAYG:
                case SCID_PLUSTALK_RES_EW:
                case SCID_PLUSTALK_RES_ANYTIME:
                case SCID_PLUSTALK_BUS_EW:
                case SCID_PLUSTALK_BUS_ANYTIME:
                case SCID_PLUSTALK_STAFF:
                case SCID_PLUSTALK_120:
                case SCID_PLUSTALK_240:
                case SCID_PLUSTALK_360:
                case SCID_PLUSTALK_480:
                    include_once('/local/data/mis/database/database_libraries/components/CPlusTalkProduct.inc');
                    $objComponent = new CPlusTalkProduct($intComponentID);
                    break;

                case SCID_METRONET_EMAIL:
                    include_once('/local/data/mis/database/database_libraries/components/CMetronetEmailProduct.inc');
                    $objComponent = new CMetronetEmailProduct($intComponentID);
                    break;

                case SCID_WLR_ANYTIME:
                case SCID_WLR_ANYTIME_PLUS:
                case SCID_WLR_EVENING_WEEKEND:
                case SCID_WLR_TALK:
                case SCID_WLR_TALK_ANYTIME:
                case SCID_WLR_ESSENTIAL:
                case SCID_GREENBEE_PHONE:
                case SCID_GREENBEE_BUNDLED_PHONE:
                case SCID_WLR_PN_TALK_ANYTIME:
                case SCID_WLR_PN_TALK_EW:
                case COMPONENT_WLR_PN_BUSINESS_PHONE_PAYG:
                case COMPONENT_WLR_PN_BUSINESS_PHONE_DAYTIME:
                case COMPONENT_WLR_PN_RES_TALK_ANYTIME_2010:
                case COMPONENT_WLR_PN_RES_MOBILE_ANYTIME_TRIAL1:
                case COMPONENT_WLR_PN_RES_MOBILE_ANYTIME_TRIAL2:
                case COMPONENT_WLR_JOHNLEWIS_RES_ANYTIME:
                case COMPONENT_WLR_JOHNLEWIS_RES_EW:
                case COMPONENT_WLR_JOHNLEWIS_RES_ANYTIME_INTL:
                case COMPONENT_WLR_JOHNLEWIS_RES_ANYTIME_INTL_W_MOBILE:
                case COMPONENT_WLR_JOHNLEWIS_RES_ANYTIME_W_MOBILE:
                case COMPONENT_WLR_PN_RES_ANYTIME_INTERNATIONAL_2013:
                case COMPONENT_WLR_PN_RES_ANYTIME_INTERNATIONAL_WITH_MOBILE_2013:
                case COMPONENT_WLR_PN_RES_ANYTIME_2013:
                case COMPONENT_WLR_PN_RES_ANYTIME_WITH_MOBILE_2013:
                case COMPONENT_WLR_PN_RES_EVENINGS_AND_WEEKENDS_FREE_2013:
                case COMPONENT_WLR_PN_RES_EVENINGS_AND_WEEKENDS_FREE_WITH_MOBILE_2013:
                case COMPONENT_WLR_PN_RES_EVENINGS_AND_WEEKENDS_2013:
                case COMPONENT_WLR_PN_RES_EVENINGS_AND_WEEKENDS_WITH_MOBILE_2013:
                case COMPONENT_WLR_PN_RES_TALK_MOBILE_2013:
                case COMPONENT_WLR_PN_RES_NO_CALLS_2013:
                case COMPONENT_WLR_PN_RES_NO_CALLS_WITH_MOBILE_2013:
                case COMPONENT_WLR_PN_RES_WEEKENDS_2013:
                case COMPONENT_WLR_PN_RES_WEEKENDS_WITH_MOBILE_2013:
                case COMPONENT_WLR_BUS_ANY_NO:
                case COMPONENT_WLR_BUS_500_NO:
                case COMPONENT_WLR_BUS_1000_NO:
                case COMPONENT_WLR_BUS_PAYG_NO:
                case COMPONENT_WLR_BUS_ANY_12:
                case COMPONENT_WLR_BUS_500_12:
                case COMPONENT_WLR_BUS_1000_12:
                case COMPONENT_WLR_BUS_PAYG_12:
                case COMPONENT_WLR_BUS_ANY_24:
                case COMPONENT_WLR_BUS_500_24:
                case COMPONENT_WLR_BUS_1000_24:
                case COMPONENT_WLR_BUS_PAYG_24:
                case COMPONENT_WLR_PN_RES_ANYTIME_INTERNATIONAL_2014:
                case COMPONENT_WLR_PN_RES_ANYTIME_INTERNATIONAL_WITH_MOBILE_2014:
                case COMPONENT_WLR_PN_RES_ANYTIME_2014:
                case COMPONENT_WLR_PN_RES_ANYTIME_WITH_MOBILE_2014:
                case COMPONENT_WLR_PN_RES_EVENINGS_AND_WEEKENDS_FREE_2014:
                case COMPONENT_WLR_PN_RES_EVENINGS_AND_WEEKENDS_FREE_WITH_MOBILE_2014:
                case COMPONENT_WLR_PN_RES_EVENINGS_AND_WEEKENDS_2014:
                case COMPONENT_WLR_PN_RES_EVENINGS_AND_WEEKENDS_WITH_MOBILE_2014:
                case COMPONENT_WLR_PN_RES_TALK_MOBILE_2014:
                case COMPONENT_WLR_PN_RES_WEEKENDS_2014:
                case COMPONENT_WLR_PN_RES_WEEKENDS_WITH_MOBILE_2014:
                case COMPONENT_WLR_PN_RES_LINE_ONLY_2014:
                case COMPONENT_WLR_PN_RES_LINE_ONLY_WITH_MOBILE_2014:
                case COMPONENT_WLR_PN_RES_EVENING_AND_WEEKEND_UK_AND_MOBILE_CALLS:
                case COMPONENT_WLR_PN_RES_UNLIMITED_UK_AND_MOBILE_CALLS:
                case COMPONENT_WLR_PN_BUS_UNLIMITED_UK_CALLS:
                case COMPONENT_WLR_PN_BUS_UNLIMITED_UK_AND_INTERENATIONAL_500_CALLS:
                case COMPONENT_WLR_PN_BUS_PAYG:
                    include_once('/local/data/mis/database/database_libraries/components/CWlrProduct.inc');
                    $objComponent = new CWlrProduct($intComponentID);
                    break;

                case SCID_ADD_ON_PRODUCT_BUNDLE:
                    include_once('/local/data/mis/database/database_libraries/components/CAddOnProductBundle.inc');
                    $objComponent = new CAddOnProductBundle($intComponentID);
                    break;

                case SCID_SECURITY_BULL_GUARD:
                case SCID_SECURITY_BULL_GUARD_TRIAL:
                    include_once('/local/data/mis/database/application_apis/SecurityProducts/SecurityProduct.class.php');
                    $objComponent = new CSecurityProduct($intComponentID);
                    break;

                case SCID_MAAF_WEBMAIL:
                    include_once('/local/data/mis/database/database_libraries/components/MAAF/Webmail.class.php');
                    $objComponent = MAAF_CProduct_Webmail::getCProduct($intComponentID);
                    break;

                case SCID_PARENTAL_CONTROL:
                    require_once '/local/data/mis/database/application_apis/SecurityProducts/ParentalControlProduct.class.php';
                    $objComponent = new ParentalControlProduct($intComponentID);
                    break;

                case SCID_ENHANCED_CARE:
                    require_once '/local/data/mis/database/database_libraries/components/CProduct.inc';
                    $objComponent = new CProduct($intComponentID);
                    break;
                case SCID__BROADBANDSCOPE_8_STATIC_IP:
                    require_once '/local/data/mis/database/database_libraries/components/CProduct.inc';
                    $objComponent = new CProduct($intComponentID);
                    break;

                case SCID_TV_YOUVIEW_PLUS:
                case SCID_TV_YOUVIEW:
                    require_once '/local/data/mis/database/database_libraries/components/CTvComponent.inc';
                    $objComponent = new CTvComponent($intComponentID);

                    break;

                case SCID_BT_SPORT_APP:
                case SCID_BT_SPORT_STAFF_BTSPORT_APP:
                    require_once '/local/data/mis/database/database_libraries/components/CProduct.inc';
                    $objComponent = new CProduct($intComponentID);
                    break;

                default:
                    require_once '/local/data/mis/database/database_libraries/components/CInternetConnectionProduct.inc';

                    if (true === CInternetConnectionProduct::isInternetConnectionProduct($intComponentID)) {
                        $objComponent = new CInternetConnectionProduct($intComponentID);

                        return $objComponent;
                    } // end if

                    // Just instantiate the object as a stand-alone component
                    // This generic component implements basic state change methods
                    require_once '/local/data/mis/database/database_libraries/components/CGenericComponent.php';
                    $objComponent = new CGenericComponent($intComponentID);
                    break;
            } // switch($intServiceComponentID)
        } // if ($intComponentID > 0)

        return $objComponent;
    } // function &createInstance($intComponentID)

    /**
    * Returns the Component Object identified by service ID and Service Compoonent ID
    *
    * <AUTHOR> Jones" <<EMAIL>>
    * @access   public
    * @static
    * @param    integer $intServiceId          Service ID
    * @param    integer $intServiceComponentId Service Component ID
    * @return   object  CComponent Object Type
    */
    function &getComponentByServiceComponentId($intServiceId, $intServiceComponentId)
    {
        $objComponent = false; // Return by reference

        $dbhRead = get_named_connection_with_db('userdata_reporting');

        $strGetComponent = "SELECT com.component_id AS intComponentId \n" .
                           "  FROM components com \n" .
                           "  WHERE com.service_id = $intServiceId \n" .
                           "    AND com.component_type_id = $intServiceComponentId \n" .
                           "    AND com.status NOT IN ('queued-destroy', 'destroyed') \n";

        if (false !== ($resGetComponent = PrimitivesQueryOrExit(
            $strGetComponent,
            $dbhRead,
            'CComponent::getComponentIdFromService',
            true
        ))) {
            if (PrimitivesNumRowsGet($resGetComponent) > 0) {
                $intComponentId = PrimitivesResultGet($resGetComponent, 'intComponentId');

                $objComponent = CComponent::createInstance($intComponentId);
            }
        } // if (false !== ($resGetComponent = PrimitivesQueryOrExit()))

        return $objComponent;
    } // function getComponentIdFromService($intServiceId, $intServiceComponentId)



    //
    // Static Methods
    //

    /**
    * Create a new product in an unconfigured state
    *
    *
    *
    * @access public
    * @static
    * <AUTHOR>
    * @param int $intServiceID          Service  ID to add the product to
    * @param int $intServiceComponentID Service Component ID - the product chosen at signup
    * @param int $intTariffID           Tariff ID - the payment option chosen at signup
    * @param int $uxtNextInvoiceDate    Here to maintain compatibility with CProduct::create
    * @param int $strProductHandle      Here to maintain compatibility with CProduct::create
    * @param int $uxtStartTime          Here to maintain compatibility with CProduct::create
    *
    * @return object component subclass instance
    */
    public static function create(
        $intServiceID,
        $intServiceComponentID,
        $intTariffID = 0,
        $uxtNextInvoiceDate = false,
        $strProductHandle = '',
        $uxtStartTime = ''
    ) {
        $objNewComponent = false; // Return by reference

        if ($intServiceID <= 0) {
            return $objNewComponent;
        }

        if ($intServiceComponentID <= 0) {
            return $objNewComponent;
        }

        if ($intTariffID <= 0) {
            return $objNewComponent;
        }

        //Create correct subclass of component object
        switch ($intServiceComponentID) {
            case SCID_PLUSTALK_RES_PAYG:
            case SCID_PLUSTALK_BUS_PAYG:
            case SCID_PLUSTALK_RES_EW:
            case SCID_PLUSTALK_RES_ANYTIME:
            case SCID_PLUSTALK_BUS_EW:
            case SCID_PLUSTALK_BUS_ANYTIME:
            case SCID_PLUSTALK_STAFF:
            case SCID_PLUSTALK_120:
            case SCID_PLUSTALK_240:
            case SCID_PLUSTALK_360:
            case SCID_PLUSTALK_480:
                include_once('/local/data/mis/database/database_libraries/components/CPlusTalkProduct.inc');
                break;

            case SCID_METRONET_EMAIL:
                include_once('/local/data/mis/database/database_libraries/components/CMetronetEmailProduct.inc');
                break;

            case SCID_WLR_ANYTIME:
            case SCID_WLR_ANYTIME_PLUS:
            case SCID_WLR_EVENING_WEEKEND:
            case SCID_WLR_TALK:
            case SCID_WLR_TALK_ANYTIME:
                include_once('/local/data/mis/database/database_libraries/components/CWlrProduct.inc');
                break;

            case SCID_ADD_ON_PRODUCT_BUNDLE:
                include_once('/local/data/mis/database/database_libraries/components/CAddOnProductBundle.inc');
                break;

            // Standalone Components
            case SCID_SOFTWARE_SELECTOR:
                $objNewComponent = CComponent::doCreate($intServiceID, $intServiceComponentID);
                return $objNewComponent;
                break;

            default:
                if (true === CInternetConnectionProduct::isInternetConnectionProduct($intServiceComponentID)) {
                    include_once '/local/data/mis/database/database_libraries/components/CInternetConnectionProduct.inc';

                    break;
                } // end if

                return $objNewComponent;
                break;
        } // switch($intServiceComponentID)

        $objNewComponent = CProduct::create($intServiceID, $intServiceComponentID, $intTariffID);

        return $objNewComponent;
    } // function &create($intServiceID, $intServiceComponentID, $intTariffID)

    /**
     * Creates a single component on a service. Used for stand-alone components
     *
     * <AUTHOR> Jones" <<EMAIL>>
     * @param integer $intServiceID          Service Id
     * @param integer $intServiceComponentID Service Component Id
     * @access public
     * @return CComponent
     */
    function doCreate($intServiceID, $intServiceComponentID)
    {
        if (!is_numeric($intServiceID) || $intServiceID <= 0) {
            return false;
        }

        if (!is_numeric($intServiceComponentID) || $intServiceComponentID <= 0) {
            return false;
        }

        // Add the Service Component Product to the service account
        $intComponentID = userdata_component_add($intServiceID, $intServiceComponentID, '-1', '', 'unconfigured');

        if (false === $intComponentID) {
            // Failed to create the component
            return false;
        }

        $objComponent = CComponent::createInstance($intComponentID);

        return $objComponent;
    } // function doCreate($intServiceId, $intServiceComponentId)

    /**
    * returns the service component id of a component
    *
    * @access public
    * @static
    * <AUTHOR>
    * @param  integer $intComponentID Component ID
    * @return integer Service Component ID
    */
    public static function getComponentType($intComponentID)
    {
        if ($intComponentID <= 0) {
            return false;
        }

        // Check Component exists and is of the correct type
        $dbhConn = get_named_connection_with_db('product');

        $strQuery="SELECT c.component_type_id as intServiceComponentID
                     FROM userdata.components c
               INNER JOIN products.service_components sc
                       ON sc.service_component_id = c.component_type_id
                    WHERE c.component_id = '$intComponentID'";

        $refResult = PrimitivesQueryOrExit(
            $strQuery,
            $dbhConn,
            'Fetch Component Info for CComponent object construction'
        );
        $arrComponentDetails = PrimitivesResultGet($refResult);

        if ((!$arrComponentDetails) || count($arrComponentDetails) < 1) {
            return false;
        }

        return intval($arrComponentDetails['intServiceComponentID']);
    } // function getComponentType($intComponentID)



    //
    // Configurator Methods  - Override for your specific component
    //

    /**
    * Refreshes the current state of the component
    *
    * If the component is in a queued state
    *
    * @access public
    * @return boolean False if the component is destroyed, otherwise true
    */
    function refreshCurrentState()
    {
        //You MUST override this function
        return false;
    }

    /**
    * Component configurator function. Enables the component.
    *
    *
    * @access private
    * <AUTHOR>
    * @return boolean False if the component is destroyed, otherwise true
    */
    function enable()
    {
        //You MUST override this function
        return false;
    }

    /**
    * Component configurator function. Checks if the compoent may be enabled.
    *
    *
    * @access private
    * <AUTHOR>
    * @return boolean False if the component is destroyed, otherwise true
    */
    function canBeEnabled()
    {

        if (true === in_array($this->GetStatus(), array('destroyed','queued-destroy','invalid'))) {
            return false;
        } else {
            return true;
        }
    }


    /**
    * Component configurator function. Disables the component.
    *
    *
    * @access private
    * <AUTHOR>
    * @return boolean False if the component is destroyed, otherwise true
    */
    function disable()
    {
        //You MUST override this function
        return false;
    }

    /**
    * Component configurator function. Checks if the component can be disabled.
    *
    *
    * @access private
    * <AUTHOR>
    * @return boolean False if the component is destroyed, otherwise true
    */
    function canBeDisabled()
    {

        if ((in_array($this->GetStatus(), array('destroyed','queued-destroy','invalid')) === true)) {
            return false;
        } else {
            return true;
        }
    }


    /**
    * Component configurator function. Destroys the component.
    *
    *
    * @access private
    * <AUTHOR>
    * @return boolean False if the component is destroyed, otherwise true
    */
    function destroy()
    {
        //You MUST override this function
        return false;
    }

    /**
     * Sets the status of the component
     *
     * @param  string $strNewStatus Status handle to change to
     *
     * @return boolean
     *
     * <AUTHOR> Jones" <<EMAIL>>
     */
    public function setStatus($strNewStatus)
    {
        $bolResult = $this->prvSetStatus($strNewStatus);

        return $bolResult;
    }



    //
    // Protected Methods
    //

    /**
    * Sets the component status and updates userdata.components
    *
    * <AUTHOR>
    * @private
    * @param string $strNewStatus New status name eg: active
    * @return   boolean True if the state was changed successfully
    */
    function prvSetStatus($strNewStatus)
    {
        switch ($strNewStatus) {
            case 'unconfigured':
            case 'active':
            case 'queued-activate':
            case 'queued-reactivate':
            case 'deactive':
            case 'queued-deactivate':
            case 'queued-deconfigure':
            case 'queued-destroy':
            case 'destroyed':
                break;

            default:
                return false;
        }

        $dbhConn = get_named_connection_with_db('userdata');

        $strQuery = "UPDATE components \n" .
                    "  SET status  = '$strNewStatus' \n" .
                    "  WHERE component_id = '{$this->m_intComponentID}' ";

        PrimitivesQueryOrExit($strQuery, $dbhConn, 'Update the component status');

        $this->m_strStatus = $strNewStatus;

        return true;
    } // function prvSetStatus($strNewStatus)

    /**
     * Sets the component status to destroy for the unconfigurable and updates userdata.components
     *
     * @public
     * @param string $intNewComponentId New status name eg: unconfigured
     * @return   boolean True if the state was changed successfully
     */
    public function setStatusToDestroy($intNewComponentId)
    {
        $dbhConn = get_named_connection_with_db('userdata');

        $strQuery = "UPDATE components \n" .
            "  SET status  = 'destroyed' \n" .
            "  WHERE component_id = '$intNewComponentId' ";

        PrimitivesQueryOrExit($strQuery, $dbhConn, 'Update the component status');

        return true;
    }

    /**
    * Returns the Event logger object, creating it the first time it is used
    *
    * @return CComponentEvent
    *
    * <AUTHOR>
     *
     * @todo Old @access tag is protected (and method name suggests private), but it is called from outside this stack
    */
    public function prvGetEventLogger()
    {
        if ($this->m_objEventLogger == null) {
            $this->m_objEventLogger = new CComponentEvent($this->m_intComponentID);
        }

        return $this->m_objEventLogger;
    } // function prvGetEventLogger()
} // class CComponent extends CObject
