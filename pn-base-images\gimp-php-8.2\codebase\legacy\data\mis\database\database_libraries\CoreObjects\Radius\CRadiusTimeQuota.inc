<?php
	/**
	* This file declares the CRadiusTimeQuota class 
	*
	* @package    Core
	* @subpackage Radius
	* <AUTHOR>
	* @version    $Id: CRadiusTimeQuota.inc,v 1.3 2005-06-02 09:05:34 gfraser Exp $
	* @filesource
	*/

	/**
	* Requires
	*/
	
	require_once('/local/data/mis/database/database_libraries/CDAO.inc');

	/**
	* This class declares the RADIUS TimeQuota class.
	* Extends functionality in CDAO (Data Access Object)
	*
	* @package  Core
	* @access   public
	*/

	class CRadiusTimeQuota extends CDAO
	{
		//
		// Private member variables
		//
		
		
		/**
		* An array of variables that are valid for this class
		*
		* @access private
		* @var    array
		*/
		var $m_arrValidVariables = array('intTimeID',
		                                 'intRadiusID',
		                                 'intGroupID',
		                                 'intPriority',
		                                 'intValidPeriod',
		                                 'intTimeAllowed',
		                                 'uxtStartTime',
		                                 'uxtStopTime',
		                                 'intTimeUsed',
		                                 'bolActive',
		                                 'uxtDateLastUpdated',
		                                 'intSyncVersion',
		                                 'intVersion');

		
		/**
		*
		* @var integer
		* @access private
		*/
		var $m_intTimeID = 0;
		
		
		/**
		*
		* @var integer
		* @access private
		*/
		var $m_intRadiusID = 0;

		
		/**
		*
		* @var integer
		* @access private
		*/
		var $m_intGroupID = 0;

		
		/**
		*
		* @var integer
		* @access private
		*/
		var $m_intPriority = 0;	

		
		/**
		*
		* @var integer
		* @access private
		*/
		var $m_intValidPeriod = '';	

		
		/**
		*
		* @var integer
		* @access private
		*/
		var $m_intTimeAllowed = '';	

		
		/**
		*
		* @var integer
		* @access private
		*/
		var $m_uxtStartTime = 0;	

		
		/**
		*
		* @var integer
		* @access private
		*/
		var $m_uxtStopTime = 0;	

		
		/**
		*
		* @var integer
		* @access private
		*/
		var $m_intTimeUsed = 0;	

		
		/**
		*
		* @var boolean
		* @access private
		*/
		var $m_bolActive = true;	

		
		/**
		*
		* @var integer
		* @access private
		*/
		var $m_uxtDateLastUpdated = 0;	

		
		/**
		*
		* @var integer
		* @access private
		*/
		var $m_intSyncVersion = 0;	

		
		/**
		*
		* @var integer
		* @access private
		*/
		var $m_intVersion = 0 ;	
	


	
		
		//
		// STATIC Methods
		//

		/**
		* Create the new object
		*
		* Creates a new instance of a CRadiusTimeQuota object
		*
		* <code>$objRadiusTimeQuota = &CRadiusTimeQuota::Create(2);</code>
		*
		* @access public
		* @static
		* @param the radius id of the user if you wish to instantiate an existing user. Defaults to 0 (new user).
		* @returns CRadiusTimeQuota instance
		*/ 
		public static function Create($intID=0)
		{
			$objNewObject = new CRadiusTimeQuota();

			if ($intID > 0) {
				$objNewObject->SetID($intID);
				$objNewObject->Refresh();
			}

			return $objNewObject;

		}

		//
		// Public Methods
		//

		/*
		* Refresh the object's data from the database
		*
		* This method discards the object's current data (if any) and
		* re-retrieves the information from the database.
		* For example:
		* <code>
		* // Want to forget the changes made to this object
		* $objObject->Refresh();
		* </code>
		*
		* @access public
		* @void
		*
		*/

		function Refresh ()
		{
			if ( $this->m_intID == 0 )
			{
				return false;
			}

			$dbConnID = get_named_connection_with_db('radius');
		
										 
			//Construct Query
			$strQuery = "SELECT time_id as intTimeID,
			                    radius_id as intRadiusID,
			                    group_id as intGroupID,
			                    priority as intPriority,
			                    valid_period as intValidPeriod,
			                    time_allowed as intTimeAllowed,
			                    UNIX_TIMESTAMP(start_time) as uxtStartTime,
			                    UNIX_TIMESTAMP(stop_time) as uxtStopTime,
			                    time_used as intTimeUsed,
			                    IF(active = 'Y', 'true', 'false') as bolActive,
			                    UNIX_TIMESTAMP(date_last_updated) as uxtDateLastUpdated,
			                    sync_version as intSyncVersion,
			                    version as intSyncVersion
			               FROM time_quota
			              WHERE time_id = '{$this->m_intID}'";
						  
			$resResult = PrimitivesQueryOrExit($strQuery, $dbConnID);

			$arrResult = PrimitivesResultsAsArrayGet($resResult);
			
			if(count($arrResult) == 0)
			{
				return false;			
			}

			//Set all of the Member Variables
			$this->SetAll($arrResult[0]);
			return true;
		}


		/**
		* Save the object's data to the database
		*
		* Saves the current state of the object to the database
		*
		* @access public
		*/

		function Save()
		{
			if($this->m_intID == 0)
			{
				return $this->prvSaveInsert();
			}
			else
			{
				return $this->prvSaveUpdate();
			}
		}


		//
		// Private Functions
		//

		/**
		* Insert a new time_quota
		*
		* Insert a new time_quota
		*
		* @access private
		* @return new ID or false
		*/
		function prvSaveInsert()
		{

			$dbConnID = get_named_connection_with_db('radius');

			if($this->m_intRadiusID == 0 || $this->m_intGroupID == 0)
			{
				return false;
			}

			//Construct Query

			$strStartTime = ($this->m_uxtStartTime == 0 || strtolower($this->m_uxtStartTime) == 'null') ? "NULL" : "FROM_UNIXTIME('{$this->m_uxtStartTime}')";
			$strStopTime  = ($this->m_uxtStopTime == 0  || strtolower($this->m_uxtStopTime)  == 'null') ? "NULL" : "FROM_UNIXTIME('{$this->m_uxtStopTime}')";
			$strDateLastUpdated = ($this->m_uxtDateLastUpdated == 0 || strtolower($this->m_uxtDateLastUpdated) == 'null') ? "NULL" : "FROM_UNIXTIME({$this->m_uxtDateLastUpdated})";
			$strActive = ($this->m_bolActive) ? "'Y'" : "'N'";
		
			$strQuery = "INSERT INTO time_quota
			                        (radius_id,
			                         group_id,
			                         priority,
			                         valid_period,
			                         time_allowed,
			                         start_time,
			                         stop_time,
			                         time_used,
			                         active,
			                         date_last_updated,
			                         sync_version,
			                         version)
			                 VALUES ('{$this->m_intRadiusID}',
			                         '{$this->m_intGroupID}',
			                         '{$this->m_intPriority}',
			                         '{$this->m_intValidPeriod}',
			                         '{$this->m_intTimeAllowed}',
			                          $strStartTime,
			                          $strStopTime,
			                         '{$this->m_intTimeUsed}',
			                          $strActive,
			                          $strDateLastUpdated,
			                         '{$this->m_intSyncVersion}',
			                         '{$this->m_intVersion}')";
			$resResult = PrimitivesQueryOrExit($strQuery, $dbConnID, 'Add new RADIUS time_quota');
			$intTimeQuotaID = PrimitivesInsertIdGet($dbConnID);
			
			if($intTimeQuotaID > 0)
			{
				$this->SetID($intTimeQuotaID);
				$this->m_intTimeID = $intTimeQuotaID;
				return true;
			}
			else
			{
				return false;
			}
		}	


		/**
		* Update an existing time_quota
		*
		* Update changes made to this object to time_quota
		* NOTE:  Incomplete. Will only update some fields.
		* If you need other fields updating, add them here, but check first
		* that it is legal to update them!!
		*
		* @access private
		* @return true or false
		*/
		function prvSaveUpdate()
		{
			$strDateLastUpdated = ($this->m_uxtDateLastUpdated == 0) ? "NULL" : "FROM_UNIXTIME('{$this->m_uxtDateLastUpdated}')";
			$strStartTime = ($this->m_uxtStartTime == 0) ? "NULL" : "FROM_UNIXTIME('{$this->m_uxtStartTime}')";
			$strStopTime  = ($this->m_uxtStopTime == 0) ? "NULL" : "FROM_UNIXTIME('{$this->m_uxtStopTime}')";
		
			$dbConnID = get_named_connection_with_db('radius');

			// This query is incomplete.  It only contains some of the updatable fields.
			// If you need to update a field which is not here, check that is it allowable then add the SET entry here.

			// Construct Query
			$strActive = 'N';

			if($this->m_bolActive)
			{
				$strActive = 'Y';
			}

			$strQuery = "UPDATE time_quota
			                SET valid_period = '{$this->m_intValidPeriod}',
			                    time_allowed = '{$this->m_intTimeAllowed}',
			                    start_time   = $strStartTime,
			                    stop_time    = $strStopTime,
			               date_last_updated = $strDateLastUpdated,
			                          active = '{$strActive}'
			                  WHERE time_id  = '{$this->m_intID}'";

			$resResult = PrimitivesQueryOrExit($strQuery, $dbConnID, 'Update an exisiting RADIUS time_quota');

			//Data has changed so refresh
			return $this->Refresh();
		}
	}
?>
