<?php

/**
 * InvalidSoulstoneEntryException Class file
 *
 * @category  Exceptions
 * @package   LegacyCodebase
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2012 Plusnet
 */

/**
 * InvalidSoulstoneEntryException Class
 *
 * Exceptions thrown when the configurators fails to find relevant entries in Soulstone DB for a component/service
 *
 * @category  Exceptions
 * @package   LegacyCodebase
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2012 Plusnet
 */
class InvalidSoulstoneEntryException extends Exception
{

    /**
     * File name for the notification log
     *
     * @var string
     */
    private static $_notificationLogShareFolder = '/share/admin/log/BVWebMailDestruction';

    /**
     * File name for the notification log
     *
     * @var string
     */
    private static $_notificationLogFile = 'invalid_soulstone_entry.log';

    /**
     * Number of days to keep the log files for
     *
     * @var integer
     */
    private static $_daysToKeepNotificationLogFilesFor = 7;

    /**
     * Appends message to the notification log file
     *
     * @param string $message The message to append
     *
     * @return void
     */
    public static function appendToNotificationLog($message)
    {
        self::createLogFolderIfItDoesNotExist();

        $res = self::openNotificationLog('a');
        fwrite($res, $message . ",");
        fclose($res);
    }

    /**
     * Remove the notification files older than 7 days
     *
     * @return void
     */
    public static function performNotificationLogHousekeeping()
    {
        if (file_exists(self::$_notificationLogShareFolder)) {

            $pathToLogFiles = self::$_notificationLogShareFolder . '/*';
            $command = '/usr/bin/find ' . $pathToLogFiles . ' -mtime +'
                . self::$_daysToKeepNotificationLogFilesFor . ' -exec rm -f {} \;';
        }
    }

    /**
     * Get the notification log file location
     *
     * @return string the log file location
     */
    public static function getNotificationLogFile()
    {
        return self::$_notificationLogShareFolder . "/" . self::$_notificationLogFile . '_' . date('Y-m-d');
    }

    /**
     * Method to overwrite the log file location
     * Needed for unit tests
     *
     * @param string $location Log file location
     *
     * @return void
     */
    public static function setShareLocation($location)
    {
        self::$_notificationLogShareFolder = $location;
    }

    /**
     * Open the notification file
     *
     * @param string $mode Mode to open the file in
     *
     * @return file resource
     */
    private static function openNotificationLog($mode = 'r')
    {
        return fopen(InvalidSoulstoneEntryException::getNotificationLogFile(), $mode);
    }

    /**
     * Creates the notification log folder if it does not exist
     *
     * @return void
     */
    private static function createLogFolderIfItDoesNotExist()
    {
        if (!is_dir(self::$_notificationLogShareFolder)) {
            mkdir(self::$_notificationLogShareFolder, 0777, true);
        }
    }
}