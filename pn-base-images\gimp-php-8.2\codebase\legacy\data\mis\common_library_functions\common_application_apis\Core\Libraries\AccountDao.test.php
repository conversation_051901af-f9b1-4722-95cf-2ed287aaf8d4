<?php
	if (! defined('SIMPLE_TEST'))
	{
		define('SIMPLE_TEST', '/local/data/mis/scripts/SimpleTests/simpletest/');
	}

	require_once(SIMPLE_TEST . 'unit_tester.php');
	require_once(SIMPLE_TEST . 'reporter.php');

	require_once ('/local/data/mis/database/standard_include.inc');
	require_once ('/local/www/database-admin/config/config.inc');

	class Test_Core_AccountDao extends UnitTestCase
	{
		function __construct()
		{
			$this->UnitTestCase();
		} // function __construct()

		function Test_get()
		{
			$intAccountId = 848598;
			$objAccountDao = Core_AccountDao::get($intAccountId);
			
			$this->assertIdentical(($objAccountDao instanceof Core_IAccountDao), true);
			$this->assertEqual($objAccountDao->getAccountId() == $intAccountId, true);
		} // function Test_get()

		function Test_write()
		{
			$objAccountDao = $this->addAccountToDb();
			$intAccountId = $objAccountDao->getAccountId();

			$this->assertIdentical(isset($intAccountId), true);
			
			// Clear after test
			$objAccountDao->delete();
			$objAccountDao->write();
			Db_Manager::commit();
		} // function Test_write()

		function Test_update()
		{
			$objAccountDao = $this->addAccountToDb();
			$intAccountId = $objAccountDao->getAccountId();

			$objAccountDaoToUpdate = Core_AccountDao::get($intAccountId);
			$floBalance = 10.01;
			$objAccountDaoToUpdate->setBalance($floBalance);

			$objAccountDaoToUpdate->write();
			Db_Manager::commit();

			$objAccountDaoToCheck = Core_AccountDao::get($intAccountId);
			$this->assertEqual($objAccountDaoToCheck->getBalance(), $floBalance);
			
			// Clear after test
			$objAccountDao->delete();
			$objAccountDao->write();
			Db_Manager::commit();
		} // function Test_update()

		function Test_reduceBalance()
		{
			// Add sample row to Db
			$objAccountDao = $this->addAccountToDb();

			$floCurrentBalance = $objAccountDao->getBalance();
			$floAmount = 2.00;
			
			$objAccountDao->reduceBalance($floAmount);
			
			$this->assertEqual($objAccountDao->getBalance(), $floCurrentBalance - $floAmount);

			// Clear after test
			$objAccountDao->delete();
			$objAccountDao->write();
			Db_Manager::commit();
		} // function Test_reduceBalance()

		/**
		 * @return Core_AccountDao
		 */
		function addAccountToDb()
		{
			$objAccountDao = new Core_AccountDao(Db_Manager::DEFAULT_TRANSACTION, true);
			$arrVars = array('customer_id'       => 848598,
			                 'address_id'        => 1,
		                     'credit_details_id' => 3,
		                     'card_type'         => 'VISA',
		                     'expiry_date'       => '2007-06-01',
		                     'check_digits'      => '',
		                     'balance'           => 0.00,
		                     'terms'             => '',
		                     'credit_limit'      => 2000);
			$objAccountDao->setVars($arrVars);
			$objAccountDao->write();

			Db_Manager::commit();
			
			return $objAccountDao;
		} // function addAccountToDb()
		
	} // class Test_Core_AccountDao extends UnitTestCase

?>