<?php
require_once 'BVDB.class.php';
require_once '/local/data/mis/database/database_libraries/userdata-access.inc';
require_once '/local/data/mis/database/crypt_config.inc';
require_once 'ConfigSoulstone.class.php';

class Brightview_Account
{
    // DB columns attrs
    protected $intMailId;
    protected $strUserName;
    protected $strRealm;
    protected $strFullName;
    protected $strPassword;
    protected $strStatus;
    protected $dteModifyDate;
    protected $arrOptions;
    protected $intMailQuota;
    protected $intWebQuota = 100;    // That's default
    protected $strFriacoCli1;
    protected $strFriacoCli2;
    protected $intFriacoLimitSoft;
    protected $intFriacoLimitHard;
    protected $intMultiChannel;
    protected $intCliChange;
    protected $arrFriacoType;
    protected $strAdslStatic;
    protected $intNewsConn;
    protected $intNewsRate;
    protected $intNewsLimit;
    protected $intParentId;
    protected $dteLastMailCollection;
    protected $dteLastFtpLogin;

    protected $objGiganewsAccount;

    // Instance attrs
    protected $arrErrors = array();

    public function setMailId($intMailId)
    {
        $this->intMailId = $intMailId;
    }

    public function setUserName($strUserName)
    {
        $this->strUserName = $strUserName;
    }

    public function setRealm($strRealm)
    {
        $this->strRealm = $strRealm;
    }

    public function setFullName($strFullName)
    {
        $this->strFullName = $strFullName;
    }

    public function setPassword($strPassword)
    {
        if ($strPassword == null || trim($strPassword) == '') return;
        $this->strPassword = $strPassword;
    }

    public function setStatus($strStatus)
    {
        $this->strStatus = $strStatus;
    }

    private function setModifyDate($dteModifyDate)
    {
        $this->dteModifyDate = $dteModifyDate;
    }

    public function setOptions($arrOptions)
    {
        $this->arrOptions = $arrOptions;
    }

    public function setMailQuota($intMailQuota)
    {
        $this->intMailQuota = $intMailQuota;
    }

    public function setWebQuota($intWebQuota)
    {
        if (true == is_numeric($intWebQuota)) {

            $this->intWebQuota = (int)$intWebQuota;
        }
    }

    public function setFriacoCli1($strFriacoCli1)
    {
        $this->strFriacoCli1 = $strFriacoCli1;
    }

    public function setFriacoCli2($strFriacoCli2)
    {
        $this->strFriacoCli2 = $strFriacoCli2;
    }

    public function setFriacoLimitSoft($intFriacoLimitSoft)
    {
        $this->intFriacoLimitSoft = $intFriacoLimitSoft;
    }

    public function setFriacoLimitHard($intFriacoLimitHard)
    {
        $this->intFriacoLimitHard = $intFriacoLimitHard;
    }

    public function setMultiChannel($intMultiChannel)
    {
        $this->intMultiChannel = $intMultiChannel;
    }

    public function setCliChange($intCliChange)
    {
        $this->intCliChange = $intCliChange;
    }

    public function setFriacoType($arrFriacoType)
    {
        $this->arrFriacoType = $arrFriacoType;
    }

    public function setAdslStatic($strAdslStatic)
    {
        $this->strAdslStatic = $strAdslStatic;
    }

    public function setNewsConn($intNewsConn)
    {
        $this->intNewsConn = $intNewsConn;
    }

    public function setNewsRate($intNewsRate)
    {
        $this->intNewsRate = $intNewsRate;
    }

    public function setNewsLimit($intNewsLimit)
    {
        $this->intNewsLimit = $intNewsLimit;
    }

    public function setParentId($intParentId)
    {
        $this->intParentId = $intParentId;
    }

    public function setLastMailCollection($dteLastMailCollection)
    {
        $this->dteLastMailCollection = $dteLastMailCollection;
    }

    public function setLastFtpLogin($dteLastFtpLogin)
    {
        $this->dteLastFtpLogin = $dteLastFtpLogin;
    }

    public function setGiganewsAccount($objGiganewsAccount)
    {
        $this->objGiganewsAccount = $objGiganewsAccount;
    }

    public function getMailId()
    {
        return $this->intMailId;
    }
    public function getUserName()
    {
        return $this->strUserName;
    }
    public function getRealm()
    {
        return $this->strRealm;
    }
    public function getFullName()
    {
        return $this->strFullName;
    }

    public function getPassword()
    {
        return $this->strPassword;
    }

    public function getStatus()
    {
        return $this->strStatus;
    }
    public function getModifyDate()
    {
        return $this->dteModifyDate;
    }

    public function getOptions()
    {
        return $this->arrOptions;
    }
    public function getMailQuota()
    {
        return $this->intMailQuota;
    }
    public function getWebQuota()
    {
        return $this->intWebQuota;
    }

    public function getFriacoCli1()
    {
        return $this->strFriacoCli1;
    }

    public function getFriacoCli2()
    {
        return $this->strFriacoCli2;
    }

    public function getFriacoLimitSoft()
    {
        return $this->intFriacoLimitSoft;
    }

    public function getFriacoLimitHard()
    {
        return $this->intFriacoLimitHard;
    }

    public function getMultiChannel()
    {
        return $this->intMultiChannel;
    }

    public function getCliChange()
    {
        return $this->intCliChange;
    }

    public function getFriacoType()
    {
        return $this->arrFriacoType;
    }

    public function getAdslStatic()
    {
        return $this->strAdslStatic;
    }

    public function getNewsConn()
    {
        return $this->intNewsConn;
    }

    public function getNewsRate()
    {
        return $this->intNewsRate;
    }

    public function getNewsLimit()
    {
        return $this->intNewsLimit;
    }

    public function getParentId()
    {
        return $this->intParentId;
    }

    public function getLastMailCollection()
    {
        return $this->dteLastMailCollection;
    }

    public function getLastFtpLogin()
    {
        return $this->dteLastFtpLogin;
    }

    public function getGiganewsAccount()
    {
        return $this->objGiganewsAccount;
    }

    public function __construct($arrVars = null)
    {
        if (is_array($arrVars)) {
            $this->setMailId($arrVars['mailid']);
            $this->setUserName($arrVars['username']);
            $this->setRealm($arrVars['realm']);
            $this->setFullName($arrVars['fullname']);
            $this->setPassword($arrVars['passwd']);
            $this->setStatus($arrVars['status']);
            $this->setModifyDate($arrVars['modifydate']);
            $this->setOptions($arrVars['options']);
            $this->setMailQuota($arrVars['mail_quota']);
            $this->setWebQuota($arrVars['web_quota']);
            $this->setFriacoCli1($arrVars['friaco_cli1']);
            $this->setFriacoCli2($arrVars['friaco_cli2']);
            $this->setFriacoLimitSoft($arrVars['friaco_limit_soft']);
            $this->setFriacoLimitHard($arrVars['friaco_limit_hard']);
            $this->setMultiChannel($arrVars['multi_channel']);
            $this->setCliChange($arrVars['cli_change']);
            $this->setFriacoType($arrVars['friaco_type']);
            $this->setAdslStatic($arrVars['adsl_static']);
            $this->setNewsConn($arrVars['news_conn']);
            $this->setNewsRate($arrVars['news_rate']);
            $this->setNewsLimit($arrVars['news_limit']);
            $this->setParentId($arrVars['parentid']);
            $this->setLastMailCollection($arrVars['maildate']);
            $this->setLastFtpLogin($arrVars['ftpdate']);
        }
    }

    public static function findByMailId($intMailId)
    {
        $strQuery = "SELECT
                p.mailid,
                p.username,
                p.realm,
                p.fullname,
                p.passwd,
                p.modifydate,
                p.options,
                p.status,
                p.mail_quota,
                p.web_quota,
                p.friaco_cli1,
                p.friaco_cli2,
                p.friaco_limit_soft,
                p.friaco_limit_hard,
                p.multi_channel,
                p.cli_change,
                p.friaco_type,
                p.adsl_static,
                p.news_conn,
                p.news_rate,
                p.news_limit,
                p.parentid,
                la.maildate,
                la.ftpdate
            FROM
                passwd p
            LEFT JOIN
                datafeed.lastaccess la USING(mailid)
            WHERE p.mailid = :mailid";

        $stmStatement = BVDB::db()->prepare($strQuery);
        $stmStatement->bindValue(':mailid', $intMailId);
        $stmStatement->execute();

        if ($arrRow = $stmStatement->fetch()) {

            $arrRow['options'] = explode(',', $arrRow['options']);
            return new self($arrRow);
        } else {

            return false;
        }
    }

    public static function findByUserNameAndRealm($strUserName, $strRealm)
    {
        if (empty($strRealm)) {

            throw new Exception("Empty realm passed to Brightview_Account::findByUserNameAndRealm");
        }

        $strQuery = "SELECT
                p.mailid,
                p.username,
                p.realm,
                p.fullname,
                p.passwd,
                p.modifydate,
                p.options,
                p.status,
                p.mail_quota,
                p.web_quota,
                p.friaco_cli1,
                p.friaco_cli2,
                p.friaco_limit_soft,
                p.friaco_limit_hard,
                p.multi_channel,
                p.cli_change,
                p.friaco_type,
                p.adsl_static,
                p.news_conn,
                p.news_rate,
                p.news_limit,
                p.parentid,
                la.maildate,
                la.ftpdate
            FROM
                passwd p
            LEFT JOIN
                datafeed.lastaccess la USING(mailid)
            WHERE
                username = :username AND realm = :realm";
        $stmStatement = BVDB::db()->prepare($strQuery);
        $stmStatement->bindValue(':username', $strUserName);
        $stmStatement->bindValue(':realm', $strRealm);
        $stmStatement->execute();
        if ($arrRow = $stmStatement->fetch()) {
            $arrRow['options'] = explode(',', $arrRow['options']);
            return new self($arrRow);
        } else {

            return false;
        }
    }

    public static function findByServiceId($intServiceId)
    {

        $objConfigSoulstone = Brightview_ConfigSoulstone::findByServiceId($intServiceId);

        if (!$objConfigSoulstone) {

            return false;
        }

        return self::findByMailId($objConfigSoulstone->getMailId());
    }

    public static function findAdslEntryByServiceId($intServiceId)
    {

        $objConfigSoulstone = Brightview_ConfigSoulstone::findByServiceId($intServiceId);

        if (!$objConfigSoulstone) {

            return false;
        }

        return self::findByMailId($objConfigSoulstone->getAdslMailId());
    }

    public function save()
    {
        if ($this->isValid()) {

            if ($this->getMailId() > 0) {
                // Update
                return $this->update();
            } else {
                // Insert
                return $this->insert();
            }
        } else {
            throw new Exception("Account is not valid: \n".implode("\n", $this->getErrors())."\n");
        }
    }
    public function destroy()
    {
        if ($this->getMailId() > 0) {
            // Destory
            $strQuery = 'DELETE FROM passwd WHERE mailid = :mailid ';
            $stmStatement = BVDB::db()->prepare($strQuery);
            $stmStatement->bindValue(':mailid', $this->getMailId(), PDO::PARAM_INT);
            return $stmStatement->execute();
        } else {
            // raise an exception
            return new Exception("Destroying an object that doesn't exist in the database");
        }
    }

    /*
     * FIXME
     */

    public function isValid()
    {
        if (count($this->arrErrors) == 0) {

            return true;
        } else {

            return false;
        }
    }

    public function getErrors()
    {
        return $this->arrErrors;
    }

    public function addError($strError)
    {
        array_push($this->arrErrors, $strError);
    }

    public function hasGiganews()
    {
        // If the account exists already
        if ($this->exists()) {

            // If the giganews account hasn't been loaded
            if ($this->getGiganewsAccount() === NULL) {

                $this->setGiganewsAccount($this->findGiganewsAccount());
            }
            return is_object($this->getGiganewsAccount());

        } else {

            return false;
        }
    }
    public function exists()
    {
        return $this->getMailId() > 0;
    }

    private function findGiganewsAccount()
    {
        $strQuery = "SELECT mailid,username,realm,fullname,passwd,status,news_conn,news_rate,news_limit,parentid " .
            "FROM passwd " .
            "WHERE parentid = :parentid AND realm = :realm AND status='active'";
        $stmStatement = BVDB::db()->prepare($strQuery);
        $stmStatement->bindValue(':parentid', $this->getMailId());
        $stmStatement->bindValue(':realm', Brightview_GiganewsAccount::getNewsRealm($this->getRealm()));

        $stmStatement->execute();
        if ($arrRow = $stmStatement->fetch()) {
            return new Brightview_GiganewsAccount($arrRow);
        } else {
            return false;
        }
    }

    private function insert()
    {
        $strQuery = 'INSERT INTO passwd SET ' .
            'username = :username, ' .
            'realm = :realm, ' .
            'fullname = :fullname, ' .
            'passwd = :passwd, ' .
            'status = :status, ' .
            'modifydate = NOW(), ' .
            'options = :options, ' .
            'mail_quota = :mail_quota, ' .
            'web_quota = :web_quota, ' .
            'friaco_cli1 = :friaco_cli1, ' .
            'friaco_cli2 = :friaco_cli2, ' .
            'friaco_limit_soft = :friaco_limit_soft, ' .
            'friaco_limit_hard = :friaco_limit_hard, ' .
            'multi_channel = :multi_channel, ' .
            'cli_change = :cli_change, ' .
            'friaco_type = :friaco_type, ' .
            'adsl_static = :adsl_static, ' .
            'news_conn = :news_conn, ' .
            'news_rate = :news_rate, ' .
            'news_limit = :news_limit, '.
            'parentid = :parentid';
        $stmStatement = BVDB::db()->prepare($strQuery);
        $stmStatement->bindValue(':username', $this->getUserName());
        $stmStatement->bindValue(':realm', $this->getRealm());
        $stmStatement->bindValue(':fullname', $this->getFullName());
        $stmStatement->bindValue(':passwd', $this->getPassword());
        $stmStatement->bindValue(':status', $this->getStatus());
        $stmStatement->bindValue(':options', implode(',', $this->getOptions()));
        $stmStatement->bindValue(':mail_quota', $this->getMailQuota());
        $stmStatement->bindValue(':web_quota', $this->getWebQuota());
        $stmStatement->bindValue(':friaco_cli1', $this->getFriacoCli1());
        $stmStatement->bindValue(':friaco_cli2', $this->getFriacoCli2());
        $stmStatement->bindValue(':friaco_limit_soft', $this->getFriacoLimitSoft());
        $stmStatement->bindValue(':friaco_limit_hard', $this->getFriacoLimitHard());
        $stmStatement->bindValue(':multi_channel', $this->getMultiChannel());
        $stmStatement->bindValue(':cli_change', $this->getCliChange());
        $stmStatement->bindValue(':friaco_type', $this->getFriacoType());
        $stmStatement->bindValue(':adsl_static', $this->getAdslStatic());
        $stmStatement->bindValue(':news_conn', $this->getNewsConn());
        $stmStatement->bindValue(':news_rate', $this->getNewsRate());
        $stmStatement->bindValue(':news_limit', $this->getNewsLimit());
        $stmStatement->bindValue(':parentid', $this->getParentId());
        $bolReturn = $stmStatement->execute();
        $this->setMailId(BVDB::db()->lastInsertId('mailid'));
        return $bolReturn;
    }

    private function update()
    {
        $strQuery = 'UPDATE passwd SET ' .
        'username = :username, ' .
        'realm = :realm, ' .
        'fullname = :fullname, ' .
        (($this->getPassword() != null) ? ('passwd = :passwd, ') : ('')) .
        'status = :status, ' .
        'modifydate = NOW(), ' .
        'options = :options, ' .
        'mail_quota = :mail_quota, ' .
        'web_quota = :web_quota, ' .
        'friaco_cli1 = :friaco_cli1, ' .
        'friaco_cli2 = :friaco_cli2, ' .
        'friaco_limit_soft = :friaco_limit_soft, ' .
        'friaco_limit_hard = :friaco_limit_hard, ' .
        'multi_channel = :multi_channel, ' .
        'cli_change = :cli_change, ' .
        'friaco_type = :friaco_type, ' .
        'adsl_static = :adsl_static, ' .
        'news_conn = :news_conn, ' .
        'news_rate = :news_rate, ' .
        'news_limit = :news_limit, '.
        'parentid = :parentid ' .
        'WHERE mailid = :mailid';
        $stmStatement = BVDB::db()->prepare($strQuery);
        $stmStatement->bindValue(':username', $this->getUserName());
        $stmStatement->bindValue(':realm', $this->getRealm());
        $stmStatement->bindValue(':fullname', $this->getFullName());

        if ($this->getPassword() != null) {
            $stmStatement->bindValue(':passwd', $this->getPassword());
        }

        $stmStatement->bindValue(':status', $this->getStatus());
        $stmStatement->bindValue(':options', implode(',', $this->getOptions()));
        $stmStatement->bindValue(':mail_quota', $this->getMailQuota());
        $stmStatement->bindValue(':web_quota', $this->getWebQuota());
        $stmStatement->bindValue(':friaco_cli1', $this->getFriacoCli1());
        $stmStatement->bindValue(':friaco_cli2', $this->getFriacoCli2());
        $stmStatement->bindValue(':friaco_limit_soft', $this->getFriacoLimitSoft());
        $stmStatement->bindValue(':friaco_limit_hard', $this->getFriacoLimitHard());
        $stmStatement->bindValue(':multi_channel', $this->getMultiChannel());
        $stmStatement->bindValue(':cli_change', $this->getCliChange());
        $stmStatement->bindValue(':friaco_type', $this->getFriacoType());
        $stmStatement->bindValue(':adsl_static', $this->getAdslStatic());
        $stmStatement->bindValue(':news_conn', $this->getNewsConn());
        $stmStatement->bindValue(':news_rate', $this->getNewsRate());
        $stmStatement->bindValue(':news_limit', $this->getNewsLimit());
        $stmStatement->bindValue(':parentid', $this->getParentId());
        $stmStatement->bindValue(':mailid', $this->getMailId());
        return $stmStatement->execute();
    }

    public static function updateDialupComponentSettings($intServiceID, $strIPAddress,
        $strPassword, $bolADSL, $strFriacoCLI='')
    {
        $objConfigSoulstone = Brightview_ConfigSoulstone::findByServiceId($intServiceID);
        if (!$objConfigSoulstone) return;

        $objAccount = self::findByMailId($objConfigSoulstone->getMailId());
        $objAdslAccount = self::findByMailId($objConfigSoulstone->getAdslMailId());

        if ($bolADSL) {
            $bolIpNotChanged = true;

            if ($objAdslAccount) {
                if ($strIPAddress == 'pool') $strIPAddress = NULL;
                $bolIpNotChanged = ($strIPAddress == $objAdslAccount->getAdslStatic());

                // ugly hack (at least it's in good company) - don't sync passwords on greenbee
                $arrService = userdata_service_get($intServiceID);
                if ($bolIpNotChanged && $arrService['isp'] != 'greenbee' && trim($strPassword) != "") {
                    $objAdslAccount->setPassword($strPassword);
                }

                $objAdslAccount->save();
            }

            if ($objAccount && $bolIpNotChanged && trim($strPassword) != "") {

                $objAccount->setPassword(Crypt_Crypt::encrypt($strPassword, 'maafDes'));
                $objAccount->save();
            }

        } else {

            if ($objAccount) {

                // Problem 56740 - we were saving passwords even if they were blank or NULL
                if (trim($strPassword) != "") {

                    $objAccount->setPassword(Crypt_Crypt::encrypt($strPassword, 'maafDes'));
                } else {

                    // If the password is set to NULL, it won't update when save is called - it's what
                    // we want if it's not been changed here..
                    $objAccount->setPassword(NULL);
                }

                // If friaco dialup then set flag and friaco cli
                if (!empty($strFriacoCLI)) {
                    $strFriacoCLI = trim($strFriacoCLI);

                    $objAccount->setFriacoCli1($strFriacoCLI);

                    $arrOptions = $objAccount->getOptions();

                    if (!empty($arrOptions) && is_array($arrOptions) && !in_array('friaco', $arrOptions)) {

                        array_push($arrOptions, 'friaco');
                    } elseif (empty($arrOptions)) {

                        $arrOptions = array('friaco');
                    }

                    $objAccount->setOptions($arrOptions);

                } // end if

                $objAccount->save();
            }

            // This shouldn't exist: if it does, eliminate it
            if ($objAdslAccount && $objAdslAccount->getStatus() == 'active') {
                $objAdslAccount->setStatus('poisoned');
                $objAdslAccount->save();
            }
        }

    } // End of function updateDialupComponentSettings

    public static function updatePassword($intServiceId, $strPassword)
    {
        $objAccount = Brightview_Account::findByServiceId($intServiceId);

        if ($objAccount) {
            $objAccount->setPassword(Crypt_Crypt::encrypt($strPassword, 'maafDes'));
            $objAccount->save();

            return true;
        }

        return false;
    } // End of function updatePassword

}
