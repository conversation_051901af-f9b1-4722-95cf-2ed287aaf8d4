<?php
/**
 * tickets-notify_user_functions.inc
 *
 * Access Library for Tickets Notify User
 *
 * @package    LegacyCodebase
 * @subPackage Database Libraries
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2012 Plusnet
 */
require_once ('/local/data/mis/database/database_libraries/product-access.inc');

/**
 * Ticket Notify User
 *
 * @param unknown $current_ticket_id Current Ticket Id
 * @param unknown $ticket_action     Ticket Action
 *
 * @return unknown
 */
function ticket_notify_user($current_ticket_id,$ticket_action,$release_date=false)
{

    $objTicketsNotifyUserFunctions = Lib_TicketsNotifyUserFunctions::singleton();
    return $objTicketsNotifyUserFunctions->ticket_notify_user($current_ticket_id,$ticket_action,$release_date);
}

/**
 * Get Ticket Position In Queue
 *
 * @param unknown $my_team      My Team
 * @param unknown $my_ticket_id My Ticket Id
 * @param unknown $limit        Limit
 *
 * @return unknown
 */
function get_ticket_position_in_queue($my_team, $my_ticket_id, $limit)
{

    $objTicketsNotifyUserFunctions = Lib_TicketsNotifyUserFunctions::singleton();
    return $objTicketsNotifyUserFunctions->get_ticket_position_in_queue($my_team, $my_ticket_id, $limit);
}

/**
 * Tickets User Past Email Destinations
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function tickets_user_past_email_destinations($service_id)
{

    $objTicketsNotifyUserFunctions = Lib_TicketsNotifyUserFunctions::singleton();
    return $objTicketsNotifyUserFunctions->tickets_user_past_email_destinations($service_id);
}

/**
 * Tickets Yesterday Closed Per Hour
 *
 * @param unknown $yesterdays_date Yesterdays Date
 *
 * @return unknown
 */
function tickets_yesterday_closed_per_hour($yesterdays_date='')
{

    $objTicketsNotifyUserFunctions = Lib_TicketsNotifyUserFunctions::singleton();
    return $objTicketsNotifyUserFunctions->tickets_yesterday_closed_per_hour($yesterdays_date);
}

/**
 * Tickets Closed Per Hour
 *
 * @param unknown $now_timedate Now Timedate
 *
 * @return unknown
 */
function tickets_closed_per_hour($now_timedate="")
{

    $objTicketsNotifyUserFunctions = Lib_TicketsNotifyUserFunctions::singleton();
    return $objTicketsNotifyUserFunctions->tickets_closed_per_hour($now_timedate);
}

/**
 * Ticket Notify User On Changes
 *
 * @param unknown $status        Status
 * @param unknown $email         Email
 * @param unknown $ticket_id     Ticket Id
 * @param unknown $sms_optin     Sms Optin
 * @param unknown $mobile_number Mobile Number
 *
 * @return unknown
 */
function ticket_notify_user_on_changes($status="no", $email, $ticket_id, $sms_optin = 'no', $mobile_number = '')
{

    $objTicketsNotifyUserFunctions = Lib_TicketsNotifyUserFunctions::singleton();
    return $objTicketsNotifyUserFunctions->ticket_notify_user_on_changes($status, $email, $ticket_id, $sms_optin, $mobile_number);
}

/**
 * Ticket Notify Destination Get
 *
 * @param unknown $ticket_id Ticket Id
 *
 * @return unknown
 */
function ticket_notify_destination_get($ticket_id)
{

    $objTicketsNotifyUserFunctions = Lib_TicketsNotifyUserFunctions::singleton();
    return $objTicketsNotifyUserFunctions->ticket_notify_destination_get($ticket_id);
}

/**
 * Ticket Notify Status
 *
 * @param unknown $ticket_id Ticket Id
 *
 * @return unknown
 */
function ticket_notify_status($ticket_id)
{

    $objTicketsNotifyUserFunctions = Lib_TicketsNotifyUserFunctions::singleton();
    return $objTicketsNotifyUserFunctions->ticket_notify_status($ticket_id);
}

/**
 * Average Time To Call Centre Response
 *
 * @return unknown
 */
function average_time_to_call_centre_response()
{

    $objTicketsNotifyUserFunctions = Lib_TicketsNotifyUserFunctions::singleton();
    return $objTicketsNotifyUserFunctions->average_time_to_call_centre_response();
}

require_once '/local/data/mis/database/database_libraries/Util_LibrarySplitter.class.php';
require_once '/local/data/mis/database/database_libraries/ticketsnotifyuserfunctions/TicketsNotifyUserFunctions.class.php';

