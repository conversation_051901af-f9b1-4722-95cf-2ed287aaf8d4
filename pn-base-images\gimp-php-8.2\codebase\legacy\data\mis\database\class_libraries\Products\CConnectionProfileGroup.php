<?php

/**
 * Classes - CConnectionProfileGroup.php
 * @version $Id: CConnectionProfileGroup.php,v 1.7 2008-05-19 05:20:34 swestcott Exp $
 * @see http://dokuwiki.internal.plus.net/
 *
 * @copyright PlusNet plc, www.plus.net
 *
 * This file is part of Classes project.
 * Generated with ArgoUML PRE-0.19.5 on 02.11.2005, 16:53:38
 *
 * <AUTHOR> <<EMAIL>>
 */

/**
 * @include CAdslProduct.php
 */

/**
 * @include CConnectionProfile.php
 */

/* user defined includes */
// section 127-0-0-1--58ea7bea:1074c2e49b4:-7ff2-includes begin
// section 127-0-0-1--58ea7bea:1074c2e49b4:-7ff2-includes end

/* user defined constants */
// section 127-0-0-1--58ea7bea:1074c2e49b4:-7ff2-constants begin
// section 127-0-0-1--58ea7bea:1074c2e49b4:-7ff2-constants end

/**
 * TODO: Describe class CConnectionProfileGroup
 *
 * @access public
 * <AUTHOR> <<EMAIL>>
 */
class CConnectionProfileGroup
{
	// --- ATTRIBUTES ---

	/**
	 * TODO: Describe attribute strName
	 *
	 * @access private
	 * @var string
	 */
	var $m_strName = '';

	/**
	 * TODO: Describe attribute arrConnectionProfile
	 *
	 * @access private
	 * @var array
	 */
	var $m_arrConnectionProfile = array();

	var $m_intProfileGroupId = '';

	var $m_strHandle = '';

	// --- OPERATIONS ---

	/**
	 * TODO: Describe method CConnectionProfileGroup
	 *
	 * @access public
	 * <AUTHOR> Szulc, <<EMAIL>>
	 * @param int
	 * @return void
	 */
	function CConnectionProfileGroup($intProfileGroupId)
	{
		// section 127-0-0-1--58ea7bea:1074c2e49b4:-7fd2 begin

		if($intProfileGroupId > 0)
		{
			// Get group name first
			$strQuery = sprintf('
				SELECT
					vchName,
					vchHandle
				FROM
					products.tblConnProfileGroup
				WHERE
					intProfileGroupId = %d', $intProfileGroupId);
			$dbhConnection = get_named_connection_with_db('product');
			if($resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection))
			{
				$arrGroup = PrimitivesResultGet($resResult);
				if(count($arrGroup) > 0)
				{
					$this->m_strName = $arrGroup['vchName'];
					$this->m_intProfileGroupId = $intProfileGroupId;
					$this->m_strHandle = $arrGroup['vchHandle'];
				}
			}

		}
		// section 127-0-0-1--58ea7bea:1074c2e49b4:-7fd2 end
	}

	/**
	 * TODO: Describe method & getInstanceForSdi
	 *
	 * @access public
	 * <AUTHOR> Szulc, <<EMAIL>>
	 * @param int
	 * @return CConnectionProfileGroup
	 */
	function &getInstanceForSdi($intSdi)
	{
		$returnValue = null;
		// section 127-0-0-1--58ea7bea:1074c2e49b4:-7fcf begin

		if($intSdi > 0)
		{
			$strQuery = sprintf('
				SELECT
					intDefaultConnectionProfileGroupId
				FROM
					products.adsl_product
				WHERE
					service_definition_id = %d', $intSdi);

			$dbhConnection = get_named_connection_with_db('product');

			$resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

			$intConnectionProfileGroupId = PrimitivesResultGet($resResult, 'intDefaultConnectionProfileGroupId');
			if(!empty($intConnectionProfileGroupId))
			{
				$returnValue = new CConnectionProfileGroup((int) $intConnectionProfileGroupId);
			}
		}

		// section 127-0-0-1--58ea7bea:1074c2e49b4:-7fcf end
		return $returnValue;
	}

	/**
	 * @access public
	 * <AUTHOR> Zaki
	 * @param iintMaxDownstream , bolTimeout, strProfileGroupName
	 * @return CConnectionProfileGroup
	 */
	function & getInstanceByHandle($strHandle)
	{
		$returnValue = null;

		if($strHandle != '')
		{
			$strQuery = "SELECT intProfileGroupId
			               FROM tblConnProfileGroup
			              WHERE vchHandle = '$strHandle'";

			$dbhConnection = get_named_connection_with_db('product');
			$resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

			$intProfileGroupId = PrimitivesResultGet($resResult, 'intProfileGroupId');
			if(!empty($intProfileGroupId))
			{
				$returnValue = new CConnectionProfileGroup((int) $intProfileGroupId);
			}
		}

		return $returnValue;
	}

	/**
	 * TODO: Describe method getName
	 *
	 * @access public
	 * <AUTHOR> Szulc, <<EMAIL>>
	 * @return string
	 */
	function getName()
	{
		$returnValue = (string) '';

		// section 127-0-0-1--58ea7bea:1074c2e49b4:-7fdb begin

		$returnValue = $this->m_strName;

		// section 127-0-0-1--58ea7bea:1074c2e49b4:-7fdb end

		return $returnValue;
	}

	/*
	 * @access public
	 * <AUTHOR> Zaki
	 * @return integer
	 */
	function getProfileGroupID()
	{
		$returnValue = 0;

		// section 127-0-0-1--58ea7bea:1074c2e49b4:-7fdb begin

		$returnValue = $this->m_intProfileGroupId;

		// section 127-0-0-1--58ea7bea:1074c2e49b4:-7fdb end

		return $returnValue;
	}

	/*
	 * @access public
	 * <AUTHOR> Zaki
	 * @return string
	 */
	function getHandle()
	{
		$returnValue = (string) '';

		// section 127-0-0-1--58ea7bea:1074c2e49b4:-7fdb begin

		$returnValue = $this->m_strHandle;

		// section 127-0-0-1--58ea7bea:1074c2e49b4:-7fdb end

		return $returnValue;
	}

	/**
	 * TODO: Describe method getDefaultProfile
	 *
	 * @access public
	 * <AUTHOR> Szulc, <<EMAIL>>
	 * @return CConnectionProfile
	 */
	function getDefaultProfile()
	{
		$returnValue = null;

		// section 127-0-0-1--58ea7bea:1074c2e49b4:-7fd6 begin

		if(count($this->m_arrConnectionProfile) == 0)
		{
			$this->assignConnectionProfile();
		}
		foreach($this->m_arrConnectionProfile as $objConnectionProfile)
		{
			if($objConnectionProfile->isDefault())
			{
				$returnValue = $objConnectionProfile;
				break;
			}
		}

		// section 127-0-0-1--58ea7bea:1074c2e49b4:-7fd6 end

		return $returnValue;
	}

	/**
	 * TODO: Describe method getProfiles
	 *
	 * @access public
	 * <AUTHOR> Szulc, <<EMAIL>>
	 * @return array
	 */
	function getProfiles($intMaxDownstream = '', $bolTimeout='', $bolCapped = '')
	{
		$returnValue = array();
		if(count($this->m_arrConnectionProfile) == 0)
		{
			$this->assignConnectionProfile($bolTimeout, $intMaxDownstream, $bolCapped);
		}

		// section 127-0-0-1--58ea7bea:1074c2e49b4:-7fd4 begin

		$returnValue = $this->m_arrConnectionProfile;

		// section 127-0-0-1--58ea7bea:1074c2e49b4:-7fd4 end

		return $returnValue;
	}


	/**
	 * Assigns the m_arrConnectionProfile
	 *
	 * @access public
	 *
	 * <AUTHOR> Zaki
	 * <AUTHOR> Marek" <<EMAIL>> Modified on 15/11/2006
	 *
	 * @see CConnectionProfile
	 *
	 * @param boolean $bolTimeout With or without timeout
	 * @param int $intMaxDownstream
	 * @param boolean $bolCapped Capped or not capped
	 *
	 */
	function assignConnectionProfile($bolTimeout='', $intMaxDownstream = '', $bolCapped = '')
	{
		$dbhConnection = get_named_connection_with_db('product');
		// Now get profiles belonging to this group
		$strQuery = 'SELECT
					       DISTINCT cp.intConnectionProfileId
                     FROM
					       products.tblConnProfileGroup cpg
                           INNER JOIN products.tblConnProfileGroupProfile cpgp ON cpg.intProfileGroupId = cpgp.intProfileGroupId
                           LEFT JOIN products.tblConnectionProfile cp ON cpgp.intConnectionProfileId = cp.intConnectionProfileId
                     WHERE
                           cpg.intProfileGroupId = '.$this->getProfileGroupID().'
                           AND cp.dtmValidTo IS NULL';

			if($bolTimeout != '')
			{
				$strQuery .= " AND cp.bolTimeout = '$bolTimeout'";
			}

			if($intMaxDownstream != '')
			{
				$strQuery .= " AND cp.intMaxDownstream = $intMaxDownstream";
			}

			if($bolCapped != '')
			{
				$strQuery .= " AND cp.bolCapped = $bolCapped";
			}

			$strQuery .= ' ORDER BY cp.bolTimeout, cp.intMaxDownstream';

			if($resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection))
			{
				$adbProfileIds = PrimitivesResultsAsArrayGet($resResult);
				foreach($adbProfileIds as $arrProfile)
				{
					$intConnectionProfileId = $arrProfile['intConnectionProfileId'];
					$this->m_arrConnectionProfile[] = new CConnectionProfile((int) $intConnectionProfileId);
				}
			}
	}
} /* end of class CConnectionProfileGroup */

?>
