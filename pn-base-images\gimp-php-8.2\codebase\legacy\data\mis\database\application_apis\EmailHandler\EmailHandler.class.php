<?php
/**
 * Dependency file
 */
require_once '/local/data/mis/database/database_libraries/tickets-access.inc';
require_once '/local/data/mis/common_library_functions/class_libraries/Logging/Logging.class.php';
require_once '/local/data/mis/database/application_apis/EmailHandler/EmailHandlerException.class.php';

/**
 * Wraps WH IspAutomatedEmail class with some basic parameter checking
 * <AUTHOR>
 */
class EmailHandler
{
    /**
     * Creates instance of IspAutomatedEmail class and sends an email with an optional service notice.
     * An optional attachment can also be given.  The filename must be in one of the allowed folders
     * (@see Email_Email::_arrAllowedFolders)
     *
     * @param integer $intServiceId          Service ID (account to send the email to)
     * @param string  $strTemplate           Email template name
     * @param array   $arrAdditionalData     Additional email template variables
     * @param integer $intSnMode             Service Notice mode (@see IspAutomatedEmail_IspAutomatedEmail)
     * @param string  $strTransactionName    Database transation name
     * @param string  $strAttachmentFile     Attachment filename (optional)
     * @param string  $strAttachmentMimeType Attachment MIME type (optional)
     * @param string  $strAttachmentName     Attachment name (optional)
     *
     * @return boolean Whether the email was sent successfully
     */
    static public function sendEmail(
        $intServiceId,
        $strTemplate,
        $arrAdditionalData = array(),
        $intSnMode = IspAutomatedEmail_IspAutomatedEmail::SN_FULL,
        $strTransactionName = Db_Manager::DEFAULT_TRANSACTION,
        $strAttachmentFile = '',
        $strAttachmentMimeType = '',
        $strAttachmentName = ''
    )
    {
        try {

            $objIspAutomatedEmail = EmailHandler::prepareEmail(
                $intServiceId,
                $strTemplate,
                $arrAdditionalData,
                $intSnMode,
                $strTransactionName,
                $strAttachmentFile,
                $strAttachmentMimeType,
                $strAttachmentName
            );

            if (false === $objIspAutomatedEmail) {

                return false;
            }

            return $objIspAutomatedEmail->send($intSnMode);

        } catch (Email_Exception $excEmail) {

            if (Email_Exception::ERROR_INVALID_RECIPIENT_ADDRESS === $excEmail->getCode()) {

                // Ignore this error - User Input problem, not a lot we can do at the moment...
                return false;
            }

            EmailHandler::raiseAutoProblem($excEmail, $strTemplate, $intServiceId);

        } catch (Exception $excEmail) {

            EmailHandler::raiseAutoProblem($excEmail, $strTemplate, $intServiceId);
        }

        return false;
    }

    /**
     * Prepares an email for sending.
     * An optional attachment can also be given.  The filename must be in one of the allowed folders
     * (@see Email_Email::_arrAllowedFolders)
     *
     * @param integer $intServiceId          Service ID (account to send the email to)
     * @param string  $strTemplate           Email template name
     * @param array   $arrAdditionalData     Additional email template variables
     * @param integer $intSnMode             Service Notice mode (@see IspAutomatedEmail_IspAutomatedEmail)
     * @param string  $strTransactionName    Database transation name
     * @param string  $strAttachmentFile     Attachment filename (optional)
     * @param string  $strAttachmentMimeType Attachment MIME type (optional)
     * @param string  $strAttachmentName     Attachment name (optional)
     *
     * @return IspAutomatedEmail_IspAutomatedEmail Prepared email object
     */
    static public function prepareEmail(
        $intServiceId,
        $strTemplate,
        $arrAdditionalData = array(),
        $intSnMode = IspAutomatedEmail_IspAutomatedEmail::SN_FULL,
        $strTransactionName = Db_Manager::DEFAULT_TRANSACTION,
        $strAttachmentFile = '',
        $strAttachmentMimeType = '',
        $strAttachmentName = ''
    )
    {
        // Validate the email template name
        $strPattern = '/^(.*\/)?(.*)$/i';
        if (!preg_match($strPattern, $strTemplate, $arrMatch)) {

            return false;
        }

        $strTemplate = $arrMatch[2];

        // Remove the file extension from the email template name, if it's there
        $strPattern = '/^(.*)(\.tpl|\.txt|\.html|\.ihtml)$/i';
        if (preg_match($strPattern, $strTemplate, $arrMatch)) {

            $strTemplate = $arrMatch[1];
        }

        $objIspAutomatedEmail = new IspAutomatedEmail_IspAutomatedEmail($intServiceId, $strTransactionName);
        $objIspAutomatedEmail->prepareIspAutomatedEmail($strTemplate, $arrAdditionalData);

        // Do we have a file attachment?
        if (!empty($strAttachmentFile)) {

            // Set the attachment name to the attachment filename if the attachment name is blank.
            $strAttachmentName = empty($strAttachmentName) ? $strAttachmentFile : $strAttachmentName;

            if (empty($strAttachmentMimeType)) {

                // If we are given no MIME type assume 'text/plain'
                $strAttachmentMimeType = 'text/plain';
            }

            $objIspAutomatedEmail->addStaticAttachment($strAttachmentFile, $strAttachmentMimeType, $strAttachmentName);
        }

        return $objIspAutomatedEmail;
    }

    /**
     * raiseAutoproblem - Raises an Auto-Problem
     *
     * Raise an auto problem for the exception
     *
     * @param Exception $excEmail
     * @param string $strTemplate
     * @param integer $intServiceId
     * @static
     * @access public
     * @return void
     */
    static public function raiseAutoProblem(Exception $excEmail, $strTemplate, $intServiceId)
    {
        // The initial message in the problem description
        $strMessage = 'An exception was thrown in the EmailHandler trying to send an email. See details given below:';

        // Also displayed in the problem description, and on all additional comments thereafter
        $strVerbose = "Failed to send email template '{$strTemplate}' to Service ID '{$intServiceId}'\n\n" .
                      'Exception: ' . get_class($excEmail) . ': ' . $excEmail->getMessage() . "\n\n" .
                      Logging::getCallStackAsString();

        $problemId = pt_raise_autoproblem(
            'EmailHandler generated an error',
            'EmailHandler generated an error for template: ' . $strTemplate,
            $strMessage,
            $strVerbose
        );

        error_log(
            __CLASS__.'::'.__FUNCTION__.' - Error sending email template ['.$strTemplate.
            '] to Service ID ['.$intServiceId.'], auto problem raised [P'.$problemId.']'
        );
    }
}
