server: coredb
role: slave
rows: single
statement:

SELECT
    p.intPremiseId,
    p.vch<PERSON><PERSON>,
    p.vchStreet,
    p.vchTown,
    p.vch<PERSON>ounty,
    p.vchPostcode,
    p.vchCountry,
    p.vchActorId,
    ps.vch<PERSON><PERSON><PERSON> as vchStatusHandle,
    pt.vch<PERSON><PERSON><PERSON> as vchTypeHandle,
    pt.vchDisplayName as vchTypeName
FROM
    userdata.tblPremise p
    LEFT JOIN userdata.tblPremiseStatus ps
        ON (ps.intPremiseStatusId = p.intPremiseStatusId)
    LEFT JOIN userdata.tblPremiseHasType pht
        ON (pht.intPremiseId = p.intPremiseId)
    LEFT JOIN userdata.tblPremiseType pt
        ON (pt.intPremiseTypeId = pht.intPremiseTypeId)
WHERE
    p.intPremiseId = :intPremiseId
