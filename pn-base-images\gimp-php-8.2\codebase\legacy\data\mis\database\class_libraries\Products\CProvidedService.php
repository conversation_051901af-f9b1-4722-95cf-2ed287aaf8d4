<?php

/**
 * PlusNet Codebase Classes - CProvidedService.php
 *
 * @package   LegacyCodebase
 * <AUTHOR> <<EMAIL>>
 * @copyright 2013 PlusNet plc
 *
 */

/**
 * @include CConnectionProfile.php
 */
require_once '/local/data/mis/database/class_libraries/Products/CConnectionProfile.php';

/**
 * @include C_Core_ADSLService.php
 */
require_once '/local/data/mis/database/class_libraries/Customers/C_Core_ADSLService.php';

/**
 * A class to manage tblProvidedService entries for a given customer
 *
 * @access  public
 * @package LegacyCodebase
 * <AUTHOR> <<EMAIL>>
 *
 */
class CProvidedService
{
    // --- ATTRIBUTES ---
    // NOTE: this was originally a PHP4 class, which meant that private/public rules could not be enforced
    // Most of the below *should* be private, but some are accessed by external entities.  For now, all have been
    // set to public, to avoid compatibility issues

    /**
     * TODO: Describe attribute intProvidedServiceId
     *
     * @access public
     * @var int
     */
    public $m_intProvidedServiceId = 0;

    /**
     * TODO: Describe attribute intServiceId
     *
     * @access public
     * @var int
     */
    public $m_intServiceId = 0;

    /**
     * Current connection profile object
     *
     * @see CProvidedService::$m_objNewConnectionProfile
     * @see CConnectionProfile
     *
     * @access public
     * @var CConnectionProfile object
     */
    public $m_objConnectionProfile = null;

    /**
     * New connection profile object that will be set
     * for customer
     *
     * Remember to call <i>saveConnectionProfile()</i> method
     * after setting this attribute
     *
     * @see CProvidedService::saveConnectionProfile()
     * @see CConnectionProfile
     *
     * @access public
     * @var CConnectionProfile object
     */
    public $m_objNewConnectionProfile = null;

    /**
     * TODO: Describe attribute uxtDateStart
     *
     * @access public
     * @var int
     */
    public $m_uxtDateStart = 0;

    /**
     * TODO: Describe attribute uxtDateEnd
     *
     * @access public
     * @var int
     */
    public $m_uxtDateEnd = 0;

    /**
     * TODO: Describe attribute intProvidedServiceEventTypeId
     *
     * @access public
     * @var int
     */
    public $m_intProvidedServiceEventTypeId = 0;

    /**
     * TODO: Describe attribute strProvidedServiceEventHandle
     *
     * @access public
     * @var string
     */
    public $m_strProvidedServiceEventHandle = '';

    /**
     * TODO: Describe attribute strProvidedServiceEventTypeDisplayName
     *
     * @access public
     * @var string
     */
    public $m_strProvidedServiceEventTypeDisplayName = '';

    /**
     * TODO: Describe attribute bolCustom
     *
     * @access public
     * @var boolean
     */
    public $m_bolCustom = false;

    /**
     * TODO: Describe attribute strWpUserId
     *
     * @access public
     * @var string
     */
    public $m_strWpUserId = '';

    /**
     * TODO: Describe attribute uxtTimestamp
     *
     * @access public
     * @var int
     */
    public $m_uxtTimestamp = 0;

    /**
     * Error message - storing any error information
     *
     * @access public
     * @var string
     */
    public $strErrorMessage = '';

    /**
     * The file which should be checked before generating debug output
     *
     * @access private
     * @var string
     */
    private static $debugLockFile = '/share/admin/locks/ProvidedServiceDebug.enabled';

    /**
     * Constructor for the class: reads details from tblProvidedService
     *
     * @param int $intProvidedServiceId the customer service id
     *
     * @access public
     *
     * @return void
     */
    public function __construct($intProvidedServiceId)
    {
        $resDb = get_named_connection_with_db('userdata');

        $strQuery = '
        SELECT
            ps.intProvidedServiceID,
            ps.intServiceID,
            ps.intConnectionProfileID,
            UNIX_TIMESTAMP(ps.dtmStart) as uxtStart,
            UNIX_TIMESTAMP(ps.dtmEnd) as uxtEnd,
            ps.bolCustom,
            ps.vchUserID,
            UNIX_TIMESTAMP(ps.stmTimestamp) as uxtTimestamp,

            pset.intProvidedServiceEventTypeID,
            pset.vchDisplayName,
            pset.vchHandle

        FROM tblProvidedService ps
        INNER JOIN tblProvidedServiceEventType pset
        ON ps.intProvidedServiceEventTypeID = pset.intProvidedServiceEventTypeID
        WHERE ps.intProvidedServiceID = ' . $intProvidedServiceId;

        $res = PrimitivesQueryOrExit($strQuery, $resDb);
        $arrProvidedService = PrimitivesResultGet($res);

        $this->m_intProvidedServiceId = $arrProvidedService['intProvidedServiceID'];
        $this->m_intServiceId = $arrProvidedService['intServiceID'];
        $this->m_uxtDateStart = $arrProvidedService['uxtStart'];
        $this->m_uxtDateEnd = $arrProvidedService['uxtEnd'];
        $this->m_intProvidedServiceEventTypeId = $arrProvidedService['intProvidedServiceEventTypeID'];
        $this->m_strProvidedServiceEventHandle = $arrProvidedService['vchHandle'];
        $this->m_strProvidedServiceEventTypeDisplayName = $arrProvidedService['vchDisplayName'];
        $this->m_bolCustom = $arrProvidedService['bolCustom'];
        $this->m_strWpUserId = $arrProvidedService['vchUserID'];
        $this->m_uxtTimestamp = $arrProvidedService['uxtTimestamp'];

        $this->m_objConnectionProfile = new CConnectionProfile($arrProvidedService['intConnectionProfileID']);
    }

    /**
     * Returns a CProvidedService object based on the customer's current provided service details
     *
     * @param int $intServiceId the service id of the customer
     *
     * @access public
     *
     * @return CProvidedService
     */
    public static function & getProvidedServiceForServiceId($intServiceId)
    {
        $returnValue = null;

        $resDb = get_named_connection_with_db('userdata');
        $strQuery = <<<EOQ
SELECT 
    intProvidedServiceID 
FROM 
    userdata.tblProvidedService ps 
WHERE 
    ps.intServiceID = $intServiceId
    AND ps.dtmEnd IS NULL
EOQ;

        if ($resReturn = PrimitivesQueryOrExit($strQuery, $resDb)) {
            $intProvidedServiceId = PrimitivesResultGet($resReturn, 'intProvidedServiceID');

            if (!empty($intProvidedServiceId)) {
                $objProvidedService = new CProvidedService($intProvidedServiceId);

                if ($objProvidedService->getId()) {
                    $returnValue =& $objProvidedService;
                }
            } else {
                $returnValue = false;
            }
        }

        return $returnValue;
    }

    /**
     * TODO: Describe method getId
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @return int
     */
    public function getId()
    {
        $returnValue = (int) 0;
        $returnValue = $this->m_intProvidedServiceId;
        return $returnValue;
    }

    /**
     * TODO: Describe method getServiceId
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @return int
     */
    public function getServiceId()
    {
        $returnValue = (int) 0;
        $returnValue = $this->m_intServiceId;
        return $returnValue;
    }

    /**
     * TODO: Describe method getConnectionProfile
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @return CConnectionProfile
     */
    public function getConnectionProfile()
    {
        $returnValue = null;
        $returnValue = $this->m_objConnectionProfile;
        return $returnValue;
    }

    /**
     * Sets current connection profile
     *
     * @param CConnectionProfile $objConnectionProfile CConnectionProfile object
     *
     * @access public
     *
     * @see CConnectionProfile
     * @see CProvidedService::setNewConnectionProfile();
     *
     * @return boolean true on success, false on failure
     */
    public function setConnectionProfile($objConnectionProfile)
    {
        if (is_object($objConnectionProfile)) {
            $this->m_objConnectionProfile = $objConnectionProfile;
            return true;
        }

        return false;
    }

    /**
     * Sets new connection profile - very useful if you want to change the customer's profile to a new one. 
     * NOTE: <i>saveConnectionProfile()</i> must be called after setting the new connection profile,
     * to commit the changes to the database
     *
     * @param CConnectionProfile $objConnectionProfile CConnectionProfile object
     *
     * @access public
     *
     * @see CConnectionProfile
     * @see CProvidedService::saveConnectionProfile()
     *
     * @return boolean true on success, false on failure
     */
    public function setNewConnectionProfile($objConnectionProfile)
    {
        if (is_object($objConnectionProfile)) {
            $this->m_objNewConnectionProfile = $objConnectionProfile;
            return true;
        }

        return false;
    }

    /**
     * Mark the customer's currect connection profile as ceased/ended
     *
     * @access public
     *
     * @return null
     */
    public function ceaseCurrentConnectionProfile()
    {
        $dbhConnection = get_named_connection_with_db('userdata');

        $intServiceId = $this->getServiceId();

        $strQuery = <<<EOQ
UPDATE
    userdata.tblProvidedService
SET
    dtmEnd = NOW()
WHERE
    intServiceID = $intServiceId
    AND dtmEnd IS NULL
EOQ;

        PrimitivesQueryOrExit($strQuery, $dbhConnection);
    }

    /**
     * Simple "debugger" method to help identify why multiple "identical" rows are being inserted into 
     * tblProvidedService - if the new connection profile is not required, the code will check to see if it
     * needs to generate a report for further debugging
     * See LCST-341 for further details
     *
     * @param int $intServiceId                the customer's service id
     * @param int $intNewConnectionProfileId   the profile which is due to be added to the customer's account
     * @param int &$currentConnectionServiceId used to return the id of the customer's current 
     *
     * @access public static
     *
     * @return bool TRUE if a new record is needed, FALSE otherwise
     */
    public static function checkIfNewProvidedServiceRecordNeeded(
        $intServiceId,
        $intNewConnectionProfileId,
        &$currentConnectionServiceId = -1
    ) {
        $bolNewRecordNeeded = true;

        $resDb = get_named_connection_with_db('userdata');

        $strQuery = <<<EOQ
SELECT 
    ps.intProvidedServiceId, ps.dtmStart
FROM 
    tblProvidedService ps
WHERE 
    ps.intServiceID = $intServiceId
    AND ps.intConnectionProfileID = $intNewConnectionProfileId
    AND dtmEnd is NULL;
EOQ;

        $res = PrimitivesQueryOrExit($strQuery, $resDb);
        $arrProvidedService = PrimitivesResultGet($res);

        if (!empty($arrProvidedService)) {

            $bolNewRecordNeeded = false;

            if ($currentConnectionServiceId !== -1) {
                $currentConnectionServiceId = $arrProvidedService['intProvidedServiceId'];
            }

            // V.simple debugging
            if (file_exists(self::$debugLockFile)) {
                global $my_id;

                error_log(
                    __METHOD__ .
                    ": WARNING: [$intServiceId] is already on connection profile $intNewConnectionProfileId.\n" .
                    "User id: [$my_id].  Date of last profile update: {$arrProvidedService['dtmStart']}\n" .
                    "Stack trace:\n" . print_r(debug_backtrace(), true) . "\n"
                );
            }
        }

        return $bolNewRecordNeeded;
    }

    /**
     * Save connection profile information in tblProvidedService table
     * and marks customer for disconnection
     *
     * Sets <i>$strErrorMessage</i> attribute if error occurs
     * Event type IDs are defined in C_Core_ADSLService.php
     *
     * @param int     $intEventType                 Event type ID (default ADSL_PROVIDED_EVENT_AUTOMATED)
     * @param boolean $bolMarkCustomerForDisconnect Default true
     *
     * @access public
     *
     * @return int new ProvidedServiceID on success, or false on failure
     */
    public function saveConnectionProfile($intEventType = 0, $bolMarkCustomerForDisconnect = true)
    {
        $mixReturnValue = false;

        if ($intEventType == 0) {
            $intEventType = ADSL_PROVIDED_EVENT_AUTOMATED;
        }

        if (!is_object($this->m_objNewConnectionProfile)) {
            $this->strErrorMessage .= "New connection profile object is invalid\n";
            return $mixReturnValue;
        }

        // If the customer is already on the requested profile, we don't regenerate it
        // However, we DO refresh the customer's radius details
        $ceaseOldProfile = false;
        $createNewProfile = false;

        if (!is_object($this->m_objConnectionProfile)) {
            $createNewProfile = true;
        } else {
            // The customer has an existing profile: this will only be ceased if we confirm that the customer
            // needs a new profile
            $createNewProfile = CProvidedService::checkIfNewProvidedServiceRecordNeeded(
                $this->getServiceId(),
                $this->m_objNewConnectionProfile->getId()
            );

            $ceaseOldProfile = $createNewProfile;
        }

        if ($ceaseOldProfile) {
            $this->ceaseCurrentConnectionProfile();
        }

        if ($createNewProfile) {
            $mixReturnValue = CProvidedService::setNewProvidedService(
                $this->getServiceId(),
                $this->m_objNewConnectionProfile,
                $intEventType
            );
        }

        config_dialup_set_all_radius_data($this->getServiceId());

        if ($bolMarkCustomerForDisconnect) {
            // mark customer for disconnection
            $arrService = userdata_service_get($this->getServiceId());
            $intRadiusID = radius_user_exists($arrService['username'], $arrService['isp']);
            unset($arrService);

            if (isset($intRadiusID) && $intRadiusID > 0) {
                $bolMarkForDisconnection = radius_mark_for_disconnection($intRadiusID);

                if (!(isset($bolMarkForDisconnection) && $bolMarkForDisconnection == true)) {
                    $this->strErrorMessage .= "Failed to mark radius disconnection\n";
                }

            } else {
                $this->strErrorMessage .= "Failed to get Radius ID\n";
            }
        }

        return $mixReturnValue;
    }

    /**
     * Allocate a new Provided service record to the given customer
     * Used during signup mostly
     * Note that this method does NOT mark the customer's current provided service record (if any) as having 
     * expired; ensuring that this is actioned is the responsibility of the caller.
     *
     * @param int                $intServiceID         ServiceID
     * @param CConnectionProfile $objConnectionProfile Connection Profile object
     * @param int                $intEventType         Event type ID (defaults to ADSL_PROVIDED_EVENT_AUTOMATED)
     *
     * @access public static
     *
     * @return int new ProvidedServiceID on success or false on failure
     */
    public static function setNewProvidedService($intServiceID, $objConnectionProfile, $intEventType = 0)
    {
        global $my_id;

        // Basic data validation/sanitisation
        if (empty($intServiceID)) {
            error_log(__METHOD__ . ": [$intServiceID]: invalid Service ID\n");
            return false;
        }

        if (!is_object($objConnectionProfile)) {
            error_log(__METHOD__ . ": [$intServiceID]: invalid connection-profile object\n");
            return false;
        }

        $intProvidedServiceID = 0;
        $strUserID = 'NULL';
        $intCpID = $objConnectionProfile->getId();
        $myEventType = ADSL_PROVIDED_EVENT_PRODUCT_CHANGE;

        $intCurrentPsID = false;
        $bolNewConnectionProfileNeeded
            = CProvidedService::checkIfNewProvidedServiceRecordNeeded(
                $intServiceID,
                $intCpID,
                $intCurrentPsID
            );

        if (!$bolNewConnectionProfileNeeded) {
            $intProvidedServiceID = $intCurrentPsID;
        }

        if ($intProvidedServiceID == 0) {
            $dbhConnection = get_named_connection_with_db('userdata');

            if (ctype_digit((string) $intEventType) && $intEventType > 0) {
                $myEventType = $intEventType;
            }

            if (strlen($my_id) > 0) {
                $strUserID = PrimitivesRealEscapeString($my_id, $dbhConnection);
            }

            $strQuery = <<<EOQ
INSERT INTO userdata.tblProvidedService (
    intServiceID,  
    intConnectionProfileID,     
    dtmStart, 
    intProvidedServiceEventTypeID, 
    bolCustom, 
    vchUserID
)
VALUES (
    $intServiceID, 
    $intCpID, 
    NOW(), 
    $myEventType, 
    0, 
    '$strUserID'
)
EOQ;

            PrimitivesQueryOrExit($strQuery, $dbhConnection);

            $intProvidedServiceID = PrimitivesInsertIdGet($dbhConnection);
        }

        return $intProvidedServiceID;
    }

    /**
     * TODO: Describe method getStartDate
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @return int
     */
    public function getStartDate()
    {
        $returnValue = (int) 0;
        $returnValue = $this->m_uxtDateStart;
        return $returnValue;
    }

    /**
     * TODO: Describe method getEndDate
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @return int
     */
    public function getEndDate()
    {
        $returnValue = (int) 0;
        $returnValue = $this->m_uxtDateEnd;
        return $returnValue;
    }

    /**
     * TODO: Describe method getProvidedServiceEventTypeId
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @return int
     */
    public function getProvidedServiceEventTypeId()
    {
        $returnValue = (int) 0;
        $returnValue = $this->m_intProvidedServiceEventTypeId;

        return $returnValue;
    }

    /**
     * TODO: Describe method getProvidedServiceEventHandle
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @return string
     */
    public function getProvidedServiceEventHandle()
    {
        $returnValue = (string) '';
        $returnValue = $this->m_strProvidedServiceEventHandle;

        return $returnValue;
    }

    /**
     * TODO: Describe method getProvidedServiceEventTypeDisplayName
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @return string
     */
    public function getProvidedServiceEventTypeDisplayName()
    {
        $returnValue = (string) '';
        $returnValue = $this->m_strProvidedServiceEventTypeDisplayName;

        return $returnValue;
    }

    /**
     * TODO: Describe method isCustom
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @return boolean
     */
    public function isCustom()
    {
        $returnValue = (bool) false;
        $returnValue = $this->m_bolCustom;

        return $returnValue;
    }

    /**
     * TODO: Describe method getWpUserId
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @return string
     */
    public function getWpUserId()
    {
        $returnValue = (string) '';
        $returnValue = $this->m_strWpUserId;

        return $returnValue;
    }

    /**
     * TODO: Describe method getTimestamp
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @return int
     */
    public function getTimestamp()
    {
        $returnValue = (int) 0;
        $returnValue = $this->m_uxtTimestamp;

        return $returnValue;
    }
}
