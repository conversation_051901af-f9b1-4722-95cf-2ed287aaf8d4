<?php
/**
 * Bare-bones cleanup - these changes will be superceded by PLATOPT-37
 */

	//////////////////////////////////////////////////////////
	// Function  : split_PrimitivesResultGet
	//
	// Purpose   : wrapper to primitives_mysql_result_get
	//
	// Arguments : result_handle, the MySQL result
	//           : single_field,  just return this field from
	//           :                the single mysql row (optional)
	//           :
	// Returns   : Array of first row in set or value of
	//           : named field each entry in mysql result set
	//////////////////////////////////////////////////////////

function split_PrimitivesResultGet($result_handle, $single_field='')
{
    $result = (empty($single_field) ? array() : false);

    $row = mysql_fetch_array($result_handle, MYSQL_ASSOC);
    mysql_free_result($result_handle);

    if (empty($row)) {
        return $result;
    }

    // Slightly dodgy nesting to account for the possibility that $single_field isn't actually in the array...
    $result = (empty($single_field) ? $row : (isset($row[$single_field]) ? $row[$single_field] : false));

    return $result;
}
