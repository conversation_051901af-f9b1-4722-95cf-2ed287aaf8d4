<?php
    /**
    * Declares a class of static functions for checking the validity of various peices of data
    *
    * If one of these function fails to do it's job, modify it and the whole codebase can benefit.
    * Add new validators at your convenience.  Try not to do all repeatable validation here.
    *
    * @package    Core
    * @access     public
    * <AUTHOR>
    * @version    $Id: CValidator.inc,v 1.4 2005-09-28 09:01:39 cblack Exp $
    * @filesource
    */

    /**
    * The base class for all error handling.
    *
    * @package Core
    * @access  public
    */
    class CValidator
    {
        //
        // Public Static Methods
        //

        /**
        * Check for a given ID value in a named table/column.
        *
        * Used to validate existence of service_ids, account_ids etc.
        *
        * @access public
        * @static
        * <AUTHOR>
        * @param  object Database connection object from get_named_connection_with_db
        * @param  mixed  ID value to look for
        * @param  string The name of the database to look in
        * @param  string The name of the table to look in
        * @return string The name of the column to look in
        */
        public static function idExistsInTable(&$dbhConn, $mxdID, $strDatabaseName, $strTableName, $strIDColumnName)
        {
            $strQuery = "SELECT count(*) as intCount
                           FROM $strDatabaseName.$strTableName
                          WHERE $strIDColumnName = '$mxdID'";

            $refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Validate a intScheduledPaymentID');
            $intCount  = PrimitivesResultGet($refResult,'intCount');

            return ($intCount > 0) ? true : false;
        }


        /**
        * Validate the format of an email address
        *
        * Does not test that the email address exist, only that it *could* be and email address
        * TODO: Needs work to validate ALL email address formats.
        *
        * @access public
        * @static
        * <AUTHOR>
        * @param  string The email address to validate
        * @return boolean true if valid, false if invalid
        */
        public static function isEmailAddressFormat($strEmailAddress)
        {
            //This will need much improved!
            if (!preg_match(
                '/^[-a-z0-9_.]{1,}@[-a-z0-9_.]{1,}\.[-a-z0-9_.]{2,}$/i',
                strtolower($strEmailAddress)
            )) {
                return false;
            }

            return true;
        }

        //
        // Suggested, but unimplemented
        //

        // isServiceIDFormat
        // isServiceID
        // isPostcodeFormat
        // isPostcode
        //
        // etc...
        //


    } // class : CValidator
?>
