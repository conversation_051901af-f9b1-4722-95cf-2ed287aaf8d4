<?php
	$global_component_configurators[COMPONENT_PAPERBILLING] = "config_paperbilling_configurator";
	$global_component_configurators[COMPONENT_PAPERBILLING_FREE] = "config_paperbilling_configurator";

    // Hack to insert the component configurator array into PHP5's global scope
    // if it's not already there
    if (!isset($GLOBALS['global_component_configurators'])) {
        $GLOBALS['global_component_configurators'] = $global_component_configurators;
    }
    else {
        foreach($global_component_configurators as $intIndex => $strConfigurator) {
            $GLOBALS['global_component_configurators'][$intIndex] = $strConfigurator;
        }
    }


	function config_paperbilling_configurator ($intComponentID, $strAction)
	{
		$arrComponent = userdata_component_get($intComponentID);

		$arrAllowedStatesEnable  = array(
			'unconfigured',
			'deactive',
			'queued-activate',
			'queued-reactivate'
			);

		switch ($strAction)
		{
		    case "auto_configure":
		    	if (true == in_array($arrComponent['status'], $arrAllowedStatesEnable)) {

		    		userdata_component_set_status($intComponentID, 'active');
		    	}
		    	break;

		    case "auto_enable":
			if (true == in_array($arrComponent['status'], $arrAllowedStatesEnable)) {

		    		userdata_component_set_status($intComponentID, 'active');
		    	}
			break;

		    case "auto_disable":
		    	userdata_component_set_status($intComponentID, 'deactive');
			break;

		    case "auto_destroy":
		    	userdata_component_set_status($intComponentID, 'destroyed');
			break;

		    case "auto_refresh":

		    	switch ($arrComponent['status'])
		    	{
			case 'unconfigured':
				userdata_component_set_status($component_id, 'queued-activate');
				break;

			case 'queued-activate':
				userdata_component_set_status($component_id, 'active');
				break;

			case 'queued-destroy':
				userdata_component_set_status($component_id, 'destroyed');
				break;

			case 'destroyed':
			case 'active':
				break;
		    	}

			break;

		    default:
				//Not a supported action
				return (-1);
				break;
		}
	}

