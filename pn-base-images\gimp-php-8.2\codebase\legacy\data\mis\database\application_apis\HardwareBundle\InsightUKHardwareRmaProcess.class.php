<?php
	class InsightUKHardwareRmaProcess extends HardwareRmaProcess
	{
		var $m_strClassPrefix = 'InsightUK';
		var $m_strSupplierTag = 'insightuk';

		function InsightUKHardwareRmaProcess()
		{
			HardwareRmaProcess::HardwareRmaProcess();
		}


		function GetRmaCode()
		{
			$recRmaType  = $this->GetRmaType();
			$strTypeCode = $recRmaType['strCode'];

			$strReasonCode = $this->GetReasonCode();

			$objOriginalBundle = &$this->GetOriginalBundle();

			$strOriginalInvoiceNumber = str_pad($objOriginalBundle->GetInvoiceNumber(), 8, '0', STR_PAD_LEFT);

			$strRmaCount = sprintf('%03d', $this->GetRmaCount());

			$strRmaCode = "$strTypeCode$strReasonCode" . "00-$strOriginalInvoiceNumber-$strRmaCount";

			return $strRmaCode;
		}


		function GetSupplierReturnsAddress()
		{
			// HACK!
			return "- Insight UK\n" .
			       "- Sheffield Distribution Centre\n" .
			       "- Tinsley Park Road\n" .
			       "- (Off Shepcote Lane)\n" .
			       "- Suite B\n" .
			       "- Sheffield\n" .
			       "- S9 5DL";
		}


		function SendRmaRequest()
		{
			$objTemplate = new CTemplateEngine();

			$objTemplate->pubSetFile('/local/data/mis/database/application_apis/templates/emails/hardware_bundle_insight_rma_initiate_email.pnt');

			$recItemChanges = $this->GetActualItemChanges();

			$recTemplateData = array('strRmaCode'     => $this->GetRmaCode(),
			                         'strComments'    => stripslashes($this->GetComment()),
			                         'arrReturnItems' => $recItemChanges['Removed']);

			$objTemplate->pubSetData($recTemplateData);

			$strMailBody = $objTemplate->pubRun();

			// Send the mail to Insight's customer support department
			mail(HARDWARE_BUNDLE_INSIGHTUK_RMA_EMAIL, 'RMA Request', $strMailBody, "From: <EMAIL>\nReturn-path: <EMAIL>");
		}

	} // class InsightUKHardwareRmaProcess
?>