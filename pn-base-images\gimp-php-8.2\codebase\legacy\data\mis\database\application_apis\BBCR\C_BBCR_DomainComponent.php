<?php

use Plusnet\HouseMoves\Services\ServiceManager;

/**
 * Class for destroying the domain components
 *
 * @package    LegacyCodebase
 * @subpackage BBCR
 * <AUTHOR> <<EMAIL>>
 */
class C_BBCR_DomainComponent
{
    /**
     * @param int $intServiceId
     * @return void
     */
    public function destroyDomainComponents($intServiceId)
    {
        $componentService = $this->getHouseMoveComponentsService();
        $domainComponents = $componentService->getComponentsByGenericHandle($intServiceId, ['DOMAIN']);

        if (!empty($domainComponents)) {
            foreach ($domainComponents as $domainComponent) {
                $objComponent = $this->createDomainComponent($domainComponent['component_id']);
                if ($objComponent) {
                    $objComponent->destroy();
                }
            }
        }
    }

    /**
     * Create a Domain Component
     *
     * @param int $intComponentId
     * @return \CGenericComponent
     */
    protected function createDomainComponent($intComponentId)
    {
        return \CComponent::createInstance($intComponentId);
    }

    /**
     * Get a ComponentsService
     *
     * @return \Plusnet\HouseMoves\Services\ComponentsService
     */
    public function getHouseMoveComponentsService()
    {
        return ServiceManager::getService('ComponentsService');
    }
}
