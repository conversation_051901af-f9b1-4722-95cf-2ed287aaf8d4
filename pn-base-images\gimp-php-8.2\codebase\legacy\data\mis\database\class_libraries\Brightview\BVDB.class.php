<?php
/**
 * Class BVDB (Bright View Data Base)
 * 
 * A database connection caching class. 
 * Database connection is established only once and stored in the class as a static property.
 * Class is currently used to access BV Soulstone Mysql database. 
 * It requires proper (named 'soulstone') entry in database_local.inc
 * 
 * It will force reconnection if connectuion was not used for self::RECONNECT_AFTER seconds
 * 
 * The only public method it's providing is db() static method that returns PDO object.
 * 
 * Example of use:
 * 		$stmt = BVDB::db()->prepare('SELECT * FROM passwd');
 * Such call will return a PDO statement object.  
 *
 */

class BVDB
{
	private static $arrDbh = array();

	/**
	 * When connection was used last time.
	 *
	 * @var $uxtLastUsed unix timestamp is stored for every connection
	 */
	private static $arrLastUsed = array();

	/**
	 * Seconds to force db re-connection since last used
	 *
	 */
	const RECONNECT_AFTER = 300;

	/**
	 * __construct
	 * 
	 * There is no need to instantiate this class
	 * Please keep the constructor private
	 */

	private function __construct(){}

	/**
	 * function db
	 * 
	 * Function to return a cached connection to a given database 
	 *
	 * @param string $strDb database name
	 * @return PDO object
	 */

    public static function db($strDb = 'new_auth')
    {
        if (empty(self::$arrDbh[$strDb]))
        {
			self::connect($strDb);
			self::$arrLastUsed[$strDb] = time();
        }

        self::reconnectIfRequired($strDb);

        return self::$arrDbh[$strDb];
    }

    private static function reconnectIfRequired($strDb)
    {
    	if ((time() - self::$arrLastUsed[$strDb]) >= self::RECONNECT_AFTER )
    	{
    		//to make it even more relaiable we can try to execute a dummy SQL here
    		//if exception will be raised than reconnect

    		self::disconnect($strDb);
    		self::connect($strDb);
    	}
    	//store last used time
    	self::$arrLastUsed[$strDb] = time();
    }

    private static function disconnect($strDb)
    {
    	self::$arrDbh[$strDb] = null;
    }

    private static function connect($strDb)
    {
		$arr = self::getPdoCredentials($strDb);
		self::$arrDbh[$strDb] = new PDO(
            							$arr['dsn'],
            							$arr['username'],
            							$arr['password'],
            							array(PDO::ATTR_PERSISTENT => false)
            							);
    }

    private static function getPdoCredentials($strDataBase)
    {
        $arr = self::getCredentials();
        return array(
        			'dsn'=>"mysql:host={$arr['hostname']};dbname=$strDataBase",
        			'username'=>$arr['username'],
        			'password'=>$arr['password']
        			);
    }

    private static function getCredentials()
    {
      require '/local/data/mis/database/database_local.inc';
      return array(
                'hostname' => $databases['soulstone']['hostname'],
                'username' => $databases['soulstone']['portal_user'][0],
                'password' => $databases['soulstone']['portal_user'][1]
                );
    }
}
