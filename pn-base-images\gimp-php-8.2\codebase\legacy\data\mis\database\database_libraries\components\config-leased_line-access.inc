<?php

	/////////////////////////////////////////////////////////////////////
	// File:     config-leased_line-access.inc
	// Purpose:  Access mini-library for config-leased_line
	/////////////////////////////////////////////////////////////////////

	/////////////////////////////////////////////////////////////////////
	// Functions
	//
	// Write functions
	// ---------------
	//
	// config_leased_line_add
	//
	// Read functions
	// --------------
	//
	// config_leased_line_get
	// config_leased_line_by_component_status
	//
	// Delete functions
	// ----------------
	//
	// config_leased_line_delete
	//
	/////////////////////////////////////////////////////////////////////

	/////////////////////////////////////////////////////////////////////
	// Data

	//These Need changing
	$global_component_configurators["142"] = "config_leased_line_configurator";

	//
	/////////////////////////////////////////////////////////////////////
	// Hack to insert the component configurator array into PHP5's global scope
	// if it's not already there
	if(!isset($GLOBALS['global_component_configurators']))
	{
		$GLOBALS['global_component_configurators'] = $global_component_configurators;
	}
	else
	{
		foreach($global_component_configurators as $intIndex => $strConfigurator)
		{
			$GLOBALS['global_component_configurators'][$intIndex] = $strConfigurator;
		}
	}

	/////////////////////////////////////////////////////////////////////
	// Library functions

	/////////////////////////////////////////////////////////////
	// Function:  config_leased_line_add
	// Purpose:   Create a leased_line configuration for a component
	// Arguments: $component_id (id of corresponding component)
	// Returns:   ID of the mrtg configuration record
	/////////////////////////////////////////////////////////////

	function config_leased_line_add($component_id)
	{

		global $global_db_src;
                $connection = get_named_connection ("userdata");

		$component_id = addslashes ($component_id);

		mysql_query ("INSERT INTO config_leased_lines (component_id)
						   VALUES ('$component_id')", $connection);

		return mysql_insert_id();

	}


	/////////////////////////////////////////////////////////////
	// Function:  config_leased_line_get
	// Purpose:   Get a component and mrtg configuration
	//            record
	// Arguments: $component_id
	// Returns:   Union of component and mrtg configuration
	/////////////////////////////////////////////////////////////

	function config_leased_line_get($component_id) 
	{

		global $global_db_src;
                $connection = get_named_connection ("userdata");

		$component_id = addslashes ($component_id);

		$result = mysql_query ("SELECT * FROM components LEFT JOIN config_leased_line
						 ON components.config_id = config_leased_line.leased_line_id
						 WHERE components.component_id = '$component_id'", $connection);

		$union = mysql_fetch_array ($result, MYSQL_ASSOC);

		mysql_free_result ($result);

		return $union;
	}


	/////////////////////////////////////////////////////////////
	// Function:  config_leased_line_get_by_component_status
	// Purpose:   Retrieve all mrtg with a particular
	//              component status
	// Arguments: $status (desired status)
	// Returns:   Array of component-leased_line unions
	/////////////////////////////////////////////////////////////

	function config_leased_line_get_by_component_status ($status) 
	{

		global $global_db_src;
                $connection = get_named_connection ("userdata");

		$status = addslashes ($status);

		$result = mysql_query ("SELECT * FROM components,config_leased_line
						 WHERE components.component_id = config_leased_line.component_id
						 AND components.status = '$status'", $connection);

		$unions = array();
                while ($union = mysql_fetch_array ($result, MYSQL_ASSOC)) 
		{
                        $unions[] = $union;
                }

		mysql_free_result ($result);

		return $unions;
	}


	/////////////////////////////////////////////////////////////
	// Function:  config_leased_line_destroy
	// Purpose:   Delete a leased_line configuration
	// Arguments: $leased_line_id
	/////////////////////////////////////////////////////////////

	function config_leased_line_destroy($component_id)
	{
		$component_id=addslashes($component_id);
		$component = userdata_component_get($component_id);
		$leased_line_id = $component['config_id'];
		config_leased_line_delete($leased_line_id);
		userdata_component_set_status($component_id, 'destroyed');
		$connection = get_named_connection ("userdata");
		$query="UPDATE components SET config_id='-1' WHERE component_id='$component_id'";
		mysql_query($query,$connection);
	}

	/////////////////////////////////////////////////////////////
	// Function:  config_leased_line_delete
	// Purpose:   Delete a mrtg configuration
	// Arguments: $leased_line_id
	/////////////////////////////////////////////////////////////

	function config_leased_line_delete ($leased_line_id) 
	{

		global $global_db_src;

		$connection = get_named_connection ("userdata");

		$component_id = addslashes ($leased_line_id);

		mysql_query ("DELETE FROM config_leased_line
				     WHERE leased_line_id = '$leased_line_id'", $connection);
	}

	/////////////////////////////////////////////////////////////
	// Function: config_leased_line_activate
	// Purpose:  activate a componet
	// Args:     
	/////////////////////////////////////////////////////////////

	function config_leased_line_activate($component_id)
	{
		global $global_db_src;
		$connection = get_named_connection ("userdata");
		$component_id=addslashes($component_id);
		//First get the leased line id
		$query="SELECT leased_line_id FROM config_leased_line WHERE component_id='$component_id'";
		$result=mysql_query($query,$connection);
		$leased_line_id= mysql_fetch_array ($result, MYSQL_ASSOC);
		$leased_line_id=$leased_line_id[leased_line_id];
		$query="UPDATE components SET status='active',config_id='$leased_line_id' WHERE component_id='$component_id'";
		mysql_query($query,$connection);
	}

	// Library functions
	/////////////////////////////////////////////////////////////////////


	function config_leased_line_configurator ($component_id, $action)
	{
		switch ($action)
		{
			case "auto_configure":
				config_leased_line_activate($component_id);
				break;

			case "auto_disable":
				
				break;

			case "auto_enable":
				
				break;

			case "auto_refresh":
				// Nothing to do here
				break;

			case "auto_destroy":
				config_leased_line_destroy ($component_id);
				break;

			default:
				break;
		}
	}
?>
