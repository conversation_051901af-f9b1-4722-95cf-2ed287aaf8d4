<?php
/**
* This file declares the CScheduledEvent class
*
* @package    Core
* @subpackage System Events
* <AUTHOR>
* @version    $Id: CScheduledEvent.inc,v 1.4 2006-07-04 04:52:38 fzaki Exp $
* @filesource
*/

require_once('/local/data/mis/database/database_libraries/CoreObjects/Utility/CValidator.inc');
require_once('/local/data/mis/database/database_libraries/CoreObjects/CObject/CObject.inc');

require_once(DATABASE_LIBRARY_ROOT.'sql_primitives.inc');

class CScheduledEvent extends CObject
{
    /**
    * Scheduled Event ID
    *
    * @var integer
    * @access private
    */
    var $m_intScheduledEventID = 0;

    /**
    * Event Type ID
    *
    * @var integer
    * @access private
    */
    var $m_intEventTypeID = 0;

    /**
    * Date the event was created
    *
    * @var integer
    * @access private
    */
    var $m_uxtCreated = 0;

    /**
    * Date the event is due
    *
    * @var integer
    * @access private
    */
    var $m_uxtDue = 0;

    /**
    * Does the even take place as part of the billing run?
    *
    * If this flag is set, the event will be performed on the next billing run after the due date.
    * If no set, the event will be performed any time after the due date.
    *
    * @var integer
    * @access private
    */
    var $m_bolOnBilling = true;

    /**
     * @var boolean
     */
    var $m_bolOnContractEnd = true;

    /**
    * Date the event is completed (0 if not completed)
    *
    * @var integer
    * @access private
    */
    var $m_uxtCompleted = 0;

    /**
    * Date the event was cancelled (0 if still active)
    *
    * @var integer
    * @access private
    */
    var $m_uxtCancelled = 0;

    /**
     * @var boolean
     */
    var $m_bolAdjustDate = false;

    /**
    * Constructor for the CScheduledEvent class
    *
    * @param int The component ID
    * <AUTHOR>
    * @return boolean success
    */
    function CScheduledEvent($intScheduledEventID)
    {
        if ($intScheduledEventID < 1) {
            return true;
        }

        $dbhConn = get_named_connection_with_db('systemEvents');

        $strQuery = "SELECT se.intScheduledEventID,
                            se.intEventTypeID,
                            IF(se.dteDue IS NULL, 0, UNIX_TIMESTAMP(se.dteDue)) as uxtDue,
                            se.bolOnBilling,
                            se.bolOnContractEnd,
                            IF(se.dtmCompleted IS NULL, 0, UNIX_TIMESTAMP(se.dtmCompleted)) as uxtCompleted,
                            IF(se.dtmCancelled IS NULL, 0, UNIX_TIMESTAMP(se.dtmCancelled)) as uxtCancelled,
                            IF(se.dtmCreated IS NULL, 0, UNIX_TIMESTAMP(se.dtmCancelled)) as uxtCreated
                       FROM dbSystemEvents.tblScheduledEvent se
                      WHERE se.intScheduledEventID = '$intScheduledEventID'";
        $refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Fetch fields for a scheduled event');
        $arrEvent = PrimitivesResultsAsArrayGet($refResult);

        $arrEvent = $arrEvent[0];

        if (!isset($arrEvent['intScheduledEventID']) || $arrEvent['intScheduledEventID'] != $intScheduledEventID) {
            return false;
        }

        $this->m_intScheduledEventID = $arrEvent['intScheduledEventID'];
        $this->m_uxtDue = $arrEvent['uxtDue'];
        $this->m_bolOnBilling = intval($arrEvent['bolOnBilling']) ? true : false;
        $this->m_bolOnContractEnd = intval($arrEvent['bolOnContractEnd']) ? true : false;
        $this->m_uxtCreated = $arrEvent['uxtCreated'];
        $this->m_uxtCompleted = $arrEvent['uxtCompleted'];
        $this->m_uxtCancelled = $arrEvent['uxtCancelled'];

        return true;
    }

    //////////////
    // Accessors
    //////////////

    //////////////////
    // Static Methods
    //////////////////

    ///////////////////////////
    // Static Validator Methods
    ///////////////////////////

    /**
    * Validates the format of a scheduled event ID
    *
    * @access public
    * @static
    * <AUTHOR>
    * @param  integer ScheduledEvent ID
    * @return boolean true if matches, false if not
    */
    function isValidScheduledEventIDFormat($intScheduledEventID)
    {
        if (!preg_match('/^[0-9]{1,8}$/i', $intScheduledEventID)) {
            return false;
        }

        return true;
    }

    /**
    * Validates a ScheduledEvent ID
    *
    * Expensive, as it requires a database hit. Only use if really required,
    * such as for financial actions.
    * Otherwise, try isValidScheduledEventIDFormat
    *
    * @access public
    * @static
    * <AUTHOR>
    * @param  integer ScheduledEvent ID
    * @return boolean true if matches, false if not
    */
    function isValidScheduledEventID($intScheduledEventID)
    {
        if (!CScheduledEvent::isValidScheduledEventIDFormat($intScheduledEventID)) {
            return false;
        }

        $dbhConn = get_named_connection_with_db('systemEvents');

        return CValidator::idExistsInTable(
            $dbhConn,
            $intScheduledEventID,
            'dbSystemEvents',
            'tblScheduledEvent',
            'intScheduledEventID'
        );
    }

    //////////////////
    // Public Methods
    //////////////////

    /**
    * Insert Event
    *
    * @access private
    * <AUTHOR>
    * @return boolean success
    */
    function insertEvent()
    {
        if ($this->m_intEventTypeID < 1) {
            return false;
        }

        if ($this->m_uxtDue == 0) {
            return false;
        }

        if ($this->m_uxtCreated == 0) {
            $this->m_uxtCreated = time();
        }

        $dbhConn = get_named_connection_with_db('systemEvents');

        $bolOnBilling = ($this->m_bolOnBilling) ? '1' : '0';
        $bolOnContractEnd = ($this->m_bolOnContractEnd) ? '1' : '0';
        $bolAdjustDate = ($this->m_bolAdjustDate) ? '1' : '0';

        $strQuery = "INSERT INTO dbSystemEvents.tblScheduledEvent
                             SET intEventTypeID = '{$this->m_intEventTypeID}',
                                 dtmCreated = FROM_UNIXTIME({$this->m_uxtCreated}),
                                 dteDue = FROM_UNIXTIME({$this->m_uxtDue}),
                                 bolOnBilling = $bolOnBilling,
                                 bolOnContractEnd = $bolOnContractEnd,
                                 bolAdjustDate = $bolAdjustDate";

        $refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Create a new Scheduled Event');

        $this->m_intScheduledEventID = PrimitivesInsertIdGet($dbhConn);

        return ($this->m_intScheduledEventID) ? true : false;
    }

    /**
    * Mark a Scheduled event as completed
    *
    * @access public
    * <AUTHOR>
    * @param  integer Unix timestamp of the time the event was completed.  Defaults to now.
    * @return boolean true on success, false on failure.
    */
    function markAsCompleted($uxtCompleted=0)
    {
        if (!CScheduledEvent::isValidScheduledEventID($this->m_intScheduledEventID)) {
            return false;
        }

        if ($uxtCompleted == 0) {
            $uxtCompleted = time();
        }

        $dbhConn = get_named_connection_with_db('systemEvents');

        $strQuery = "UPDATE dbSystemEvents.tblScheduledEvent
                        SET dtmCompleted = FROM_UNIXTIME($uxtCompleted)
                      WHERE intScheduledEventID = '{$this->m_intScheduledEventID}'";

        $refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Mark a Scheduled Event as completed');

        if (PrimitivesAffectedRowsGet($dbhConn) != 1) {
            return false;
        }

        $this->m_uxtCompleted = $uxtCompleted;

        return true;
    }

    /**
    * Cancel this Scheduled event
    *
    * @access private
    * <AUTHOR>
    * @return
    */
    function cancelEvent()
    {
        if (!CScheduledEvent::isValidScheduledEventID($this->m_intScheduledEventID)) {
            return false;
        }

        $uxtCancelled = time();

        $dbhConn = get_named_connection_with_db('systemEvents');

        $strQuery = "UPDATE dbSystemEvents.tblScheduledEvent
                        SET dtmCancelled = FROM_UNIXTIME($uxtCancelled)
                      WHERE intScheduledEventID = '{$this->m_intScheduledEventID}'";

        $refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Mark a Scheduled Event as cancelled');

        if (PrimitivesAffectedRowsGet($dbhConn) != 1) {
            return false;
        }

        $this->m_uxtCancelled = $uxtCancelled;

        return true;

    }

    /**
    * Update the details of a scheduled event
    *
    * @return boolean true on success, false on failure.
    */
    public function updateEvent()
    {
        if (!CScheduledEvent::isValidScheduledEventID($this->m_intScheduledEventID)) {
            return false;
        }

        if ($uxtCompleted == 0) {
            $uxtCompleted = time();
        }

        $dbhConn = get_named_connection_with_db('systemEvents');

        $bolOnBilling = ($this->m_bolOnBilling) ? '1' : '0';
        $bolOnContractEnd = ($this->m_bolOnContractEnd) ? '1' : '0';
        $bolAdjustDate = ($this->m_bolAdjustDate) ? '1' : '0';

        $strQuery = "UPDATE dbSystemEvents.tblScheduledEvent
                        SET intEventTypeID = '{$this->m_intEventTypeID}',
                            dteDue = FROM_UNIXTIME({$this->m_uxtDue}),
                            bolOnBilling = $bolOnBilling,
                            bolOnContractEnd = $bolOnContractEnd,
                            bolAdjustDate = $bolAdjustDate
                      WHERE intScheduledEventID = '{$this->m_intScheduledEventID}'";

        $refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Update details of a Scheduled Event');

        if (PrimitivesAffectedRowsGet($dbhConn) != 1) {
            return false;
        }

        return true;
    }

    //////////////////
    // Private Methods
    //////////////////

    /**
    * Fetch Event type id by handle
    *
    * @access private
    * <AUTHOR>
    * @param  string The event handle
    * @return integer Event ID
    */
    function getEventTypeID($strHandle)
    {
        $dbhConn = get_named_connection_with_db('systemEvents');

        $strQuery = "SELECT intEventTypeID
                       FROM tblEventType
                      WHERE vchHandle = '$strHandle'";

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Get event type ID');
        $arrEventType = PrimitivesResultGet($resResult);

        return $arrEventType['intEventTypeID'];
    }
}
