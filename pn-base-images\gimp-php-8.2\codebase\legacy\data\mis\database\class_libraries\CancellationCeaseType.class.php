<?php
require_once '/local/data/mis/database/class_libraries/InvalidHandleException.class.php';
/**
 * CancellationCeaseType Class File
 *
 * @category  LegacyCodebase
 * @package   LegacyCodebase
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 PlusNet
 * @since     File available since 2009-11-19
 */
/**
 * CancellationCeaseType Class
 *
 * @category  LegacyCodebase
 * @package   LegacyCodebase
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2009 PlusNet
 */
class CancellationCeaseType
{
	/**
	 * MigrationOut reported in Lost Migration report
	 */
	const MIGRATION_OUT = 'MIGRATION_OUT';
	
	/**
	 * MigrationOut without MAC key reported in OrderDelta report
	 * This is due to customer migrating to LLU provider
	 * 
	 */
	const MIGRATION_OUT_WITHOUT_MAC = 'MIGRATION_OUT_WITHOUT_MAC';
	
	/**
	 * PSTN Cease reported in OrderDelta report
	 */
	const BB_CEASE      = 'BB_CEASE';

	/**
	 * @var String
	 */
	protected $strHandle;

	/**
	 * Populate the object from the handle
	 *
	 * @throws InvalidHandle_Exception
	 * @return CancellationCeaseType
	 */
	public function populateByHandle($strHandle)
	{
		$arrCeaseTypesHandles = array(self::MIGRATION_OUT, self::MIGRATION_OUT_WITHOUT_MAC, self::BB_CEASE);
		
		if(in_array($strHandle, $arrCeaseTypesHandles))
		{
			$this->strHandle = $strHandle;
		} 
		else
		{
			throw new InvalidHandle_Exception("Invalid Handle: $strHandle");
		}
	}

	/**
	 * Returns handle
	 *
	 * @return string
	 */
	public function getHandle()
	{
		return $this->strHandle;
	}
}
