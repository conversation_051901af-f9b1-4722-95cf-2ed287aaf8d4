<?php
/*
* C_Simple_Error
* Adds a few convenience methods for error handling
* because we cannot use exceptions (yet)
*/

## Direct requirements of this class

class C_Simple_Error
{
	var $m_arrErrors = null;

	function C_Simple_Error()
	{
		$this->m_arrErrors = null;
	}
	# Returns: array of current errors
	function getErrors()
	{
		return $this->m_arrErrors;
	}

	# Sets: array of current errors (cannot use Exceptions - php4)
	# Validates: should be an array
	# Returns: true on success, false on failure
	function setErrors($arrErrors)
	{
		if (is_array($arrErrors))
		{
			$this->m_arrErrors = $arrErrors;
			return true;
		}
		return false;
	}

	# Sets: Appends the error to the array of current errors
	# Validates: error string not empty
	# Returns: true on success, false on failure
	function addError($strError)
	{
		if ($strError!='')
		{
			$ary = $this->getErrors();
			$ary[] = $strError;
			$this->setErrors($ary);
			return true;
		}
		return false;
	}
}
?>
