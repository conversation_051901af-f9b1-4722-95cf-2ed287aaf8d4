<?php

/**
 * Class used to communicate with Netintelligence APIs
 *
 */
class NetInterface
{

	/**
	 * Base url
	 *
	 * @var string
	 */
	private $strBaseUrl;

	/**
	 * File Name need to add with base url
	 *
	 * @var string
	 */
	private $strFileName;

		/**
	 * Associative arrary contains name and value pairs
		 *
		 * @var array
		 */
	private$arrParams;

	/**
	 * Number of seconds to timeout
	 *
	 * @var integer
	 */
	private $intTimeout;

	/**
	 * Contains the fetched web source
	 *
	 * @var string
	 */
	private $strResult;


	/**
	 * Contains any error occurred
	 *
	 * @var string
	 */
	private $strError;

	/**
	 * Contains the last header
	 *
	 * @var string
	 */
	private $strHeader;

	/**
	 * Constructor for initializing the class with default values.
	 *
	 * @return void
	 */
	public function __construct()
	{
		$this->clear();
		$this->intTimeout = '60';
	}

	/**
	 * Clear all values except base url in the class variable
	 *
	 * @return void
	 */
	public function clear()
	{
		$this->strFileName = '';
		$this->strResult = '';
		$this->strHeader = '';
		$this->strError = '';
		$this->arrParams = array();
	}


	/**
	 * Set Base URL
	 *
	 * Example use:
	 * 		http://www.somedomain.com
	 *
	 * @param string $strUrl URL target resource without file name.
	 * @return void
	 */
	public function setBaseUrl($strBaseUrl)
	{
		$this->strBaseUrl = $strBaseUrl;
	}

	/**
	 * Set file name into Base URL
	 *
	 * Example use:
	 * 		api2_set_expiry.php
	 *
	 * @param string $strUrl File name need to add with base url.
	 * @return void
	 */
	public function setFileName($strFileName)
	{
		$this->strFileName = $strFileName;
	}

	/**
	 * Set timeout of execution
	 *
	 * @param integer $intSeconds Timeout delay in seconds
	 * @return void
	 */
	public function setTimeout($intSeconds)
	{
		if (0 < $intSeconds) {
			$this->intTimeout = $intSeconds;
		}
	}

	/**
	 * Set request parameters
	 *
	 * @param array $arrParams Array containing name and value pairs.
	 * @return void
	 */
	public function setParams($arrParams)
	{
		if (is_array($arrParams)) {
			$this->arrParams = array_merge($this->arrParams, $arrParams);
		}
	}

	/**
	 * Add request parameters
	 *
	 * @param string $strName Name of the parameter
	 * @param string $strValue Value of the parameter
	 * @return void
	 */
	public function addParam($strName, $strValue)
	{
		if (!empty($strName)) {
			$this->arrParams[$strName] = $strValue;
		}
	}

	/**
	 * Get execution result body
	 *
	 * @return string Output of execution
	 */
	public function getResult()
	{
		return $this->strResult;
	}

	/**
	 * Get header information
	 *
	 * @return string Last header of execution
	 */
	public function getHeader()
	{
		return $this->strHeader;
	}

	/**
	 * Get last execution error
	 *
	 * @return string Last error message (if any)
	 */
	public function getError()
	{
		return $this->strError;
	}

	/**
	 * Execute a HTTP request
	 *
	 * Executes the http fetch using all the set properties.
	 *
	 * @return string Response body of the target page
	 */
	public function execute()
	{
		$this->validation();

		$strTargetUrl = $this->createTargetUrl();

		$fphCurl = curl_init();

		curl_setopt ($fphCurl, CURLOPT_HTTPGET, TRUE);

		curl_setopt($fphCurl, CURLOPT_HEADER,         TRUE);
		curl_setopt($fphCurl, CURLOPT_NOBODY,         FALSE);

		curl_setopt($fphCurl, CURLOPT_TIMEOUT,        $this->intTimeout);
		curl_setopt($fphCurl, CURLOPT_URL,            $strTargetUrl);

		curl_setopt($fphCurl, CURLOPT_SSL_VERIFYPEER, FALSE);
		curl_setopt($fphCurl, CURLOPT_SSL_VERIFYHOST, FALSE);
		curl_setopt($fphCurl, CURLOPT_RETURNTRANSFER, TRUE);

		$strContent = curl_exec($fphCurl);

		if($strContent === FALSE) {
			throw new RuntimeException("cURL request to $strTargetUrl failed: " . curl_error($fphCurl));
		}

		$arrCurlContent = explode("\r\n\r\n", $strContent);

		$this->strResult = $arrCurlContent[count($arrCurlContent) - 1];

		$this->strHeader = ($arrCurlContent[count($arrCurlContent) - 2]);

		curl_close($fphCurl);

		return $this->strResult;
	}

	/**
	 * Create target Url
	 *
	 * @return string Target Url
	 */
	private function createTargetUrl()
	{
		$strBaseUrl = trim($this->strBaseUrl);
		$strTargetUrl = '';
		$strQueryString = '';

		if ('/' == $strBaseUrl[strlen($strBaseUrl) - 1]) {
			$strTargetUrl = $strBaseUrl . $this->strFileName;
		}
		else {
			$strTargetUrl = $strBaseUrl . '/' . $this->strFileName;
		}

		$strQueryString = $this->createQueryString();

		$strTargetUrl = $strTargetUrl . "?" . $strQueryString;

		return $strTargetUrl;
	}

	/**
	 * Prepare query string
	 *
	 * @return string Query string
	 */
	private function createQueryString()
	{
		$arrTemp = array();
		$strQueryString = '';

		if(is_array($this->arrParams) && (0 < count($this->arrParams))) {
			foreach ($this->arrParams as $strKey => $strValue) {
				if(0 < strlen(trim($strValue))) {
					$arrTemp[] = $strKey . "=" . $strValue;
				}
			}

			$strQueryString = join('&', $arrTemp);
		}
		else {
			throw new Exception('No paramenter found to create query string');
		}

		return $strQueryString;
	}

	/**
	 * Validate Base Url
	 *
	 * @return TRUE - Succssful validation.
	 * @throws RestInterfaceException
	 */
	private function validateBaseUrl()
	{
		if (0 == strlen(trim($this->strBaseUrl))) {
			throw new Exception('Base URL not found');
		}
		return TRUE;
	}

	/**
	 * Validate file name
	 *
	 * @return TRUE - Succssful validation.
	 * @throws RestInterfaceException
	 */
	private function validateFileName()
	{
		if (0 == strlen(trim($this->strFileName))) {
			throw new Exception('File Name not found');
		}
		return TRUE;
	}

	/**
	 * Validate all basic class variables.
	 *
	 * @return TRUE - Successful validation.
	 * @throws RestInterfaceException
	 */
	private function validation()
	{
		$this->validateBaseUrl();
		$this->validateFileName();
		return TRUE;
	}

}

