<?php

	abstract class Util_LibrarySplitter
	{
		protected $arrFunctions = array();
		protected $myName;
		protected $myFile;

		abstract protected function __construct();

		// The singleton method

		public function __call($strMethod, $arrArgs)
		{
			$strSudoMethodName = $this->myName."_$strMethod";
			if(!isset($this->arrFunctions[$strSudoMethodName]))
			{
				// functions should be in a sub-directory 
				// e.g., for "userdata-access.inc" a sub-directory of "userdata"
				$strMyPath = dirname($this->myFile)."/".strtolower(preg_replace('/^Lib_/', '', $this->myName)).'/';
				$strPath = realpath($strMyPath);

				$strSudoMethodFile = "$strPath/$strMethod.method.php";
		
				if( (!is_dir($strPath)) || (!file_exists($strSudoMethodFile)) )
				{
					// try hard coded database_libraries path ...
					$strSudoMethodFile = '/local/data/mis/database/database_libraries/' . strtolower(preg_replace('/^Lib_/', '', $this->myName)).'/' . $strMethod . '.method.php';

					if(!file_exists($strSudoMethodFile))
					{
						throw new Exception("method $strSudoMethodName is unknown it should exist in file\n$strSudoMethodFile\n");
					}
				}	

				require_once($strSudoMethodFile);
				$this->arrFunctions[$strSudoMethodName] = $strSudoMethodName;
			}

			// Call method
			if(!function_exists('split_'.$strMethod))
			{
				$strErrorMessage = 'function split_' . $strMethod . ' not found';
				trigger_error($strErrorMessage, E_USER_ERROR);
				return false;
			}

			return call_user_func_array('split_'.$strMethod,$arrArgs);
		}

		public function __sleep()
		{
		}
	}

?>
