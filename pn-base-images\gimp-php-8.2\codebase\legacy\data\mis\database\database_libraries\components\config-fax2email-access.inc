<?php

	/////////////////////////////////////////////////////////////////////
	// File:     config-fax2email-access.inc
	// Purpose:  Access mini-library for config_fax2emails
	/////////////////////////////////////////////////////////////////////

	/////////////////////////////////////////////////////////////////////
	// Functions
	//
	// Write functions
	// ---------------
	//
	// config_fax2email_add
	// config_fax2email_manual_config
	// configFax2EmailAddLog
	//
	// Read functions
	// --------------
	//
	// config_fax2email_get 
	// configFax2EmailActionLogIdGet
	// configFax2EmailSourceLogIdGet
	//
	// Modify Functions
	// ----------------
	// config_fax2email_update
	//
	// Delete functions
	// ----------------
	//
	// config_fax2email_delete
	//
	/////////////////////////////////////////////////////////////////////


	/////////////////////////////////////////////////////////////////////
	// Data

	$global_component_configurators["3"] = "config_fax2email_configurator";

	// Data
	/////////////////////////////////////////////////////////////////////
	// Hack to insert the component configurator array into PHP5's global scope
	// if it's not already there
	if(!isset($GLOBALS['global_component_configurators']))
	{
		$GLOBALS['global_component_configurators'] = $global_component_configurators;
	}
	else
	{
		foreach($global_component_configurators as $intIndex => $strConfigurator)
		{
			$GLOBALS['global_component_configurators'][$intIndex] = $strConfigurator;
		}
	}

	/////////////////////////////////////////////////////////////////////
	// Library functions

	/////////////////////////////////////////////////////////////
	// Function:  config_fax2email_add
	// Purpose:   Create a fax2email configuration for a
	//            component
	// Arguments: $component_id (id of corresponding component)
	// Returns:   ID of the fax2email configuration record
	/////////////////////////////////////////////////////////////

	function config_fax2email_add ($component_id, $email, $strSourceTag, $intFax2EmailUploadId = 0) {

		global $global_db_src;
                $connection = get_named_connection ("userdata");

		$component_id = addslashes ($component_id);
		$email = addslashes ($email);

		// Find and lock free number
		mysql_query ("LOCK TABLES config_fax2email_availability WRITE", $connection)
			or report_error (__FILE__, __LINE__, mysql_error($connection));

		$result = mysql_query ("SELECT fax_number FROM config_fax2email_availability
							  WHERE available = 'yes'
							  ORDER BY releasedate
							  LIMIT 1", $connection)
			or report_error (__FILE__, __LINE__, mysql_error($connection));

		if (mysql_num_rows ($result) == 0) {
			// Bail out in terror
			mysql_free_result ($result);
			mysql_query ("UNLOCK TABLES", $connection)
				or report_error (__FILE__, __LINE__, mysql_error($connection));
			return -1;
		}
		$fax_number = mysql_result ($result, 0, 0);
		mysql_free_result ($result);

		mysql_query ("UPDATE config_fax2email_availability SET available = 'no'
				     WHERE fax_number = '$fax_number'", $connection)
			or report_error (__FILE__, __LINE__, mysql_error($connection));

		mysql_query ("UNLOCK TABLES", $connection)
			or report_error (__FILE__, __LINE__, mysql_error($connection));


		// Create the config record
		mysql_query ("INSERT INTO config_fax2email (component_id, fax_number, email)
						        VALUES ('$component_id', '$fax_number', '$email')", $connection)
			or report_error (__FILE__, __LINE__, mysql_error($connection));

		$intFax2EmailId = mysql_insert_id ($connection);

		//Add the log
		configFax2EmailAddLog($component_id,$fax_number,$strSourceTag,'assigned',$intFax2EmailUploadId);

		return $intFax2EmailId;

	}

	//////////////////////////////////////////////////////////////
	// Function:  config_fax2email_manual_config
	// Purpose:   configure the fax to email component
	// Arguments: $component_id (id of component), email address
	// Returns:
	// Author: D Lewis
	///////////////////////////////////////////////////////////////

	function config_fax2email_manual_config($component_id, $email, $strSourceTag = 'portal', $intFax2EmailUploadId = 0)
	{
		$component_id=addslashes($component_id);
		$email=addslashes($email);

		//Check to see if there is an component and get its status
		$component_details=userdata_component_get($component_id);
		switch($component_details['status'])
		{
			case"unconfigured":
					$action="do_new";
					break;
			case"queued-activate":
					$action="do_change";
					break;
			case"queued-reactivate":
					$action="do_change";
					break;
			case"active":
					$action="do_change";
					break;
			case"queued-deactivate":
					$action="do_change";
					break;
			case"queued-deconfigure":
					$action="do_change";
					break;
			case"deactive":
					break;
			case"queued-destroy":
					break;
			case"destroyed":
					break;
			case"invalid":
					break;
			default:
				echo "Error with component";
		}
		switch($action)
		{
			case"do_new":
			$fax2email_config_id=config_fax2email_add($component_id, $email, $strSourceTag, $intFax2EmailUploadId);
			$connection = get_named_connection ("userdata");
			$query="UPDATE components SET config_id = '$fax2email_config_id', status='active' WHERE component_id = '$component_id'";
			mysql_query($query, $connection) or report_error (__FILE__, __LINE__, mysql_error ($connection));
			break;

			case"do_change":
			$connection = get_named_connection ("userdata");
			$query="UPDATE config_fax2email SET email = '$email' WHERE component_id = '$component_id'";
			mysql_query ($query, $connection) or report_error (__FILE__, __LINE__, mysql_error($connection));
			$query="UPDATE components SET status='active' WHERE component_id = '$component_id'";
			mysql_query ($query, $connection) or report_error (__FILE__, __LINE__, mysql_error ($connection));
			break;
		}
	}

	//////////////////////////////////////////////////////////////
	// Function:   configFax2EmailAddLog
	// Purpose:    Add an entry into the log table because of a 
	//              change in a fax number
	// Arguments:  $intComponentId
	//             $intFaxNumber
	//             $strSourceTag
	//             $strActionTag
	//
	// Returns:    $intFax2EmailNumberLogId - the insert Id
	/////////////////////////////////////////////////////////////
	function configFax2EmailAddLog($intComponentId,$intFax2EmailNumber,$strSourceTag,$strActionTag,$intFax2EmailUploadId = 0)
	{
		//Get the action Id
		$intFax2EmailLogActionId = configFax2EmailActionLogIdGet($strActionTag);

		//Get the source Id
		$intFax2EmailLogSourceId = configFax2EmailSourceLogIdGet($strSourceTag);

		//Get the component details
		$arrComponent = userdata_component_get($intComponentId);

		//Get the service Id
		$intServiceId = $arrComponent['service_id'];

		//Get a connectino to the db
		$dbhConn = get_named_connection_with_db('userdata');

		$strInsertQurery = "INSERT INTO tblFax2EmailNumberLog
		                            SET intFax2EmailNumber      = '$intFax2EmailNumber',
		                                intServiceId            = '$intServiceId',
		                                intFax2EmailLogActionId = '$intFax2EmailLogActionId',
		                                intFax2EmailLogSourceId = '$intFax2EmailLogSourceId',
		                                dtmDateLogged           = NOW()";

		//If the log was created by the reclamation script add the upload Id that caused the insert
		if($intFax2EmailUploadId != 0)
		{
			$strInsertQurery .= ",intFax2EmailUploadId = $intFax2EmailUploadId";
		}

		$resResult = PrimitivesQueryOrExit($strInsertQurery, $dbhConn);

		return $resResult;

	}//end of function: configFax2EmailAddLog()




	//////////////////////////k///////////////////////////////////
	// Function:  config_fax2email_modify
	// Purpose:   Updates email address in component	//           
	// Arguments: $component_id (id of component)
	// Returns:   
	/////////////////////////////////////////////////////////////

	function config_fax2email_update($fax2email_id, $email)
	{
		$connection = get_named_connection ("userdata");

		$fax2email_id = addslashes ($fax2email_id);

		mysql_query ("UPDATE config_fax2email SET email = '$email' WHERE fax2email_id = '$fax2email_id'", $connection)
				or report_error (__FILE__, __LINE__, mysql_error($connection));

		// $fax2email = mysql_fetch_array ($result, MYSQL_ASSOC);

		// return $fax2email;  
	}

	//////////////////////////k///////////////////////////////////
	// Function:  config_fax2email_get
	// Purpose:   Get a component and portal configuration
	//            record
	// Arguments: $component_id (id of component)
	// Returns:   Union of component and fax2email configuration
	/////////////////////////////////////////////////////////////

	function config_fax2email_get ($fax2email_id) {

		global $global_db_src;
                $connection = get_named_connection ("userdata");

		$fax2email_id = addslashes ($fax2email_id);

		$result = mysql_query ("SELECT * FROM config_fax2email
						 WHERE fax2email_id = '$fax2email_id'", $connection)
			or report_error (__FILE__, __LINE__, mysql_error($connection));

		$fax2email = mysql_fetch_array ($result, MYSQL_ASSOC);

		mysql_free_result ($result);

		return $fax2email;

	}

	///////////////////////////////////////////////////////////////////
	// Function: configFax2EmailActionLogIdGet
	// Purpose:  gets the Id for the action from a tag
	//           each log has an action type, eg assigned, removed
	// Arguments: $strSourceTag - the tag
	// Returns: $intFax2EmailLogActionId
	///////////////////////////////////////////////////////////////////
	function configFax2EmailActionLogIdGet($strActionTag)
	{

		//Get a connectino to the db
		$dbhConn = get_named_connection_with_db('userdata');

		//Select the source Id
		$strActionTag = addslashes($strActionTag);
		$strActionIdQuery = "SELECT intFax2EmailLogActionId 
	                              FROM vblFax2EmailLogAction
		                     WHERE vchFax2EmailLogActionTag = '$strActionTag'";

		$resResult = PrimitivesQueryOrExit($strActionIdQuery, $dbhConn);
		
		$intResult = PrimitivesResultGet($resResult, 'intFax2EmailLogActionId');

		return $intResult;

	}//end of functino: configFax2EmailActionLogIdGet()

	///////////////////////////////////////////////////////////////////
	// Function: configFax2EmailSourceLogIdGet
	// Purpose:  gets the Id for the source from a tag
	// Arguments: $strSourceTag - the tag
	// Returns: $intFax2EmailLogSourceId
	///////////////////////////////////////////////////////////////////
	function configFax2EmailSourceLogIdGet($strSourceTag)
	{

		//Get a connectino to the db
		$dbhConn = get_named_connection_with_db('userdata');

		//Select the source Id
		$strSourceTag = addslashes($strSourceTag);
		$strSourceIdQuery = "SELECT intFax2EmailLogSourceId 
	                              FROM vblFax2EmailLogSource 
		                     WHERE vchFax2EmailLogSourceTag = '$strSourceTag'";

		$resResult = PrimitivesQueryOrExit($strSourceIdQuery, $dbhConn);
		
		$intResult = PrimitivesResultGet($resResult, 'intFax2EmailLogSourceId');

		return $intResult;

	}//end of functino: configFax2EmailSourceLogIdGet()


	/////////////////////////////////////////////////////////////
	// Function:  config_fax2email_delete
	// Purpose:   Delete a fax2email configuration
	// Arguments: $intComponentId
	//            $strSourceTag - The source of the deletion
	//            $intFax2EmailUploadId - the ID of the reclamation script, if used
	/////////////////////////////////////////////////////////////

	function config_fax2email_delete ($intComponentId,$strSourceTag = 'portal', $intFax2EmailUploadId = 0)
	{


                $connection = get_named_connection ("userdata");

		// Release number
		$result = mysql_query ("SELECT fax_number
		                          FROM config_fax2email
		                         WHERE component_id  = '$intComponentId'", $connection)
			or report_error (__FILE__, __LINE__, mysql_error($connection));
		$fax_number = mysql_result ($result, 0, 0);
		mysql_free_result ($result);

		//Add the log
		configFax2EmailAddLog($intComponentId,$fax_number,$strSourceTag,'removed',$intFax2EmailUploadId);

		mysql_query ("UPDATE config_fax2email_availability
				SET available = 'yes', releasedate = NOW()
				WHERE fax_number = '$fax_number'", $connection)
			or report_error (__FILE__, __LINE__, mysql_error($connection));

		// Delete config
		mysql_query ("DELETE FROM config_fax2email
				     WHERE fax_number = '$fax_number'", $connection)
			or report_error (__FILE__, __LINE__, mysql_error($connection));

	}


	/////////////////////////////////////////////////////////////
	// Function:  config_fax2email_auto_configure
	// Purpose:   'unconfigured' -> 'queued-activate' state
	//            transition handler for auto-configuration
	// Arguments: $component_id
	/////////////////////////////////////////////////////////////

	function config_fax2email_auto_configure ($component_id) {

		// Do nothing - user must ask for a fax2email to be activated

// left for later instruction
//		// Create the config record
//		// FIXME
//		$fax2email_id = config_fax2email_add ($component_id, "user_email");
//
//		if ($fax2email_id != -1) {
//			// Attach the config record to the component
//			userdata_component_set_configuration ($component_id, $fax2email_id, '');
//
//			// Vivify the component
//			userdata_component_set_status ($component_id, 'active');
//		} else {
//			// FIXME
//			print "Horreur! No fax2emails left?";
//		}

	}


	/////////////////////////////////////////////////////////////
	// Function:  config_fax2email_auto_destroy
	// Purpose:   * -> 'queued-destroy' state
	//            transition handler for auto-destruction
	// Arguments: $component_id
	//            $strSourceTag - The source of the deletion
	//            $intFax2EmailUploadId - the ID of the reclamation script
	/////////////////////////////////////////////////////////////

	function config_fax2email_auto_destroy ($component_id, $strSourceTag = 'portal', $intFax2EmailUploadId = 0)
	{
		$component = userdata_component_get ($component_id);

		switch ($component["status"]) {
		    case "unconfigured":
		    case "queued-activate":
		    case "destroyed":
			// These states have no configuration
			break;

		    case "active":
		    case "deactive":
		    case "queued-deactivate":
		    case "queued-deconfigure":
		    case "queued-reconfigure":
		    case "queued-destroy":
			// These states have a configuration
			// Excise the config record and associated backend entry
			config_fax2email_delete ($component_id,$strSourceTag,$intFax2EmailUploadId);

		    default:
			break;
		}

		// Mortify the component
		userdata_component_set_status ($component_id, "destroyed");

	}


	function config_fax2email_configurator ($component_id, $action, $strSourceTag = 'portal', $intFax2EmailUploadId = 0) {

		switch ($action) {

		    case "auto_configure":
			config_fax2email_auto_configure ($component_id, $strSourceTag, $intFax2EmailUploadId);
			break;

		    case "auto_refresh":
			// FIXME
			// config_fax2email_auto_refresh ($component_id);
			break;

		    case "auto_disable":
			// FIXME
			// config_fax2email_auto_disable ($component_id);
			break;

		    case "auto_enable":
			// FIXME
			// config_fax2email_auto_enable ($component_id);
			break;

		    case "auto_destroy":
			config_fax2email_auto_destroy ($component_id, $strSourceTag, $intFax2EmailUploadId);
			break;

		    default:
			break;

		}

	}

	// Library functions
	/////////////////////////////////////////////////////////////////////


?>
