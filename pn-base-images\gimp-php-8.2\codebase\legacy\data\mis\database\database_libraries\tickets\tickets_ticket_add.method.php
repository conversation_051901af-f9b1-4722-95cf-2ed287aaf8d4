<?php


    /////////////////////////////////////////////////////////////////////
    // Name      : split_tickets_ticket_add
    // Purpose   : Creates a Question and a Question Contact
    // Arguments : $source         (Internal, Portal or Script)
    //             $service_id     (the service identifier
    //                              associated to the user)
    //             $error_class    (ID of the error class)
    //             $error_subclass (ID of the error subclass)
    //             $status         ('Open', 'Closed', 'Assigned')
    //             $actioner_id    (ID of the staff member creating the ticket,
    //                              0 if customer)
    //             $body           (Body text to be added to the created contact)
    // Returns :   $ticket_id (The Question indetifier)
    //////////////////////////////////////////////////////////////////////
    /**
     * Creates a Question and the first Question Contact
     *
     * A function to make appropriate database entries for Question creation,
     * the function creates two database entries. The first is an addition to the
     * Questions table in the Questions database this represents the head of the ticket.
     * The second is an entry into the ticket_contacts table in the Questions database,
     * this entry represents the initial text part of the ticket, it contains the body text.
     *
     * @param String [$source] The origin of the Question Internal, Portal or Script
     * @param int [$service_id] Tthe service identifier associated with the user, the PK for the user's username and password
     * @param int [$error_class] ID of the error class
     * @param int [$error_subclass] ID of the error subclass
     * @param String [$status] Question status 'Open', 'Closed', 'Assigned'
     * @param int [$actioner_id] ID of the staff member creating the ticket, 0 if customer
     * @param String [$body] Body text to be added to the created contact
     * @param int [$return_to] The service_id of the person this Question should be returned to defaults to 0
     * @param int [$team_id] The id of the customer services team that should deal with the ticket, should be -1 if Question is with the customer
     * @param int [$owner_id] the id of the workplace user that owns the ticket
     * @param string $strCauseHandle  Optional handle stating the cause of the ticket. Defaults to 'Other'.
     * @param int [$intQuestionTopicId] Optional qestiong topic id.
     * @return int The Question indetifier
     */
    function split_tickets_ticket_add($source, $service_id, $error_class, $error_subclass, $status, $actioner_id, $body, $return_to = '0', $team_id = 0, $owner_id = '0', $strCauseHandle = '', $strQuestionTopicId = '')
    {

        // CSC - Technical Support Team
        // Default internal team changed from 11 to 1 on 05/03/02, as part of fix to Problem 9701
        $stc_team=1;

        global $global_db_src, $default_team;

        $connection = get_named_connection('tickets');

        if ($team_id == 0)
        {
            if ($source == 'Internal')
            {
                $team_id = $stc_team;
            }
            else
            {
                $team_id = $default_team;
            }
        }

        $source     = addslashes($source);
        $service_id = addslashes($service_id);
        $body          = addslashes($body);

        // Evil hack to get rid of instances of { and } - this cocks up PHPLib templates
        $body = str_replace('{', '&#123;', $body);
        $body = str_replace('}', '&#125;', $body);

        $date_raised = date('Y-m-d H:i:s');

///////////////
//
//  Service Notice
//
        if($status == 'Closed' && $source == 'Script')
        {
            //This isn't a Question - it's a closed contact
            //Store it as a Service Notice

            $objServiceNotice = CServiceNotice::Create();

            $arrServiceNoticeType = CServiceNoticeType::Retrieve($source);

            $arrDetails = array(
                'intServiceNoticeTypeID'    => $arrServiceNoticeType[0]['intServiceNoticeTypeID'],
                'intServiceID'              => $service_id,
                'strBody'                   => $body,
                'strActionerID'             => $actioner_id,
                'strDateRaised'             => $date_raised
            );

            $objServiceNotice->SetAll($arrDetails);

            $objServiceNotice->Save();

            //Do not return the insert id as the calling function may treat this as a ticket_id but return positive result
            return true;
        }


        // Make sure the actioner_id we're inserting into tickets is valid, i.e:
        //  + empty
        //  + 0, or -1
        //  + A service id
        //  + A php_lib user id
        $actioner_id = trim($actioner_id);

        $bolRaiseActionerProblem = false;

        if (
             $actioner_id != '' && $actioner_id != '0' &&
             $actioner_id != '-1' && strlen($actioner_id) != 32  &&
             filter_var(ltrim($actioner_id, '0'), FILTER_VALIDATE_INT) === false
        ) {
                // If the actioner_id isn't valid, then set it to '0', so the
                // ticket can be raised, rather than breaking Workplace..
                $oldActionerId = $actioner_id;
                $bolRaiseActionerProblem = true;
                $actioner_id = 0;
        }

        $query = "INSERT INTO  tickets (
                               ticket_source, service_id,  owner_id,
                               actioner_id,   error_class, error_subclass,
                               team_id,       date_raised, status,
                               return_to)
                       VALUES ('$source',       '$service_id',  '$owner_id',
                               '$actioner_id',  '$error_class', '$error_subclass',
                               '$team_id', '$date_raised', '$status',
                               '$return_to')";

        $result = mysql_query($query, $connection)
            or report_error (__FILE__, __LINE__, mysql_error($connection));

        $ticket_id = mysql_insert_id ($connection);
        // If we've had to change the actioner_id above, then report it
        // (here as we need the ticket id)
        if ($bolRaiseActionerProblem) {

            require_once '/local/data/mis/common_library_functions/class_libraries/Logging/Logging.class.php';

            // Raise an auto-problem so the root-cause can be traced and
            // fixed.
            $autoProblemClient = BusTier_BusTier::getClient('autoproblem');


            // Get a business actor for the script user
            $scriptUser = SoapSession::getScriptUserDetails('PLUSNET');
            $scriptActor = Auth_BusinessActor::get($scriptUser['actorId']);

            // Get a stack trace, and tidy it up a little
            $trace = str_replace('>','',Logging::getCallStackAsString());

            $autoProblem = $autoProblemClient->prepareAutoProblem(
                'invalidActionerIdForTicket',
                array(
                        'serviceId'  => $service_id,
                        'actionerId' => $oldActionerId,
                        'ticketId'   => $ticket_id,
                        'trace'      => htmlentities($trace)
                ),
                $scriptActor
            );

            $autoProblemId = $autoProblem->raiseProblem();

        }

        tickets_ticket_contacts_add($ticket_id, $owner_id, $body, $error_class,
                                    $error_subclass, $team_id, $status, $actioner_id, $date_raised);

        /**
         * PnDesktop is on hold
         */
        /*
        if(-1 == $team_id)
        {
            tickets_sendDesktopClientCustomerActionNeededAlert($service_id, $ticket_id, TRUE);
        }
        */

        // add data about open Questions to summary table
        if(strtolower($status) != 'closed' && (defined('OPEN_TICKETS_SUMMARY_TABLE') && (OPEN_TICKETS_SUMMARY_TABLE == 'live')))
        {
            tickets_open_ticket_summary_add($ticket_id, 0, $source, $service_id, $error_class, $error_subclass, $status, 
                   $actioner_id, $return_to, $team_id, $owner_id, time());
        }

        // fix problem 36559 - dbargiel
        // Autoproblem will be raised when ticket doesn't have a corresponding entry in the
        // common_tickets.open_tickets table
        if (strtolower($status) != 'closed')
        {
            // Check if ticket exists in open_tickets
            $sqlQuery = 'SELECT SQL_NO_CACHE ticket_id FROM open_tickets WHERE ticket_id='.intval($ticket_id).' AND partner_id=0';
            $debugConnection = get_named_connection_with_db('common_tickets');
            $resResult       = PrimitivesQueryOrExit($sqlQuery, $debugConnection);
            if (count(PrimitivesResultsAsArrayGet($resResult))==0)
            {
                // It should not be 0 - raise autoproblem

                // Message for autoproblem
                $message  = 'Ticket '.$ticket_id.' doesn\'t have a corresponding entry in the';
                $message .= ' common_tickets.open_tickets table. This ticket will fail to appear';
                $message .= ' in ticket pushing'."\n\n";
                $message .= 'SID:'.$service_id.' | Partner ID:0. | Status:'.$status.' | ';
                $message .= 'Source:'.$source.' | Error class: '.$error_class.' | ';
                $message .= 'Error subclass:'.$error_subclass.' | Status '.$status.' | ';
                $message .= 'Actioner id:'.$actioner_id.' | Return to:'.$return_to;
                $message .= 'Team ID:'.$team_id.' | Owner ID:'.$owner_id;
                $message = htmlspecialchars(wordwrap($message));

                pt_raise_autoproblem('ticket_creation_not_completed',
                                     'Ticket not recorded into common_tickets.open_tickets table',
                                     $message,
                                     $message);

            }
        }
        // End of fix problem 36559


///////
//
// Question Class
//

//This is a catchall for all scripts or internally raised tickets.
//Those raised from the portal will set the class at the point they are raised, as there is no way to pass class id to split_tickets_ticket_add.

        if(strtolower($source) == 'script')
        {
            $intClassID = CClass::Retrieve('automated_tickets');

            $objTicketClass  = CTicketClass::Create();

            $objTicketClass->SetAll(array('intClassID' => $intClassID,
                                          'intTicketID'=> $ticket_id));

            $objTicketClass->Save();
        }
        elseif(strtolower($source) == 'internal')
        {
            if($status == 'Closed')
            {
                //Call record
                $intClassID = CClass::Retrieve('call_record');

                $objTicketClass  = CTicketClass::Create();

                $objTicketClass->SetAll(array('intClassID' => $intClassID,
                                  'intTicketID'=> $ticket_id));

                $objTicketClass->Save();
            }
            else
            {

                $intClassID = CClass::Retrieve('internally_raised_tickets');

                $objTicketClass  = CTicketClass::Create();

                $objTicketClass->SetAll(array('intClassID' => $intClassID,
                                  'intTicketID'=> $ticket_id));

                $objTicketClass->Save();
            }
        }

//
//
//
//////////////



        // add data about Question to metrics table
        if(defined('TICKET_METRICS_TABLE')==true && TICKET_METRICS_TABLE == 'live')
        {
            TicketsUpdateMetrics($ticket_id, 0, $status, $team_id, date('Y-m-d h:i:s'), $actioner_id );
        }

        //NADS 747: Alert the 'BOT - Simultaneous Provides' team whenever a Question is raised to their pool.
        //WARINING!!! The ID of the BOT - Simultaneous Provides' team is HARD CODEDED!!!
        //71 is the team id of 'TEAM BOT': SELECT * FROM php_lib.group_details where group_id = 71
        //112 is the team id of 'BOT - Simultaneous Provides'.  You're confused... WTF about me??!?
        if ($team_id == 112)
        {
            global $whoami;
            $strAlertDescription = 'A TICKET HAS BEEN PLACED IN TO THE SIMULTANIOUS PROVIDE POOL';
            $arrTmp = phplib_user_get_from_group(71);
            $arrSimulTeamMembers = array();
            foreach ($arrTmp as $arrDbRow)
            {
                foreach ($arrDbRow as $strFieldName => $strFieldVal) {
                    if ($strFieldName == 'user_id') {
                        array_push($arrSimulTeamMembers, $strFieldVal);
                    }
                }
            }

            if(function_exists('alert_alert_add')){

                $intAlertID = alert_alert_add(1,
                                'now()',
                                $strAlertDescription,
                                'http://workplace.plus.net/tickets/my_tickets_push.html',
                                $whoami,
                                $arrSimulTeamMembers,
                                array(),
                                'no',
                                'Simultanious provide Question raised');
            }

            mail('<EMAIL>', 'A TICKET HAS BEEN PLACED IN TO THE SIMULTANIOUS PROVIDE POOL', "ID: $ticket_id");
        }

        if (isset($strQuestionTopicId) && ($strQuestionTopicId != ''))
        {
            CTicketTopic::setTicketTopic($ticket_id, $strQuestionTopicId);
        }

        return $ticket_id;
    }
