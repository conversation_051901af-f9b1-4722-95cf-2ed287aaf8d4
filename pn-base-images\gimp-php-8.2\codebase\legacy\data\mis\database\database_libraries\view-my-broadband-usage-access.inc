<?php
/**
 * view-my-broadband-usage-access.inc
 *
 * Access Library for View My Broadband Usage
 *
 * @package    LegacyCodebase
 * @subPackage Database Libraries
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2012 Plusnet
 */

require_once '/local/data/mis/database/application_apis/Cbc/PaygBwMaintenance.class.php';

// Whether to show a holding page on VMBU tool
define('HOLDINGPAGE', false);

define('FILTER_PEAK',    0);
define('FILTER_OFFPEAK', 1);

$arrPAYGPeakHours = array(8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23);

/**
 * Vmbu Quick Table Exists
 *
 * @param unknown $dbhConnection       Dbh Connection
 * @param string  $strTableNameSummary Table Name Summary
 *
 * @return unknown
 */
function vmbuQuickTableExists($dbhConnection, $strTableNameSummary)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->vmbuQuickTableExists($dbhConnection, $strTableNameSummary);
}

/**
 * Get Control Groups With Number Of Filter Times
 *
 * @return unknown
 */
function getControlGroupsWithNumberOfFilterTimes()
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->getControlGroupsWithNumberOfFilterTimes();
}

/**
 * Get All Control Group IDs With Filter Times
 *
 * @return unknown
 */
function getAllControlGroupIDsWithFilterTimes()
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->getAllControlGroupIDsWithFilterTimes();
}

/**
 * Get Control Group Name
 *
 * @param int $intGroupID Group ID
 *
 * @return unknown
 */
function getControlGroupName($intGroupID)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->getControlGroupName($intGroupID);
}

/**
 * Get Control Group Filter Times
 *
 * @param int $intGroupID Group ID
 *
 * @return unknown
 */
function getControlGroupFilterTimes($intGroupID)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->getControlGroupFilterTimes($intGroupID);
}

/**
 * Update All Control Group Filter Times
 *
 * @param array $arrFilterTimes Filter Times
 * @param array $arrDates       Dates
 *
 * @return unknown
 */
function updateAllControlGroupFilterTimes($arrFilterTimes, $arrDates)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->updateAllControlGroupFilterTimes($arrFilterTimes, $arrDates);
}

/**
 * Update Control Group Filter Times
 *
 * @param int   $intGroupID     Group ID
 * @param array $arrFilterTimes Filter Times
 * @param array $arrDates       Dates
 *
 * @return unknown
 */
function updateControlGroupFilterTimes($intGroupID, $arrFilterTimes, $arrDates)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->updateControlGroupFilterTimes($intGroupID, $arrFilterTimes, $arrDates);
}

/**
 * Has View My Broadband Usage
 *
 * @param int $intServiceID Service ID
 *
 * @return unknown
 */
function HasViewMyBroadbandUsage($intServiceID)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->HasViewMyBroadbandUsage($intServiceID);
}

/**
 * Get All ADSLGroups
 *
 * @return unknown
 */
function GetAllADSLGroups()
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->GetAllADSLGroups();
}

/**
 * Get CBCRadius ID
 *
 * @param string $strUsername Username
 * @param string $strISP      ISP
 *
 * @return unknown
 */
function GetCBCRadiusID($strUsername, $strISP)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->GetCBCRadiusID($strUsername, $strISP);
}

/**
 * Get Total Broadband Usage
 *
 * @param int $intServiceID Service ID
 * @param int $intGroupID   Group ID
 *
 * @return unknown
 */
function GetTotalBroadbandUsage($intServiceID, $intGroupID)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->GetTotalBroadbandUsage($intServiceID, $intGroupID);
}

/**
 * Get Total Bandwidth Used By Type
 *
 * @param int    $intServiceID          Service ID
 * @param string $strADSLDialupGroupIDs ADSLDialup Group IDs
 *
 * @return unknown
 */
function getTotalBandwidthUsedByType($intServiceID, $strADSLDialupGroupIDs)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->getTotalBandwidthUsedByType($intServiceID, $strADSLDialupGroupIDs);
}

/**
 * Get ADays Chargeable Bytes
 *
 * @param string $strUsername           Username
 * @param string $strISP                ISP
 * @param int    $uxtDate               Date
 * @param string $strADSLDialupGroupIDs ADSLDialup Group IDs
 *
 * @return unknown
 */
function getADaysChargeableBytes($strUsername, $strISP, $uxtDate, $strADSLDialupGroupIDs)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->getADaysChargeableBytes($strUsername, $strISP, $uxtDate, $strADSLDialupGroupIDs);
}

/**
 * Get Total Broadband Usage Using Username ISP
 *
 * @param string $strUsernameISP Username ISP
 * @param int    $intGroupID     Group ID
 *
 * @return unknown
 */
function GetTotalBroadbandUsageUsingUsernameISP($strUsernameISP, $intGroupID)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->GetTotalBroadbandUsageUsingUsernameISP($strUsernameISP, $intGroupID);
}

/**
 * Work Out Usage Bar Break Down
 *
 * @param int $intTotalBandwidthForBar Total Bandwidth For Bar
 * @param int $intBasePointToStart     Base Point To Start
 *
 * @return unknown
 */
function WorkOutUsageBarBreakDown($intTotalBandwidthForBar, $intBasePointToStart = 0)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->WorkOutUsageBarBreakDown($intTotalBandwidthForBar, $intBasePointToStart);
}

/**
 * Vmbu Get Usage Bar Limit
 *
 * @param int $intTotalBytes Total Bytes
 * @param int $intAccuracy   Accuracy
 *
 * @return unknown
 */
function vmbuGetUsageBarLimit($intTotalBytes, $intAccuracy = 1)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->vmbuGetUsageBarLimit($intTotalBytes, $intAccuracy);
}

/**
 * Work Out Rounded Free Gigabyte
 *
 * @param int $intIncludedBandwidthBytes  Included Bandwidth Bytes
 * @param int $intExtraBandwidthProvided  Extra Bandwidth Provided
 * @param int $intTotalBandwidthUsedBytes Total Bandwidth Used Bytes
 *
 * @return unknown
 */
function WorkOutRoundedFreeGigabyte($intIncludedBandwidthBytes, $intExtraBandwidthProvided, $intTotalBandwidthUsedBytes)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->WorkOutRoundedFreeGigabyte($intIncludedBandwidthBytes, $intExtraBandwidthProvided, $intTotalBandwidthUsedBytes);
}

/**
 * Work Out Percentages For Monthly Graph
 *
 * @param int $intIncludedBandwidthProvided Included Bandwidth Provided
 * @param int $intExtraBandwidthProvided    Extra Bandwidth Provided
 * @param int $intTotalBandwidthUsed        Total Bandwidth Used
 * @param int $intBarMax                    Bar Max
 *
 * @return unknown
 */
function WorkOutPercentagesForMonthlyGraph($intIncludedBandwidthProvided, $intExtraBandwidthProvided, $intTotalBandwidthUsed , $intBarMax = 0)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->WorkOutPercentagesForMonthlyGraph($intIncludedBandwidthProvided, $intExtraBandwidthProvided, $intTotalBandwidthUsed, $intBarMax);
}

/**
 * Work Out Todays Usage
 *
 * @param int    $intRadiusID   Radius ID
 * @param string $strISP        ISP
 * @param bool   $bolGroupIndex Group Index
 *
 * @return unknown
 */
function WorkOutTodaysUsage($intRadiusID, $strISP, $bolGroupIndex=false )
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->WorkOutTodaysUsage($intRadiusID, $strISP, $bolGroupIndex);
}

/**
 * Work Out Todays Usage Billing
 *
 * @param int    $intRadiusID        Radius ID
 * @param string $strISP             ISP
 * @param bool   $bolGroupIndex      Group Index
 * @param int    $uxtCalculationDate Calculation Date
 *
 * @return unknown
 */
function WorkOutTodaysUsageBilling($intRadiusID, $strISP, $bolGroupIndex=false, $uxtCalculationDate=false )
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->WorkOutTodaysUsageBilling($intRadiusID, $strISP, $bolGroupIndex, $uxtCalculationDate);
}

/**
 * Work Out Todays Usage For Graph
 *
 * @param int    $intServiceID Service ID
 * @param string $strGroupID   Group ID
 *
 * @return unknown
 */
function WorkOutTodaysUsageForGraph($intServiceID, $strGroupID)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->WorkOutTodaysUsageForGraph($intServiceID, $strGroupID);
}

/**
 * Work Out Daily Usage
 *
 * @param int    $intServiceID Service ID
 * @param string $strISP       ISP
 * @param string $strGroupID   Group ID
 *
 * @return unknown
 */
function WorkOutDailyUsage($intServiceID, $strISP, $strGroupID)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->WorkOutDailyUsage($intServiceID, $strISP, $strGroupID);
}

/**
 * Work Out Usage For Few Hours Of ADay
 *
 * @param int    $intServiceID      Service ID
 * @param string $strISP            ISP
 * @param int    $uxtStartTimeStamp Start Time Stamp
 * @param int    $uxtEndTimeStamp   End Time Stamp
 * @param string $strGroupID        Group ID
 *
 * @return unknown
 */
function WorkOutUsageForFewHoursOfADay($intServiceID, $strISP, $uxtStartTimeStamp, $uxtEndTimeStamp, $strGroupID='')
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->WorkOutUsageForFewHoursOfADay($intServiceID, $strISP, $uxtStartTimeStamp, $uxtEndTimeStamp, $strGroupID);
}

/**
 * Work Out Daily Usage For Billing Period
 *
 * @param int    $intServiceID Service ID
 * @param string $strISP       ISP
 * @param int    $uxtStartDate Start Date
 * @param int    $uxtEndDate   End Date
 * @param string $strGroupID   Group ID
 *
 * @return unknown
 */
function WorkOutDailyUsageForBillingPeriod($intServiceID, $strISP, $uxtStartDate, $uxtEndDate, $strGroupID)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->WorkOutDailyUsageForBillingPeriod($intServiceID, $strISP, $uxtStartDate, $uxtEndDate, $strGroupID);
}

/**
 * Work Out Distinct In And Out Daily Usage
 *
 * @param int    $intServiceID Service ID
 * @param string $strISP       ISP
 * @param int    $uxtStartDate Start Date
 * @param int    $uxtEndDate   End Date
 * @param string $strGroupID   Group ID
 *
 * @return unknown
 */
function WorkOutDistinctInAndOutDailyUsage($intServiceID, $strISP, $uxtStartDate, $uxtEndDate, $strGroupID='')
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->WorkOutDistinctInAndOutDailyUsage($intServiceID, $strISP, $uxtStartDate, $uxtEndDate, $strGroupID);
}

/**
 * Get VMBUService Period History
 *
 * @param int   $intServiceID Service ID
 * @param int   $intLimit     Limit
 * @param array $arrGroupIDs  Group IDs
 *
 * @return unknown
 */
function GetVMBUServicePeriodHistory($intServiceID, $intLimit=false, $arrGroupIDs=array())
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->GetVMBUServicePeriodHistory($intServiceID, $intLimit, $arrGroupIDs);
}

/**
 * Get VMBUService Period Start
 *
 * @param int  $intServiceID Service ID
 * @param bool $bolUnixTimes Unix Timestamp; if false, the customer's invoice date will be used
 *
 * @return unknown
 */
function GetVMBUServicePeriodStart($intServiceID, $bolUnixTimes = false)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->GetVMBUServicePeriodStart($intServiceID, $bolUnixTimes);
}

/**
 * Get VMBUDates To Use
 *
 * @param int    $intServiceID     Service ID
 * @param string $strInvoicePeriod Invoice Period
 *
 * @return unknown
 */
function GetVMBUDatesToUse($intServiceID, $strInvoicePeriod)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->GetVMBUDatesToUse($intServiceID, $strInvoicePeriod);
}

/**
 * Vmbu Service Period Get
 *
 * @param int $intServiceID Service ID
 *
 * @return unknown
 */
function vmbuServicePeriodGet($intServiceID)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->vmbuServicePeriodGet($intServiceID);
}

/**
 * Vmbu Get CBCBilling Period Type
 *
 * @param int $intServiceID Service ID
 *
 * @return unknown
 */
function vmbuGetCBCBillingPeriodType($intServiceID)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->vmbuGetCBCBillingPeriodType($intServiceID);
}

/**
 * Get Start End Date
 *
 * @param unknown $dtmStart      Dtm Start
 * @param unknown $dtmEnd        Dtm End
 * @param unknown $dtmTodaysDate Dtm Todays Date
 *
 * @return unknown
 */
function GetStartEndDate($dtmStart, $dtmEnd, $dtmTodaysDate)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->GetStartEndDate($dtmStart, $dtmEnd, $dtmTodaysDate);
}

/**
 * Get Billing Day Details
 *
 * @param int    $intServiceID           Service ID
 * @param string $strAdslGroupsFormatted Adsl Groups Formatted
 *
 * @return unknown
 */
function GetBillingDayDetails($intServiceID, $strAdslGroupsFormatted)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->GetBillingDayDetails($intServiceID, $strAdslGroupsFormatted);
}

/**
 * Get Rounded Bandwidth
 *
 * @param int $intExtraBandwidthProvided Extra Bandwidth Provided
 *
 * @return unknown
 */
function GetRoundedBandwidth($intExtraBandwidthProvided)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->GetRoundedBandwidth($intExtraBandwidthProvided);
}

/**
 * VMBUWork Out Peak Bandwidth Usage Over Date Range
 *
 * @param int    $intServiceID           Service ID
 * @param string $strISP                 ISP
 * @param int    $uxtStartDate           Start Date
 * @param int    $uxtEndDate             End Date
 * @param string $strAdslGroupsFormatted Adsl Groups Formatted
 *
 * @return unknown
 */
function VMBUWorkOutPeakBandwidthUsageOverDateRange($intServiceID, $strISP, $uxtStartDate, $uxtEndDate, $strAdslGroupsFormatted)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->VMBUWorkOutPeakBandwidthUsageOverDateRange($intServiceID, $strISP, $uxtStartDate, $uxtEndDate, $strAdslGroupsFormatted);
}

/**
 * VMBUWork Out Peak Bandwidth Usage Over Date Range Per Group ID
 *
 * @param int    $intServiceID Service ID
 * @param string $strISP       ISP
 * @param int    $uxtStartDate Start Date
 * @param int    $uxtEndDate   End Date
 * @param string $strGroupID   Group ID
 *
 * @return unknown
 */
function VMBUWorkOutPeakBandwidthUsageOverDateRangePerGroupID($intServiceID, $strISP, $uxtStartDate, $uxtEndDate, $strGroupID)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->VMBUWorkOutPeakBandwidthUsageOverDateRangePerGroupID($intServiceID, $strISP, $uxtStartDate, $uxtEndDate, $strGroupID);
}

/**
 * VMBUDoes User Have Peak Usage Viewing Entitlement
 *
 * @param int $intServiceID Service ID
 *
 * @return unknown
 */
function VMBUDoesUserHavePeakUsageViewingEntitlement($intServiceID)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->VMBUDoesUserHavePeakUsageViewingEntitlement($intServiceID);
}

/**
 * Reset Bandwidth Usage
 *
 * @param int  $intServiceID       Service ID
 * @param bool $bolFromBilling     From Billing
 * @param int  $uxtCalculationDate Calculation Date
 *
 * @return unknown
 */
function ResetBandwidthUsage($intServiceID, $bolFromBilling=false, $uxtCalculationDate=false)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->ResetBandwidthUsage($intServiceID, $bolFromBilling, $uxtCalculationDate);
}

/**
 * Get Current VMBUService Period ID
 *
 * @param int $intServiceID Service ID
 *
 * @return unknown
 */
function GetCurrentVMBUServicePeriodID($intServiceID)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->GetCurrentVMBUServicePeriodID($intServiceID);
}

/**
 * Insert VMBUService Period
 *
 * @param int  $intServiceID     Service ID
 * @param bool $bolInitialRecord Initial Record
 *
 * @return unknown
 */
function InsertVMBUServicePeriod($intServiceID, $bolInitialRecord=false)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->InsertVMBUServicePeriod($intServiceID, $bolInitialRecord);
}

/**
 * Insert VMBUService Period Complete
 *
 * @param int     $intServiceID Service ID
 * @param unknown $dtmStart     Dtm Start
 * @param unknown $dtmEnd       Dtm End
 * @param string  $strDBSrc     DBSrc
 *
 * @return unknown
 */
function InsertVMBUServicePeriodComplete($intServiceID, $dtmStart, $dtmEnd=null, $strDBSrc='')
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->InsertVMBUServicePeriodComplete($intServiceID, $dtmStart, $dtmEnd, $strDBSrc);
}

/**
 * Update VMBUService Period
 *
 * @param int    $intVMBUServicePeriodID VMBUService Period ID
 * @param int    $intRadiusID            Radius ID
 * @param string $strISP                 ISP
 * @param bool   $bolFromBilling         From Billing
 *
 * @return unknown
 */
function UpdateVMBUServicePeriod($intVMBUServicePeriodID, $intRadiusID, $strISP, $bolFromBilling=false)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->UpdateVMBUServicePeriod($intVMBUServicePeriodID, $intRadiusID, $strISP, $bolFromBilling);
}

/**
 * End VMBUService Period
 *
 * @param int $intVMBUServicePeriodID VMBUService Period ID
 *
 * @return unknown
 */
function EndVMBUServicePeriod($intVMBUServicePeriodID)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->EndVMBUServicePeriod($intVMBUServicePeriodID);
}

/**
 * Get Last Invoice Date
 *
 * @param int $intServiceID Service ID
 *
 * @return unknown
 */
function GetLastInvoiceDate($intServiceID)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->GetLastInvoiceDate($intServiceID);
}

/**
 * Sort Get Current CBCFlex Service Log ID
 *
 * @param int $intServiceID Service ID
 *
 * @return unknown
 */
function SortGetCurrentCBCFlexServiceLogID($intServiceID)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->SortGetCurrentCBCFlexServiceLogID($intServiceID);
}

/**
 * Get CBCv ISPTable Name
 *
 * @param string $strISP ISP
 *
 * @return unknown
 */
function GetCBCvISPTableName($strISP)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->GetCBCvISPTableName($strISP);
}

/**
 * Get Days Total Data Transfer
 *
 * @param string  $strUsername            Username
 * @param string  $strISP                 ISP
 * @param unknown $dtmDay                 Dtm Day
 * @param string  $strAdslGroupsFormatted Adsl Groups Formatted
 *
 * @return unknown
 */
function GetDaysTotalDataTransfer($strUsername, $strISP, $dtmDay, $strAdslGroupsFormatted)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->GetDaysTotalDataTransfer($strUsername, $strISP, $dtmDay, $strAdslGroupsFormatted);
}

/**
 * Get Group Data Transfer Between Dates
 *
 * @param int     $intRadiusID Radius ID
 * @param string  $strISP      ISP
 * @param unknown $dtmStart    Dtm Start
 * @param unknown $dtmEnd      Dtm End
 *
 * @return unknown
 */
function GetGroupDataTransferBetweenDates($intRadiusID, $strISP, $dtmStart, $dtmEnd)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->GetGroupDataTransferBetweenDates($intRadiusID, $strISP, $dtmStart, $dtmEnd);
}

/**
 * Insert VMBUService Period Group
 *
 * @param int    $intVMBUServicePeriodID VMBUService Period ID
 * @param int    $intGroupID             Group ID
 * @param int    $intDataTransfer        Data Transfer
 * @param string $strDBSrc               DBSrc
 *
 * @return unknown
 */
function InsertVMBUServicePeriodGroup($intVMBUServicePeriodID, $intGroupID, $intDataTransfer, $strDBSrc='')
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->InsertVMBUServicePeriodGroup($intVMBUServicePeriodID, $intGroupID, $intDataTransfer, $strDBSrc);
}

/**
 * Update Service Period Usage
 *
 * @param int     $intRadiusID            Radius ID
 * @param string  $strISP                 ISP
 * @param int     $intVMBUServicePeriodID VMBUService Period ID
 * @param unknown $dtmStart               Dtm Start
 * @param unknown $dtmEnd                 Dtm End
 *
 * @return unknown
 */
function UpdateServicePeriodUsage($intRadiusID, $strISP, $intVMBUServicePeriodID, $dtmStart, $dtmEnd)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->UpdateServicePeriodUsage($intRadiusID, $strISP, $intVMBUServicePeriodID, $dtmStart, $dtmEnd);
}

/**
 * Delete All VMBUService Period Groups For Service Period ID
 *
 * @param int $intVMBUServicePeriodID VMBUService Period ID
 *
 * @return unknown
 */
function DeleteAllVMBUServicePeriodGroupsForServicePeriodID($intVMBUServicePeriodID)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->DeleteAllVMBUServicePeriodGroupsForServicePeriodID($intVMBUServicePeriodID);
}

/**
 * Delete All VMBURecords For AService ID
 *
 * @param int $intServiceID Service ID
 *
 * @return unknown
 */
function DeleteAllVMBURecordsForAServiceID($intServiceID)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->DeleteAllVMBURecordsForAServiceID($intServiceID);
}

/**
 * Insert VMBUBilling Reset Log
 *
 * @param int $intServiceID Service ID
 * @param int $uxtBilling   Billing
 *
 * @return unknown
 */
function insertVMBUBillingResetLog($intServiceID, $uxtBilling)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->insertVMBUBillingResetLog($intServiceID, $uxtBilling);
}

/**
 * Get VMBUBilling Reset Log ID
 *
 * @param int $intServiceID Service ID
 *
 * @return unknown
 */
function getVMBUBillingResetLogID($intServiceID)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->getVMBUBillingResetLogID($intServiceID);
}

/**
 * Mark VMBUBilling Reset Log As Reset
 *
 * @param int $intVMBUBillingResetLogID VMBUBilling Reset Log ID
 *
 * @return unknown
 */
function markVMBUBillingResetLogAsReset($intVMBUBillingResetLogID)
{

    $objViewMyBroadbandUsage = Lib_ViewMyBroadbandUsage::singleton();
    return $objViewMyBroadbandUsage->markVMBUBillingResetLogAsReset($intVMBUBillingResetLogID);
}

require_once '/local/data/mis/database/database_libraries/Util_LibrarySplitter.class.php';
require_once '/local/data/mis/database/database_libraries/viewmybroadbandusage/ViewMyBroadbandUsage.class.php';
