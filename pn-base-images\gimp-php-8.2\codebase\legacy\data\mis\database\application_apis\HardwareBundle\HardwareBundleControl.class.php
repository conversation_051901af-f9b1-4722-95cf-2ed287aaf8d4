<?php
/**
 * HardwareBundleControl.class.php
 *
 * @package application_apis
 * @subpackage HardwareBundle
 */
/**
 * HardwareBundleControl Class
 *
 * Used to handle hardware bundle issues before we know specifically what
 * supplier classes we need to be using. This class also deals with getting
 * updates on orders and providing reporting info.
 *
 * @package application_apis
 * @subpackage HardwareBundle
 */
class HardwareBundleControl extends HardwareBundleBase
{
    var $m_arrStatuses = array();

    // This is used to store an array of hardware bundles that the class is
    // working on
    var $m_arrHardwareBundles = array();

    // this is used to store a list of items to be updated when they are RMA's.
    // Don't want all item status's to be updated when individual items are RMA's
    var $m_arrItems = array();

    /**
     * constructor
     *
     * @return void
     */
    function HardwareBundleControl()
    {
        // Call base constructor
        $this->HardwareBundleBase();
    }

    /**
     * Reset synamic content, pass true to delete cached data also
     * (including statuses etc)
     *
     * @param boolean $binClearAll ClearAll
     *
     * @return void
     */
    function Reset($binClearAll)
    {
        $this->m_arrHardwareBundles = array();

        // Do we need to clear everything?
        if ($binClearAll == true) {
            $this->m_arrStatusIDs = array();
        }
    }

    /**
     * GetStatus
     *
     * @param string $strStatusTag StatusTag
     *
     * @return boolean|multitype:
     */
    function GetStatus($strStatusTag)
    {
        if (!isset($this->m_arrStatuses[$strStatusTag])) {
            // Run a query to get the info since we don't already have it
            // stored.
            $hConnection = get_named_connection('product_reporting');

            $strQuery = 'SELECT hos.hardware_order_status_id AS intStatus, ' .
                              ' hos.display_name             AS strDisplayName, ' .
                              ' hos.description              AS strDescription, ' .
                              ' hos.portal_description       AS strPortalDescription ' .
                          'FROM hardware_order_statuses AS hos ' .
                         "WHERE hos.tag='$strStatusTag'";

            $hResult = mysql_query($strQuery, $hConnection)
                or report_error(__FILE__, __LINE__, mysql_error($hConnection));

            if (mysql_num_rows($hResult) == 0) {
                // We couldn't find the status in the database, so
                // something is wrong...
                $PHP_SELF = $_SERVER['PHP_SELF'];
                error_log(
                    "HardwareBundleControl::GetStatus - '$strStatusTag' doesn't ".
                    "exist in the database! PHP_SELF is '$PHP_SELF'"
                );

                return false;
            }

            $this->m_arrStatuses[$strStatusTag] = mysql_fetch_assoc($hResult);
        }

        return $this->m_arrStatuses[$strStatusTag];
    }

    /**
     * Find a service ID for a given order number
     *
     * @param string $strOrderNumber OrderNumber
     *
     * @return boolean
     */
    function GetServiceIDByOrderNumber($strOrderNumber)
    {
        $hConnection = get_named_connection('userdata');

        $strQuery = 'SELECT DISTINCT service_id AS intServiceID ' .
                               'FROM components              AS c, ' .
                                   ' config_hardware_bundles AS chb, ' .
                                   ' hardware_bundle_items   AS hbi ' .
                              'WHERE c.component_id=chb.component_id ' .
                                'AND chb.config_hardware_bundle_id=hbi.config_hardware_bundle_id ' .
                                "AND hbi.order_number='$strOrderNumber'";

        $hResult = mysql_query($strQuery, $hConnection)
            or report_error(__FILE__, __LINE__, mysql_error($hConnection));

        if (mysql_num_rows($hResult) == 0) {
            $intRetVal = false;
        } else {
            $intRetVal = mysql_result($hResult, 0, 0);
        }

        mysql_free_result($hResult);

        return $intRetVal;
    }

    /**
     * Find a service ID for a given order number
     *
     * @param int $intBundleId BundleId
     *
     * @return boolean
     */
    function GetServiceIDByBundleID($intBundleId)
    {
        $hConnection = get_named_connection('userdata');

        $intBundleId = addslashes($intBundleId);

        $strQuery = 'SELECT DISTINCT service_id AS intServiceID ' .
                               'FROM components              AS c, ' .
                                   ' config_hardware_bundles AS chb ' .
                              'WHERE c.component_id=chb.component_id ' .
                                "AND chb.config_hardware_bundle_id='$intBundleId'";

        $hResult = mysql_query($strQuery, $hConnection)
            or report_error(__FILE__, __LINE__, mysql_error($hConnection));

        if (mysql_num_rows($hResult) == 0) {
            $intRetVal = false;
        } else {
            $intRetVal = mysql_result($hResult, 0, 0);
        }

        mysql_free_result($hResult);

        return $intRetVal;

    } // function GetServiceIDByBundleID

    /**
     * GetStatusID
     * Return the ID of a status tag.
     *
     * @param string $strStatusTag StatusTag
     *
     * @return Ambigous <>
     */
    function GetStatusID($strStatusTag)
    {
        $rStatus = HardwareBundleControl::GetStatus($strStatusTag);

        return $rStatus['intStatus'];
    }

    /**
     * Return an array of HardwareBundleSupplier objects: one for each
     * supplier of the correct type.
     * *** STATIC ***
     *
     * @return multitype:unknown
     */
    function GetAllSupplierObjects()
    {
        $hConnection = get_named_connection('product_reporting');

        $strQuery = 'SELECT php_class_prefix AS strClassPrefix ' .
                      'FROM hardware_suppliers';

        $hResult = mysql_query($strQuery, $hConnection)
            or report_error(__FILE__, __LINE__, mysql_error($hConnection));

        $arrSuppliers = array();

        while ($rRow = mysql_fetch_assoc($hResult)) {
            // HACK - disabling certain suppliers
            if (in_array($rRow['strClassPrefix'], array('InsightUK'))) {
                continue;
            }

            $strClassName = $rRow['strClassPrefix'] . 'HardwareBundleSupplier';

            if (class_exists($strClassName) == true) {
                $arrSuppliers[] = new $strClassName();
            }
        }

        return $arrSuppliers;
    }

    /**
     * Get a hardware bundle object for the passed Service ID
     *
     * @param int $intServiceID ServiceID
     *
     * @return Ambigous <boolean, unknown>
     */
    public static function GetHardwareBundleByServiceID($intServiceID)
    {
        $hConnection = get_named_connection('product');

        // First get the class prefix.
        $strQuery = 'SELECT DISTINCT php_class_prefix AS strClassPrefix, ' .
                          ' c.component_id            AS intComponentID, ' .
                          ' c.status                  AS strComponentStatus, ' .
                          ' hos.tag                   AS strOrderStatusHandle ' .
                      'FROM hardware_suppliers               AS hs, ' .
                          ' hardware_supplier_products       AS hsp, ' .
                          ' hardware_bundle_config_items     AS hbci, ' .
                          ' component_hardware_bundle_config AS chbc, ' .
                          ' service_components               AS sc, ' .
                          ' userdata.components              AS c, ' .
                          ' userdata.config_hardware_bundles AS chb, ' .
                          ' hardware_order_statuses          AS hos ' .
                     'WHERE hs.hardware_supplier_id=hsp.hardware_supplier_id ' .
                       'AND hsp.hardware_supplier_product_id=hbci.hardware_supplier_product_id ' .
                       'AND hbci.component_hardware_bundle_config_id=chbc.component_hardware_bundle_config_id ' .
                       'AND chbc.service_component_id=sc.service_component_id ' .
                       'AND sc.service_component_id=c.component_type_id ' .
                       'AND c.component_id=chb.component_id ' .
                       'AND chb.overall_hardware_order_status_id=hos.hardware_order_status_id ' .
                       'AND c.status IN ("active", "queued-activate", "queued-reactivate", "unconfigured") ' .
                       "AND c.service_id='$intServiceID'";

        $hResult = mysql_query($strQuery, $hConnection)
            or report_error(__FILE__, __LINE__, mysql_error($hConnection));

        if (mysql_num_rows($hResult) == 0) {
            $unkRetVal = false;
        } else {
            // We have more than one component. Run through them all and
            // find the one with the highest precedence
            // Order of precedence is as follows:
            // Order  Component Status   Hardware Status
            // 1      active             rma_in_progress
            // 2.     queued-activate    Anything other than dispatched, cancelled, rejected, rma_in_progress
            // 3.     active             dispatched

            // Arbitrary high number
            $intBestFoundPrecedence = 99999;
            $recBundleToUse         = false;

            while ($recRow = mysql_fetch_assoc($hResult)) {
                // Work out what precedence this is
                $intPrecedence = 99999;

                switch ($recRow['strComponentStatus']) {
                    case 'active':
                        switch ($recRow['strOrderStatusHandle']) {
                            case 'rma_in_progress':
                                $intPrecedence = 1;

                            case 'dispatched':
                                $intPrecedence = 3;
                        }

                        break;

                    case 'queued-activate':
                        switch ($recRow['strOrderStatusHandle']) {
                            case 'dispatched':
                            case 'cancelled':
                            case 'rejected':
                            case 'rma_in_progress':
                                $intPrecedence = 99999;

                            case 'dispatched':
                                $intPrecedence = 2;
                        }
                        break;
                }

                // Is this better than the current best?
                if ($intPrecedence <= $intBestFoundPrecedence) {
                    $intBestFoundPrecedence = $intPrecedence;

                    $recBundleToUse = $recRow;
                }
            }

            // Have we found one to use?
            if ($recBundleToUse === false) {
                error_log(
                    __FILE__ . ':' . __LINE__ .
                    " Service '$intServiceID' has more than one hardware bundle component. ".
                    "Can't figure out which to use so returning false."
                );

                $unkRetVal = false;
            } else {
                $strClassName   = $recBundleToUse['strClassPrefix'] . 'HardwareBundle';
                $intComponentID = $recBundleToUse['intComponentID'];

                $unkRetVal = new $strClassName();

                $unkRetVal->GetByComponentID($recBundleToUse['intComponentID']);
            }
        }

        return $unkRetVal;

    } // function GetHardwareBundleByServiceID($intServiceID)

    /**
     * Get a list of supplies supported by the system
     *
     * @return multitype:unknown
     */
    function GetSuppliers()
    {
        $hConnection = get_named_connection('product');

        $strQuery = 'SELECT hardware_supplier_id AS intSupplierID, ' .
                          ' supplier_name        AS strSupplierName ' .
                      'FROM hardware_suppliers';

        $hResult = mysql_query($strQuery, $hConnection)
            or report_error(__FILE__, __LINE__, mysql_error($hConnection));

        $arrSuppliers = array();

        while ($rRow = mysql_fetch_assoc($hResult)) {
            $arrSuppliers[] = $rRow;
        }

        mysql_free_result($hResult);

        return $arrSuppliers;
    }

    /**
     * Get a supplier object by its numeric ID
     *
     * @param int $intSupplierID SupplierID
     *
     * @return unknown
     */
    function GetSupplierByID($intSupplierID)
    {
        $hConnection = get_named_connection('product');

        $strQuery = 'SELECT php_class_prefix ' .
                      'FROM hardware_suppliers ' .
                     "WHERE hardware_supplier_id='$intSupplierID'";

        $hResult = mysql_query($strQuery, $hConnection)
            or report_error(__FILE__, __LINE__, mysql_error($connection));

        if (mysql_num_rows($hResult) == 0) {
            report_error(__FILE__, __LINE__, "Supplier ID '$intSupplierID' not found");
        }

        $strClassName = mysql_result($hResult, 0, 0) . 'HardwareBundleSupplier';

        return new $strClassName();
    }

    /**
     * Returns true if the passed account type includes a hardware bundle
     *
     * @param int $intServiceDefinitionID ServiceDefinitionID
     *
     * @return boolean
     */
    function ServiceDefinitionHasHardware($intServiceDefinitionID)
    {
        $hConnection = get_named_connection('product');

        $strQuery = 'SELECT COUNT(*) ' .
                      'FROM service_component_config         AS scc, ' .
                          ' component_hardware_bundle_config AS chbc ' .
                     'WHERE scc.service_component_id=chbc.service_component_id ' .
                       "AND scc.service_definition_id='$intServiceDefinitionID' " .
                       'AND default_quantity >= 1';

        $hResult = mysql_query($strQuery, $hConnection)
            or report_error(__FILE__, __LINE__, mysql_error($hConnection));

        $intCount = mysql_result($hResult, 0, 0);

        mysql_free_result($hResult);

        if ($intCount > 0) {
            return true;
        } else {
            return false;
        }

    } // function ServiceDefinitionHasHardware

    /**
     * Utility function to get the component type of of the component used
     * for replacement hardware orders
     *
     * @return number
     */
    function GetReplacementHardwareComponentTypeID()
    {
        // Cache the result so we don't have to get it more than once.
        static $intComponentTypeID = 0;

        if ($intComponentTypeID == 0) {
            $dbhConnection = get_named_connection('product');

            $strQuery = 'SELECT service_component_id ' .
                          'FROM component_hardware_bundle_config  ' .
                         'WHERE is_replacement_hardware=1';

            $resResult = mysql_query($strQuery, $dbhConnection)
                or report_error(__FILE__, __LINE__, mysql_error($dbhConnection));

            $intComponentTypeID = mysql_result($resResult, 0, 0);

            mysql_free_result($resResult);
        }

        return $intComponentTypeID;
    } // function GetReplacementHardwareComponentTypeID
} // HardwareBundleControl
?>
