<?php

/**
 * Access library for WLR Call Feature
 *
 * @package    Core
 * @subpackage WLR
 * @access     public
 * <AUTHOR>
 * @version    $Id: CWlrCallFeature.inc,v 1.12.2.6 2009/07/17 16:00:45 fzaki Exp $
 * @filesource
 */
require_once '/local/data/mis/database/database_libraries/components/CWlrProductComponent.inc';
require_once '/local/data/mis/database/database_libraries/components/CWlrProduct.inc';

configDefineIfMissing('WLR_MINIMUM_CHARGEABLE_AMOUNT', 300);

class CWlrCallFeature extends CWlrProductComponent
{
    var $m_bolCanBeBundled;

    /**
     * WLR call feature constructor
     *
     * <AUTHOR>
     * @access    public
     *
     * @param    integer     Product Component Instance ID
     *
     * @return    boolean     Only if this is called as a member method of a sub-class
     */
    function CWlrCallFeature($intProductComponentInstanceId)
    {
        parent::__construct($intProductComponentInstanceId);
        $this->_setCanBeBundled();
    }


    /**
     * Setter function to set m_bolCanBeBundled
     *
     * @access private
     * <AUTHOR>
     * @return none
     */
    private function _setCanBeBundled()
    {

        $db = get_named_connection_with_db('dbProductComponents');
        $strQuery = "SELECT bolCanBeBundled
                       FROM tblWlrCallFeatureConfig cfc
                 INNER JOIN tblProductComponentConfig pcc ON pcc.intProductComponentConfigID = cfc.intProductComponentConfigId
                 INNER JOIN userdata.tblProductComponentInstance pci ON pci.intProductComponentID = pcc.intProductComponentID
                 INNER JOIN products.tblServiceComponentProduct scp ON scp.intServiceComponentProductID = pcc.intServiceComponentProductID
                      WHERE pci.intProductComponentInstanceID = '" . $this->getProductComponentInstanceID() . "'" .
            " AND scp.intServiceComponentID  = '" . $this->getServiceComponentID() . "'";
        $res = PrimitivesQueryOrExit($strQuery, $db, 'CWlrCallFeature::_setCanBeBundled', false);

        if (!$res) {
            return false;
        }

        $bolCanBeBundled = PrimitivesResultGet($res, 'bolCanBeBundled');
        $this->m_bolCanBeBundled = $bolCanBeBundled > 0 ? true : false;
    }

    /**
     * Getter function to get m_bolCanBeBundled
     *
     * @access private
     * <AUTHOR>
     * @return boolean m_bolCanBeBundled
     */
    private function _getCanBeBundled()
    {

        return $this->m_bolCanBeBundled;
    }

    /**
     * Static function to get monthly cost for a given combination of product and call feature handle
     *
     * @access public
     * <AUTHOR>
     * @return int - monthly cost
     */
    function getMonthlyCost($intServiceComponentId, $strCallFeatureHandle)
    {
        $intServiceComponentId = intval($intServiceComponentId);
        if ($intServiceComponentId <= 0)
            return false;

        $db = get_named_connection_with_db('unprivileged_reporting');

        $strCallFeatureHandle = PrimitivesRealEscapeString($strCallFeatureHandle, $db);

        $strQuery = "SELECT t.intCostIncVatPence
                     FROM dbProductComponents.tblTariff t
                     INNER JOIN dbProductComponents.tblProductComponentConfig pcc
                             ON pcc.intProductComponentConfigID = t.intProductComponentConfigID
                     INNER JOIN dbProductComponents.tblProductComponent pc
                             ON pc.intProductComponentID = pcc.intProductComponentID
                     INNER JOIN products.tblServiceComponentProduct scp
                             ON scp.intServiceComponentProductID = pcc.intServiceComponentProductID
                     WHERE pc.vchHandle = '$strCallFeatureHandle'
                       AND scp.intServiceComponentID = '$intServiceComponentId'
                       AND t.dtmEnd IS NULL";


        $res = PrimitivesQueryOrExit($strQuery, $db, 'CWlrCallFeature::getMonthlyCost()', false);

        if (!$res)
            return false;

        return PrimitivesResultGet($res, 'intCostIncVatPence');
    }


    /**
     * getInitialCost
     *
     * @param integer $intServiceComponentId
     * @param string  $strCallFeatureHandle
     *
     * @access public
     * @return int
     */
    function getInitialCost($intServiceComponentId, $strCallFeatureHandle)
    {
        $intServiceComponentId = intval($intServiceComponentId);
        if (!$intServiceComponentId) {

            return false;
        }

        $db = get_named_connection_with_db('unprivileged_reporting');

        $strCallFeatureHandle = PrimitivesRealEscapeString($strCallFeatureHandle, $db);

        $strQuery = "SELECT cfc.intSetupCostIncVatPence
                     FROM dbProductComponents.tblWlrCallFeatureConfig cfc
                     INNER JOIN dbProductComponents.tblProductComponentConfig pcc
                       ON pcc.intProductComponentConfigID = cfc.intProductComponentConfigId AND pcc.dtmEnd IS NULL
                     INNER JOIN products.tblServiceComponentProduct scp
                       USING(intServiceComponentProductID)
                     INNER JOIN dbProductComponents.tblProductComponent pc
                       ON pc.intProductComponentID = pcc.intProductComponentID
                     WHERE scp.intServiceComponentId = '$intServiceComponentId'
                     AND pc.vchHandle = '$strCallFeatureHandle'";

        $res = PrimitivesQueryOrExit($strQuery, $db, 'CWlrCallFeature::getInitialCost()', false);

        if (!$res)
            return false;

        return PrimitivesResultGet($res, 'intSetupCostIncVatPence');
    }

    /**
     * @param Integer $intServiceComponentId Service Component ID
     * @param Integer $intProductComponentID Product Component ID
     *
     * @return array|bool|unknown
     */
    public static function getMonthlyCostByTypeId($intServiceComponentId, $intProductComponentID)
    {
        self::includeLegacyFiles();

        $intServiceComponentId = intval($intServiceComponentId);
        if (!$intServiceComponentId) {
            return false;
        }

        $db = get_named_connection_with_db('dbProductComponents_reporting');
        $intProductComponentID = PrimitivesRealEscapeString($intProductComponentID, $db);

        $strQuery = "SELECT t.intCostIncVatPence
                     FROM dbProductComponents.tblTariff t
                     INNER JOIN dbProductComponents.tblProductComponentConfig pcc
                             ON pcc.intProductComponentConfigID = t.intProductComponentConfigID
                     INNER JOIN dbProductComponents.tblProductComponent pc
                             ON pc.intProductComponentID = pcc.intProductComponentID
                     INNER JOIN products.tblServiceComponentProduct scp
                             ON scp.intServiceComponentProductID = pcc.intServiceComponentProductID
                     WHERE pc.intProductComponentID = '$intProductComponentID'
                       AND scp.intServiceComponentID = '$intServiceComponentId'
                       AND t.dtmEnd IS NULL";

        $res = PrimitivesQueryOrExit($strQuery, $db, 'CWlrCallFeature::getMonthlyCostByTypeId()', false);

        if (!$res) {
            return false;
        }

        return PrimitivesResultGet($res, 'intCostIncVatPence');
    }


    /**
     * Method to get the the monthly cost of the call feature
     *
     *
     * @access public
     * <AUTHOR>
     * @return int
     */
    function getCurrentCost()
    {
        $objProduct = CProduct::createInstance($this->getComponentID());

        return CWlrCallFeature::getMonthlyCost($objProduct->getComponentTypeID(), $this->getHandle());
    }

    /**
     * Static method to get the list of chargable statuses  fo call feature
     *
     *
     * @access public
     * <AUTHOR>
     * @return arr - array of stauses
     */

    public static function getChargableStatuses()
    {
        self::includeLegacyFiles();

        return array(PRODUCT_COMPONENT_ACTIVE, CWlrCallFeature::getRemovalStatus());
    }


    /**
     * Static method to get the list of call features types (intProductComponentID)
     *
     *
     * @access public
     * @return array
     * <AUTHOR>
     */

    function getAllCallFeaturesTypes($intHomePhoneComponentId)
    {
        return self::getCallFeaturesData($intHomePhoneComponentId, '', 'intProductComponentID');
    }


    /**
     * Static method to get the list of call features types (intProductComponentID)
     *
     *
     * @access public
     * @return array
     * <AUTHOR>
     */

    static function getAllCallFeatures($intHomePhoneComponentId)
    {
        self::includeLegacyFiles();

        return CWlrCallFeature::getAllCallFeaturesTypes($intHomePhoneComponentId);
    }


    /**
     * Static method to get the list of call features that can be bundled
     *
     *
     * @access public
     * @return array
     * <AUTHOR>
     */

    public static function getBundleableCallFeatures($intHomePhoneComponentId)
    {
        self::includeLegacyFiles();

        return self::getCallFeaturesData($intHomePhoneComponentId, true, 'intProductComponentID');
    }

    /**
     * Static method to get the list of call features that can not be bundled
     *
     * @param int $intHomePhoneComponentID the WLR component ID
     *
     * @return array
     */
    public static function getNoBundleableCallFeatures($intHomePhoneComponentId)
    {
        self::includeLegacyFiles();

        return self::getCallFeaturesData($intHomePhoneComponentId, false, 'intProductComponentID');
    }

    /**
     * Calculates the cost for the call features - takes bundling into account
     * The cost is returned in pence (e.g. 299)
     *
     * @param array $arrCF                   the list of call features
     * @param int   $intHomePhoneComponentID the WLR component ID
     *
     * @return int
     */
    function getCostForSetOfCallFeatures($arrCF, $intHomePhoneComponentId)
    {
        $intNoOfCallFeatures = 0;
        $intCost = 0;
        $bolVoiceMailExtra = false;
        $bolVoiceMailPlus = false;

        // TODO: this code generally needs to be reviewed - we're potentially making two calls to getMonthlyCost()
        // when processing voicemail components.  This is outside the current scope, however.
        foreach ($arrCF as $strHandle) {
            if ($strHandle == 'WlrVoiceMailExtra') {
                $bolVoiceMailExtra = true;
            } elseif ($strHandle == 'WlrVoiceMailPlus') {
                $bolVoiceMailPlus = true;
            } else {
                $intNoOfCallFeatures++;
                // Nasty "hack" - if there's only one CF, then this will be passed to the getMonthlyCost() call below
                $strCFHandle = $strHandle;
            }
        }

        // if more than one CF then get cost of CF bundle
        if ($intNoOfCallFeatures > 1) {
            $intCost += CWlrCallFeaturesBundle::getCostByAmountOfFeatures(
                $intNoOfCallFeatures,
                $intHomePhoneComponentId
            );
        } else {
            // otherwise get cost of call feature
            $intCost += CWlrCallFeature::getMonthlyCost($intHomePhoneComponentId, $strCFHandle);
        }

        // if there is a VME take also this cost
        if ($bolVoiceMailExtra) {
            $intCost += CWlrCallFeature::getMonthlyCost($intHomePhoneComponentId, 'WlrVoiceMailExtra');
        }

        if ($bolVoiceMailPlus) {
            $intCost += CWlrCallFeature::getMonthlyCost($intHomePhoneComponentId, 'WlrVoiceMailPlus');
        }

        return $intCost;
    }


    function getCostByHandle($strHandle, $intHomePhoneComponentId = SCID_WLR_ANYTIME, $strPayFreq = 'MONTHLY')
    {
        if ($intHomePhoneComponentId == 'none') {
            return 0;
        }

        return CWlrCallFeature::getMonthlyCost('WlrVoiceMailExtra', $intHomePhoneComponentId);
    }

    /**
     * Static method to get call features data
     *
     * @param int  $intHomePhoneComponentId id of home phone component
     * @param bool $bolBundlable            unset: all CF. true: only bundlable. false: only CF that cannot be bundled
     * @param str  $strFieldName            database field name to pick from table
     *
     * @return array
     */
    protected static function getCallFeaturesData($intHomePhoneComponentId, $bolBundlable = '', $strFieldName)
    {
        self::includeLegacyFiles();
        // Stephen Smith, 2011
        // I have no idea why this query was recently updated to exclude stuff beginning WlrCallBarring%.
        // Doesn't seem right, but at the same time I don't have time to do a postmortem on problem 65053.
        //
        // My understanding of the WlrCallBarringOCB and WlrCallBarringRTCC "features" is that they're not
        // actually features, but instead defined as product components to make the Java tier happy when
        // applying/removing debt management for WLR3.
        //
        // Having the % wildcard looks to break the fact that we have genuine callbarring call features,
        // WlrCallBarringPRS and WlrCallBarringPRS_IC which were originally created for MAAF products.
        // John Lewis WLR products also use these two call features.
        //
        // Therefore I've rearranged the hack so that it's explicit in the "features" its wanting to exclude.

        $strConfigCriteria = '';
        if ($bolBundlable !== '') {
            $strConfigCriteria .= "AND cf.bolCanBeBundled=" . (int)$bolBundlable;
        }

        $strQuery = <<<EOQ
SELECT
    pc.$strFieldName
FROM
    products.tblServiceComponentProduct                         AS scp
    INNER JOIN dbProductComponents.tblProductComponentConfig    AS pcc
        ON pcc.intServiceComponentProductID = scp.intServiceComponentProductID
        AND scp.intServiceComponentID='$intHomePhoneComponentId'
        AND pcc.dtmEnd IS NULL
    INNER JOIN dbProductComponents.tblWlrCallFeatureConfig      AS cf
        ON pcc.intProductComponentConfigID = cf.intProductComponentConfigID
        $strConfigCriteria
    INNER JOIN dbProductComponents.tblProductComponent          AS pc
        ON pcc.intProductComponentID = pc.intProductComponentID
        AND pc.vchHandle NOT IN ('WlrCallBarringOCB', 'WlrCallBarringRTCC')
EOQ;

        $dbConn = get_named_connection_with_db('unprivileged_reporting');
        $result = PrimitivesQueryOrExit($strQuery, $dbConn);

        return (PrimitivesNumRowsGet($result) > 0) ? PrimitivesResultsAsListGet($result) : array();
    }

    /**
     * Static method to get the list of call features handles
     *
     * @param  $intHomePhoneComponentId - id of home phone component
     * @param  $bolBundlable            - if not set select all CF, if true select only bundlable, if false select CF
     *                                  that can not be bundled
     *
     * @access public
     * <AUTHOR>
     * @return arr - array of call features handles
     */

    function getCallFeaturesHandles($intHomePhoneComponentId, $bolBundlable = '')
    {
        return self::getCallFeaturesData($intHomePhoneComponentId, $bolBundlable, 'vchHandle');
    }

    /**
     * Static method to get the list of bundlable call features handles
     *
     * @param  $intHomePhoneComponentId - id of home phone component
     *
     * @access public
     * <AUTHOR>
     * @return arr - array of bundlable call features handles
     */

    function getBundlableCallFeaturesHandles($intHomePhoneComponentId)
    {
        return CWlrCallFeature::getCallFeaturesHandles($intHomePhoneComponentId, true);
    }

    /**
     * Static method to get the list of no bundlable call features handles
     *
     * @param  $intHomePhoneComponentId - id of home phone component
     *
     * @access public
     * <AUTHOR>
     * @return arr - array of no bundlable call features handles
     */

    function getNoBundlableCallFeaturesHandles($intHomePhoneComponentId)
    {
        return CWlrCallFeature::getCallFeaturesHandles($intHomePhoneComponentId, false);
    }

    /**
     * Method to mark call feature for removal
     *
     * @access public
     * <AUTHOR>
     */

    function markForRemoval()
    {
        $this->prvSetStatus($this->getStatusHandleFromID(CWlrCallFeature::getRemovalStatus()));
    }

    /**
     * Static. Returns removal status
     *
     * @access public
     * <AUTHOR>
     */

    public static function getRemovalStatus()
    {
        return PRODUCT_COMPONENT_QUEUED_DEACTIVATE;
    }

    /**
     * Returns provisioning status - which is diffrent than normal status
     *
     * @access public
     * <AUTHOR>
     */

    function getProvisioningStatus()
    {
        $strStatus = $this->getStatusHandleFromID($this->getStatusID());
        if ($this->getStatusID() == CWlrCallFeature::getRemovalStatus())
            return 'MARKED_FOR_REMOVAL';

        switch ($strStatus) {
            case 'UNCONFIGURED':
                $strRet = 'MARKED_TO_ADD';
                break;
            case 'ACTIVE':
                $strRet = 'ACTIVE';
                break;
            default:
                $strRet = 'NOT_PRESENT';
        }

        return $strRet;
    }

    /**
     * Returns active or unconfigured call features bundle object for this call feature
     *
     * @access public
     * <AUTHOR>
     * @return obj or null if there is no bundle on the account
     */

    function getMyActiveOrUnconfiguredBundle()
    {
        $arrBundle = CWlrProduct::getSelectedProductComponentInstanceIDs(
            CWlrCallFeaturesBundle::getAllTypes(),
            array(
                PRODUCT_COMPONENT_ACTIVE,
                PRODUCT_COMPONENT_UNCONFIGURED
            ),
            $this->getComponentID()
        );

        return (count($arrBundle) > 0) ? CProductComponent::createInstance($arrBundle[0]) : null;
    }

    /**
     * onEnable - this method is called by enable method of superclass
     * this method is overwritten in two subclasses - WlrVoiceMail and WLRVoiceMailExtra
     *
     * @access public
     * <AUTHOR>
     * @return true if success
     */

    function onEnable($arrArgs = array())
    {
        $uxtEnableDate = (isset($arrArgs['uxtEnableDate'])) ? $arrArgs['uxtEnableDate'] : time();
        $bolTakeProRataPayment = (isset($arrArgs['bolTakeProRataPayment'])) ? $arrArgs['bolTakeProRataPayment'] : false;

        //if this is a bundleable feature, set the next invoice date to NULL as charges would be taken by bundle
        if ($this->_getCanBeBundled() AND $this->getMyActiveOrUnconfiguredBundle() != null) {
            // do not take any payments etc - all charges must be taken by bundle
            // just set next billing date to null - billing script will not charge this product component
            $this->setNextInvoiceDate(null);
        } elseif ($bolTakeProRataPayment) {
            //we have to take prorata payment
            $this->chargeProrataCost($uxtEnableDate);
        }

        return true;
    }

    function chargeProrataCost($uxtProRataCalculationDate = 0, $uxtNextInvoiceDate = '')
    {
        if (\Plusnet\Feature\FeatureToggleManager::isOnFiltered(
            'RBM_MIGRATION_COMPLETE',
            null,
            null,
            null,
            $this->getServiceID()
        )
        ) {
            return true;
        }

        $arrProrataCostDetails = CProductComponent::generateProRataPaymentDetails($this->getServiceID(), $this->getTariffID(), $strServiceComponentProductTypeHandle = '',
                                                                                  $uxtProRataCalculationDate = false);

        $uxtNextInvoiceDate = I18n_Date::fromTimestamp($arrProrataCostDetails['uxtProRataNextInvoice'])
            ->getModified(-1, I18n_Date::DAYS)
            ->getTimestamp();


        $intProrataCost = $arrProrataCostDetails['intProRataCost'];
        if ($intProrataCost > 0) {
            $strProRataDescription = $arrProrataCostDetails['strProRataDescription'];

            if ($intProrataCost <= WLR_MINIMUM_CHARGEABLE_AMOUNT) {
                //raise a schedule payment
                $objPaymentScheduler = new CProductComponentPaymentScheduler($this->getProductComponentInstanceID(), '');

                $objPaymentScheduler->addIncVatAmount($intProrataCost,
                                                      $arrProrataCostDetails['uxtProRataNextInvoice'],
                                                      $strProRataDescription,
                                                      time(),
                                                      $uxtNextInvoiceDate);

                $intSchedulePaymentID = $objPaymentScheduler->m_intScheduledPaymentID;
                if (!isset($intSchedulePaymentID)) {
                    $strTicketBody = "A schedule payment could not be be raised for {$arrProrataCostDetails['floProRataCostDisplay']} for the " .
                        "$strProRataDescription. The component has been activated, please take this payment manually.";
                    tickets_ticket_add('Script', $this->getServiceID(), '', '', 'Open', 0, $strTicketBody);
                }
            } else {

                $arrInvoiceItems = array(
                    array(
                        'amount'      => $intProrataCost / 100,
                        'description' => $strProRataDescription,
                        'gross'       => true
                    )
                );

                $intInvoiceID = CWlrProduct::takePayment($this->getServiceID(),
                                                         $arrInvoiceItems,
                                                         $intProrataCost / 100,
                                                         $strProRataDescription);
                if (isset($intInvoiceID) && $intInvoiceID > 0) {
                    $objPaymentScheduler = new CProductComponentPaymentScheduler($this->getProductComponentInstanceID(), '');

                    $objPaymentScheduler->addIncVatAmount($intProrataCost,
                                                          time(),
                                                          $strProRataDescription,
                                                          time(),
                                                          $uxtNextInvoiceDate);
                    $intScheduledPaymentID = $objPaymentScheduler->m_intScheduledPaymentID;

                    if (($intScheduledPaymentID > 0) && ($intInvoiceID > 0)) {
                        $objScheduledPayment = new CProductComponentScheduledPayment($intScheduledPaymentID);
                        $objScheduledPayment->markAsInvoiced($intInvoiceID, $arrInvoiceItems[0]['intLineItemId']);
                    }

                    $strTicketBody = "A payment was taken for &pound;{$arrProrataCostDetails['floProRataCostDisplay']} for " .
                        "$strProRataDescription.";
                    tickets_ticket_add('Script', $this->getServiceID(), '', '', 'Closed', 0, $strTicketBody);

                    return true;
                } else {
                    $strTicketBody = "A payment could not be be taken for &pound;{$arrProrataCostDetails['floProRataCostDisplay']} for " .
                        "$strProRataDescription. The component has been activated, please take this payment manually.";

                    tickets_ticket_add('Script', $this->getServiceID(), '', '', 'Open', 0, $strTicketBody);

                    return false;
                }
            }
        } else {
            return true;
        }
    }


////////////////////////////////////////////////////////////////////////////////

    public static function enumAllCallFeatures(& $objWlrProduct)
    {
        self::includeLegacyFiles();

        if (!is_object($objWlrProduct)) {
            return false;
        }

        $intComponentId = $objWlrProduct->getComponentID();

        $strQuery = "SELECT
                        pci.intProductComponentInstanceID,
                        pc.vchHandle AS strFeatureHandle,
                        pc.vchDisplayName AS strFeatureName,
                        ts.vchHandle AS strStatus
                     FROM
                        userdata.tblProductComponentInstance AS pci
                     INNER JOIN userdata.components AS c
                        ON c.component_id = pci.intComponentID
                     INNER JOIN dbProductComponents.tblStatus AS ts
                        ON ts.intStatusID = pci.intStatusID
                     INNER JOIN dbProductComponents.tblProductComponentConfig pcc
                        ON pcc.intProductComponentID = pci.intProductComponentID AND pcc.dtmEnd IS NULL
                     INNER JOIN dbProductComponents.tblProductComponent pc
                        ON pc.intProductComponentID = pci.intProductComponentID
                     INNER JOIN dbProductComponents.tblWlrCallFeatureConfig cf
                        ON pcc.intProductComponentConfigID = cf.intProductComponentConfigID
                     INNER JOIN products.tblServiceComponentProduct scp
                        ON pcc.intServiceComponentProductID = scp.intServiceComponentProductID
                        AND scp.intServiceComponentID = c.component_type_id
                     WHERE c.component_id = '{$intComponentId}'
                     AND ts.vchHandle NOT IN ('QUEUED_DESTROY', 'DESTROYED')";

        // would it be safe to use reporting server here or not?
        $db = get_named_connection_with_db('userdata');

        $res = PrimitivesQueryOrExit($strQuery, $db, 'Get all call features regardless of status (except DESTROYED and QUEUED_DESTROY)', false);

        if (!$res) {
            return false;
        }

        $arrResults = array();

        $arrResults['added'] = array();
        $arrResults['removed'] = array();
        $arrResults['active'] = array();
        $arrResults['other'] = array();

        $arrData = PrimitivesResultsAsArrayGet($res);

        foreach ($arrData as $thisItem) {
            switch ($thisItem['strStatus']) {
                case 'UNCONFIGURED':
                    $arrResults['added'][] = $thisItem;
                    break;

                case 'QUEUED_DEACTIVATE':
                    $arrResults['removed'][] = $thisItem;
                    break;

                case 'ACTIVE':
                    $arrResults['active'][] = $thisItem;
                    break;

                default:
                    $arrResults['other'][] = $thisItem;
                    break;
            }
        }

        return $arrResults;
    }


    /**
     * Function to get Features and rate
     *
     * @param integer $intServiceComponentId
     *
     * @return array
     */
    public static function getCallFeatureNameAndRate($intServiceComponentId)
    {
        self::includeLegacyFiles();

        $db = get_named_connection_with_db('userdata');
        $strQuery = "SELECT pc.vchHandle, pc.vchDisplayName, cfc.bolCanBeBundled, t.intCostIncVatPence/100 as intCostIncVatPence
                     FROM dbProductComponents.tblProductComponent pc
                     INNER JOIN dbProductComponents.tblProductComponentConfig pcc
                             ON pc.intProductComponentID = pcc.intProductComponentID  AND pcc.dtmEnd IS NULL
                     INNER JOIN dbProductComponents.tblWlrCallFeatureConfig cfc
                             ON cfc.intProductComponentConfigId = pcc.intProductComponentConfigId
                     INNER JOIN products.tblServiceComponentProduct scp
                             ON scp.intServiceComponentProductID = pcc.intServiceComponentProductID
                     INNER JOIN dbProductComponents.tblTariff t
                             ON t.intProductComponentConfigID = pcc.intProductComponentConfigID
                          WHERE scp.intServiceComponentID = '$intServiceComponentId'
                            AND t.dtmEnd IS NULL AND pc.vchHandle NOT LIKE 'WlrCallBarring%'";

        $res = PrimitivesQueryOrExit($strQuery, $db);
        $arrResult = PrimitivesResultsAsArrayGet($res);

        return $arrResult;

    }


    /* Generates an array of bundle prices based on the current product, calculating the number of bundlable items
     * and the maximum saving for each bundle.
     *
     * @return array - array keyed on bundle size, with floPrice for the price, and floMaxSaving for the max saving
     *           for that bundle.
    */
    public static function getBundlePrices($intServiceComponentId)
    {
        self::includeLegacyFiles();

        // Get the bundlable features (array_unique because the function can return duplicates)
        $arrFeatures = array_unique(CWlrCallFeature::getBundleableCallFeatures($intServiceComponentId));

        $intFeatureCount = 0;
        $arrTotalFeatures = array();
        $arrPrices = array();
        $arrBundlePrices = array();

        // Go through the bundlable features, adding them to the bundle one by one to calculate the bundle price for each number
        // of features.
        foreach ($arrFeatures as $strFeatureHandle) {

            $intFeatureCount++;

            // Each time through the foreach, add an extra feature to the bundle..
            $arrTotalFeatures[] = $strFeatureHandle;

            // Build up an array of individual prices, so we can calculate the max saving later.
            $arrPrices[$strFeatureHandle] = sprintf("%.2f", CWlrCallFeature::getMonthlyCostByTypeId($intServiceComponentId, $strFeatureHandle) / 100);

            if ($intFeatureCount > 1) {

                // Get the cost for the current bundle..
                $intBundlePrice = CWlrCallFeature::getCostForSetOfCallFeatures($arrTotalFeatures, $intServiceComponentId);

                // If the price hasn't increased with an extra feature, then we've reached the max chargeable bundle price
                // for example 5+ features cost the same as 5 features, so stop calculating..
                if ($intBundlePrice != $intLastPrice) {

                    $arrBundlePrices[$intFeatureCount]['floPrice'] = sprintf("%.2f", $intBundlePrice / 100);
                    $intLastPrice = $intBundlePrice;
                } else {

                    continue;
                }

            }

        }

        // intMaxBundleSize is calculated here, and is meant to represent the number of features that the largest
        // available bundle contains.
        // As arrBundlePrices is a list of all the available bundles and we take a count of it, we need to add 1 to
        // get the number of features in that bundle, e.g. we have Bundle1 (2 features), Bundle2 (3 features) etc.
        $intMaxBundleSize = count($arrBundlePrices) + 1;

        // Go through the bundle prices array again, and work out the maximum savings..

        // Sort all of the bundlable features, highest price first.
        rsort($arrPrices, SORT_NUMERIC);

        $floMaxPossibleSaving = 0;

        foreach ($arrBundlePrices as $intNumFeatures => $arrPriceData) {

            $intSubTotal = 0;

            // If we're taking the maximum bundle size, then the max saving should be calculated based on taking all
            // of the bundlable features individually..
            if ($intNumFeatures == $intMaxBundleSize) {

                $intNumFeaturesToAddUp = count($arrPrices);
            } else {

                $intNumFeaturesToAddUp = $intNumFeatures;
            }

            // Add up the individual prices of the x most expensive options based on our bundle size..
            for ($intFeatureCounter = 1; $intFeatureCounter <= $intNumFeaturesToAddUp; $intFeatureCounter++) {

                $intSubTotal += $arrPrices[$intFeatureCounter - 1];
            }

            // Max. saving is the max individual cost of the number of features taken, minus the actual bundle price..
            $floCurrentSaving = $intSubTotal - $arrPriceData['floPrice'];
            $arrBundlePrices[$intNumFeatures]['floMaxSaving'] = sprintf("%.2f", $floCurrentSaving);

            if ($floCurrentSaving > $floMaxPossibleSaving) {

                $floMaxPossibleSaving = $floCurrentSaving;
            }
        }


        return array("arrBundlePrices" => $arrBundlePrices, "floMaxPossibleSaving" => sprintf('%.2f', $floMaxPossibleSaving));


    }

    /**
     * Protected method to check if schedule payments needs raising for a call feature.
     * when a call feature is activated the next invoice date is set to NULL if its bundleable and the component has
     * enough number of call features to assign a bubndle.
     *
     * @param  none
     *
     * @access protected
     * <AUTHOR> Zaki
     * @return boolean TRUE/FALSE
     */

    protected function shouldGenerateScheduledPaymentsForContract()
    {
        //If next invoice date is set it means we have to create schedule payments for this feature
        if ($this->getNextInvoiceDate() > 0) {
            return true;
        }

        return false;
    }

    /**
     * Function to set the next invoice date.
     * If the next invoice set need to set to null then it means call feature not need to be billed
     * so we need to cancel the outstanding scheduled payment other than the current billing period
     *
     * @param date $uxtNextInvoice next invoice date
     *
     * @return void
     */
    public function setNextInvoiceDate($uxtNextInvoice = null)
    {
        if (is_null($uxtNextInvoice)) {
            $this->cancelOutStandingScheduledPayments();
        }

        return parent::setNextInvoiceDate($uxtNextInvoice);
    }

    /**
     * destroy functionality of call feature
     * Cancel all outstanding schedule payments
     *
     * @return bool
     */
    function onDestroy()
    {
        $this->cancelOutStandingScheduledPayments();

        return true;
    }

    /**
     * Get any extra parameters this call feature requires for set up
     * as an array of key => value
     *
     * @return string[]
     */
    public function getParameters()
    {
        return array(); // pbe01 is desperately out of date and doesn't support PHP 5.6
    }
}
