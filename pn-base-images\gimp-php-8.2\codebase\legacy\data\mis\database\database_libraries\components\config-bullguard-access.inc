<?php
	require_once(APPLICATION_API_ROOT . 'SecurityProducts/BullGuardManager.class.php');
	
	$global_component_configurators[COMPONENT_BULL_GUARD] = "config_bullguard_configurator";
	$global_component_configurators[COMPONENT_BULL_GUARD_TRIAL] = "config_bullguard_configurator";

    // Hack to insert the component configurator array into PHP5's global scope
    // if it's not already there
    if (!isset($GLOBALS['global_component_configurators'])) {
        $GLOBALS['global_component_configurators'] = $global_component_configurators;
    }
    else {
        foreach($global_component_configurators as $intIndex => $strConfigurator) {
            $GLOBALS['global_component_configurators'][$intIndex] = $strConfigurator;
        }
    }


	function config_bullguard_configurator($intComponentID, $strAction, $strHandle=''){

		try {
			
			$objBullGuardManager = new BullGuardManager();
			$objBullGuardManager->populateDataByComponent($intComponentID);
				
			// Don't support configure, enable and refresh component, it should be done using business logic (payments, etc)
			switch ($strAction)
			{
				case "auto_configure":
				return -1;
				break;
				
				case "auto_enable":
				return -1;
				break;
	
				case "auto_disable":
				return -1;
				break;
					
				case "auto_destroy":
				return (0 <> $objBullGuardManager->deactivateProduct());
				break;
	
				case "auto_refresh":
	
				// Ups, we have broken component here, it's useless because there is no billing, also customer get no proper BG subscription server reply. 
				// Run auto_destroy to clean-up it
				if (false == ($objBullGuardManager->getComponent() instanceof CBullGuardComponent) || 
				   ($objBullGuardManager->getComponent() instanceof CBullGuardProductComponent && !$objBullGuardManager->getSubscription())) {

					config_bullguard_configurator($intComponentID, 'auto_destroy');
					return -1;
				}

				// If account has valid billing date and for some reason component stuck in queued-activate refresh will 
				// process post-signup activation, if component is active just refresh expiry date to next billing date
				try {
	
					if (true == ($objBullGuardManager->getComponent() instanceof CBullGuardProductComponent)) {
						
						$arrComponent = $objBullGuardManager->getComponentArray();
						$arrService = userdata_service_get($arrComponent['service_id']);
						
						switch ($arrComponent['status'])
						{
					    	case 'queued-activate': 
					    		
							// Set BG date to be in line with current invoice date
							if ('' <> $arrService['next_invoice'] && '9999-09-09' <> $arrService['next_invoice'])
							{
								$uxtNextInvoice = strtotime($arrService['next_invoice']);
				
								foreach ($objBullGuardManager->getProduct()->getProductComponents() as $objProductComponent)
								{
									$objProductComponent->setNextInvoiceDate($uxtNextInvoice);
								}
							}
	
					    		$objBullGuardManager->processPostSignupActivation();
							return 1;
						
					    	case 'active':
						    	$utxContractEnd =  $objBullGuardManager->getSubscription()->getContractEnd();
						    	
							$utxExpiryDate = mktime(
								0,
								0,
								0,
								date('m', $utxContractEnd),
								date('d', $utxContractEnd) + 1,
								date('Y', $utxContractEnd)
								);
							
							$objBullGuardManager->getComponent()->setExpiryDate($utxExpiryDate);
							return 1;	
						}
					}
				}
				catch (BullGuardManagerException $objException) {
							
					error_log('BullGuardManagerException occured, exception message was: ' . $objException->getMessage());
				}
				
			    	return -1;
				break;
	
				default:
				return -1;
				break;
			}
		}
		catch (Exception $objException) {
			
			error_log(__FUNCTION__. ': carched bullguard exception, message was: ' . $objException->getMessage());
			return -1;
		}
	}
