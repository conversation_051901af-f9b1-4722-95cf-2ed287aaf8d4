<?php

	/////////////////////////////////////////////////////////////
	// Function    : split_TicketsTicketActionLogCustScript
	// Description : Used to add an entry to common_tickets.tblTicketActionLog
	//               to store that a user has done something to a ticket
	// Arguments   : $intTicketId,
	//               $intPartnerId,
	//               $intContactId,
	//               $strAction,
	//               $intTeamId,
	//               $strOwnerId
	//               $strReopenedTicket 'TRUE' OR 'FALSE'
	//               $strRaisedBy 'script' OR 'portaluser'
	// Returns     : $intTicketActionLogId
	// Author      : D Lewis
	// Date        : 03/04/2003
	/////////////////////////////////////////////////////////////
	function split_TicketsTicketActionLogCustScript($intTicketId, $intPartnerId, $intContactId, $strAction, $intTeamId, $strOwnerId, $strReopenedTicket, $strRaisedBy)
	{
		switch($strRaisedBy){
			case'script':
				$strAction.=' by script';

				break;
			case'portaluser':

				$strAction.=' by user';

				//tickets that are returned via the portal have a status of 'open',
				//for recording perps should be 'assigned'
				if($strReopenedTicket=='TRUE'){
					$strAction = 'Assigned by user';
				}

				//check the number of contacts on the Question if open as they may be ammending the ticket
				if(strtolower($strAction) == 'open by user' && count(tickets_contacts_get_all($intTicketId))>1){
					$strAction = 'Amended by user';
				}

				//we need to check if this has come from 'tickets on hold' thta have been placed back into the pool

				$dbConnection   = get_named_connection_with_db('tickets_reporting');

				$strQuery = 'SELECT SQL_NO_CACHE c.ticket_id, h.release_date,h.when_done
				             FROM ticket_contacts as c, tickets_on_hold as h
				             WHERE c.ticket_id="'.$intTicketId.'"
				             AND c.contact_id=h.contact_id
				             AND h.when_done > DATE_SUB(NOW(), INTERVAL 120 SECOND)';

				$arrResult = PrimitivesQueryOrExit($strQuery, $dbConnection, __FILE__.': function split_TicketsTicketActionLogCustScript() FUBAR');

				$arrResults = PrimitivesResultsAsArrayGet($arrResult);

				//check if we have results
				if(is_array($arrResults) && count($arrResults)==1){

					$strAction = 'Taken off hold via script';

				}

				break;
			default:
				return 0;
				break;
		}
		static $arrTicketActionLookupArray;

		$intTicketActionLogId = 0;

		$dbConnection = get_named_connection('common_tickets');

		// populate the lookup array if it's not already been set
		if((!is_array($arrTicketActionLookupArray)) || (count($arrTicketActionLookupArray) == 0))
		{
			$arrTicketActionLookupArray = array();

			$strQuery = 'SELECT vchTicketAction,
			                    intTicketActionLookupCustScriptId
			               FROM tblTicketActionLookupCustScript';

			$resResult = mysql_query($strQuery, $dbConnection)
			             or report_error(__FILE__, __LINE__, mysql_error($dbConnection));

			while($arrRow = mysql_fetch_assoc($resResult))
			{
				$strTicketAction   = $arrRow['vchTicketAction'];
				$intTicketActionId = $arrRow['intTicketActionLookupCustScriptId'];

				$arrTicketActionLookupArray[strtolower($strTicketAction)] = $intTicketActionId;
			}

			mysql_free_result($resResult);
		}

		// Get the action lookup id from the array
		if(isset($arrTicketActionLookupArray[strtolower($strAction)]))
		{
			$intTicketActionId = $arrTicketActionLookupArray[strtolower($strAction)];
		}
		else
		{
			$intTicketActionId = 0;
		}

		$strQuery = 'INSERT INTO tblTicketActionLogCustScript
		                         (intTicketId,
		                          unPartnerId,
		                          intContactId,
		                          intTicketActionLookupCustScriptId,
		                          intEscalatedToTeamId,
		                          vchEscalatedToUserId,
		                          dtmDateTime)
		                  VALUES ("'.$intTicketId.'",
		                          "'.$intPartnerId.'",
		                          "'.$intContactId.'",
		                          "'.$intTicketActionId.'",
		                          "'.$intTeamId.'",
		                          "'.$strOwnerId.'",
		                          NOW())';

		$resResult = mysql_query($strQuery, $dbConnection)
		             or report_error(__FILE__, __LINE__, mysql_error($dbConnection));

		$intTicketActionLogId = mysql_insert_id($dbConnection);

		return $intTicketActionLogId;

	} // function split_TicketsTicketActionLogCustScript()

?>
