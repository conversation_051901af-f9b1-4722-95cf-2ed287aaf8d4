<?php
/**
 * support-wizard-access.inc
 *
 * Access Library for Support Wizard
 *
 * @package    LegacyCodebase
 * @subPackage Database Libraries
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2012 Plusnet
 */

require_once '/local/data/mis/database/database_libraries/phplib-access.inc';
require_once COMMON_LIBRARY_ROOT . 'class_libraries/Tickets/CClass.inc';
require_once COMMON_LIBRARY_ROOT . 'class_libraries/Tickets/CTicketClass.inc';
require_once '/local/data/mis/database/database_libraries/CPageRating.inc';

require_once '/local/data/mis/database/database_libraries/Util_LibrarySplitter.class.php';
require_once '/local/data/mis/database/database_libraries/supportwizard/SupportWizard.class.php';

/**
 * Wizard Get Wizards
 *
 * @param unknown $wizard_id Wizard Id
 *
 * @return unknown
 */
function wizard_get_wizards($wizard_id='')
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_get_wizards($wizard_id);
}

/**
 * Wizard Get Page Question Topic Id
 *
 * @param unknown $page_id Page Id
 *
 * @return unknown
 */
function wizard_get_page_question_topic_id($page_id)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_get_page_question_topic_id($page_id);
}

/**
 * Wizard Get All Wizards
 *
 * @return unknown
 */
function wizard_get_all_wizards()
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_get_all_wizards();
}

/**
 * Wizard Get By Name
 *
 * @param unknown $name Name
 *
 * @return unknown
 */
function wizard_get_by_name($name)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_get_by_name($name);
}

/**
 * Wizard Get First Page
 *
 * @param unknown $wizard_id Wizard Id
 *
 * @return unknown
 */
function wizard_get_first_page($wizard_id)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_get_first_page($wizard_id);
}

/**
 * Wizard Set First Page
 *
 * @param unknown $wizard_id Wizard Id
 * @param unknown $page_id   Page Id
 *
 * @return unknown
 */
function wizard_set_first_page($wizard_id, $page_id)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_set_first_page($wizard_id, $page_id);
}

/**
 * Wizard Get Page
 *
 * @param unknown $wizard_id Wizard Id
 * @param unknown $page_id   Page Id
 *
 * @return unknown
 */
function wizard_get_page($wizard_id, $page_id)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_get_page($wizard_id, $page_id);
}

/**
 * Wizard Get Page Links
 *
 * @param unknown $page_id Page Id
 *
 * @return unknown
 */
function wizard_get_page_links($page_id)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_get_page_links($page_id);
}

/**
 * Wizard Get Page Links Filtered
 *
 * @param unknown $wizard_id Wizard Id
 * @param unknown $page_id   Page Id
 * @param unknown $visp      Visp
 *
 * @return unknown
 */
function wizard_get_page_links_filtered($wizard_id, $page_id, $visp)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_get_page_links_filtered($wizard_id, $page_id, $visp);
}

/**
 * Wizard Get Page Parent Id
 *
 * @param unknown $page_id Page Id
 *
 * @return unknown
 */
function wizard_get_page_parent_id($page_id)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_get_page_parent_id($page_id);
}

/**
 * Wizard Get Page Ticket Pool
 *
 * @param unknown $page_id Page Id
 *
 * @return unknown
 */
function wizard_get_page_ticket_pool($page_id)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_get_page_ticket_pool($page_id);
}

/**
 * Wizard Get Page Ticket Class
 *
 * @param unknown $page_id Page Id
 *
 * @return unknown
 */
function wizard_get_page_ticket_class($page_id)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_get_page_ticket_class($page_id);
}

/**
 * Wizard Get Page Ticket Text
 *
 * @param unknown $page_id Page Id
 *
 * @return unknown
 */
function wizard_get_page_ticket_text($page_id)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_get_page_ticket_text($page_id);
}

/**
 * Wizard Get Title
 *
 * @param unknown $page_id Page Id
 *
 * @return unknown
 */
function wizard_get_title($page_id)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_get_title($page_id);
}

/**
 * Wizard Variables
 *
 * @param unknown $visp    Visp
 * @param unknown $partner Partner
 *
 * @return unknown
 */
function wizard_variables($visp, $partner='products')
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_variables($visp, $partner);
}

/**
 * Wizard Variable All Visps
 *
 * @param unknown $variable Variable
 *
 * @return unknown
 */
function wizard_variable_all_visps($variable)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_variable_all_visps($variable);
}

/**
 * Wizard Page Get External Links
 *
 * @param unknown $page_id Page Id
 *
 * @return unknown
 */
function wizard_page_get_external_links($page_id)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_page_get_external_links($page_id);
}

/**
 * Wizard Get External Link
 *
 * @param unknown $link_id Link Id
 *
 * @return unknown
 */
function wizard_get_external_link($link_id)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_get_external_link($link_id);
}

/**
 * Wizard Page Add External Link
 *
 * @param unknown $page_id Page Id
 * @param unknown $url     Url
 * @param unknown $name    Name
 *
 * @return unknown
 */
function wizard_page_add_external_link($page_id, $url, $name)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_page_add_external_link($page_id, $url, $name);
}

/**
 * Wizard Page Edit External Link
 *
 * @param unknown $link_id Link Id
 * @param unknown $url     Url
 * @param unknown $name    Name
 *
 * @return unknown
 */
function wizard_page_edit_external_link($link_id, $url, $name)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_page_edit_external_link($link_id, $url, $name);
}

/**
 * Wizard Page Delete External Link
 *
 * @param unknown $link_id Link Id
 *
 * @return unknown
 */
function wizard_page_delete_external_link($link_id)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_page_delete_external_link($link_id);
}

/**
 * Wizard Add Page
 *
 * @param unknown $wizard_id           Wizard Id
 * @param unknown $parent_id           Parent Id
 * @param unknown $page_name           Page Name
 * @param unknown $page_body           Page Body
 * @param unknown $ticket_pool_id      Ticket Pool Id
 * @param unknown $ticket_body         Ticket Body
 * @param unknown $ticket_class_id     Ticket Class Id
 * @param string  $strSectionName      Section Name
 * @param string  $strPageSummary      Page Summary
 * @param string  $strURL              URL
 * @param int     $intQuestionTopicID  Question Topic ID
 * @param string  $strServiceStatusTag Service Status Tag
 * @param bool    $bolHideTicketLink   Hide Ticket Link
 *
 * @return unknown
 */
function wizard_add_page($wizard_id, $parent_id, $page_name, $page_body, $ticket_pool_id, $ticket_body, $ticket_class_id=0, $strSectionName='', $strPageSummary='', $strURL='', $intQuestionTopicID=0, $strServiceStatusTag='', $bolHideTicketLink = 0)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_add_page($wizard_id, $parent_id, $page_name, $page_body, $ticket_pool_id, $ticket_body, $ticket_class_id, $strSectionName, $strPageSummary, $strURL, $intQuestionTopicID, $strServiceStatusTag, $bolHideTicketLink);
}

/**
 * Wizard Edit Page
 *
 * @param unknown $page_id             Page Id
 * @param unknown $page_name           Page Name
 * @param unknown $page_body           Page Body
 * @param unknown $ticket_pool_id      Ticket Pool Id
 * @param unknown $ticket_body         Ticket Body
 * @param unknown $ticket_class_id     Ticket Class Id
 * @param string  $strSectionName      Section Name
 * @param string  $strPageSummary      Page Summary
 * @param string  $strURL              URL
 * @param int     $intQuestionTopicID  Question Topic ID
 * @param string  $strServiceStatusTag Service Status Tag
 * @param bool    $bolHideTicketLink   Hide Ticket Link
 *
 * @return unknown
 */
function wizard_edit_page($page_id, $page_name, $page_body, $ticket_pool_id, $ticket_body, $ticket_class_id, $strSectionName='', $strPageSummary='', $strURL='', $intQuestionTopicID=0, $strServiceStatusTag='', $bolHideTicketLink = 0)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_edit_page($page_id, $page_name, $page_body, $ticket_pool_id, $ticket_body, $ticket_class_id, $strSectionName, $strPageSummary, $strURL, $intQuestionTopicID, $strServiceStatusTag, $bolHideTicketLink);
}

/**
 * Wizard Get Page Move
 *
 * @param unknown $wizard_id Wizard Id
 * @param unknown $page_id   Page Id
 *
 * @return unknown
 */
function wizard_get_page_move($wizard_id, $page_id)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_get_page_move($wizard_id, $page_id);
}

/**
 * Wizard Change Parent
 *
 * @param unknown $wizard_id  Wizard Id
 * @param unknown $page_id    Page Id
 * @param unknown $parent_id  Parent Id
 * @param unknown $pwizard_id Pwizard Id
 *
 * @return unknown
 */
function wizard_change_parent($wizard_id, $page_id, $parent_id, $pwizard_id)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_change_parent($wizard_id, $page_id, $parent_id, $pwizard_id);
}

/**
 * Wizard Page Delete
 *
 * @param unknown $page_id       Page Id
 * @param unknown $kill_children Kill Children
 *
 * @return unknown
 */
function wizard_page_delete($page_id, $kill_children='false')
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_page_delete($page_id, $kill_children);
}

/**
 * Wizard Kill Children
 *
 * @param unknown $page_id Page Id
 *
 * @return unknown
 */
function wizard_kill_children($page_id)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_kill_children($page_id);
}

/**
 * Wizard Page Change Parent
 *
 * @param unknown $page_id   Page Id
 * @param unknown $parent_id Parent Id
 *
 * @return unknown
 */
function wizard_page_change_parent($page_id, $parent_id)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_page_change_parent($page_id, $parent_id);
}

/**
 * Wizard Page Get Parents
 *
 * @param unknown $page_id Page Id
 *
 * @return unknown
 */
function wizard_page_get_parents($page_id)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_page_get_parents($page_id);
}

/**
 * Wizard Page Set Parents
 *
 * @param unknown $page_id       Page Id
 * @param unknown $parents       Parents
 * @param unknown $exclude_array Exclude Array
 *
 * @return unknown
 */
function wizard_page_set_parents($page_id, $parents, $exclude_array ='')
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_page_set_parents($page_id, $parents, $exclude_array);
}

/**
 * Wizard Add Wizard
 *
 * @param unknown $name            Name
 * @param unknown $initial_page_id Initial Page Id
 * @param unknown $live            Live
 * @param unknown $creation        Creation
 *
 * @return unknown
 */
function wizard_add_wizard($name, $initial_page_id, $live='yes', $creation='')
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_add_wizard($name, $initial_page_id, $live, $creation);
}

/**
 * Wizard Parse Variables
 *
 * @param string $string          Ing
 * @param array  $array_variables Variables
 *
 * @return unknown
 */
function wizard_parse_variables($string, $array_variables)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_parse_variables($string, $array_variables);
}

/**
 * Wizard Filter Variables
 *
 * @param string $string          Ing
 * @param array  $array_variables Variables
 *
 * @return unknown
 */
function wizard_filter_variables($string, $array_variables)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_filter_variables($string, $array_variables);
}

/**
 * Wizard Page Get Visps
 *
 * @param unknown $page_id Page Id
 *
 * @return unknown
 */
function wizard_page_get_visps($page_id)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_page_get_visps($page_id);
}

/**
 * Wizard Page Set Visps
 *
 * @param unknown $page_id    Page Id
 * @param unknown $visp_array Visp Array
 *
 * @return unknown
 */
function wizard_page_set_visps($page_id, $visp_array)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_page_set_visps($page_id, $visp_array);
}

/**
 * Wizard Get All Partners
 *
 * @return unknown
 */
function wizard_get_all_partners()
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_get_all_partners();
}

/**
 * Wizard Get All Visps
 *
 * @return unknown
 */
function wizard_get_all_visps()
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_get_all_visps();
}

/**
 * Wizard Get All Visp Names
 *
 * @return unknown
 */
function wizard_get_all_visp_names()
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_get_all_visp_names();
}

/**
 * Wizard Get Ticket Pools
 *
 * @return unknown
 */
function wizard_get_ticket_pools()
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_get_ticket_pools();
}

/**
 * Wizard Copy Branch
 *
 * @param unknown $wizard_id        Wizard Id
 * @param unknown $branch_id        Branch Id
 * @param unknown $parent_id        Parent Id
 * @param unknown $parent_wizard_id Parent Wizard Id
 *
 * @return unknown
 */
function wizard_copy_branch($wizard_id, $branch_id, $parent_id, $parent_wizard_id)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_copy_branch($wizard_id, $branch_id, $parent_id, $parent_wizard_id);
}

/**
 * Wizard Get Ticket Text
 *
 * @param unknown $page_id Page Id
 *
 * @return unknown
 */
function wizard_get_ticket_text($page_id)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_get_ticket_text($page_id);
}

/**
 * Wizard Raise Ticket
 *
 * @param unknown $service_id                 Service Id
 * @param unknown $page_id                    Page Id
 * @param unknown $ticket_body                Ticket Body
 * @param unknown $email_address              Email Address
 * @param string  $strRaiseOwnQuestionPageURL Raise Own Question Page URL
 * @param unknown $sms_optin                  Sms Optin
 * @param unknown $mobile_number              Mobile Number
 *
 * @return unknown
 */
function wizard_raise_ticket($service_id, $page_id, $ticket_body, $email_address, $strRaiseOwnQuestionPageURL = '', $sms_optin = '', $mobile_number = '')
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_raise_ticket($service_id, $page_id, $ticket_body, $email_address, $strRaiseOwnQuestionPageURL, $sms_optin, $mobile_number);
}

/**
 * Wizard Get Partner Ticket Pool
 *
 * @param unknown $ticket_pool_id Ticket Pool Id
 * @param unknown $partner        Partner
 *
 * @return unknown
 */
function wizard_get_partner_ticket_pool($ticket_pool_id, $partner)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_get_partner_ticket_pool($ticket_pool_id, $partner);
}

/**
 * Wizard Browser
 *
 * @param unknown $text Text
 *
 * @return unknown
 */
function wizard_browser($text)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_browser($text);
}

/**
 * Wizard Set Status
 *
 * @param unknown $wizard_id  Wizard Id
 * @param unknown $new_status New Status
 *
 * @return unknown
 */
function wizard_set_status($wizard_id, $new_status)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_set_status($wizard_id, $new_status);
}

/**
 * Wizard Set Ticket Pool
 *
 * @param int $intPageId       Page Id
 * @param int $intTicketPoolId Ticket Pool Id
 *
 * @return unknown
 */
function WizardSetTicketPool($intPageId, $intTicketPoolId)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->WizardSetTicketPool($intPageId, $intTicketPoolId);
}

/**
 * Wizard Get All Children
 *
 * @param int $intParentId Parent Id
 *
 * @return unknown
 */
function WizardGetAllChildren($intParentId)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->WizardGetAllChildren($intParentId);
}

/**
 * Wizard Set Child Ticket Pools
 *
 * @param int $intParentId Parent Id
 * @param int $intFromPool From Pool
 * @param int $intToPool   To Pool
 *
 * @return unknown
 */
function WizardSetChildTicketPools($intParentId, $intFromPool, $intToPool)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->WizardSetChildTicketPools($intParentId, $intFromPool, $intToPool);
}

/**
 * Wizard Validate Ticket Pools
 *
 * @param int $intSpecificTeamId     Specific Team Id
 * @param int $intTeamIdToUseInstead Team Id To Use Instead
 *
 * @return unknown
 */
function WizardValidateTicketPools($intSpecificTeamId = 0, $intTeamIdToUseInstead = 1)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->WizardValidateTicketPools($intSpecificTeamId, $intTeamIdToUseInstead);
}

/**
 * Wizard Export Wizard
 *
 * @param int $intWizardId Wizard Id
 *
 * @return unknown
 */
function WizardExportWizard($intWizardId)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->WizardExportWizard($intWizardId);
}

/**
 * Wizard Import Wizard
 *
 * @param array $arrWizardFromFile Wizard From File
 * @param int   $intWizardId       Wizard Id
 *
 * @return unknown
 */
function WizardImportWizard($arrWizardFromFile, $intWizardId = 0)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->WizardImportWizard($arrWizardFromFile, $intWizardId);
}

/**
 * Wizard Import Wizard Update Array
 *
 * @param array  &$arrArrayElements Array Elements
 * @param int    $intOldId          Old Id
 * @param string $strField          Field
 * @param int    $intNewId          New Id
 *
 * @return unknown
 */
function WizardImportWizardUpdateArray(&$arrArrayElements, $intOldId, $strField, $intNewId)
{

    if (count($arrArrayElements) > 0) {
        foreach ($arrArrayElements as $intElementId => $arrElement) {
            if (isset($arrElement[$strField]) === true && $arrElement[$strField] == $intOldId) {
                $arrArrayElements[$intElementId][$strField] = $intNewId;
            }
        }
    }
}

/**
 * Wizard Page Set Order
 *
 * @param int $intPageID Page ID
 * @param int $intOrder  Order
 *
 * @return unknown
 */
function wizard_page_set_order($intPageID, $intOrder)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->wizard_page_set_order($intPageID, $intOrder);
}

/**
 * Get Highest Order For New Page
 *
 * @param int $intWizardID Wizard ID
 * @param int $intParentID Parent ID
 *
 * @return unknown
 */
function getHighestOrderForNewPage($intWizardID, $intParentID)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->getHighestOrderForNewPage($intWizardID, $intParentID);
}

/**
 * Get Question Topic Display Name By ID
 *
 * @param int $intQuestionTopicID Question Topic ID
 *
 * @return unknown
 */
function getQuestionTopicDisplayNameByID($intQuestionTopicID)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->getQuestionTopicDisplayNameByID($intQuestionTopicID);
}

/**
 * Get Question Topic IDBy Display Name
 *
 * @param string $strDisplayName Display Name
 * @param bool   $bolActiveOnly  Active Only
 *
 * @return unknown
 */
function getQuestionTopicIDByDisplayName($strDisplayName, $bolActiveOnly=true)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->getQuestionTopicIDByDisplayName($strDisplayName, $bolActiveOnly);
}

/**
 * Insert Question Topic
 *
 * @param string $strDisplayName Display Name
 *
 * @return unknown
 */
function insertQuestionTopic($strDisplayName)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->insertQuestionTopic($strDisplayName);
}

/**
 * Get Or Create Question Topic By Display Name
 *
 * @param string $strDisplayName Display Name
 *
 * @return unknown
 */
function getOrCreateQuestionTopicByDisplayName($strDisplayName)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->getOrCreateQuestionTopicByDisplayName($strDisplayName);
}

/**
 * Get All Question Topics
 *
 * @param bool $bolActiveOnly Active Only
 *
 * @return unknown
 */
function getAllQuestionTopics($bolActiveOnly=true)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->getAllQuestionTopics($bolActiveOnly);
}

/**
 * Wizard Set Member Page Links
 *
 * @param unknown $isp Isp
 *
 * @return unknown
 */
function WizardSetMemberPageLinks($isp)
{

    $objSupportWizard = Lib_SupportWizard::singleton();
    return $objSupportWizard->WizardSetMemberPageLinks($isp);
}

