<?php
/**
* HardwareBundleSupplier.class.php
*
* @package application_apis
* @subpackage HardwareBundle
*/
/**
 * HardwareBundleSupplier Class
 *
 * @package application_apis
 * @subpackage HardwareBundle
 */
class HardwareBundleSupplier extends HardwareBundleBase
{
    var $m_arrStatuses               = array();
    var $m_arrOrderStatusEregResults = array();

    /**
     * HardwareBundleSupplier
     *
     * @return void
     */
    function HardwareBundleSupplier()
    {
        // Call base constructor
        $this->HardwareBundleBase();
    }

    // Returns a hardware bundle of the correct class type for the component ID.
    /**
     * Returns a hardware bundle of the correct class type for the component ID.
     *
     * @param int $intComponentID ComponentID
     * @param int $intRMA         RMA
     *
     * @return unknown
     */
    function GetHardwareBundleByComponent($intComponentID,$intRMA=0)
    {
        $hConnection = get_named_connection('product');

        // First get the class prefix.
        $strQuery = 'SELECT php_class_prefix '.
             'FROM products.hardware_suppliers AS hs '.
             'JOIN products.hardware_supplier_products AS hsp '.
               'ON hs.hardware_supplier_id=hsp.hardware_supplier_id '.
             'JOIN products.hardware_bundle_config_items AS hbci '.
               'ON hsp.hardware_supplier_product_id=hbci.hardware_supplier_product_id '.
             'JOIN products.component_hardware_bundle_config AS chbc '.
               'ON chbc.component_hardware_bundle_config_id=hbci.component_hardware_bundle_config_id '.
             'JOIN userdata.components AS c '.
               'ON chbc.service_component_id=c.component_type_id '.
            'WHERE c.component_id='.$intComponentID;

        $hResult = mysql_query($strQuery, $hConnection)
            or report_error(__FILE__, __LINE__, mysql_error($hConnection));

        if (mysql_num_rows($hResult) == 0) {
            $strClassName = 'HardwareBundle';
        } else {
            $strClassName = mysql_result($hResult, 0, 0) . 'HardwareBundle';
        }

        mysql_free_result($hResult);

        // Create the required object
        $HardwareBundle = new $strClassName();

        $HardwareBundle->GetByComponentID($intComponentID, $intRMA);

        return $HardwareBundle;

    } // function GetHardwareBundleByComponent($intComponentID)

    /**
     * Get the ID of the passed status tag
     *
     * @param string $strStatusTag StatusTag
     *
     * @return boolean
     */
    function GetStatusID($strStatusTag)
    {
        if (!isset($this->m_arrStatuses[$strStatusTag])) {
            $hConnection = get_named_connection('product');

            $strQuery = 'SELECT hos.hardware_order_status_id     AS intStatusID, ' .
                              ' hos.display_name                 AS strDisplayName, ' .
                              ' hos.description                  AS strDescription, ' .
                              ' hos.portal_description           AS strPortalDescription ' .
                          'FROM hardware_order_statuses          AS hos, ' .
                              ' hardware_supplier_order_statuses AS hsos ' .
                         'WHERE hos.hardware_order_status_id=hsos.hardware_order_status_id ' .
                           "AND hos.tag='$strStatusTag'";

            $hResult = mysql_query($strQuery, $hConnection)
                or report_error(__FILE__, __LINE__, mysql_error($hConnection));

            if (mysql_num_rows($hResult) == 0) {
                // Either the status tag doesn't exist, or this supplier can't use it.
                return false;
            }

            $this->m_arrStatuses[$strStatusTag] = mysql_fetch_assoc($hResult);
        }

        return $this->m_arrStatuses[$strStatusTag]['intStatusID'];

    } // function GetStatusID($strStatusTag)

    /**
     * Get a list of statuses that are recognised by this supplier
     *
     * @return multitype:
     */
    function GetStatuses()
    {
        // Have we cached it already?
        if (count($this->m_arrStatuses) == 0) {
            $hConnection = get_named_connection('product');

            $intSupplierID = $this->GetSupplierID();

            $strQuery = 'SELECT hos.hardware_order_status_id AS intID, ' .
                              ' hos.tag                      AS strTag, ' .
                              ' hos.display_name             AS strDisplayName, ' .
                              ' hos.description              AS strDescription, ' .
                              ' hos.description              AS strPortalDescription, ' .
                              ' hsos.order_can_be_cancelled  AS binCanBeCancelled ' .
                          'FROM hardware_order_statuses          AS hos, ' .
                              ' hardware_supplier_order_statuses AS hsos ' .
                         'WHERE hos.hardware_order_status_id=hsos.hardware_order_status_id ' .
                           "AND hsos.hardware_supplier_id='$intSupplierID'" .
                        ' ORDER BY hos.intReportingOrder ';

            $hResult = mysql_query($strQuery, $hConnection)
                or report_error(__FILE__, __LINE__, mysql_error($hConnection));

            while ($rRow = mysql_fetch_assoc($hResult)) {
                $this->m_arrStatuses[] = $rRow;
            }
        }

        return $this->m_arrStatuses;

    } // function GetStatuses

    /**
     * Get the billing address for orders. This defaults to PlusNet's
     * address, which is likely to be what is wanted in all cases.
     * However, if a particular supplier wants something different, just
     * override this function in the supplier's classes.
     *
     * @return multitype:string
     */
    function GetBillingAddress()
    {
        return array('house'    => 'The Balance',
                     'street'   => '2 Pinfold Street',
                     'town'     => 'Sheffield',
                     'postcode' => 'S1 2GU');
    }

    /**
     * Send pending orders for a supplier
     *
     * @param int $intConfigHardwareBundleId ConfigHardwareBundleId
     *
     * @return void|boolean
     */
    function SendPendingOrders($intConfigHardwareBundleId = null)
    {
        $hConnection = get_named_connection('userdata');
        //P49875 - changing query to faster one (changing AND's to join's)
        // needed clean up in other places in this file!
        $strQuery =  'SELECT DISTINCT c.component_id AS intComponentID '
                  .   ' FROM userdata.hardware_bundle_items   AS hbi '
                  .   ' JOIN userdata.config_hardware_bundles AS chb '
                  .     ' ON chb.config_hardware_bundle_id = hbi.config_hardware_bundle_id '
                  .   ' JOIN userdata.components AS c '
                  .     ' ON c.component_id = chb.component_id '
                  .   ' JOIN userdata.services AS s '
                  .     ' ON s.service_id = c.service_id '
                  .   ' JOIN products.hardware_order_statuses AS hos '
                  .     ' ON chb.overall_hardware_order_status_id = hos.hardware_order_status_id '
                  .   ' JOIN products.hardware_supplier_products AS hsp '
                  .     ' ON hbi.hardware_supplier_product_id = hsp.hardware_supplier_product_id '
                  .  ' WHERE hos.tag IN ("awaiting_ordering", "awaiting_rma") '
                  .    ' AND s.username NOT LIKE "ztest%" '
                  .    ' AND hsp.hardware_supplier_id = "' . $this->m_intSupplierID . '" '
                  .    ' AND c.status IN ("queued-activate", "queued-reactivate", "active")  '
                  .    ' AND s.status IN ("queued-activate","queued-reactivate","active",'
                  .'"queued-deactivate", "unconfigured" ,"invalid","presignup") ';

        if (!empty($intConfigHardwareBundleId)) {
            $strQuery .= sprintf(
                ' AND chb.config_hardware_bundle_id = %d ', mysql_real_escape_string($intConfigHardwareBundleId)
            );
        }

        // Create a hardware bundle object for each pending order
        $strClassName = $this->GetClassPrefix() . 'HardwareBundle';

        $arrHardwareBundles = array();

        $hResult = mysql_query($strQuery, $hConnection)
            or report_error(__FILE__, __LINE__, mysql_error($hConnection));

        while ($rRow = mysql_fetch_assoc($hResult)) {
            // Create the component
            $HardwareBundle = new $strClassName();
            $HardwareBundle->GetByComponentID($rRow['intComponentID']);

            $arrHardwareBundles[] = $HardwareBundle;
        }

        // Do we have any orders to send?
        if (count($arrHardwareBundles) > 0) {
            // The derived class now sends the orders
            return $this->SendOrders($arrHardwareBundles);
        } else {
            // Return true (we've successfully done nothing)
            return true;
        }

    } // function SendPendingOrders

    /**
     * Function that *MUST* be subclassed. Should run through the array
     * provided and send an order (or orders) for the bundles in the array.
     * If the derived supplier doesn't support pending orders the function
     * should log an error because this function will only be called if
     * there are pending orders (i.e. something will be screwed).
     *
     * @param array $arrHardwareBundles HardwareBundles
     *
     * @return void
     */
    function SendOrders($arrHardwareBundles)
    {
        report_error(__FILE__, __LINE__, 'You must override HardwareBundleSupplier::SendOrders()!');
    }

    /**
     * Virtual function: must be overridden by derived class
     *
     * @param string $strOrderString
     *
     * @return void
     */
    function PlaceOrder($strOrderString)
    {
        report_error(__FILE__, __LINE__, 'You must override HardwareBundleSupplier::PlaceOrder()!');
    }

    /**
     * Virtual function: must be overridden by derived class
     *
     * @return void
     */
    function PollForOrderAcknowledgements()
    {

    }

    /**
     * Get updates for all orders associated with this supplier
     *
     * @param string $strNotOlderThan           date ordered
     * @param int    $intConfigHardwareBundleId ConfigHardwareBundleId
     *
     * @return boolean
     */
    function PollForOrderUpdates($strNotOlderThan = null, $intConfigHardwareBundleId = null)
    {
        if (null == $strNotOlderThan) {
            //Poll dispatched orders that were dispatched up to 6 days ago(for the tracking number)
            $strNotOlderThan = date("Y-m-d 00:00:00", (mktime() - (60 * 60 * 24 * 6)));
        }

        $hConnection = get_named_connection('product_reporting');

        // Get a list of components that are needing updating
        $strQuery = 'SELECT DISTINCT c.component_id          AS intComponentID ' .
                      'FROM hardware_suppliers               AS hs, ' .
                          ' hardware_supplier_products       AS hsp, ' .
                          ' hardware_bundle_config_items     AS hbci, ' .
                          ' component_hardware_bundle_config AS chbc, ' .
                          ' service_components               AS sc, ' .
                          ' userdata.components              AS c, ' .
                          ' userdata.config_hardware_bundles AS chb, ' .
                          ' hardware_order_statuses          AS hos, ' .
                          ' userdata.services                AS s ' .
                     'WHERE hs.hardware_supplier_id=hsp.hardware_supplier_id ' .
                       'AND hsp.hardware_supplier_product_id=hbci.hardware_supplier_product_id ' .
                       'AND hbci.component_hardware_bundle_config_id=chbc.component_hardware_bundle_config_id ' .
                       'AND chbc.service_component_id=sc.service_component_id ' .
                       'AND sc.service_component_id=c.component_type_id ' .
                       'AND c.component_id=chb.component_id ' .
                       'AND chb.overall_hardware_order_status_id=hos.hardware_order_status_id ' .
                       'AND c.service_id = s.service_id ' .
                       "AND hs.php_class_prefix='{$this->m_strClassPrefix}' " .
                       "AND s.username NOT LIKE 'ztest%' " .
                       'AND ((c.status IN ("queued-activate", "queued-reactivate") ' .
                              'AND hos.tag NOT IN ("dispatched", "rejected", "not_ordered", "awaiting_ordering")) '.
                           'OR (c.status IN ("active", "queued-activate", "queued-reactivate") ' .
                              'AND hos.tag IN("dispatched","awaiting_processing") '.
                              "AND when_ordered > '{$strNotOlderThan}'))";

        if (!empty($intConfigHardwareBundleId)) {
            $strQuery .= sprintf(
                ' AND chb.config_hardware_bundle_id = %d ', mysql_real_escape_string($intConfigHardwareBundleId)
            );
        }


        $hResult = mysql_query($strQuery, $hConnection)
            or report_error(__FILE__, __LINE__, mysql_error($hConnection));

        $strClassName = $this->m_strClassPrefix . 'HardwareBundle';

        $intErrCount = 0;
        $strExceptionText = '';

        if (mysql_num_rows($hResult) == 0) {

            $this->setMessage(date('H:i:s')." No Orders to update\n");
            return true;
        }

        while ($rRow = mysql_fetch_assoc($hResult)) {
            $intComponentID = $rRow['intComponentID'];

            // Create the bundle component
            $HardwareBundle = new $strClassName();

            // Initialise the component
            $HardwareBundle->GetByComponentID($intComponentID);

            // Ask the component to poll for an update
            try
            {
                $this->setMessage(date('H:i:s')." Updating Component Id: ".$intComponentID."\n");

                $HardwareBundle->PollForOrderUpdate();

                $this->setMessage($HardwareBundle->getMessage());
            }
            catch (Exception $e)
            {
                //P1 fix - Problem 65645
                // Get the text, dump to log, and save in case supplier has died
                $strErrText =
                    "Exception $intErrCount: " .  $e->getMessage() . "\nStacktrace:\n" . $e->getTraceAsString() . "\n";

                $strExceptionText += $strErrText;
                error_log($strErrText);

                // If too many errors have occurred, bail out
                $intErrCount++;
                if ($intErrCount > 5) {
                    throw new Exception(
                        "5 hardware $strClassName order updates failed: aborting. " .
                        "Full error details:\n" + $strExceptionText
                    );
                }
            }
        }
    } // function PollForOrderUpdates()

    /**
     * Get updates for all RMAs associated with this supplier
     *
     * @return void
     */
    function PollForRmaUpdates()
    {
        $dbhConnection = get_named_connection('userdata');

        $strClassPrefix = $this->GetClassPrefix();

        // Get a list of components that are needing updating
        $strQuery = 'SELECT DISTINCT hrp.usiHardwareRmaProcessID ' .
                      'FROM tblHardwareRmaProcess   AS hrp, ' .
                          ' config_hardware_bundles AS chb, ' .
                          ' products.hardware_order_statuses AS hos, ' .
                          ' components AS c, ' .
                          ' services AS s, ' .
                          ' hardware_bundle_items AS hbi, ' .
                          ' products.hardware_supplier_products AS hsp, ' .
                          ' products.hardware_suppliers AS hs ' .
                     'WHERE s.service_id=c.service_id ' .
                       'AND c.component_id=chb.component_id ' .
                       'AND chb.config_hardware_bundle_id=hrp.usiConfigHardwareBundleID ' .
                       'AND hrp.usiHardwareorderStatusID=hos.hardware_order_status_id ' .
                       'AND chb.config_hardware_bundle_id=hbi.config_hardware_bundle_id ' .
                       'AND hbi.hardware_supplier_product_id=hsp.hardware_supplier_product_id ' .
                       'AND hsp.hardware_supplier_id=hs.hardware_supplier_id ' .
                       'AND hos.tag IN ("rma_in_progress", "delivered") ' .
                       'AND hrp.dtmEnd IS NULL ' .
                       "AND hs.php_class_prefix='$strClassPrefix' " .
                       'AND hrp.dtmStart > (NOW() - INTERVAL 1 MONTH)';

        $recResult = mysql_query($strQuery, $dbhConnection)
            or report_error(__FILE__, __LINE__, mysql_error($dbhConnection));

        $strClassName = $strClassPrefix . 'HardwareRmaProcess';

        while ($recRow = mysql_fetch_assoc($recResult)) {
            $intRmaID = $recRow['usiHardwareRmaProcessID'];

            // Create the bundle component
            $objRma = new $strClassName();

            // Initialise the component
            $objRma->GetByID($intRmaID);

            // Ask the component to poll for an update
            $objRma->PollForUpdate();
        }

    } // function PollForRmaUpdates()

    /**
     * FetchLatestRmaStatus
     *
     * @param object $objRma Rma
     *
     * @return void
     */
    function FetchLatestRmaStatus($objRma)
    {
        report_error(__FILE__, __LINE__, 'You must override HardwareBundleSupplier::FetchLatestRmaStatus()!');
    }

    /**
     * Translate the order status reponse returned from the supplier into a
     * status native to our system.
     *
     * @param string $strStatusResponse StatusResponse
     *
     * @return multitype:|boolean
     */
    function DecodeOrderStatusResponse($strStatusResponse)
    {
        // First see if we have this result already
        if (isset($this->m_arrOrderStatusEregResults[$strStatusResponse])) {
            return $this->m_arrOrderStatusEregResults[$strStatusResponse];
        }

        // The following query will return all statuses that match the
        // supplier and the message returned from the supplier. This
        // information is stored, so each return message will only be
        // queried for one per use of the object.
        $intSupplierID = $this->GetSupplierID();

        $hConnection = get_named_connection('product_reporting');

        $strQuery = 'SELECT hos.tag                              AS strTag ' .
                      'FROM hardware_order_statuses              AS hos, ' .
                          ' hardware_supplier_order_statuses     AS hsos, ' .
                          ' hardware_supplier_order_status_eregs AS hsose ' .
                     'WHERE hos.hardware_order_status_id=hsos.hardware_order_status_id ' .
                       'AND hsos.hardware_supplier_order_status_id=hsose.hardware_supplier_order_status_id ' .
                       "AND hsos.hardware_supplier_id='$intSupplierID' " .
                       "AND '$strStatusResponse' RLIKE hsose.return_value_ereg";

        // What have we found?
        $hResult = mysql_query($strQuery, $hConnection)
            or report_error(__FILE__, __LINE__, mysql_error($hConnection));

        $intRowCount = mysql_num_rows($hResult);

        if ($intRowCount == 0) {
            // If we've got here we haven't found a match. Help!
            error_log(
                "HardwareBundleSupplier::DecodeOrderStatusResponse - ".
                "Couldn't find  status to match the response from the supplier: '$strStatusResponse'"
            );

            $strRetVal = false;

        } else if ($intRowCount > 1) {
            // We've got more than one, which is not right, but we can
            // guess and just take the first one. Log and error anyway since this shouldn't happen.
            // Don't cache this value because it's not right.
            error_log(
                "HardwareBundleSupplier::DecodeOrderStatusResponse - ".
                "Found more than one status to match the response ('$strStatusResponse') ".
                "from the supplier: '$strStatusResponse'"
            );

            $strRetVal = mysql_result($hResult, 0, 0);
        } else {
            // Cool, we have our status.
            $strRetVal = mysql_result($hResult, 0, 0);

            // Cache the result for later
            $this->m_arrOrderStatusEregResults[$strStatusResponse] = $strRetVal;
        }

        mysql_free_result($hResult);

        return $strRetVal;

    } // function DecodeOrderStatusResponse

    //
    // Reporting Functions
    //
    // Gets a summary
    /**
     * Gets a summary
     *
     * @param array $arrCriteria Criteria
     *
     * @return Ambigous <multitype:, unknown>
     */
    function GetBundleStatusCountsForPeriod($arrCriteria)
    {
        // Get the category types available
        $arrTypes = get_category_types();

        // Get a list of the possible statuses
        $arrOrderStatuses = $this->GetStatuses();

        $arrQueries = array();

        // Prepare the return array
        $arrResults = array();

        $hConnection = get_named_connection('userdata_reporting');

        // Ignore RMA statuses at this point
        $strQuery = 'SELECT sd.type  AS strType, ' .
                          ' hos.tag  AS strStatus, ' .
                          ' COUNT(DISTINCT(chb.config_hardware_bundle_id)) AS intCount ' .
                      'FROM products.service_definitions     AS sd, ' .
                          ' products.hardware_order_statuses AS hos, ' .
                          ' config_hardware_bundles          AS chb, ' .
                          ' components                       AS c, ' .
                          ' services                         AS s, ' .
                          ' hardware_bundle_items            AS hbi, '.
                          ' products.hardware_supplier_products AS hsp, '.
                          ' products.hardware_suppliers      AS hs '.
                     'WHERE sd.service_definition_id=s.type ' .
                       'AND s.service_id=c.service_id ' .
                       'AND c.component_id=chb.component_id ' .
                       'AND chb.overall_hardware_order_status_id=hos.hardware_order_status_id ' .
                       'AND chb.config_hardware_bundle_id = hbi.config_hardware_bundle_id '.
                       'AND hbi.hardware_supplier_product_id = hsp.hardware_supplier_product_id '.
                       'AND hsp.hardware_supplier_id = hs.hardware_supplier_id '.
                       'AND s.username NOT LIKE "ztest%" ' .
                       'AND s.status IN("active","queued-activate") ' .
                       'AND c.status IN ("active", "queued-activate", "queued-reactivate") ' .
                       'AND hos.tag NOT IN ("rma_in_progress", "rma_complete")' .
                       "AND hs.tag = '{$this->m_strSupplierTag}' ";


        // Process criteria and append to query as appropriate
        if (isset($arrCriteria['StartDate']) || isset($arrCriteria['EndDate'])) {
            $arrQueryChunks = array();

            if (isset($arrCriteria['StartDate'])) {
                $datStartDate = date(MYSQL_DATETIME, $arrCriteria['StartDate']);

                $arrQueryChunks[] = "chb.when_ordered > '$datStartDate'";
            }

            if (isset($arrCriteria['EndDate'])) {
                $datEndDate = date(MYSQL_DATETIME, $arrCriteria['EndDate']);

                $arrQueryChunks[] = "chb.when_ordered < '$datEndDate'";
            }

            $strCollatedChunks = implode(' AND ', $arrQueryChunks);

            $strQuery .= "AND (($strCollatedChunks) OR chb.when_ordered='0000-00-00 00:00:00')";
        }

        $strQuery .= ' GROUP BY sd.type, hos.tag '.
                     ' ORDER BY hos.intReportingOrder ';

        $arrQueries[] = $strQuery;


        // "RMA in progress"
        $strQuery = 'SELECT sd.type  AS strType, ' .
                          ' hos.tag  AS strStatus, ' .
                          ' COUNT(DISTINCT(chb.config_hardware_bundle_id)) AS intCount ' .
                      'FROM products.service_definitions     AS sd, ' .
                          ' products.hardware_order_statuses AS hos, ' .
                          ' config_hardware_bundles          AS chb, ' .
                          ' components                       AS c, ' .
                          ' services                         AS s, ' .
                          ' tblHardwareRmaProcess            AS hrp, ' .
                          ' hardware_bundle_items            AS hbi, '.
                          ' products.hardware_supplier_products AS hsp, '.
                          ' products.hardware_suppliers      AS hs '.
                     'WHERE sd.service_definition_id=s.type ' .
                       'AND s.service_id=c.service_id ' .
                       'AND c.component_id=chb.component_id ' .
                       'AND chb.config_hardware_bundle_id=hrp.usiConfigHardwareBundleID ' .
                       'AND hrp.usiHardwareOrderstatusID=hos.hardware_order_status_id ' .
                       'AND chb.config_hardware_bundle_id = hbi.config_hardware_bundle_id '.
                       'AND hbi.hardware_supplier_product_id = hsp.hardware_supplier_product_id '.
                       'AND hsp.hardware_supplier_id = hs.hardware_supplier_id '.
                       'AND s.username NOT LIKE "ztest%" ' .
                       'AND s.status IN("active","queued-activate") ' .
                       'AND hos.tag="rma_in_progress" ' .
                       'AND c.status IN ("active", "queued-activate", "queued-reactivate") '.
                       "AND hs.tag = '{$this->m_strSupplierTag}' ";

        // Process criteria and append to query as appropriate
        if (isset($arrCriteria['StartDate']) || isset($arrCriteria['EndDate'])) {
            $arrQueryChunks = array();

            if (isset($arrCriteria['StartDate'])) {
                $datStartDate = date(MYSQL_DATETIME, $arrCriteria['StartDate']);

                $arrQueryChunks[] = "chb.when_ordered > '$datStartDate'";
            }

            if (isset($arrCriteria['EndDate'])) {
                $datEndDate = date(MYSQL_DATETIME, $arrCriteria['EndDate']);

                $arrQueryChunks[] = "chb.when_ordered < '$datEndDate'";
            }

            $strCollatedChunks = implode(' AND ', $arrQueryChunks);

            $strQuery .= "AND (($strCollatedChunks)) ";
        }

        $strQuery .= ' GROUP BY sd.type, hos.tag';
                     ' ORDER BY hos.intReportingOrder ';

        $arrQueries[] = $strQuery;

        // "RMA Complete"
        $strQuery = 'SELECT sd.type  AS strType, ' .
                          ' hos.tag  AS strStatus, ' .
                          ' COUNT(DISTINCT(chb.config_hardware_bundle_id)) AS intCount ' .
                      'FROM products.service_definitions     AS sd, ' .
                          ' products.hardware_order_statuses AS hos, ' .
                          ' config_hardware_bundles          AS chb, ' .
                          ' components                       AS c, ' .
                          ' services                         AS s, ' .
                          ' tblHardwareRmaProcess            AS hrp, ' .
                          ' hardware_bundle_items            AS hbi, '.
                          ' products.hardware_supplier_products AS hsp, '.
                          ' products.hardware_suppliers      AS hs '.
                     'WHERE sd.service_definition_id=s.type ' .
                       'AND s.service_id=c.service_id ' .
                       'AND c.component_id=chb.component_id ' .
                       'AND chb.config_hardware_bundle_id=hrp.usiConfigHardwareBundleID ' .
                       'AND hrp.usiHardwareOrderstatusID=hos.hardware_order_status_id ' .
                       'AND chb.config_hardware_bundle_id = hbi.config_hardware_bundle_id '.
                       'AND hbi.hardware_supplier_product_id = hsp.hardware_supplier_product_id '.
                       'AND hsp.hardware_supplier_id = hs.hardware_supplier_id '.
                       'AND s.username NOT LIKE "ztest%" ' .
                       'AND s.status IN("active","queued-activate") ' .
                       'AND hos.tag="rma_complete" ' .
                       'AND c.status IN ("active", "queued-activate", "queued-reactivate") ' .
                       "AND hs.tag = '{$this->m_strSupplierTag}' ";

        // Process criteria and append to query as appropriate
        if (isset($arrCriteria['StartDate']) || isset($arrCriteria['EndDate'])) {
            $arrQueryChunks = array();

            if (isset($arrCriteria['StartDate'])) {
                $datStartDate = date(MYSQL_DATETIME, $arrCriteria['StartDate']);

                $arrQueryChunks[] = "chb.when_ordered > '$datStartDate'";
            }

            if (isset($arrCriteria['EndDate'])) {
                $datEndDate = date(MYSQL_DATETIME, $arrCriteria['EndDate']);

                $arrQueryChunks[] = "chb.when_ordered < '$datEndDate'";
            }

            $strCollatedChunks = implode(' AND ', $arrQueryChunks);

            $strQuery .= "AND (($strCollatedChunks))";
        }

        $strQuery .= ' GROUP BY sd.type, hos.tag';
                     ' ORDER BY hos.intReportingOrder ';

        $arrQueries[] = $strQuery;

        foreach ($arrQueries as $strQuery) {
            $hResult = mysql_query($strQuery, $hConnection)
                or report_error(__FILE__, __LINE__, mysql_error($hConnection));

            // Collate the results
            while ($rRow = mysql_fetch_assoc($hResult)) {
                if (!isset($arrResults[$rRow['strType']])) {
                    // Initialise statuses for each one
                    foreach ($arrOrderStatuses as $rOrderStatus) {
                        $arrResults[$rRow['strType']][$rOrderStatus['strTag']] = 0;
                    }
                }

                $arrResults[$rRow['strType']][$rRow['strStatus']] += $rRow['intCount'];
            }

            mysql_free_result($hResult);
        }

        return $arrResults;

    } // function GetBundleStatusCountsForPeriod

    /**
     * Gets a list of accounts that match the passed criteria
     *
     * @param array $arrCriteria Criteria
     *
     * @return multitype:unknown
     */
    function GetBundleStatusAccounts($arrCriteria)
    {
        // Prepare the return array
        $arrResults = array();

        $hConnection = get_named_connection('userdata_reporting');

        $strQuery = 'SELECT chb.config_hardware_bundle_id AS intHardwareID, ' .
                          ' s.username                    AS strUsername, ' .
                          ' s.service_id                  AS intServiceID, ' .
                          ' sd.name                       AS strAccountType, ' .
                          ' sc.name                       AS strHardwareType, ' .
                          ' hos.display_name              AS strStatusDisplayName, ' .
                          ' s.status                      AS strServiceStatus, ' .
                          ' aid.date_active               AS dteDateActive ' .
                      'FROM products.service_definitions     AS sd, ' .
                          ' products.hardware_order_statuses AS hos, ' .
                          ' products.service_components      AS sc, ' .
                          ' config_hardware_bundles          AS chb, ' .
                          ' components                       AS c, ' .
                          ' services                         AS s, ' .
                          ' hardware_bundle_items            AS hbi, '.
                          ' products.hardware_supplier_products AS hsp, '.
                          ' products.hardware_suppliers      AS hs '.
                 'LEFT JOIN adsl.install_diary               AS aid ' .
                        'ON aid.service_id=s.service_id ' .
                     'WHERE sd.service_definition_id=s.type ' .
                       'AND s.service_id=c.service_id ' .
                       'AND c.component_id=chb.component_id ' .
                       'AND c.component_type_id=sc.service_component_id ' .
                       'AND chb.overall_hardware_order_status_id=hos.hardware_order_status_id ' .
                       'AND chb.config_hardware_bundle_id = hbi.config_hardware_bundle_id '.
                       'AND hbi.hardware_supplier_product_id = hsp.hardware_supplier_product_id '.
                       'AND hsp.hardware_supplier_id = hs.hardware_supplier_id '.
                       'AND s.username NOT LIKE "ztest%"' .
                       'AND s.status IN("active","queued-activate") ' .
                       'AND c.status IN ("active", "queued-activate", "queued-reactivate") '.
                       "AND hs.tag = '{$this->m_strSupplierTag}' ";

        // Process criteria and append to query as appropriate
        if (isset($arrCriteria['StartDate']) || isset($arrCriteria['EndDate'])) {
            $arrQueryChunks = array();

            if (isset($arrCriteria['StartDate'])) {
                $datStartDate = date(MYSQL_DATETIME, $arrCriteria['StartDate']);

                $arrQueryChunks[] = "chb.when_ordered > '$datStartDate'";
            }

            if (isset($arrCriteria['EndDate'])) {
                $datEndDate = date(MYSQL_DATETIME, $arrCriteria['EndDate']);

                $arrQueryChunks[] = "chb.when_ordered < '$datEndDate'";
            }

            $strCollatedChunks = implode(' AND ', $arrQueryChunks);

            $strQuery .= "AND (($strCollatedChunks) OR chb.when_ordered='0000-00-00 00:00:00')";
        }

        // Optional criteria
        if (isset($arrCriteria['StatusTag'])) {
            $strStatusTag = addslashes($arrCriteria['StatusTag']);

            $strQuery .= " AND hos.tag='$strStatusTag'";
        } else if (!isset($arrCriteria['binIncludeDispatched'])) {
            $strQuery .= ' AND hos.tag NOT IN ("dispatched","delivered") ';
        }

        if (isset($arrCriteria['CategoryName'])) {
            $strCategoryName = addslashes($arrCriteria['CategoryName']);

            $strQuery .= " AND sd.type='$strCategoryName'";
        }

        // Tack on the GROUP BY clause
        $strQuery .= ' GROUP BY chb.config_hardware_bundle_id';

        $hResult = mysql_query($strQuery, $hConnection)
            or report_error(__FILE__, __LINE__, mysql_error($hConnection));

        while ($rRow = mysql_fetch_assoc($hResult)) {
            $arrResults[] = $rRow;
        }

        return $arrResults;

    } // function GetBundleStatusAccounts

    /**
     * Gets a Summary of RMAs that match the passed criteria
     *
     * @param array $arrCriteria Criteria
     *
     * @return array
     */
    function GetRmaStatusSummary($arrCriteria)
    {
        // Prepare the return array
        $arrResults = array();

        $dbhConnection = get_named_connection('product_reporting');

        // Product types
        $arrProductTypes = array('residential' => array('intCount'    => 0,
                                                        'arrAccounts' => array()),
                                 'business'    => array('intCount'    => 0,
                                                        'arrAccounts' => array()));

        // If the criteria specified a particular product type, remove the
        // others from the array.
        if (isset($arrCriteria['CategoryName'])) {
            foreach (array_keys($arrProductTypes) as $strHandle) {
                if ($arrCriteria['CategoryName'] != $strHandle) {

                    unset($arrProductTypes[$strHandle]);
                }
            }
        }

        $arrResults['arrProductTypes'] = $arrProductTypes;

        // Get RMA Types
        $strQuery = 'SELECT usiHardwareRmaTypeID, ' .
                          ' vchHandle      AS strHandle, ' .
                          ' vchDisplayName AS strDisplayName ' .
                      'FROM vblHardwareRmaType ' .
                     'WHERE dtmWhenRemoved IS NULL';

        $resResult = mysql_query($strQuery, $dbhConnection)
            or report_error(__FILE__, __LINE__, mysql_error($dbhConnection));

        $arrTypes = array();

        while ($recRow = mysql_fetch_assoc($resResult)) {
            $arrTypes[$recRow['strHandle']] = $recRow;
        }

        mysql_free_result($resResult);

        $arrResults['arrRmaTypes'] = $arrTypes;

        // Get Hardware types
        $strQuery = 'SELECT hpc_child.hardware_product_class_id AS intClassID, ' .
                          ' hsp.hardware_supplier_product_id    AS intProductID, ' .
                          ' hpc_child.tag                       AS strClassHandle, ' .
                          ' hpc_child.display_name              AS strClassDisplayName, ' .
                          ' hsp.display_name                    AS strProductDisplayName ' .
                      'FROM hardware_product_classes        AS hpc_parent, ' .
                          ' hardware_product_classes_lookup AS hpcl, ' .
                          ' hardware_product_classes        AS hpc_child, ' .
                          ' hardware_supplier_products      AS hsp, ' .
                          ' products.hardware_suppliers         AS hs '.
                     'WHERE hpc_parent.hardware_product_class_id=hpcl.ancestor_id ' .
                       'AND hpcl.hardware_product_class_id=hpc_child.hardware_product_class_id ' .
                       'AND hpc_child.hardware_product_class_id=hsp.hardware_product_class_id ' .
                       'AND hsp.hardware_supplier_id = hs.hardware_supplier_id '.
                       'AND hsp.when_removed IS NULL ' .
                       "AND hpc_parent.tag='terminal_equipment' ".
                       "AND hs.tag = '{$this->m_strSupplierTag}' ";

        $resResult = mysql_query($strQuery, $dbhConnection)
            or report_error(__FILE__, __LINE__, mysql_error($dbhConnection));

        $arrClasses = array();

        while ($recRow = mysql_fetch_assoc($resResult)) {
            $arrClasses[$recRow['strClassHandle']] = $recRow;
        }

        mysql_free_result($resResult);

        $arrResults['arrClasses'] = $arrClasses;

        // Loop through and tot up the accounts

        $strQuery = 'SELECT chb.config_hardware_bundle_id    AS intHardwareID, ' .
                          ' s.username                       AS strUsername, ' .
                          ' s.service_id                     AS intServiceID, ' .
                          ' sd.name                          AS strAccountType, ' .
                          ' sc.name                          AS strHardwareType, ' .
                          ' hos.display_name                 AS strStatusDisplayName, ' .
                          ' s.status                         AS strServiceStatus, ' .
                          ' aid.date_active                  AS dteDateActive, ' .
                          ' hbi.hardware_supplier_product_id AS intProductID, ' .
                          ' hpc.tag                          AS strClassHandle, ' .
                          ' hrt.vchHandle                    AS strRmaTypeHandle, ' .
                          ' sd.type                          AS strProductType ' .
                      'FROM products.service_definitions        AS sd, ' .
                          ' products.hardware_order_statuses    AS hos, ' .
                          ' products.service_components         AS sc, ' .
                          ' config_hardware_bundles             AS chb, ' .
                          ' tblHardwareRmaProcess               AS hrp, ' .
                          ' tblHardwRmaProcessItem              AS hrpi, ' .
                          ' products.vblHardwRmaOptDirection    AS hrod, ' .
                          ' hardware_bundle_items               AS hbi, ' .
                          ' products.hardware_supplier_products AS hsp, ' .
                          ' products.hardware_product_classes   AS hpc, ' .
                          ' products.hardware_suppliers         AS hs, '.
                          ' products.vblHardwareRmaType         AS hrt, ' .
                          ' components                          AS c, ' .
                          ' services                            AS s, ' .
                          ' adsl.install_diary                  AS aid ' .
                     'WHERE sd.service_definition_id=s.type ' .
                       'AND s.service_id=c.service_id ' .
                       'AND c.component_id=chb.component_id ' .
                       'AND c.component_type_id=sc.service_component_id ' .
                       'AND hrpi.usiHardwRmaOptDirectionID=hrod.usiHardwRmaOptDirectionID ' .
                       'AND hrpi.usiHardwareBundleItemID=hbi.hardware_bundle_item_id ' .
                       'AND hbi.hardware_supplier_product_id=hsp.hardware_supplier_product_id ' .
                       'AND hsp.hardware_product_class_id=hpc.hardware_product_class_id ' .
                       'AND hsp.hardware_supplier_id = hs.hardware_supplier_id '.
                       'AND hrp.usiHardwareRmaTypeID=hrt.usiHardwareRmaTypeID ' .
                       'AND hrp.usiHardwareOrderStatusID=hos.hardware_order_status_id ' .
                       'AND aid.service_id=s.service_id ' .
                       'AND s.username NOT LIKE "ztest%"' .
                       'AND s.status IN("active","queued-activate") ' .
                       'AND hrod.vchHandle="Remove" ' .
                       'AND c.status IN ("active", "queued-activate", "queued-reactivate") ' .
                       "AND hos.tag='{$arrCriteria['strStatusTag']}' ".
                       "AND hs.tag = '{$this->m_strSupplierTag}' ";

        // The status selected controls the join and the fields to filter on for dates
        switch ($arrCriteria['strStatusTag']) {
            case 'rma_in_progress':
                $strQuery .= ' AND hrp.usiConfigHardwareBundleID=chb.config_hardware_bundle_id ';

                if (isset($arrCriteria['StartDate']) || isset($arrCriteria['EndDate'])) {
                    $arrQueryChunks = array();

                    if (isset($arrCriteria['StartDate'])) {
                        $datStartDate = date(MYSQL_DATETIME, $arrCriteria['StartDate']);

                        $arrQueryChunks[] = "chb.when_ordered > '$datStartDate'";
                    }

                    if (isset($arrCriteria['EndDate'])) {
                        $datEndDate = date(MYSQL_DATETIME, $arrCriteria['EndDate']);

                        $arrQueryChunks[] = "chb.when_ordered < '$datEndDate'";
                    }

                    $strCollatedChunks = implode(' AND ', $arrQueryChunks);

                    $strQuery .= "AND $strCollatedChunks ";
                }

                break;

            case 'rma_complete':
                $strQuery .= ' AND hrp.usiConfigHardwareBundleID=chb.config_hardware_bundle_id ';

                if (isset($arrCriteria['StartDate']) || isset($arrCriteria['EndDate'])) {
                    $arrQueryChunks = array();

                    if (isset($arrCriteria['StartDate'])) {
                        $datStartDate = date(MYSQL_DATETIME, $arrCriteria['StartDate']);

                        $arrQueryChunks[] = "chb.when_ordered > '$datStartDate'";
                    }

                    if (isset($arrCriteria['EndDate'])) {
                        $datEndDate = date(MYSQL_DATETIME, $arrCriteria['EndDate']);

                        $arrQueryChunks[] = "chb.when_ordered < '$datEndDate'";
                    }

                    $strCollatedChunks = implode(' AND ', $arrQueryChunks);

                    $strQuery .= "AND $strCollatedChunks";
                }

                break;

            default:
                report_error('Invalid status passed');
        }

        if (isset($arrCriteria['CategoryName'])) {
            $strCategoryName = addslashes($arrCriteria['CategoryName']);

            $strQuery .= " AND sd.type='$strCategoryName'";
        }

        // Tack on the GROUP BY clause
        $strQuery .= ' GROUP BY chb.config_hardware_bundle_id';

        $dbhConnection = get_named_connection('userdata_reporting');

        $resResult = mysql_query($strQuery, $dbhConnection)
            or report_error(__FILE__, __LINE__, mysql_error($dbhConnection));

        $arrData = array();

        while ($recRow = mysql_fetch_assoc($resResult)) {
            if (!isset($arrData[$recRow['strProductType']]['arrClasses'][$recRow['strClassHandle']]['arrRmaTypes'][$recRow['strRmaTypeHandle']]['arrAccounts'])) {
                $arrData[$recRow['strProductType']]['arrClasses'][$recRow['strClassHandle']]['arrRmaTypes'][$recRow['strRmaTypeHandle']]['arrAccounts'] = array($recRow);

                $arrData[$recRow['strProductType']]['arrClasses'][$recRow['strClassHandle']]['arrRmaTypes'][$recRow['strRmaTypeHandle']]['intCount'] = 1;
            } else {
                $arrData[$recRow['strProductType']]['arrClasses'][$recRow['strClassHandle']]['arrRmaTypes'][$recRow['strRmaTypeHandle']]['arrAccounts'][] = $recRow;

                ++$arrData[$recRow['strProductType']]['arrClasses'][$recRow['strClassHandle']]['arrRmaTypes'][$recRow['strRmaTypeHandle']]['intCount'];
            }
        }

        $arrResults['arrData'] = $arrData;

        return $arrResults;

    } // function GetRmaStatusSummary


    // Gets a Summary of RMA reasons that match the passed criteria
    /**
     * Gets a Summary of RMA reasons that match the passed criteria
     *
     * @param array $arrCriteria Criteria
     *
     * @return array
     */
    function GetRmaReasonSummary($arrCriteria)
    {
        // Prepare the return array
        $arrResults = array();

        $dbhConnection = get_named_connection('product_reporting');

        // Product types
        $arrProductTypes = array('residential' => array('intCount'    => 0,
                                                        'arrAccounts' => array()),
                                 'business'    => array('intCount'    => 0,
                                                        'arrAccounts' => array()));

        // If the criteria specified a particular product type, remove the
        // others from the array.
        if (isset($arrCriteria['strProductTypeHandle'])) {
            foreach (array_keys($arrProductTypes) as $strHandle) {
                if ($arrCriteria['strProductTypeHandle'] != $strHandle) {
                    unset($arrProductTypes[$strHandle]);
                }
            }
        }

        $arrResults['arrProductTypes'] = $arrProductTypes;

        // Get RMA Reasons
        $strQuery = 'SELECT hrr.usiHardwareRmaReasonID, ' .
                          ' vchDisplayName AS strDisplayName, ' .
                          ' vchHandle      AS strHandle ' .
                      'FROM tblHardwareRmaReason         AS hrr, ' .
                          ' tblHardwareSupplierRmaReason AS hsrr ' .
                     'WHERE hrr.usiHardwareRmaReasonID=hsrr.usiHardwareRmaReasonID ' .
                       'AND hrr.dtmWhenRemoved IS NULL';

        $resResult = mysql_query($strQuery, $dbhConnection)
            or report_error(__FILE__, __LINE__, mysql_error($dbhConnection));

        $arrReasons = array();

        while ($recRow = mysql_fetch_assoc($resResult)) {
            $arrReasons[$recRow['strHandle']] = $recRow;
        }

        mysql_free_result($resResult);

        $arrResults['arrReasons'] = $arrReasons;

        // Loop through and tot up the accounts

        $strQuery = 'SELECT chb.config_hardware_bundle_id    AS intHardwareID, ' .
                          ' s.username                       AS strUsername, ' .
                          ' s.isp                            AS strVisp, ' .
                          ' s.service_id                     AS intServiceID, ' .
                          ' sd.name                          AS strAccountType, ' .
                          ' sc.name                          AS strHardwareType, ' .
                          ' hos.display_name                 AS strStatusDisplayName, ' .
                          ' s.status                         AS strServiceStatus, ' .
                          ' aid.date_active                  AS dteDateActive, ' .
                          ' hrp.usiHardwareRmaProcessID      AS usiHardwareRmaProcessID, ' .
                          ' hbi.hardware_supplier_product_id AS intProductID, ' .
                          ' hpc.tag                          AS strClassHandle, ' .
                          ' hrt.vchHandle                    AS strRmaTypeHandle, ' .
                          ' sd.type                          AS strProductType, ' .
                          ' hrr.vchHandle                    AS strReasonHandle ' .
                      'FROM products.service_definitions        AS sd, ' .
                          ' products.hardware_order_statuses    AS hos, ' .
                          ' products.service_components         AS sc, ' .
                          ' config_hardware_bundles             AS chb, ' .
                          ' tblHardwareRmaProcess               AS hrp, ' .
                          ' tblHardwRmaProcessItem              AS hrpi, ' .
                          ' products.vblHardwRmaOptDirection    AS hrod, ' .
                          ' hardware_bundle_items               AS hbi, ' .
                          ' products.hardware_supplier_products AS hsp, ' .
                          ' products.hardware_product_classes   AS hpc, ' .
                          ' products.hardware_suppliers         AS hs, '.
                          ' products.vblHardwareRmaType         AS hrt, ' .
                          ' products.tblHardwareRmaReason       AS hrr, ' .
                          ' components                          AS c, ' .
                          ' services                            AS s, ' .
                          ' adsl.install_diary                  AS aid ' .
                     'WHERE sd.service_definition_id=s.type ' .
                       'AND s.service_id=c.service_id ' .
                       'AND c.component_id=chb.component_id ' .
                       'AND c.component_type_id=sc.service_component_id ' .
                       'AND hrpi.usiHardwRmaOptDirectionID=hrod.usiHardwRmaOptDirectionID ' .
                       'AND hrpi.usiHardwareBundleItemID=hbi.hardware_bundle_item_id ' .
                       'AND hbi.hardware_supplier_product_id=hsp.hardware_supplier_product_id ' .
                       'AND hsp.hardware_product_class_id=hpc.hardware_product_class_id ' .
                       'AND hrp.usiHardwareRmaTypeID=hrt.usiHardwareRmaTypeID ' .
                       'AND hrp.usiHardwareRmaReasonID=hrr.usiHardwareRmaReasonID ' .
                       'AND hrp.usiHardwareOrderStatusID=hos.hardware_order_status_id ' .
                       'AND aid.service_id=s.service_id ' .
                       'AND s.username NOT LIKE "ztest%" ' .
                       'AND s.status IN("active","queued-activate") ' .
                       'AND hrod.vchHandle="Remove" ' .
                       'AND c.status IN ("active", "queued-activate", "queued-reactivate") ' .
                       "AND hos.tag='{$arrCriteria['strStatusTag']}' " .
                       "AND hos.tag='{$arrCriteria['strStatusTag']}' ";

        // The status selected controls the join and the fields to filter on for dates
        switch ($arrCriteria['strStatusTag']) {
            case 'rma_in_progress':
                $strQuery .= ' AND hrp.usiConfigHardwareBundleID=chb.config_hardware_bundle_id ';

                if (isset($arrCriteria['StartDate']) || isset($arrCriteria['EndDate'])) {
                    $arrQueryChunks = array();

                    if (isset($arrCriteria['StartDate'])) {
                        $datStartDate = date(MYSQL_DATETIME, $arrCriteria['StartDate']);

                        $arrQueryChunks[] = "chb.when_ordered > '$datStartDate'";
                    }

                    if (isset($arrCriteria['EndDate'])) {
                        $datEndDate = date(MYSQL_DATETIME, $arrCriteria['EndDate']);

                        $arrQueryChunks[] = "chb.when_ordered < '$datEndDate'";
                    }

                    $strCollatedChunks = implode(' AND ', $arrQueryChunks);

                    $strQuery .= "AND ($strCollatedChunks) ";
                }

                break;

            case 'rma_complete':
                $strQuery .= ' AND hrp.usiConfigHardwareBundleID=chb.config_hardware_bundle_id ';

                if (isset($arrCriteria['StartDate']) || isset($arrCriteria['EndDate'])) {
                    $arrQueryChunks = array();

                    if (isset($arrCriteria['StartDate'])) {
                        $datStartDate = date(MYSQL_DATETIME, $arrCriteria['StartDate']);

                        $arrQueryChunks[] = "chb.when_ordered > '$datStartDate'";
                    }

                    if (isset($arrCriteria['EndDate'])) {
                        $datEndDate = date(MYSQL_DATETIME, $arrCriteria['EndDate']);

                        $arrQueryChunks[] = "chb.when_ordered < '$datEndDate'";
                    }

                    $strCollatedChunks = implode(' AND ', $arrQueryChunks);

                    $strQuery .= "AND ($strCollatedChunks)";
                }
                break;

            default:
                report_error('Invalid status passed');
        }

        if (isset($arrCriteria['strProductTypeHandle'])) {
            $strProductTypeHandle = addslashes($arrCriteria['strProductTypeHandle']);

            $strQuery .= " AND sd.type='$strProductTypeHandle'";
        }

        if (isset($arrCriteria['strClassHandle'])) {
            $strClassHandle = $arrCriteria['strClassHandle'];

            $strQuery .= " AND hpc.tag='$strClassHandle'";
        }

        if (isset($arrCriteria['strReasonHandle'])) {
            $strReasonHandle = $arrCriteria['strReasonHandle'];

            $strQuery .= " AND hrr.vchHandle='$strReasonHandle'";
        }

        //strRmaTypeHandle
        if (isset($arrCriteria['strRmaTypeHandle']) && $arrCriteria['strRmaTypeHandle'] != '') {
            $strRmaTypeHandle = addslashes($arrCriteria['strRmaTypeHandle']);

            $strQuery .= " AND hrt.vchHandle = '$strRmaTypeHandle'";
        }

        // Tack on the GROUP BY clause
        $strQuery .= ' GROUP BY chb.config_hardware_bundle_id';

        $dbhConnection = get_named_connection('userdata');

        $resResult = mysql_query($strQuery, $dbhConnection)
            or report_error(__FILE__, __LINE__, mysql_error($dbhConnection));

        $arrData = array();

        while ($recRow = mysql_fetch_assoc($resResult)) {
            if (!isset($arrData[$recRow['strProductType']]['arrReasons'][$recRow['strReasonHandle']]['arrAccounts'])) {
                $arrData[$recRow['strProductType']]['arrReasons'][$recRow['strReasonHandle']]['arrAccounts'] = array($recRow);

                $arrData[$recRow['strProductType']]['arrReasons'][$recRow['strReasonHandle']]['intCount'] = 1;
            } else {
                $arrData[$recRow['strProductType']]['arrReasons'][$recRow['strReasonHandle']]['arrAccounts'][] = $recRow;
                ++$arrData[$recRow['strProductType']]['arrReasons'][$recRow['strReasonHandle']]['intCount'];
            }
        }

        $arrResults['arrData'] = $arrData;

        return $arrResults;

    } // function GetRmaReasonSummary

} // class HardwareBundleSupplier
