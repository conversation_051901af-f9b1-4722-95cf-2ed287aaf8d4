<?php

	/////////////////////////////////////////////////////////////////////
	// File:     config-webspace-access.inc
	// Purpose:  Access mini-library for config_webspace
	/////////////////////////////////////////////////////////////////////

	/////////////////////////////////////////////////////////////////////
	// Functions
	//
	// Write functions
	// ---------------
	//
	// config_webspace_add
	//
	// Read functions
	// --------------
	//
	// config_webspace_get
	// config_webspace_find_missing_qactivate_components
	// config_webspace_find_missing_qdestroy_components
	// config_webspace_find_by_username_isp
	//
	// Delete functions
	// ----------------
	//
	// config_webspace_delete
	//
	// Update functions
	// ----------------
	// config_webspace_set_to_unconfigured
	//
	/////////////////////////////////////////////////////////////////////


	/////////////////////////////////////////////////////////////////////

	require_once '/local/data/mis/common_library_functions/class_libraries/Logging/Logging.class.php';
	require_once '/local/data/mis/database/database_libraries/components/ShowHostingLinks.class.php';	

	// Data

	$global_component_configurators["4"]  = "config_webspace_configurator"; // plus.net
	$global_component_configurators["26"] = "config_webspace_configurator"; // f9
	$global_component_configurators["40"] = "config_webspace_configurator"; // freeonline
	$global_component_configurators["43"] = "config_webspace_configurator"; // dabsol
	$global_component_configurators["47"] = "config_webspace_configurator"; // searchpro

	$global_component_configurators["143"] = "config_webspace_configurator"; // Generic 25/100 Webspace
	$global_component_configurators["144"] = "config_webspace_configurator"; // Generic 50/150 Webspace
	$global_component_configurators["145"] = "config_webspace_configurator"; // Generic 250/250 Webspace
	$global_component_configurators["146"] = "config_webspace_configurator"; // Generic 500/500 Webspace
	$global_component_configurators["188"] = "config_webspace_configurator"; // Generic 500/500 Webspace
		
	//  192 is component for beta
	//$global_component_configurators["192"] = "config_webspace_configurator"; // Generic 500/500 Webspace
	// Data
	/////////////////////////////////////////////////////////////////////

	// Hack to insert the component configurator array into PHP5's global scope
	// if it's not already there
	if(!isset($GLOBALS['global_component_configurators']))
	{
		$GLOBALS['global_component_configurators'] = $global_component_configurators;
	}
	else
	{
		foreach($global_component_configurators as $intIndex => $strConfigurator)
		{
			$GLOBALS['global_component_configurators'][$intIndex] = $strConfigurator;
		}
	}

	require_once '/local/data/mis/database/crypt_config.inc';

	/////////////////////////////////////////////////////////////////////
	// Library functions

	/////////////////////////////////////////////////////////////
	// Function:  config_webspace_add
	// Purpose:   Create a webspace component configuration
	// Arguments: $component_id (id of corresponding component)
	//	    $isp
	// Returns:   ID of the webspace configuration record
	/////////////////////////////////////////////////////////////

	function config_webspace_add ($component_id, $isp) 
	{
		global $global_db_src;
		$connection = get_named_connection ("userdata");

		$component_id = addslashes($component_id);
		$isp = addslashes ($isp);

		if($component_id == 0 || $isp == '') {

		Dbg_Dbg::enable('config_webspace_add');
		Dbg_Dbg::write("invalid component id '" .$component_id. "' or invalid isp '" .$isp. "' caused a validation error" . PHP_EOL . Logging::getCallStackAsString(),"config_webspace_add");

		return false;
		}

		// Find the username, password, service_id
		$query = "SELECT username, password " .
		           "FROM services, components " .
		          "WHERE services.service_id = components.service_id " .
		            "AND components.component_id = '$component_id'";

		$result = mysql_query ($query, $connection)
			or report_error(__FILE__, __LINE__, mysql_error($connection));

		$row = mysql_fetch_array ($result, MYSQL_ASSOC);
		mysql_free_result ($result);
		$username = $row['username'];
		$password = Crypt_Crypt::decrypt($row['password'], 'services');

		$strEncryptedPassword = addslashes(Crypt_Crypt::encrypt($password, 'config_webspace'));

		// Look to see if a component is already attached to this account
		$query = "SELECT webspace_id, component_id " .
		           "FROM config_webspace " .
		          "WHERE username='$username' " .
		            "AND isp='$isp'";

		$result = mysql_query($query, $connection)
			or report_error(__FILE__, __LINE__, mysql_error($connection));

		if (mysql_num_rows($result) == 0)
		{
			// Create the config record
			$query = "INSERT INTO config_webspace ( " .
			                    " component_id, isp, username, " .
			                    " password ) " .
			              "VALUES ( " .
			                    " '$component_id', '$isp', '$username', " .
			                    " '$strEncryptedPassword')";

			mysql_query($query, $connection)
			      or report_error(__FILE__, __LINE__, mysql_error($connection));

			$webspace_id = mysql_insert_id($connection);

			userdata_component_set_configuration ($component_id, $webspace_id, '');
			userdata_component_set_status ($component_id, 'queued-activate');

			return mysql_insert_id($connection);
		}
		else if (mysql_num_rows($result) > 1)
		{
			// This should never happen!!
			return 0;
		}
		else
		{
			// Set the existing component to destroyed
			$row = mysql_fetch_array($result, MYSQL_ASSOC);

			$old_component_id = $row['component_id'];

			userdata_component_set_status($component_id, 'destroyed');

			$type_query = "SELECT component_type_id
			                 FROM components
			                WHERE component_id = '$component_id'";

			$query = mysql_query($type_query, $connection)
			         or report_error(__FILE__, __LINE__, mysql_error($connection));

			$result = mysql_fetch_array($query, MYSQL_ASSOC);
			$component_type = $result['component_type_id'];

			mysql_query("UPDATE components
			                SET component_type_id = '$component_type'
			              WHERE component_id = '$old_component_id'", $connection)
			      or report_error(__FILE__, __LINE__, mysql_error($connection));

			userdata_component_set_status ($old_component_id, 'queued-reactivate');

			return $row['webspace_id'];
		}
	}


	/////////////////////////////////////////////////////////////
	// Function:  config_webspace_get
	// Purpose:   Get a component and webspace configuration
	//	    record
	// Arguments: $component_id (id of component)
	// Returns:   Union of component and webspace configuration
	/////////////////////////////////////////////////////////////

	function config_webspace_get ($component_id) {

		global $global_db_src;
		$connection = get_named_connection ("userdata");

		$component_id = addslashes ($component_id);

		$result = mysql_query ("SELECT * FROM components LEFT JOIN config_webspace
						 ON components.config_id = config_webspace.webspace_id
						 WHERE components.component_id = '$component_id'", $connection)
			or report_error (__FILE__, __LINE__, mysql_error($connection));

		$union = mysql_fetch_array ($result, MYSQL_ASSOC);

		mysql_free_result ($result);

		return $union;

	}

	/////////////////////////////////////////////////////////////////
	// Function : config_webspace_find_missing_qactivate_components
	// Purpose  : Find all components which are webspace components
	//	    but do not have a matching recod in config_webspace
	//	    which are queued activated or reactivated
	// Arguments: None
	// Returns  : Array of all webspace component_ids
	/////////////////////////////////////////////////////////////////

	function config_webspace_find_missing_qactivate_components ($array_webspace_components)
	{
		$connection = get_named_connection('userdata');

		for ($count = 0; $count < count($array_webspace_components); ++$count)
		{
			$type_id = addslashes($array_webspace_components[$count]);

			if (($count+1) == count($array_webspace_components))
			{
				$type_list = $type_list . $type_id;
			}
			else
			{
				$type_list = $type_list . $type_id . ",";
			}
		}

		$query = "SELECT components.component_id, config_webspace.webspace_id
		            FROM components
		            LEFT JOIN config_webspace
		              ON components.config_id = config_webspace.webspace_id
		           WHERE components.component_type_id in ($type_list)
		             AND components.status IN ('queued-activate', 'queued-reactivate')
		          HAVING config_webspace.webspace_id is null";

		$result = mysql_query ($query, $connection)
				   or report_error (__FILE__, __LINE__, mysql_error($connection));

		$missing_webspace_components = array();

		while ($row = mysql_fetch_row($result))
		{
			$missing_webspace_components[] = $row[0];
		}

		mysql_free_result($result);

		return $missing_webspace_components;
	}

	/////////////////////////////////////////////////////////////////
	// Function : config_webspace_find_missing_qdestroy_components
	// Purpose  : Find all components which are webspace components
	//	    but do not have a matching recod in config_webspace
	//	    which are queued destroy
	// Arguments: Array of all webspace component type ids
	// Returns  : Array of all found webspace component_ids
	/////////////////////////////////////////////////////////////////

	function config_webspace_find_missing_qdestroy_components ($array_webspace_components)
	{
		$connection = get_named_connection('userdata');

		for ($count = 0; $count < count($array_webspace_components); ++$count)
		{
			$type_id = addslashes($array_webspace_components[$count]);

			if (($count+1) == count($array_webspace_components))
			{
				$type_list = $type_list . $type_id;
			}
			else
			{
				$type_list = $type_list . $type_id . ",";
			}
		}

		$query = "SELECT components.component_id, config_webspace.webspace_id
			    FROM components
			    LEFT JOIN config_webspace
			      ON components.config_id	 =  config_webspace.webspace_id
			   WHERE components.component_type_id in ($type_list)
			     AND components.status	    = 'queued-destroy'
			  HAVING config_webspace.webspace_id is null";

		$result = mysql_query ($query, $connection)
				   or report_error (__FILE__, __LINE__, mysql_error($connection));

		$missing_webspace_components = array();

		while ($row = mysql_fetch_row($result))
		{
			$missing_webspace_components[] = $row[0];
		}

		mysql_free_result($result);

		return $missing_webspace_components;
	}

	/////////////////////////////////////////////////////////////////
	// Function : config_webspace_find_by_username_isp
	// Purpose  : Find ig there is a webspace id already for given
	//	      usermame or isp
	// Arguments: $username, $isp
	// Returns  : $webspace_id
	/////////////////////////////////////////////////////////////////

	function config_webspace_find_by_username_isp ($username, $isp)
	{
		$connection = get_named_connection ("userdata");

		$username = addslashes($username);
		$isp      = addslashes($isp);

		$query = "SELECT webspace_id
		            FROM config_webspace
		           WHERE username = '$username'
		             AND isp = '$isp'";

		$result = mysql_query ($query, $connection)
				   or report_error (__FILE__, __LINE__, mysql_error($connection));

		$details = mysql_fetch_array($result);

		$webspace_id = $details['webspace_id'];

		return $webspace_id;

	}	

	/////////////////////////////////////////////////////////////
	// Function:  config_webspace_delete
	// Purpose:   Delete a webspace configuration
	// Arguments: $webspace_id
	/////////////////////////////////////////////////////////////

	function config_webspace_delete ($webspace_id) {

		$connection = get_named_connection ("userdata");

		$webspace_id = addslashes ($webspace_id);

		// Delete config
		mysql_query ("DELETE FROM config_webspace
				     WHERE webspace_id = '$webspace_id'", $connection)
			or report_error (__FILE__, __LINE__, mysql_error($connection));

	}


	/////////////////////////////////////////////////////////////
	// Function:  config_webspace_auto_configure
	// Purpose:   'unconfigured' -> 'queued-activate' state
	//	    transition handler for auto-configuration
	// Arguments: $component_id
	/////////////////////////////////////////////////////////////

	function config_webspace_auto_configure ($component_id) {

		$component = userdata_component_get ($component_id);
		switch ($component["status"]) {

		    // Needs configuring
		    case "unconfigured":
				$service = userdata_service_get ($component["service_id"]);
				$isp = $service["isp"];
				$webspace_id = config_webspace_add ($component_id, $isp);
			break;

		    // These aren't the droids you're looking for
		    case "active":
		    case "queued-activate":
		    case "queued-reactivate":
		    case "deactive":
		    case "queued-deactivate":
		    case "queued-deconfigure":
		    case "queued-destroy":
		    case "destroyed":
			break;

		    // The sky is falling!
		    default:
			break;

		}

	}


	/////////////////////////////////////////////////////////////
	// Function:  config_webspace_auto_destroy
	// Purpose:   * -> 'queued-destroy' state
	//	    transition handler for auto-destruction
	// Arguments: $component_id
	/////////////////////////////////////////////////////////////

	function config_webspace_auto_destroy ($component_id) {

		$component = userdata_component_get ($component_id);

		switch ($component["status"]) {

		    // Free pass to "destroyed"
		    case "unconfigured":
				userdata_component_set_status ($component_id, "destroyed");
			break;

		    // Have or may have configuration, pass to the reaper
		    case "active":
		    case "deactive":
		    case "queued-reactivate":
		    case "queued-deactivate":
		    case "queued-activate":
		    case "queued-deconfigure":
		    case "queued-destroy":
				userdata_component_set_status ($component_id, "queued-destroy");
			break;

		    // Already there
		    case "destroyed":
			break;

		    default:
			// The sky is falling!
			break;

		}

	}


	/////////////////////////////////////////////////////////////
	// Function:  config_webspace_set_password
	// Purpose:
	// Arguments: $component_id
	//	    $password
	/////////////////////////////////////////////////////////////

	function config_webspace_set_password ($component_id, $password) {

		$connection = get_named_connection ("userdata");

		$strEncryptedPassword = addslashes(Crypt_Crypt::encrypt($password, 'config_webspace'));

		$component_id = addslashes ($component_id);

		mysql_query ("UPDATE config_webspace
		                 SET password = '$strEncryptedPassword'
		               WHERE component_id = '$component_id'", $connection)
		      or report_error (__FILE__, __LINE__, mysql_error($connection));

	}

	function config_webspace_auto_refresh ($component_id) {

		$component = userdata_component_get ($component_id);

		switch ($component["status"]) {

		    // fix by Lukasz Zmywaczyk - prob: 29873
		    case "queued-activate":
		    case "queued-reactivate":
		    case "active":
				$service = userdata_service_get ($component["service_id"]);
				$password = $service["password"];
				config_webspace_set_password ($component_id, $password);
				userdata_component_set_status ($component_id, "queued-reactivate");
			break;

		    // Anything else is none of our business here
		    default:
			break;

		}

	}


	function config_webspace_configurator ($component_id, $action) {

		switch ($action) {

		    case "auto_configure":
				config_webspace_auto_configure ($component_id);
			break;

		    case "auto_disable":
// FIXME
			break;

		    case "auto_enable":
// FIXME
			break;

		    case "auto_refresh":
				config_webspace_auto_refresh ($component_id);
			break;

		    case "auto_destroy":
				config_webspace_auto_destroy ($component_id);
			break;

		    default:
			break;

		}

	}
	
	
	
	function ConfigWebspaceRead($intServiceComponentId)
	{
		$dbConnection          = get_named_connection('product');
		$intServiceComponentId = addslashes($intServiceComponentId);
		
		$strQuery   = 'SELECT * '.
		                'FROM component_webspace_config '.
		               'WHERE service_component_id = "'.$intServiceComponentId.'"';
		$resResult = mysql_query($strQuery, $dbConnection)
			or report_error(__FILE__, __LINE__, mysql_error($connection));
			
		$arrWebspaceConfig = array();
		if(mysql_num_rows($resResult) > 0)
		{
			$arrWebspaceConfig = mysql_fetch_assoc($resResult);
		}

		return $arrWebspaceConfig;
	}


/**
 * Determine whether to show various hosting links for a given 
 * usermame or isp
 * 
 * @param string $username Username
 * @param string $isp      ISP
 *
 * @return ShowHostingLinks
 */
function config_webspace_show_hosting_links ($username, $isp)
{
    $advancedWebstatsEnabled = 0;
    $myWebspaceEnabled = 0;
    $plusnetHostingEnabled = 0;
    
    $intWebSpaceId = config_webspace_find_by_username_isp($username, $isp);
    
    $intWebStatsAdvancedId = config_webspace_find_component_by_username_isp_id(
        $username,
        $isp,
        array(COMPONENT_GENERIC_WEBSTATS_ADVANCED)
    );    
    
    $intPlusnetHostingId = config_webspace_find_component_by_username_isp_id(
        $username,
        $isp,
        array(COMPONENT_PLUSNET_BUSINESS_HOSTING)
    );

    if (!empty($intWebSpaceId)) {
        $myWebspaceEnabled = 1;
    }

    if (!empty($intWebStatsAdvancedId)) {
        $advancedWebstatsEnabled = 1;
    }

    if (!empty($intPlusnetHostingId)) {
        $plusnetHostingEnabled = 1;
    }

    $showHostingLinks = new ShowHostingLinks($advancedWebstatsEnabled, $myWebspaceEnabled, $plusnetHostingEnabled);

    return $showHostingLinks;
}


/**
 * Find if there is a webstats id already for given usermame, isp, component_id array
 *
 * @param string $username      Username
 * @param string $isp           ISP
 * @param array  $component_ids Array of component ids 
 *
 * @return component_id
 */
function config_webspace_find_component_by_username_isp_id ($username, $isp, $component_ids)
{
    $connection = get_named_connection("userdata");

    $username = addslashes($username);
    $isp      = addslashes($isp);

    $query = "select C.component_id from userdata.services S
    inner join userdata.components C on C.service_id = S.service_id
    inner join products.service_components SC on SC.service_component_id = C.component_type_id
    where S.username = '$username' and S.isp = '$isp' and S.status = 'active'
    and SC.service_component_id in ( " . implode(",", $component_ids) .")";

    $result = mysql_query($query, $connection)
    or report_error(__FILE__, __LINE__, mysql_error($connection));

    $details = mysql_fetch_array($result);

    $component_db_id = $details['component_id'];

    return $component_db_id;

}
