<?php
require_once('/local/data/mis/database/class_libraries/Customers/C_Core_Service.php');
require_once('/local/data/mis/database/application_apis/Mail/Db.class.php');
require_once('/local/data/mis/database/application_apis/Mail/Exception.class.php');
require_once('/local/data/mis/database/application_apis/Mail/MailApi.class.php');
require_once('/local/data/mis/database/application_apis/Mail/Mailbox.class.php');
require_once('/local/data/mis/database/application_apis/Mail/Redirect.class.php');
require_once('/local/data/mis/database/application_apis/Mail/List.class.php');
require_once('/local/data/mis/database/application_apis/Mail/List.class.php');

/**
 * Email Tool backend class to manage mail configuration
 * Instantiate it using component id
 * Static methods can be used to get any mailbox(es)
 */
final class Mail_Manager
{
    /**
     * @var array definition of types passed to get methods from frontend
     */
    private static $_arrTypes = array('mailbox' => array('default', 'mailbox', 'blackhole'),
                                        'alias' => array('default', 'mailbox', 'blackhole'),
                                     'redirect' => array('redirect'),
                                         'list' => array('list'),
                                          'any' => array('default', 'mailbox', 'blackhole', 'redirect', 'list'));

    /**
     * @var array list of fields in userdata.config_email that can be used for searching and sorting
     */
    private static $_arrFields = array('email_id', 'component_id', 'box_number', 'type', 'isp',
                                        'username', 'login', 'password', 'redirect_to',
                                        'mlist_options', 'delivery_method',
                                        'smtp_delivery_ip', 'status');

    /**
     * @var array Mailbox statuses that should be included on portal etc.
     */
    private static $_arrVisibleStatuses = array('active','queued-activate', 'queued-reactivate');

    /**
     * @var string name of calling context e.g. mail configurator, if set
     */
    public static $strContext = '';

    /**
     * @var integer component id
     */
    private $_intComponentId = null;

        /**
         * @var object C_Core_Service
         */
        private $_objService = null;

    /**
     * Maximum number of aliases for free accounts
     */
    const MAX_FREE_ALIASES = 5;

        /**
         * Address we use for blackholing
         */

        const BLACKHOLE_EMAIL_ADDRESS = '<EMAIL>';

        /**
         * Search criteria modes, configured per table field
         */

        const SEARCH_CRITERIA_BEGINS_WITH      = 1;
        const SEARCH_CRITERIA_ENDS_WITH        = 2;
        const SEARCH_CRITERIA_CONTAINS         = 3;
        const SEARCH_CRITERIA_EXACT            = 4;

    /**
     * Instantiates a class manager for specific customer
     *
     * @param integer component id
     */

    public function __construct($intId)
    {
        if (!is_numeric($intId) or $intId < 1) {
            throw new Mail_Exception("Invalid id ($intId) passed to Manager::__construct");
        }

        $this->_intComponentId = $intId;
    }

    /**
     * for partner eu where the reseller owns a domain return the domain they
     * have requested to be used for email addresses for all other accounts...
     * Returns $username.$isp_extension
     * but in the case of partner EU accounts where the resellerhas their own
     * domains it returns that domain
     *
     * @return string
     * @throws Mail_Exception
     */
    public function getUserDomain()
    {
        $strQuery = 'SELECT s.service_id, s.username, s.isp '
                  . 'FROM userdata.components c INNER JOIN userdata.services s ON c.service_id = s.service_id '
                  . 'WHERE c.component_id = ' . $this->_intComponentId;
        $arrRow = Mail_Db::getInstance()->getSingleRow($strQuery, 'userdata');

        // see if user is a partner eu and does their reseller have their own domain
        if (isset($arrRow['service_id'])) {
                $objResellerDomainHelper = Reseller_DomainHelper::getByServiceId(new Int($arrRow['service_id']));
                if (!is_null($objResellerDomainHelper)) {

                        return $objResellerDomainHelper->getMailDomain()->getValue();
                }
        }

        if (isset($arrRow['isp']) &&
            ($arrRow['isp'] == 'vodafone' || $arrRow['isp'] == 'johnlewis')) {
            return Mail_Mailbox::getIspExtension($arrRow['isp'], true);
        } else if (isset($arrRow['username']) and isset($arrRow['isp'])) {
            return $arrRow['username'] . '.' . Mail_Mailbox::getIspExtension($arrRow['isp']);
        } else {
            throw new Mail_Exception("Failed to retrieve user domain");
        }
    }

    /**
    * Gets all mailbox types for wizard summary
    *
    * @return array - Returns array of mailboxes
    * @param string - strOrder - how to order the query
    * @param array - arrLimit - Limit the results by what amount.
    * @param string - strSearch - What string are we searching for.
    * @param array - arrCriteria - Search by begin or end.
    */
    public function getAllMailboxes($strOrder, $arrLimit, $strSearch, $arrCriteria, $strSortBy)
    {

        if ($strSearch !== NULL && $strSearch != '') {

                if ($arrCriteria !== NULL && is_array($arrCriteria) && isset($arrCriteria['login'])) {

                        $intCriteria = $arrCriteria['login'];

                } else {

                        $intCriteria = self::SEARCH_CRITERIA_EXACT;

                }

                $strValue = mysql_real_escape_string($strSearch);

                switch ($intCriteria) {

                        case self::SEARCH_CRITERIA_BEGINS_WITH:
                                $strCriteriaAndValue = " AND vchName LIKE '" . addcslashes($strValue, '_%') . '%'."'";
                            break;

                        case self::SEARCH_CRITERIA_ENDS_WITH:
                                $strCriteriaAndValue = " AND vchName LIKE '%" . addcslashes($strValue, '_%')."'";
                            break;

                        case self::SEARCH_CRITERIA_CONTAINS:
                                $strCriteriaAndValue = " AND vchName LIKE '%" . addcslashes($strValue, '_%') . '%'."'";
                            break;

                        case self::SEARCH_CRITERIA_EXACT:
                        default:
                                $strCriteriaAndValue = " AND vchName = '{$strValue}'";
                            break;
                }

        }

        $strQuery = "(SELECT email_id as id,component_id,box_number,type,isp,username,
                     login,password,redirect_to,mlist_options,delivery_method,
                     smtp_delivery_ip,status, login as vchName, 'intMailAliasID'
                     FROM config_email
                     WHERE component_id = '".$this->_intComponentId."'
                     AND status IN('active','queued-activate','queued-reactivate')
                     AND type IN('default','mailbox','blackhole','redirect','list') ";

        $strQuery .= str_replace('vchName', 'login', $strCriteriaAndValue) . ")";

        $strQuery .= "UNION
                      (SELECT
                      email_id as id,component_id,box_number,'alias' as type,isp,username,
                      login,password,'redirect',mlist_options,delivery_method,
                      smtp_delivery_ip,status, ma.vchName, ma.intMailAliasID
                      FROM config_email AS ce
                      INNER JOIN tblMailAlias AS ma
                      ON ce.email_id = ma.intConfigEmailID
                      INNER JOIN dbProductComponents.tblStatus AS pcs
                      ON pcs.intStatusID = ma.intStatusID
                      WHERE ce.component_id = '".$this->_intComponentId."'
                      AND pcs.vchHandle IN('ACTIVE', 'QUEUED_ACTIVATE')
                      AND ce.status IN('active','queued-activate','queued-reactivate')";

        $strQuery .= str_replace('vchName', 'ma.vchName', $strCriteriaAndValue) . ")";

        switch($strSortBy){

                case "Email":

                        $strQuery .= "ORDER BY vchName ".$strOrder;

                    break;

                case "Type":

                         $strQuery .= "ORDER BY type, vchName ".$strOrder;
                    break;

                case "Dest":

                         $strQuery .= "ORDER BY redirect_to ".$strOrder;
                    break;

                default:

                         $strQuery .= "ORDER BY vchName ".$strOrder;
        }

        $strQuery .= " LIMIT ".implode(',', $arrLimit);

        return Mail_Db::getInstance()->getArray($strQuery, 'userdata');
    }



    /**
     * Returns default mailbox for this account
     * It has to find a single default mailbox or will throw an Exception
     *
     * @return Mail_Mailbox
     * @throws Mail_Exception
     */
    public function getDefaultMailbox()
    {
        $arrWhere = array('component_id' => $this->_intComponentId,
                                'status' => self::$_arrVisibleStatuses,
                                  'type' => 'default');
        $arrMailboxes = self::findMailboxes($arrWhere);
        //there has to be one default mailbox per component
        if (1 == count($arrMailboxes)) {
            return $arrMailboxes[0];
        } else {
            throw new Mail_Exception('Failed to retrieve single default mailbox');
        }
    }

    /**
     * Checks if mailbox of specified type and name exist
     *
     * @param string $strType
     * @param string $strName
     * @return boolean
     * @throws Mail_Exception
     */
    public function mailboxExists($strType, $strName)
    {
        $arrWhere = array('component_id' => $this->_intComponentId,
                                'status' => self::$_arrVisibleStatuses,
                                  'type' => $strType,
                                 'login' => $strName);
        if (1 == count(self::findMailboxes($arrWhere))) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Returns mailbox that has catchall option turned on (should be only 1)
     *
     * @return mixed Mail_Mailbox or false if not found
     */
    public function getCatchAllMailbox()
    {
        $arrWhere = array('component_id' => $this->_intComponentId,
                                'status' => self::$_arrVisibleStatuses,
                                  'type' => self::$_arrTypes['mailbox']);
        $arrMailboxes = self::findMailboxes($arrWhere);
        $arrTemp = array();
        if (!empty($arrMailboxes)) {
            foreach ($arrMailboxes as $objMailbox) {
                if ($objMailbox->isCatchAll()) {
                    $arrTemp[] = $objMailbox;
                }
            }
        }

        //there has to be either one or zero mailboxes with catchall turned on
        if (1 === count($arrTemp)) {
            return $arrTemp[0];
        } else if (0 === count($arrTemp)) {
            return false;
        } else {
            throw new Mail_Exception('Failed to retrieve single catchall mailbox');
        }
    }

        /**
         * This method will determine if the user has a postmaster address
         *
         * <AUTHOR> Jones" <<EMAIL>>
         * @return      boolean true or false
         */
        public function hasPostmasterAddress()
        {
                $arrWhere = array('component_id' => $this->_intComponentId,
                                  'login'        => 'postmaster',
                                  'status'       => 'active');

                $arrMailboxes = $this->findMailboxes($arrWhere);
                $objMailbox = $arrMailboxes[0];

                if ($objMailbox instanceof Mail_Mailbox
                        || $objMailbox instanceof Mail_Redirect) {
                        return true;
                } else {
                        $arrAlias = $this->getAliasMailboxes('username', 1, 'postmaster');

                        if (false === $arrAlias) {
                                return false;
                        } else {
                                return true;
                        }
                }
        } // public function hasPostmasterAddress()

    /**
     * Returns number of mailboxes of specified type
     *
     * @param string type
     * @throws Mail_Exception
     */
    public function getMailboxCount($strType)
    {
        if ('alias' == $strType) {
            return $this->getAliasMailboxCount();
        } else {
            if (!array_key_exists($strType, self::$_arrTypes)) {
                throw new Mail_Exception("Invalid type '$strType', use: " . implode(',', array_keys(self::$_arrTypes)));
            }
            $strTypes = "'" . implode("','", self::$_arrTypes[$strType]) . "'";
            $strStatuses = "'" . implode("','", self::$_arrVisibleStatuses) . "'";

            $strQuery = "SELECT count(email_id) as count "
                       . "FROM config_email as ce "
                       . "WHERE ce.type IN($strTypes) "
                       . "AND ce.status IN($strStatuses) "
                       . 'AND ce.component_id = ' . $this->_intComponentId;

            $intMailboxCount = Mail_Db::getInstance()->getSingleValue($strQuery, 'count', 'userdata');

            if ($strType == 'any') {

                $intMailboxCount += $this->getAliasMailboxCount();
            }

            return $intMailboxCount;

        }
    }

    /**
     * Returns array of mailboxes, redirects, lists...
     *
     * @return mixed array of mailboxes or null if non found
     */
        public function getMailboxes($strType, $order = null, $limit = null,
            $search = null, $arrCriteria = NULL)
        {
                if (!array_key_exists($strType, self::$_arrTypes)) {

                    throw new Mail_Exception(
                        "Invalid type '$strType', use: " . implode(
                            ', ', array_keys(self::$_arrTypes)
                        )
                    );

                }


                $orderby = "type, login {$order} ";


                if ('alias' == $strType) {

                        //a bit of a hack

                        return $this->getAliasMailboxes($orderby, $limit, $search);
                }

                $arrWhere = array('component_id' => $this->_intComponentId,
                                        'status' => self::$_arrVisibleStatuses,
                                          'type' => self::$_arrTypes[$strType]);

                if ($search !== NULL && $search != '') {

                        $arrWhere['login'] = $search;


                        // The following bit of code has been added to emulate existing behaviour on live..
                        // As to whether it's correct (or broken) waits to be seen, but including it for now
                        // for backward compatibility purposes...

                        if ($arrCriteria === NULL) {

                                $arrCriteria = array('login' => self::SEARCH_CRITERIA_CONTAINS);

                        }
                }

                $arrMailboxes = self::findMailboxes($arrWhere, $orderby, $limit, $arrCriteria, $strType);

                if (!empty($arrMailboxes)) {

                        $arrReturn = array();
                        $userDomain = $this->getUserDomain();

                        foreach ($arrMailboxes as $objMailbox) {

                                //extract information from each mailbox that will be passed to smarty

                                $arrMailbox['id'] = (int)$objMailbox->email_id;
                                $arrMailbox['status'] = $objMailbox->status;
                                $arrMailbox['type'] = $objMailbox->type;
                                $arrMailbox['password'] = $objMailbox->password;
                                $arrMailbox['catchall'] = $objMailbox->isCatchAll();


                                //set checked options for mailing list

                                if ($objMailbox instanceof Mail_List) {

                                        $arrMailbox['options'] = array_flip(explode(',', $objMailbox->mlist_options));

                                        foreach ($arrMailbox['options'] as &$value) {

                                                $value = true;

                                        }

                                        unset($value);
                                }


                                //set redirect target

                                if ($objMailbox instanceof Mail_Redirect) {

                                        $arrMailbox['redirect_to'] = $objMailbox->redirect_to;

                                }

                                $arrMailbox['login'] = $objMailbox->login;
                                $arrMailbox['domain'] = $userDomain;

                                $arrReturn[] = $arrMailbox;

                                unset($arrMailbox);
                        }
                }

                return $arrReturn;
        }

    /**
    * Gets the mailbox details based on id
    *
    * @return array - Returns mailbox by id
    * @param integer - Mailbox ID
    */

    public function getMailboxById($intMailboxId)
    {

        $strQuery = "SELECT email_id as id,component_id,box_number,type,isp,username,
                        login,password,redirect_to,mlist_options,delivery_method,
                        smtp_delivery_ip,status FROM config_email
                        WHERE email_id = '".$intMailboxId."'";

        return Mail_Db::getInstance()->getArray($strQuery, 'userdata');
    }

    /**
     * Returns number of aliases
     *
     * @return integer
     */
    public function getAliasMailboxCount()
    {
        $strQuery = 'SELECT count(email_id) as count '
                  . 'FROM config_email '
                  . 'INNER JOIN tblMailAlias ON email_id=intConfigEmailID '
                  . 'WHERE component_id = ' .$this->_intComponentId;

        return Mail_Db::getInstance()->getSingleValue($strQuery, 'count', 'userdata');
    }

    /**
     * Returns array of mailboxes that have aliases
     *
     * @return mixed array of mailboxes or null if non found
     */
    public function getAliasMailboxes($orderby, $limit, $search = null, $arrCriteria = null)
    {
        //first get a list of mailboxes that have aliases and match search parameters
        $strQuery = 'SELECT DISTINCT email_id '
                  . 'FROM config_email '
                  . 'INNER JOIN tblMailAlias ON email_id=intConfigEmailID '
                  . 'INNER JOIN dbProductComponents.tblStatus using(intStatusID) '
                  . "WHERE component_id = $this->_intComponentId "
                  . "AND vchHandle IN('ACTIVE', 'QUEUED_ACTIVATE')";

        if ($search !== NULL && $search != '') {

                if ($arrCriteria !== NULL && is_array($arrCriteria) && isset($arrCriteria['login'])) {

                        $intCriteria = $arrCriteria['login'];

                } else {

                        $intCriteria = self::SEARCH_CRITERIA_EXACT;

                }

                $value = mysql_real_escape_string($search);

                switch ($intCriteria) {

                        case self::SEARCH_CRITERIA_BEGINS_WITH:
                                $strCriteriaAndValue = " AND vchName LIKE '" . addcslashes($value, '_%') . '%';
                            break;

                        case self::SEARCH_CRITERIA_ENDS_WITH:
                                $strCriteriaAndValue = " AND vchName LIKE '%" . addcslashes($value, '_%');
                            break;

                        case self::SEARCH_CRITERIA_CONTAINS:
                                $strCriteriaAndValue = " AND vchName LIKE '%" . addcslashes($value, '_%') . '%';
                            break;

                        case self::SEARCH_CRITERIA_EXACT:
                        default:
                                $strCriteriaAndValue = " AND vchName = '{$value}";
                            break;
                }


                $strQuery .= $strCriteriaAndValue ."'";
        }

        $arrEmailIds = Mail_Db::getInstance()->getList($strQuery, 'userdata');
        if (empty($arrEmailIds)) {
            return false;
        }

        // now retrieve those mailboxes using find
        $arrWhere = array(
            'component_id' => $this->_intComponentId,
            'status'       => self::$_arrVisibleStatuses,
            'type'         => self::$_arrTypes['alias'],
            'email_id'     => $arrEmailIds
        );

        $arrMailboxes = self::findMailboxes($arrWhere, $orderby, $limit);

        if (!empty($arrMailboxes)) {
            $arrReturn = array();
            $userDomain = $this->getUserDomain();

            foreach ($arrMailboxes as $objMailbox) {
                $arrMailbox['id']       = (int)$objMailbox->email_id;
                $arrMailbox['status']   = $objMailbox->status;
                $arrMailbox['catchall'] = $objMailbox->isCatchAll();
                $arrMailbox['type']     = $objMailbox->type;
                $arrMailbox['login']    = $objMailbox->login;
                $arrMailbox['domain']   = $userDomain;
                $arrMailbox['aliases']  = array();

                foreach ($objMailbox->getAliases() as $objAlias) {
                    // display only aliases matching search clause
                    if ($search && strpos($objAlias->vchName, $search) === false) {
                        continue;
                    }

                    $arrMailbox['aliases'][] = array(
                        'name'   => $objAlias->vchName,
                        'id'     => (int)$objAlias->intMailAliasID,
                        'status' => $objAlias->strStatus
                    );
                }

                // mark last alias so different image can be displayed
                $intLastIndex = count($arrMailbox['aliases']) - 1;
                $arrMailbox['aliases'][$intLastIndex]['last'] = true;
                $arrReturn[] = $arrMailbox;
            }

            return $arrReturn;
        }
    }

    /**
     * Checks existing mailboxes and alises to see if login is already used
     *
     * @return boolean
     */
    public function isLoginAvailable($strName, $bolMailboxOnly = false)
    {

        //catchall.email is reserved for ironport catchall config record
        //customers are not allowed this address
        if ($strName == 'catchall.email') {
                return FALSE;
        }

        if ($this->getDefaultMailbox()->isLoginAvailableOnDomain($strName)) {

                return false;
        }

        $arrMailboxes = self::findMailboxes(
            array('component_id' => $this->_intComponentId,
                  'status' => self::$_arrVisibleStatuses
            )
        );
        if (is_array($arrMailboxes) and !empty($arrMailboxes)) {
            $arrExistingLogins = array();
            foreach ($arrMailboxes as $objMailbox) {
                $arrExistingLogins[] = $objMailbox->login;
                if ($objMailbox->type == 'list') {
                        $arrExistingLogins[] = $objMailbox->login . '-subscribe';
                        $arrExistingLogins[] = $objMailbox->login . '-unsubscribe';
                        $arrExistingLogins[] = $objMailbox->login . '-list';
                }
                //check aliases too
                if (!$bolMailboxOnly) {
                        $arrAliases = $objMailbox->getAliases();
                        if (!empty($arrAliases)) {
                                $arrExistingLogins = array_merge($arrExistingLogins, array_keys($arrAliases));
                        }
                }
            }
            if (in_array($strName, $arrExistingLogins, TRUE) ||
               in_array($strName . '-subscribe', $arrExistingLogins, TRUE) ||
               in_array($strName . '-unsubscribe', $arrExistingLogins, TRUE) ||
               in_array($strName . '-list', $arrExistingLogins, TRUE) ) {
                return false;
            } else {
                return true;
            }
        } else {
            //no mailboxes?
            return true;
        }
    }

    /**
     * Adds catch all option to the specified mailbox (removes from any other if present)
     *
     * @param integer $intMailboxId
     * @return boolean success or failure
     */
    public function enableCatchAll($intMailboxId)
    {
        try {
            //remove existing catchall before proceeding
                        $bolMovingCatchAll = true;
            $this->disableCatchAll($bolMovingCatchAll);

            //enable it on selected mailbox
            $objMailbox = self::findMailboxById($intMailboxId);
            if ($objMailbox instanceof Mail_Mailbox) {
                $objMailbox->enableCatchAll();
                // update tblCatchAllOptionLog if the entry exists
                $strQuery = "UPDATE tblCatchAllOptionLog SET bolRemove='0' WHERE intComponentId = '{$this->_intComponentId}' LIMIT 1";
                Mail_Db::getInstance()->query($strQuery, 'userdata');
            } else {
                throw new Mail_Exception('Invalid Mail_Mailbox object');
            }
            return true;
        } catch (Mail_Exception $e) {
            return false;
        }
    }

    /**
     * Return generated name for default alias
     *
     * @return string
     * @throws Mail_Exception
     */
    public function generateDefaultAliasName()
    {
        $strQuery = "SELECT s.username, lower(substring_index(u.forenames,' ',1)) AS firstname "
                  . "FROM components c INNER JOIN services s USING (service_id) "
                  . "INNER JOIN users u using (user_id) "
                  . "WHERE component_id = $this->_intComponentId";
        $arrUser = Mail_Db::getInstance()->getArray($strQuery, 'userdata');
        if (empty($arrUser)) {
            throw new Mail_Exception("Failed to load service details for component id: $this->_intComponentId");
        }
        $strFirstname = $arrUser[0]['firstname'];
        $strUsername = $arrUser[0]['username'];
        //if alias generated from firstname is not valid use the username
        preg_match('/^[a-z0-9_\.-]{1,45}$/', $strFirstname)
            ? $strAliasName = $strFirstname : $strAliasName = $strUsername;
        return $strAliasName;
    }

    /**
     * Removes catch all option from mailbox that has it (should be only one per account)
     *
     * @return boolean success or failure
     */
    public function disableCatchAll($bolMovingCatchAll = false)
    {
        try {
                        if (!$bolMovingCatchAll) {
                                if (!$this->hasPostmasterAddress()) {
                                        $this->autoCreatePostmasterAlias();
                                }
                        }

            $objCatchAllMailbox = $this->getCatchAllMailbox();
            if ($objCatchAllMailbox instanceof Mail_Mailbox) {
                $objCatchAllMailbox->disableCatchAll();
                // update tblCatchAllOptionLog if the entry exists
                $strQuery = "UPDATE tblCatchAllOptionLog SET bolRemove='1' WHERE intComponentId = '{$this->_intComponentId}' LIMIT 1";
                Mail_Db::getInstance()->query($strQuery, 'userdata');
            }
            return true;
        } catch (Mail_Exception $e) {
            return false;
        }
    }

    /**
     * Adds mailbox from array
     * It's included in php4 code and cannot throw exceptions
     *
     * @param array $arrArgs
     */
    public function addMailbox($arrArgs)
    {
        if (!is_array($arrArgs) or empty($arrArgs)) {
            return false;
        }
        try {

            $arrMailboxParams = array(
                                       'component_id'     => $this->_intComponentId,
                                       'login'            => $arrArgs['login'],
                                       'type'             => $arrArgs['type'],
                                       'password'         => $arrArgs['password'],
                                       'isp'              => $arrArgs['isp'],
                                       'username'         => $arrArgs['username'],
                                       'delivery_method'  => $arrArgs['delivery_method'],
                                       'smtp_delivery_ip' => $arrArgs['smtp_delivery_ip']
                                     );
            $objMailbox = new Mail_Mailbox($arrMailboxParams);
            $objMailbox->create();
            $objMailbox->enable();
            return $objMailbox->email_id;
        } catch (Mail_Exception $e) {
            return false;
        }
    }

    /**
     * Creates new mailbox and saves it to database
     *
     * @param string login for new mailbox
     * @param string password for new mailbox
     * @return boolean success or failure
     */
    public function addSubMailbox($strName, $strPassword)
    {
        try {
            if (!is_string($strName) or !is_string($strPassword)) {
                throw new Mail_Exception("Failed addSubMailbox($strName, $strPassword)");
            }
            //required data is copied over from default mailbox
            $objDefaultMailbox = $this->getDefaultMailbox();

            $arrMailboxParams = array(
                                       'component_id'     => $this->_intComponentId,
                                       'login'            => $strName,
                                       'type'             => 'mailbox',
                                       'password'         => $strPassword,
                                       'isp'              => $objDefaultMailbox->isp,
                                       'username'         => $objDefaultMailbox->username,
                                       'delivery_method'  => $objDefaultMailbox->delivery_method,
                                       'smtp_delivery_ip' => ''
                                     );

            $objMailbox = new Mail_Mailbox($arrMailboxParams);
            $objMailbox->create();
            $objMailbox->enable();
            return true;
        } catch (Mail_Exception $e) {
            return false;
        }
    }

        /**
         * Destroys mailbox
         *
         * @param       Mailbox ID to destroy
         */
        public function destroyMailbox($intMailboxId)
        {
                $objMailbox = $this->findMailboxById($intMailboxId);
                $strMailboxName = $objMailbox->login;

                // Ensure we have a postmaster address
                if ($objMailbox->login == 'postmaster'
                                || ($objMailbox->isCatchAll() && !$this->hasPostmasterAddress())) {
                        $this->autoCreatePostmasterAlias();
                }

                $objMailbox->destroy();
        }

    /**
     * Destroys all mailboxes, aliases, redirects, mlists for the particluar component id
     *
     * @return true on success, false otherwise
     */
    public function destroy()
    {
        try {
          // Find all submailboxes and redirects
            $arrMailboxes = $this->findMailboxes(
                array('status' => array('active', 'queued-activate', 'queued-destroy'),
                     'component_id' => $this->_intComponentId,
                     'type' => array('mailbox', 'redirect')
                 )
            );
            if (!empty($arrMailboxes)) {
              foreach ($arrMailboxes as $objMailbox) {
                $objMailbox->destroy(false);
              }
            }
            // Find the default mailbox
            $arrMailboxes = $this->findMailboxes(
                array('status' => array('active', 'queued-activate', 'queued-destroy'),
                      'component_id' => $this->_intComponentId,
                      'type' => 'default'
                )
            );
            if (!empty($arrMailboxes)) {
              foreach ($arrMailboxes as $objMailbox) {
                $objMailbox->destroy(true);
              }
            }

            return true;
        } catch (Mail_Exception $e) {
            return false;
        }
    }

    /**
     * Refreshes individual mailboxes
     *
     * @return true on succes, false otherwise
     */
    public function refresh()
    {
        try {
                        $this->addTLK();
                        $arrMailboxes = $this->findMailboxes(
                            array('status' => array('active', 'queued-activate', 'queued-reactivate'),
                                  'component_id' => $this->_intComponentId
                            )
                        );

            foreach ($arrMailboxes as $objMailbox) {
              $objMailbox->refresh();
            }
            return true;
        } catch (Mail_Exception $e) {
            return false;
        }
    }

    /**
     * Checks if the product for this component id is free
     *
     * @throws Mail_Exception
     */
    public function isFreeProduct()
    {
        $strQuery = "SELECT type FROM userdata.services "
                  . 'INNER JOIN userdata.components using(service_id) '
                  . "WHERE component_id = $this->_intComponentId";
        $intType = Mail_Db::getInstance()->getSingleValue($strQuery, 'type', 'userdata');
        require_once('/local/data/mis/database/database_libraries/product-access.inc');
        if (product_service_is_free($intType)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Checks if customer can have additional aliases (they are limited for free products)
     *
     * @throws Mail_Exception
     */
    public function canHaveMoreAliases()
    {
        if ($this->isFreeProduct()) {
            //count current number of aliases
            $strQuery = 'SELECT count(*) as count FROM config_email '
                      . 'INNER JOIN tblMailAlias ON email_id = intConfigEmailID '
                      . 'INNER JOIN dbProductComponents.tblStatus USING(intStatusID) '
                      . "WHERE vchHandle IN('ACTIVE', 'QUEUED_ACTIVATE') "
                      . "AND component_id = $this->_intComponentId";
            $intCount = Mail_Db::getInstance()->getSingleValue($strQuery, 'count', 'userdata');
            if ($intCount < self::MAX_FREE_ALIASES) {
                return true;
            } else {
                return false;
            }
        } else {
            //unlimited if product not free
            return true;
        }
    }

    /**
     * Adds new alias to the specified mailbox
     *
     * @param string $strName
     * @param integer $intMailboxId
     * @return boolean success or failure
     */
    public function addAlias($strName, $intMailboxId)
    {
        try {
            $objMailbox = self::findMailboxById($intMailboxId);
            if (!$objMailbox instanceof Mail_Mailbox) {
                throw new Mail_Exception('Invalid Mail_Mailbox object');
            }
            $objMailbox->addAlias($strName);
            return true;
        } catch (Mail_Exception $e) {
            return false;
        }
    }

    /**
     * Removes alias from the specified mailbox
     *
     * @param string $strName
     * @param integer $intMailboxId
     * @return boolean success or failure
     */
    public function removeAlias($strName, $intMailboxId)
    {
        try {
            $objMailbox = self::findMailboxById($intMailboxId);
            if (!$objMailbox instanceof Mail_Mailbox) {
                throw new Mail_Exception('Invalid Mail_Mailbox object');
            }
            $objMailbox->removeAlias($strName);
            return true;
        } catch (Mail_Exception $e) {
            return false;
        }
    }

        /**
         * Transfers alias from one mailbox to another (used in control panel)
         *
         * @param integer $intAliasId
         * @param integer $intTargetMailboxId
         * @return boolean success or failure
         */
        public function moveAlias($intAliasId, $intTargetMailboxId)
        {
                try {
                        Mail_Db::getInstance()->begin('userdata', 'maildelivery');

                        $objMailbox = self::findMailboxById($intTargetMailboxId);
                        if (!$objMailbox instanceof Mail_Mailbox) {
                                throw new Mail_Exception('Invalid Mail_Mailbox object');
                        }

                        $objAlias = Mail_Alias::findById($intAliasId);
                        if (!$objAlias instanceof Mail_Alias) {
                                throw new Mail_Exception('Invalid Mail_Alias object');
                        }
                        $objMailbox->moveAlias($objAlias->vchName);
                        $strQuery = 'UPDATE userdata.tblMailAlias '
                                    . "SET intConfigEmailID = $intTargetMailboxId "
                                    . "WHERE intMailAliasID = $intAliasId";
                        Mail_Db::getInstance()->query($strQuery, 'userdata');
                        Mail_Db::getInstance()->commit('userdata', 'maildelivery');
                        return true;
                } catch (Mail_Exception $e) {
                        Mail_Db::getInstance()->rollback('userdata', 'maildelivery');
                        return false;
                }
        }

    /**
     * Adds new redirect
     *
     * @param string $strName
     * @param string $strDestination
     * @return boolean success or failure
     */
    public function addRedirect($strName, $strDestination)
    {
        try {
            //required data is copied over from default mailbox
            $objDefaultMailbox = $this->getDefaultMailbox();

            $arrRedirectParams = array(
                                      'component_id'     => $this->_intComponentId,
                                      'login'            => $strName,
                                      'type'             => 'redirect',
                                      'redirect_to'      => $strDestination,
                                      'isp'              => $objDefaultMailbox->isp,
                                      'username'         => $objDefaultMailbox->username
                                     );
            $objRedirect = new Mail_Redirect($arrRedirectParams);
            $objRedirect->create();
            $objRedirect->enable();
            return true;
        } catch (Mail_Exception $e) {
            return false;
        }
    }

    /**
     * Adds new mailing list
     *
     * @param string $strName
     * @param array $arrOptions
     * @return boolean success or failure
     */
    public function addList($strName, $arrOptions)
    {
        try {
            //required data is copied over from default mailbox
            $objDefaultMailbox = $this->getDefaultMailbox();

            $arrMailListParams = array(
                                       'component_id'     => $this->_intComponentId,
                                       'login'            => $strName,
                                       'type'             => 'list',
                                       'mlist_options'    => implode(',', $arrOptions),
                                       'isp'              => $objDefaultMailbox->isp,
                                       'username'         => $objDefaultMailbox->username
                                     );
            $objList = new Mail_List($arrMailListParams);
            $objList->create();
            return true;
        } catch (Mail_Exception $e) {
            return false;
        }
    }

    /**
     * This method encapsulates php5 functionality needed by
     * userdata_service_set_password.method.php which is included by billing
     *
     * @param integer $intComopnentId
     * @param string $strNewPassword
     * @return bol success or failure
     */
     public static function changeDefaultPassword($intComopnentId, $strNewPassword)
     {
        try {
            $objMailManager = new self($intComopnentId);
            $objDefaultMailbox = $objMailManager->getDefaultMailbox();
            $objDefaultMailbox->changePassword($strNewPassword);
            return true;
        } catch (Exception $e) {
          return false;
        }
     }

    /**
     * Changes smtp settings for default mailbox
     *
     * @param string $strProtocol smtp|pop3
     * @param string $strIp
     * @return boolean success or failure
     */
    public function setSMTP($strProtocol, $strIp, $strDomainName = NULL)
    {
        try {
            $objDefaultMailbox = $this->getDefaultMailbox();
            //validated in frontend
            $objDefaultMailbox->smtp_delivery_ip = $strIp;
            $objDefaultMailbox->delivery_method = $strProtocol;
            $objDefaultMailbox->changeDeliveryMethod($strDomainName);

            $objDefaultMailbox->save();
            return true;
        } catch (Mail_Exception $e) {
            return false;
        }
    }

        /**
         * getSpamFilterType
         *
         * @param integer $intVirtualDomainId
         * @access public
         * @return mixed - string on Success, false on Failure
         */
        public function getSpamFilterType($intVirtualDomainId)
        {
                try {
                        $objMailbox = $this->getDefaultMailbox();
                        return $objMailbox->getSpamFilter($intVirtualDomainId);
                }
                catch (Mail_Exception $mailException) {
                        return false;
                }
        }

        /**
         * getSpamAggressiveness
         *
         * @param integer $intVirtualDomainId
         * @access public
         * @return void
         */
        public function getSpamAggressiveness($intVirtualDomainId)
        {
                try{
                        $objMailbox = $this->getDefaultMailbox();
                        return $objMailbox->getSpamAggressiveness($intVirtualDomainId);
                }
                catch(Mail_Exception $e){

                        return FALSE;
                }
        }

        /**
         * setSpamFilterType
         *
         * @param integer $intSettingId
         * @param integer $intAggressiveness
         * @param boolean $bolQNotification
         * @param boolean $bolAntiVirus
         * @param boolean $bolSpam
         * @param boolean $bolBsb
         * @param mixed $unkDomain
         * @param boolean $bolSpamTag
         * @param integer $intMailboxId
         * @param string $strNotificationEmail
         * @param boolean $bolBlackhole
         * @access public
         * @return void
         */
        public function setSpamFilterType($intSettingId, $intAggressiveness,
            $bolQNotification, $bolAntiVirus, $bolSpam, $bolBsb, $unkDomain,
            $bolSpamTag, $intMailboxId, $strNotificationEmail, $bolBlackhole)
        {
                try {
                        $arrMailbox = $this->getMailboxById($intMailboxId);

                        $objMailbox = $this->getDefaultMailbox();
                        $objMailbox->setSpamFilter(
                            $intSettingId, $intAggressiveness, $bolQNotification, $bolAntiVirus, $bolSpam,
                            $bolBsb, $unkDomain, $bolSpamTag, $arrMailbox, $strNotificationEmail, $bolBlackhole
                        );
                        return true;
                }
                catch (Mail_Exception $e) {
                        throw new Mail_Exception('Unable to set Spam Filter Type: '. $e->getMessage());
                }
        }

        /**
         * clearEmailList
         *
         * @param mixed $unkVirtualDomain
         * @access public
         * @return void
         */
        public function clearEmailList($unkVirtualDomain)
        {
            try{
                $objMailbox = $this->getDefaultMailbox();
                $objMailbox->clearEmailList($unkVirtualDomain);

                return TRUE;
            } catch (Mail_Exception $e) {

                throw new Mail_Exception(
                    'Unable to clean approved/blocked lists with postini: '.
                    $e->getMessage()
                );
            }
        }

        /**
         * setApprovedEmailList
         *
         * @param string $strApprovedSenders
         * @access public
         * @return void
         */
        public function setApprovedEmailList($strApprovedSenders=NULL,$unkDomain)
        {
                try{
                        $objMailbox = $this->getDefaultMailbox();
                        $objMailbox->setApprovedEmailList($strApprovedSenders, $unkDomain);

                        return TRUE;
                }
                catch(Mail_Exception $e){

                        throw new Mail_Exception('Unable to set Approved Mailing List');
                }
        }

        /**
        * setBlockedEmailList
        *
        * @param string $strBlockedSenders
        * @access public
        * @return void
        */
        public function setBlockedEmailList($strBlockedSenders=NULL,$unkDomain)
        {
                try{
                        $objMailbox = $this->getDefaultMailbox();
                        $objMailbox->setBlockedEmailList($strBlockedSenders, $unkDomain);

                        return TRUE;
                }
                catch(Mail_Exception $e){

                        throw new Mail_Exception('Enable to set Blocked Mailing List');
                }
        }

        /**
         * getEmailList
         *
         * @param integer $intVirtualDomainId
         * @access public
         * @return void
         */
        public function getEmailList($intVirtualDomainId)
        {
                try{
                        $objMailbox = $this->getDefaultMailbox();
                        return $objMailbox->getEmailList($intVirtualDomainId);
                }
                catch(Mail_Exception $e){

                        return FALSE;
                }
        }

        /**
         * setDefaultSpamFilterType
         * @access public
         * @return void
         */
        public function setDefaultSpamFilterType()
        {
                //set default settings.
                $intSettingId      = 3; //deliver to spam folder
                $intAggressiveness = 1;
                $bolQNotification  = 0;
                $bolAntiVirus      = 1;
                $bolSpam           = 1;
                $bolBsb            = 1;
                $unkDomain         = 'all';
                $bolSpamTag        = 1;
                $intMailboxId      = NULL;
                $bolBlackhole      = FALSE;

                return $this->setSpamFilterType(
                    $intSettingId, $intAggressiveness, $bolQNotification, $bolAntiVirus,
                    $bolSpam, $bolBsb, $unkDomain, $bolSpamTag, $intMailboxId, '', $bolBlackhole
                );
        }

        /**
         * Performing certain actions will automatically create a postmaster
         * alias. This does that trick, but partner eu accounts with a partner that
         * owns a domain cannot have a postmaster
         *
         * @return      void
         */
        protected function autoCreatePostmasterAlias()
        {
                $component = userdata_component_get($this->_intComponentId);
                if (is_null(Reseller_DomainHelper::getByServiceId(new Int($component['service_id'])))) {

                        // Retrieve default mailbox
                        $objMailbox = $this->getDefaultMailbox();

                        // Create alias to default mailbox
                        $objMailbox->addAlias('postmaster');
                }
        }

        /**
         * Get the Service object based on component above
         */
        private function getService()
        {
                if (! is_a($this->_objService, 'C_Core_Service')) {
                        $strQuery = "SELECT service_id AS intServiceId
                                       FROM userdata.components com
                                      WHERE component_id = {$this->_intComponentId}";
                        $intServiceId = Mail_Db::getInstance()->getSingleValue($strQuery, 'intServiceId', 'userdata');
                        $this->_objService = new C_Core_Service($intServiceId);
                }

                return $this->_objService;
        }

    /**
     * Returns single Mail_Mailbox object based on specified email_id
     *
     * @param integer email_id
     * @return Mail_Mailbox
     * @throws Mail_Exception
     */
    public static function findMailboxById($intId)
    {
        if (!is_numeric($intId)) {
            throw new Mail_Exception('$intId should be a numeric value');
        }
        $arrRows = self::findMailboxes(array('email_id' => $intId));
        if (count($arrRows) === 1) {
            return $arrRows[0];
        }
    }

    /**
     * Returns array of Mail_Mailbox objects based on specified parameters
     *
     * @param array $where ('field1'=>value, field2=>array('value1',value2'))
     * @param string|array $orderby field name or array('field','asc|desc')
     * @param integer|array $limit limit or array('offset','limit')
     * @return mixed array | null
     * @throws Mail_Exception
     */
    public static function findMailboxes($where, $orderby = null, $limit = null, $arrCriteria = NULL, $strType = NULL)
    {
        $arrRows = self::fetchMailboxes($where, $orderby, $limit, $arrCriteria, $strType);
        $arrReturn = array();
        if ($arrRows) {
            foreach ($arrRows as $row) {
                $arrReturn[] = self::factory($row);
            }
            return $arrReturn;
        }
    }

    /**
     * Factory method to create mailbox based on the type read from database
     *
     * @param array row from config_email
     * @return Mail_Mailbox
     * @throws Mail_Exception
     */
    public static function factory($row)
    {
        if (in_array($row['type'], self::$_arrTypes['mailbox'])) {
            return new Mail_Mailbox($row);
        } else if (in_array($row['type'], self::$_arrTypes['redirect'])) {
            return new Mail_Redirect($row);
        } else if (in_array($row['type'], self::$_arrTypes['list'])) {
            return new Mail_List($row);
        } else {
            throw new Mail_Exception('Factory method failed for ' . print_r($row));
        }
    }

        /**
         * Reads rows from config_email table based on specified parameters
         *
         * @param array $where ('field1'=>value, field2=>array('value1',value2'))
         * @param string|array $orderby field name or array('field','asc|desc')
         * @param integer|array $limit limit or array('offset','limit')
         * @return array
         * @throws Mail_Exception
         */
        private static function fetchMailboxes($where, $orderby = null,
            $limit = null, $arrCriteria = NULL, $strType = NULL)
        {

                $strFields = implode(',', self::$_arrFields);

                if ($strType == 'any') {

                        $strFields .= ",ma.intConfigEmailID,ma.vchName";
                }

                $strQuery = "SELECT $strFields FROM config_email ";

                if ($strType == 'any') {

                        $strQuery .= " LEFT JOIN tblMailAlias as ma
                                       ON ma.intConfigEmailID = config_email.email_id ";
                }

                $strQuery .= "WHERE ";

                //process where conditions

                $arrConditions = array();

                foreach ($where as $field => $value) {

                        if (!in_array($field, self::$_arrFields)) {

                                throw new Mail_Exception("Cannot use $field to find a mailbox". print_r($where, true));

                        }


                        //multiple values - IN()

                        if (is_array($value)) {

                                $arrConditions[] = "$field IN('" . implode("','", $value) . "')";

                                //single value - LIKE/=

                        } else if (is_string($value) || is_numeric($value)) {

                                // If a field-specific search criteria has been given, then use it.
                                // Otherwise, simply default to the 'exact' match mode, which is especially
                                // useful when comparing component/mailbox IDs.. (LIKE '%..%' would be a *disaster*!)

                                if ($arrCriteria !== NULL && is_array($arrCriteria) && isset($arrCriteria[$field])) {

                                        $intCriteria = $arrCriteria[$field];

                                } else {

                                        $intCriteria = self::SEARCH_CRITERIA_EXACT;

                                }

                                $value = mysql_real_escape_string($value);

                                switch ($intCriteria) {

                                        case self::SEARCH_CRITERIA_BEGINS_WITH:
                                                $strCriteriaAndValue = "LIKE '" . addcslashes($value, '_%') . '%';
                                            break;

                                        case self::SEARCH_CRITERIA_ENDS_WITH:
                                                $strCriteriaAndValue = "LIKE '%" . addcslashes($value, '_%');
                                            break;

                                        case self::SEARCH_CRITERIA_CONTAINS:
                                                $strCriteriaAndValue = "LIKE '%" . addcslashes($value, '_%') . '%';
                                            break;

                                        case self::SEARCH_CRITERIA_EXACT:
                                        default:
                                                $strCriteriaAndValue = "= '{$value}";
                                            break;
                                }

                                $arrConditions[] = " $field $strCriteriaAndValue' ";

                        } else {

                            throw new Mail_Exception(
                                "Cannot handle field type of $field in ".
                                print_r($where, true)
                            );

                        }

                }

                $strQuery .= implode(' AND ', $arrConditions);


                //process order clause

                if ($orderby) {

                        if (is_string($orderby)) {

                                $strQuery .= " ORDER BY $orderby ";

                        }
                }


                //process limit clause

                if ($limit) {

                        if (is_array($limit) && count($limit) == 2 && is_numeric($limit[0]) && is_numeric($limit[1])) {

                                $strQuery .= ' LIMIT ' . $limit[0] . ',' . $limit[1] . ' ';

                        } else if (is_numeric($limit)) {

                                $strQuery .= " LIMIT $limit ";

                        } else {

                                throw new Mail_Exception('Invalid limit parameters: '. print_r($limit, true));

                        }
                }

                return Mail_Db::getInstance()->getArray($strQuery, 'userdata');
        }

        /**
         * Returns back the address that the system uses when we want to blackhole
         * email addresses/redirects, etc.
         *
         * @access static
         * @return string
         */
        public static function getBlackholeAddress()
        {
                return self::BLACKHOLE_EMAIL_ADDRESS;
        }

        /**
         * @param string $strContext
         * @access static
         * @return void
         */
        public static function setContext($strContext)
        {
                self::$strContext = $strContext;
        }

        /**
         * @access static
         * @return string $strContext
         */
        public static function getContext()
        {
                return self::$strContext;
        }

        /**
         * getSummary - retrives all mailboxes, aliases, lists and redirects from DB
         *
         * tblMailAlias.intStatusID ID 2,3 and 4 corespond to 'active','queued-activate',
         * 'queued-reactivate' statuses.
         *
         * @param string $strOrderBy
         * @access public
         * @return array
         */
        public function getSummary($strOrderBy)
        {
                $arrAllowedOrder = array('name', 'type', 'destination');

                if (empty($strOrderBy) || !in_array($strOrderBy, $arrAllowedOrder)) {
                        $strOrderBy = 'name';
                }

                $strQuery = '(SELECT login AS name, type, redirect_to AS destination '.
                            'FROM config_email '.
                                        "WHERE  component_id = '{$this->_intComponentId}' ".
                                        "AND status IN('active', 'queued-activate', 'queued-reactivate')) ".
                                        'UNION ALL '.
                                        "(SELECT ma.vchName as name, 'alias', ce.login as destination ".
                                        'FROM config_email ce '.
                                        'INNER JOIN tblMailAlias ma ON ce.email_id = ma.intConfigEmailID '.
                                        "WHERE ce.component_id = '{$this->_intComponentId}' ".
                                        'AND ma.intStatusID IN(2, 3, 4)) '.
                                        "ORDER BY $strOrderBy";

                return Mail_Db::getInstance()->getArray($strQuery, 'userdata');
        }

        public function addTLK()
        {
                $resDBHandle = get_named_connection_with_db('userdata');
                $this->getService();
                $strKey = substr($this->_objService->getUsername(), 0, 2);
                $strIsp = $this->_objService->getIsp();
                if (trim($strKey) == '' || trim($strIsp) == '') {
                        return false;
                }
                if ($strIsp == 'plus.net') {
                        $strIsp = "plusn";
                }
                $strKeyIsp = str_pad(substr($strKey.'_'.$strIsp, 0, 8), 8, ".");

                $strQuery = " SELECT key_isp, uid "
                          .   " FROM userdata.system_core_uids "
                          .  " WHERE key_isp = '".PrimitivesRealEscapeString($strKeyIsp, $resDBHandle)."' ";
                $resResult = PrimitivesQueryOrExit($strQuery, $resDBHandle);
                if (PrimitivesNumRowsGet($resResult) == 0 ) {
                    $strInsertQuery = "INSERT INTO userdata.system_core_uids ".
                        "(key_isp) VALUES ('".PrimitivesRealEscapeString($strKeyIsp, $resDBHandle)."') ";
                    PrimitivesQueryOrExit($strInsertQuery, $resDBHandle);
                }

                return true;
        }


    /**
     * Edit mailbox and saves it to database
     *
     * @param string password for new mailbox
     * @param array $arrMailBox  details of mailbox to be edited
     * @return boolean success or failure
     */
    public function updateSubMailbox($strPassword, $arrMailBox)
    {
        $strName = false;
        if (isset($arrMailBox['login'])) {
            $strName = $arrMailBox['login'];
        }
        try {
            if (!is_string($strName) || !is_string($strPassword)) {
                throw new Mail_Exception("Failed updateSubMailbox($strName, $strPassword)");
            }
            //required data is copied over from default mailbox
            $objDefaultMailbox = $this->getDefaultMailbox();

            $arrMailboxParams = array( 'email_id'         => $arrMailBox['id'],
                                       'component_id'     => $this->_intComponentId,
                                       'login'            => $strName,
                                       'type'             => $arrMailBox['type'],
                                       'password'         => $strPassword,
                                       'isp'              => $objDefaultMailbox->isp,
                                       'username'         => $objDefaultMailbox->username,
                                       'delivery_method'  => $objDefaultMailbox->delivery_method,
                                       'smtp_delivery_ip' => '',
                                       'status'           => $arrMailBox['status'],
                                       'redirect_to'      =>$arrMailBox['redirect_to'],
                                     );

            $objMailbox = new Mail_Mailbox($arrMailboxParams);
            $objMailbox->changePassword($strPassword);
            return true;
        } catch (Mail_Exception $e) {
            return false;
        }
    }

}
