<?php
	
	/**
	* Access library for Metronet Email component.  Contains component configurator and component class
	*
	* @package    Core
	* @subpackage Metronet
	* @access     public
	* <AUTHOR> <<EMAIL>>
	* @version    $Id: config-metronet-email-access.inc,v 1.3 2007-07-25 06:38:46 rjones Exp $
	* @filesource
	*/

	require_once('/local/data/mis/database/database_libraries/components/component-defines.inc');
	require_once('/local/data/mis/database/database_libraries/sql_primitives.inc');
	require_once('/local/data/mis/database/database_libraries/components/CComponent.inc');


	/* Globally executed code, make configurator entries for Metronet Email components */

	$dbhConn = get_named_connection_with_db('unprivileged_reporting');

	$strQuery = "SELECT scp.intServiceComponentID
	               FROM products.tblServiceComponentProduct scp
	         INNER JOIN products.tblServiceComponentProductType scpt
	                 ON scp.intServiceComponentProductTypeID = scpt.intServiceComponentProductTypeID
	              WHERE scpt.vchHandle = 'METRONET_EMAIL'";

	$refResult = PrimitivesQueryOrExit($strQuery, $dbhConn,'Make Metronet email configurator entries');
	$arrMetronetEmailConfigurators = PrimitivesResultsAsArrayGet($refResult);

	foreach($arrMetronetEmailConfigurators as $arrConfigurator)
	{
		$arrConfigurator['intServiceComponentID'] = $arrConfigurator['intServiceComponentID'] * 1;
		$global_component_configurators[$arrConfigurator['intServiceComponentID']] =
		                               "config_metronetemail_configurator";
	}

	// Hack to insert the component configurator array into PHP5's global scope
	// if it's not already there
	if(!isset($GLOBALS['global_component_configurators']))
	{
		$GLOBALS['global_component_configurators'] = $global_component_configurators;
	}
	else
	{
		foreach($global_component_configurators as $intIndex => $strConfigurator)
		{
			$GLOBALS['global_component_configurators'][$intIndex] = $strConfigurator;
		}
	}

	unset($arrMetronetEmailConfigurators);


	/**
	* Component configurator
	*
	* Follows standard component configurator patter.  Do not modify name or param list.
	*
	* @access public
	* @global
	* <AUTHOR>
	* @param integer component id
	* @param string action
	* @return integer -1 if the component does not exist, otherwise the result of the action performed
	*/
	function config_metronetemail_configurator ($intComponentID, $strAction) 
	{
		// Retrieve wifi components

		$objMetronetEmailProduct = CComponent::createInstance($intComponentID); 

		if(!$objMetronetEmailProduct)
		{
			return (-1);
		}

		switch ($strAction) 
		{
		    case "auto_configure":
				//Do nothing - the product should be added unconfigured.
				break;
			
		    case "auto_enable":
				return $objMetronetEmailProduct->enable();
				break;

		    case "auto_disable":
				return $objMetronetEmailProduct->disable();
				break;
				
		    case "auto_destroy":
				return $objMetronetEmailProduct->destroy();
				break;

		    case "auto_refresh":
		    	return $objMetronetEmailProduct->refreshCurrentState();
				break;

		    default:
				//Not a supported action
				return (-1);
				break;
		}
	}


?>
