<?php

/**
 * Plusnet Business Hosting Access Configurator
 *
 * @package   LegacyCodebase
 * <AUTHOR> <<EMAIL>>
 * @copyright 2013 Plusnet
 *
 */

require_once USERDATA_ACCESS_LIBRARY;
require_once PRODUCT_ACCESS_LIBRARY;
require_once SECURE_TRANSACTION_ACCESS_LIBRARY;
require_once FINANCIAL_ACCESS_LIBRARY;
require_once TICKETS_ACCESS_LIBRARY;
require_once '/local/data/mis/database/database_libraries/components/component-defines.inc';

$global_component_configurators[COMPONENT_PLUSNET_BUSINESS_HOSTING]  = "config_plusnetbusinesshosting_configurator";

if (!isset($GLOBALS['global_component_configurators'])) {
    $GLOBALS['global_component_configurators'] = $global_component_configurators;
} else {
    foreach ($global_component_configurators as $intIndex => $strConfigurator) {
        $GLOBALS['global_component_configurators'][$intIndex] = $strConfigurator;
    }
}

/**
 * Config Plusnet Business Hosting Auto Configure
 *
 * @param Int $componentId Component ID
 *
 * @return void
 */
function config_plusnetbusinesshosting_auto_configure($componentId)
{
    // Do nothing - user must ask for a plusnet business hosting to be activated
}

/**
 * Config Plusnet Business Hosting Auto Refresh
 *
 * @param Int $componentId Component ID
 *
 * @return void
 */
function config_plusnetbusinesshosting_auto_refresh($componentId)
{
    $component = userdata_component_get($componentId);
    switch ($component["status"]) {
        case "active":
            userdata_component_set_status($componentId, 'queued-reactivate');
            break;
        default:
            break;
    }
}

/**
 * Config Plusnet Business Hosting Auto Enable
 *
 * @param Int $componentId Component ID
 *
 * @return void
 */
function config_plusnetbusinesshosting_auto_enable($componentId)
{
    $component = userdata_component_get($componentId);

    switch ($component["status"]) {
        case 'queued-deactivate':
        case 'deactive':
            userdata_component_set_status($componentId, 'queued-reactivate');
            break;
        default:
            break;
    }
}

/**
 * Config Plusnet Business Hosting Auto Destroy
 *
 * @param Int $componentId Component ID
 *
 * @return void
 */
function config_plusnetbusinesshosting_auto_destroy($componentId)
{
    global $my_id;
    if (isset($my_id) && !empty($my_id) && !prCheckComponentHostingDestroyPermission($my_id)) {
        return "Sorry, you have no permission to destroy hosting component";
    }

    userdata_component_set_status($componentId, 'queued-destroy');

    if (plusnetbusinesshosting_destroy_ticket($componentId)) {
        userdata_component_set_status($componentId, 'destroyed');
    }
}

/**
 * Config Plusnet Business Hosting Configurator
 *
 * @param Int    $componentId Component ID
 * @param String $action      The action to undertake
 *
 * @return void
 */
function config_plusnetbusinesshosting_configurator($componentId, $action)
{
    switch ($action) {
        case 'auto_configure':
            config_plusnetbusinesshosting_auto_configure($componentId);
            break;
        case 'auto_refresh':
            config_plusnetbusinesshosting_auto_refresh($componentId);
            break;
        case 'auto_enable':
            config_plusnetbusinesshosting_auto_enable($componentId);
            break;
        case 'auto_destroy':
            return config_plusnetbusinesshosting_auto_destroy($componentId);
            break;
        default:
            break;
    }
}

/**
 * Plusnet Business Hosting Destroy Ticket
 *
 * @param Int $componentId Component ID
 *
 * @return boolean
 */
function plusnetbusinesshosting_destroy_ticket($componentId)
{
    if (!is_numeric($componentId) || 0 >= $componentId) {
        return false;
    }

    $component = userdata_component_get($componentId);

    if (isset($component)) {

        $connection = get_named_connection("userdata");
        $hosting_id = mysql_real_escape_string($hosting_id);

        $result = mysql_query("SELECT * FROM tblConfigHosting WHERE intComponentId = '$componentId'", $connection)
            or report_error(__FILE__, __LINE__, mysql_error($connection));

        if (mysql_num_rows($result) > 0) {
            $hosting = mysql_fetch_array($result, MYSQL_ASSOC);
        } else {
            $hosting['vchHostopiaId'] = 'NONE SET';
        }

        mysql_free_result($result);

        $ticketText = "This customer's account has been downgraded/cancelled and their 'Plusnet CGI (2013)' component destroyed. Please schedule the customer's hosting account for closure by logging into WebhostOS and setting the account status to 'disable'. Hostopia Id of the user is: " . $hosting['vchHostopiaId'] . ".\n\n";

        $teamId = phplibGetTeamIdByHandle('PLUSNET_BUSINESS_HOSTING');

        $result = tickets_ticket_add(
            'Internal',
            $component['service_id'],
            0,
            0,
            'OPEN',
            SCRIPT_USER,
            $ticketText,
            0,
            $teamId
        );

        $connection = get_named_connection("userdata");
        $result = mysql_query("DELETE FROM tblConfigHosting WHERE intComponentId = '$componentId'", $connection)
            or report_error(__FILE__, __LINE__, mysql_error($connection));

        return true;
    }

    return false;
}
