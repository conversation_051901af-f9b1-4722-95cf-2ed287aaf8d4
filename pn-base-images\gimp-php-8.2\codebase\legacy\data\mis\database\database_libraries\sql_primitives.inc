<?php
/**
 * sql_primitives.inc
 *
 * Access Library for Sql
 *
 * @package    LegacyCodebase
 * @subPackage Database Libraries
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2012 Plusnet
 */

if (!isset($global_array_existing_string_locks)) {
    $global_array_existing_string_locks = array();
}

if (!isset($global_transactions_in_progess)) {
    $array_transactions_in_progress = array();
}

/**
 * Primitives Get Lock
 *
 * @param unknown $lock_string     Lock String
 * @param unknown $connection      Connection
 * @param unknown $timeout         Timeout
 * @param unknown $exit_on_failure Exit On Failure
 *
 * @return unknown
 */
function primitives_get_lock($lock_string, $connection, $timeout = 5, $exit_on_failure = true)
{
    $objSqlPrimitives = Lib_SqlPrimitives::singleton();
    return $objSqlPrimitives->primitives_get_lock($lock_string, $connection, $timeout, $exit_on_failure);
}

/**
 * Primitives Release Lock
 *
 * @param unknown $lock_string     Lock String
 * @param unknown $connection      Connection
 * @param unknown $exit_on_failure Exit On Failure
 *
 * @return unknown
 */
function primitives_release_lock($lock_string, $connection, $exit_on_failure = true)
{

    $objSqlPrimitives = Lib_SqlPrimitives::singleton();
    return $objSqlPrimitives->primitives_release_lock($lock_string, $connection, $exit_on_failure);
}

/**
 * Primitives Named Db Prefix
 *
 * @param unknown $base_db         Base Db
 * @param unknown $second_database Second Database
 *
 * @return unknown
 */
function primitives_named_db_prefix($base_db, $second_database)
{

    $objSqlPrimitives = Lib_SqlPrimitives::singleton();
    return $objSqlPrimitives->primitives_named_db_prefix($base_db, $second_database);
}

/**
 * Primitives Desc Table
 *
 * @param unknown $table_name      Table Name
 * @param unknown $connection      Connection
 * @param string  $strDBName       DBName
 * @param unknown $exit_on_failure Exit On Failure
 *
 * @return unknown
 */
function primitives_desc_table($table_name, $connection, $strDBName = '', $exit_on_failure = true)
{

    $objSqlPrimitives = Lib_SqlPrimitives::singleton();
    return $objSqlPrimitives->primitives_desc_table($table_name, $connection, $strDBName, $exit_on_failure);
}

/**
 * Monitoring Database Thread Status Add
 *
 * @param unknown $database_name    Database Name
 * @param unknown $tested_timestamp Tested Timestamp
 * @param unknown $threads          Threads
 * @param unknown $sleeping_threads Sleeping Threads
 * @param unknown $active_threads   Active Threads
 * @param unknown $slow_threads     Slow Threads
 * @param unknown $locked_threads   Locked Threads
 *
 * @return unknown
 */
function monitoring_database_thread_status_add(
    $database_name, $tested_timestamp, $threads, $sleeping_threads, $active_threads, $slow_threads, $locked_threads
) {

    $objSqlPrimitives = Lib_SqlPrimitives::singleton();
    return $objSqlPrimitives->monitoring_database_thread_status_add(
        $database_name, $tested_timestamp, $threads, $sleeping_threads, $active_threads, $slow_threads, $locked_threads
    );
}

/**
 * Monitoring Slow Database Query Add
 *
 * @param unknown $database_name    Database Name
 * @param unknown $tested_timestamp Tested Timestamp
 * @param unknown $user             User
 * @param unknown $host             Host
 * @param unknown $slow_query       Slow Query
 * @param unknown $seconds          Seconds
 *
 * @return unknown
 */
function monitoring_slow_database_query_add($database_name, $tested_timestamp, $user, $host, $slow_query, $seconds)
{

    $objSqlPrimitives = Lib_SqlPrimitives::singleton();
    return $objSqlPrimitives->monitoring_slow_database_query_add(
        $database_name, $tested_timestamp, $user, $host, $slow_query, $seconds
    );
}

/**
 * Primitives Db Processlist Get
 *
 * @param unknown $db_name         Db Name
 * @param unknown $exit_on_failure Exit On Failure
 *
 * @return unknown
 */
function primitives_db_processlist_get($db_name = 'userdata', $exit_on_failure = false)
{

    $objSqlPrimitives = Lib_SqlPrimitives::singleton();
    return $objSqlPrimitives->primitives_db_processlist_get($db_name, $exit_on_failure);
}

/**
 * Primitives Array Addslashes
 *
 * @param unknown $unslashed_array Unslashed Array
 *
 * @return unknown
 */
function primitives_array_addslashes($unslashed_array)
{

    $objSqlPrimitives = Lib_SqlPrimitives::singleton();
    return $objSqlPrimitives->primitives_array_addslashes($unslashed_array);
}

/**
 * Primitives Private Array Section Addslashes
 *
 * @param unknown &$unslashed_array Unslashed Array
 * @param unknown &$syntax_error    Syntax Error
 *
 * @return unknown
 */
function primitives_private_array_section_addslashes(&$unslashed_array, &$syntax_error)
{
    $func = 'primitives_private_array_section_addslashes';

    if (!is_array($unslashed_array)) {
        $unslashed_array = addslashes($unslashed_array);
        return(true);
    }

    foreach ($unslashed_array AS $index => $data) {
        // $index can't be slashed, check it only contains chars which don't need slashing instead
        // 2002-5-08
        if (!preg_match('/^[-0-9a-zA-Z_ ]+$/', $index)) {
            // May need slashing
            error_log("$func (".__FILE__.'@'.__LINE__.") Array index '$index' is in an  invaild format '", 0);
            $syntax_error = true;
            return false ;
        }

        primitives_private_array_section_addslashes($unslashed_array[$index], $syntax_error);

        if ($syntax_error) {
            return false ;
        }
    }

    return true ;
}

/**
 * Primitives Safe List
 *
 * @param array  $arrLiterals       Literals
 * @param string $strSubIndex       Sub Index
 * @param string $strLeadQuoteChar  Lead Quote Char
 * @param string $strTrailQuoteChar Trail Quote Char
 * @param string $strSeperatorChar  Seperator Char
 *
 * @return unknown
 */
function PrimitivesSafeList(
    $arrLiterals, $strSubIndex = '', $strLeadQuoteChar  = "'", $strTrailQuoteChar = "'", $strSeperatorChar  = ","
) {
    $objSqlPrimitives = Lib_SqlPrimitives::singleton();
    return $objSqlPrimitives->PrimitivesSafeList(
        $arrLiterals, $strSubIndex, $strLeadQuoteChar, $strTrailQuoteChar, $strSeperatorChar
    );
}

/**
 * Primitives Mysql Safe List
 *
 * @param unknown $literals_array   Literals Array
 * @param unknown $sub_index        Sub Index
 * @param unknown $lead_quote_char  Lead Quote Char
 * @param unknown $trail_quote_char Trail Quote Char
 * @param unknown $seperator_char   Seperator Char
 *
 * @return unknown
 */
function primitives_mysql_safe_list(
    $literals_array, $sub_index = '', $lead_quote_char="'", $trail_quote_char = "'", $seperator_char = ","
) {

    $objSqlPrimitives = Lib_SqlPrimitives::singleton();
    return $objSqlPrimitives->primitives_mysql_safe_list(
        $literals_array, $sub_index, $lead_quote_char, $trail_quote_char, $seperator_char
    );
}

/**
 * Make Quoted Mysql Safe List
 *
 * @param unknown $literals_array   Literals Array
 * @param unknown $sub_index        Sub Index
 * @param unknown $lead_quote_char  Lead Quote Char
 * @param unknown $trail_quote_char Trail Quote Char
 * @param unknown $seperator_char   Seperator Char
 *
 * @return unknown
 */
function make_quoted_mysql_safe_list(
    $literals_array, $sub_index = '', $lead_quote_char="'", $trail_quote_char = "'", $seperator_char = ","
) {

    $objSqlPrimitives = Lib_SqlPrimitives::singleton();
    return $objSqlPrimitives->make_quoted_mysql_safe_list(
        $literals_array, $sub_index, $lead_quote_char, $trail_quote_char, $seperator_char
    );
}

/**
 * Primitives NOT IN Clause
 *
 * @param unknown $prefix        Prefix
 * @param unknown $field_name    Field Name
 * @param array   $array_in_list Ay In List
 * @param unknown $sub_index     Sub Index
 *
 * @return unknown
 */
function primitives_NOT_IN_clause($prefix, $field_name, $array_in_list, $sub_index ='')
{

    $objSqlPrimitives = Lib_SqlPrimitives::singleton();
    return $objSqlPrimitives->primitives_NOT_IN_clause($prefix, $field_name, $array_in_list, $sub_index);
}

/**
 * Primitives IN Clause
 *
 * @param unknown $prefix        Prefix
 * @param unknown $field_name    Field Name
 * @param array   $array_in_list Ay In List
 * @param unknown $sub_index     Sub Index
 *
 * @return unknown
 */
function primitives_IN_clause($prefix, $field_name, $array_in_list, $sub_index ='')
{

    $objSqlPrimitives = Lib_SqlPrimitives::singleton();
    return $objSqlPrimitives->primitives_IN_clause($prefix, $field_name, $array_in_list, $sub_index);
}

/**
 * Primitives Day Start Timestamp
 *
 * @param unknown $timestamp Timestamp
 *
 * @return unknown
 */
function primitives_day_start_timestamp($timestamp)
{

    $objSqlPrimitives = Lib_SqlPrimitives::singleton();
    return $objSqlPrimitives->primitives_day_start_timestamp($timestamp);
}

/**
 * Primitives Date From Timestamp Clause
 *
 * @param unknown $prefix              Prefix
 * @param unknown $field_name          Field Name
 * @param unknown $comparison_operator Comparison Operator
 * @param unknown $timestamp           Timestamp
 *
 * @return unknown
 */
function primitives_date_from_timestamp_clause($prefix, $field_name, $comparison_operator, $timestamp)
{

    $objSqlPrimitives = Lib_SqlPrimitives::singleton();
    return $objSqlPrimitives->primitives_date_from_timestamp_clause(
        $prefix, $field_name, $comparison_operator, $timestamp
    );
}

/**
 * Primitives Order By Clause
 *
 * @param unknown $orderby_array Orderby Array
 *
 * @return unknown
 */
function primitives_order_by_clause($orderby_array = array())
{

    $objSqlPrimitives = Lib_SqlPrimitives::singleton();
    return $objSqlPrimitives->primitives_order_by_clause($orderby_array);
}

/**
 * Primitives Limit Clause
 *
 * @param unknown $start_limit Start Limit
 * @param unknown $end_limit   End Limit
 *
 * @return unknown
 */
function primitives_limit_clause($start_limit = -1, $end_limit = 0)
{

    $objSqlPrimitives = Lib_SqlPrimitives::singleton();
    return $objSqlPrimitives->primitives_limit_clause($start_limit, $end_limit);
}

/**
 * DJM2013: function PrimitivesQueryOrExit() moved to data/mis/common_library_functions/common_standard_include.inc
 */

/**
 * DJM2013: function primitives_query_or_exit() moved to data/mis/common_library_functions/common_standard_include.inc
 */

/**
 * DJM2013: function query_or_exit() moved to data/mis/common_library_functions/common_standard_include.inc
 */

/**
 * Primitives Results As Array Get
 *
 * @param unknown $resResult  Res Result
 * @param string  $strIndexOn Index On
 *
 * @return unknown
 */
function PrimitivesResultsAsArrayGet($resResult, $strIndexOn = '')
{

    $objSqlPrimitives = Lib_SqlPrimitives::singleton();
    return $objSqlPrimitives->PrimitivesResultsAsArrayGet($resResult, $strIndexOn);
}

/**
 * Primitives Results As List Get
 *
 * @param unknown $resResult Res Result
 *
 * @return unknown
 */
function PrimitivesResultsAsListGet($resResult)
{

    $objSqlPrimitives = Lib_SqlPrimitives::singleton();
    return $objSqlPrimitives->PrimitivesResultsAsListGet($resResult);
}

/**
 * Primitives Mysql Results As Array Get
 *
 * @param unknown $result_handle Result Handle
 * @param unknown $index_on      Index On
 *
 * @return unknown
 */
function primitives_mysql_results_as_array_get($result_handle, $index_on='')
{

    $objSqlPrimitives = Lib_SqlPrimitives::singleton();
    return $objSqlPrimitives->primitives_mysql_results_as_array_get($result_handle, $index_on);
}

/**
 * Get Mysql Results As Array
 *
 * @param unknown $result_handle Result Handle
 * @param unknown $index_on      Index On
 *
 * @return unknown
 */
function get_mysql_results_as_array($result_handle, $index_on='')
{

    $objSqlPrimitives = Lib_SqlPrimitives::singleton();
    return $objSqlPrimitives->get_mysql_results_as_array($result_handle, $index_on);
}

/**
 * Primitives Result Get
 *
 * @param unknown $result_handle Result Handle
 * @param unknown $single_field  Single Field
 *
 * @return unknown
 */
function PrimitivesResultGet($result_handle, $single_field='')
{

    $objSqlPrimitives = Lib_SqlPrimitives::singleton();
    return $objSqlPrimitives->PrimitivesResultGet($result_handle, $single_field);
}

/**
 * Primitives Mysql Result Get
 *
 * @param unknown $result_handle Result Handle
 * @param unknown $single_field  Single Field
 *
 * @return unknown
 */
function primitives_mysql_result_get($result_handle, $single_field='')
{

    $objSqlPrimitives = Lib_SqlPrimitives::singleton();
    return $objSqlPrimitives->primitives_mysql_result_get($result_handle, $single_field);
}

/**
 * Primitives Explain Query
 *
 * @param unknown $SymbolicDatabaseName   Symbolic Database Name
 * @param unknown $SelectQuery            Select Query
 * @param unknown &$ErrorMessage          Error Message
 * @param unknown $MaximiumNumberOfRows   Maximium Number Of Rows
 * @param unknown $MaximumUnindexedSearch Maximum Unindexed Search
 * @param unknown $ErrorMessage           Error Message
 *
 * @return unknown
 */
function primitives_explain_query(
    $SymbolicDatabaseName, $SelectQuery, &$ErrorMessage, $MaximiumNumberOfRows =20000, $MaximumUnindexedSearch = -1
)
{
    // Clear error message
    $ErrorMessage='';

    // Check Include libraries
    if (!function_exists('primitives_query_or_exit')) {
        error_log(__FILE__.'@'.__LINE__.' Please include the SQL_PRIMITIVES_LIBRARY');
        $ErrorMessage ="Please include the SQL_PRIMITIVES_LIBRARY";
        return false ;
    }

    // Check it's a select
    $SelectQuery = trim($SelectQuery);
    if (!preg_match("/^select /i", $SelectQuery)) {
        $ErrorMessage ="Only select queries can be checked";
        return false ;
    }

    $Connection = get_named_connection_with_db($SymbolicDatabaseName);
    $Query ="EXPLAIN ".$SelectQuery;

    $Result = primitives_query_or_exit($Query, $Connection, 'Perform Explain', false);

    if (!$Result) {
        $ErrorMessage ="Failed to explain query '$Query'\n The error was '" . mysql_error($Connection['handle']) . "'";
        return(false);
    }

    $ArrayTablesExamined = primitives_mysql_results_as_array_get($Result);

    $TotalRowsInspected  = 1; // used in multiplication, should not be zero

    foreach ($ArrayTablesExamined AS $ArrayTableExamined) {
        // Allow for 'optimised away' queries
        if ((!isset($ArrayTableExamined['rows'])) || ($ArrayTableExamined['rows']==0)) {
            continue;
        }

        $TotalRowsInspected = $TotalRowsInspected * $ArrayTableExamined['rows'];

        if (($MaximumUnindexedSearch > 0)
            && ($ArrayTableExamined['key']=='')
            && ($ArrayTableExamined['rows'] > $MaximumUnindexedSearch)
        ) {
            $ErrorMessage ="The searching of {$ArrayTableExamined['table']} would be examine ".
                                   "{$ArrayTableExamined['rows']} rows without using an index.";
            return(false);
        }
    }

    if ($TotalRowsInspected > $MaximiumNumberOfRows) {
        $ErrorMessage ="The query would search $TotalRowsInspected rows across (".count($ArrayTablesExamined).") table(s).";
        return(false);
    }

    // Query should be OK
    return(true);
}

/**
 * Primitives Insert Id Get
 *
 * @param unknown $dbConnection Db Connection
 *
 * @return unknown
 */
function PrimitivesInsertIdGet($dbConnection)
{

    $objSqlPrimitives = Lib_SqlPrimitives::singleton();
    return $objSqlPrimitives->PrimitivesInsertIdGet($dbConnection);
}

/**
 * Primitives Num Rows Get
 *
 * @param unknown $resResult Res Result
 *
 * @return unknown
 */
function PrimitivesNumRowsGet($resResult)
{

    $objSqlPrimitives = Lib_SqlPrimitives::singleton();
    return $objSqlPrimitives->PrimitivesNumRowsGet($resResult);
}

/**
 * Primitives Affected Rows Get
 *
 * @param unknown $dbConnection Db Connection
 *
 * @return unknown
 */
function PrimitivesAffectedRowsGet($dbConnection)
{

    $objSqlPrimitives = Lib_SqlPrimitives::singleton();
    return $objSqlPrimitives->PrimitivesAffectedRowsGet($dbConnection);
}

/**
 * Primitives Table Exists
 *
 * @param unknown $dbhConnection Dbh Connection
 * @param string  $strTableName  Table Name
 *
 * @return unknown
 */
function PrimitivesTableExists($dbhConnection, $strTableName)
{

    $objSqlPrimitives = Lib_SqlPrimitives::singleton();
    return $objSqlPrimitives->PrimitivesTableExists($dbhConnection, $strTableName);
}

/**
 * Primitives Real Escape String
 *
 * @param unknown $unkVar        Unk Var
 * @param array   $dbhConnection Dbh Connection
 *
 * @return unknown
 */
function PrimitivesRealEscapeString($unkVar, $dbhConnection)
{

    $objSqlPrimitives = Lib_SqlPrimitives::singleton();
    return $objSqlPrimitives->PrimitivesRealEscapeString($unkVar, $dbhConnection);
}

/**
 * Primitives Fetch Row
 *
 * @param unknown $res Res
 *
 * @return unknown
 */
function PrimitivesFetchRow($res)
{
    return Lib_SqlPrimitives::singleton()->PrimitivesFetchRow($res);
}

/**
 * Wrapper function that performs a mysql_error call.
 *
 * @param resource $dbRes MySQL connection resource.
 *
 * @return string
 **/
function PrimitivesError($dbRes)
{
    return Lib_SqlPrimitives::singleton()->PrimitivesError($dbRes);
}

/**
 * Wrapper function around MySQL library function mysql_errno
 *
 * @param array $dbhRes Legacy codebase Plusnet database connection
 *                      as used by PrimitivesQueryOrExit, et al.
 *
 * @return integer
 **/
function PrimitivesErrno($dbhRes)
{

    $objSqlPrimitives = Lib_SqlPrimitives::singleton();
    return $objSqlPrimitives->PrimitivesErrno($dbhRes);
}

/**
 * Primitives Free Result
 *
 * @param unknown $resResult Res Result
 *
 * @return unknown
 */
function PrimitivesFreeResult($resResult)
{
    return Lib_SqlPrimitives::singleton()->PrimitivesFreeResult($resResult);
}

require_once '/local/data/mis/database/database_libraries/Util_LibrarySplitter.class.php';
require_once '/local/data/mis/database/database_libraries/sqlprimitives/SqlPrimitives.class.php';
