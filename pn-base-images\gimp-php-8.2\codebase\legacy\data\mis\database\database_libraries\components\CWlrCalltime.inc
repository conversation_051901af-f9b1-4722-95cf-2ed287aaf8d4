<?php
/**
 * Access library for WLR Calltime component
 *
 * @package    Core
 * @subpackage WLR
 * @access     public
 * <AUTHOR> <<EMAIL>>
 * @version    $Id: CWlrCalltime.inc,v 1.8.2.1 2009/07/15 10:01:34 ssmith Exp $
 * @filesource
 * @link       link
 */

require_once '/local/data/mis/database/database_libraries/components/CWlrProductComponent.inc';
require_once '/local/data/mis/database/application_apis/EmailHandler/EmailHandler.class.php';
require_once '/local/data/mis/database/database_libraries/programme-tool-access.inc';

/**
 * WLR Call Time Component
 *
 * Component for handling the Calltime charges
 *
 * @package Core
 * @access  public
 * <AUTHOR> <<EMAIL>>
 * @link    link
 */
class CWlrCalltime extends CWlrProductComponent
{
    const UNLIMITED_INCLUSIVE_MINUTES = 44941;   // the magic number used to represent "unlimited" inclusive minutes.

    /**
     * On enable
     *
     * @param array $arrArgs Arguments
     *
     * @access    private (ahem, more like "protected")
     *
     * @return bool
     */
    public function onEnable($arrArgs = array())
    {
        $uxtEnableDate = isset($arrArgs['uxtEnableDate']) ? $arrArgs['uxtEnableDate'] : time();
        $this->addProRataMinutes($uxtEnableDate);

        return true;
    }

    /**
     * Returns the number of minutes that comes with this product
     *
     * @access public
     *
     * @return integer    Minutes on product
     */
    public function getConfig()
    {
        $dbhConn = get_named_connection_with_db('dbProductComponents');

        $strQuery = "SELECT intMinutes
                       FROM tblWlrCallTimeConfig ctc
                 INNER JOIN tblProductComponentConfig pcc
                         ON pcc.intProductComponentConfigID = ctc.intProductComponentConfigId
                 INNER JOIN tblTariff t
                         ON t.intProductComponentConfigID = ctc.intProductComponentConfigId
                 INNER JOIN userdata.tblProductComponentInstance pci
                         ON pci.intTariffID = t.intTariffID
                      WHERE pci.intProductComponentInstanceID = " . $this->getProductComponentInstanceID();

        $resConfig = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Fetch the inclusive minutes');
        $arrConfig = PrimitivesResultGet($resConfig);

        if (isset($arrConfig) && count($arrConfig) > 0) {

            return $arrConfig;
        }

        return false;
    } // function getConfig()

    /**
     * Creates an array of CWlrRedeemableSeconds for the given call.
     *
     * @param int    $intCallSectionPriceId Maps to dbProductComponents.tblCallSectionPrice.intCallSectionPriceId
     * @param int    $intStartTime          Start time (uxt) of call section.
     * @param int    $intDuration           Duration of call section (in seconds)
     * @param string $strRedemptionTable    The redemption table name (allows this method to service both Cdr and
     *                                      Fraud redemption)
     *
     * @access public
     * <AUTHOR>
     *
     * @return Array, or false for karkage.
     */
    public function getSecondsRedeemable($intCallSectionPriceId, $intStartTime, $intDuration, $strRedemptionTable)
    {
        static $arrObjectCache = array();

        // tblIncludedMinutes starts included minutes period from 00:00:01 not 00:00:00
        if (date("H:i:s", $intStartTime) == '00:00:00') {
            $strTmStart = date("Y-m-d H:i:s", $intStartTime + 1);
        } else {
            $strTmStart = date('Y-m-d H:i:s', $intStartTime);
        }

        $strTmEnd = date('Y-m-d H:i:s', ($intStartTime + $intDuration));

        $dbhConn = get_named_connection_with_db('wlr_calls');
        $arrRet = array();

        //First, Look up the split for this $intCallTimeIncludedMinutesSplitId
        $strSql = 'SELECT ctims.intCallTimeIncludedMinutesSplitId, ctims.intUpToMinutes ' .
            'FROM dbProductComponents.tblProductComponentConfig pcc ' .
            'INNER JOIN dbProductComponents.tblWlrCallTimeConfig ctc ' .
            'ON pcc.intProductComponentConfigId = ctc.intProductComponentConfigId ' .
            'INNER JOIN dbProductComponents.tblCallTimeIncludedMinutesSplit ctims ' .
            'ON ctc.intWlrCallTimeConfigId = ctims.intWlrCallTimeConfigId ' .
            'INNER JOIN dbProductComponents.tblIncludedMinutesSplitMember imsm ' .
            'ON ctims.intCallTimeIncludedMinutesSplitId = imsm.intCallTimeIncludedMinutesSplitId ' .
            'INNER JOIN dbProductComponents.tblCallSectionPrice csp ' .
            'ON imsm.intCallSectionPriceId = csp.intCallSectionPriceId ' .
            "WHERE pcc.intProductComponentId = '{$this->m_intProductComponentID}' AND " .
            "csp.intCallSectionPriceId = '$intCallSectionPriceId'";

        if (!$resResult = PrimitivesQueryOrExit($strSql, $dbhConn, 'CWlrCalltime::getSecondsRedeemable()[1]', false)) {

            return false;
        }

        if (PrimitivesNumRowsGet($resResult) != 1) {
            return false;
        }

        $arrResults = PrimitivesResultsAsArrayGet($resResult);

        $intSplitId = $arrResults[0]['intCallTimeIncludedMinutesSplitId'];
        $intLocationMaxSecs = 60 * $arrResults[0]['intUpToMinutes'];

        //Gets a bag of included minutes for this user which hasn't already been spent.
        $strSql = 'SELECT im.intIncludedMinutesId, im.dtmStart, im.dtmEnd, im.intIncludedMinutes ' .
            'FROM dbWlrCalls.tblIncludedMinutes im ' .
            "LEFT JOIN dbWlrCalls.$strRedemptionTable isr " .
            'ON isr.intIncludedMinutesId = im.intIncludedMinutesId ' .
            "WHERE im.intProductComponentInstanceId = '{$this->m_intProductComponentInstanceID}' AND " .
            "im.dtmStart <= '$strTmStart' AND (im.dtmEnd >= '$strTmEnd' OR im.dtmEnd IS NULL) " .
            'GROUP BY im.intIncludedMinutesId, im.dtmStart, im.dtmEnd, im.intIncludedMinutes ' .
            'HAVING ' .
            'IF(SUM(isr.intSecondsRedeemed), SUM(isr.intSecondsRedeemed), 0) < (60 * im.intIncludedMinutes) ';
        if ($strRedemptionTable == 'tblFraudIncludedSecondsRedemption') {
            //Inserts union.
            $strSql .= ' UNION SELECT im.intIncludedMinutesId, im.dtmStart, im.dtmEnd, im.intIncludedMinutes ' .
                'FROM dbWlrCalls.tblIncludedMinutes im ' .
                "LEFT JOIN dbWlrCalls.tblIncludedSecondsRedemption isr " .
                'ON isr.intIncludedMinutesId = im.intIncludedMinutesId ' .
                "WHERE im.intProductComponentInstanceId = '{$this->m_intProductComponentInstanceID}' AND " .
                "im.dtmStart <= '$strTmStart' AND (im.dtmEnd >= '$strTmEnd' OR im.dtmEnd IS NULL) " .
                'GROUP BY im.intIncludedMinutesId, im.dtmStart, im.dtmEnd, im.intIncludedMinutes ' .
                'HAVING ' .
                'IF(SUM(isr.intSecondsRedeemed), SUM(isr.intSecondsRedeemed), 0) < (60 * im.intIncludedMinutes) ' .
                'ORDER BY dtmStart ';
        } else {

            $strSql .= 'ORDER BY im.dtmStart ';
        }

        if (!$resResult = PrimitivesQueryOrExit($strSql, $dbhConn, 'CWlrCalltime::getSecondsRedeemable()[2]', false)) {

            return false;
        }

        $arrResults = PrimitivesResultsAsArrayGet($resResult);

        $strLinkField = ($strRedemptionTable == 'tblFraudIncludedSecondsRedemption') ?
            'intFraudCallSectionId' : 'intCallBillingSectionId';
        $strCallSectionTable = ($strRedemptionTable == 'tblFraudIncludedSecondsRedemption') ?
            'tblFraudCallSection' : 'tblCallBillingSection';

        foreach ($arrResults as $arrRow) {
            //Counts the # of seconds already redeemed for the given included minutes bag on the given split_id
            $strSql = 'SELECT IF(SUM(isr.intSecondsRedeemed), SUM(isr.intSecondsRedeemed), 0) AS intRedeemedAlready ' .
                'FROM dbWlrCalls.tblIncludedMinutes im ' .
                "INNER JOIN dbWlrCalls.$strRedemptionTable isr " .
                'ON im.intIncludedMinutesId = isr.intIncludedMinutesId ' .
                "INNER JOIN dbWlrCalls.$strCallSectionTable cbs " .
                "ON isr.$strLinkField = cbs.$strLinkField ";
            if ($strRedemptionTable == 'tblFraudIncludedSecondsRedemption') {
                //Need to do an extra link on to tblFraudCall to EXCLUDE obscoleted fraud calls (these will have
                //redeemed sections in tblIncludedSecondsRedemption
                $strSql .= 'INNER JOIN tblFraudCall fc ' .
                    'ON cbs.intFraudCallId = fc.intFraudCallId AND ' .
                    "fc.bolObsolete != '1' ";
            }

            $strSql .= 'INNER JOIN dbProductComponents.tblCallSectionPrice csp ' .
                'ON cbs.intCallSectionPriceId = csp.intCallSectionPriceId ' .
                'INNER JOIN dbProductComponents.tblIncludedMinutesSplitMember imsm ' .
                'ON csp.intCallSectionPriceId = imsm.intCallSectionPriceId ' .
                'INNER JOIN dbProductComponents.tblCallTimeIncludedMinutesSplit ctims ' .
                'ON imsm.intCallTimeIncludedMinutesSplitId = ctims.intCallTimeIncludedMinutesSplitId ' .
                "WHERE im.intIncludedMinutesId = '{$arrRow['intIncludedMinutesId']}' AND " .
                "ctims.intCalltimeIncludedMinutesSplitId = '$intSplitId' ";
            'GROUP BY isr.intIncludedMinutesId ';
            if ($strRedemptionTable == 'tblFraudIncludedSecondsRedemption') {
                $strSql .= ' UNION ' .
                    'SELECT IF(SUM(isr.intSecondsRedeemed), SUM(isr.intSecondsRedeemed), 0) AS intRedeemedAlready ' .
                    'FROM dbWlrCalls.tblIncludedMinutes im ' .
                    'INNER JOIN dbWlrCalls.tblIncludedSecondsRedemption isr ' .
                    'ON im.intIncludedMinutesId = isr.intIncludedMinutesId ' .
                    'INNER JOIN dbWlrCalls.tblCallBillingSection cbs ' .
                    'ON isr.intCallBillingSectionId = cbs.intCallBillingSectionId ' .
                    'INNER JOIN dbProductComponents.tblCallSectionPrice csp ' .
                    'ON cbs.intCallSectionPriceId = csp.intCallSectionPriceId ' .
                    'INNER JOIN dbProductComponents.tblIncludedMinutesSplitMember imsm ' .
                    'ON csp.intCallSectionPriceId = imsm.intCallSectionPriceId ' .
                    'INNER JOIN dbProductComponents.tblCallTimeIncludedMinutesSplit ctims ' .
                    'ON imsm.intCallTimeIncludedMinutesSplitId = ctims.intCallTimeIncludedMinutesSplitId ' .
                    "WHERE im.intIncludedMinutesId = '{$arrRow['intIncludedMinutesId']}' AND " .
                    "ctims.intCalltimeIncludedMinutesSplitId = '$intSplitId' ";
                'GROUP BY isr.intIncludedMinutesId';

            }

            if (!$resResult = PrimitivesQueryOrExit(
                $strSql,
                $dbhConn,
                'CWlrCalltime::getSecondsRedeemable()[3]',
                false
            )
            ) {
                return false;
            }
            $intRedeemedAlreadyOnSplit = PrimitivesResultGet($resResult, 'intRedeemedAlready');
            $intSecsAvailISR = 60 * $arrRow['intIncludedMinutes'];

            //NIGEL - NEW CODE!!!!!!!!!!!
            //Count the total second used (regardless of split)
            $strSql = 'SELECT IF(SUM(isr.intSecondsRedeemed), SUM(isr.intSecondsRedeemed), 0) AS intRedeemedAlready ' .
                'FROM dbWlrCalls.tblIncludedMinutes im ' .
                'INNER JOIN dbWlrCalls.tblIncludedSecondsRedemption isr ' .
                'ON im.intIncludedMinutesId = isr.intIncludedMinutesId ' .
                "WHERE isr.intIncludedMinutesId = '{$arrRow['intIncludedMinutesId']}' " .
                'GROUP BY isr.intIncludedMinutesId';

            if (!$resResult = PrimitivesQueryOrExit(
                $strSql,
                $dbhConn,
                'CWlrCalltime::getSecondsRedeemable()[4]',
                false
            )
            ) {
                return false;
            }

            $arrTmp = PrimitivesResultsAsArrayGet($resResult);
            if (count($arrTmp) == 0) {

                $intRedeemedInTotal = 0;

            } else {

                $intRedeemedInTotal = ($arrTmp[0]['intRedeemedAlready'] == '') ? 0 : $arrTmp[0]['intRedeemedAlready'];
            }

            //Now calculate the remaining available seconds, taking into account the total used and the total
            //available for the split.
            $intSecsAvail
                = min($intLocationMaxSecs - $intRedeemedAlreadyOnSplit, $intSecsAvailISR - $intRedeemedInTotal);
            //NEW CODE ENDS
            /*
            printf("  \$intLocationMaxSecs
                = %s, \$intRedeemedAlreadyOnSplit = %s, \$intSecsAvailISR = %s, \$intRedeemedInTotal = %s\n",
                $intLocationMaxSecs,
                $intRedeemedAlreadyOnSplit,
                $intSecsAvailISR,
                $intRedeemedInTotal);
            */
            if (($intSecsAvail) > 0) {

                if (isset($arrObjectCache[$arrRow['intIncludedMinutesId']][$intSplitId])) {

                    $arrRet[] = $arrObjectCache[$arrRow['intIncludedMinutesId']][$intSplitId];

                } else {

                    $objRedSecs = new Wlr_RedeemableSeconds(
                        strtotime($arrRow['dtmStart']),
                        strtotime($arrRow['dtmEnd']),
                        ($intSecsAvail),
                        $this->m_intProductComponentInstanceID,
                        $arrRow['intIncludedMinutesId']
                    );
                    $arrRet[] = $objRedSecs;
                    $arrObjectCache[$arrRow['intIncludedMinutesId']][$intSplitId] = $objRedSecs;
                }
            }
        } //foreach

        return $arrRet;

    }

    /**
     * Method to insert free minute redemptions into the database.
     *
     * @param int    $intCallSectionBillingId Maps to dbProductComponents.tblCallSectionPrice.intCallSectionPriceId
     * @param int    $intSecondsRedeemed      The number of seconds to redeem
     * @param obj    $objRedeemableSeconds    Objects as returned by $this.getSecondsRedeemable()
     * @param string $strTable                The table in which to insert the data.
     *                                        Allows this method to service both Cdr and Fraud data
     *
     * <AUTHOR>
     * @access public
     *
     * @return void
     */
    public static function redeemIncludedSeconds(
        $intCallSectionBillingId,
        $intSecondsRedeemed,
        $objRedeemableSeconds,
        $strTable
    ) {
        self::includeLegacyFiles();

        if (strtolower(get_class($objRedeemableSeconds)) != 'wlr_redeemableseconds') {

            return false;
        }
        //Check the provided table names.
        if (!($strTable == 'tblFraudIncludedSecondsRedemption' || $strTable == 'tblIncludedSecondsRedemption')) {

            return false;
        }
        //Figure out the section table id to use.
        $strSectionTabId = ($strTable == 'tblFraudIncludedSecondsRedemption')
            ? 'intFraudCallSectionId' : 'intCallBillingSectionId';
        $dbhConn = get_named_connection_with_db('wlr_calls');
        $strSql = "INSERT INTO $strTable " .
            "(intIncludedMinutesId, $strSectionTabId, intSecondsRedeemed) " .
            "VALUES ('{$objRedeemableSeconds->intIncludedMinutesId}', " .
            "'$intCallSectionBillingId', " .
            "'$intSecondsRedeemed')";
        if (!PrimitivesQueryOrExit($strSql, $dbhConn, 'CWlrCallTime->redeemIncludedSeconds()', false)) {

            return false;
        }

        return true;
    }

    /**
     * Get the included minutes for the component instance, without redemptions
     *
     * @param uxt $uxtStart Start date
     * @param uxt $uxtEnd   End date
     *
     * <AUTHOR> Jones" <<EMAIL>>
     * @access    public
     *
     * @return float Remaining minutes, or false if we failed
     */
    public function getIncludedMinutesForPeriod($uxtStart = null, $uxtEnd = null)
    {
        $dbhRead = get_named_connection_with_db('wlr_calls_reporting');

        $dtmStart = (is_null($uxtStart)) ? 'IS NOT NULL' : ">= FROM_UNIXTIME('$uxtStart')";
        $dtmEnd = (is_null($uxtEnd)) ? 'IS NULL' : "<= FROM_UNIXTIME('$uxtEnd')";
        // The query
        $strGetSeconds = "SELECT im.intIncludedMinutes \n" .
            "  FROM tblIncludedMinutes im \n" .
            "  WHERE intProductComponentInstanceID = " . $this->getProductComponentInstanceID() . "\n" .
            "    AND dtmStart $dtmStart \n" .
            "    AND dtmEnd $dtmEnd \n";

        if (false === ($resGetSeconds = PrimitivesQueryOrExit(
                $strGetSeconds,
                $dbhRead,
                'CWlrCalltime::getIncludedMinutesForPeriod{GetSeconds}',
                true
            ))
        ) {

            // The above function will fatal on error. However, if we change this we may want to handle it
            return false;
        }

        $intRows = PrimitivesNumRowsGet($resGetSeconds);

        // If no rows returned just return 0
        if ($intRows == 0) {

            return 0;
        }

        // Uh-oh... too many open minutes!
        if ($intRows > 1) {

            $strError = "Product Component Instance '$this->m_intProductComponentInstanceID' has multiple " .
                "Included Minutes entries open.";
            $this->setError(__FILE__, __LINE__, $strError);

            return false;
        }

        // Otherwise, return the seconds returned
        $intRemainingMinutes = PrimitivesResultGet($resGetSeconds, 'intIncludedMinutes');

        return $intRemainingMinutes;
    } // function getIncludedMinutesForPeriod()

    /**
     * Get the included minutes for the component instance
     *
     * @param uxt $uxtStart Start date
     * @param uxt $uxtEnd   End date
     *
     * <AUTHOR> Jones" <<EMAIL>>
     * @access    public
     *
     * @return int Remaining minutes, or false if we failed
     */
    public function getCurrentIncludedMinutes($uxtStart = null, $uxtEnd = null)
    {
        $dbhRead = get_named_connection_with_db('wlr_calls_reporting');

        $dtmStart = (is_null($uxtStart)) ? 'IS NOT NULL' : ">= FROM_UNIXTIME('$uxtStart')";
        $dtmEnd = (is_null($uxtEnd)) ? 'IS NULL' : "<= FROM_UNIXTIME('$uxtEnd')";
        // The query
        $strGetSeconds = "SELECT CASE WHEN SUM(isr.intSecondsRedeemed) IS NOT NULL \n" .
            "            THEN ((im.intIncludedMinutes * 60) - SUM(isr.intSecondsRedeemed)) \n" .
            "            ELSE (im.intIncludedMinutes * 60) \n" .
            "         END AS intRemainingSeconds\n" .
            "  FROM tblIncludedMinutes im \n" .
            "    LEFT JOIN tblIncludedSecondsRedemption isr \n" .
            "      ON isr.intIncludedMinutesId = im.intIncludedMinutesId \n" .
            "  WHERE intProductComponentInstanceID = " . $this->getProductComponentInstanceID() . "\n" .
            "    AND dtmStart $dtmStart \n" .
            "    AND dtmEnd $dtmEnd \n" .
            "  GROUP BY im.intIncludedMinutesId, im.intIncludedMinutes \n";
        // The GROUP BY here might not be necessary, and may in fact cause undesirable results
        // I've also had strange results with SUM if there is nothing in the database to get... it actually
        // returns a null row. In order to avoid confusion below, if this happens, add:
        // HAVING(SUM(isr.intSecondsRedeemed)) IS NOT NULL

        if (false === ($resGetSeconds = PrimitivesQueryOrExit(
                $strGetSeconds,
                $dbhRead,
                'CWlrCalltime::getCurrentIncludedMinutes{GetSeconds}',
                true
            ))
        ) {
            // The above function will fatal on error. However, if we change this we may want to handle it
            return false;
        }

        $intRows = PrimitivesNumRowsGet($resGetSeconds);

        // If no rows returned just return 0
        if ($intRows == 0) {

            return 0;
        }

        // Uh-oh... too many open minutes!
        if ($intRows > 1) {

            $strError = "Product Component Instance '$this->m_intProductComponentInstanceID' has multiple " .
                "Included Minutes entries open.";
            $this->setError(__FILE__, __LINE__, $strError);

            return false;
        }

        // Otherwise, return the seconds returned
        $intRemainingSeconds = PrimitivesResultGet($resGetSeconds, 'intRemainingSeconds');

        // This will return a decimal, e.g 165.82
        // The .82 is a partial minute, (60 * 0.82 = approx. 49 seconds)
        $floRemainingMinutes = round($intRemainingSeconds / 60, 2);

        return $floRemainingMinutes;
    } // function getCurrentIncludedMinutes()

    /**
     * Function to add the prorata minutes based on the next invoice date
     *
     * @param uxt $uxtCalculationDate Calculation date
     *
     * @access public
     * <AUTHOR>
     *
     * @return boolean TRUE/FALSE
     */
    public function addProRataMinutes($uxtCalculationDate = false)
    {
        if (!$uxtCalculationDate) {

            $uxtCalculationDate = time();
        }

        $uxtNextInvoice = $this->getNextInvoiceDate();
        $intTariffID = $this->getTariffID();

        $arrConfig = $this->getConfig();
        $intProRataMinutes = CProductComponent::calculateProRataItems(
            $uxtNextInvoice,
            $intTariffID,
            $arrConfig['intMinutes'],
            $uxtCalculationDate
        );

        $intProRataMinutes = (isset($intProRataMinutes) && $intProRataMinutes > 0) ? $intProRataMinutes : 0;

        if ($this->addIncludedMinutes($intProRataMinutes, $uxtCalculationDate)) {

            return true;
        }

        return false;
    } // function addProrataMinutes()

    /**
     * Private Methods
     */

    /**
     * Renew the component, ending the old and starting a new
     *
     * <AUTHOR> Jones" <<EMAIL>>
     * @access public
     *
     * @return int The ID of the new included minutes config, or false on fail
     */
    public function renew()
    {
        parent::renew();

        // Billing period. Start the new period from midnight
        list($intYear, $intMonth, $intDay) = explode('-', date('Y-m-d', time()));
        $uxtRenewDate = mktime(0, 0, 0, $intMonth, $intDay, $intYear);

        // Expire the old call time
        //(rharvey) Commented out this line, as the rest of the system (including the base class) use 00:00:00.
        //$uxtEndDate = $uxtRenewDate - 1; // Upto 23:59:59 of the day before
        if (!$this->expireCalltime($uxtRenewDate)) {

            return false;
        }

        // Create new entry for new bill period
        $arrConfig = $this->getConfig();
        // start date should be 1 sec past midnight
        if (false === ($intIncludedMinutesId = $this->addIncludedMinutes(
                $arrConfig['intMinutes'],
                $uxtRenewDate + 1
            ))
        ) {
            return false;
        }

        return $intIncludedMinutesId;
    } // function renew()

    /**
     * Ends an included minutes config
     *
     * @param uxt $uxtEndTime The end time to use
     *
     * <AUTHOR> Jones" <<EMAIL>>
     *
     * @return bool True on success, or false
     */
    public function expireCalltime($uxtEndTime = null)
    {
        if (!isset($uxtEndTime)) {

            $uxtEndTime = time();
        }

        $dbhWrite = get_named_connection_with_db('wlr_calls');
        $intProductComponentInstanceID = $this->getProductComponentInstanceID();

        $strExpireCalltime = "UPDATE tblIncludedMinutes \n" .
            "  SET dtmEnd = FROM_UNIXTIME($uxtEndTime) \n" .
            "  WHERE intProductComponentInstanceID = $intProductComponentInstanceID \n" .
            "    AND dtmStart IS NOT NULL\n" .
            "    AND dtmEnd IS NULL\n";

        if (PrimitivesQueryOrExit($strExpireCalltime, $dbhWrite, 'CWlrCalltime::expireCalltime{ExpireCalltime}')) {

            return true;
        }

        return false;
    } // function expireCalltime()

    /**
     * Delete call time
     *
     * @return boolean
     */
    public function deleteCalltime()
    {
        $dbhWrite = get_named_connection_with_db('wlr_calls');
        $intProductComponentInstanceID = $this->getProductComponentInstanceID();

        $strExpireCalltime
            = "DELETE FROM tblIncludedMinutes WHERE intProductComponentInstanceID = $intProductComponentInstanceID";

        if (PrimitivesQueryOrExit($strExpireCalltime, $dbhWrite, 'CWlrCalltime::deleteCalltime{ExpireCalltime}')) {

            return true;
        }

        return false;
    } // function expireCalltime()

    /**
     * Function to add included minutes to account
     *
     * @param int $intMinutes    Minutes
     * @param uxt $uxtEnableDate Enabled date
     *
     * @access private
     * <AUTHOR> Jones" <<EMAIL>>
     *
     * @return boolean TRUE/FALSE
     */
    public function addIncludedMinutes($intMinutes, $uxtEnableDate = false)
    {
        if ($uxtEnableDate === false || $uxtEnableDate < 1) {

            $uxtEnableDate = time();
        }

        $dbhWrite = get_named_connection_with_db('wlr_calls');
        $intProductComponentInstanceID = $this->getProductComponentInstanceID();
        $strNewMinutes = "INSERT INTO tblIncludedMinutes" .
            "  (intProductComponentInstanceID, dtmStart, intIncludedMinutes) \n" .
            "  VALUES \n" .
            "  ({$intProductComponentInstanceID}, FROM_UNIXTIME({$uxtEnableDate}) , {$intMinutes}) \n";

        if (PrimitivesQueryOrExit($strNewMinutes, $dbhWrite, 'CWlrCalltime::addIncludedMinutes{NewMinutes}')) {

            return PrimitivesInsertIdGet($dbhWrite);
        }

        return false;
    } // function addIncludedMinutes($uxtInvoiceDate, $intMinutes)

    /**
     * WLR Product Component status change logger.
     *
     * @param string $strStatus Status
     *
     * @access private
     * <AUTHOR> Jones" <<EMAIL>>
     *
     * @return void
     */
    public function logStatusChange($strStatus)
    {
        $objEventLogger = $this->prvGetEventLogger();

        switch ($strStatus) {
            case 'CONFIGURED':
                $objEventLogger->logStatusChange('WlrCalltimeConfiguration');
                break;
            case 'ACTIVE':
                $objEventLogger->logStatusChange('WlrCalltimeActivation');
                break;
            case 'DEACTIVE':
                $objEventLogger->logStatusChange('WlrCalltimeDeactivation');
                break;
            case 'DESTROYED':
                $objEventLogger->logStatusChange('WlrCalltimeDestruction');
                break;
            default:
                break;
        } // switch ($strStatus)
    } // function logStatusChange($strStatus) // function logStatusChange($strStatus)

    /**
     * Get seconds used for fraud calls
     *
     * @return int
     */
    public function getSecondsUsedForFraudCalls()
    {
        $dbhWrite = get_named_connection_with_db('wlr_calls');

        $strQuery = "SELECT ifnull(sum(intSecondsRedeemed),0) AS intSecondsUsed
                                 FROM tblFraudIncludedSecondsRedemption fr
                            LEFT JOIN tblIncludedMinutes m
                                   ON m.intIncludedMinutesId = fr.intIncludedMinutesId
                           INNER JOIN tblFraudCallSection fcs
                                   ON fcs.intFraudCallSectionId = fr.intFraudCallSectionId
                           INNER JOIN tblFraudCall fc
                                   ON fc.intFraudCallId = fcs.intFraudCallId
                                WHERE m.dtmEnd IS NULL
                                  AND fc.bolObsolete = 0
                                  AND m.intProductComponentInstanceID = " . $this->getProductComponentInstanceID();
        $resResult = PrimitivesQueryOrExit(
            $strQuery,
            $dbhWrite,
            'Fetch the inclusive minutes used for fraud calls'
        );
        $intSecondsUsed = PrimitivesResultGet($resResult, 'intSecondsUsed');

        if (isset($intSecondsUsed)) {

            return $intSecondsUsed;
        }

        return 0;
    }

    /**
     * Get seconds used for cdr calls
     *
     * @return int
     */
    public function getSecondsUsedForCdrCalls()
    {
        $dbhWrite = get_named_connection_with_db('wlr_calls');

        $strQuery = "SELECT ifnull(sum(intSecondsRedeemed),0) AS intSecondsUsed
                       FROM tblIncludedSecondsRedemption fr
                  LEFT JOIN tblIncludedMinutes m
                         ON m.intIncludedMinutesId = fr.intIncludedMinutesId
                      WHERE m.dtmEnd IS NULL
                  AND m.intProductComponentInstanceID = " . $this->getProductComponentInstanceID();

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhWrite, 'Fetch the inclusive minutes used for CDR calls');
        $intSecondsUsed = PrimitivesResultGet($resResult, 'intSecondsUsed');

        if (isset($intSecondsUsed)) {

            return $intSecondsUsed;
        }

        return 0;
    }

    /**
     * Gets the Tarriff Id for calltime based on intServiceComponentProductID and intMinutes.
     *
     * @param int $intServiceComponentProductID Service component product id
     * @param int $intMinutes                   Minutes
     *
     * @access   public
     * <AUTHOR> Zaki" <<EMAIL>>
     *
     * @return intTariffId
     */
    public static function getTariffIDByMinutes($intServiceComponentProductID, $intMinutes)
    {
        self::includeLegacyFiles();

        $dbhConn = get_named_connection_with_db('dbProductComponents_reporting');
        $strQuery = "SELECT intTariffID
                       FROM tblTariff t
                 INNER JOIN tblProductComponentConfig pcc
                         ON pcc.intProductComponentConfigID = t.intProductComponentConfigID
                 INNER JOIN tblWlrCallTimeConfig ctc
                         ON ctc.intProductComponentConfigId = pcc.intProductComponentConfigId
                 INNER JOIN products.tblServiceComponentProduct scp
                         ON scp.intServiceComponentProductID = pcc.intServiceComponentProductID
                 INNER JOIN tblProductComponent pc ON pc.intProductComponentID = pcc.intProductComponentID
                      WHERE ctc.intMinutes = '$intMinutes'
                        AND scp.intServiceComponentProductID = '$intServiceComponentProductID'
                        AND pc.vchHandle = 'wlrcalltime'";

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Fetch the TariffID for inclusive minutes ', false);
        $intTariffId = PrimitivesResultGet($resResult, 'intTariffID');

        if ($intTariffId > 0) {
            return $intTariffId;
        }

        return 0;
    }
}
// class CWlrCalltime extends CProductComponent
