<?php

    /////////////////////////////////////////////////////////////////////
    // File:     config-cbc-flex-access.inc
    // Purpose:  Access mini-library for configuring the customers CBC Flex component
    /////////////////////////////////////////////////////////////////////

    /////////////////////////////////////////////////////////////////////
    // Functions
    //
    // Write functions
    // ---------------
    //
    // config_cbc_flex_restrict
    //
    // Read functions
    // --------------
    //
    // Modify Functions
    // ----------------
    //
    // Delete functions
    // ----------------
    //
    /////////////////////////////////////////////////////////////////////

    /////////////////////////////////////////////////////////////////////
    // Requires

    require_once('/local/data/mis/database/database_libraries/userdata-access.inc');
    require_once('/local/data/mis/database/database_libraries/cbc-access.inc');
    require_once('/local/data/mis/database/database_libraries/radius-access.inc');
    require_once('/local/data/mis/database/application_apis/Cbc/PaygBwMaintenance.class.php');

    // Requires
    /////////////////////////////////////////////////////////////////////

    /////////////////////////////////////////////////////////////////////
    // Data

    $global_component_configurators['310'] = 'ConfigFlexConfigurator';

    // Data
    /////////////////////////////////////////////////////////////////////

    // Hack to insert the component configurator array into PHP5's global scope
    // if it's not already there
    if (!isset($GLOBALS['global_component_configurators'])) {
        $GLOBALS['global_component_configurators'] = $global_component_configurators;
    } else {
        foreach ($global_component_configurators as $intIndex => $strConfigurator) {
            $GLOBALS['global_component_configurators'][$intIndex] = $strConfigurator;
        }
    }

    /////////////////////////////////////////////////////////////////////
    // Library functions

    /////////////////////////////////////////////////////////////
    // 
    // 
    function ConfigInsertCBCFlexID($intComponentID, $intCBCFlexID, $bolFixAllowed = FALSE)
    {
        $dbhConnection = get_named_connection_with_db('userdata');

        // CBC Fix defaults to on if allowed
        $intCbcFix = $bolFixAllowed ? 1 : 0;

        $strQuery = 'INSERT INTO tblConfigCBCFlex ' .
                    '           (intComponentID, ' .
                    '            intCBCFlexID, ' .
                    '            bolCBCFix, ' .
                    '            vchDBSrc) ' .
                    "    VALUES ('$intComponentID', " .
                    "            '$intCBCFlexID', " .
                    "            $intCbcFix, " .
                    '            "CBl")';

        PrimitivesQueryOrExit($strQuery, $dbhConnection);

        $intConfigID = PrimitivesInsertIdGet($dbhConnection);

        // Update the component with the new config id
        $strQuery = 'UPDATE components ' .
                    "   SET config_id = '$intConfigID', " .
                    '       db_src = "CBl" ' .
                    " WHERE component_id = '$intComponentID'";

        PrimitivesQueryOrExit($strQuery, $dbhConnection);

        $intAffectedRows = PrimitivesAffectedRowsGet($dbhConnection);

        // Update the components description to reflect the included bandwidth
        ConfigFlexUpdateDescription($intComponentID, $intCBCFlexID);

        return $intConfigID;

    } // function ConfigInsertCBCFlexID

    function ConfigUpdateCBCFlexID($intComponentID, $intCBCFlexID, $bolFixAllowed)
    {
        $dbhConnection = get_named_connection_with_db('userdata');

        $strQuery = 'UPDATE tblConfigCBCFlex ' .
                    "   SET intCBCFlexID = '$intCBCFlexID', " .
                    ($bolFixAllowed ? '' : 'bolCBCFix = 0, ') .
                    '       vchDBSrc = "CBl" ' .
                    " WHERE intComponentID = '$intComponentID'";

        PrimitivesQueryOrExit($strQuery, $dbhConnection);

        $intAffectedRows = PrimitivesAffectedRowsGet($dbhConnection);

        // Update the components description to reflect the included bandwidth
        ConfigFlexUpdateDescription($intComponentID, $intCBCFlexID);

        return $intAffectedRows;

    } // function ConfigUpdateCBCFlexID

    function ConfigFlexGetCBCFlexID($intComponentID)
    {
        $dbhConnection = get_named_connection_with_db('userdata');

        $strQuery = 'SELECT intCBCFlexID ' .
                    '  FROM tblConfigCBCFlex ' .
                    " WHERE intComponentID = '$intComponentID'";

        $resCBCFlexID = PrimitivesQueryOrExit($strQuery, $dbhConnection);

        $intCBCFlexID = PrimitivesResultGet($resCBCFlexID, 'intCBCFlexID');

        return $intCBCFlexID;

    } // function ConfigFlexGetCBCFlexID

    function ConfigFlexUpdateDescription($intComponentID, $intCBCFlexID)
    {
        $dbhConnection = get_named_connection_with_db('userdata');
        

        $arrCBCFlex = GetCBCFlexDetails($intCBCFlexID);

        $arrComponent = userdata_component_get($intComponentID);

        $userService = userdata_service_get($arrComponent['service_id']);

        if ($userService['isp'] != 'partner') {
            if (TRUE === userdataIsBvUser($arrComponent['service_id'])) {
                $arrUnits = array('GIGABYTE' => GIBIBYTE, 'MEGABYTE' => MIBIBYTE, 'KILOBYTE' => KIBIBYTE);
            } else {
                $arrUnits = null;
            }
        } else {
            $arrUnits = null;
        }

        $strDescription = addslashes(GetCustomerFriendlyBytes($arrCBCFlex['intIncludedBandwidthBytes'], 2, 0, $arrUnits) . ' Included Bandwidth');

        $strQuery = 'UPDATE components ' .
                    "   SET description = '$strDescription', " .
                    '       db_src = "CBl" ' .
                    " WHERE component_id = '$intComponentID'";

        PrimitivesQueryOrExit($strQuery, $dbhConnection);

        $intAffectedRows = PrimitivesAffectedRowsGet($dbhConnection);

        return $intAffectedRows;

    } // function ConfigFlexUpdateDescription

    function ConfigFlexSetCBCFlexID($intComponentID, $intCBCFlexID, $bolFixAllowed = FALSE)
    {
        // Check whether a record has been already added for this component
        $intCurrentCBCFlexID = ConfigFlexGetCBCFlexID($intComponentID);

        if ($intCBCFlexID == $intCurrentCBCFlexID) {
            return true;
        } elseif ($intCurrentCBCFlexID > 0) {
            $bolUpdated = ConfigUpdateCBCFlexID($intComponentID, $intCBCFlexID, $bolFixAllowed);

            return $bolUpdated;
        } else {
            $intConfigID = ConfigInsertCBCFlexID($intComponentID, $intCBCFlexID, $bolFixAllowed);

            return $intConfigID;
        }

        return false;

    } // function ConfigFlexSetCBCFlexID

    function ConfigFlexDelete($intComponentID)
    {
        $dbhConnection = get_named_connection_with_db('userdata');

        // Delete the config entry for this component
        $strQuery = 'DELETE FROM tblConfigCBCFlex ' .
                    " WHERE intComponentID = '$intComponentID'";

        PrimitivesQueryOrExit($strQuery, $dbhConnection);

        $intAffectedRows = PrimitivesAffectedRowsGet($dbhConnection);

        // If row was removed then upadte the component details to reflect it
        if ($intAffectedRows > 0) {
            $strQuery = 'UPDATE components ' .
                        '   SET config_id = "-1", ' .
                        '       description = "" ' .
                        " WHERE component_id = '$intComponentID'";

            PrimitivesQueryOrExit($strQuery, $dbhConnection);

            $intAffectedRows = PrimitivesAffectedRowsGet($dbhConnection);
        }

        return $intAffectedRows;

    } // function ConfigFlexDelete

    function ConfigFlexActivate($intComponentID, $intCBCFlexID)
    {
        $arrComponent = userdata_component_get($intComponentID);

        switch ($arrComponent['status'])
        {
            case 'unconfigured' :

                $bolFixAllowed = isCbcFixAllowed($arrComponent['service_id']);

                // Set the Flex option of the product
                $bolSetCBCFlexID = ConfigFlexSetCBCFlexID($intComponentID, $intCBCFlexID, $bolFixAllowed);

                // Activate the component
                if ($bolSetCBCFlexID > 0) {
                    userdata_component_set_status($intComponentID, 'active');

                    // If we have just added this component due to an account change from an Unlimited Account to a CBC Account
                    // we need to reset the usgae so this user does not get charged for usage that was built up from their Unlimited days
                    if (!CBCFlexResetUsage($arrComponent['service_id'])) {

                        $strHandle = 'CONFIGURE_CBC_FLEX_RESET_FAILURE';
                        $strSubject = 'Auto Configure of CBC Flex Component:: Reset Usage Failed';
                        $strDescription = $strSubject . ". Please investigate, if this was a none scheduled account change then you must reset the user's bandwidth " .
                                          "and bandwidth threshold. Failure to do so will result in the user being charged for usage used from their previous " .
                                          "account.\nIf this was not an account change then there will not be any usage to reset, therefore investigate why this " .
                                          "failed.\n\nA good place to start looking is in /local/data/mis/database/database_libraries/components/config-cbc-flex-access.inc";
                        $objAutoProblem = new PomsClient_AutoProblem(
                                                                     $strHandle,
                                                                     $strSubject,
                                                                     $strDescription,
                                                                     "Service ID: {$arrComponent['service_id']}\nComponent ID: $intComponentID"
                                                                    );
                        $objAutoProblem->raiseProblem();
                    }

                    // Find if the customer has an Extra Bandwdith Component
                    $intCBCExtraBandwidthComponentID = GetCBCExtraBandwidthUsableComponentID($arrComponent['service_id']);

                    if ($intCBCExtraBandwidthComponentID > 0) {
                        $bolConfigured = ConfigExtraBandwidthConfigurator($intCBCExtraBandwidthComponentID, 'auto_configure');

                        if ($bolConfigured == true) {
                            return true;
                        }

                        return false;
                    }

                    // Or if they have a PAYG Bandwidth Component
                    $intCBCPAYGBandwidthComponentID = GetCBCPAYGBandwidthUsableComponentID($arrComponent['service_id']);

                    if ($intCBCPAYGBandwidthComponentID > 0) {
                        $bolConfigured = ConfigCBCPAYGConfigurator($intCBCPAYGBandwidthComponentID, 'auto_configure');

                        if ($bolConfigured == true) {
                            return true;
                        }

                        return false;
                    }

                } // endif

                break;

            default :

                // Do nothing
                break;

        } // switch

        return false;

    } // function ConfigFlexActivate

    function ConfigFlexRefresh($intComponentID, $intCBCFlexID, $bolForceRefresh = true)
    {
        $arrComponent = userdata_component_get($intComponentID);

        $bolSetCBCFlexID = false;

        $bolConfigured = false;

        $bolUnrestrictSubscriber = true;

        switch ($arrComponent['status'])
        {
            case 'active' :

                //This needs to be done BEFORE we update the FlexID due to the squashed bandwidth calcs    
                // Unsquash the subscriber in Ellacoya

                $bolUnrestrictSubscriber = unrestrictCbcSubscriber($arrComponent['service_id']);

                $bolFixAllowed = isCbcFixAllowed($arrComponent['service_id']);

                $bolSetCBCFlexID = ConfigFlexSetCBCFlexID($intComponentID, $intCBCFlexID, $bolFixAllowed);

                // Find if the customer has an Extra Bandwdith Component
                $intCBCExtraBandwidthComponentID = GetCBCExtraBandwidthUsableComponentID($arrComponent['service_id']);

                if ($intCBCExtraBandwidthComponentID > 0) {
                    $bolConfigured = ConfigExtraBandwidthConfigurator($intCBCExtraBandwidthComponentID, 'auto_configure', $bolForceRefresh);
                } else {
                    // Or if they have a PAYG Bandwidth Component
                    $intCBCPAYGBandwidthComponentID = GetCBCPAYGBandwidthUsableComponentID($arrComponent['service_id']);

                    if ($intCBCPAYGBandwidthComponentID > 0) {
                        $bolConfigured = ConfigCBCPAYGConfigurator($intCBCPAYGBandwidthComponentID, 'auto_configure', $bolForceRefresh);
                    }
                }

                return (bool) ($bolConfigured && $bolSetCBCFlexID && $bolUnrestrictSubscriber);

                break;

            default :

                // Do nothing
                break;

        } // switch

        return false;

    } // function ConfigFlexRefresh

    function ConfigFlexDestroy($intComponentID)
    {
        $arrComponent = userdata_component_get($intComponentID);

        switch ($arrComponent['status']) {
            case 'active' :

                // Remove component settings and clear component fields
                $bolDelete = ConfigFlexDelete($intComponentID);

                if ($bolDelete > 0) {
                    userdata_component_set_status($intComponentID, 'destroyed');

                    return true;
                }

                break;

            case 'unconfigured' :

                userdata_component_set_status($intComponentID, 'destroyed');

                return true;

                break;

            default :

                // Do nothing
                break;
        }

        return false;

    } // function ConfigFlexDestroy

    /////////////////////////////////////////////////////////////
    // Function:   ConfigFlexConfigurator
    // Purpose:    Auto-matically performs actions
    //             Note that the auto-configure DOES NOT DO ANYTHING as the component requires the CBC Flex option setting
    // Arguments:  $intComponentID
    //             $strAction
    //             $intCBCFlexID
    // Returns:    bool
    /////////////////////////////////////////////////////////////
    function ConfigFlexConfigurator($intComponentID, $strAction, $intCBCFlexID=false)
    {
        $bolConfigured = false;
        if (in_array($strAction, array('auto_enable', 'auto_refresh', 'auto_configure')) && 
            $intCBCFlexID == false) {

            $intCBCFlexID = ConfigFlexGetCBCFlexID($intComponentID);
            if ($intCBCFlexID == false) {
                $component = userdata_component_get($intComponentID);

                $service_id = $component['service_id'];
                //get flex ids for this type of account
                $service = userdata_service_get($service_id);
                $arrCBCFlexIDs = GetCurrentCBCFlexIDs($service['type']);
                $intCBCFlexID = empty($arrCBCFlexIDs[0]) ? 0 : $arrCBCFlexIDs[0];
            }
            if ($intCBCFlexID == false) {
                return false;
            }
        }
        switch ($strAction) {
            case 'auto_enable' :

                $bolConfigured = ConfigFlexActivate($intComponentID, $intCBCFlexID);

                break;

            case 'auto_refresh' :

                $bolConfigured = ConfigFlexRefresh($intComponentID, $intCBCFlexID);

                break;

            case 'auto_destroy' :

                $bolConfigured = ConfigFlexDestroy($intComponentID);

                break;

            case 'auto_configure' :

                $arrComponent = userdata_component_get($intComponentID);

                if ($arrComponent['status'] == 'unconfigured') {
                    $bolConfigured = ConfigFlexActivate($intComponentID, $intCBCFlexID);
                } elseif ($arrComponent['status'] == 'active') {
                    $bolConfigured = ConfigFlexRefresh($intComponentID, $intCBCFlexID);
                }

            case 'auto_disable' :
            default :

                // Do nothing
                break;
        }

        if ($bolConfigured == true) {
            return true;
        }

        return false;

    } // function ConfigFlexConfigurator

    /////////////////////////////////////////////////////////////
    // Function:   CBCFlexResetUsage
    // Purpose:    During an account change the user needs their bandwidth reseting
    //             so when we add the new flex component we should try and reset the usage
    // Arguments:  $intServiceId
    // Returns:    bool
    /////////////////////////////////////////////////////////////
    function CBCFlexResetUsage($intServiceId)
    {
        $arrService = userdata_service_get($intServiceId);
        $intRadiusId = radius_get_radius_id_from_username_isp($arrService['username'], $arrService['isp']);
        if (ctype_digit($intRadiusId) && $intRadiusId > 0) {

            // Note: cbc_reporting is actually the connection we *write* to tblTotalBandwidthUsed on.
            $dbCbcR = get_named_connection_with_db('cbc_reporting');

            $strQuery = "UPDATE tblTotalBandwidthUsed SET
                         time_used = 0,
                         bytes_recieved = 0,
                         bytes_transfered = 0
                         WHERE radius_id = {$intRadiusId}";
            $res = PrimitivesQueryOrExit($strQuery, $dbCbcR, 'CBCFlexResetUsage', FALSE);

            if ($res) {

                Cbc_PaygBwMaintenance::autoSetCbcBandwidthThreshold($intServiceId);
                return TRUE;
            }
        } else {

            // as there is no radius id for this service then we have to assume this component
            // is being added as part of a new signup, therefore there will be no usage
            // to reset, so return true
            return TRUE;
        }
        return FALSE;
    } // function CBCFlexResetUsage

    // Library functions
    /////////////////////////////////////////////////////////////////////