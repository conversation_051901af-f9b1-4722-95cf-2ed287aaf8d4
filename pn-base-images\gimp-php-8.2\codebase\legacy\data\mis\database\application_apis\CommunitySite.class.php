<?php

    //Secret key used for hashing requests
    define('XMLRPC_KEY', 'JJUMo6wBBnCpbhUNgEVK29HBQUn4bX');

	/**
	* CommunitySite.class.php
	*
	* Defines an object to be used for contacting the Community Site from the
	* portals and/or Workplace
	*
	* @package    Community Site
	* @access     public
	* <AUTHOR>
	* @copyright  Copyright PlusNet PLC
	*
	*/


	class CommunitySite
	{

		/**
		* A Smarty object
		*
		* @access private
		* @var Object
		*/
		var $objSmarty;

		/**
		* Holds error message
		*
		* @access private
		* @var String
		*/

		var $strError = '';

		/**
		* Create Smarty
		*
		*/

		function initialize()
		{

			$this->objSmarty = new Smarty;
			$this->objSmarty->compile_dir = SMARTY_COMPILE_DIR;
			$this->objSmarty->cache_dir = SMARTY_CACHE_DIR;
			$this->objSmarty->template_dir = '/local/data/mis/database/application_apis/templates';

		}


		/**
		* refresh - send a refresh command to the Community Site
		*
		* @returns Boolean
		*
		*/
		function refresh($strUsername, $bolIsFree)
		{

            //Generate a hash, for security
            $timestamp = time();
            $hash = $this->signRequest(array($strUsername), $timestamp); 

			$this->objSmarty->assign('strUsername', $strUsername);
			$this->objSmarty->assign('bolIsFree', $bolIsFree);
			$this->objSmarty->assign('timestamp', $timestamp);
			$this->objSmarty->assign('hash', $hash);

			$strXML = $this->objSmarty->fetch('CommunitySite_update.xml');
			$bolResult = $this->_contactCommunitySite($strXML);
			return $bolResult;

		}


		/**
		* create - Sends an account creation command to the Community Site
		*
		* @returns Boolean
		*
		*/
		function create($strUsername, $strEmail, $strPassword, $bolIsFree)
		{
            $strWPPassword = md5($strPassword);
            $strSMFPassword = sha1(strtolower($strUsername) . $strPassword);

            //Generate a hash, for security
            $timestamp = time();
            $hash = $this->signRequest(array($strUsername, $strWPPassword, $strSMFPassword, $strEmail), $timestamp); 

			// Assign Smarty Variables
			$this->objSmarty->assign('strUsername', $strUsername);
			$this->objSmarty->assign('strWPPassword', $strWPPassword);
			$this->objSmarty->assign('strSMFPassword', $strSMFPassword);
			$this->objSmarty->assign('strEmail', $strEmail);
			$this->objSmarty->assign('bolIsFree', $bolIsFree);
			$this->objSmarty->assign('timestamp', $timestamp);
			$this->objSmarty->assign('hash', $hash);

			// Submit
			$strXML = $this->objSmarty->fetch('CommunitySite_register.xml');
			$bolResult = $this->_contactCommunitySite($strXML);
			return $bolResult;

		}


		/**
		* getUsernameFromComponentID - When fed a Component ID return the
		* username for the user on the Community Site.
		*
		* @returns String
		*
		*/
		function getUsernameFromComponentID($intComponentID)
		{

			$dbhConnection = get_named_connection_with_db('userdata_reporting');

			$strSQL = "SELECT vchUsername FROM tblConfigCommunitySite WHERE intComponentID  = '" .
                                  mysql_real_escape_string($intComponentID) . "'";

			$resResults = PrimitivesQueryOrExit($strSQL, $dbhConnection);
			$strUsername = PrimitivesResultGet($resResults, 'vchUsername');

			return $strUsername;

		}

		/**
		* getServiceIDFromUsername - When fed a Community Username return the
		* service ID for the user
		*
		* @returns Integer
		*
		*/

		function getServiceIDFromUsername($strUsername)
		{

			$dbhConnection = get_named_connection_with_db('userdata_reporting');

			$strSQL = "SELECT intComponentID FROM tblConfigCommunitySite WHERE vchUsername = '" .
				  mysql_real_escape_string($strUsername) . "'";

			$resResults = PrimitivesQueryOrExit($strSQL, $dbhConnection);
			$intComponentID = PrimitivesResultGet($resResults, 'intComponentID');

			$arrComponent = userdata_component_get($intComponentID);

			if (true === is_array($arrComponent))
			{
				return $arrComponent['service_id'];
			}
			else
			{
				return 0;
			}
		}

		/**
		* getErrorMessage
		*
		* @returns the Error Message
		*/

		function getErrorMessage()
		{
			return $this->strError;
		}

		/**
		* _contactCommunitySite - Contact community site and return a bool
		*
		* @returns Boolean
		*
		*/

        function _contactCommunitySite($strXMLBody)
        {
            // ST-1187 Return true by default - removes XMLRPC call
            return true;
        }

        protected function signRequest($params, $timestamp) {
            return hash_hmac('sha256', implode($params) . $timestamp, XMLRPC_KEY);
        }

	}
?>
