<?php

/**
 * Plusnet Business Hosting Access Configurator
 *
 * @package   LegacyCodebase
 * <AUTHOR> <<EMAIL>>
 * @copyright 2013 Plusnet
 *
 */

$global_component_configurators[COMPONENT_PLUSNET_BUSINESS_HOSTING] = "config_hosting_configurator";

// Data
/////////////////////////////////////////////////////////////////////
// Hack to insert the component configurator array into PHP5's global scope
// if it's not already there
if (!isset($GLOBALS['global_component_configurators'])) {
    $GLOBALS['global_component_configurators'] = $global_component_configurators;
} else {
    foreach ($global_component_configurators as $intIndex => $strConfigurator) {
        $GLOBALS['global_component_configurators'][$intIndex] = $strConfigurator;
    }
}

/**
 * Create a hosting configuration for a component
 * 
 * @param type $component_id ID of component
 * @param type $hostopia_id  ID in Hostopia Hosting System
 * 
 * @return type
 */

function configHostingAdd($component_id, $hostopia_id)
{
    $connection = get_named_connection("userdata");
    $component_id = mysql_real_escape_string($component_id);
    $hostopia_id  = mysql_real_escape_string($hostopia_id);

    mysql_query(
        "INSERT INTO tblConfigHosting (intComponentId, vchHostopiaId) VALUES ('$component_id', '$hostopia_id')",
        $connection
    ) or report_error(__FILE__, __LINE__, mysql_error($connection));

    $intHostingId = mysql_insert_id($connection);
    return $intHostingId;

}

/**
 * Add or update Hosting component
 * 
 * @param type $component_id ID of component
 * @param type $hostopia_id  ID in Hostopia Hosting System
 * 
 * @return null
 */

function configHostingManualConfig($component_id, $hostopia_id)
{
    //Check to see if there is an component and get its status
    $action = null;
    $component_details=userdata_component_get($component_id);
    switch($component_details['status']) {
        case "unconfigured":
            $action="do_new";
            break;
        case "queued-activate":
        case "queued-reactivate":
        case "active":
        case "queued-deactivate":
        case "queued-deconfigure":
            $action="do_change";
            break;
        default:
            report_error(__FILE__, __LINE__, "Error with component");
    }
    $connection = get_named_connection("userdata");
    switch($action) {
        case "do_new":
            $hosting_config_id= configHostingAdd($component_id, $hostopia_id);
            $component_id = mysql_real_escape_string($component_id);
            $query="UPDATE components SET config_id = '$hosting_config_id', status='active' WHERE component_id = '$component_id'";
            mysql_query($query, $connection) or report_error(__FILE__, __LINE__, mysql_error($connection));
            break;

        case "do_change":
            configHostingUpdate($component_id, $hostopia_id);
            $component_id = mysql_real_escape_string($component_id);
            $query="UPDATE components SET status='active' WHERE component_id = '$component_id'";
            mysql_query($query, $connection) or report_error(__FILE__, __LINE__, mysql_error($connection));
            break;
    }
}

/**
 * Update hostopia_id of Hosting component
 * 
 * @param type $component_id Component ID
 * @param type $hostopia_id  ID in Hostopia Hosting System
 * 
 * @return null
 */

function configHostingUpdate($component_id, $hostopia_id)
{
    $connection = get_named_connection("userdata");
    $component_id = mysql_real_escape_string($component_id);
    $hostopia_id  = mysql_real_escape_string($hostopia_id);

    mysql_query("UPDATE tblConfigHosting SET vchHostopiaId = '$hostopia_id' WHERE intComponentId = '$component_id'", $connection)
        or report_error(__FILE__, __LINE__, mysql_error($connection));
}

/**
 * Get hosting element by Hostopia ID
 * 
 * @param type $hostopia_id Hostopia ID
 * 
 * @return type
 */

function configHostingGetByHostopiaId($hostopia_id)
{

    $connection = get_named_connection("userdata");
    $hosting_id = mysql_real_escape_string($hosting_id);

    $result = mysql_query("SELECT * FROM tblConfigHosting WHERE vchHostopiaId = '$hostopia_id'", $connection)
        or report_error(__FILE__, __LINE__, mysql_error($connection));

    $hosting = mysql_fetch_array($result, MYSQL_ASSOC);
    mysql_free_result($result);
    return $hosting;
}

/**
 * Get hosting element by hosting_id
 * 
 * @param type $hosting_id Hosting ID
 * 
 * @return type
 */

function configHostingGet($hosting_id)
{

    $connection = get_named_connection("userdata");
    $hosting_id = mysql_real_escape_string($hosting_id);

    $result = mysql_query("SELECT * FROM tblConfigHosting WHERE intHostingId = '$hosting_id'", $connection)
        or report_error(__FILE__, __LINE__, mysql_error($connection));

    $hosting = mysql_fetch_array($result, MYSQL_ASSOC);
    mysql_free_result($result);
    return $hosting;
}

/**
 * Delete a hosting configuration
 * 
 * @param type $intComponentId ID of component
 * 
 * @return null
 */

function configHostingDelete($intComponentId)
{

    $connection = get_named_connection("userdata");

    $result = mysql_query(
        "SELECT vchHostopiaId
        FROM tblConfigHosting
        WHERE intComponentId  = '$intComponentId'",
        $connection
    ) or report_error(__FILE__, __LINE__, mysql_error($connection));
    $hostopia_id = mysql_result($result, 0, 0);
    mysql_free_result($result);

    mysql_query(
        "DELETE FROM tblConfigHosting
        WHERE vchHostopiaId = '$hostopia_id'",
        $connection
    ) or report_error(__FILE__, __LINE__, mysql_error($connection));

}

/**
 * Destroy Hosting component
 * 
 * @param type $component_id ID of component
 * 
 * @return null 
 */
function configHostingAutoDestroy ($component_id)
{
    $component = userdata_component_get($component_id);

    switch ($component["status"]) {
        case "unconfigured":
        case "queued-activate":
        case "destroyed":
            // These states have no configuration
            break;

        case "active":
        case "deactive":
        case "queued-deactivate":
        case "queued-deconfigure":
        case "queued-reconfigure":
        case "queued-destroy":
            // These states have a configuration
            // Excise the config record and associated backend entry
            configHostingDelete($component_id);
            break;

        default:
            break;
    }

    // Mortify the component
    userdata_component_set_status($component_id, "destroyed");

}

/**
 * Hosting component Configuratir
 * 
 * @param type $component_id ID of Component
 * @param type $action       Action name
 * 
 * @return null
 */

function configHostingConfigurator($component_id, $action)
{
    switch ($action) {

        case "auto_destroy":
            configHostingAutoDestroy($component_id);
            break;

        default:
            break;

    }

}
