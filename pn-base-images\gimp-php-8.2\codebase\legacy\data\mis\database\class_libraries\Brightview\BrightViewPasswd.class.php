<?php
require_once '/local/data/mis/database/class_libraries/Brightview/BVDB.class.php';

class BrightViewPasswd
{
	private $strUserName;
	private $strRealm;
	private $strFullName;
	private $strPasswd;
	private $dteModifyDate;
	protected $intMailId;
	private $intParentId;
	private $strOptions; // very badly named - this is a string!!!

	//http://www.vbmysql.com/articles/mysql/the-mysql-set-datatype/
	const OPT_DIALUP =     1;
	const OPT_MAIL =       2;
	const OPT_FTP =        4;
	const OPT_POP3 =       8;
	const OPT_IMAP =      16;
	const OPT_FRIACO =    32;
	const OPT_WEBMAIL =   64;
	const OPT_SMTP =     128;
	const OPT_GRAB =     256;
	const OPT_RESTRICT = 512;
	
	const STATUS_ACTIVE = 'active';
	const STATUS_POISONED = 'poisoned';
	const STATUS_ARCHIVE = 'archive';

	private static $arrSoulstoneSet = array(
		'dialup'   => self::OPT_DIALUP,
		'mail'     => self::OPT_MAIL,
		'ftp'      => self::OPT_FTP,
		'pop3'     => self::OPT_POP3,
		'imap'     => self::OPT_IMAP,
		'friaco'   => self::OPT_FRIACO,
		'webmail'  => self::OPT_WEBMAIL,
		'smtp'     => self::OPT_SMTP,
		'grab'     => self::OPT_GRAB
	);

	/**
	 * method getMailIdByUserNameAndRealm
	 * 
	 * Function to get mailid for a givem username and realm
	 *
	 * @param string $strUserName
	 * @param string $strRealm
	 * @return int
	 */

	public static function getMailIdByUserNameAndRealm($strUserName, $strRealm)
	{
		$stmt = BVDB::db()->prepare('SELECT mailid FROM passwd WHERE username = ? AND realm = ?');
		$stmt->execute(array($strUserName, $strRealm));
		
		return $stmt->fetchColumn();
	}

	private static function fromResult($intType, $arrRes)
	{
		switch (count($arrRes))
		{
			case   0: return null;
			case   1: break;
			default : throw new Exception('More than one mialid: '.print_r($arrRes, true));
		}

		switch ($intType)
		{
			case self::OPT_MAIL: return BrightViewMail::fromArray($arrRes[0]);
		}
		throw new Exception('Uknnown type: '.$intTypeId);
	}

	/**
	 * method fromUserNameAndRealm
	 * 
	 * Function to return BrightView* object based on passed object typem username and realm
	 *
	 * @param int $intType
	 * @param string $strUserName
	 * @param string $strRealm
	 * @return object
	 */
	public static function fromUserNameAndRealm($intType, $strUserName, $strRealm)
	{
		$stmt = BVDB::db()->prepare('SELECT * FROM passwd WHERE username = ? AND realm = ? AND options & ?');
		$stmt->execute(array($strUserName, $strRealm, $intType));
		return self::fromResult($intType, $stmt->fetchAll(PDO::FETCH_ASSOC));
	}

	/**
	 * method getMail
	 * 
	 * Function to return BrightViewMail object for a given username and realm 
	 *
	 * @param string $strUserName
	 * @param strin $strRealm
	 * @return object
	 */

	public static function getMail($strUserName, $strRealm)
	{
		return self::fromUserNameAndRealm(self::OPT_MAIL, $strUserName, $strRealm);
	}

	public function setUserName($strUserName) {$this->strUserName = $strUserName;}
	public function setRealm($strRealm) {$this->strRealm = $strRealm;}
	public function setFullName($strFullName) {$this->strFullName = $strFullName;}
	public function setPassword($strPasswd) {$this->strPasswd = $strPasswd;}
	public function setModifyDate($dteModifyDate) {$this->dteModifyDate = $dteModifyDate;}
	public function setMailId($intMailId) {$this->intMailId = $intMailId;}
	public function setParentId($intParentId) {$this->intParentId = $intParentId;}
	public function setOptions($strOptions) {$this->strOptions = $strOptions;}

	public function getUserName() {return $this->strUserName;}
	public function getMailId() {return $this->intMailId;}
	public function getRealm() {return $this->strRealm;}
	public function getPasswd() {return $this->strPasswd;}
	public function getParentId() {return $this->intParentId;}
	public function getFullName() {return $this->strFullName;}
	public function getOptions() {return $this->strOptions;}

	public function getOptionsAsBits()
	{
		$intBitmask = 0;

		$arrSelectedOptions = array_intersect(explode(',', $this->strOptions), array_keys(self::$arrSoulstoneSet));

		foreach ($arrSelectedOptions as $strOption) {

			$intBitmask |= self::$arrSoulstoneSet[$strOption];
		}


		return $intBitmask;
	}



	protected function setVars($arr)
	{
		$this->setMailId($arr['mailid']);
		$this->setUserName($arr['username']);
		$this->setFullName($arr['fullname']);
		$this->setPassword($arr['passwd']);
		$this->setRealm($arr['realm']);
		$this->setModifyDate($arr['modifydate']);
		$this->setParentId($arr['parentid']);
		$this->setOptions($arr['options']);
	}
}
