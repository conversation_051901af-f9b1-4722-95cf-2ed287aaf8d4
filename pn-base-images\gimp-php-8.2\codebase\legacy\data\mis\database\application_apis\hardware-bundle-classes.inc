<?php

	///////////////////////////////////////////////////////////////////////////
	// hardware-bundle-classes.inc
	// <PERSON>, October 2002
	// Revised Neil <PERSON>, June 2004
	// $Id: hardware-bundle-classes.inc,v 1.35 2009-06-17 18:34:43 rmerewood Exp $
	//
	// This file contains requires for all of the hardware bundle classes.
	// I have divided the classes into base, extended, Insight and BT.
	// This makes for more managable files and easier editing.
	//
	// IMPORTANT:
	// The database has been designed to allow each item of the order to be
	// handled by a different supplier, thereby having its own status. However,
	// these classes have been written with the assumption that the supplier
	// and status for each item in an order are the same.

	// Base bundle classes
	require_once('/local/data/mis/database/application_apis/base-hardware-bundle-classes.inc');
	// Extended bundle classes
	require_once('/local/data/mis/database/application_apis/extended-hardware-bundle-classes.inc');
	// RMA extended classes
	require_once('/local/data/mis/database/application_apis/rma-hardware-bundle-classes.inc');
	// BT classes
	require_once('/local/data/mis/database/application_apis/bt-hardware-bundle-classes.inc');
	//Insight - pelase do not remove this even if we do not use Insight as a hardware provider any more
	require_once('/local/data/mis/database/application_apis/insight-hardware-bundle-classes.inc');
	//NetLynk classes
	require_once('/local/data/mis/database/application_apis/netlynk-hardware-bundle-classes.inc');
