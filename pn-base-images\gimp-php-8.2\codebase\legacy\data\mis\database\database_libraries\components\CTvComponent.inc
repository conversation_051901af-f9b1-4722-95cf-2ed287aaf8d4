<?php

require_once('/local/data/mis/database/database_libraries/components/CComponent.inc');
require_once('/local/data/mis/database/database_libraries/components/CProduct.inc');
require_once('/local/data/mis/database/database_libraries/components/CProductComponent.inc');

/**
 * YouView Tv Component
 *
 * <AUTHOR> <<EMAIL>>
 */
class CTvComponent extends CProduct
{
	/**
	 * Destroy the TV Component and any related product components
	 */
	public function destroy()
	{
		$this->prvSetStatus('queued-destroy');

		$tvComponent = new CProduct($this->getComponentID());
		$arrTvProductComponents = $tvComponent->getProductComponents();

		/** @var CProductComponent $productComponent */
		foreach ($arrTvProductComponents as $productComponent) {
			$productComponent->destroy();
		}

		$this->prvSetStatus('destroyed');

		return true;
	}
}
