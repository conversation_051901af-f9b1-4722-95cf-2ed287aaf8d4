<?php
/**
* MigrationAway
* Interface to Java MigrationAway manager
*
* @package  SoapSession
* @depend	SoapSession
* <AUTHOR> <<EMAIL>>
* @version  $Id: MigrationAway.class.php,v 1.2 2007-05-10 08:21:19 mjagielski Exp $:
*/

configDefineIfMissing('MIGRATION_AWAY_WSDL', 'http://mboss.businesstier.plus.net:8080/mboss-wsdl-fix/MigrationAwayDelegate.wsdl');

class MigrationAway extends SoapSession
{	

	/**
	 * m_strPackage 
	 * package description (will be included in autoproblem raised)
	 * @var string
	 * @access public
	 */
	var $m_strPackage = 'Migration Away SOAP Library';

	/**
	 * m_strProblemGroup 
	 * problem group name 
	 * 
	 * @var string
	 * @access public
	 */
	var $m_strProblemGroup = 'MACMigrateAwaySoapCallFailed';

	/**
	 * objSoapClient 
	 * 
	 * @var object
	 * @access public
	 */
	var $objSoapClient;

	/**
	 * MigrationAway 
	 * 
	 * @param mixed $strPartnerDataset 
	 * @access public
	 * @return void
	 */
	function MigrationAway($strPartnerDataset)
	{
		$this->SoapSession($strPartnerDataset);

		$this->objSoapClient = $this->getSoapClient(MIGRATION_AWAY_WSDL); 

		if(!is_object($this->objSoapClient))
		{
			//object wasn't created and event was raised.
			//raise problem with that event 
			$arrArgsExtra = array('strFile' => __FILE__, 'intLine' => __LINE__);
			$this->eventRaise($arrArgsExtra);

			return FALSE;
		}
	}

	/**
	 * sendMacReceived 
	 * 
	 * @param int $intServiceID 
	 * @param string $strMacKey 
	 * @param date $dteMacExpiryDate 
	 * @param bool $bolIsMigrationExternal 
	 * @param int $intOriginalTicketID 
	 * @param string $strSupplierHandle 
	 * @param bool $bolIsAutomaticProcess 
	 * @access public
	 * @return bool  
	 */
	function sendMacReceived($intServiceID, $strMacKey, $dteMacExpiryDate, $bolIsMigrationExternal, 
	                         $intOriginalTicketID, $strSupplierHandle, $bolIsAutomaticProcess)
	{
		/* macReceived parameters: 
		 * sessId - sessionId
	     * serviceId - serviceId of customer
		 * macKey - received MAC key
		 * macExpiryDate - received expiry date from supplier, ISO 8601 format, date part only (2007-03-21)
		 * isMigrationExternal - flag to determine if it is external or internal migration
		 * originalTicketId - original ticket id tracking progress of migration process
		 * supplierHandle - handle of supplier (BT, TISCALI)
		 * isAutomaticProcess - was the MAC received in automatic way (true) or manual (false). Used only to decide what service event should be raised on a customer account
		 * returns void or throws exception
		 */
		$unkResult = $this->callSoapClient('macReceived',
		                                   array($this->_arrSession, 
		                                         $intServiceID, 
		                                         $strMacKey,
		                                         $dteMacExpiryDate,
		                                         $bolIsMigrationExternal,
		                                         $intOriginalTicketID,
		                                         $strSupplierHandle,
		                                         $bolIsAutomaticProcess
		                                        ),
		                                   $this->objSoapClient
		                                  );

		if(!$unkResult) 
		{

			return FALSE;
		}

		return TRUE;
	}


	/**
	 * sendMacFailed 
	 * 
	 * @param int $intServiceID 
	 * @param bool $bolIsMigrationExternal 
	 * @param string $strOriginalTicketID 
	 * @param string $strSupplierHandle 
	 * @access public
	 * @return bool
	 */
	function sendMacFailed($intServiceID, $bolIsMigrationExternal, $strOriginalTicketID, $strSupplierHandle)
	{
		$unkResult = $this->callSoapClient('failedToReceiveMac',
		                                   array($this->_arrSession,
		                                         $intServiceID,
		                                         $bolIsMigrationExternal,
		                                         $strOriginalTicketID,
		                                         $strSupplierHandle
		                                        ),
		                                   $this->objSoapClient
		                                  );

		if(!$unkResult) {

			return FALSE;
		}

		return TRUE;
	}


} // end class MigrationAway 

