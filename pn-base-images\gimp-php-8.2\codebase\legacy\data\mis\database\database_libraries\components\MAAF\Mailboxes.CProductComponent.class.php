<?php
/**
 * Webmail product component
 *
 * @package components.MAAF
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @version $Id: Mailboxes.CProductComponent.class.php,v 1.2 2007-12-03 07:52:15 swestcott Exp $
 */

/**
 * Needed libraries
 */
require_once('/local/data/mis/database/database_libraries/components/CProductComponent.inc');
require_once('/local/data/mis/database/database_libraries/userdata-access.inc');

/**
 * Product component class for classes  MAAF_CProductComponent_BundledMailboxes
 * and MAAF_CProductComponent_PackOf5Mailboxes
 *
 * <AUTHOR> <<EMAIL>>
 */
abstract class MAAF_CProductComponent_Mailboxes extends CProductComponent
{
    /**
     * Status event raiser
     *
     * @var string
     */
    protected $strStatusEventRaiser = '';

    /**
     * Method changes the status of mailboxes to active
     *
     * <AUTHOR> <<EMAIL>>
     *
     * @return void
     */
    public function reEnable()
    {
        $this->prvSetStatus('QUEUED_ACTIVATE');

        $bolOperationResult = $this->modifyMailboxStatus('active');

        if ($bolOperationResult) {
            $this->prvSetStatus('ACTIVE');
            $this->logStatusChange('ACTIVE');
        }

        return $bolOperationResult;

    } // end of method reEnable()

    /**
     * Method checks if component is full of mailboxes
     *
     * <AUTHOR> Bargiel <<EMAIL>>
     *
     * @return boolean
     */
    public function isFullOfMailboxes()
    {
        $resDBHandle = get_named_connection_with_db('userdata');

        $strQuery = 'SELECT count(*) AS intNumberOfMailboxes '
            . 'FROM BrightviewServices.tblConfigWebMail '
            . 'WHERE intProductComponentInstanceID='
            . mysql_real_escape_string($this->getProductComponentInstanceID());

        $arrCount = self::doQuery($strQuery, $resDBHandle);

        if (intval($arrCount['intNumberOfMailboxes']) < $this->getNumberOfAllowedMailboxes()) {

            return false;
        }

        return true;

    } // end of method isFullOfMailboxes()

    /**
     * Method add mail id to configuration table
     *
     * <AUTHOR> Bargiel <<EMAIL>>
     *
     * @param integer $intMailID
     *
     * @return void
     */
    public function addMailIDToConfiguration($intMailID)
    {
        $resDBHandle = get_named_connection_with_db('userdata');

        $strQuery = 'INSERT INTO BrightviewServices.tblConfigWebMail '
            . 'SET intMailID=' . mysql_real_escape_string($intMailID)
            . ', intProductComponentInstanceID='
            . mysql_real_escape_string($this->getProductComponentInstanceID());

        self::doQuery($strQuery, $resDBHandle);

    } // end of method addMailIDToConfiguration()

    /**
     * Enter description here...
     *
     * @param unknown_type $intMailID
     *
     * @return void
     */
    public static function deleteMailIDFromConfiguration($intMailID)
    {
        self::includeLegacyFiles();

        $resDBHandle = get_named_connection_with_db('userdata');

        $strQuery = 'DELETE FROM BrightviewServices.tblConfigWebMail '
            . 'WHERE intMailID=' . mysql_real_escape_string($intMailID);

        self::doQuery($strQuery, $resDBHandle);
    } // end of method deleteMailIDFromConfiguration()

    /**
     * Method returns number of mailboxes
     * This method is a wrapper that returns value of
     * constance MAILBOXES_NUMBER (see child classes)
     *
     * <AUTHOR> Bargiel <<EMAIL>>
     *
     * @return integer
     */
    abstract protected function getNumberOfAllowedMailboxes();

    /**
     * Component configurator function. Disables the component.
     *
     * <AUTHOR> Bargiel <<EMAIL>>
     *
     * @return void
     */
    function disable()
    {
        // Set state to queued-activate
        $this->prvSetStatus('QUEUED_DEACTIVATE');

        $this->modifyMailboxStatus('poisoned');

        // Set status to active
        $this->prvSetStatus('DEACTIVE');

        $this->refreshInstance($this->m_intProductComponentInstanceID);

        // Log status change
        $this->logStatusChange('DEACTIVE');

        return true;

    } // end of method disable()

    /**
     * Method modifies mailboxes statuses
     *
     * <AUTHOR> Bargiel <<EMAIL>>
     *
     * @param string $strStatus
     *
     * @return void
     */
    protected function modifyMailboxStatus($strStatus)
    {
        // Get mailids to disable
        $resDBHandle = get_named_connection_with_db('userdata');

        $strSQL = 'SELECT intMailID FROM BrightviewServices.tblConfigWebMail '
            . 'WHERE intProductComponentInstanceID = '
            . mysql_real_escape_string($this->getProductComponentInstanceID());

        $arrMailID = self::doQuery($strSQL, $resDBHandle);

        $strSQL = 'UPDATE passwd SET status="' . mysql_real_escape_string($strStatus) . '" WHERE mailid = :mailid';

        $objPDOStatement = BVDB::db()->prepare($strSQL);

        foreach ($arrMailID as $arrMailID) {
            $objPDOStatement->bindValue(':mailid', $arrMailID['intMailID'], PDO::PARAM_INT);
            $objPDOStatement->execute();
        }

        return true;

    } // end of method modifyMailboxStatus()

    /**
     * Method destroys component
     *
     * <AUTHOR> Bargiel <<EMAIL>>
     *
     * @param  array Array of optional arguments (required to satisfy interface)
     *
     * @return void
     */
    public function destroy($arrArgs = array())
    {
        $this->modifyMailboxStatus('archive');
        $this->logStatusChange('DESTROYED');

        return parent::destroy();

    } // end of method destroy()

    /**
     * Method executes query
     *
     * <AUTHOR> Bargiel <<EMAIL>>
     *
     * @param string $strQuery    Query to execute
     * @param array  $resDBHandle Db connection details
     *
     * @throws Exception
     *
     * @return array
     *
     */
    protected static function doQuery($strQuery, $resDBHandle = null)
    {
        self::includeLegacyFiles();

        if (is_null($resDBHandle)) {
            $resDBHandle = get_named_connection_with_db('userdata');
        }

        $resResults = PrimitivesQueryOrExit(
            $strQuery,
            $resDBHandle,
            'MAAF_CProductComponent_PackOf5Mailboxes class',
            false
        );

        if (!$resResults) {
            throw new Exception("Error during db query $strQuery with message: " . mysql_error());
        }

        return PrimitivesResultGet($resResults);
    } // end of method doQuery()


    /**
     * Component status change logger.
     *
     * <AUTHOR> Bargiel <<EMAIL>>
     *
     * @param string $strStatus Component status
     *
     * @return void
     */
    protected function logStatusChange($strStatus)
    {
        $objEventLogger = $this->prvGetEventLogger();

        switch (strtoupper($strStatus)) {
            case 'ACTIVE':
                $objEventLogger->logStatusChange('MaafWebMail' . $this->strStatusEventRaiser . 'Activation');
                break;
            case 'DEACTIVE':
                $objEventLogger->logStatusChange('MaafWebMail' . $this->strStatusEventRaiser . 'Deactivation');
                break;
            case 'DESTROYED':
                $objEventLogger->logStatusChange('MaafWebMail' . $this->strStatusEventRaiser . 'Destruction');
            default;
                break;
        }
    } // end of method logStatusChange()

} // end of class MAAF_CProductComponent_Mailboxes
