<?php
    // Time Formats API

    /////////////////////////////////////////////////
    // Function   SmartDate
    // Purpose    Returns the date in a smart format so that humans can read it easily
    // Arguments  strTimestamp
    // Returns    Smart formatted date, ie
    //                  Today @ hh:mm am/pm
    //                  Yesterday @ hh:mm am/pm
    //                  Date @ hh:mm am/pm
    /////////////////////////////////////////////////
    function SmartDate($strTimestamp)
    {
        $intYear    = substr($strTimestamp, 0, 4);
        $intMonth   = substr($strTimestamp, 4, 2);
        $intDay     = substr($strTimestamp, 6, 2);
        $intHour    = substr($strTimestamp, 8, 2);
        $intMinute  = substr($strTimestamp, 10, 2);
        $intSeconds = substr($strTimestamp, 12, 2);

        $strTime = date('H:i', mktime($intHour,$intMinute,$intSeconds,$intMonth,$intDay,$intYear));

        // As we want to find the actual whole days, we need to be a bit cleverer with this
        $intSecondsToday     = date('U', mktime(23, 59, 59, date('n'), date('j'), date('Y')));
        $intSecondsTimestamp = date('U', mktime(0, 0, 1, $intMonth, $intDay, $intYear));

        $intDaysDifference = DaysBetweenTimestamps($intSecondsTimestamp, $intSecondsToday);

        $strDay = date('D jS Y', mktime($intHour, $intMinute, $intSeconds, $intMonth, $intDay, $intYear));
        if($intDaysDifference == 0)
        {
            $strDay = 'Today';
        }
        elseif($intDaysDifference == 1)
        {
            $strDay = 'Yesterday';
        }

        return $strDay . ' @ '. $strTime;
    }

    /**
     * function DateIsBetween
     *
     * Function to check if timestamp $uxtNeedle is between $uxtFrom and $uxtTo
     * If $uxtTo parameter is not passed we do not check this param
     *
     * @param $uxtNeedle unix time stamp - a date to check
     * @param $uxtFrom   unix time stamp - a start date
     * @param $uxtTo     unix time stamp - an end date
     * @return boolean - true if  $uxtNeedle is between $uxtFrom and $uxtTo
     */

    function DateIsBetween($uxtNeedle, $uxtFrom, $uxtTo = 0)
    {
        //if 'from date' is less than needle and 'to_date' is grater than needle return true
        if ($uxtFrom <= $uxtNeedle && (0 == $uxtTo || $uxtTo >= $uxtNeedle)){
            return true;
        }
        else{
            return false;
        }
    }

    /////////////////////////////////////////////////
    // Function   DaysBetweenTimestamps
    // Purpose    Calculate the number of days between two timestamps
    // Arguments  uxtFirstTimestamp
    //            uxtSecondTimestamp
    // Returns    number of days between timestamps
    /////////////////////////////////////////////////
    function DaysBetweenTimestamps($uxtSecondsFirstTimestamp, $uxtSecondsSecondTimestamp)
    {
        // We need to work with the lower value diffed from the higher value, or we get
        // DST issues
        if ($uxtSecondsFirstTimestamp > $uxtSecondsSecondTimestamp)
        {
            $uxtLowSecs = $uxtSecondsSecondTimestamp;
            $uxtHighSecs = $uxtSecondsFirstTimestamp + 1; // Add one second to get around last-second bug
        }
        else
        {
            $uxtLowSecs = $uxtSecondsFirstTimestamp;
            $uxtHighSecs = $uxtSecondsSecondTimestamp + 1; // Midnight to 23:59:59 is one day
        }

        // DST scalar
        // no change in DST = 0; Gained an hour = +1; lost an hour = -1;
        $intDSTChange = date('I', $uxtHighSecs) - date('I', $uxtLowSecs);

        // Get the difference and include adjustment for DST
        $intSeconds  = $uxtHighSecs - $uxtLowSecs + ($intDSTChange * 3600);

        // Now do the calculation
        $intDays = intval($intSeconds / (24 * 60 * 60));

        return $intDays;
    }

    /**
    * DSTCorrect
    *
    * Correct the timestamp if it is in DST
    *
    * @param    unixtime
    * @return    unixtime
    */
    function DSTCorrect($uxtTimestamp)
    {
        if (date('I', $uxtTimestamp))
        {
            return $uxtTimestamp - 3600;
        }
        else
        {
            return $uxtTimestamp;
        }
    }

    /**
    * DSTAdd
    *
    * If the timestamp is in DST add an hour. Useful when performing arithmetic
    * operations on a timestamp, such as modulus.
    *
    * @param    unixtime
    * @return    unixtime
    */
    function DSTAdd($uxtTimestamp)
    {
        if (date('I', $uxtTimestamp))
        {
            return $uxtTimestamp + 3600;
        }
        else
        {
            return $uxtTimestamp;
        }
    }

    /**
    * DSTChange
    *
    * Has there been a DST change between timestamp 1 and timestamp 2?
    *
    * @param    unixtime
    * @param    unixtime
    * @return    integer
    */
    function DSTChange($uxtTS1, $uxtTS2)
    {
        // Make sure we take the later TS from the earlier, or we will get an issue!
        if ($uxtTS1 > $uxtTS2)
        {
            $uxtFrTS = $uxtTS2;
            $uxtToTS = $uxtTS1;
        }
        else
        {
            $uxtFrTS = $uxtTS1;
            $uxtToTS = $uxtTS2;
        }

        $intDSTChange = date('I', $uxtFrTS) - date('I', $uxtToTS);

        return $intDSTChange;
    }


    /////////////////////////////////////////////////
    // Function   DaySeconds
    // Purpose    Calculate the number of seconds in a given day according to
    //            Timezone DST settings
    // Arguments  uxtTimestamp
    // Returns    number of seconds in the day specified by the timestamp
    /////////////////////////////////////////////////
    function DaySeconds($uxtTimestamp)
    {
        // Need beginning and end of day to see if there is a change in DST
        list($intYear, $intMonth, $intDay) = explode('-', date('Y-m-d', $uxtTimestamp));
        $uxtDayStart = mktime(0, 0, 0, $intMonth, $intDay, $intYear);
        $uxtDayEnd   = mktime(23, 59, 59, $intMonth, $intDay, $intYear);

        // DST scalar
        $intDSTChange = date('I', $uxtDayStart) - date('I', $uxtDayEnd);

        $intDaySeconds = (60 * 60 * 24) + ($intDSTChange * 3600);

        return $intDaySeconds;
    }

    /////////////////////////////////////////////////
    // Function  GetHTMLDateTimeRange
    // Purpose   To stop me from having to create another dropdown of date/time range
    // Arguments See below - all in int format
    // Returns   HTML Table
    /////////////////////////////////////////////////
    function GetHTMLDateTimeRange($error_message='',$selected_start_hour='',$selected_start_hour_quarter='',
                                  $selected_start_day='',$selected_start_month='',$selected_start_year='',
                                                                $selected_end_hour='',$selected_end_hour_quarter='',
                                  $selected_end_day='',$selected_end_month='',$selected_end_year='',
                                                                $start_year='1998')
    {
        //set up the template
        $template = new Template('/local/data/mis/database/application_apis/templates');
        $template->set_file('page', 'date_time_range.ihtml');
        $template->set_var('error',$error_message);

        //set the start hour
        $template->set_block('page', 'start_hour_block', 'start_hour_block_block');
        for($i=0;$i<=23;++$i)
        {
            if($selected_start_hour==$i)
            {
                $template->set_var('selected','selected');
            }
            else
            {
                $template->set_var('selected','');
            }
            $template->set_var('value',$i);
            $template->set_var('show_value',$i);
            $template->parse('start_hour_block_block','start_hour_block', true);
        }

        //set the start hour quarter
        if($selected_start_hour_quarter=='00')
        {
            $template->set_var('selected','selected');
        }
        else
        {
            $template->set_var('selected','');
        }

        $template->set_block('page', 'start_hour_quarter_block', 'start_hour_quarter_block_block');
        $template->set_var('value','00');
        $template->set_var('show_value','00');
        $template->parse('start_hour_quarter_block_block','start_hour_quarter_block', true);
        for($i=15;$i<=45;$i=$i+15)
        {
            if($selected_start_hour_quarter==$i)
            {
                $template->set_var('selected','selected');
            }
            else
            {
                $template->set_var('selected','');
            }
            $template->set_var('value',$i);
            $template->set_var('show_value',$i);
            $template->parse('start_hour_quarter_block_block','start_hour_quarter_block', true);
        }

        //set the start day
        $template->set_block('page', 'start_day_block', 'start_day_block_block');
        for($i=1;$i<=31;++$i)
        {
            if($selected_start_day==$i)
            {
                $template->set_var('selected','selected');
            }
            else
            {
                $template->set_var('selected','');
            }
            $template->set_var('value',$i);
            $template->set_var('show_value',$i);
            $template->parse('start_day_block_block','start_day_block', true);
        }

        //set the start month
        $template->set_block('page', 'start_month_block', 'start_month_block_block');
        for($i=1;$i<=12;++$i)
        {
            if($selected_start_month==$i)
            {
                $template->set_var('selected','selected');
            }
            else
            {
                $template->set_var('selected','');
            }
            $template->set_var('value',$i);
            $template->set_var('show_value',$i);
            $template->parse('start_month_block_block','start_month_block', true);
        }

        //set the start year
        $template->set_block('page', 'start_year_block', 'start_year_block_block');
        for($i=$start_year;$i<=date('Y');++$i)
        {
            if($selected_start_year==$i)
            {
                $template->set_var('selected','selected');
            }
            else
            {
                $template->set_var('selected','');
            }
            $template->set_var('value',$i);
            $template->set_var('show_value',$i);
            $template->parse('start_year_block_block','start_year_block', true);
        }

        //set the end hour
        $template->set_block('page', 'end_hour_block', 'end_hour_block_block');
        for($i=0;$i<=23;++$i)
        {
            if($selected_end_hour==$i)
            {
                $template->set_var('selected','selected');
            }
            else
            {
                $template->set_var('selected','');
            }
            $template->set_var('value',$i);
            $template->set_var('show_value',$i);
            $template->parse('end_hour_block_block','end_hour_block', true);
        }

        //set the end hour quarter
        if($selected_end_hour_quarter=='00')
        {
            $template->set_var('selected','selected');
        }
        else
        {
            $template->set_var('selected','');
        }

        $template->set_block('page', 'end_hour_quarter_block', 'end_hour_quarter_block_block');
        $template->set_var('value','00');
        $template->set_var('show_value','00');
        $template->parse('end_hour_quarter_block_block','end_hour_quarter_block', true);
        for($i=15;$i<=45;$i=$i+15)
        {
            if($selected_end_hour_quarter==$i)
            {
                $template->set_var('selected','selected');
            }
            else
            {
                $template->set_var('selected','');
            }
            $template->set_var('value',$i);
            $template->set_var('show_value',$i);
            $template->parse('end_hour_quarter_block_block','end_hour_quarter_block', true);
        }

        //set the end day
        $template->set_block('page', 'end_day_block', 'end_day_block_block');
        for($i=1;$i<=31;++$i)
        {
            if($selected_end_day==$i)
            {
                $template->set_var('selected','selected');
            }
            else
            {
                $template->set_var('selected','');
            }
            $template->set_var('value',$i);
            $template->set_var('show_value',$i);
            $template->parse('end_day_block_block','end_day_block', true);
        }

        //set the end month
        $template->set_block('page', 'end_month_block', 'end_month_block_block');
        for($i=1;$i<=12;++$i)
        {
            if($selected_end_month==$i)
            {
                $template->set_var('selected','selected');
            }
            else
            {
                $template->set_var('selected','');
            }
            $template->set_var('value',$i);
            $template->set_var('show_value',$i);
            $template->parse('end_month_block_block','end_month_block', true);
        }

        //set the end year
        $template->set_block('page', 'end_year_block', 'end_year_block_block');
        for($i=$start_year;$i<=date('Y');++$i)
        {
            if($selected_end_year==$i)
            {
                $template->set_var('selected','selected');
            }
            else
            {
                $template->set_var('selected','');
            }
            $template->set_var('value',$i);
            $template->set_var('show_value',$i);
            $template->parse('end_year_block_block','end_year_block', true);
        }

        //output and return
        $template->parse("output","page",true);
        return $template->varvals['output'];
    }

    /////////////////////////////////////////////////
    // Function  GetHTMLDateRange
    // Purpose   To stop me from having to create another dropdown of date range
    // Arguments See below - all in int format
    // Returns   HTML Table
    /////////////////////////////////////////////////
    function GetHTMLDateRange($error_message='',$selected_start_day='',$selected_start_month='',$selected_start_year='',
                              $selected_end_day='',$selected_end_month='',$selected_end_year='',$start_year='1998')
    {
        //set up the template
        $template = new Template('/local/data/mis/database/application_apis/templates');
        $template->set_file('page', 'date_range.ihtml');
        $template->set_var('error',$error_message);

        //set the start day
        $template->set_block('page', 'start_day_block', 'start_day_block_block');
        for($i=1;$i<=31;++$i)
        {
            if($selected_start_day==$i)
            {
                $template->set_var('selected','selected');
            }
            else
            {
                $template->set_var('selected','');
            }
            $template->set_var('value',$i);
            $template->set_var('show_value',$i);
            $template->parse('start_day_block_block','start_day_block', true);
        }

        //set the start month
        $template->set_block('page', 'start_month_block', 'start_month_block_block');
        for($i=1;$i<=12;++$i)
        {
            if($selected_start_month==$i)
            {
                $template->set_var('selected','selected');
            }
            else
            {
                $template->set_var('selected','');
            }
            $template->set_var('value',$i);
            $template->set_var('show_value',$i);
            $template->parse('start_month_block_block','start_month_block', true);
        }

        //set the start year
        $template->set_block('page', 'start_year_block', 'start_year_block_block');
        for($i=$start_year;$i<=date('Y');++$i)
        {
            if($selected_start_year==$i)
            {
                $template->set_var('selected','selected');
            }
            else
            {
                $template->set_var('selected','');
            }
            $template->set_var('value',$i);
            $template->set_var('show_value',$i);
            $template->parse('start_year_block_block','start_year_block', true);
        }

        //set the end day
        $template->set_block('page', 'end_day_block', 'end_day_block_block');
        for($i=1;$i<=31;++$i)
        {
            if($selected_end_day==$i)
            {
                $template->set_var('selected','selected');
            }
            else
            {
                $template->set_var('selected','');
            }
            $template->set_var('value',$i);
            $template->set_var('show_value',$i);
            $template->parse('end_day_block_block','end_day_block', true);
        }

        //set the end month
        $template->set_block('page', 'end_month_block', 'end_month_block_block');
        for($i=1;$i<=12;++$i)
        {
            if($selected_end_month==$i)
            {
                $template->set_var('selected','selected');
            }
            else
            {
                $template->set_var('selected','');
            }
            $template->set_var('value',$i);
            $template->set_var('show_value',$i);
            $template->parse('end_month_block_block','end_month_block', true);
        }

        //set the end year
        $template->set_block('page', 'end_year_block', 'end_year_block_block');
        for($i=$start_year;$i<=date('Y');++$i)
        {
            if($selected_end_year==$i)
            {
                $template->set_var('selected','selected');
            }
            else
            {
                $template->set_var('selected','');
            }
            $template->set_var('value',$i);
            $template->set_var('show_value',$i);
            $template->parse('end_year_block_block','end_year_block', true);
        }

        //output and return
        $template->parse("output","page",true);
        return $template->varvals['output'];
    }

    /////////////////////////////////////////////////
    // Function  GetHTMLDate
    // Purpose   To stop me from having to create another dropdown of date range
    // Arguments See below - all in int format
    // Returns   HTML Table
    /////////////////////////////////////////////////
    function GetHTMLDate($error_message='',$selected_start_day='',$selected_start_month='',$selected_start_year='', $start_year='1998')
    {
        //set up the template
        $template = new Template('/local/data/mis/database/application_apis/templates');
        $template->set_file('page', 'date.ihtml');
        $template->set_var('error',$error_message);

        //set the start day
        $template->set_block('page', 'start_day_block', 'start_day_block_block');
        for($i=1;$i<=31;++$i)
        {
            if($selected_start_day==$i)
            {
                $template->set_var('selected','selected');
            }
            else
            {
                $template->set_var('selected','');
            }
            $template->set_var('value',$i);
            $template->set_var('show_value',$i);
            $template->parse('start_day_block_block','start_day_block', true);
        }

        //set the start month
        $template->set_block('page', 'start_month_block', 'start_month_block_block');
        for($i=1;$i<=12;++$i)
        {
            if($selected_start_month==$i)
            {
                $template->set_var('selected','selected');
            }
            else
            {
                $template->set_var('selected','');
            }
            $template->set_var('value',$i);
            $template->set_var('show_value',$i);
            $template->parse('start_month_block_block','start_month_block', true);
        }

        //set the start year
        $template->set_block('page', 'start_year_block', 'start_year_block_block');
        for($i=$start_year;$i<=date('Y');++$i)
        {
            if($selected_start_year==$i)
            {
                $template->set_var('selected','selected');
            }
            else
            {
                $template->set_var('selected','');
            }
            $template->set_var('value',$i);
            $template->set_var('show_value',$i);
            $template->parse('start_year_block_block','start_year_block', true);
        }

        //output and return
        $template->parse("output","page",true);
        return $template->varvals['output'];
    }

?>
