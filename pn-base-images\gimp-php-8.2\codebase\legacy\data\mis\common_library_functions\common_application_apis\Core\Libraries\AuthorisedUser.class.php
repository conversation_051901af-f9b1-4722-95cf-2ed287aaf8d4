<?php
/**
 * Authorised User base class
 *
 * @package    Core
 * @subpackage AuthorisedUser
 * <AUTHOR> <<EMAIL>>
 * @link       http://www.plus.net/
 */
/**
 * Authorised User base class
 *
 * @package    Core
 * @subpackage AuthorisedUser
 * <AUTHOR> <<EMAIL>>
 * @link       http://www.plus.net/
 */
class Core_AuthorisedUser
{
    /**
     * @access private
     * @var Core_AuthorisedUserDao
     */
    private $_objDao = null;

    /**
     * Default constructor
     *
     * @param int    $authorisedUserId   Authorised User Id
     * @param string $strTransactionName Transaction name
     *
     * @return void
     */
    public function __construct($authorisedUserId = null, $strTransactionName = Db_Manager::DEFAULT_TRANSACTION)
    {
        if (isset($authorisedUserId)) {
            $this->setDao(Core_AuthorisedUserDao::get($authorisedUserId, $strTransactionName));
        }
    }

    /**
     * Takes care of all calls to setters and getters of DAO object
     *
     * @param string $method Method name
     * @param array  $params Params passed to method called
     *
     * @throws Exception
     * @return mixed
     */
    public function __call($method, $params)
    {
        if (preg_match('/^(get|set)/', $method)) {
            return call_user_func_array(array($this->_objDao, $method), $params);
        }

        throw new BadMethodCallException('Method does not exist: '.get_class($this).'::'.$method);
    }

    /**
     * Calls write method of DAO object
     *
     * @return bool
     */
    public function write()
    {
        return $this->_objDao->write();
    }

    /**
     * Sets DAO
     *
     * @param Core_AuthorisedUserDao $authorisedUserDao Authorised User DAO
     *
     * @return void
     */
    public function setDao(Core_AuthorisedUserDao $authorisedUserDao)
    {
        $this->_objDao = $authorisedUserDao;
    }
}
