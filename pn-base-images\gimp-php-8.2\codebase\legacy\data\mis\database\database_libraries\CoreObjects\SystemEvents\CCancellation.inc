<?php
/**
 * phpcs DOCBLOCK header
 *
 * @package LegacyCodebase
 * <AUTHOR> <<EMAIL>>
 */

require_once('/local/data/mis/database/database_libraries/CoreObjects/SystemEvents/CScheduledEvent.inc');

/**
 * THe CCancellation class
 *
 * @package LegacyCodebase
 * <AUTHOR> <<EMAIL>>
 */
class CCancellation extends CScheduledEvent
{
    /**
     * Cancellation ID
     *
     * @var integer
     */
    private $m_intCancellationID = 0;

    /**
     * Component ID
     *
     * @var integer
     */
    private $m_intComponentID = 0;

    /**
     * Contructor for the CCancellation class
     *
     * @param integer $intCancellationID the component ID
     *
     * @return boolean
     */
    public function CCancellation($intCancellationID = 0)
    {
        $intCancellationID = (int)$intCancellationID;

        if ($intCancellationID < 1) {
            // No action required
            // DJM2013: as this is a constructor, the return value won't be picked up by anything!
            // Ideally, an exception should be thrown, but that's outside the scope of the current work...
            return true;
        }

        $query = "SELECT c.intCancellationID, c.intComponentID, c.intScheduledEventID" .
            " FROM dbSystemEvents.tblCancellation c WHERE c.intCancellationID = $intCancellationID";

        $conn = get_named_connection_with_db('systemEvents');
        $refResult = PrimitivesQueryOrExit($query, $conn, 'Fetch details for a cancellation event');
        $arrEvent = PrimitivesResultsAsArrayGet($refResult);

        if (!$arrEvent) {
            return false;
        }

        $arrEvent = $arrEvent[0];
        if (!isset($arrEvent['intCancellationID']) || $arrEvent['intCancellationID'] != $intCancellationID) {
            return false;
        }

        $this->m_intCancellationID = $arrEvent['intCancellationID'];
        $this->m_intComponentID = $arrEvent['intComponentID'];
        $this->m_intScheduledEventID = $arrEvent['intScheduledEventID'];

        // Call Parent constructor
        return $this->CScheduledEvent($this->m_intScheduledEventID);
    }

    /**
     * Return the Component ID
     *
     * @return int
     */
    public function getComponentID()
    {
        return $this->m_intComponentID;
    }

    /**
     * Create a new CCancellation object.  Returns false if the object cannot be created
     *
     * @param int  $intComponentID   The id of the component to be cancelled
     * @param uxt  $uxtDateDue       Unix timestamp of the date to cancel on
     * @param bool $bolOnContractEnd Perform immediately, or on contract end (defaults to wait for end of contract)
     * @param bool $bolAdjustDate    indicates that the date should be adjusted
     *
     * @return obj
     */
    public static function create(
        $intComponentID,
        $uxtDateDue = 0,
        $bolOnContractEnd = true,
        $bolAdjustDate = false
    ) {
        $intComponentID = (int)$intComponentID;
        if ($intComponentID < 1) {
            return false;
        }

        $uxtDateDue = (!empty($uxtDateDue) ? $uxtDateDue : time());

        $objNewEvent = new CCancellation();
        $objNewEvent->m_intComponentID = $intComponentID;
        $objNewEvent->m_uxtDue = $uxtDateDue;
        $objNewEvent->m_bolOnContractEnd = $bolOnContractEnd;
        $objNewEvent->m_intEventTypeID = CScheduledEvent::getEventTypeID('PRODUCT_CANCELLATION');
        $objNewEvent->m_bolAdjustDate = $bolAdjustDate;

        $bolSuccess = $objNewEvent->insertEvent();
        if (!$bolSuccess) {
            return false;
        }

        $bolSuccess = $objNewEvent->insertEventDetails();
        if (!$bolSuccess) {
            return false;
        }

        return $objNewEvent;
    }

    /**
     * Get Existing events for a given component ID.  If the component ID is not set, all events will be returned
     *
     * @param int   $intComponentID           The component ID
     * @param bool  $bolIncludePending        Include Events which are not yet due
     * @param bool  $bolIncludeCancelled      Include Events which are cancelled
     * @param bool  $bolIncludeCompleted      Include Events which are completed
     * @param int   $intLimit                 The maximum number of records to return (-1: unlimited)
     * @param array $serviceIdList            A list of service-ids which the query should filter by
     * @param bool  $excludeDestroyedServices Shall we exclude services with status 'destroyed'
     *
     * @return array
     */
    public static function getExistingEvents(
        $intComponentID,
        $bolIncludePending = false,
        $bolIncludeCancelled = false,
        $bolIncludeCompleted = false,
        $intLimit = -1,
        $serviceIdList = null,
        $excludeDestroyedServices = false
    ) {
        // Generate the optional WHERE clauses...
        $whereClauseList = array();

        if ($excludeDestroyedServices) {
            $whereClauseList[] = 's.status != \'destroyed\'';
        }

        if (!empty($intComponentID)) {
            $intComponentID = (int)$intComponentID;
            $whereClauseList[] = "c.component_id = $intComponentID";
        }

        if (!$bolIncludePending) {
            $whereClauseList[] = 'se.dteDue <= NOW()';
        }

        if (!$bolIncludeCancelled) {
            $whereClauseList[] = 'se.dtmCancelled IS NULL';
        }

        if (!$bolIncludeCompleted) {
            $whereClauseList[] = 'se.dtmCompleted IS NULL';
        }

        if (!empty($serviceIdList)) {
            $whereClauseList[] = 'c.service_id IN (' . implode(',', $serviceIdList) . ')';
        }

        if (!empty($whereClauseList)) {
            $whereClause = 'WHERE ' . implode(' AND ', $whereClauseList);
        }

        $intLimit = (int)$intLimit;
        if ($intLimit < -1) {
            $intLimit = 0;
        }

        $strLimitClause = ($intLimit != -1 ? "LIMIT $intLimit" : '');

        $query = <<<EOQ
SELECT
    cl.intCancellationID                                                AS intCancellationID,
    cl.intComponentID,
    c.service_id                                                        AS intServiceID,
    sc.name                                                             AS strServiceComponentName,
    scpt.vchHandle                                                      AS strServiceComponentType,
    s.status                                                            AS strServiceStatus,
    s.type                                                              AS intServiceType,
    s.isp                                                               AS strServiceIsp,
    IF((se.dtmCreated IS NULL), 0, UNIX_TIMESTAMP(se.dtmCreated))       AS uxtCreated,
    IF((se.dteDue IS NULL), 0, UNIX_TIMESTAMP(se.dteDue))               AS uxtDue,
    se.bolOnContractEnd,
    IF((se.dtmCompleted IS NULL), 0, UNIX_TIMESTAMP(se.dtmCompleted))   AS uxtCompleted,
    IF((se.dtmCancelled IS NULL), 0, UNIX_TIMESTAMP(se.dtmCancelled))   AS uxtCancelled,
    se.bolAdjustDate
FROM
    tblCancellation                                     AS cl
    INNER JOIN tblScheduledEvent                        AS se   ON se.intScheduledEventID = cl.intScheduledEventID
    INNER JOIN userdata.components                      AS c    ON c.component_id = cl.intComponentID
    INNER JOIN userdata.services                        AS s    ON s.service_id = c.service_id
    INNER JOIN products.service_components              AS sc   ON sc.service_component_id = c.component_type_id
    LEFT JOIN products.tblServiceComponentProduct       AS scp
        ON scp.intServiceComponentID = sc.service_component_id
    LEFT JOIN products.tblServiceComponentProductType   AS scpt
        ON scpt.intServiceComponentProductTypeID = scp.intServiceComponentProductTypeID
$whereClause
ORDER BY
    se.dteDue, c.service_id DESC
$strLimitClause
EOQ;

        $conn = get_named_connection_with_db('systemEvents');
        $resResult = PrimitivesQueryOrExit($query, $conn, 'Fetch details of outstanding component cancellations');
        $arrEvents = PrimitivesResultsAsArrayGet($resResult);

        return $arrEvents;
    }

    /**
     * Get ALL Existing events, ie not limited by component id.  Wrapper to getExistingEvents()
     *
     * @param int   $intLimit                 The maximum number of records to return (-1: unlimited)
     * @param array $serviceIdList            A list of service-ids which the query should filter by
     * @param bool  $excludeDestroyedServices Shall we exclude services with status 'destroyed'
     *
     * @return array
     */
    public static function getAllExistingEvents(
        $intLimit = -1,
        $serviceIdList = null,
        $excludeDestroyedServices = false
    ) {
        return CCancellation::getExistingEvents(
            '',
            false,
            false,
            false,
            $intLimit,
            $serviceIdList,
            $excludeDestroyedServices
        );
    }

    /**
     * Inserts supplimentary details for this particular event type
     *
     * @return bool
     */
    public function insertEventDetails()
    {
        if (!CScheduledEvent::isValidScheduledEventID($this->m_intScheduledEventID)) {
            return false;
        }

        $query = <<<EOQ
INSERT INTO
    dbSystemEvents.tblCancellation
SET
    intScheduledEventID = {$this->m_intScheduledEventID},
    intComponentID = {$this->m_intComponentID}
EOQ;

        $conn = get_named_connection_with_db('systemEvents');
        PrimitivesQueryOrExit($query, $conn, 'Create a new scheduled cancellation event');

        $this->m_intCancellationID = PrimitivesInsertIdGet($conn);

        return ($this->m_intCancellationID > 0) ? true : false;
    }

    /**
     * Cancel the Cancellation Event
     *
     * @return boolean success
     */
    public function cancel()
    {
        //Nothing specific to do for this event type, so just cancel the event
        return $this->cancelEvent();
    }

    /**
     * The function used to return the cease type and write off charge
     * with respect to the cancellation reason for the cancellation id
     *
     * @param int $intCancellationID cancellation id from tblCancellation
     *
     * @return array
     */
    public static function getCeaseTypeWriteOffCharge($intCancellationID)
    {
        $response = array();

        $query = "SELECT tct.vchHandle AS ceaseType,
                    wf.vchHandle AS writeOff
                    FROM dbSystemEvents.tblCancellation tc
                    LEFT JOIN dbSystemEvents.tblCeaseTypeMapping tct
                    ON tct.intCancellationReasonId = tc.intCancellationReasonID
                    LEFT JOIN dbSystemEvents.tblWriteOffReasonMapping wf
                    ON wf.intCancellationReasonId = tc.intCancellationReasonID
                    WHERE tc.intCancellationID = %u";
        $query = sprintf($query, (int)$intCancellationID);

        $conn = get_named_connection_with_db('systemEvents');
        $resResult = PrimitivesQueryOrExit($query, $conn, 'Fetch the cease type corresponding to cancellation reason');
        $arrEvents = PrimitivesResultsAsArrayGet($resResult);

        if (!empty($arrEvents)) {
            $arrEvents = $arrEvents[0];
            $response['ceaseType'] = $arrEvents['ceaseType'];
            $response['writeOff'] = $arrEvents['writeOff'];
        }

        return $response;
    }
}
