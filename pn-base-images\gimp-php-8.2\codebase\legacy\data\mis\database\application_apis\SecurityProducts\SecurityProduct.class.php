<?php

	/**
	* Access library for security product.
	* Contains component configurator and component class
	*
	* @see CBullGuardComponent.inc
	*
	* @package Product component
	* @subpackage Security product
	*
	* <AUTHOR> <<EMAIL>>
	* @version    $Id: SecurityProduct.class.php,v 1.1.2.4 2007/11/07 11:29:14 akurylowicz Exp $
	*/

	require_once('/local/data/mis/database/database_libraries/components/CProduct.inc');

	require_once(dirname(__FILE__) . '/BullGuardProductComponent.class.php');
	require_once(dirname(__FILE__) . '/BullGuardTrialComponent.class.php');
	require_once(dirname(__FILE__) . '/BullGuardManager.class.php');

	class CSecurityProduct extends CProduct
	{

		/**
		 * Constructor, create instance of product component
		 *
		 * @param unknown_type $intComponentId
		 * @return unknown
		 */
		public function __construct($intComponentId)
		{
			return $this->CProduct($intComponentId);
		}

		/**
		 * Check is given component id a security product component
		 *
		 * @param unknown_type $intComponentId
		 * @return unknown
		 */
		public function isSecurityProduct($intComponentId=0)
		{
			if(false == is_numeric($intComponentId) || $intComponentId < 1) {

				return false;
			}

			$dbhConn = get_named_connection_with_db('userdata');

			$strQuery ="SELECT count(*) as intCountSecurityComponents " .
			             "FROM userdata.components c " .
			       "INNER JOIN products.tblServiceComponentProduct scp " .
			               "ON c.component_type_id = scp.intServiceComponentID " .
			       "INNER JOIN products.tblServiceComponentProductType scpt " .
			               "ON scp.intServiceComponentProductTypeID = scpt.intServiceComponentProductTypeID " .
			            "WHERE c.component_id ='{$intComponentId}' " .
			              "AND scpt.vchHandle = 'SECURITY'";

			$refResult = PrimitivesQueryOrExit($strQuery, $dbhConn);
			$intCount  = PrimitivesResultGet($refResult,'intCountSecurityComponents');

			return ($intCount > 0) ? true : false;
		}

		/**
		 * Get a list of the security product types
		 *
		 * @return unknown
		 */
		public function getSecurityComponentTypes()
		{
			return $this->getProductComponentTypes('SECURITY');
		}

		/**
		 * Configurators
		 */

		/**
		 * Refreshes the current state of the component
		 *
		 * @return bol
		 */
		public function refreshCurrentState()
		{
			switch($this->m_strStatus)
			{
				case 'queued-activate':
				case 'queued-reactivate':
				return $this->enable();
				break;

				case 'queued-deactivate':
				return $this->disable();
				break;

				case 'queued-destroy':
				return $this->destroy();
				break;

				case 'active':
				case 'deactive':
				break;

				default:
				break;
			}

			return 0;
		}

		/**
		 * Configure product components
		 *
		 * @return bool
		 */
		public function configure()
		{
			if (false == $this->canBeEnabled()) {

				return false;
			}

			$utxNextInvoice = false;

			foreach($this->getProductComponents() as $objComponent)
			{
				// Try to configure all Bull Guard components, if configuration record already exist configure() return false
				if (true == ($objComponent instanceof CBullGuardComponent)) {

					$objComponent->configure();
				}

				// Get next invoice date from subscription component
				if (PRODUCT_COMPONENT_SUBSCRIPTION == $objComponent->getProductComponentID()) {

					$utxNextInvoice = $objComponent->getNextInvoiceDate();
				}
			}

			foreach($this->getProductComponents() as $objComponent)
			{
				if ('ACTIVE' == $objComponent->getStatus()) {

					// Set expiry date the same as next invoice date
					if (true == ($objComponent instanceof CBullGuardProductComponent) && false !== $utxNextInvoice) {

						$objComponent->setExpiryDate($utxNextInvoice);
					}

					// Set expiry date to one month after component instalation
					if (true == ($objComponent instanceof CBullGuardTrialComponent) && false !== $objComponent->getInstalationDate()) {

						$utxExpiry = mktime(
							23,
							59,
							59,
							date('m', $objComponent->getInstalationDate()),
							date('d', $objComponent->getInstalationDate()) + CBullGuardTrialComponent::TRIAL_PERIOD_DAYS,
							date('Y', $objComponent->getInstalationDate())
							);

						$objComponent->setExpiryDate($utxExpiry);
					}
				}
			}

			$this->SetStatus('queued-activate');
			$this->refreshInstance($this->getComponentID());

			if ('queued-activate' != strtolower($this->getStatus())) {

				return false;
			}

			$this->logStatusChange('QUEUED_ACTIVATE');

			return true;
		}

		/**
		 * Component configurator function. Enables the component.
		 *
		 * @return unknown
		 */
		public function enable($arrArgs = array())
		{
			if (false == $this->canBeEnabled()) {

				return false;
			}

			// Enable all components
			if(false == $this->enableProductComponents($arrArgs)) {

				return false;
			}

			$this->SetStatus('active');
			$this->refreshInstance($this->getComponentID());

			if ('active' != strtolower($this->getStatus())) {

				return false;
			}

			$this->logStatusChange('ACTIVE');

			return true;
		}

		/**
		 * Component configurator function. Disables the component.
		 *
		 * @return unknown
		 */
		function disable()
		{
			if (false == $this->canBeDisabled()) {

				return -1;
			}

			$this->prvSetStatus('queued-deactivate');

			if(false == $this->disableProductComponents()) {

				return -1;
			}

			$this->prvSetStatus('deactive');
			$this->logStatusChange('deactive');

			return 0;
		}

		/**
		 * Set product and components into queue destroy state
		 *
		 * @return bool
		 */
		function queueDestroy()
		{
			$bolError = false;

			foreach(array_keys($this->getProductComponents()) as $intID)
			{

				$objProductComponent = $this->m_arrProductComponents[$intID];

				if(false == $objProductComponent->prvSetStatus('QUEUED_DESTROY')) {

					$bolError = true;
				}

				$this->m_arrProductComponents[$intID] = $objProductComponent;
			}

			if($bolError) {

				return false;
			}

			$this->prvSetStatus('queued-destroy');
			$this->refreshInstance($this->getComponentID());

			return true;
		}

		/**
		 * Component configurator function. Destroys the component.
		 *
		 * @return unknown
		 */
		function destroy()
		{
			$this->prvSetStatus('queued-destroy');

			if(false == $this->destroyProductComponents()) {

				return -1;
			}

			$this->prvSetStatus('destroyed');

			$this->refreshInstance($this->getComponentID());

			$this->logStatusChange('DESTROYED');

			return 0;
		}

		/**
		 * Log component status change event
		 *
		 * @param unknown_type $strStatus
		 */
		public function logStatusChange($strStatus)
		{
			$objEventLogger = $this->prvGetEventLogger();

			switch ($strStatus)
			{
				case 'SIGNUP':
				$objEventLogger->logStatusChange('SecuritySignup');
				break;

				case 'QUEUED_ACTIVATE':
				$objEventLogger->logStatusChange('SecurityConfiguration');
				break;

				case 'ACTIVE':
				$objEventLogger->logStatusChange('SecurityActivation');
				break;

				case 'DEACTIVE':
				$objEventLogger->logStatusChange('SecurityDeactivation');
				break;

				case 'DESTROYED':
				$objEventLogger->logStatusChange('SecurityDestruction');
				break;

				default;
				break;
			}
		}



		/**
		 * Acquire basic summary information about this product.
		 * To be used where only a basic summary is required, like on the customer
		 * details page in Workplace.
		 */

		public function getExtendedSummaryInfo()
		{
			// the various calls made in this method are inspired from
			// BullGuardManager::displayPage()

			// I'd prefer not to use the manager class to perform this next action, but I'm relying on it
			// knowing what statuses of product components to return back to me. (alternative to this is to
			// ditch using the manager class and use oldskool product component instance-accessing methods,
			// along with an array of suitable statuses)

			$objManager = new BullGuardManager();
			$objManager->populateDataByComponent($this->getComponentID());

			$objComponent = $objManager->getComponent();

			if (!is_object($objComponent)) {

				return FALSE;
			}


			$arrData = array(
				'strComponentStatus' => array(ucfirst($this->getStatus())),
				'intStatus'          => array($objComponent->getState()),
				'strActivated'       => array($objComponent->getActivationDate()),
				'strInstalled'       => array($objComponent->getInstalationDate()),
				'strExpiry'          => array($objComponent->getExpiryDate())
			);

			unset($objComponent);
			unset($objManager);

			return $arrData;
		}
	}
