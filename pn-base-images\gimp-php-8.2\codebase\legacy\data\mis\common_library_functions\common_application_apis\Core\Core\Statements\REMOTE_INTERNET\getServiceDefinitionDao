server: itw
role: slave
rows: single
statement:

SELECT
		sd.service_definition_id,
		sd.intProductVariantId,
		name,
		sd.isp,
		minimum_charge,
		date_created,
		requires,
		initial_charge,
		type,
		password_visible_to_support,
		end_date,
		signup_via_portal,
		bt_product_id,
		pv.vchHandle AS strProductVariant,
		pf.vchHandle AS strProductFamily,
		IF (ISNULL(adsl_product_id), 0, 1) AS bolAdsl
FROM
		dbRemoteInternet.service_definitions sd
LEFT JOIN
		dbRemoteInternet.tblProductVariant pv ON (pv.intProductVariantId = sd.intProductVariantId)
LEFT JOIN
		dbRemoteInternet.tblProductFamily pf ON (pf.intProductFamilyId = pv.intProductFamilyId)
LEFT JOIN
		dbRemoteInternet.adsl_product ap ON (ap.service_definition_id = sd.service_definition_id)
WHERE
		sd.service_definition_id = :service_definition_id
