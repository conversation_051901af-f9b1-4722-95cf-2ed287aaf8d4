<?php

	// Do addslashes on every element of an array.
	function array_addslashes(&$the_array)
	{
		foreach ($the_array as $key => $value)
		{
			$the_array[$key] = addslashes($value);
		}
	}


	function find_val_in_array($thearray, $val, $field='')
	{
		$found = -1;

		while (list($index, $value) = each($thearray))
		{
			if (strlen($field) == 0)
			{
				$compareval = $value;
			}
			else
			{
				// Check if $value is an array or an object
				if (gettype($value) == 'object')
				{
					$compareval = $value->$field;
				}
				else
				{
					$compareval = $value["$field"];
				}
			}

			if ($compareval == $val)
			{
				$found = $index;
			}
		}

		return $found;
	}


	function renumber_array($thearray)
	{
		$newarray = array();

		while (list($i, $val) = each($thearray))
		{
			$newarray[] = $val;
		}

		return $newarray;
	}


	function invert_array($thearray)
	{
		$newarray = array();

		while (list ($key, $val) = each ($thearray))
		{
			$newarray[] = $key;
		}

		return $newarray;
	}

	function concatenate_arrays($array1, $array2)
	{
		$newarray = array();

		if (count($array1) > 0)
		{
			while (list($i, $val) = each($array1))
			{

				$newarray[] = $val;
			}
		}

		if (count($array2) > 0)
		{
			while (list($i, $val) = each($array2))
			{
				$newarray[] = $val;
			}
		}

		return $newarray;
	}


	// Sort an associative array by a field
	function qsort($array, $field, $direction)
	{
		qsort_do($array, 0, count($array) - 1, $field, $direction);
	}

	// Internal to qsort
	function qsort_do(&$array, $left, $right, $field, $direction)
	{
		if ($left < $right)
		{
			qsort_partition($array, $left, $right, $leftp, $rightp, $field, $direction);
			qsort_do($array, $left,   $leftp, $field, $direction);
			qsort_do($array, $rightp, $right, $field, $direction);
		}
	}

	// Internal to qsort (default comparison function)
	function qsort_compare($val1, $val2, $direction)
	{
		if ($val1 >= $val2)
		{
			return $direction == 'ASC' ? 1 : 0;
		}

		return $direction == 'ASC' ? 0 : 1;
	}

	// Internal to qsort
	function qsort_partition(&$array, $left, $right, $leftp, $rightp, $field, $direction)
	{
		$i = $left + 1;
		$j = $left + 1;

		while ($j <= $right)
		{
			if (qsort_compare($array[$j][$field], $array[$left][$field], $direction))
			{
				$tmp = $array[$j];
				$array[$j] = $array[$i];
				$array[$i] = $tmp;
				$i++;
			}

			$j++;
		}

		$x = $array[$left];
		$array[$left] = $array[$i - 1];
		$array[$i - 1] = $x;

		$leftp  = $i - 2;
		$rightp = $i;
	}

?>