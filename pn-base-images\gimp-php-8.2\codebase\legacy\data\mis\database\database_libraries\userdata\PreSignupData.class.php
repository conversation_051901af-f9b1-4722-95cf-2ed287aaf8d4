<?php
/**
 * PreSignupData
 *
 * Business logic wrapper for data stored in presignup data tables.
 *
 * @package    Signup
 * @subpackage PreSignup
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2010 PlusNet
 * @since      File available since 2010-03-12
 */
/**
 * PreSignupData class
 *
 * @package    Signup
 * @subpackage PreSignup
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2010 PlusNet
 */
class PreSignupData
{
    /**
     * @see userdata.tblLineType for details
     * @var int
     */
    private $_lineType;

    /**
     * @var bool
     */
    private $_hasBtSocket;

    /**
     * @var bool
     */
    private $_canRetainCLi;

    /**
     * @var int
     */
    private $_stoppedLinesNo;

    /**
     * @var int
     */
    private $_workingLinesNo;

    /**
     * @var bool
     */
    private $_takeOverLine;

    /**
     * @var bool
     */
    private $_validPreSignupData;

    /**
     * Default constructor
     *
     * @param array $presignupData PreSignupData array
     */
    public function __construct(array $presignupData)
    {
        if (!$this->validate($presignupData)) {

            return;
        }

        if (isset($presignupData['intLineTypeId'])) {

            $this->_lineType = $presignupData['intLineTypeId'];
        }
        if (isset($presignupData['bolBtSocket'])) {

            $this->_hasBtSocket = $presignupData['bolBtSocket'];
        }
        if (isset($presignupData['bolCanRetainCli'])) {

            $this->_canRetainCLi = $presignupData['bolCanRetainCli'];
        }
        if (isset($presignupData['intStoppedLinesNumber'])) {

            $this->_stoppedLinesNo = $presignupData['intStoppedLinesNumber'];
        }
        if (isset($presignupData['intWorkingLinesNumber'])) {

            $this->_workingLinesNo = $presignupData['intWorkingLinesNumber'];
        }
        if (isset($presignupData['bolWorkingLineTakeover'])) {

            $this->_takeOverLine = $presignupData['bolWorkingLineTakeover'];
        }
    }

    /**
     * Validates <i>$presignupData</i> to check whether it's empty.
     * It's only populated for PN Res customers
     *
     * The array should be retrived using {@link userdataGetPresignupData()}
     *
     * @param array $presignupData
     *
     * @return bool
     */
    protected function validate(array $presignupData)
    {
        $this->_validPreSignupData = (!empty($presignupData));

        return $this->_validPreSignupData;
    }

    /**
     * Check whether an automated WLR order can be placed.
     *
     * If presignupdata array is empty then order should be placed only
     * if customer has no CLI and WLR component has been created properly.
     *
     * @see CWlrProduct::doSignup()
     *
     * @return bool
     */
    public function canPlaceAutomatedOrder()
    {
        return (
            !$this->_validPreSignupData ||
            ($this->_validPreSignupData &&
                ($this->canRestartLineForCable() ||
                 $this->newProvideRequired() ||
                 $this->canRestartLineForNewProvide()
                )
            )
        );
    }

    /*
     * Check whether an engineer is required for new provide
     *
     * The following rule apply:
     *  - new provide required
     *  - customer confirmed he want a new provide and not take over the line
     *  - customer has a stopped line at the premises but has no BT socket
     *
     * @return bool
     */
    public function requiresEngineerForNewProvide()
    {
        return (
            $this->_validPreSignupData &&
            $this->newProvideRequired()
        );
    }

    /*
     * Check whether we want to take over the line.
     *
     * @return bool
     */
    public function wantToTakeOverLine()
    {
        return (
            $this->_validPreSignupData &&
            $this->_takeOverLine
        );
    }

    /*
     * Check whether a stopped line can be restarted
     *
     * The following rule apply:
     *  - cable provided line
     *  - at least one stopped line found
     *  - customer confirmed he has BT socket
     *  - CLI number can't be retained
     *
     * @return bool
     */
    protected function canRestartLineForCable()
    {
        return (
            $this->_lineType == 4 &&
            $this->_stoppedLinesNo > 0 &&
            $this->_hasBtSocket &&
            !$this->_canRetainCLi
        );
    }

    /*
     * Check whether we want to place a new provide order
     *
     * The following rule apply:
     *  - new provide required
     *  - at least one stopped line found at the premises,
     *    but customer confirmed he has no BT socket
     *  - no stopped and working lines found at the premises
     *  - customer doesn't want to take over a working line if found
     *
     * @return bool
     */
    protected function newProvideRequired()
    {
        return (
            ($this->_lineType == 3 &&
             $this->_stoppedLinesNo > 0 &&
             !$this->_hasBtSocket
            ) ||
            ($this->_lineType == 3 &&
             $this->_stoppedLinesNo == 0 &&
             $this->_workingLinesNo == 0
            ) ||
            $this->dontWantToTakeOverLine()
       );
    }

    /*
     * Check whether we want to restart a stopped line for a customer
     * who has no BT line (new provide order)
     *
     * The following rule apply:
     *  - new provide required
     *  - at least one stopped line found at the premises
     *  - customer confirmed he has BT socket
     *
     * @return bool
     */
    protected function canRestartLineForNewProvide()
    {
        return (
            $this->_lineType == 3 &&
            $this->_stoppedLinesNo > 0 &&
            $this->_hasBtSocket
       );
    }

    /*
     * Check whether we want to place a new provide order
     * without taking over the line.
     *
     * The following rule apply:
     *  - new provide required
     *  - no stopped lines found at the premises
     *  - at least one working line found at the premises
     *  - customer confirmed he want a new provide and not take over the line
     *
     * @return bool
     */
    protected function dontWantToTakeOverLine()
    {
        return (
            $this->_lineType == 3 &&
            $this->_stoppedLinesNo == 0 &&
            $this->_workingLinesNo > 0
            && !$this->_takeOverLine
       );
    }
}