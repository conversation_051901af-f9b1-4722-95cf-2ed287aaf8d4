<?php
	class Lib_ProgrammeTool extends Util_LibrarySplitter 
	{
		protected static $objInstance;

		protected function __construct()
		{
			$this->myName = __CLASS__;
			$this->myFile = __FILE__;
		}

		public static function singleton()
		{
			if (!isset(self::$objInstance))
			{
				$strClass = __CLASS__;
				self::$objInstance = new $strClass;
			}

			return self::$objInstance;
		} // public static function singleton()

	} // class Lib_ProgrammeTool extends Util_LibrarySplitter 
?>
