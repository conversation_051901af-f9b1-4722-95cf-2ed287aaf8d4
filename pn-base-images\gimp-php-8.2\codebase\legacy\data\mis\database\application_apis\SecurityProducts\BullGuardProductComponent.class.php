<?php
	require_once(dirname(__FILE__) . '/BullGuardComponent.class.php');

	class CBullGuardProductComponent extends CBullGuardComponent
	{
		public function setExpiryDate($utxDate = 0)
		{
			// Update component description
			if (true == parent::setExpiryDate($utxDate)) {

				$strDate = empty($utxDate) ? date('Y/m/d') : date('d/m/Y', $utxDate);
				$this->setComponentDesctiption("Active until {$strDate}");

				return true;
			}

			return false;
		}

		/**
		 * Override for interface methods
		 *
		 */

		/**
		 * Method to get the the monthly cost
		 *
		 * @return unknown
		 */
		function getCurrentCost()
		{
            // SALES-2340: Confirmed we don't charge for this one any more, also any previous call to $this->calculateCharges() would
            // have resulted in a fatal error as it didn't pass a parameter.
            return 0;
		}

		/**
		 * Method to get the the monthly cost
		 *
		 * @return unknown
		 */
		function calculateCharges()
		{
			return $this->getCurrentCost();
		}

		/**
		 * Function to add the schedule charges
		 *
		 * @return unknown
		 */
		function scheduleCharges($uxtStartDate)
		{
			return array();
		}

		/**
		 * Returns the details for scheduled payment
		 *
		 * @return unknown
		 */
		function getScheduledPaymentDetails()
		{
			return false;
		}

		/**
		 * Charges to be applied on a cancellation request
		 * Should be none
		 *
		 * @return unknown
		 */
		function calculateCancellationCharges()
		{
			return array();
		}

		/**
		 * Billing methods
		 *
		 */

		/**
		 * This function is called from billing, to renew component instance, need to be
		 * overriten due to set new subscription expity date
		 */
		public function renew()
		{
			$bolResult = parent::renew();

			$this->refreshInstance($this->getProductComponentInstance());

			$this->setExpiryDate($this->getNextInvoiceDate());

			return $bolResult;
		}
	}
