<?php
/**
 * Class for updating Market and Exchange Information
 *
 * @package    LegacyCodebase
 * @subpackage BBCR
 * <AUTHOR> <<EMAIL>>
 */

class Core_MarketExchangeUpdater
{
    /**
     * @var Core_Service
     */
    private $coreService;

    /**
     * @var AccountChange_ExchangeHelper
     */
    private $exchangeHelper;

    /**
     * @var Core_MarketExchangeHelper
     */
    private $marketExchangeHelper;

    /**
     * @var string the databasename
     */
    private $databaseName = 'userdata';

    /**
     * Constructor
     *
     * MarketExchangeUpdater constructor
     *
     * @param Core_Service                 $coreService          core service
     * @param AccountChange_ExchangeHelper $exchangeHelper       exchange helper
     * @param Core_MarketExchangeHelper    $marketExchangeHelper market exchange helper
     *
     */
    public function __construct($coreService, $exchangeHelper, $marketExchangeHelper)
    {
        $this->coreService = $coreService;
        $this->exchangeHelper = $exchangeHelper;
        $this->marketExchangeHelper = $marketExchangeHelper;
    }

    /**
     * Update the market and exchange data for a given service Id
     *
     * @param String $serviceId service id
     * @param String $marketId  market id
     *
     * @return void
     */
    public function updateMarketAreaAndExchangeData($serviceId, $marketId = null)
    {
        $lineCheckResult = $this->marketExchangeHelper->getLineCheckFromDatabase($serviceId);
        if ($this->marketExchangeHelper->isLineCheckValid($lineCheckResult)) {
            $this->marketExchangeHelper->updateCustomerExchangeData(
                $lineCheckResult->getExchangeId(),
                $serviceId,
                $this->exchangeHelper,
                $marketId
            );
        } else {
            $this->performNewLineCheckAndStoreResult();
        }
    }

    /**
     * Performs a new line check and stores the result in the line checker history
     *
     * @return void
     **/
    private function performNewLineCheckAndStoreResult()
    {
        $lineCheck = $this->getLineCheck($this->coreService);
        $this->writeLineCheckResultToDb($this->coreService, $lineCheck);
        Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);
    }

    /**
     * Returns line check result for the entered service
     *
     * @param Core_Service $service service
     *
     * @return LineCheck_Result
     */
    private function getLineCheck($service)
    {
        $request = $this->marketExchangeHelper->getLineCheckRequest($service);

        $lineCheck = $this->marketExchangeHelper->buildLineCheck($request);
        $result = $this->marketExchangeHelper->buildLineCheckResult();

        try {
            $lineCheck->sendRequest();
        } catch (LineCheck_BtRequestException $objException) {
            $arrLineCheckResult['bolServiceUnavaliable'] = 1;
            $arrLineCheckResult['strLineCheckInput'] = $service->getCliNumber();
            return $arrLineCheckResult;
        }

        $lineCheck->getResult($result);

        return $result;
    }

    /**
     * write the link check result to the database
     *
     * @param Core_Service     $service         service
     * @param LineCheck_Result $lineCheckResult lineCheckResult
     *
     * @return LineCheck_Result
     */
    private function writeLineCheckResultToDb($service, $lineCheckResult)
    {
        $lineCheckResult->setServiceId($service->getServiceId());
        $adslDetails = $service->getAdslDetails();
        $lineCheckResult->setServiceDefinitionId($adslDetails['service_definition_id']);
        $lineCheckResult->setLineCheckStatusId(LineCheck_Result::STATUS_ACTIVE);
        $lineCheckResult->write();
    }

    /**
     * Updates the tblCustomerExchange table with the supplied market id
     * for the supplied service id
     *
     * @param String $marketId  market id
     * @param String $serviceId service id
     *
     * @throws Core_Exception
     *
     * @return void
     */
    public function updateMarketData($marketId, $serviceId)
    {
        if (!$this->validateInputs($marketId, $serviceId)) {
            throw new Core_Exception(
                "Invalid parameters passed to updateMarketData() - marketId: [$marketId] and serviceId: [$serviceId]"
            );
        }

        $query = $this->buildInsertQuery($marketId, $serviceId);
        if ($this->exchangeHelper->serviceHasExhangeDataSet($serviceId)) {
            $query = $this->buildUpdateQuery($marketId, $serviceId);
        }

        $this->marketExchangeHelper->executePrimitivesQuery($query, $this->databaseName);
    }

    /**
     * Validate passed-in values
     *
     * @param  mixed $marketId  Passed in exchange marketId value
     * @param  mixed $serviceId Passed in account serviceId value
     *
     * @return bool whether values are valid
     */
    private function validateInputs($marketId, $serviceId)
    {
        return is_numeric($marketId) && is_numeric($serviceId);
    }

    /**
     * Builds an update query with given values
     *
     * @param String $marketId  market id
     * @param String $serviceId service id
     *
     * @return string
     */
    private function buildUpdateQuery($marketId, $serviceId)
    {
        return "UPDATE userdata.tblCustomerExchange SET intMarketId = $marketId WHERE intServiceId = $serviceId";
    }

    /**
     * Builds an insert query with given values
     *
     * @param String $marketId  market id
     * @param String $serviceId service id
     *
     * @return string
     */
    private function buildInsertQuery($marketId, $serviceId)
    {
        return "INSERT INTO userdata.tblCustomerExchange SET intMarketId = $marketId, intServiceId = $serviceId";
    }
}
