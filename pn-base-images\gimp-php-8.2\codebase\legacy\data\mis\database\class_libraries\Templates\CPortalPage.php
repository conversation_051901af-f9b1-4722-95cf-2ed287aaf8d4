<?php

/**
 * PlusNet Codebase Classes - CPortalPage.php
 * @version $Id: CPortalPage.php,v 1.3 2005-11-17 18:36:50 adoroshko Exp $
 * @see http://dokuwiki.internal.plus.net/
 *
 * @copyright PlusNet plc, www.plus.net
 *
 * This file is part of PlusNet Codebase Classes project.
 * Generated with ArgoUML PRE-0.19.5 on 07.11.2005, 12:59:34
 *
 * <AUTHOR> <<EMAIL>>
 */

/**
 * @include CPage.php
 */

/* user defined includes */
// section 127-0-0-1-252b0afe:1076ab571c4:-7fe8-includes begin
// section 127-0-0-1-252b0afe:1076ab571c4:-7fe8-includes end

/* user defined constants */
// section 127-0-0-1-252b0afe:1076ab571c4:-7fe8-constants begin
// section 127-0-0-1-252b0afe:1076ab571c4:-7fe8-constants end

/**
 * TODO: Describe class CPortalPage
 *
 * @access public
 * <AUTHOR> <<EMAIL>>
 */
class CSmartyPortalPage
	extends CSmartyPage
{
	// --- ATTRIBUTES ---

	// --- OPERATIONS ---

	/**
	 * TODO: Describe method CPortalPage
	 *
	 * @access public
	 * <AUTHOR> Szulc, <<EMAIL>>
	 * @param string
	 * @return void
	 */
	function CSmartyPortalPage($strTemplateDir = '')
	{
		// section 127-0-0-1-252b0afe:1076ab571c4:-7fe0 begin

		$this->CSmartyPage($strTemplateDir);

		// section 127-0-0-1-252b0afe:1076ab571c4:-7fe0 end
	}

} /* end of class CPortalPage */

?>
