<?php

	/**
	* Access library for configuring the dialup authentication component (for dialups where we do not handle the actual connectivity, just authentication)
	*
	* @package    components
	* @subpackage dialup
	* <AUTHOR> <<EMAIL>>
	*/

	/**
	* Defines
	*/

	define ('COMPONENT_DIALUP_AUTHENTICATION', 501);

	/**
	* Requires
	*/

	require_once ('/local/data/mis/database/database_libraries/userdata-access.inc');
	require_once ('/local/data/mis/database/database_libraries/product-access.inc');
	require_once ('/local/data/mis/database/database_libraries/tickets-access.inc');
	require_once ('/local/data/mis/database/database_libraries/components/config-dialup-access.inc');
	require_once ('/local/data/mis/database/database_libraries/CoreObjects/Radius/CRadiusUser.inc');
//	require_once ('/local/data/mis/scripts/portal_config.inc');

	/**
	* Global configurator setting
	*/

	$global_component_configurators[COMPONENT_DIALUP_AUTHENTICATION] = 'DialupAuthenticationConfigurator';

	/**
	* Hack to insert the component configurator array into PHP5's global scope if it's not already there
	*/

	if(!isset($GLOBALS['global_component_configurators']))
	{
		$GLOBALS['global_component_configurators'] = $global_component_configurators;
	}
	else
	{
		foreach($global_component_configurators as $intIndex => $strConfigurator)
		{
			$GLOBALS['global_component_configurators'][$intIndex] = $strConfigurator;
		}
	}

	/**
	* Functions
	*/

	/**
	* Update the users password
	*
	* <AUTHOR>
	* @param  int Component ID
	* @param  str New Password
	* @return bool Success or Failure
	*/
	function DialupAuthenticationChangePassword($intComponentID, $strNewPassword)
	{
		$bolUpdated = false;

		// Get the radius id for this dialup
		$arrDialupAuthenticationDetails = DialupAuthenticationGetDetails($intComponentID);

		if (isset($arrDialupAuthenticationDetails['intRadiusID']) && $arrDialupAuthenticationDetails['intRadiusID'] > 0)
		{
			$objRadiusUser = CRadiusUser::Create($arrDialupAuthenticationDetails['intRadiusID']);

			if (isset($objRadiusUser) && is_object($objRadiusUser))
			{
				$objRadiusUser->setPassword($strNewPassword);

				$bolUpdated = $objRadiusUser->prvSaveUpdate();

				unset($objRadiusUser);

			} // end if

		} // end if

		if (isset($bolUpdated) && $bolUpdated == true)
		{
			return true;
		}

		return false;

	} // End of function DialupAuthenticationChangePassword

	/**
	* Get all active Dialup Authentication components on a service id
	*
	* <AUTHOR>
	* @param  int Service ID
	* @return arr Component IDs
	*/
	function DialupAuthenticationGetAllActive($intServiceID)
	{
		$dbhConnection = get_named_connection_with_db('userdata');

		$strQuery = 'SELECT component_id ' .
		            ' FROM components ' .
		            " WHERE service_id = '$intServiceID' " .
		            ' AND component_type_id = "' . COMPONENT_DIALUP_AUTHENTICATION . '" ' .
		            ' AND status = "active" ' . 
		            ' ORDER BY component_id';

		$resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

		$arrComponentIDs = PrimitivesResultsAsListGet($resResult);

		return $arrComponentIDs;

	} // End of function DialupAuthenticationGetAllActive

	/**
	* Get the number of active Dialup Authentication components on a service id
	*
	* <AUTHOR>
	* @param  int Service ID
	* @return arr Component IDs
	*/
	function DialupAuthenticationGetNumberActive($intServiceID)
	{
		$dbhConnection = get_named_connection_with_db('userdata');

		$strQuery = 'SELECT count(*) as intNumberActive ' .
		            ' FROM components ' .
		            " WHERE service_id = '$intServiceID' " .
		            ' AND component_type_id = "' . COMPONENT_DIALUP_AUTHENTICATION . '" ' .
		            ' AND status = "active"';

		$resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

		$intNumberActive = PrimitivesResultGet($resResult, 'intNumberActive');

		return $intNumberActive;

	} // End of function DialupAuthenticationGetNumberActive

	/**
	* Get the full details for the component, including username and password from radius
	*
	* <AUTHOR>
	* @param  int Component ID
	* @return arr Component Details
	*/
	function DialupAuthenticationGetDetails($intComponentID)
	{
		$dbhConnection = get_named_connection_with_db('userdata');

		$strQuery = 'SELECT c.component_id as intComponentID, ' .
		            '       c.service_id as intServiceID, ' .
		            '       c.component_type_id as intServiceComponentID, ' .
		            '       c.description as strDescription, ' .
		            '       c.status as strStatus, ' .
		            '       UNIX_TIMESTAMP(c.creationdate) as uxtCreation, ' .
		            '       IF (cda.intConfigID IS NULL, 0, cda.intConfigID) as intConfigID, ' .
		            '       IF (cda.intRadiusID IS NULL, 0, cda.intRadiusID) as intRadiusID, ' .
		            '       IF (cda.dtmStart IS NULL, 0, UNIX_TIMESTAMP(cda.dtmStart)) as uxtConfigStart, ' .
		            '       IF (cda.dtmEnd IS NULL, 0, UNIX_TIMESTAMP(cda.dtmEnd)) as uxtConfigEnd ' .
		            ' FROM components c ' .
		            ' LEFT JOIN tblConfigDialupAuthentication cda ON c.component_id = cda.intComponentID ' .
		            " WHERE c.component_id = '$intComponentID'";

		$resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

		$arrConfigDetails = PrimitivesResultGet($resResult);

		$arrConfigDetails['strUsername'] = '';
		$arrConfigDetails['strPassword'] = '';

		if (isset($arrConfigDetails['intRadiusID']) && $arrConfigDetails['intRadiusID'] > 0)
		{
			$objRadiusUser = CRadiusUser::Create($arrConfigDetails['intRadiusID']);

            if (isset($objRadiusUser) && is_object($objRadiusUser))
            {
				$arrConfigDetails['strUsername'] = $objRadiusUser->getUsername();

				$arrConfigDetails['strPassword'] = $objRadiusUser->getPassword();

                unset($objRadiusUser);

            } // end if

		} // end if

		return $arrConfigDetails;

	} // End of function DialupAuthenticationGetDetails

	/**
	* Add a ticket to the account when the password is updated
	*
	* <AUTHOR>
	* @static
	* @param  int Service ID
	* @param  int Component ID
	* @param  str Username
	* @return bool Success or Failure
	*/
	function DialupAuthenticationAddTicketForChangePassword($intServiceID, $intComponentID, $strUsername)
	{
		global $my_id;

		if (!isset($my_id))
		{
			$my_id = '';
		}

		$strTicketBody = "Customer has updated their password for the Dialup Authentication (component id $intComponentID) for username $strUsername.";

		return tickets_ticket_add('Internal', $intServiceID, 0, 0, 'Closed', $my_id, $strTicketBody);

	} // End of function DialupAuthenticationAddTicketForChangePassword

	/**
	* Add a ticket to the account when they delete the Dialup authentication
	*
	* <AUTHOR>
	* @static
	* @param  int Service ID
	* @param  int Component ID
	* @param  str Username
	* @return bool Success or Failure
	*/
	function DialupAuthenticationAddTicketForDeletion($intServiceID, $intComponentID, $strUsername)
	{
		global $my_id;

		if (!isset($my_id))
		{
			$my_id = '';
		}

		$strTicketBody = "Customer has deleted their Dialup Authentication (component id $intComponentID) for username $strUsername.";

		return tickets_ticket_add('Internal', $intServiceID, 0, 0, 'Closed', $my_id, $strTicketBody);

	} // End of function DialupAuthenticationAddTicketForDeletion

	/**
	* Validate the password entered
	*
	* <AUTHOR>
	* @static
	* @param  int Component ID
	* @param  str New Password
	* @param  str Confirmation Password
	* @return arr Errors
	*/
	function DialupAuthenticationValidatePassword($intComponentID, $strNewPassword, $strConfirmationPassword)
    {
		global $password_regex;

		$arrErrors = array();

		// make sure both boxes were filled in
		if (!(isset($strNewPassword) && strlen($strNewPassword) > 0 && isset($strConfirmationPassword) && strlen($strConfirmationPassword) > 0))
		{
			$arrErrors['strErrorMessage'] = 'You must fill in both password boxes';

			return $arrErrors;
		}

		// validate the password; make sure it follows the simple rules.
		if (isset($password_regex) && sizeof($password_regex) > 0)
		{
			$arrReturn = ereg_validate($strNewPassword, $password_regex);

			if(!$arrReturn['result'])
			{
				$arrErrors['strErrorMessage'] = $arrReturn['message'];

				return $arrErrors;
			}

		} // end if

		// do both password boxes contain the same password?
		if ($strNewPassword != $strConfirmationPassword)
		{
			$arrErrors['strErrorMessage'] = 'Passwords do not match';

			return $arrErrors;
		}

		// Check that the password is not the same as the username
		$arrDialupAuthenticationDetails = DialupAuthenticationGetDetails($intComponentID);

		if (isset($arrDialupAuthenticationDetails['strUsername']) && stristr($strNewPassword, $arrDialupAuthenticationDetails['strUsername']))
		{
			$arrErrors['strErrorMessage'] = 'Passwords must not contain the username';

			return $arrErrors;
		}

		//  Check that the password is not on the banned list
		$arrBannedPasswords = userdataGetBannedPasswords();

		foreach ($arrBannedPasswords as $arrBannedPassword)
		{
			if ($strNewPassword == $arrBannedPassword['vchBannedPassword'])
			{
				$arrErrors['strErrorMessage'] = 'The password is not allowed';

				return $arrErrors;
			}

		} // end foreach

		return $arrErrors;

	} // End of function DialupAuthenticationValidatePassword

	/**
	* Check whether we need to activate the adsl accounts 0845 dialup components if there are no more dialup authentication components left
	*
	* <AUTHOR>
	* @param  int Component ID
	* @param  int Service ID
	* @return bool Success or Failure
	*/
	function DialupAuthenticationMaintain0845Dialups($intServiceID)
	{
		global $global_component_configurators;

		// Check whether there are some active components
		$intNumberActive = DialupAuthenticationGetNumberActive($intServiceID);

		if (isset($intNumberActive) && $intNumberActive > 0)
		{
			return true;
		}

		$bolError = false;

		// If no active components then find all the associated accounts and activate their 0845 dialup components
		$arrServiceStatuses = array('queued-activate', 'queued-reactivate', 'active', 'queued-deactivate', 'deactive');

		$arrChildServiceIDs = userdata_get_resold_services($intServiceID, $arrServiceStatuses);

		if (isset($arrChildServiceIDs) && sizeof($arrChildServiceIDs) > 0)
		{
			$arr0845ServiceComponentIDs = product_dialup_component_type_get('0845');

			$arrComponentStatuses       = array('unconfigured',
			                                    'queued-activate',
			                                    'queued-reactivate',
			                                    'active',
			                                    'queued-deactivate',
			                                    'queued-deconfigure',
			                                    'deactive');

			foreach ($arrChildServiceIDs as $intServiceID)
			{
				$arrCriteria = array('service_id' => $intServiceID,
				                     'type'       => $arr0845ServiceComponentIDs,
				                     'status'     => $arrComponentStatuses);

				$arr0845Components = userdata_component_find($arrCriteria);

				if (isset($arr0845Components) && sizeof($arr0845Components) > 0)
				{
					foreach ($arr0845Components as $arr0845Component)
					{
						// If the configurator is found then auto configure the dialup component
						if (isset($global_component_configurators[$arr0845Component['component_type_id']]))
						{
							$strConfigurator = $global_component_configurators[$arr0845Component['component_type_id']];

							$strConfigurator($arr0845Component['component_id'], 'auto_configure');
						}
						else
						{
							// Configurator was not found
							$bolError = true;
						}

					} // foreach

				}
				else
				{
					// if no current available component on the account the add an unconfigured one and activate it
					$arrService = userdata_service_get($intServiceID);

					// Find the relevant 0845 component that goes on the account
					$arrProductServiceComponentIDs = product_get_default_not_active_components_for_service($arrService['type']);

					if (isset($arrProductServiceComponentIDs) && sizeof($arrProductServiceComponentIDs) > 0)
					{
						foreach ($arrProductServiceComponentIDs as $intProductServiceComponentID)
						{
							if (in_array($intProductServiceComponentID, $arr0845ServiceComponentIDs))
							{
								$int0845ComponentID = userdata_component_add($intServiceID, $intProductServiceComponentID, -1, '', 'unconfigured');

								if (isset($int0845ComponentID) &&
								    $int0845ComponentID > 0 &&
								    isset($global_component_configurators[$intProductServiceComponentID]))
								{
									$strConfigurator = $global_component_configurators[$intProductServiceComponentID];

									$strConfigurator($int0845ComponentID, 'auto_configure');
								}
								else
								{
									// Configurator was not found
									$bolError = true;

								} // end if

							} // end if

						} // end foreach

					} // end if

				} // end if

			} // end foreach

		} // end if

		if ($bolError)
		{
			return false;
		}

		return true;
 
	} // End of function DialupAuthenticationMaintain0845Dialups

	/**
	* Standard Component Configurator functions
	* ONLY SPECIFICATIONALLY REQUIRED FUNCTIONS ARE INCLUDED - OTHERS MAY BE ADDED IN FUTURE IF NEEDED BUT FOR METRONET LIFT AND SHIFT PROJECT THEY
	* WILL NOT BE DONE
	*/

	/**
	* Destroy the dialup authentication componenent
	*
	* <AUTHOR>
	* @param  int Component ID
	* @return bool Success or Failure
	*/
	function DialupAuthenticationDestroy($intComponentID)
	{
		$arrComponent = userdata_component_get($intComponentID);

		switch ($arrComponent['status'])
		{
			case 'active' :
			case 'queued-destroy' :

				$bolUpdated = false;

				// Set the component to queued destroyed
				userdata_component_set_status($intComponentID, 'queued-destroy');

				// Deactivate the radius user for this dialup authentication component
				$arrDialupAuthenticationDetails = DialupAuthenticationGetDetails($intComponentID);

				if (isset($arrDialupAuthenticationDetails['intRadiusID']) && $arrDialupAuthenticationDetails['intRadiusID'] > 0)
				{
					$objRadiusUser = CRadiusUser::Create($arrDialupAuthenticationDetails['intRadiusID']);

					if (isset($objRadiusUser) && is_object($objRadiusUser))
					{
						// Set whether user is active in the object 
						$objRadiusUser->setActive(false);

						$bolUpdated = $objRadiusUser->prvSaveUpdate();

						unset($objRadiusUser);

					} // end if

				} // end if

				if ($bolUpdated == true)
				{
					// Check whether we should activate associated adsl accounts 0845 components
					$bolMaintain0845Dialups = DialupAuthenticationMaintain0845Dialups($arrComponent['service_id']);

					if (isset($bolMaintain0845Dialups) && $bolMaintain0845Dialups == true)
					{
						userdata_component_set_status($intComponentID, 'destroyed');

						return true;

					} // end if

				} // end if

				break;

			case 'unconfigured' :

				// Check whether we should activate associated adsl accounts 0845 components
				$bolMaintain0845Dialups = DialupAuthenticationMaintain0845Dialups($arrComponent['service_id']);

				if (isset($bolMaintain0845Dialups) && $bolMaintain0845Dialups == true)
				{
					userdata_component_set_status($intComponentID, 'destroyed');

					return true;

				} // end if

				break;

			default :

				// Do nothing
				break;
		}

		return false;

	} // End of function DialupAuthenticationDestroy

	/**
	* Auto configurator for dialup authentication component
	*
	* <AUTHOR>
	* @param  int Component ID
	* @param  str Action
	* @return bool Success or Failure
	*/
	function DialupAuthenticationConfigurator($intComponentID, $strAction)
	{
		$bolConfigured = false;

		switch ($strAction)
		{
			// Only allow the auto destroy action as no others should be valid
			case 'auto_destroy' :

				$bolConfigured = DialupAuthenticationDestroy($intComponentID);

				break;

			case 'auto_enable' :
			case 'auto_configure' :
			case 'auto_refresh' :
			case 'auto_disable' :
			default :

				// Do nothing
				break;

		} // end switch

		if ($bolConfigured == true)
		{
			return true;
		}

		return false;

    } // End of function DialupAuthenticationConfigurator

?>
