<?php
/**
 * @package HardwareBundle
 * @<NAME_EMAIL>
 */
/**
 * @package HardwareBundle
 * @<NAME_EMAIL>
 */
class NetLynkHardwareRmaProcess extends HardwareRmaProcess
{

	/**
	 * @var string
	 */
	var $m_strSupplierTag = 'NetLynk';

	/**
	 * @var string
	 */
	var $m_strClassPrefix = 'NetLynk';

	/**
	 * Initiate RMA for the hardware bundle
	 *
	 * @param int $intComponentId
	 */
	public function InitiateRma($intComponentId = 0)
	{
		// Update the object
		$this->m_objOriginalBundle->SetStatusTag(-1, 'awaiting_rma');
		$this->m_objOriginalBundle->Commit();

		return true;
	}

	/**
	 * Get the return address
	 *
	 * Get the suppliers return address
	 *
	 * @access protected
	 * @return String - The return address
	 */
	function GetReturnsAddress()
	{
		// Return generic message.
		$strReturnAddress = 'Not applicable';

		return $strReturnAddress;
	}

	/**
	 * RmaCode returning 0,
	 * copied from BTHardwareRmaProcess class
	 */
	function GetRmaCode()
	{
		return 0;
	}


}
