<?php
/**
 * Access library for WLR component.  Contains component configurator and component class
 *
 * @package    Core
 * @subpackage WLR
 * @access     public
 * <AUTHOR> <<EMAIL>>
 * @filesource
 */

require_once '/local/data/mis/database/database_libraries/components/CProduct.inc';
require_once '/local/data/mis/database/database_libraries/components/CWlrCallFeature.inc';
require_once '/local/data/mis/database/database_libraries/components/CWlrCallFeaturesBundle.php';
require_once '/local/data/mis/database/database_libraries/components/CWlrLineRent.inc';
if (!defined('CUSTOMER_DETAILS')) {
    require_once SECURE_TRANSACTION_ACCESS_LIBRARY;
    require_once FINANCIAL_ACCESS_LIBRARY;
    require_once TICKETS_ACCESS_LIBRARY;
    require_once DIRECT_DEBIT_ACCESS_LIBRARY;
    require_once '/local/data/mis/database/database_libraries/mailer_functions.inc';
}
require_once CONFIG_DIALUP_ACCESS_LIBRARY;
require_once '/local/data/mis/portal_modules/payment/cc_result.inc';
require_once '/local/data/mis/database/database_libraries/CoreObjects/Financial/CProductComponentScheduledPayment.inc';
require_once '/local/data/mis/database/application_apis/wlr/spg/Wlr_OrderType_DebtManagement.class.php';
require_once '/local/data/mis/common_library_functions/class_libraries/CAutoProblem.class.php';
require_once PHPLIB_ACCESS_LIBRARY;
require_once '/local/data/mis/common_library_functions/common_application_apis/Core/Libraries/Service.class.php';
require_once '/local/data/mis/database/application_apis/EmailHandler/EmailHandler.class.php';
require_once '/local/data/mis/database/class_libraries/C_Financial_Failed_Billing.inc';
require_once '/local/data/mis/database/database_libraries/sql_primitives.inc';
require_once '/local/data/mis/database/database_libraries/programme-tool-access.inc';
require_once '/local/data/mis/common_library_functions/common_application_apis/Core/Libraries/Account.class.php';
require_once '/local/data/mis/database/database_libraries/components/CWlrProvisionType.class.php';
require_once '/local/data/mis/database/database_libraries/userdata/PreSignupData.class.php';
use \Plusnet\ContractsClient\Instance\InstanceInterface;
use \Plusnet\Feature\FeatureToggleManager;

/**
 * WLR Product
 *
 * Landline product
 *
 * @access     public
 * <AUTHOR> <<EMAIL>>
 */
//!!!!!!!!!! Component defines moved to the bottom of the file

class CWlrProduct extends CProduct
{
    const TYPE_HANDLE = 'WLR';

    const MAX_CREDIT_LIMIT = 100000;
    const MED_CREDIT_LIMIT = 25000;
    const BASE_CREDIT_LIMIT_BUSINESS = 20000;
    const BASE_CREDIT_LIMIT_RESIDENTIAL = 10000;

    const GROUP_FINANCE = 'Finance';

    const CSC_TEAM_LEADERS = 'CSC Team Leaders';
    const SA_CSC_MANAGERS = 'SA CSC Managers';
    const SA_CUSTOMER_SERVICE_TEAM_LEADERS = 'SA Customer Service Team Leaders';
    const SA_TECHNICAL_SUPPORT_TEAM_LEADERS = 'SA Technical Support Team Leaders';
    const CSC_TLS = 'CSC TLs';

    const EVENING_WEEKEND_COMPONENT_ID = COMPONENT_WLR_EVENING_WEEKEND;

    private $bolIsBusinessAccount = null;
    private $intLineRentId = null;

    /**
     * Mapping of wlr product types to call barring types
     *
     * @var array
     **/
    protected $_callBarringMap = array();

    /**
     * Constructor
     */

    /**
     * Constructor for the CWLRProduct class
     * Load a component instance
     */
    public function CWlrProduct($intComponentID)
    {
        $this->CComponent($intComponentID);
        $this->refreshInstance($this->getComponentID());

        return true;
    } // function CWlrProduct($intComponentID)

    /**
     * Static Methods
     */

    /**
     * Function to tell us if we're provisioned on WLR3 or not
     *
     * @return boolean True if it's a WLR3 component
     **/
    public function provisionedOnWlr3()
    {
        $lineRentId = $this->getLineRentId();
        $platform = '';

        if ($lineRentId) {
            $platform = self::getSupplierPlatform($lineRentId);
        }

        if ($platform == CWlrProvisionType::WLR3) {
            return true;
        }

        return false;
    }

    /**
     * Retrieves component statuses when we would show the WLR control panel
     *
     * <AUTHOR>
     * @static
     * @access   public
     * @return   array
     */
    public function getControlPanelStatuses()
    {
        return array('active', 'queued-deactivate', 'deactive', 'queued-destroy', 'queued-reactivate');
    }

    /**
     * Retrieves the WLR Product by Service ID
     *
     * <AUTHOR> Jones" <<EMAIL>>
     *
     * @param        integer    Service ID
     * @param string $strStatus (Optional) Status of the component to find
     *
     * @return   object  CWlrProduct Object
     */
    public static function getWlrProductFromServiceId($intServiceId, $strStatus = null)
    {
        $objWlrProduct = CProduct::getProductByProductTypeHandle($intServiceId, 'WLR', $strStatus);

        return $objWlrProduct;
    }

    /**
     * canAddWlr
     * You can add WLR to service only if
     * - there are no WLR components at all
     * - there are destroyed WLR components
     *
     * This function does not check if component is allowed on a service definition
     *
     * @param  int $intServiceId
     *
     * @return bool
     */

    public static function canAddWlr($intServiceId)
    {
        $arr = CProduct::getAllComponentsByServiceId($intServiceId, self::TYPE_HANDLE);
        //no components - we can add one
        if (empty($arr))
            return true;

        $arrDestroyed = array(CComponent::STATUS_QUEUED_DESTROY, CComponent::STATUS_DESTROYED);
        foreach ($arr as $arrComponent) {
            //there are non destroyed components
            if (!in_array($arrComponent['strStatus'], $arrDestroyed)) {
                return false;
            }
        }

        return true;
    }

    /**
     * canChangeWlr
     * WLR can be changed only when:
     * - there is only one active WLR component
     *
     * @param  int|string $intServiceId
     *
     * @return bool
     */
    public static function canChangeWlr($intServiceId)
    {
        $arr = CProduct::getAllComponentsByServiceId((int) $intServiceId, self::TYPE_HANDLE);
        //if no components - nothing to change
        if (empty($arr))
            return false;

        $arrDestroyed = array(CComponent::STATUS_QUEUED_DESTROY, CComponent::STATUS_DESTROYED);
        $intActive = 0;
        foreach ($arr as $arrComponent) {
            //destroyed are ok
            if (in_array($arrComponent['strStatus'], $arrDestroyed)) {
                continue;
            }
            //if other than active - can not change
            if (CComponent::STATUS_ACTIVE !== $arrComponent['strStatus']) {
                return false;
            }
            //how many active ones we have ?
            //ATM it can be only one
            if (CComponent::STATUS_ACTIVE === $arrComponent['strStatus']) {
                $intActive++;
            }
        }

        return (1 === $intActive);
    }

    /**
     * Get a list of the WLR Component types
     *
     * Get a simple array containing the WLR Service Component
     * types.
     *
     * <code>
     * $arrList = CProduct::getProductComponentTypes();
     * </code>
     *
     * @access public
     * @static
     * <AUTHOR> Jones" <<EMAIL>>
     *
     * @param  string    Product handle
     *
     * @return array service component ID's
     * @throws none none
     */
    public function getWLRComponentTypes($strCompanyBrand = null)
    {
        $arrTypes = self::getProductComponentTypes('WLR');

        if (null !== $strCompanyBrand && is_array($arrTypes) && !empty($arrTypes)) {

            // Perform a funky dance. Ideally, the following code should be
            // implemented using database queries, but no such table structures
            // exist and I don't have permissions to create new tables :(

            // It should probably also be further down the chain in CProduct
            // to allow for future expansion, but as only WLR has identified this
            // particular quirk, it can live in here for now.

            $arrCompanyBrandMapping = array(
                'PLUSNET'    => array(
                    WLR_ANYTIME_COMPONENT_ID,
                    WLR_ANYTIME_PLUS_COMPONENT_ID,
                    WLR_EVENING_WEEKEND_COMPONENT_ID,
                    WLR_ESSENTIAL_COMPONENT_ID,
                    WLR_PN_TALK_ANYTIME_COMPONENT_ID,
                    WLR_PN_TALK_EVENING_WEEKEND_COMPONENT_ID,
                    COMPONENT_WLR_PN_BUSINESS_PHONE_PAYG,
                    COMPONENT_WLR_PN_BUSINESS_PHONE_DAYTIME,
                    COMPONENT_WLR_PN_RES_TALK_ANYTIME_2010,
                    COMPONENT_WLR_PN_RES_MOBILE_ANYTIME_TRIAL1,
                    COMPONENT_WLR_PN_RES_MOBILE_ANYTIME_TRIAL2,
                    COMPONENT_WLR_JOHNLEWIS_RES_ANYTIME,
                    COMPONENT_WLR_JOHNLEWIS_RES_EW,
                    COMPONENT_WLR_JOHNLEWIS_RES_ANYTIME_INTL,
                    COMPONENT_WLR_BUS_ANY_NO,
                    COMPONENT_WLR_BUS_500_NO,
                    COMPONENT_WLR_BUS_1000_NO,
                    COMPONENT_WLR_BUS_PAYG_NO,
                    COMPONENT_WLR_BUS_ANY_12,
                    COMPONENT_WLR_BUS_500_12,
                    COMPONENT_WLR_BUS_1000_12,
                    COMPONENT_WLR_BUS_PAYG_12,
                    COMPONENT_WLR_BUS_ANY_24,
                    COMPONENT_WLR_BUS_500_24,
                    COMPONENT_WLR_BUS_1000_24,
                    COMPONENT_WLR_BUS_PAYG_24,
                ),
                'BRIGHTVIEW' => array(
                    WLR_TALK_COMPONENT_ID,
                    WLR_TALK_ANYTIME_COMPONENT_ID,
                    WLR_GREENBEE_PHONE_COMPONENT_ID,
                    WLR_GREENBEE_PHONE_BUNDLE_COMPONENT_ID
                )
            );

            $strCompanyBrand = strtoupper($strCompanyBrand);

            if (!isset($arrCompanyBrandMapping[$strCompanyBrand])) {

                // an invalid handle passed in, thus simulate a query
                // that would return back no results.
                return array();
            }

            // removing entries in-place is likely to be a bit slower than building
            // a new array containing the entries we require, but an advantage is
            // that it functions more reliably as a fake inner join.

            $arrSelectedBrand = $arrCompanyBrandMapping[$strCompanyBrand];

            foreach (array_keys($arrTypes) as $intKey) {

                if (!in_array($intKey, $arrSelectedBrand)) {

                    unset($arrTypes[$intKey]);
                }
            }
        }

        return $arrTypes;
    } // function getWLRComponentTypes()

    /**
     * Helper function, used to determine if a particular service component is Wlr type
     *
     * @param $intComponentTypeId int
     * @param $strCompanyBrand    string, plusnet or brightview
     *
     * @return bool
     */
    public static function isWlrComponentType($intComponentTypeId, $strCompanyBrand)
    {
        if (empty($intComponentTypeId) || !is_numeric($intComponentTypeId)) {
            return false;
        }

        $arrComponents = CWlrProduct::getWLRComponentTypes($strCompanyBrand);

        if (empty($arrComponents)) {
            return false;
        } else {
            return in_array($intComponentTypeId, array_keys($arrComponents));
        }
    }

    /**
     * getWlrProductFromCli
     *
     * <AUTHOR> Jones" <<EMAIL>>
     * @static
     * @access   public
     *
     * @param    string  CLI number
     * @param    array   Array of acceptable statuses to retrieve the object in
     *
     * @return   object  CWlrProduct Object
     */
    public static function getWlrProductFromCli($strCli, $arrStatus = array())
    {
        $objWlrProduct = false;

        if (false !== ($objWlrLineRentComponent = CWlrProduct::getLineRentObjectFromCli($strCli, $arrStatus))) {
            $intComponentId = $objWlrLineRentComponent->getComponentID();
            $objWlrProduct = CProduct::createInstance($intComponentId);
        }

        return $objWlrProduct;
    } // function &getWlrProductFromCli($strCli)

    /**
     * isSignupOrUpgrade
     *
     * <AUTHOR> Przybyszewski" <<EMAIL>>
     *
     * @param  integer $intServiceID
     * @param  string  $strProductHandle
     *
     * @return string  [SIGNUP|UPGRADE]
     */
    public function isSignupOrUpgrade($intServiceID, $strProductHandle = 'WLR')
    {
        return CProduct::isSignupOrUpgrade($intServiceID, $strProductHandle);
    }

    /**
     * Actually create a new component product
     *
     * This method creates the WLR component on the account, and adds the Product
     * Components to the new Component. It does NOT do any validation of the values
     * passed to it and it does not create the configurations required by the
     * LineRent and PAYG product components. You probably really want to call doSignup.
     *
     * <AUTHOR> Jones" <<EMAIL>>
     * @static
     */
    public static function create($intServiceId, $intServiceComponentId, $intTariffId = 0, $uxtNextInvoiceDate = false,
                                  $strProductHandle = '', $uxtStartTime = '', $arrProductComponentOptions = array())
    {
        // Get the next invoice date from the service
        $arrService = userdata_service_get($intServiceId);
        list($intYear, $intMonth, $intDay) = explode('-', $arrService['next_invoice']);
        $uxtNextInvoice = mktime(0, 0, 0, $intMonth, $intDay, $intYear);
        // Create the component (and default product components)
        $objWlrComponent = CProduct::create(
            $intServiceId, $intServiceComponentId,
            $intTariffId, $uxtNextInvoice, 'WLR', $uxtStartTime
        );

        $intComponentId = $objWlrComponent->getComponentID();

        // Add optional features, checking the option is allowed on the component and not included by default
        $arrAllowedProductComponentId = CProduct::getAllowedProductComponentIDs($intServiceComponentId);
        $arrDefaultProductComponentId = CProduct::getDefaultProductComponentIDs($intServiceComponentId);
        $arrAllowedProductComponentOptions = array_diff($arrAllowedProductComponentId, $arrDefaultProductComponentId);

        $arrBundleTypes = CwlrCallFeaturesBundle::getAllTypes();

        foreach ($arrProductComponentOptions as $strProductComponentHandle) {
            $intProductComponentId = CProductComponent::getProductComponentIDByHandle($strProductComponentHandle);

            // Only create the Product Component if this Service Component Type allows it
            // And if it is not a bundle
            if (in_array($intProductComponentId, $arrAllowedProductComponentOptions) &&
                !in_array($intProductComponentId, $arrBundleTypes)
            ) {
                CProductComponent::create(
                    $intServiceId, $intComponentId, $intProductComponentId,
                    0, false, 'WLR', $uxtStartTime
                );
            }
        }

        CWlrProduct::createBundleIfApplicable($intServiceId, $objWlrComponent);

        return $objWlrComponent;
    }

    /**
     * Is it applicable to try and create a bundle.
     *
     * @param
     *
     * @return boolean
     */
    public function createBundleIfApplicable($intServiceId, $objWlrComponent)
    {
        //add call features bundle if required
        $arrFeatures = $objWlrComponent->getSelectedProductComponentInstanceIDs(
            CWlrCallFeature::getBundleableCallFeatures($objWlrComponent->getComponentTypeID()),
            array(PRODUCT_COMPONENT_UNCONFIGURED)
        );

        //check if there is a unconfigured bundle, if so do not create one
        $arrBundles = $objWlrComponent->getSelectedProductComponentInstanceIDs(
            CWlrCallFeaturesBundle::getAllTypes($objWlrComponent->getComponentTypeID()),
            array(PRODUCT_COMPONENT_UNCONFIGURED)
        );

        if (count($arrBundles) == 0) {
            //add call features bundle if required
            $intProductComponentId = CWlrCallFeaturesBundle::getTypeByAmountOfFeatures(
                count($arrFeatures),
                $objWlrComponent->getComponentTypeID()
            );

            if ($intProductComponentId) {
                CProductComponent::create(
                    $intServiceId, $objWlrComponent->getComponentID(),
                    $intProductComponentId, 0, false, 'WLR'
                );
            }
        }
    }

    /**
     * Create the wlr product and all of the sub components in the database
     *
     * @param int    $intServiceId               Service Id
     * @param int    $intServiceComponentId      Service Component Id
     * @param string $strCli                     Customers Cli
     * @param array  $arrProductComponentOptions Product Component Options
     * @param array  $arrOptParams               Optional paramters, just for good measure
     *
     * @return CWlrProduct
     */
    public function createWithComponents($intServiceId, $intServiceComponentId, $strCli,
                                         $arrProductComponentOptions = array(), $arrOptParams = array())
    {
        // Optional parameters
        $strContractHandle = (isset($arrOptParams['strContractHandle'])) ? $arrOptParams['strContractHandle'] : 'MONTHLY';
        $strPaymentFrequencyHandle = (isset($arrOptParams['strPaymentFrequencyHandle'])) ? $arrOptParams['strPaymentFrequencyHandle'] : 'MONTHLY';
        $strSupplierReference = (isset($arrOptParams['strSupplierReference'])) ? $arrOptParams['strSupplierReference'] : null;
        $bolRetentionOffer = (isset($arrOptParams['bolRetentionOffer']) && $arrOptParams['bolRetentionOffer'] == true) ? true : false;
        $bolLeaveUnconfigured = (isset($arrOptParams['bolLeaveUnconfigured']) && true === $arrOptParams['bolLeaveUnconfigured']);
        $bolAddIncludedMinutes = (isset($arrOptParams['bolAddIncludedMinutes']) && $arrOptParams['bolAddIncludedMinutes'] === true ? true : false);
        $bolWlrAllCallsBarred = (isset($arrOptParams['bolCallbarring']) && $arrOptParams['bolCallbarring'] == true ? true : false);
        $strSupplierPlatform = (isset($arrOptParams['strSupplierPlatform'])) ? $arrOptParams['strSupplierPlatform'] : CWlrProvisionType::WLR3;
        $bolLineRentalSaver = (isset($arrOptParams['bolLineRentalSaver'])) ? $arrOptParams['bolLineRentalSaver'] : false;

        $tariffTypeHandle = ($bolLineRentalSaver) ? 'LINE_RENTAL_SAVER' : 'DEFAULT';

        //This is for BPR. On business phome 1 component will support multiple wlrcalltime product component.
        //The wlrcalltime product component instance will be created here depending upon the number of minutes the
        //business phone componnet is signed up for
        //$arrOptParams['intIncludedMinutes'] = number of minutes could be 1200 or 0
        //$arrOptParams['bolAddIncludedMinutes'] would be TRUE for business phone
        if (isset($arrOptParams['bolAddIncludedMinutes']) && $bolAddIncludedMinutes) {
            $intIncludedMins = $arrOptParams['intIncludedMinutes'];
        }

        $arrProdComps = CProduct::getDefaultProductComponentHandles($intServiceComponentId);

        if (in_array('SUBSCRIPTION', $arrProdComps)) {

            // Get a Tariff for the subscription product
            if (!($intTariffId = CProduct::getProductTariffID(
                $intServiceComponentId, $strContractHandle, $strPaymentFrequencyHandle, 'SUBSCRIPTION', $tariffTypeHandle
            ))
            ) {

                // No Tariff ID for subscription component
                $objWlrProduct = new CWlrProduct(0);
                $objWlrProduct->setError(
                    __FILE__, __LINE__,
                    "A tariff could not be found for Home Phone Product Type [{$intServiceComponentId}] for [{$strContractHandle}] contract and [{$strPaymentFrequencyHandle}] payment frequency. Depending on the product's capabilities, it may require an alternative contract length/payment frequency selecting"
                );

                return $objWlrProduct;
            }
        } else {

            $intTariffId = null;
        }

        $objWlrProduct = CWlrProduct::create(
            $intServiceId, $intServiceComponentId, $intTariffId, false, '', '', $arrProductComponentOptions
        );

        $intComponentId = $objWlrProduct->getComponentID();

        if ($bolRetentionOffer) {
            CWlrProduct::markAsRetentionOffer($intComponentId);
        }
        $arrWlrComponent = $objWlrProduct->listProductComponentInstanceIdByHandle();

        if (empty($arrWlrComponent)) {
            $objWlrProduct->setError(
                __FILE__, __LINE__,
                "Could not create Product Components for Component [{$intComponentId}]"
            );

            return $objWlrProduct;
        }

        // Get the Call Plan ID for this product type
        if (!$intCallPlanId = CWlrProduct::getCallPlanIdFromServiceComponentId($intServiceComponentId)) {
            $intCallPlanId = 0;
        }

        // Create the Line Rent Config
        $intWlrLineRentInstanceId = $arrWlrComponent['WlrLineRent'];
        $objWlrLineRentProdCompIns = CProductComponent::createInstance($intWlrLineRentInstanceId);

        $intWlrLineRentConfigId = $objWlrLineRentProdCompIns->createUserConfig(
            $strCli, $intCallPlanId, $strSupplierReference, $strSupplierPlatform
        );

        // Create the PAYG Config
        $intWlrPaygInstanceId = $arrWlrComponent['WlrPayg'];
        $objWlrPaygProdCompIns = CProductComponent::createInstance($intWlrPaygInstanceId);
        $intWlrPaygConfigId = $objWlrPaygProdCompIns->createUserConfig();

        //This is a busiess phone component so create the call time instance accordingly
        if ($bolAddIncludedMinutes && is_object($objWlrPaygProdCompIns) && is_object($objWlrProduct)) {

            require_once '/local/data/mis/database/database_libraries/components/CWlrCalltime.inc';
            //Create Calltime object for business customers.
            $intTariffID = CWlrCalltime::getTariffIDByMinutes(
                $objWlrPaygProdCompIns->getServiceComponentProductID(), $intIncludedMins
            );

            if ($intTariffID > 0) {
                CProductComponent::create(
                    $objWlrProduct->getServiceID(), $objWlrProduct->getComponentID(),
                    PRODUCT_COMPONENT_WLR_CALLTIME, $intTariffID, false, 'WLR', time()
                );
            }
        }

        // We've opted to barr all calls using debt management rather than a feature.
        if ($bolWlrAllCallsBarred) {
            CProductComponent::create(
                $objWlrProduct->getServiceID(), $objWlrProduct->getComponentID(),
                PRODUCT_COMPONENT_WLR_ALL_CALLS_BARRED, 0, false, 'WLR', time()
            );
        }

        // A note why we'd want to have a component in an "unconfigured" state..
        // It is to prevent a possible race condition between the WLR provisioning
        // process submitting an order and the action of manually adding a WLR component
        // to an account which is then "immediately" activated.

        // There aren't any other reasons (except for product changes, where the new
        // component is temporarily in an unconfigured state) why you'd want an unconfigured
        // component -- it's to prevent provisioning from picking them up.

        if (!$bolLeaveUnconfigured) {
            $objWlrProduct->setStatus('queued-activate');
        }

        // Log that they have signed up to a 12 month contract for home phone
        if (isset($arrOptParams['strContractHandle']) && 'annual' == strtolower($arrOptParams['strContractHandle'])) {

            userdata_service_event_add(
                $intServiceId, SERVICE_EVENT_12MONTH_CONTRACT_START_WLR,
                '', $intServiceComponentId
            );
        }

        // Log this event
        $objEventLogger = new CComponentEvent($intComponentId);
        $objEventLogger->logSignup('WLR');
        unset($objEventLogger);

        return $objWlrProduct;
    }

    //will return empty array if you do not pass product component types at all
    //otherwise will call getProductComponentInstanceIDs
    public function getSelectedProductComponentInstanceIDs($arrTypes, $arrStatuses, $intComponentIdArg = '')
    {
        if (empty($arrTypes))
            return array();

        //terrible hack to handle both dynamic and static calls
        if (empty($intComponentIdArg) && isset($this)) {
            $intComponentIdArg = $this->m_intComponentID;
        }

        return self::getProductComponentInstanceIDs($arrTypes, $arrStatuses, $intComponentIdArg);
    }

    /**
     * Is the Product an Upgrade?
     *
     * <AUTHOR> Jones" <<EMAIL>>
     * @static
     * @access   public
     *
     * @param    integer Service ID
     * @param    integer New Service Component ID we are going to (Component Type ID)
     *
     * @return   boolean True if this is an upgrade
     */
    public function isProductUpgrade($intServiceId = null, $intNewServiceComponentId)
    {
        if (null == $intServiceId) {
            if (!is_object($this)) {
                return false;
            }
            $objWlrProduct = $this;
        } else {
            if (false === ($objWlrProduct = CWlrProduct::getWlrProductFromServiceId($intServiceId))) {
                return false;
            }
        }

        if (false === ($intOldServiceComponentId = $objWlrProduct->getComponentTypeID())) {
            return false;
        }


        $bolIsProductHierarchyUpgrade = CWlrProduct::isProductHierarchyUpgrade(
            $intOldServiceComponentId, $intNewServiceComponentId
        );

        if ($bolIsProductHierarchyUpgrade) {
            return true;
        }

        return false;
    } // function isProductUpgrade($intOldServiceComponentId, $intNewServiceComponentId)

    /**
     * Changing from a lesser product to a greater product?
     */
    public function isProductHierarchyUpgrade($intExistingServiceComponentID, $intNewServiceComponentID)
    {
        $arrProductHierarchyPriority = array(
            WLR_ANYTIME_COMPONENT_ID                 => 10, // WLR Anytime
            WLR_EVENING_WEEKEND_COMPONENT_ID         => 11, // WLR Evenings & Weekend
            WLR_ANYTIME_PLUS_COMPONENT_ID            => 20, // WLR Anytime+
            WLR_PN_TALK_ANYTIME_COMPONENT_ID         => 30, // Talk Anytime
            WLR_PN_TALK_EVENING_WEEKEND_COMPONENT_ID => 30
        ); // Talk E&w... Yup same value as Talk anytime as we
        //want them to change between themselves immediately

        $bolIsProductHierarchyUpgrade = false;

        $intExistingServiceComponentID += 0;
        $intNewServiceComponentID += 0;

        if ($arrProductHierarchyPriority[$intNewServiceComponentID] >= $arrProductHierarchyPriority[$intExistingServiceComponentID]) {
            $bolIsProductHierarchyUpgrade = true;
        }

        return $bolIsProductHierarchyUpgrade;
    } // function isProductHierarchyUpgrade($intExistingServiceComponentID, $intNewServiceComponentID)

    /**
     * Creates a product instance to add to the account
     *
     * <AUTHOR> Jones" <<EMAIL>>
     * @access   public
     * @static
     *
     * @param    integer     Service ID
     * @param    integer     Service Component ID (the component type to create)
     * @param    string      Telephone number
     * @param    array       Product Component options to add
     * @param    array       Optional Parameters, provided with keys:
     *                       strContractHandle   (MONTHLY|QUARTERLY|ANNUAL|NONE)
     *                       strPaymentFrequency (MONTHLY|QUARTERLY|ANNUAL|NONE)
     *
     * @return   integer Component Instance ID
     */
    public static function doSignup($intServiceId, $intServiceComponentId, $strCli,
                                    $arrProductComponentOptions, $arrOptParams = array())
    {

        // If no platform has been supplied, it is defaulted to Wlr3.
        $supplierPlatformType = isset($arrOptParams['strSupplierPlatform'])
            ? $arrOptParams['strSupplierPlatform']
            : CWlrProvisionType::WLR3;

        // Get my_id as we need it for tickets
        global $my_id;
        if (!isset($my_id) || is_null($my_id) || $my_id == '') {
            // The Script User
            $my_id = '********************************';
        }

        // Create the empty component class so we can set any errors if they arise;
        $objWlrProduct = new CWlrProduct(0);

        // Sanity check the ServiceComponent is of WLR type
        if (false === (CProduct::isValidProductComponentType($intServiceComponentId, 'WLR'))) {
            // Not a valid product component type
            $objWlrProduct->setError(
                __FILE__, __LINE__,
                "Service Component ID [{$intServiceComponentId}] is not a Home Phone Product."
            );

            return $objWlrProduct;
        }

        // 'bolLegacyHomePhoneSanityCheck' should *ALWAYS* be set to TRUE, especially when invoked via
        // the portal or Workplace.
        //
        // The only exception to this would be if you *really* need to force the addition of a depreciated
        // Home Phone product, such as component type 509. But in these cases, you'll be using a one-off script
        // to drive it, won't you? (like problem 39049 will be)

        $bolLegacyHomePhoneSanityCheck = (isset($arrOptParams['bolLegacyHomePhoneSanityCheck'])) ? $arrOptParams['bolLegacyHomePhoneSanityCheck'] : true;

        if ($bolLegacyHomePhoneSanityCheck) {

            // Sanity check that this product can be signed up to
            $arrServiceComponent = product_get_component($intServiceComponentId);
            if ($arrServiceComponent['available'] == 'No') {
                // Product Cannot be signed up to
                $objWlrProduct->setError(
                    __FILE__, __LINE__,
                    "Service Component ID [{$intServiceComponentId}] is not available for sign-up."
                );

                return $objWlrProduct;
            }
        }

        // Sanity check that this user doesn't already have a WLR Component
        if (false !== (CWlrProduct::getWlrProductFromServiceId($intServiceId))) {
            // Oo-er... this user already has WLR!

            $objWlrProduct->setError(
                __FILE__, __LINE__,
                "Service ID [{$intServiceId}] already has a Phone Product on their account."
            );

            return $objWlrProduct;
        }

        $strCli = trim($strCli);

        $arrProduct = userdataServiceProductGet($intServiceId);
        $isPnResidential = (
            $arrProduct['strProductType'] == 'residential' &&
            $arrProduct['strISP'] == 'plus.net'
        );

        // if we have CLI but we want to restart a stopped line
        // a new provide order needs to be sent, hence blanking the CLI.
        // This operation only applies for PN residential customers
        if (!empty($strCli) &&
            $isPnResidential &&
            isset($arrOptParams['placeNewProvideOrder']) &&
            $arrOptParams['placeNewProvideOrder'] === true &&
            $supplierPlatformType == CWlrProvisionType::WLR2
        ) {

            $strCli = null;
        }

        $takeOverLine = (
            isset($arrOptParams['takeOverLine']) &&
            $arrOptParams['takeOverLine'] === true
        );
        // Try and create a line rental object from a CLI
        // If the cli is blank, then we've got a new provide, and we shouldn't try and instantiate the object.
        if (!empty($strCli)) {

            $objWlrLineRent = CWlrProduct::getLineRentObjectFromCli($strCli);
        } else {

            $objWlrLineRent = null;
        }

        // If we can, then the CLI is probably in use,
        if (is_object($objWlrLineRent)) {

            // Let's dig further to check if it's a current one
            $arrUserConfig = $objWlrLineRent->getUserConfig();
        } else {

            // No object created, therefore the number hasn't been used... ever!
            $arrUserConfig = null;
        }

        // If we have a user configuration (and therefore a current number), and the CLI's are identical, then the number is current and in use.
        // We can't use the number, throw an error..
        if (is_array($arrUserConfig) && $arrUserConfig['vchCli'] == $strCli) {

            // Oo-er... we already have a config for this CLI!!!
            $objWlrProduct->setError(
                __FILE__, __LINE__,
                "An active WLR product already exists for CLI [" . $strCli . "]"
            );

            // is this a temporary ADSL upgrade account? If so we need to get the parent account
            // service ID to determine if the upgrade belongs to a ztest account.

            $arrAdslUpgradeInfo = userdata_is_service_adsl_upgrade($intServiceId);

            if (is_array($arrAdslUpgradeInfo) === true) {
                // it's an upgrade, thus get parent service's service ID

                $intServiceIdToCheck = $arrAdslUpgradeInfo['service_id'];
            } else {
                // it's not an upgrade, thus carry on as usual

                $intServiceIdToCheck = $intServiceId;
            }

            // let's check to see if this is a ztest account, and if it is then don't raise a ticket

            $arrServiceInfo = userdata_service_get($intServiceIdToCheck);

            if (preg_match('/^ztest/i', $arrServiceInfo['username']) != 1) {
                $arrServiceComponentDetails = CProduct::getServiceComponentDetails($intServiceComponentId);

                $strProductName = $arrServiceComponentDetails['strName'];

                $lineRentalSaverText = (isset($arrOptParams['bolLineRentalSaver']) && ($arrOptParams['bolLineRentalSaver'])) ? ' (with Line Rental Saver) ' : '';

                // Raise a ticket on the account
                $strTicketBody = "This question has been raised because a " . $strProductName . $lineRentalSaverText . " sign up was attempted, but failed because " .
                    "the telephone number $strCli is already in use on another " . $strProductName . " account. Please verify " .
                    "that this is the case, and resolve any issues with the account to allow the customer to sign up.";


                $intTeamId = PHPLibTeamIdGetByName('BOT WLR Provisioning Errors');

                // Raise a ticket
                tickets_ticket_add('Script', $intServiceId, 0, 0, 'Open', $my_id, $strTicketBody, 0, $intTeamId);
            }

            return $objWlrProduct;
        }

        $objWlrProduct = self::createWithComponents(
            $intServiceId, $intServiceComponentId, $strCli,
            $arrProductComponentOptions, $arrOptParams
        );

        // Greenbee and MAAF drop out for manual provisioning...
        if (
            $arrProduct['strISP'] == 'greenbee' ||
            $arrProduct['strISP'] == 'madasafish'
        ) {

            // If we've got engineering appointments
            // then add them to the ticket..
            $engineerAppointments = self::getEngineerAppointments($arrOptParams['arrProvisionDetails']);
            self::raiseTicketForWlrProvisioning($intServiceId, $engineerAppointments);

            return $objWlrProduct;
        }

        // If there are no errors and we have a Wlr3 supplier platform then handle accordingly
        if (false == $objWlrProduct->getError() &&
            $supplierPlatformType == CWlrProvisionType::WLR3 &&
            self::isWlr3OrderRequired($arrOptParams, $objWlrProduct)
        ) {

            try {
                self::handleWlr3Order(
                    $intServiceId,
                    $objWlrProduct,
                    $strCli,
                    $arrProductComponentOptions,
                    $arrOptParams
                );

            } catch (Exception $e) {
                $objWlrProduct->setError(__FILE__, __LINE__, $e->getMessage() .
                                                 "\n" . $e->getTraceAsString());

                $strJourneyType = $arrOptParams['arrProvisionDetails']['journeyType'];
                $strCli = empty($strCli) ? "Not available" : $strCli;

                $strJourneyDescription = $objWlrProduct->getSignupJourneyDescriptionByType($strJourneyType);

                $strTicketContent = "Unable to submit WLR order to be processed, this could be due to a temporary system issue.\n\n" .
                    "The following information is available about this order:\n" .
                    "* Journey: {$strJourneyType} - {$strJourneyDescription}\n" .
                    "* CLI: {$strCli} \n\n" .
                    "Please track this ticket and ensure the order is progressed and the WLR component is in the correct state.";

                $intTeamId = PHPLibTeamIdGetByName('BOT - Priority DJ');
                tickets_ticket_add('Script', $intServiceId, 0, 0, 'Open', $my_id, $strTicketContent, 0, $intTeamId);

                $strProblemDesc = <<<APDESC
Failed to send WLR order XML to the Java tier.  This should be investigated by LCST and a member of the
Platform Team should be informed as a JBOSS restart may be required.

Service ID: {$intServiceId}
CLI: {$strCli}
------------------------------
More information on the data sent and the response (PHP) may be available in:
- /share/admin/log/AuditLog/audit.log
------------------------------
The XML sent to Java may also be visible in the file below on pbe07/08:
- /local/services/jboss3/server/all/log/server.log
------------------------------
Errors may be visible following the logged XML which may help identify if the Platform Team are required.
APDESC;

                pt_raise_autoproblem(
                    'WLR_FAILED_XML_SUBMISSION',
                    "Failed to send WLR order XML to the Java tier",
                    $strProblemDesc
                );
            }
        }

        return $objWlrProduct;
    }

    /**
     * Given a signup journey, return the description of this journey type.
     *
     * @param string $strJourneyType The journey type code (A, B, C....)
     *
     * @return string The journey description
     */
    private function getSignupJourneyDescriptionByType($strJourneyType)
    {
        $strJourneyDescription = '';

        switch ($strJourneyType) {
            case 'A':
                $strJourneyDescription = 'Transfer';
                break;
            case 'B':
                $strJourneyDescription = 'MPF - Retainable';
                break;
            case 'C':
                $strJourneyDescription = 'MPF - No retain';
                break;
            case 'H':
                $strJourneyDescription = 'Cable new provide (retainable)';
                break;
            case 'M':
                $strJourneyDescription = 'Cable new provide (non retainable)';
                break;
            case 'TBC':
                $strJourneyDescription = 'Cable stopped line (retainable)';
                break;
            case 'N':
                $strJourneyDescription = 'Cable stopped line (non retainable)';
                break;
            case 'J':
                $strJourneyDescription = 'Start of Stop';
                break;
            case 'K':
                $strJourneyDescription = 'New provide';
                break;
            case 'L':
                $strJourneyDescription = 'Working line takeover';
                break;
            default:
                $strJourneyDescription = 'Unknown';
        }

        return $strJourneyDescription;
    }

    /**
     * If this is a manual order from the Add Phone tool - if so then the AddWlr module will have sent
     * the order so we do not want to duplicate it here.
     *
     * @param array $arrOptParams Provision details data from signup or add phone tool
     *
     * @return boolean Whether the WLR3 order needs to be sent.
     */
    protected static function isWlr3OrderRequired($arrOptParams, $objWlrProduct)
    {
        $strSID = $arrOptParams['service_definition_id'];

        $productAttributes = product_get_account_attributes($strSID);
        $isFTTC = (isset($productAttributes['vchProvisioningProfile']) && $productAttributes['vchProvisioningProfile'] == 'FTTC') ? true : false;

        if (isset($arrOptParams['arrProvisionDetails']['orderOrigin']) &&
            ($arrOptParams['arrProvisionDetails']['orderOrigin'] == Wlr3_OrderOrigin::ADD_PHONE_TOOL_MANUAL ||
            $arrOptParams['arrProvisionDetails']['orderOrigin'] == Wlr3_OrderOrigin::HOUSE_MOVE)
        ) {
            return false;

        } elseif (self::isCurrentJourneyTobeDisabled($arrOptParams, $isFTTC)) {
            self::raiseTicketForJourneyTobeDisabled($arrOptParams, $objWlrProduct, $isFTTC);

            return false;
        } else {
            return true;
        }
    }

    /**
     *This method is used to check the type of jouney type and based on which it tries to disable the legacy journey
     *
     * @param array   $arrOptParams Provision details data from signup or add phone tool
     * @param boolean $isFTTC       is the current product fibre
     *
     * @return boolean whether the journey needs disabling
     */
    protected static function isCurrentJourneyTobeDisabled($arrOptParams, $isFTTC)
    {
        $strJourneyType = $arrOptParams['arrProvisionDetails']['journeyType'];
        $globalIsp = $arrOptParams['global_isp'];
        $strAccType = $arrOptParams['account_type'];
        $disableJourney = false;

        // Dont disable legacy provisioning if isp is not PN or JLP
        // LA-215 : B and C biz and JLP are not in pega scope
        if (!in_array($globalIsp, array('plus.net', 'johnlewis'))) {
            return $disableJourney;
        }

        if (isset($strJourneyType)) {

            switch ($strJourneyType) {
                case 'L':
                case 'TBC':
                case 'H':
                case 'K':
                case 'M':
                case 'N':
                    $disableJourney = true;
                    break;

                case 'B':
                case 'C':
                    if (!$isFTTC || ($isFTTC && $globalIsp == "plus.net" && $strAccType == "residential")) {
                        $disableJourney = true;
                    }
                    break;

                case 'J':
                    if ($isFTTC) {
                        $disableJourney = true;
                    }
                    break;

                default:
                    $disableJourney = false;
            }
        } else {
            $disableJourney = false;
        }

        return $disableJourney;
    }

    /**
     * This method is used to raise ticket to disable legacy provisioning
     *
     * @param array   $arrOptParams gets the service id of the journey for which the ticket must be raised
     * @param boolean $isFTTC       is the current product fibre
     *
     * @return the ticket to be raised
     */
    protected static function raiseTicketForJourneyTobeDisabled($arrOptParams, $objWlrProduct, $isFTTC)
    {
        // MPF fibre scenarios do not need any tickets, return if current journey is MPF Fibre
        $strJourneyType = $arrOptParams['arrProvisionDetails']['journeyType'];

        if (($strJourneyType == 'B' || $strJourneyType == 'C') && $isFTTC) {
            return;
        }

        $journeyDesc = array(
            'A'   => 'Transfer',
            'B'   => 'MPF - CLI can be retained',
            'C'   => 'MPF - CLI cannot be retained',
            'TBC' => 'Cable - Stopped Line present - CLI can be retained',
            'N'   => 'Cable - Stopped Line present - CLI cannot be retained',
            'H'   => 'Cable - No stopped line Present - CLI can be retained',
            'M'   => 'Cable - No stopped line Present - CLI cannot be retained',
            'I'   => 'BT in-flight order',
            'J'   => 'Start of Stopped Line',
            'K'   => 'New Provide',
            'L'   => 'Working Line Takeover'
        );
        $strJourneyType = $arrOptParams['arrProvisionDetails']['journeyType'];

        $intTeamId = self::getTeamId($arrOptParams, $isFTTC);
        $my_id = '0764571063ddae82107fbd24d5955e0c';
        $intServiceId = $arrOptParams['service_id'];
        $strISPType = $arrOptParams['global_isp'];
        $strAccType = $arrOptParams['account_type'];

        if (isset($intTeamId, $my_id, $intServiceId, $strISPType, $strAccType)) {
            if ($strISPType == "johnlewis") {
                $contactUsLink = "<a href='https://www.johnlewisbroadband.com/contact-us/' target='_blank'>CONTACT US</a>";
            } elseif ($strISPType == "plus.net") {
                if ($strAccType == 'business') {
                    $contactUsLink = "<a href='https://www.plus.net/business/contact/' target='_blank'>CONTACT US</a>";
                } else {
                    $contactUsLink = "<a href='https://www.plus.net/home-broadband/contact/' target='_blank'>CONTACT US</a>";
                }
            }

            $callPackageName = 'N/A';
            $callPlanDetails = self::getCallPlanDetailsFromServiceComponentId($objWlrProduct->getServiceComponentID());
            if (!empty($callPlanDetails['strDisplayName'])) {
                $callPackageName = $callPlanDetails['strDisplayName'];
            }

            $manualAppt = 'N/A';
            if (!empty($arrOptParams['arrProvisionDetails']['appointmentdate1'])
                && !empty($arrOptParams['arrProvisionDetails']['appointmenttime1'])
            ) {
                $manualAppt = "&nbsp;&nbsp;&nbsp;&nbsp;* ";
                $manualAppt .= date('d/m/Y',
                                    $arrOptParams['arrProvisionDetails']['appointmentdate1']);

                $manualAppt .= ($arrOptParams['arrProvisionDetails']['appointmenttime1'] == 'AM' ? ' AM' : ' PM');
            }

            if (!empty($arrOptParams['arrProvisionDetails']['appointmentdate2'])
                && !empty($arrOptParams['arrProvisionDetails']['appointmenttime2'])
            ) {
                $manualAppt = ($manualAppt != 'N/A' && !empty($manualAppt)) ? $manualAppt . "<br/>&nbsp;&nbsp;&nbsp;&nbsp;* " : $manualAppt;
                $manualAppt .= date('d/m/Y',
                                    $arrOptParams['arrProvisionDetails']['appointmentdate2']);

                $manualAppt .= ($arrOptParams['arrProvisionDetails']['appointmenttime2'] == 'AM' ? ' AM' : ' PM');
            }

            if (!empty($arrOptParams['arrProvisionDetails']['appointmentdate3'])
                && !empty($arrOptParams['arrProvisionDetails']['appointmenttime3'])
            ) {
                $manualAppt = ($manualAppt != 'N/A' && !empty($manualAppt)) ? $manualAppt . "<br/>&nbsp;&nbsp;&nbsp;&nbsp;* " : $manualAppt;
                $manualAppt .= date('d/m/Y',
                                    $arrOptParams['arrProvisionDetails']['appointmentdate3']);

                $manualAppt .= ($arrOptParams['arrProvisionDetails']['appointmenttime3'] == 'AM' ? ' AM' : ' PM');
            }

            if (!empty($arrOptParams['arrProvisionDetails']['engineerAppointment'])) {
                $manualAppt = "&nbsp;&nbsp;&nbsp;&nbsp;* " . $arrOptParams['arrProvisionDetails']['engineerAppointment'] . " (live appointment)<br/>";
            }

            $callFeature = '';
            if (!empty($arrOptParams ['arrCallFeatures'])) {
                $callFeatures = array_filter(explode(',', $arrOptParams ['arrCallFeatures']));
                foreach ($callFeatures as $feature) {
                    $callFeature .= "&nbsp;&nbsp;&nbsp;&nbsp;* " . CProductComponent::getProductComponentDisplayNameFromHandle($feature) . "<br/>";
                }
            }

            $cliCanbeRetained = '';
            $isNewProvide = true;
            if (!empty($arrOptParams['intPresignupId'])) {
                $db = Db_Manager::getAdaptor('PlusnetPreSignup');
                $preSignupData = $db->getPresignupData($arrOptParams['intPresignupId']);
                $cliCanbeRetained = $preSignupData['bolCanRetainCli'] ? ' can' : ' cannot';
                $isNewProvide = (!empty($preSignupData['wlrLineType']) && strtolower($preSignupData['wlrLineType']) == 'newprovide') ? true : false;
            }

            $strTicketBody = "We are processing your orders and all updates will be amended to this ticket shortly.";
            $strTicketBody .= "If you have any queries please get in touch the details in $contactUsLink as updates to this ticket will not be picked up.<br/><br/>";

            $strTicketBody .= "[Internal]<br/><br/>";

            $strTicketBody .= "This ticket is being picked up by our automated system and updates will be provided on this ticket if work is required. Please do not updated this ticket prior to this being done<br/><br/>";

            $strTicketBody .= "This is a " . $journeyDesc[$strJourneyType] . ' (' . $strJourneyType . ($isFTTC ? ' Fibre' : ' Adsl') . ")<br/>";
            $strTicketBody .= "Call package: " . $callPackageName . "<br/>";
            $strTicketBody .= "Call Features: <br/>";
            $strTicketBody .= (empty($callFeature) ? "&nbsp;&nbsp;&nbsp;&nbsp;N/A<br/>" : $callFeature);
            $strTicketBody .= "Engineer Appointment Dates:<br/>";
            $strTicketBody .= ($manualAppt == 'N/A' ? '&nbsp;&nbsp;&nbsp;&nbsp;' : '') . $manualAppt . "<br/>";
            $strTicketBody .= "Engineer notes: " . (!empty($arrOptParams['arrProvisionDetails']['strEngineeringNotes']) ?
                    $arrOptParams['arrProvisionDetails']['strEngineeringNotes'] : 'N/A') . "<br/>";
            $strTicketBody .= "Data extension kit required: " . ($arrOptParams['dataExtensionKitRequired'] ? 'Yes' : 'No') . "<br/>";

            if (!$isNewProvide && !empty($preSignupData['vchCli'])) {
                $strTicketBody .= "The phone number " . $preSignupData['vchCli'] . $cliCanbeRetained . " be retained.<br/><br/>";
            }
            $ticketId = tickets_ticket_add(
                'Internal', $intServiceId, 0, 0, 'Open',
                $my_id, $strTicketBody, 0, $intTeamId
            );

            // LA-229: If initial payment is done by DD, then account will not be active immidiately
            // So ticket must be put on hold till DD is active or for a maximum of 14 days.
            // Any ticket on hold will be released by direct debit processing script
            if (isset($arrOptParams ['payment_type']) && $arrOptParams ['payment_type'] == 'dd') {
                Db_Manager::commit();

                $releaseDate = date('Y-m-d', strtotime('+14 days'));
                $wlrActor = Auth_BusinessActor::getActorByExternalUserId($my_id);

                $ticketClient = BusTier_BusTier::getClient('tickets');
                $ticket = $ticketClient->getTicket($ticketId);

                $ticket->hold(I18n_Date::fromString($releaseDate), $wlrActor);
                Db_Manager::commit();
            }
        }
    }


    /**
     * This method returns the Team id to raise the ticket
     *
     * @param array   $arrOptParams gets the service id of the journey for which the ticket must be raised
     * @param boolean $isFTTC       is the current product Fibre
     *
     * @return the teamId
     */
    protected static function getTeamId($arrOptParams, $isFTTC)
    {
        $strJourneyType = $arrOptParams['arrProvisionDetails']['journeyType'];
        $teamId = '';
        $teamName = '';

        if (isset($strJourneyType, $teamId)) {
            switch ($strJourneyType) {
                case 'K':
                    if ($isFTTC) {
                        $teamName = 'BOT - FTTC Provides';
                    } else {
                        $teamName = 'BOT - New PSTN Provides';
                    }
                    break;
                case 'J':
                    if ($isFTTC) {
                        $teamName = 'BOT - FTTC Provides';
                    }
                    break;
                case 'N':
                    $teamName = 'BOT - FTTC Provides';
                    break;
                case 'M':
                    $teamName = 'BOT - FTTC Provides';
                    break;
                case 'B':
                    $teamName = 'BOT - Phone Provisioning MPF';
                    break;
                case 'C':
                    $teamName = 'BOT - Phone Provisioning MPF';
                    break;
                case 'L':
                    if ($isFTTC) {
                        $teamName = 'BOT - FTTC Provides';
                    } else {
                        $teamName = 'BOT - Phone provisioning WLT';
                    }
                    break;
                case 'TBC':
                    $teamName = 'BOT - Phone Prov Cable Number port';
                    break;
                case 'H':
                    $teamName = 'BOT - Phone Prov Cable Number port';
                    break;

                default:
                    $teamName = '';
            }
        }
        $teamId = PHPLibTeamIdGetByName($teamName);

        return $teamId;
    }

    /**
     * Handle communication with the Wlr3 order platform (Currently Java Tier). We send everything
     * over to the java tier, and it is their responsibility to figure out which kind of order
     * should be sent. To enable this we send them everything we currently have. This is done in
     * self::getAdditionalWlr3OrderDetails(), so please do not clutter handleWlr3Order with getters
     * of information.
     *
     * @param int         $serviceId               The service id of the customer signing up
     * @param CWlrProduct $product                 Customers product object (CWlrProduct)
     * @param string      $cli                     Cli (may be empty)
     * @param array       $productComponentOptions Product component options
     * @param array       $params                  Extra paramters we probably need
     *
     * @uses Wlr3_PlaceOrder
     *
     * @return void
     */
    protected static function handleWlr3Order(
        $serviceId,
        CWlrProduct $product,
        $cli,
        array $productComponentOptions = array(),
        array $params = array()
    ) {
        // Need to get the product component instance ids for features, if applicable
        $features = $product->listProductComponentInstanceIdByHandle();

        // Get the engineer appointment data, if applicable
        $engineerAppointments = self::getEngineerAppointments($params['arrProvisionDetails']);

        $additionalData = self::getAdditionalWlr3OrderDetails(
            $serviceId,
            $product->getServiceComponentID(),
            $params,
            $cli
        );
        try {
            // Set up all the order
            $order = new Wlr3_PlaceOrder();

            // Order Type is null because Java Tier decides what order type to send
            $provideOrderType = null;
            $provide = new Wlr3_OrderTypeProvide(
                new Int($serviceId),
                new Int($product->getComponentID()),
                $provideOrderType,
                $features,
                $additionalData,
                $engineerAppointments
            );

            $order->addOrder($provide);

        } catch (Exception $e) {
            error_log("Failed to create Wlr3_OrderTypeProvide object.");

            throw $e;
        }

        $tryCount = 0;
        $retryWait = 0;
        $lastException = null;

        while ($tryCount != 3) {
            //Send the order to the Java tier
            try {
                //Sleep for our current delay value, there is no delay for the first call
                sleep($retryWait);

                $order->sendOrder();

            } catch (Exception $e) {
                $lastException = $e;

                //Increase the delay (15, 30) and decrease the try counter
                $retryWait = 15;
                $tryCount++;

                error_log("Exception caught when sending the order to the Java tier.  " .
                          "The exception message was: " . $e->getMessage());

                continue;
            }

            //No exception was returned on this try, return from here for success
            return;

        };

        //An exception was caught on all 4 tries, throw from here to allow all attempts to complete
        //before throwing the exception.
        throw $lastException;
    }

    /**
     * The WLR3 prov process as stipulated by SI has a rule that says a
     * phonebook entry cannot contain more than 3 words and can't contain
     * any punctuation characters; spaces are OK however.
     *
     * This method was created for http://jira.internal.plus.net/browse/RES-1047
     * to simply map the customer-friendly selected text to something that is
     * still sensible and WLR3-SI provisioning suitable.
     *
     * Return values are:
     *
     *  - returns NULL if the input is NULL.
     *  - returns a string if the supplied input was successfully
     *    mapped to a WLR3-compatible phrase.
     *
     * In the event of failure, an exception is raised.
     *
     * @param string $customerFriendlyNatureOfBusinessText
     *
     * @return mixed
     * @throws InvalidArgumentException
     * @throws LogicException
     **/
    protected static function mapNatureOfBusiness($customerFriendlyNatureOfBusinessText)
    {
        if ($customerFriendlyNatureOfBusinessText === null) {
            return null;
        }

        if (empty($customerFriendlyNatureOfBusinessText) ||
            !is_scalar($customerFriendlyNatureOfBusinessText)
        ) {

            throw new InvalidArgumentException(
                "unable to map nature-of-business because of invalid input. " .
                "The supplied variable is of type '" . gettype($customerFriendlyNatureOfBusinessText) .
                "' when a scalar/string type was expected"
            );
        }

        $pseudoHandle = strtoupper(
            preg_replace('/[^A-Za-z0-9]/', '', $customerFriendlyNatureOfBusinessText)
        );

        // Key-value mappings.
        //
        // Keys = pseudo-handle version of what customer selected during signup,
        // i.e. customer-friendly text version. These pseudo handles contain
        // uppercase/numerical versions of the user's selected choice and contain
        // no punctuation characters.
        //
        // Values = WLR3/SI prov compatible variant.
        //
        // Notice that most of them are identical.
        //
        // The "point" of this pseudo-handle stuff is to make the matching
        // less fuzzy.
        //
        // Ideally all the code like this that's duplicated everywhere would be
        // moved into a single location (IspPrimitives?) to be shared across
        // framework and legacy code.

        $businessNatures = array(
            'MARKETINGPRADVERTISING'           => 'Marketing PR Advertising',
            'PROFESSIONALANDFINANCIALSERVICES' => 'Professional Financial services',
            'ARCHITECTURE'                     => 'Architecture',
            'ITTELECOMSINTERNETPROVISION'      => 'IT Telecoms Internet',
            'CONSULTANT'                       => 'Consultant',
            'TRADE'                            => 'Trade',
            'EDUCATION'                        => 'Education',
            'CHARITY'                          => 'Charity',
            'HEALTHCAREANDSOCIALCARE'          => 'Healthcare Social Care',
            'SCIENCEANDENGINEERING'            => 'Science and Engineering',
            'WHOLESALERETAIL'                  => 'Wholesale Retail',
            'PROPERTY'                         => 'Property',
            'PRINTING'                         => 'Printing',
            'AGRICULTURE'                      => 'Agriculture'
        );

        if (!isset($businessNatures[$pseudoHandle])) {

            throw new LogicException(
                "unable to map nature-of-business due to the pseudo handle " .
                "'{$pseudoHandle}' not being recognised"
            );
        }

        return $businessNatures[$pseudoHandle];
    }

    /**
     * Retrieve all of the extra nuggets of information we may need for the Wlr3 order.
     * At the moment we need to pass the following extra information:
     * - The variables that are defined in $varsNeeded which comes from arrProvisionDetails in $params
     *
     * For the future it should be noted that any data being sent that is not captured
     * in signup or the add phone tool should be actually retrieved by the Java Tier
     * and not the PHP Tier
     *
     * @param int    $serviceId          The customers Service Id
     * @param int    $serviceComponentId The customers Service Component Id
     * @param array  $params             The extra bag of information we have been passed
     * @param string $cli                CLI (optional)
     *
     * @return array
     */
    protected static function getAdditionalWlr3OrderDetails(
        $serviceId,
        $serviceComponentId,
        array $params = array(),
        $cli = null
    ) {
        // Which vars we are interested in from $params
        $varsNeeded = array(
            'strContactName',
            'strContactNumber',
            'strExistingWiring',
            'intSparePairs',
            'installationType',
            'addressCategory',
            'strInstallationBuilding',
            'strInstallationStreet',
            'strInstallationCity',
            'strInstallationCounty',
            'addressReference',
            'cssDbCode',
            'journeyType',
            'liveAppointingFailed',
            'addressMatchFailed',
            'sim2Ref',
            'customerRequiredDate',
        );
        $additionalData = array();

        foreach ($params['arrProvisionDetails'] as $key => $value) {
            if (in_array($key, $varsNeeded)) {
                $additionalData[$key] = $value;
            }
        }

        $additionalData['cli'] = $cli;

        // Some additional information required for the order..
        $user = userdata_user_find(array('service_id' => $serviceId));
        if (is_array($user[0])) {
            $user = $user[0];
            $additionalData['strTitle'] = $user['salutation'];
            $additionalData['strContactName'] = $user['forenames'];
            $additionalData['strContactSurname'] = $user['surname'];
            $additionalData['postCode'] = $user['postcode'];

        } else {
            unset($user);
            $additionalData['strTitle'] = null;
            $additionalData['strContactName'] = null;
            $additionalData['strContactSurname'] = null;
            $additionalData['postCode'] = null;
        }

        // Sanity check -- if we've not got a GOLD address match for a journey K
        // (provide), then we need to supply the following additional data if
        // it's not come through already
        if (
            isset($additionalData['journeyType']) &&
            $additionalData['journeyType'] == 'K' &&
            (!isset($additionalData['addressCategory']) ||
                strtoupper($additionalData['addressCategory']) != 'GOLD') &&
            !isset($additionalData['strInstallationBuilding']) &&
            !isset($additionalData['strInstallationStreet']) &&
            !isset($additionalData['strInstallationCity'])
        ) {
            if (!isset($user)) {
                $user = array(
                    'house'  => null,
                    'street' => null,
                    'town'   => null
                );
            }
            $additionalData['strInstallationBuilding'] = $user['house'];
            $additionalData['strInstallationStreet'] = $user['street'];
            $additionalData['strInstallationCity'] = $user['town'];
        }

        // WLR3 Prov:
        //  If we've got a business account, then pass through some additional parameters that
        //  are required.
        if (isset($params['account_type']) && $params['account_type'] == 'business') {
            $additionalData['strLocation'] = 'Business';
            $additionalData['strBusinessName'] = isset($params['company_name']) ? $params['company_name'] : null;
            $additionalData['strBusinessDescription'] = self::mapNatureOfBusiness(
                isset($params['nature_of_business']) ? $params['nature_of_business'] : null
            );
        } else {
            $additionalData['strLocation'] = 'Residential';
            $additionalData['strBusinessName'] = null;
            $additionalData['strBusinessDescription'] = null;
        }

        // If the order is from the Account Change wizard then we must set the journey type to 'A' - Transfer.
        if (isset($params['arrProvisionDetails']['orderOrigin']) &&
            $params['arrProvisionDetails']['orderOrigin'] == Wlr3_OrderOrigin::ACCOUNT_CHANGE
        ) {

            $additionalData['journeyType'] = 'A';
        }

        // Get the call plan name
        $callPlanDetails = self::getCallPlanDetailsFromServiceComponentId($serviceComponentId);
        if (isset($callPlanDetails['strDisplayName'])) {

            $additionalData['callPackageType'] = $callPlanDetails['strDisplayName'];
        }

        // pass fttc sim provide flag to the java tier....
        $productAttributes = product_get_account_attributes($params['service_definition_id']);
        $simProvide = isset($productAttributes['vchProvisioningProfile'])
            && $productAttributes['vchProvisioningProfile'] == 'FTTC'
            && isset($params['journeyType'])
            && in_array($params['journeyType'], array('C', 'L', 'M', 'K', 'H', 'TBC', 'B'));

        // Only include the CLI with the order if the journey type is not one of the following:
        // * N (Cable - Stopped Line present - CLI cannot be retained)
        // * M (Cable - No stopped line Present - CLI cannot be retained)
        // i.e. one of the cable journeys where the CLI cannot be retained.
        // * J or K (normally the CLI won't be present, but if it is, we don't want to send it)
        // see P68213
        if (isset($params['journeyType']) &&
            in_array($params['journeyType'], array('N', 'M', 'J', 'K'))
        ) {

            unset($additionalData['cli']);
        }

        if (!empty($params['sim2Ref'])) {
            $additionalData['strOrderMatchRef'] = $params['sim2Ref'];
        }

        $additionalData['fttcSimProvide'] = false;
        if ($simProvide || $additionalData['strOrderMatchRef']) {
            $additionalData['fttcSimProvide'] = true;
        }

        if (in_array($params['journeyType'], array('B', 'C')) && isset($productAttributes['vchProvisioningProfile'])
            && $productAttributes['vchProvisioningProfile'] == 'FTTC'
        ) {
            $additionalData['isManualOrder'] = 'Y';
        }

        return $additionalData;
    }

    /**
     * Extract the engineer appointment data from provision details data from signup or add phone tool
     *
     * @param array $arrProvisionDetails Provision details data from signup or add phone tool
     *
     * @return Wlr3_Engineer_Appointments Engineer Appointments
     */
    protected static function getEngineerAppointments($arrProvisionDetails)
    {
        if (!in_array($arrProvisionDetails['journeyType'], array('H', 'K', 'M'))) {

            // If we are not on journeys H, K or M then no engineer appointment required
            return null;
        }

        if (!isset($arrProvisionDetails['engineerAppointment']) &&
            !isset($arrProvisionDetails['appointmentdate1'])
        ) {

            // No engineer appointment data available
            return null;
        }

        $engineerAppointments = new Wlr3_Engineer_Appointments();

        // Engineering notes
        if (isset($arrProvisionDetails['strEngineeringNotes'])) {

            $engineerAppointments->setEngineeringNotes($arrProvisionDetails['strEngineeringNotes']);
        }

        // Live appointing available
        if (isset($arrProvisionDetails['engineerAppointment'])) {

            // Need to split engineering appointment up into two fields, date and time of day
            // It comes in from signup as dd/mm/yyyy{AM|PM}
            $appointment = $arrProvisionDetails['engineerAppointment'];

            $endOfAppointment = strtoupper(substr($appointment, -2));

            if ($endOfAppointment == 'AM' || $endOfAppointment == 'PM') {

                $engineerAppointmentDate = substr($appointment, 0, strlen($appointment) - 2);
                $engineerAppointmentSlot = $endOfAppointment;

            } else {

                $engineerAppointmentDate = $appointment;

                // Should have a slot, but default to AM as a failsafe (better than loosing the appointment)
                $engineerAppointmentSlot = 'AM';
            }

            $engineerAppointments->addEngineerAppointment(
                new Wlr3_Engineer_Appointment($engineerAppointmentDate, $engineerAppointmentSlot)
            );
        }

        // Live appointing unavailable
        if (isset($arrProvisionDetails['appointmentdate1'])) {

            // Include the three alternative engineer appointments
            $engineerAppointments->addEngineerAppointment(
                new Wlr3_Engineer_Appointment(
                    date('d/m/Y', $arrProvisionDetails['appointmentdate1']),
                    strtoupper($arrProvisionDetails['appointmenttime1'])
                )
            );
            $engineerAppointments->addEngineerAppointment(
                new Wlr3_Engineer_Appointment(
                    date('d/m/Y', $arrProvisionDetails['appointmentdate2']),
                    strtoupper($arrProvisionDetails['appointmenttime2'])
                )
            );
            $engineerAppointments->addEngineerAppointment(
                new Wlr3_Engineer_Appointment(
                    date('d/m/Y', $arrProvisionDetails['appointmentdate3']),
                    strtoupper($arrProvisionDetails['appointmenttime3'])
                )
            );
        }

        return $engineerAppointments;
    }

    public function getActiveOptionalWlrProductComponents($bolIncludeFeaturesThatAreToBeRemoved = false)
    {
        $dbhConn = get_named_connection_with_db('userdata');

        // even if a customer has requested to remove a call feature, it's still active until
        // it is physically removed by the EDR on the necessary day. Thus, 'active' can mean
        // either features that really *are* active, or are active until they're removed.

        if ($bolIncludeFeaturesThatAreToBeRemoved) {
            $arrFeatureStatuses = array('ACTIVE', 'QUEUED_DEACTIVATE');
        } else {
            $arrFeatureStatuses = array('ACTIVE');
        }

        $strFeatureStatuses = implode("', '", $arrFeatureStatuses);

        $strQuery = "SELECT
                      pci.intProductComponentInstanceID as intProductComponentInstanceID
                   FROM
                      userdata.tblProductComponentInstance AS pci
                   INNER JOIN dbProductComponents.tblProductComponent pc
                      ON pc.intProductComponentID = pci.intProductComponentID
                   INNER JOIN dbProductComponents.tblStatus AS st
                      ON st.intStatusID = pci.intStatusID
                   INNER JOIN userdata.components AS c
                      ON c.component_id = pci.intComponentID
                   INNER JOIN dbProductComponents.tblProductComponentConfig pcc
                      ON pcc.intProductComponentID = pci.intProductComponentID
                   INNER JOIN dbProductComponents.tblWlrCallFeatureConfig cf
                      ON pcc.intProductComponentConfigID = cf.intProductComponentConfigID
                   INNER JOIN products.tblServiceComponentProduct scp
                      ON scp.intServiceComponentID = c.component_type_id
                      AND pcc.intServiceComponentProductID = scp.intServiceComponentProductID
                   INNER JOIN products.tblServiceComponentProductType scpt
                      ON scp.intServiceComponentProductTypeID = scpt.intServiceComponentProductTypeID
                   WHERE
                      pci.intComponentID = '{$this->m_intComponentID}' AND
                      st.vchHandle IN ('{$strFeatureStatuses}') AND
                      scpt.vchHandle = 'WLR' ";
        $refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, "CwlrProduct::getActiveOptionalWlrProductComponents");
        $arrProductComponentInstanceIDs = PrimitivesResultsAsListGet($refResult);

        $arrOptionalProductComponents = array();
        foreach ($arrProductComponentInstanceIDs as $intID) {
            $objCF = CProductComponent::createInstance($intID);
            if ($objCF != null)
                $arrOptionalProductComponents[$intID] = $objCF;
        }

        return $arrOptionalProductComponents;
    }

    /**
     * Returns the full name of this Product, including any extra strings like number of minutes included
     *
     *
     * @access public
     * <AUTHOR>
     * @return string full product name
     */
    public function getFullName()
    {
        return $this->m_strComponentName;
    }

    /**
     * Get the cost of signing up to this product.
     *
     *
     * @access public
     * @static
     * <AUTHOR>
     *
     * @param  integer The service ID of the
     * @param
     * @param
     *
     * @return
     */
    public function getSignupCost($intServiceID, $intServiceComponentID,
                                  $intTariffID, $uxtProRataCalculationDate = 0)
    {
        $arrTariffDetails = CProduct::getTariffDetails($intTariffID);
        $arrProRataCostDetails = CProductComponent::generateProRataPaymentDetails(
            $intServiceID, $intTariffID, 'WLR', $uxtProRataCalculationDate
        );
        $arrSignupCostDetails = array();

        $arrSignupCostDetails['arrProRataCostDetails'] = $arrProRataCostDetails;

        $arrSignupCostDetails['arrTariffDetails'] = $arrTariffDetails;

        return $arrSignupCostDetails;
    } // function getSignupCost($intServiceID, $intServiceComponentID, $intTariffID, $uxtProRataCalculationDate = 0)

    /**
     * Get the cost of upgrading to this product.
     *
     * Assumption: $intTariffID is a tariff ID that is *always* linked to a 'SUBSCRIPTION' product component.
     * Hmmm.. yes.. lets stress "ALWAYS". It should continue to do so unless
     * you want unexpected behaviour.
     * Also note this method is *not* backward compatible to what it used to return!
     *
     *
     * @access public
     * @static
     * <AUTHOR>
     *
     * @param                   int       service ID of account that wants to upgrade their WLR product.
     * @param                   int       service component ID of target WLR product
     * @param                   int       tariff ID of new SUBSCRIPTION-based tariff. It's important that
     *                                    this remains to be associated with a SUBSCRIPTION product component
     *                                    and nothing else (e.g. line rental). Wait; I already mentioned that
     *                                    in the above assumption :-)
     * @param uxt (depreciated) timestamp relating to prorata calculation. Maintaining
     *                                    parameter for those calls which supply 4 arguments.
     *
     * @return array on success, FALSE on failure
     */
    public function getUpgradeCost($intServiceID, $intServiceComponentID,
                                   $intTariffID, $uxtProRataCalculationDate = 0)
    {
        $arrOutput = array(
            'base' => array()
        );

        $arrOldTariffDetails = array();

        // Get the tariff details for their current WLR product, so we know how much
        // they're currently paying.

        $arrChargeableProductComponents = CProductComponent::getChargeableProductComponentsFromServiceComponent(
            $intServiceComponentID
        );

        if ($arrChargeableProductComponents == false) {
            return false;
        }


        foreach ($arrChargeableProductComponents as $strProductComponentHandle) {

            $intOldTariffId = CProductComponent::getExistingTariffIdForProductComponent(
                $intServiceID, 'WLR', $strProductComponentHandle
            );

            if ($intOldTariffId > 0) {

                $arrOldTariffDetails[$strProductComponentHandle] = CProduct::getTariffDetails($intOldTariffId);
            }
        }


        // We know the tariff ID of the requested new subscription, as it's given to us
        // via $intTariffID . What we need to know now is the tariff ID of the line rental
        // tariff for the new product.

        $intNewLineRentalTariffId = CWlrLineRent::getTariffIdFromServiceComponent($intServiceComponentID);

        if ($intNewLineRentalTariffId == false) {
            return false;
        }

        $arrNewTariffDetails = array(
            'SUBSCRIPTION' => CProduct::getTariffDetails($intTariffID),
            'WlrLineRent'  => CProduct::getTariffDetails($intNewLineRentalTariffId)
        );

        foreach ($arrNewTariffDetails as $strProductComponentHandle => $arrNewTariff) {

            $arrOutput['base'][$strProductComponentHandle] = $arrNewTariff['intCostIncVatInPence'] -
                $arrOldTariffDetails[$strProductComponentHandle]['intCostIncVatInPence'];
        }

        return $arrOutput;
    } // function getUpgradeCost($intServiceID, $intServiceComponentID, $intTariffID, $uxtProRataCalculationDate = 0)

    /**
     * Get active call features budle product component
     */
    public function getActiveCallFeaturesBundle()
    {
        return $this->getCallFeaturesBundle(array(PRODUCT_COMPONENT_ACTIVE));
    }

    /**
     * Get call features budle product component
     *
     * @param     array $arrStates States the bundle are required to be in
     *
     * @access public
     * <AUTHOR>
     * @return object - active call features bundle product component
     */
    public function getCallFeaturesBundle(array $arrStates)
    {
        $arrActiveBundle = $this->getSelectedProductComponentInstanceIDs(
            CWlrCallFeaturesBundle::getAllTypes(), $arrStates
        );
        //if there is more tah one active bundle rise a problem
        if (count($arrActiveBundle) > 1) {
            //rise a problem
            $objProblem = new CAutoProblem('WLRCallFeaturesBundleException');
            $objProblem->setProblemTitle('More than one call features bundle on the account ' . $this->getServiceId());

            $strDescription = "Account " . $this->getServiceId() . " has more than one active call features bundle\n";
            $strDescription .= "List of the active call features bundles :\n";
            foreach ($arrActiveBundle as $intBundleId) {
                $objBundle = CProductComponent::createInstance($intBundleId);
                $strDescription .= $objBundle->getDisplayName() . "\n";
            }

            $strDescription .= "\nList of call features :\n";
            $arrFeatures = $this->getChargableBundlableCallFeatures();
            foreach ($arrFeatures as $intFeatureId) {
                $objCF = CProductComponent::createInstance($intFeatureId);
                $strDescription .= $objCF->getDisplayName() . "\n";
            }
            $strDescription .= "\nThere should be only one active call features bundle an it should be:\n";
            $intBundleType = CWlrCallFeaturesBundle::getTypeByAmountOfFeatures(
                count($arrFeatures), $this->getComponentTypeID()
            );
            $strDescription .= CProductComponent::getProductComponentDisplayName($intBundleType) . "\n";
            $strDescription .= "Please remove needless call features bundles from the account.";

            $objProblem->setProblemDescription($strDescription);
            $objProblem->setProblemAdditionalComment("Service ID: " . $this->getServiceId());
            $objProblem->raiseProblem();
        }

        $objCurrentCFBundle = (!isset($arrActiveBundle[0])) ? null : CProductComponent::createInstance($arrActiveBundle[0]);

        return $objCurrentCFBundle;
    }

    /**
     * Get all valid call features budle product component
     *
     *
     * @access public
     * <AUTHOR> Jones <<EMAIL>>
     * @return object - active call features bundle product component
     */
    public function getValidCallFeaturesBundle()
    {
        $arrStates = array(
            PRODUCT_COMPONENT_UNCONFIGURED,
            PRODUCT_COMPONENT_QUEUED_ACTIVATE,
            PRODUCT_COMPONENT_QUEUED_REACTIVATE,
            PRODUCT_COMPONENT_ACTIVE,
            PRODUCT_COMPONENT_QUEUED_DEACTIVATE,
            PRODUCT_COMPONENT_QUEUED_DECONFIGURE,
            PRODUCT_COMPONENT_DEACTIVE
        );

        $arrValidBundle = $this->getSelectedProductComponentInstanceIDs(
            CWlrCallFeaturesBundle::getAllTypes(), $arrStates
        );
        //if there is more tah one active bundle rise a problem
        if (count($arrValidBundle) > 1) {
            //rise a problem
            $objProblem = new CAutoProblem('WLRCallFeaturesBundleException');
            $objProblem->setProblemTitle('More than one call features bundle on the account ' . $this->getServiceId());

            $strDescription = "Account " . $this->getServiceId() . " has more than one valid call features bundle\n";
            $strDescription .= "List of the valid call features bundles :\n";
            foreach ($arrValidBundle as $intBundleId) {
                $objBundle = CProductComponent::createInstance($intBundleId);
                $strDescription .= $objBundle->getDisplayName() . "\n";
            }

            $strDescription .= "\nList of call features :\n";
            $arrFeatures = $this->getChargableBundlableCallFeatures();
            foreach ($arrFeatures as $intFeatureId) {
                $objCF = CProductComponent::createInstance($intFeatureId);
                $strDescription .= $objCF->getDisplayName() . "\n";
            }
            $strDescription .= "\nThere should be only one active call features bundle an it should be:\n";
            $intBundleType = CWlrCallFeaturesBundle::getTypeByAmountOfFeatures(
                count($arrFeatures), $this->getComponentTypeID()
            );
            $strDescription .= CProductComponent::getProductComponentDisplayName($intBundleType) . "\n";
            $strDescription .= "Please remove needless call features bundles from the account.";

            $objProblem->setProblemDescription($strDescription);
            $objProblem->setProblemAdditionalComment("Service ID: " . $this->getServiceId());
            $objProblem->raiseProblem();
        }

        $objCurrentCFBundle = (!isset($arrValidBundle[0])) ? null : CProductComponent::createInstance($arrValidBundle[0]);

        return $objCurrentCFBundle;
    }

    /**
     * Get all  active call features
     *
     *
     * @access public
     * <AUTHOR>
     * @return array of ids
     */
    public function getActiveCallFeatures()
    {
        return $this->getSelectedProductComponentInstanceIDs(
            CWlrCallFeature::getAllCallFeaturesTypes(), array(PRODUCT_COMPONENT_ACTIVE)
        );
    }

    /**
     * Get active call features instances
     *
     *
     * @access public
     * <AUTHOR>
     * @return array of objects
     */
    public function getActiveCallFeaturesInstances()
    {
        $arrCallFeaturesIds = $this->getActiveCallFeatures();
        $arrRet = array();

        foreach ($arrCallFeaturesIds as $intId) {
            $arrRet[] = CProductComponent::createInstance($intId);
        }

        return $arrRet;
    }

    /**
     * Gets active bundle call features
     */
    public function getActiveBundlableCallFeatures()
    {
        return $this->getBundlableCallFeatures(array(PRODUCT_COMPONENT_ACTIVE));
    }

    /**
     * Get bundlable call features
     *
     * @param  array $arrStates States the features are required to be in
     *
     * @access public
     * <AUTHOR>
     * @return array of ids
     */
    public function getBundlableCallFeatures(array $arrStates)
    {
        //get only active Call Features that can be bundled
        return $this->getSelectedProductComponentInstanceIDs(
            CWlrCallFeature::getBundleableCallFeatures($this->getComponentTypeID()),
            $arrStates
        );
    }

    /**
     * Get valid Bundlable Call Features
     */
    public function getValidBundlableCallFeatures()
    {
        $arrStates = array(
            PRODUCT_COMPONENT_UNCONFIGURED,
            PRODUCT_COMPONENT_QUEUED_ACTIVATE,
            PRODUCT_COMPONENT_QUEUED_REACTIVATE,
            PRODUCT_COMPONENT_ACTIVE,
            PRODUCT_COMPONENT_QUEUED_DEACTIVATE,
            PRODUCT_COMPONENT_QUEUED_DECONFIGURE,
            PRODUCT_COMPONENT_DEACTIVE
        );

        // get unconfigured, active, deactive call features that can be bundled
        return $this->getSelectedProductComponentInstanceIDs(
            CWlrCallFeature::getBundleableCallFeatures($this->getComponentTypeID()),
            $arrStates
        );
    }

    /**
     * Get chargable bundlable call features
     *
     *
     * @access public
     * <AUTHOR>
     * @return array of ids
     */
    public function getChargableBundlableCallFeatures()
    {
        //get only active Call Features that can be bundled
        return $this->getSelectedProductComponentInstanceIDs(
            CWlrCallFeature::getBundleableCallFeatures($this->getComponentTypeID()),
            array(PRODUCT_COMPONENT_ACTIVE, CWlrCallFeature::getRemovalStatus())
        );
    }

    /**
     * Gets bundleable call features in the desired state(s)
     *
     * @param array $arrStates States features required to be in
     */
    public function getBundlableCallFeaturesInstances(array $arrStates)
    {
        $arrCallFeaturesIds = $this->getBundlableCallFeatures($arrStates);
        $arrRet = array();

        foreach ($arrCallFeaturesIds as $intId) {
            $arrRet[] = CProductComponent::createInstance($intId);
        }

        return $arrRet;
    }

    /**
     * Get active bundlable call features instances
     *
     *
     * @access public
     * <AUTHOR>
     * @return array of objects
     */
    public function getActiveBundlableCallFeaturesInstances()
    {
        $arrCallFeaturesIds = $this->getActiveBundlableCallFeatures($this->getComponentTypeID());
        $arrRet = array();

        foreach ($arrCallFeaturesIds as $intId) {
            $arrRet[] = CProductComponent::createInstance($intId);
        }

        return $arrRet;
    }

    /**
     * Get active no bundleable call features
     */
    public function getActiveNoBundlableCallFeatures()
    {
        return $this->getNoBundlableCallFeatures(array(PRODUCT_COMPONENT_ACTIVE));
    }

    /**
     * Get no bundlable call features
     *
     * @param    array $arrStates States components required to be in
     *
     * @access public
     * <AUTHOR>
     * @return array of ids
     */
    public function getNoBundlableCallFeatures(array $arrStates)
    {
        //get only active Call Features that can be bundled
        return $this->getSelectedProductComponentInstanceIDs(
            CWlrCallFeature::getNoBundleableCallFeatures($this->getComponentTypeID()),
            $arrStates
        );
    }

    /**
     * Gets nobundle call features instance in the required state
     *
     * @param array $arrStates States nobundle features are required to be in
     */
    public function getNoBundlableCallFeaturesInstances(array $arrStates)
    {
        $arrCallFeaturesIds = $this->getNoBundlableCallFeatures($arrStates);
        $arrRet = array();

        foreach ($arrCallFeaturesIds as $intId) {
            $arrRet[] = CProductComponent::createInstance($intId);
        }

        return $arrRet;
    }

    /**
     * Get active nobundlable call features instances
     *
     *
     * @access public
     * <AUTHOR>
     * @return array of objects
     */
    public function getActiveNoBundlableCallFeaturesInstances()
    {
        $arrCallFeaturesIds = $this->getActiveNoBundlableCallFeatures();
        $arrRet = array();

        foreach ($arrCallFeaturesIds as $intId) {
            $arrRet[] = CProductComponent::createInstance($intId);
        }

        return $arrRet;
    }

    /**
     * Get cost of call features
     *
     *
     * @access public
     * <AUTHOR>
     * @return array of objects
     */
    public function getCurrentCallFeaturesCost()
    {
        $objCFB = $this->getActiveCallFeaturesBundle();
        if (is_object($objCFB)) {
            $intCost = $objCFB->getCurrentCost();
        } else {
            $arrCF = $this->getChargableBundlableCallFeatures();

            $intCFCost = 0;
            foreach ($arrCF as $intId) {
                $objCF = CProductComponent::createInstance($intId);
                $intCFCost += $objCF->getCurrentCost();
                unset($objCF);
            }
            $intCost = $intCFCost;
        }

        //add cost of no bundlable features
        $arrNonBundleableCF = $this->getSelectedProductComponentInstanceIDs(
            CWlrCallFeature::getNoBundleableCallFeatures($this->getComponentTypeID()),
            array(
                PRODUCT_COMPONENT_QUEUED_DEACTIVATE,
                PRODUCT_COMPONENT_ACTIVE
            )
        );

        if (is_array($arrNonBundleableCF)) {

            $intNonBundleableCFCost = 0;
            foreach ($arrNonBundleableCF as $intId) {
                $objCF = CProductComponent::createInstance($intId);
                $intNonBundleableCFCost += $objCF->getCurrentCost();
                unset($objCF);
            }
            $intCost += $intNonBundleableCFCost;
        }
        if ($this->isBusinessAccount()) {
            $arrCostInfo = CFinancialHelper::splitVat($intCost * 100);
            $intCost = $arrCostInfo['exvat'] / 100;
        }

        return $intCost;
    }

    /**
     * Gets the current list of call features assigned to the WLR component
     *
     * @return array list of features containing Bundable and Non-bundable features
     */
    public function getCurrentCallFeatures()
    {
        $arrFeatures = array(
            'arrBundleableCallFeatures'    => array(),
            'arrNonBundleableCallFeatures' => array()
        );

        // Get Active Bundable Call Features
        $arrBundleableCallFeatures = $this->getActiveBundlableCallFeaturesInstances();
        if (is_array($arrBundleableCallFeatures)) {
            $arrFeatures['arrBundleableCallFeatures'] =
                $this->populateCallFeatures($arrBundleableCallFeatures);
        }

        // Get Active Non-Bundable Call Features
        $arrNoBundleableCallFeatures = $this->getActiveNoBundlableCallFeaturesInstances();
        if (is_array($arrNoBundleableCallFeatures)) {
            $arrFeatures['arrNonBundleableCallFeatures'] =
                $this->populateCallFeatures($arrNoBundleableCallFeatures);
        }

        return $arrFeatures;
    }

    /**
     * Populates the list of call features with: Handle, Price, Display Name and Is a Bundle
     *
     * @param array $arrRawCallFeatures array of features to populate with
     *
     * @return an array of call features
     */
    private function populateCallFeatures(array $arrRawCallFeatures)
    {
        $arrCallFeatures = array();

        foreach ($arrRawCallFeatures as $objCallFeature) {

            $arrCallFeatures[$objCallFeature->getHandle()] =
                array(
                    'strHandle'      => $objCallFeature->getHandle(),
                    'floPrice'       => $objCallFeature->getCurrentCost() / 100,
                    'strDisplayName' => $objCallFeature->getDisplayname()
                );

            unset($objCallFeature);
        }

        return $arrCallFeatures;
    }

    //returns call features order state

    public function getCallFeaturesOrderState($intWlrComponentID = '', $intWlrComponentTypeID = '')
    {
        if ($intWlrComponentID == '' && is_object($this)) {
            $intWlrComponentID = $this->getComponentId();
            $intWlrComponentTypeID = $this->getComponentTypeID();
        }
        if ($intWlrComponentTypeID == '') {
            $arrComponentDetails = CProduct::getComponentDetailsByComponentID($intWlrComponentID);
            $intWlrComponentTypeID = $arrComponentDetails[0]['component_type_id'];
        }

        $arrFeatureTypes = CWlrCallFeature::getAllCallFeatures($intWlrComponentTypeID);

        $arrFeaturesUnc = self::getSelectedProductComponentInstanceIDs(
            $arrFeatureTypes, array(PRODUCT_COMPONENT_UNCONFIGURED), $intWlrComponentID
        );

        $arrFeaturesQa = self::getSelectedProductComponentInstanceIDs(
            $arrFeatureTypes, array(PRODUCT_COMPONENT_QUEUED_DEACTIVATE), $intWlrComponentID
        );

        if ($arrFeaturesUnc) {

            if ($arrFeaturesQa) {
                return 'ACTIVE_ORDER_BOTH';

            } else {
                return 'ACTIVE_ORDER_ADD';

            }
        } else {

            if ($arrFeaturesQa) {
                return 'ACTIVE_ORDER_REMOVE';

            } else {
                return null;

            }
        }
    }

    public function doUpgrade($objNewProduct, $intSalesInvoiceID = 0, $uxtStartDate = 0,
                              $intFreeMonths = 0, $uxtDateRefPoint, $bolRetentionOffer = false, $uxtContractEnd = null)
    {
        //validate new object
        if ($this->IsValidToUpgrade($objNewProduct, $intSalesInvoiceID)) {
            $intNewWlrComponentId = $objNewProduct->getComponentID();
            $intOldWlrComponentId = $this->getComponentID();

            if ($bolRetentionOffer) {
                CWlrProduct::markAsRetentionOffer($intNewWlrComponentId);
            }

            $objEventLogger = $this->prvGetEventLogger();
            $objEventLogger->logWlrProductChangeRequest($intOldWlrComponentId);

            $this->cancelAllOutstandingProductScheduledActions($intOldWlrComponentId, $objNewProduct);
            //cancel all outstanding scheduled product cancellation

            CWlrProduct::cancelAllOutstandingProductCancellation($intOldWlrComponentId);

            // Home Phone Refresh 2009 - now we don't schedule wlr changes, but apply them instantly
            CWlrProduct::changeAccountType(
                $intOldWlrComponentId, $intNewWlrComponentId,
                false, $uxtDateRefPoint, $uxtContractEnd
            );
            $objEventLogger->logWlrProductChange($intNewWlrComponentId);

            $objNewProduct->refreshInstance($objNewProduct->getComponentID());
            $this->refreshInstance($this->getComponentID());

            $bolUpgrade = false;
            if ($objNewProduct->getStatus() == 'active' && $this->getStatus() == 'destroyed') {
                $objEventLogger->logWlrProductChange($objNewProduct->getComponentID());
                $bolUpgrade = true;
            }


            unset($objEventLogger);

            return $bolUpgrade;
        }
    }

    /**
     * schedule a wlr account change
     * Refactored and copied from DatabaseAccessCommon/database_libraries/CWlrUpgrade.inc
     * Done as part of HPR2009 development
     *
     *
     * @access public
     * <AUTHOR>
     * @return array of things to be used by doDowngrade in DatabaseAccessCommon/database_libraries/CWlrUpgrade.inc
     */
    public static function scheduleComponentChange($intServiceID,
                                                   $intServiceComponentID, $intTariffID, $bolRetentionOffer = false, $uxtContractEnd = null)
    {
        $arrReturn = array();

        $uxtDateDue = CProductComponent::getCustomerExistingContractExpiredDate($intServiceID, 'WLR', 'WlrLineRent');
        //product change should be scheduled on the next day after the contract expired
        $uxtDateDue = $uxtDateDue + 24 * 3600;

        $intExistingComponentID = CProduct::getComponentIDByServiceID($intServiceID, 'WLR');
        $objNewWlrComponent = CWlrProduct::create(
            $intServiceID, $intServiceComponentID, $intTariffID, false, '', $uxtDateDue, array()
        );
        $intNewComponentID = $objNewWlrComponent->getComponentID();

        if ($bolRetentionOffer) {
            CWlrProduct::markAsRetentionOffer($intNewComponentID);
        }

        CProduct::cancelAllOutstandingProductScheduledActions($intExistingComponentID);
        $objEvent = CProductChange::create(
            $intExistingComponentID, $intNewComponentID,
            $uxtDateDue, true, $uxtContractEnd
        );
        $arrTariffDetails = CProduct::getTariffDetails($intTariffID);
        $objExistingComponent = new CComponent($intExistingComponentID);
        $strExistingComponentName = $objExistingComponent->getComponentName();

        $objNewComponent = new CComponent($intNewComponentID);
        $strNewComponentName = $objNewComponent->getComponentName();

        //raise a ticket for action
        $strTicketContent = "Product change scheduled successfully.\n" .
            'Existing product: ' . $strExistingComponentName . "\n" .
            'New product: <b>' . $strNewComponentName . "</b>\n" .
            'Due date: <b>' . date('d/m/Y', $uxtDateDue) . "</b>\n\n";

        tickets_ticket_add('Script', $intServiceID, 0, 0, 'Closed', 0, $strTicketContent);

        //Return this array to be used by DoDowngrade() in DatabaseAccessCommon/database_libraries/CWlrUpgrade.inc
        $arrReturn['strAction'] = 'DOWNGRADE';
        $arrReturn['intServiceComponentID'] = $intNewComponentID;
        $arrReturn['intExistingServiceComponentID'] = $intExistingComponentID;
        $arrReturn['strExistingProductName'] = $strExistingComponentName;
        $arrReturn['strProductName'] = $strNewComponentName;
        $arrReturn['strContractName'] = $arrTariffDetails['strContractName'];
        $arrReturn['uxtStartDate'] = $uxtDateDue;
        $arrReturn['intScheduledEventID'] = $objEvent->m_intScheduledEventID;

        return $arrReturn;
    }

    /**
     * Change type of WLR component, or destroy non-bundle - create bundle component if required by service defionition
     * Method called in userdata_service_account_type_change() and adslUpgradeMakeActive(). $intOldWlrComponentId is
     * id of existing WLR (from userdata.components), $intNewWlrComponentTypeId is type of new WLR,
     * $intNewWlrComponentId is optional and it is id of new created (or moved if it's upgrade from temporary account)
     * WLR - in that case no new component will be created
     *
     * @param integer $intServiceId
     * @param integer $intNewServiceDefId
     * @param integer $intOldWlrComponentId
     * @param integer $intNewWlrComponentTypeId
     * @param integer $intNewWlrComponentId
     */
    public static function changeAccountTypeHandler($intServiceId,
                                                    $intNewServiceDefId, $intOldWlrComponentId, $intNewWlrComponentTypeId,
                                                    $intNewWlrComponentId = 0)
    {
        // Fetch the list of all available (including optional) component_type_id's
        $arrServiceComponentsIds = array();
        $arrServiceComponentsIdsData = ProductGetAllComponentsForService($intNewServiceDefId);

        foreach ($arrServiceComponentsIdsData as $arrType) {
            $arrServiceComponentsIds[] = $arrType['intServiceComponentID'];
        }

        $arrWlrTypes = array();
        $arrWlrTypeData = CProduct::getProductComponentTypes('WLR');

        foreach ($arrWlrTypeData as $arrType) {
            $arrWlrTypes[] = $arrType['intServiceComponentID'];
        }

        // Get from database (in case of CLI update)
        $arrService = userdata_service_get($intServiceId, true);

        // Create new WLR component if required
        if ($intNewWlrComponentTypeId && $intNewWlrComponentTypeId > 0 && $intNewWlrComponentId == 0) {

            if ($intOldWlrComponentId && $intOldWlrComponentId > 0) {

                // If there id old WLR component use createWithComponents to avoid duplicate component errors
                $objNewWlrProduct = CWlrProduct::createWithComponents(
                    $intServiceId, $intNewWlrComponentTypeId,
                    $arrService['cli_number'], array(), array()
                );
            } else {

                $objNewWlrProduct = CWlrProduct::doSignup(
                    $intServiceId, $intNewWlrComponentTypeId,
                    $arrService['cli_number'], array()
                );
            }

            // thank goodness you're here, bizarrely circumlocutory error handling mechanism!
            if (($objError = $objNewWlrProduct->getError())) {

                error_log(
                    'Error adding WLR component during account type change: ' .
                    $objError->getErrorMessage() . ' @ ' .
                    $objError->getSourceFile() . ':' .
                    $objError->getLineNumber()
                );
            } else {

                $intNewWlrComponentId = $objNewWlrProduct->getComponentId();
            }
        }

        if ($intOldWlrComponentId && $intOldWlrComponentId > 0) {

            // Created new type of WLR component, change old one to new one
            if ($intNewWlrComponentId && $intNewWlrComponentId > 0) {

                CWlrProduct::changeAccountType(
                    $intOldWlrComponentId, $intNewWlrComponentId,
                    true, time()
                );
            }

            // else
            // Do not destroy WLR component, service need to keep it even if not match to service definition (eg. PAYG)
            // To destroy call: CWlrProduct::createInstance($intOldWlrComponentId)->destroy();
        }
    }

    /**
     * Transfer account and configurations from old component to new
     * This is particularly used during the account upgrade process
     * Enables the new account and destroys the old
     *
     * @param  int    intOldComponentId      Component Id for the component migrating from
     * @param  int    intNewComponentId      Component Id for the component migrating to
     * @param  bool   bolSheduledChange      Determines if is a scheduled change
     * @param  date   uxtDateRefPoint        Date account type change occuring
     * @param  date   uxtContractEnd         Contract End Date
     * @param  bool   bolHpRef2009Migration  Determines if HP 2009 migrating account
     *
     * @return bool
     */
    public function changeAccountType($intOldComponentId, $intNewComponentId,
                                      $bolSheduledChange = false, $uxtDateRefPoint, $uxtContractEnd = null,
                                      $bolHpRef2009Migration = false)
    {
        $objOldWlrProduct = CWlrProduct::createInstance($intOldComponentId);
        $arrOldWlrComponent = $objOldWlrProduct->listProductComponentInstanceIdByHandle();

        $objNewWlrProduct = CWlrProduct::createInstance($intNewComponentId);
        $arrNewWlrComponent = $objNewWlrProduct->listProductComponentInstanceIdByHandle();

        //if it is scheduled change, create call features
        if ($bolSheduledChange) {
            //get optional product components from old component
            $arrOptionalProductComponents = $objOldWlrProduct->getOptionalProductComponents(array('ACTIVE'));
            $arrNewFeatures = array_keys($arrNewWlrComponent);

            $arrBundleTypes = CwlrCallFeaturesBundle::getAllTypes();

            $intServiceId = null;

            //go through all product components and create new ones on the new wlr component
            foreach ($arrOptionalProductComponents as $objProductComponent) {
                $intServiceId = $objProductComponent->getServiceID();

                // Problem 50698 - we should check if the feature already exists before adding it again..
                if (!in_array($objProductComponent->getHandle(), $arrNewFeatures) &&
                    !in_array($objProductComponent->getProductComponentID(), $arrBundleTypes)
                ) {
                    CProductComponent::create(
                        $objProductComponent->getServiceID(), $intNewComponentId,
                        $objProductComponent->getProductComponentId(), 0, false,
                        'WLR', $uxtDateRefPoint
                    );
                }
            }

            CWlrProduct::createBundleIfApplicable($intServiceId, $objNewWlrProduct);
        }

        //
        // Minutes to add
        //
        $arrConfig = array();

        $objNewCalltime = CProductComponent::createInstance($arrNewWlrComponent['WlrCallTime']);
        $arrConfig = $objNewCalltime->getConfig();

        //We want to give the full minutes in case of downgrade
        if ($bolSheduledChange) {
            //just in case this is not set
            $intMinutesToAdd = count($arrConfig) > 0 ? $arrConfig['intMinutes'] : 0;
        } else {
            $objOldCalltime = CProductComponent::createInstance($arrOldWlrComponent['WlrCallTime']);

            $intSecondsUsedForFraudCalls = $objOldCalltime->getSecondsUsedForFraudCalls();
            $intSecondsUsedForCdrCalls = $objOldCalltime->getSecondsUsedForCdrCalls();

            $intMinutesUsedOnOldProduct = round(($intSecondsUsedForFraudCalls / 60) + ($intSecondsUsedForCdrCalls / 60));

            $intMinutesToAdd = $arrConfig['intMinutes'] - $intMinutesUsedOnOldProduct;
        }

        //
        // Transfer Configurations
        //

        // Get the Call Plan ID for this product type
        $intServiceComponentId = $objNewWlrProduct->getComponentTypeID();
        if (!$intCallPlanId = CWlrProduct::getCallPlanIdFromServiceComponentId($intServiceComponentId)) {
            $intCallPlanId = 0;
        }

        // Clone the old Line Rent config to the new
        $intWlrLineRentInstanceId = $arrNewWlrComponent['WlrLineRent'];
        $objWlrLineRentProdCompIns = CProductComponent::createInstance($intWlrLineRentInstanceId);
        $intWlrLineRentConfigId = $objWlrLineRentProdCompIns->transferUserConfig(
            $arrOldWlrComponent['WlrLineRent'], $intCallPlanId, $bolHpRef2009Migration
        );

        // Clone the old PAYG config to the new
        $intWlrPaygInstanceId = $arrNewWlrComponent['WlrPayg'];
        $objWlrPaygProdCompIns = CProductComponent::createInstance($intWlrPaygInstanceId);
        $intWlrPaygConfigId = $objWlrPaygProdCompIns->transferUserConfig($arrOldWlrComponent['WlrPayg']);

        // Transfer the credit from the old PAYG component to the new
        $objOldPaygProdCompIns = CProductComponent::createInstance($arrOldWlrComponent['WlrPayg']);
        $intAvailableCredit = $objOldPaygProdCompIns->getCredit();

        if ($intAvailableCredit > 0) {
            // Transfer old credit to new component
            CWlrPayg::transferCredit(
                $arrOldWlrComponent['WlrPayg'], $arrNewWlrComponent['WlrPayg'], $intAvailableCredit
            );
        }

        if (isset($arrOldWlrComponent['FavouriteNumbers']) && $arrOldWlrComponent['FavouriteNumbers'] > 0) {
            // clone favourite numbers
            $objNewFavouriteNumbers = null;
            if (isset($arrNewWlrComponent['FavouriteNumbers']) && $arrNewWlrComponent['FavouriteNumbers'] > 0) {
                $objNewFavouriteNumbers = CProductComponent::createInstance($arrNewWlrComponent['FavouriteNumbers']);
            } else {
                $objNewFavouriteNumbers = CProductComponent::create(
                    $objOldWlrProduct->getServiceID(), $intOldComponentId,
                    PRODUCT_COMPONENT_WLR_FAVOURITE_NUMBER
                );
            }

            $objOldFavouriteNumbers = null;
            $objOldFavouriteNumbers = CProductComponent::createInstance($arrOldWlrComponent['FavouriteNumbers']);

            if ($objOldFavouriteNumbers instanceof CWlrFavouriteNumber
                && $objNewFavouriteNumbers instanceof CWlrFavouriteNumber
            ) {
                $objNewFavouriteNumbers->createUserConfig();
                $objNewFavouriteNumbers->transferUserConfig($objOldFavouriteNumbers->getUserConfigId());
            }
        }

        //
        // Reschedule the payments
        //

        //cancel all outstanding scheduled payments

        // The Home Phone 2009 refresh is going to introduce the concept of charging folks
        // using the line rental product component instance too. By my reckoning, once
        // people start actively using the new products, we'll want to cancel any pending
        // scheduled payments over on the line rental product component as well.
        // Therefore, lets look for product components that are chargeable, and not just
        // the SUBSCRIPTION product component.

        $arrChargableProductComponents = $objOldWlrProduct->getChargableProductComponents();   // this gets active ones only

        $isOldWlrProductDeactive = ($objOldWlrProduct->getStatus() == 'deactive') ? true : false;

        foreach ($arrChargableProductComponents as $objProductComponent) {

            $objPaymentScheduler = new CProductComponentPaymentScheduler(
                $objProductComponent->getProductComponentInstanceID(), ''
            );

            // If the old wlr product was in deactive state, there would be some oustanding scheduled payments that are due.
            // We need to charge them and only cancel the future scheduled payments.
            $objPaymentScheduler->cancelAllOutstandingPayments(0, $isOldWlrProductDeactive);
        }

        // Home Phone 2009 - jiggle contract end date if old product was on an annual contract.
        // See the ASCII art at http://documentation.plus.net/index.php/Plusnet:applications:wlr:hpr_res_2009
        // to explain what this is for.

        $objOldSubscriptionComponent = CProductComponent::createInstance($arrOldWlrComponent['SUBSCRIPTION']);

        $strContractLengthHandle = CProductComponent::getContractLengthHandleFromTariffID(
            $objOldSubscriptionComponent->getTariffID()
        );

        if ($strContractLengthHandle == 'ANNUAL') {

            $uxtContractEnd = $objOldSubscriptionComponent->getContractEnd();
        }

        // create scheduled payments if no retention offer is in place.

        $arrRetentionOffer = CWlrProduct::getRetentionOfferDetails($intNewComponentId);
        $bolRetentionOffer = count($arrRetentionOffer) > 0 && $arrRetentionOffer['dtmEnd'] == '' ? true : false;

        if ($bolRetentionOffer == false) {

            $arrChargableProductComponents =
                $objNewWlrProduct->getChargableProductComponents(
                    array('ACTIVE', 'UNCONFIGURED'),
                    array(PRODUCT_COMPONENT_ACTIVE, PRODUCT_COMPONENT_UNCONFIGURED)
                );

            foreach ($arrChargableProductComponents as $objProductComponent) {

                // Generatate new scheduled payments
                $strHandle = $objProductComponent->getHandle();

                // If an old instance of this product component exists
                // then use its next invoice date, otherwise default to
                // the SUBSCRIPTION next invoice date
                if (isset($arrOldWlrComponent[$strHandle])) {
                    $objOldProductComponent = CProductComponent::createInstance(
                        $arrOldWlrComponent[$strHandle]
                    );
                    $uxtStartDate = $objOldProductComponent->getNextInvoiceDate();
                } else {
                    $uxtStartDate = $objOldSubscriptionComponent->getNextInvoiceDate();
                }

                //get new tariff ID
                $intTariffID = $objProductComponent->getTariffID();

                // If there is one (should be, it's chargeable) then make scheduled payments.
                // Technically there should *always* be a tariff, but its possible that a
                // next invoice date is NULL. This can happen for call features when a
                // bundle product component is attached to the component. In this situation,
                // the bundle product component has a valid next invoice date and the call
                // feature has NULL for the invoice date, as the call feature is indirectly
                // billed for as part of the bundle's charge.

                if (!empty($intTariffID) && $uxtStartDate > 0) {
                    $objStartDate = I18n_Date::fromTimestamp($uxtStartDate);
                    $objContractEnd = (empty($uxtContractEnd)) ? null : I18n_Date::fromTimestamp($uxtContractEnd);
                    $arrScheduledPaymentDetails = $objProductComponent->generateScheduledPaymentsDetailsForContract(
                        $intTariffID, $objStartDate, 0, $objContractEnd
                    );
                    $objProductComponent->createScheduledPayments($arrScheduledPaymentDetails);
                }
            }
        }

        // Transfer any retention details from the old WLR product onto the new one.

        $objNewWlrProduct->transferRetentionOffer($objOldWlrProduct);

        //
        // Destroy Old WLR Product and Enable New WLR Product
        //

        // Is this all we have to do now?
        // We only want to charge for outstanding calls when scheduling the product change
        $arrDestructionParams = array(
            'bolFullCancellation' => $bolSheduledChange,
            'uxtCancelled'        => $uxtDateRefPoint - 30,
            'bolPayCallCharges'   => false
        );    // End at specified date

        $objOldWlrProduct->destroy($arrDestructionParams);

        // enable the new product
        $objNewWlrProduct->enable(
            array(
                'uxtEnableDate'    => $uxtDateRefPoint,
                'bolSkipContracts' => true,
                'bolSkipBounty'    => true,
                'uxtContractEnd'   => $uxtContractEnd
            )
        );

        $arrFeatures = $objNewWlrProduct->getSelectedProductComponentInstanceIDs(
            CWlrCallFeature::getBundleableCallFeatures($objNewWlrProduct->getComponentTypeID()),
            array(PRODUCT_COMPONENT_ACTIVE)
        );

        if (count($arrFeatures) > 1) {

            foreach ($arrFeatures as $callFeatureId) {

                $feature = &CProductComponent::createInstance($callFeatureId);
                $schedulePayments = $feature->getScheduledPayments();

                foreach ($schedulePayments as $schedulePaymentDetails) {
                    $schedulePayment = new CScheduledPayment($schedulePaymentDetails['intScheduledPaymentID']);
                    $schedulePayment->cancel();
                }
                $feature->setNextInvoiceDate(null);
            }
        }


        // If the old product was in deactive state, we want the new component to be in deactive state as well.
        if ($isOldWlrProductDeactive == true) {

            $objNewWlrProduct->setStatus('deactive');

        }

        //expire the minutes we have added
        $objNewCalltime->deleteCalltime();
        $objNewCalltime->addIncludedMinutes($intMinutesToAdd ? $intMinutesToAdd : 0);

        return true;
    }

    /**
     *
     */
    public function getProductNameFromContractID($intProductComponentContractID)
    {
        $dbhConnection = get_named_connection_with_db('product');

        $strQuery = 'SELECT DISTINCT sc.name as strProductName ' .
            ' FROM service_components sc ' .
            ' INNER JOIN userdata.components c ON sc.service_component_id = c.component_type_id ' .
            ' INNER JOIN userdata.tblProductComponentInstance pci ON c.component_id = pci.intComponentID ' .
            ' INNER JOIN userdata.tblProductComponentContract pcc ON pci.intProductComponentInstanceID = pcc.intProductComponentInstanceID ' .
            " AND pcc.intProductComponentContractID = '$intProductComponentContractID'";
        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

        return PrimitivesResultGet($resResult, 'strProductName');

    } // End of function getProductNameFromContractID

    /**
     * Get the periods for itemised billing using the Subscription contract dates
     *
     * @access public
     * <AUTHOR>
     *
     * @param  int Service ID
     *
     * @return arr Itemised Billing Periods
     */
    public function getItemisedBillingPeriods($intServiceID)
    {
        $dbhConnection = get_named_connection_with_db('userdata_reporting');

        $strQuery = "    SELECT DISTINCT pcc.intProductComponentContractID, " .
            "           UNIX_TIMESTAMP(pcc.dteContractStart) AS uxtStart, " .
            "           UNIX_TIMESTAMP(COALESCE(pcc.dtmCancelled, " .
            "                (pcc.dteContractEnd + interval 1 day - interval 1 second))) AS uxtEnd, " .
            "           UNIX_TIMESTAMP(pcc.dtmCancelled) AS uxtCancelled " .
            "      FROM tblProductComponentContract pcc  " .
            "INNER JOIN tblProductComponentInstance pci " .
            "        ON (pcc.intProductComponentInstanceID = pci.intProductComponentInstanceID) " .
            "INNER JOIN dbProductComponents.tblProductComponent pc " .
            "        ON pci.intProductComponentID = pc.intProductComponentID " .
            "       AND pc.vchHandle = 'WlrLineRent' " .
            "INNER JOIN components c " .
            "        ON (pci.intComponentID = c.component_id " .
            "       AND c.service_id = '{$intServiceID}') " .
            "INNER JOIN dbProductComponents.tblTariff t " .
            "        ON (t.intTariffID = pci.intTariffID) " .
            "INNER JOIN dbProductComponents.tblProductComponentConfig pccf " .
            "        ON (pccf.intProductComponentConfigID = t.intProductComponentConfigID) " .
            "INNER JOIN products.tblServiceComponentProduct scp " .
            "        ON (scp.intServiceComponentProductID = pccf.intServiceComponentProductID) " .
            "INNER JOIN products.tblServiceComponentProductType scpt " .
            "        ON (scpt.intServiceComponentProductTypeID = scp.intServiceComponentProductTypeID) " .
            "     WHERE pcc.dteContractStart <= NOW() " .
            "       AND pcc.dteContractStart>=DATE_SUB(NOW(),INTERVAL 12 MONTH) " .
            "       AND scpt.vchHandle = 'WLR' " .
            "  ORDER BY uxtEnd DESC, pcc.intProductComponentContractID DESC ";

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);
        $arrPeriods = PrimitivesResultsAsArrayGet($resResult);

        //in case of upgrade next period should start from cancel date nod contract start day
        foreach ($arrPeriods as $i => $arrPeriod) {

            if (isset($arrPeriods[$i + 1])) {
                //if this period start day matches previous cancel date replace it
                if (date("Ymd", $arrPeriod['uxtStart']) == date("Ymd", $arrPeriods[$i + 1]['uxtCancelled'])) {

                    $arrPeriod['uxtStart'] = $arrPeriods[$i + 1]['uxtCancelled'];
                }
            }
            $arrTmp[] = $arrPeriod;
        }

        return $arrTmp;

    } // End of function getItemisedBillingPeriods

    public function getItemisedBillingCallSummaryForPeriod($intServiceId, $strType, $uxtStart, $uxtEnd)
    {
        $arrWlrLineRentInsId = CWlrProduct::getAllLineRentInstanceIdFromServiceId($intServiceId);

        if ($arrWlrLineRentInsId === false || empty($arrWlrLineRentInsId)) {
            return false;
        }

        $strQuery = '';
        $strCommonSelect = "SELECT COUNT(*) as total_calls, \n" .
            "       SUM(intRoundedDurationSeconds) total_duration, \n" .
            "       SUM(intCustomerPricePence) total_cost \n" .
            "  FROM tblCall c \n" .
            "    INNER JOIN dbProductComponents.tblCallDestination cd \n" .
            "      ON cd.intCallDestinationId=c.intCallDestinationId \n" .
            "    INNER JOIN dbProductComponents.tblCallType ct \n" .
            "      ON ct.intCallTypeId=cd.intCallTypeId \n";
        $strCommonWhere = 'c.intProductComponentInstanceId IN (' . implode(', ', $arrWlrLineRentInsId) . ")\n" .
            "    AND c.dtmProcessCompleted BETWEEN CAST(FROM_UNIXTIME('{$uxtStart}') AS DATETIME) \n" .
            "        AND CAST(FROM_UNIXTIME('{$uxtEnd}') AS DATETIME)";

        switch ($strType) {
            case 'uklandline':
                $strQuery = $strCommonSelect .
                    "    INNER JOIN dbProductComponents.tblCountry cc \n" .
                    "      ON cc.intCountryId=cd.intCountryId \n" .
                    "  WHERE ct.vchDisplayName = 'LandLine' \n" .
                    "    AND cc.vchDisplayName='Great Britain' \n" .
                    "    AND " . $strCommonWhere;
                break;

            case 'mobile':
                $strQuery = $strCommonSelect .
                    "    INNER JOIN dbProductComponents.tblCountry cc \n" .
                    "      ON cc.intCountryId=cd.intCountryId \n" .
                    "  WHERE ct.vchDisplayName='Mobile' \n" .
                    "    AND cc.vchDisplayName='Great Britain' \n" .
                    "    AND " . $strCommonWhere;
                break;

            case 'international':
                $strQuery = $strCommonSelect .
                    "    INNER JOIN dbProductComponents.tblCountry cc \n" .
                    "      ON cc.intCountryId=cd.intCountryId \n" .
                    "  WHERE cc.vchDisplayName!='Great Britain' \n" .
                    "    AND " . $strCommonWhere;
                break;

            case 'other':
                $strQuery = $strCommonSelect .
                    "  WHERE ct.vchDisplayName NOT IN ('LandLine','Mobile') \n" .
                    "    AND " . $strCommonWhere;
                break;

            case 'total':
                $strQuery = $strCommonSelect .
                    "  WHERE " . $strCommonWhere;
                break;
        } // switch($strType)

        $dbhConnection = get_named_connection_with_db('wlr_calls_reporting');
        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

        $arrResults = PrimitivesResultsAsArrayGet($resResult);

        $intHour = str_pad(floor($arrResults[0]['total_duration'] / 3600), '2', '0', STR_PAD_LEFT);
        $intMins = str_pad(floor($arrResults[0]['total_duration'] / 60) - ($intHour * 60), '2', '0', STR_PAD_LEFT);
        $intSec = str_pad($arrResults[0]['total_duration'] - ($intHour * 3600) - ($intMins * 60), '2', '0', STR_PAD_LEFT);
        $strTotalDuration = "$intHour:$intMins:$intSec";

        //the total number of calls made
        if (isset($arrResults[0]['total_calls']) === true) {
            $arrReturn['total_calls_' . $strType] = $arrResults[0]['total_calls'];
        } else {
            $arrReturn['total_calls_' . $strType] = 0;
        }

        //the total call duration
        $arrReturn['duration_' . $strType] = $strTotalDuration;

        //the total cost
        $arrReturn['total_cost_' . $strType] = $arrResults[0]['total_cost'];

        return $arrReturn;
    }

    /**
     * Get the next invoice date based on the subscription component
     *
     * <AUTHOR> Jones" <<EMAIL>>
     * @static
     * @access   public
     *
     * @param    integer $intServiceID                 - Service ID
     * @param    string  $strServiceCompProdTypeHandle - the Service Component Product Type Handle
     *
     * @return   uxt     Unix timestamp
     */
    public function getNextInvoiceDate($intServiceID, $strServiceCompProdTypeHandle = '')
    {
        return CProduct::getNextInvoiceDate($intServiceID, 'WLR');
    }

    /**
     * Get call charges for a given period
     *
     *
     * @access public
     * @return int
     */
    public function getCallChargesForPeriod($intServiceID, $uxtStart, $uxtEnd)
    {
        $arrWlrLineRentInsId = CWlrProduct::getAllLineRentInstanceIdFromServiceId($intServiceID);

        if ($arrWlrLineRentInsId === false || empty($arrWlrLineRentInsId)) {
            return false;
        }
        $strSQL = "
                SELECT SUM(intCustomerPricePence) as intCallCharges
                FROM
                tblCall c
                WHERE
                c.intProductComponentInstanceId IN (" . implode(', ', $arrWlrLineRentInsId) . ")
                AND
                c.dtmProcessCompleted BETWEEN CAST(FROM_UNIXTIME('$uxtStart') AS DATETIME)
                    AND CAST(FROM_UNIXTIME('$uxtEnd') AS DATETIME);
                ";
        $dbhConnection = get_named_connection_with_db('wlr_calls_reporting');
        $resResult = PrimitivesQueryOrExit($strSQL, $dbhConnection);

        return PrimitivesResultGet($resResult, 'intCallCharges');
    }

    /**
     * getIncludedMinutes - gets included minutes from the tarrif of the product
     *
     * @access public
     * @return int - included minutes
     */
    public function getIncludedMinutes()
    {
        $arrComponentStatus = array('ACTIVE', 'UNCONFIGURED', 'QUEUED_ACTIVATE');

        $intProductComponentInstanceId = CProductComponent::getProductComponentInstance(
            $this->m_intServiceID, 'WlrCallTime', $arrComponentStatus
        );

        if ($intProductComponentInstanceId > 0) {
            $objCalltime = CProductComponent::createInstance($intProductComponentInstanceId);

            $arrConfig = $objCalltime->getConfig();

            return isset($arrConfig['intMinutes']) ? $arrConfig['intMinutes'] : 0;
        }

        return 0;
    }

    /**
     * Get included minutes for a given period
     *
     *
     * @access public
     * @return int
     */
    public function getIncludedMinutesForPeriod($intServiceID, $uxtStart, $uxtEnd)
    {

        $intProductComponentInstanceId = CProductComponent::getProductComponentInstance(
            $intServiceID, 'WlrCallTime'
        );

        if (isset($intProductComponentInstanceId) && $intProductComponentInstanceId > 0) {
            $objWlrCalltime = CProductComponent::createInstance($intProductComponentInstanceId);

            return $objWlrCalltime->getCurrentIncludedMinutes($uxtStart, $uxtEnd);
        } else {
            return 0;
        }
    }

    /**
     * Get the subscription charges for the period specified.
     * If bolUnpaid is set to TRUE then retrieve only unpaid subscription charges
     *
     * <AUTHOR> Jones" <<EMAIL>>
     * @static
     * @access   public
     *
     * @param    integer Service ID
     * @param    integer Period Start Time as Unix Timestamp
     * @param    integer Period End Time as Unit Timestamp
     * @param    boolean Retrieve only Unpaid Scheduled payments
     */
    public function getSubscriptionChargesForPeriod($intServiceID, $uxtStart, $uxtEnd, $uxtCancelled)
    {
        $intProductComponentInstanceID = CProductComponent::getProductComponentInstanceForPeriod(
            $intServiceID, 'SUBSCRIPTION', $uxtStart, $uxtEnd, 'WLR', $uxtCancelled
        );
        //Replace the query from this function to get the tariff from database. This code would be replaced after 2 days of launch of
        //HPR for rpr pricing
        if ($intProductComponentInstanceID > 0) {

            //get connection
            $dbhConnection = get_named_connection_with_db('financial_reporting');

            //escaping
            $intProductComponentInstanceID = PrimitivesRealEscapeString(
                $intProductComponentInstanceID, $dbhConnection
            );
            $dteStart = PrimitivesRealEscapeString(date('Y-m-d', $uxtStart), $dbhConnection);
            $dteEnd = PrimitivesRealEscapeString(date('Y-m-d', $uxtEnd), $dbhConnection);

            //query to retrieve the invoiced price by product component instance ID and invoice period.
            $strSQL = '
             SELECT
              intAmountExVatPence + intVatPence AS cost
             FROM
              financial.tblScheduledPayment sp
             INNER JOIN
              financial.tblConfigProductComponent cpc
             ON cpc.intScheduledPaymentID = sp.intScheduledPaymentID
             WHERE
             cpc.intProductComponentInstanceID = ' . $intProductComponentInstanceID . '
             AND sp.dteStartPeriodCovered = "' . $dteStart . '" AND dteEndPeriodCovered = "' . $dteEnd . '"
            ';

            //run query and return result
            $resResult = PrimitivesQueryOrExit($strSQL, $dbhConnection);
            $cost = PrimitivesResultGet($resResult, 'cost');

            if ($cost > 0) {
                return $cost;
            }

            $objSub = CProductComponent::createInstance($intProductComponentInstanceID);

            return $objSub->getCurrentCost();
        }

        return null;
    } // function getSubscriptionChargesForPeriod($intServiceID, $uxtStart, $uxtEnd, $uxtCancelled)


    /**
     * Get the subscription charges for the period specified.
     *
     * <AUTHOR> Zaki" <<EMAIL>>
     * @static
     * @access   public
     *
     * @param        integer Service ID
     * @param        integer Period Start Time as Unix Timestamp
     * @param        integer Period End Time as Unit Timestamp
     * @param        integer date cancelled as Unix TimeStamp
     */
    public static function getCallPlanChargesForPeriod($intServiceID, $uxtStart, $uxtEnd, $uxtCancelled)
    {
        $intProductComponentInstanceID = CProductComponent::getProductComponentInstanceForPeriod(
            $intServiceID, 'WlrLineRent', $uxtStart, $uxtEnd, 'WLR', $uxtCancelled
        );

        if ($intProductComponentInstanceID > 0) {

            //get connection
            $dbhConnection = get_named_connection_with_db('financial_reporting');

            //escaping
            $intProductComponentInstanceID = PrimitivesRealEscapeString(
                $intProductComponentInstanceID, $dbhConnection
            );
            $dteStart = PrimitivesRealEscapeString(date('Y-m-d', $uxtStart), $dbhConnection);
            $dteEnd = PrimitivesRealEscapeString(date('Y-m-d', $uxtEnd), $dbhConnection);

            //query to retrieve the invoiced price by product component instance ID and invoice period.
            $strSQL = '
             SELECT
              intAmountExVatPence + intVatPence AS cost
             FROM
              financial.tblScheduledPayment sp
             INNER JOIN
              financial.tblConfigProductComponent cpc
             ON cpc.intScheduledPaymentID = sp.intScheduledPaymentID
             WHERE
             cpc.intProductComponentInstanceID = ' . $intProductComponentInstanceID . '
             AND sp.dteStartPeriodCovered = "' . $dteStart . '" AND dteEndPeriodCovered = "' . $dteEnd . '"
             AND sp.vchDescription LIKE "Call Plan%"
            ';

            //run query and return result
            $resResult = PrimitivesQueryOrExit($strSQL, $dbhConnection);
            $cost = PrimitivesResultGet($resResult, 'cost');

            if ($cost > 0) {
                return $cost;
            }

            $objLineRent = CProductComponent::createInstance($intProductComponentInstanceID);

            return $objLineRent->getCurrentCost();
        }

        return 0;

    } // function getCallPlanChargesForPeriod($intServiceID, $uxtStart, $uxtEn, $uxtCancelled)

    /**
     * Returns all possible call feature handles (including bundles)
     *
     * @access public static
     * @return array list of call features
     */
    public function getAvailableCallFeatureHandles()
    {
        $strSQL = 'SELECT DISTINCT vchHandle
            FROM dbProductComponents.tblProductComponent pc
            INNER JOIN dbProductComponents.tblProductComponentConfig pcc
            ON pcc.intProductComponentID = pc.intProductComponentID
            INNER JOIN dbProductComponents.tblWlrCallFeatureConfig cf
            ON cf.intProductComponentConfigID = pcc.intProductComponentConfigID
            UNION
            SELECT DISTINCT vchHandle
            FROM dbProductComponents.tblProductComponent pc
            INNER JOIN dbProductComponents.tblProductComponentConfig pcc
            ON pcc.intProductComponentID = pc.intProductComponentID
            INNER JOIN dbProductComponents.tblWlrCallFeaturesBundleConfig cf
            ON cf.intProductComponentConfigID = pcc.intProductComponentConfigID';

        $dbhConnection = get_named_connection_with_db('dbProductComponents_reporting');
        $resResult = PrimitivesQueryOrExit($strSQL, $dbhConnection);

        return PrimitivesResultsAsListGet($resResult);
    }

    /**
     * Get the call feature charges for the period specified.
     * If bolUnpaid is set to TRUE then retrieve only unpaid subscription charges
     *
     * @access public static
     *
     * @param integer Service ID
     * @param integer Period Start Time as Unix Timestamp
     * @param integer Period End Time as Unit Timestamp
     * @param boolean Retrieve only Unpaid Scheduled payments
     *
     * @return integer
     */
    public function getCallFeaturesChargesForPeriod($intServiceID, $uxtStart, $uxtEnd, $bolUnpaid = true)
    {
        $arrCallFeatureHandles = CWlrProduct::getAvailableCallFeatureHandles();
        $arrProductComponentInstanceIDs = CProductComponent::getProductComponentInstanceArrayForPeriod(
            $intServiceID, $arrCallFeatureHandles, $uxtStart, $uxtEnd, 'WLR'
        );

        if (empty($arrProductComponentInstanceIDs))
            return 0; //charge for call features = �0

        $strProductComponentInstanceIDs = implode(',', $arrProductComponentInstanceIDs);
        $strSQL = " SELECT SUM(sp.intAmountExVatPence+sp.intVatPence) as intCallFeatureChargesForPeriod " .
            " FROM financial.tblScheduledPayment sp " .
            " INNER JOIN financial.tblConfigProductComponent cpc " .
            "    ON sp.intScheduledPaymentID = cpc.intScheduledPaymentID " .
            " WHERE cpc.intProductComponentInstanceID IN($strProductComponentInstanceIDs) " .
            " AND sp.dteStartPeriodCovered >= FROM_UNIXTIME('$uxtStart') " .
            " AND sp.dteStartPeriodCovered <= FROM_UNIXTIME('$uxtEnd') " .
            " AND sp.dtmCancelled IS NULL ";
        if ($bolUnpaid === true) {
            $strSQL .= ' AND sp.intSalesInvoiceID IS NULL ';
        }

        $dbhConnection = get_named_connection_with_db('financial_reporting');
        $resResult = PrimitivesQueryOrExit($strSQL, $dbhConnection);

        return PrimitivesResultGet($resResult, 'intCallFeatureChargesForPeriod');
    }

    public function getMinutesUsedForPeriod($intServiceID, $uxtStart, $uxtEnd)
    {
        $arrWlrLineRentInsId = CWlrProduct::getAllLineRentInstanceIdFromServiceId($intServiceID);

        if ($arrWlrLineRentInsId === false || empty($arrWlrLineRentInsId)) {
            return false;
        }

        $strSQL = "
                SELECT
                sum(intDurationSeconds) as intMinutesUsed
                FROM
                        tblCall c
                INNER JOIN
                        tblCallBillingSection cbs
                                ON cbs.intCallId=c.intCallId
                INNER JOIN
                        tblIncludedSecondsRedemption isr
                                ON isr.intCallBillingSectionId=cbs.intCallBillingSectionId
                WHERE
                        c.intProductComponentInstanceId IN (" . implode(', ', $arrWlrLineRentInsId) . ")
                AND
                        c.dtmStart BETWEEN CAST(FROM_UNIXTIME('$uxtStart') AS DATETIME)
                            AND CAST(FROM_UNIXTIME('$uxtEnd') AS DATETIME);";

        $dbhConnection = get_named_connection_with_db('wlr_calls_reporting');
        $resResult = PrimitivesQueryOrExit($strSQL, $dbhConnection);

        return PrimitivesResultGet($resResult, 'intMinutesUsed');
    }

    /**
     * Get the inclusive minutes on all applicable products
     *
     * @access public
     * @static
     * <AUTHOR>
     * @return arr Service Components with minutes
     */
    public function getAllProductsWithInclusiveMinutes()
    {
        $dbhConnection = get_named_connection_with_db('dbProductComponents');

        $strQuery = 'SELECT CAST(scp.intServiceComponentID AS UNSIGNED INTEGER) AS intServiceComponentID, ' .
            '       SUM(cc.intMinutes) as intMinutes ' .
            '  FROM tblWlrCallTimeConfig cc ' .
            ' INNER JOIN tblProductComponentConfig pcc ' .
            '    ON cc.intProductComponentConfigID = pcc.intProductComponentConfigID ' .
            '   AND pcc.dtmStart < NOW() ' .
            '   AND (pcc.dtmEnd >= NOW() OR pcc.dtmEnd IS NULL) ' .
            ' INNER JOIN products.tblServiceComponentProduct scp ' .
            '    ON pcc.intServiceComponentProductID = scp.intServiceComponentProductID ' .
            ' GROUP BY intServiceComponentID';

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

        $arrAllProductsWithInclusiveMinutes = PrimitivesResultsAsArrayGet($resResult, 'intServiceComponentID');

        return $arrAllProductsWithInclusiveMinutes;

    } // End of function getAllProductsWithInclusiveMinutes

    /**
     * Get call summary for period
     *
     *
     * @access   public
     *
     * @param int    $intServiceID - the service ID
     * @param string $strType      - type of data to get (sip,includedmins,uklandline,mobile,international,other,total)
     * @param        date          - $dtmStartDate - the start date
     * @param        date          - $dtmEndDate - the end date
     * @param        int           - $intOffSet
     * @param        mixed         - $unkOrder - optional string or array of ordering columns
     *
     * @return array of named arrays
     * @tutorial I know it's monster
     **/
    public function getCallSummaryForPeriod($intServiceID, $strType,
                                            $uxtStart, $uxtEnd, $intOffSet, $unkOrder = array())
    {

        if ($intOffSet == '') {
            $intOffSet = '0';
        }

        $arrWlrLineRentInsId = CWlrProduct::getAllLineRentInstanceIdFromServiceId($intServiceID);

        if ($arrWlrLineRentInsId === false || empty($arrWlrLineRentInsId)) {
            return false;
        }

        // RJones - I refactored these queries so that common elements are defined once
        //          and only the differences between each type are added in the switch
        //          statement. This should be cleaner and easier to maintain. Thanks x
        // PMitchell - refactored using SQL_CALC_FOUND_ROWS to eliminate the COUNT query
        $strQuery = "SELECT SQL_CALC_FOUND_ROWS c.vchDestinationCli called_number,\n" .
            "       c.intCallDestinationId destination_id, \n" .
            "       date(c.dtmStart) call_date, \n" .
            "       time(c.dtmStart) call_time, \n" .
            "       intRoundedDurationSeconds duration, \n" .
            "       CASE WHEN SUM(isr.intSecondsRedeemed) = intRoundedDurationSeconds THEN 'yes' \n" .
            "            WHEN SUM(isr.intSecondsRedeemed) < intRoundedDurationSeconds \n" .
            "               AND isr.intSecondsRedeemed > 0 THEN 'partial' \n" .
            "            ELSE 'no' END included, \n" .
            "       CASE WHEN c.dtmQueried IS NULL THEN 'false' \n" .
            "            ELSE 'true' END queried, \n" .
            "       IF(intSchedulePaymentId is null,'yes','no') as paymentRequired, \n" .
            "       intSchedulePaymentId, \n" .
            "       intCustomerPricePence cost, \n" .
            "       cd.vchDisplayName destination \n" .
            "  FROM dbWlrCalls.tblCall c \n" .
            "    INNER JOIN dbProductComponents.tblCallDestination cd \n" .
            "      ON cd.intCallDestinationId=c.intCallDestinationId \n" .
            "    INNER JOIN dbProductComponents.tblCallType ct \n" .
            "      ON ct.intCallTypeId=cd.intCallTypeId \n" .
            "    INNER JOIN dbProductComponents.tblCountry cc \n" .
            "      ON cc.intCountryId=cd.intCountryId \n" .
            "    LEFT JOIN dbWlrCalls.tblCallBillingSection cbs \n" .
            "      ON cbs.intCallId=c.intCallId \n" .
            "    LEFT JOIN dbWlrCalls.tblIncludedSecondsRedemption isr \n" .
            "      ON isr.intCallBillingSectionId=cbs.intCallBillingSectionId \n";

        $strCountQuery = "SELECT FOUND_ROWS() MyCount";

        switch ($strType) {
            case 'uklandline':
                $strQuery .= "  WHERE ct.vchDisplayName = 'Landline' \n" .
                    "    AND cc.vchDisplayName = 'Great Britain' \n" .
                    "    AND ";
                break;

            case 'mobile':
                $strQuery .= "  WHERE ct.vchDisplayName = 'Mobile' \n" .
                    "    AND cc.vchDisplayName = 'Great Britain' \n" .
                    "    AND ";
                break;

            case'international':
                $strQuery .= "  WHERE cc.vchDisplayName != 'Great Britain' \n" .
                    "    AND ";
                break;

            case 'other':
                $strQuery .= "  WHERE ct.vchDisplayName NOT IN ('Landline','Mobile') \n" .
                    "    AND ";
                break;

            case 'total':
            case 'csv':
                $strQuery .= "  WHERE ";
                break;
        } // switch ($strType)

        // Determine ORDER BY clause
        $arrOrderParamToColumn = array(
            'number'   => 'c.vchDestinationCli',
            'duration' => 'intDurationSeconds DESC',
            'cost'     => 'intCustomerPricePence DESC',
            'time'     => 'time(c.dtmStart)'
        );

        $arrOrderBy = array();
        foreach ((array)$unkOrder as $strOrder) {
            if (array_key_exists($strOrder, $arrOrderParamToColumn)) {
                $arrOrderBy[] = $arrOrderParamToColumn[$strOrder];
            }
        }

        // Default order is by date, and is always the last ordering condition
        $arrOrderBy[] = 'c.dtmStart DESC';
        $strOrderBy = join(',', $arrOrderBy);

        // This bit is the same on both queries
        $strCommonClause = "c.intProductComponentInstanceId IN (" . implode(', ', $arrWlrLineRentInsId) . ")\n" .
            "    AND c.dtmProcessCompleted BETWEEN CAST(FROM_UNIXTIME('{$uxtStart}') AS DATETIME) \n" .
            "    AND CAST(FROM_UNIXTIME('{$uxtEnd}') AS DATETIME) \n";

        $strQuery .= $strCommonClause .
            "  GROUP BY c.vchDestinationCli, \n" .
            "           date(c.dtmStart), \n" .
            "           time(c.dtmStart), \n" .
            "           intDurationSeconds, \n" .
            "           intCustomerPricePence, \n" .
            "           c.intCallDestinationId, \n" .
            "           c.intRoundedDurationSeconds, \n" .
            "           c.dtmQueried, \n" .
            "           c.intSchedulePaymentId, \n" .
            "           cd.vchDisplayName \n" .
            "  ORDER BY $strOrderBy \n";
        if ('csv' !== $strType) {
            $strQuery .= "  LIMIT {$intOffSet}, 10 \n";
        }

        $dbhConnection = get_named_connection_with_db('wlr_calls_reporting');
        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection, 'getCallChargesForPeriod');
        $arrResults = PrimitivesResultsAsArrayGet($resResult);
        $resCountResult = PrimitivesQueryOrExit($strCountQuery, $dbhConnection, 'getCallChargesForPeriod');
        $arrResults['count'] = PrimitivesResultGet($resCountResult, 'MyCount');

        return $arrResults;
    }

    /**
     * Get call summary for an invoice. Copied from getCallSummaryForPeriod()
     *
     *
     * @access   public
     *
     * @param int    $intServiceID - the service ID
     * @param string $strType      - type of data to get (sip,includedmins,uklandline,mobile,international,other,total)
     * @param        int           - $intInvoiceId - the ID of the invoice
     * @param        int           - $intOffSet
     * @param        mixed         - $unkOrder - optional string or array of ordering columns
     *
     * @return array of named arrays
     * @security Can only retrieve data for invoices belonging to the specified service ID
     **/
    public function getCallSummaryForInvoice($intServiceID, $strType, $intInvoiceId, $intOffSet, $unkOrder = array())
    {

        if (!is_numeric($intInvoiceId)) {
            return false;
        }

        if ($intOffSet == '') {
            $intOffSet = '0';
        }

        $arrWlrLineRentInsId = CWlrProduct::getAllLineRentInstanceIdFromServiceId($intServiceID);

        if ($arrWlrLineRentInsId === false || empty($arrWlrLineRentInsId)) {
            return false;
        }

        // Get all the scheduled payment IDs that constitute the invoice
        $strSchedPaymentsQuery =
            "    SELECT p.intScheduledPaymentID, " .
            "           UNIX_TIMESTAMP(p.dteStartPeriodCovered) AS uxtStartPeriodCovered, " .
            "           UNIX_TIMESTAMP(p.dteEndPeriodCovered) AS uxtEndPeriodCovered, " .
            "           pc.vchHandle  " .
            "      FROM financial.tblScheduledPayment p " .
            "INNER JOIN financial.tblConfigProductComponent cpc " .
            "        ON (cpc.intScheduledPaymentID = p.intScheduledPaymentID) " .
            "INNER JOIN userdata.tblProductComponentInstance ci " .
            "        ON (ci.intProductComponentInstanceID = cpc.intProductComponentInstanceID) " .
            "INNER JOIN dbProductComponents.tblProductComponent pc " .
            "        ON (pc.intProductComponentID = ci.intProductComponentID)  " .
            "     WHERE intSalesInvoiceID = %d ";

        $arrSchedPayments = PrimitivesResultsAsArrayGet(
            PrimitivesQueryOrExit(
                sprintf($strSchedPaymentsQuery, $intInvoiceId),
                get_named_connection_with_db('financial_reporting'), __METHOD__
            )
        );

        $strSchPaymIds = '';
        $arrSchPaymIds = array();

        if (is_array($arrSchedPayments) && false == empty($arrSchedPayments)) {

            // Set some dummy data to avoid SQL errors and get no rows in case of missing call component
            $dtmPeriodStart = '1970-01-01 00:00:00';
            $dtmPeriodEnd = '1970-01-01 00:00:00';

            foreach ($arrSchedPayments as $arrSchedPaymentId) {
                $arrSchPaymIds[] = $arrSchedPaymentId['intScheduledPaymentID'];

                // If it's a call component get payment period
                if ('wlrlinerent' === strtolower($arrSchedPaymentId['vchHandle'])) {
                    // Get billing periods to get free calls for selected invoice
                    // If component has being changed durring billing period smallest and the biggest date should be taken
                    $dtmPeriodStart = ($arrSchedPaymentId['uxtStartPeriodCovered'] < strtotime($dtmPeriodStart) || '1970-01-01 00:00:00' == $dtmPeriodStart)
                        ? date('Y-m-d 00:00:00', $arrSchedPaymentId['uxtStartPeriodCovered'])
                        : $dtmPeriodStart;

                    $uxtPeriodEnd = mktime(
                        23, 59, 59, date('n', $arrSchedPaymentId['uxtEndPeriodCovered']),
                        date('j', $arrSchedPaymentId['uxtEndPeriodCovered']) - 1,
                        date('Y', $arrSchedPaymentId['uxtEndPeriodCovered'])
                    );

                    $dtmPeriodEnd = ($uxtPeriodEnd > strtotime($dtmPeriodEnd))
                        ? date("Y-m-d H:i:s", $uxtPeriodEnd)
                        : $dtmPeriodEnd;
                }
            }

            $strSchPaymIds = implode(',', $arrSchPaymIds);
        }

        if ($strSchPaymIds === '') {
            return false;
        }

        // RJones - I refactored these queries so that common elements are defined once
        //          and only the differences between each type are added in the switch
        //          statement. This should be cleaner and easier to maintain. Thanks x
        // PMitchell - refactored using SQL_CALC_FOUND_ROWS to eliminate the COUNT query
        $strQuery = "SELECT SQL_CALC_FOUND_ROWS c.vchDestinationCli called_number,\n" .
            "       c.intCallDestinationId destination_id, \n" .
            "       date(c.dtmStart) call_date, \n" .
            "       time(c.dtmStart) call_time, \n" .
            "       intRoundedDurationSeconds duration, \n" .
            "       CASE WHEN SUM(isr.intSecondsRedeemed) = intRoundedDurationSeconds THEN 'yes' \n" .
            "            WHEN SUM(isr.intSecondsRedeemed) < intRoundedDurationSeconds AND isr.intSecondsRedeemed > 0 THEN 'partial' \n" .
            "            ELSE 'no' END included, \n" .
            "       CASE WHEN c.dtmQueried IS NULL THEN 'false' \n" .
            "            ELSE 'true' END queried, \n" .
            "       IF(intSchedulePaymentId is null,'yes','no') as paymentRequired, \n" .
            "       intSchedulePaymentId, \n" .
            "       intCustomerPricePence cost, \n" .
            "       cd.vchDisplayName destination \n" .
            "  FROM dbWlrCalls.tblCall c \n" .
            "    INNER JOIN dbProductComponents.tblCallDestination cd \n" .
            "      ON cd.intCallDestinationId=c.intCallDestinationId \n" .
            "    INNER JOIN dbProductComponents.tblCallType ct \n" .
            "      ON ct.intCallTypeId=cd.intCallTypeId \n" .
            "    INNER JOIN dbProductComponents.tblCountry cc \n" .
            "      ON cc.intCountryId=cd.intCountryId \n" .
            "    LEFT JOIN dbWlrCalls.tblCallBillingSection cbs \n" .
            "      ON cbs.intCallId=c.intCallId \n" .
            "    LEFT JOIN dbWlrCalls.tblIncludedSecondsRedemption isr \n" .
            "      ON isr.intCallBillingSectionId=cbs.intCallBillingSectionId \n";

        $strCountQuery = "SELECT FOUND_ROWS() MyCount";

        switch ($strType) {
            case 'uklandline':
                $strQuery .= "  WHERE ct.vchDisplayName = 'Landline' \n" .
                    "    AND cc.vchDisplayName = 'Great Britain' \n" .
                    "    AND ";
                break;

            case 'mobile':
                $strQuery .= "  WHERE ct.vchDisplayName = 'Mobile' \n" .
                    "    AND cc.vchDisplayName = 'Great Britain' \n" .
                    "    AND ";
                break;

            case 'international':
                $strQuery .= "  WHERE cc.vchDisplayName != 'Great Britain' \n" .
                    "    AND ";
                break;

            case 'other':
                $strQuery .= "  WHERE ct.vchDisplayName NOT IN ('Landline','Mobile') \n" .
                    "    AND ";
                break;

            case 'total':
            case 'csv':
                $strQuery .= "  WHERE ";
                break;
        } // switch ($strType)

        // Determine ORDER BY clause
        $arrOrderParamToColumn = array(
            'number'   => 'c.vchDestinationCli',
            'duration' => 'intDurationSeconds DESC',
            'cost'     => 'intCustomerPricePence DESC',
            'time'     => 'time(c.dtmStart)'
        );

        $arrOrderBy = array();
        foreach ((array)$unkOrder as $strOrder) {
            if (array_key_exists($strOrder, $arrOrderParamToColumn)) {
                $arrOrderBy[] = $arrOrderParamToColumn[$strOrder];
            }
        }

        // Default order is by date, and is always the last ordering condition
        $arrOrderBy[] = 'c.dtmStart DESC';
        $strOrderBy = join(',', $arrOrderBy);

        // This bit is the same on both queries
        // - Select chargeable calls using payment id from given invoice
        // - Free calls have no paument id so can be selected using payment period
        $strCommonClause = "c.intProductComponentInstanceId IN (" . implode(', ', $arrWlrLineRentInsId) . ")\n" .
            "AND (c.intSchedulePaymentId IN ({$strSchPaymIds})\n" .
            " OR (c.intSchedulePaymentId IS NULL\n" .
            "AND c.dtmProcessCompleted IS NOT NULL\n" .
            "AND c.dtmProcessCompleted >= '2008-12-01 00:00:00'\n" .
            "AND c.dtmProcessCompleted BETWEEN CAST('{$dtmPeriodStart}' AS DATETIME) \n" .
            "AND CAST('{$dtmPeriodEnd}' AS DATETIME)))\n";

        $strQuery .= $strCommonClause .
            "  GROUP BY c.vchDestinationCli, \n" .
            "           date(c.dtmStart), \n" .
            "           time(c.dtmStart), \n" .
            "           intDurationSeconds, \n" .
            "           c.intCallDestinationId, \n" .
            "           c.intRoundedDurationSeconds, \n" .
            "           c.dtmQueried, \n" .
            "           c.intSchedulePaymentId, \n" .
            "           c.intCustomerPricePence, \n" .
            "           cd.vchDisplayName \n" .
            "  ORDER BY $strOrderBy \n";

        if ('csv' !== $strType) {
            $strQuery .= "  LIMIT {$intOffSet}, 10 \n";
        }

        $dbhConnection = get_named_connection_with_db('wlr_calls_reporting');
        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection, __METHOD__);
        $arrResults = PrimitivesResultsAsArrayGet($resResult);
        $resCountResult = PrimitivesQueryOrExit($strCountQuery, $dbhConnection, __METHOD__);
        $arrResults['count'] = PrimitivesResultGet($resCountResult, 'MyCount');

        return $arrResults;
    }

    /**
     * Public Methods
     */

    //
    // Configurator Methods
    //

    /**
     * Refreshes the current state of the component
     *
     * If the component is in a queued state
     *
     * @access public
     * @return boolean False if the component is destroyed, otherwise true
     */
    public function refreshCurrentState()
    {
        // if we're in a queued state attempt to action it
        // breaks show deliberate fall throughs.
        switch ($this->m_strStatus) {
            case 'queued-reactivate':
                return $this->enable();
                break;

            case 'queued-deactivate':
                return $this->disable();
                break;

            case 'queued-destroy':
                return $this->destroy();
                break;

            case 'queued-activate':
            case 'active':
            case 'deactive':
                break;

            default:
                //not in a queued state
                break;
        } // switch($this->m_strStatus)

        return true;
    } // function refreshCurrentState()

    public function prvGetNextInvoiceDate()
    {
        $arrService = userdata_get_service($this->m_intServiceID);
    }

    /**
     * syncStartWithEnableDate
     *
     * @param  string $uxtEnableDate - unix timestamp
     *
     * @return void
     */
    public function syncStartWithEnableDate($uxtEnableDate)
    {
        if (empty($uxtEnableDate)) {
            return false;
        }

        $dbhConnection = get_named_connection_with_db('userdata');
        $uxtEnableDate = mysql_real_escape_string($uxtEnableDate, $dbhConnection['handle']);
        $intComponentId = mysql_real_escape_string($this->getComponentID(), $dbhConnection['handle']);

        $strQuery = "UPDATE tblProductComponentInstance SET dtmStart = FROM_UNIXTIME($uxtEnableDate)
                     WHERE intComponentID = '$intComponentId'";

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection, 'syncStartWithEnableDate', false);
    }

    /**
     * Utility method to try and make enable() less complicated!!
     *
     * A contract period spans between dates A and B. The span (length)
     * of this period is derived by a tariff; e.g. it could be one month
     * or 3 months in duration.
     *
     * Given the start ($uxtStartOffset) point A of a contract, this method
     * calculates the end date (B) by taking into consideration the tariff.
     *
     * @param int $uxtStartOffset initial date in time to use when
     *                            determining the end date.
     *
     * @return int
     **/
    protected function determineContractPeriodEnd($uxtStartOffset)
    {
        $serviceId = $this->getServiceID();

        $intSubscriptionProductComponentInstanceId = CProductComponent::getProductComponentInstance(
            $serviceId, 'SUBSCRIPTION', array('UNCONFIGURED'), 'WLR'
        );
        $objSubscriptionProductComponent = CProductComponent::createInstance(
            $intSubscriptionProductComponentInstanceId
        );

        // Problem 63197 and Task Id 306522 - we're seeing some cases where a valid objSubscriptionProductComponent
        // isn't being created.  We need to trap and report this so we don't get a fatal error.
        if (!$objSubscriptionProductComponent instanceof CProductComponentSubscription) {

            // Raise an auto-problem
            $autoProblemClient = BusTier_BusTier::getClient('autoproblem');
            $scriptUser = SoapSession::getScriptUserDetails('PLUSNET');
            $scriptActor = Auth_BusinessActor::get($scriptUser['actorId']);

            $autoProblem = $autoProblemClient->prepareAutoProblem(
                'invalidCProdCompSubObject',
                array(
                    'instanceId' => $intSubscriptionProductComponentInstanceId,
                    'raisedBy'   => $scriptActor,
                    'serviceId'  => $serviceId,
                ),
                $scriptActor
            );
            $autoProblemId = $autoProblem->raiseProblem();

            $this->setError(
                __FILE__,
                __LINE__,
                'Error enabling component [' . $this->getComponentID() .
                '] because there is no SUBSCRIPTION product component instance in an UNCONFIGURED state.'
            );

            return false;
        }

        $intTariffId = $objSubscriptionProductComponent->getTariffID();

        $uxtProRataNextInvoice = CProductComponent::generateProRataNextInvoiceDate(
            $this->getServiceID(), $intTariffId, $uxtStartOffset
        );

        $objContractEnd = I18n_Date::fromTimestamp($uxtProRataNextInvoice);
        $objContractEnd->modify("-1", I18n_Date::DAYS);

        return $objContractEnd->getTimestamp();
    }


    /**
     * Component configurator function. Enables the component.
     *
     * @access public
     * <AUTHOR> Jones" <<EMAIL>>
     *
     * @param array $arrArgs A container for all arguments/parameters
     *
     * @return boolean
     */
    public function enable($arrArgs = array())
    {
        // When we enter this method we could either be:
        //
        // + activating after a new provide
        // + activating after a line transfer
        // + product change
        //
        // It's worth noting that $uxtEnableDate is actually an alias for "contract start date",
        // commonly known as 'uxtContractStart' in other parts of the codebase such as
        // /local/data/mis/database/database_libraries/components/CProductComponent.inc
        //
        // Thus, when we're actually enabling a WLR component, I see it working as follows:
        //
        //          %                     *
        //          %                     *
        //          %<======== p ========>*<============ m ============>
        //          %                     *
        //
        //  A       e                     B                             C                             D
        //  |-------+---------------------|-----------------------------|-----------------------------|
        //
        //          %                     *
        //          %                     *
        //          %                     *
        //          %                     *
        //
        //
        //      KEY:
        //      ----
        //
        //        * line            = next invoice date.
        //        % line, 'e' point = time where WLR goes active. (uxtEnableDate)
        //        A, B, C, D        = monthly billing cycle/periods.
        //        <== p ==>         = pro-rata period.
        //        <== m ==>         = next full monthly bill period.

        $bolCreateScheduledPayment = false;
        $bolTakeProRataPayment = (isset($arrArgs['bolTakeProRataPayment'])) ? $arrArgs['bolTakeProRataPayment'] : false;
        $bolSkipContracts = (isset($arrArgs['bolSkipContracts'])) ? $arrArgs['bolSkipContracts'] : false;
        $bolSkipBounty = (isset($arrArgs['bolSkipBounty'])) ? $arrArgs['bolSkipBounty'] : false;
        $uxtEnableDate = (isset($arrArgs['uxtEnableDate'])) ? $arrArgs['uxtEnableDate'] : time();
        $bolSyncStartWithEnableDate = (isset($arrArgs['bolSyncStartWithEnableDate'])) ? $arrArgs['bolSyncStartWithEnableDate'] : false;
        $uxtContractEnd = (isset($arrArgs['uxtContractEnd']) ? $arrArgs['uxtContractEnd'] : null);

        // Check if WLR fees were taken during signup, if no do not charge
        // pro-rata, and remove initial fee scheduled payment flag.
        $hasTransientInitialContractPaymentFlag = false;

        if ($bolTakeProRataPayment) {
            // If we need to take pro-rata payment, check whether the payment was not taken along with initial fees
            $scheduledPayment = $this->getInitialScheduledPayment();

            $hasTransientInitialContractPaymentFlag = !empty($scheduledPayment);

            $bolTakeProRataPayment = !$hasTransientInitialContractPaymentFlag;
        }

        // Sanitise the enable date here... just to be REALLY sure!!!
        if ($uxtEnableDate === false || $uxtEnableDate < 1) {
            $uxtEnableDate = time();
        }

        $service_record = userdata_service_get($this->getServiceID());

        $objBillingDate = Core_BillingDate_Facade::getBillingDateFromDateAndPeriod(
            $this->getServiceID(), $service_record['next_invoice'],
            $service_record['invoice_period'], $service_record['invoice_day']
        );

        $bolServiceNextInvoiceSet = false;
        if (!$objBillingDate->isValid()) {

            // No invoice date set - so generate one and set it
            //
            // In this case, if there's no invoice date already then we will use
            // the enable date + next billing period to calculate the next invoice date. As we find out
            // about WLR activations the day after they happen, this date will be
            // one day ago.

            $multiplier = Core_BillingDate::getBillingPeriodFromHandle($service_record['invoice_period']);
            $next_invoice_date = date('Y-m-d', strtotime("+{$multiplier} month", $uxtEnableDate));

            userdata_service_set_next_invoice($this->getServiceID(), $next_invoice_date);

            $objBillingDate = Core_BillingDate_Facade::getBillingDateFromDateAndPeriod(
                $this->getServiceID(), $next_invoice_date,
                $service_record['invoice_period'], date('j', strtotime($next_invoice_date))
            );

            $bolServiceNextInvoiceSet = true;

            // check to see if payment taken
            $scheduledPayment = $this->getInitialScheduledPayment();

            if (empty($scheduledPayment)) {

                // no payment found - take payment
                $bolCreateScheduledPayment = true;
            }
        }

        if ($bolSyncStartWithEnableDate) {
            $this->syncStartWithEnableDate($uxtEnableDate);
        }

        $arrRetentionOffer = CWlrProduct::getRetentionOfferDetails($this->getComponentID());

        $bolRetentionOffer = false;
        if (count($arrRetentionOffer) > 0 && $arrRetentionOffer['dtmEnd'] == '') {
            $bolRetentionOffer = true;
        }

        // Based on our diagram above, $objContractEnd represents the day before point "B"
        //
        // As the pro-rata period is to cover just a small period of time (defined
        // as the time period =p= ) we know when this period shall end.
        //
        // enableProductComponents() will cause contracts to be created that span between
        // the uxtEnableDate and uxtContractEnd dates.
        $uxtContractStart = $uxtEnableDate;

        // In the event that uxtContractEnd is NULL, it will create a contract that ends
        // one month in the future on the anniversary of the enable date, hence the out-
        // of-sync periods shown on the portal/Workplace.
        // Thus, if we're attempting to take a pro-rata payment and the contract end date
        // hasn't been specified, figure out the contract end date which will be the day
        // before their next invoice date.
        // At this point in time, their product components will be in an UNCONFIGURED state
        // so that's why we need to look for an unconfigured SUBSCRIPTION product component.
        if ($uxtContractEnd == null) {
            $uxtContractEnd = $this->determineContractPeriodEnd($uxtContractStart);

            // If we've been unable to determine the end date, then we
            // cannot enable the component.  Return false for the calling
            // function to deal with it.
            if (false === $uxtContractEnd) {
                return false;
            }
        }

        // At this point, I'd expect $uxtContractStart and $uxtContractEnd to
        // span one of the following, c.f. ASCII art above:
        //
        //  Pro rata contract starting at enable date
        //  - contract start = e
        //  - contract end = B
        //
        //  Forced end date specified in args
        //  - contract start = e
        //  - contract end = ?

        // Attempt to enable all sub-components
        $bundlableCallFeatures = array();
        if ($this->getValidCallFeaturesBundle() instanceof CProductComponent) {

            $bundlableCallFeatures = $this->getValidBundlableCallFeatures();
        }

        if (!$this->enableProductComponents(
            array(
                'uxtEnableDate'              => $uxtEnableDate,     // WLR will use this for whatever it pleases
                'uxtContractStart'           => $uxtContractStart,  // createContract() says omnomnomnom
                'bolSyncStartWithEnableDate' => $bolSyncStartWithEnableDate,
                'bolTakeProRataPayment'      => false,
                'bolCreateScheduledPayment'  => $bolCreateScheduledPayment,
                'bolRetentionOffer'          => $bolRetentionOffer,
                'uxtContractEnd'             => $uxtContractEnd,
                'bundlableCallFeatures'      => $bundlableCallFeatures
            )
        )
        ) {
            return false;
        }

        $arrProductComponents = $this->listProductComponentInstanceIdByHandle();

        if (empty($arrProductComponents)) {
            error_log('Failed to get list of components for: ' . $this->getComponentID());

            return false;
        }

        $intSubscriptionProductComponentInstanceID = $arrProductComponents['SUBSCRIPTION'];

        if (false === CProductComponentSubscription::configureSubscriptions(
                $intSubscriptionProductComponentInstanceID, $arrArgs)
        ) {
            error_log("Unable to configure subscription component for: " . $intSubscriptionProductComponentInstanceID);

            return false;
        }

        // If the main next invoice date hasn't just been set, then we might be
        // able to do other stuff such as (optionally) take pro-rata fees.
        // Remember: it doesn't make sense to take a pro-rata if we've just
        // set their next invoice date. This is because there's no other service
        // to align WLR to which would warrant a pro-rata.
        // Also, if the RBM migration is complete then this activation cost will be taken in the new system
        if (!FeatureToggleManager::isOnFiltered('RBM_MIGRATION_COMPLETE', null, null, null, $this->getServiceID()) &&
            $bolTakeProRataPayment && !$bolServiceNextInvoiceSet
        ) {

            //charge prorata activation cost
            $this->chargeProrataActivationCost($uxtEnableDate, '', $bolRetentionOffer);
        }


        // Get any invoices that were generated on the transfer date
        $arrAccount = userdata_account_get_by_service($this->getServiceID());
        $intAccountId = $arrAccount['account_id'];
        $arrInvoice = financialGetInvoicesForDate($intAccountId, $uxtEnableDate);
        if (!empty($arrInvoice)) {
            foreach ($arrInvoice as $recInvoice) {
                // Check if we need to generate a referral token
                if (preg_match('/Account Billing/', $recInvoice['strInvoiceDesc'])) {
                    // We need to generate a referral token, as the user has a full billing month,
                    // but we would not have generated a Home Phone token
                    financial_referral_create_referral_tokens(
                        $this->getServiceID(),
                        $uxtEnableDate,
                        1, // Month's Subscription (1 in this case)
                        'regular_subscription', // NOT chargeable_component - that would generate the wrong tokens!
                        $recInvoice['intInvoiceId'] // Where am I gonna get this from?
                    );
                } // if (preg_match('/Account Billing/', $recInvoice['strInvoiceDesc']))
            } // foreach ($arrInvoice as $recInvoice)
        } // if (! empty($arrInvoice))

        // BGW: If WLR is activating first, then WLR needs to activate the service as well
        if ($bolServiceNextInvoiceSet) {

            userdata_service_set_status($this->getServiceID(), 'active');
        }

        // We've successfully activated the component. Now make it active

        // BPR 2008 - if we've got an 'All Calls Barred' product component instance, we don't want to set the status to
        //            active here, as it will override our 'queued-deactivate' status..
        if (false === $this->hasActiveAllCallsBarred()) {

            $this->prvSetStatus('active');

            $this->refreshInstance($this->getComponentID());

            // Sanity check
            if ($this->getStatus() != 'active') {

                $this->setError(
                    __FILE__,
                    __LINE__,
                    'Error enabling component [' . $this->getComponentID() .
                    '] because the component status [' . $this->getStatus() . '] is not active.'
                );

                return false;
            }

            $this->logStatusChange('ACTIVE');
        }

        // activate pending profit forgone contracts once the wlr product has activated
        $this->_activateContract();

        //BOUNTY REFERRAL
        //Select all Special Offers concerning Home Phone

        if (false == $bolSkipBounty) {
            require_once '/local/data/mis/database/application_apis/ReferralBounty/processBountyReferralsComponents.class.php';
            $arrValues = processBountyReferralsComponents::getSpecialOffers('WLR');

            //if any found include the referral
            if (is_array($arrValues) && count($arrValues) > 0) {
                foreach ($arrValues as $arrSpecialOfferIds) {
                    $intSpecialOfferId = $arrSpecialOfferIds['intSpecialOfferId'];
                    $objBounty = new processBountyReferralsComponents(
                        $intSpecialOfferId, $this->getServiceID(), $this->getServiceComponentID()
                    );
                    $arrReferrals = $objBounty->findReferralsToProcess();
                    $objBounty->setBolLive(true);
                    if (count($arrReferrals) == 1) {
                        $arrReferral = $arrReferrals[0];
                        $intRefereeServiceId = $arrReferral['referral_signedup_sid'];
                        $bolReferralProcessed = $objBounty->processSingleReferral($arrReferral);
                    }
                }
            }
        }
        //END BOUNTY REFERRAL

        // uSwitch and MoneySavingSupermarket.com offers
        // check to see if there is a discount in queued activate. If there is, use the components module to activate it.
        $criteria = array(
            'service_id' => $this->getServiceId(),
            'type'       => array(COMPONENT_199HP_OFFER, COMPONENT_FREEHP_OFFER),
            'status'     => array('queued-activate')
        );

        $components = userdata_component_find($criteria);
        if (is_array($components) and count($components) > 0) {
            // Moving from queued activate to active will set up the discounts
            try {
                Components_Api::activate($components[0]['component_id']);
            } catch (Exception $e) {

                if ($this->getServiceId() > 0) {

                    global $my_id;

                    $strTicketBody = 'Attempted to enable \'Home Phone &pound;1.99 Offer\' or  \'Free Phone\' offer. Failed with this reason: ' . $e->getMessage() . '. Please enable this component manually.';
                    $intTeamId = PHPLibTeamIdGetByName('BOT WLR Provisioning Errors');
                    tickets_ticket_add(
                        'Script', $this->getServiceId(), 0, 0,
                        'Open', $my_id, $strTicketBody, 0, $intTeamId
                    );
                }
            }
            Db_Manager::commit();
        }

        // All done, if payment for WLR was taken as initial fees, then remove the scheduled payment flag
        if ($hasTransientInitialContractPaymentFlag) {
            $this->deleteTransientInitialContractPaymentFlag();
        }

        return true;
    } // function enable()

    /**
     * Component configurator function. Disables the component.
     *
     * @access   public
     * <AUTHOR> Jones" <<EMAIL>>
     * @return           boolean True on success
     */
    public function disable()
    {
        if (!$this->disableProductComponents()) {
            return false;
        }

        // We've successfully activated the component. Now make it active
        $this->prvSetStatus('deactive');

        // Sanity check
        if ($this->getStatus() != 'deactive') {
            return false;
        }

        $this->logStatusChange('DEACTIVE');

        return true;
    } // function disable()

    /**
     * Component configurator function. Destroys the component.
     *
     * @access   public
     * <AUTHOR> Jones" <<EMAIL>>
     * @return   boolean True on success
     */
    public function destroy($arrArgs = array())
    {
        if (!$this->destroyProductComponents($arrArgs)) {
            return false;
        }

        // We've successfully activated the component. Now make it active
        $this->prvSetStatus('destroyed');

        // Sanity check
        if ($this->getStatus() != 'destroyed') {
            return false;
        }

        $this->logStatusChange('DESTROYED');

        return true;
    } // function destroy()

    //
    // CallTime Product Component Method Factories
    //

    /**
     * Retrieves a list of calltime objects
     *
     * <AUTHOR> Jones" <<EMAIL>>
     * @access   public
     * @static
     *
     * @param    array   An array of calltime config ids to get the objects for
     * @param    integer Line Rent Product Component Instance ID
     * @param    integer Unixtime of the start of the period to retrieve
     * @param    integer Unixtime of the end of the period to retrieve
     *
     * @return   array   Array of CWlrCallTime Objects
     */
    public static function getAvailableCallTimeObjects($arrWlrCallTimeConfigIds,
                                                       $intLineRentalProdCompInsId, $uxtStart = null, $uxtEnd = null)
    {
        static $arrObjectCache;

        $arrCallTimeObjects = array();

        $dbhRead = get_named_connection_with_db('userdata_reporting');

        $strWlrCallTimeConfigIds = implode(', ', $arrWlrCallTimeConfigIds);

        $strGetCallTime = "SELECT pci.intProductComponentInstanceID \n" .
            "  FROM tblProductComponentInstance pci \n" .
            "    INNER JOIN dbProductComponents.tblProductComponentConfig pcc \n" .
            "      ON pcc.intProductComponentID = pci.intProductComponentID \n" .
            "    INNER JOIN dbProductComponents.tblTariff trf \n" .
            "      ON pcc.intProductComponentConfigID = trf.intProductComponentConfigID \n" .
            "        AND pci.intTariffID = trf.intTariffID \n" .
            "    INNER JOIN dbProductComponents.tblWlrCallTimeConfig ctc \n" .
            "      ON ctc.intProductComponentConfigId = trf.intProductComponentConfigId \n" .
            "    INNER JOIN tblProductComponentInstance plr \n" .
            "      ON pci.intComponentID = plr.intComponentID \n" .
            "        AND plr.intProductComponentInstanceId = $intLineRentalProdCompInsId \n" .
            "  WHERE ctc.intWlrCallTimeConfigID IN ($strWlrCallTimeConfigIds)";

        if (false === ($resGetCallTime = PrimitivesQueryOrExit($strGetCallTime, $dbhRead, 'CWlrProduct::getAvailableCallTimeObjects{GetCallTime}', false))) {
            // Query failed. If we change true to false above we probably don't want to die here
            $bolFalse = false;

            return $bolFalse;
        }

        if (PrimitivesNumRowsGet($resGetCallTime) > 0) {
            $arrCallTimeProdCompInsId = PrimitivesResultsAsArrayGet($resGetCallTime);

            foreach ($arrCallTimeProdCompInsId as $idxKey => $recProdCompIns) {
                $id = $recProdCompIns['intProductComponentInstanceID'];
                if (isset($arrObjectCache[$id])) {
                    //object in cache
                    $arrCallTimeObjects[$idxKey] = $arrObjectCache[$id];

                } else {
                    //object not in cache
                    $arrObjectCache[$id] = CProductComponent::createInstance($recProdCompIns['intProductComponentInstanceID']);
                    $arrCallTimeObjects[$idxKey] = $arrObjectCache[$id];
                }
            }
        }

        return $arrCallTimeObjects;
    } // function &getAvailableCallTimeObjects($arrWlrCallTimeConfigIds, $intLineRentalProdCompInsId, $uxtStart = NULL, $uxtEnd = NULL)

    /**
     *
     */
    public function getCallTimeObjectFromLineRentCompInstance($intLineRentComponentInstanceId)
    {
        $objLineRentProductComponent = CProductComponent::createInstance($intLineRentComponentInstanceId);
        $intServiceId = $objLineRentProductComponent->getServiceID();

        $intProductComponentInstanceId = CProductComponent::getProductComponentInstance($intServiceId, 'WlrCallTime');

        if (isset($intProductComponentInstanceId) && $intProductComponentInstanceId > 0) {
            $objProductComponent = CProductComponent::createInstance($intProductComponentInstanceId);
        } else {
            $objProductComponent = false;
        }

        return array($objProductComponent);
    } // function &getCallTimeObjectFromLineRentCompInstance($intLineRentComponentInstanceId)

    //
    // PAYG Product Component Method Factories
    //

    /**
     *
     */
    public static function getPaygObjectFromLineRentCompInstance($intLineRentComponentInstanceId)
    {
        $objLineRentProductComponent = CProductComponent::createInstance($intLineRentComponentInstanceId);
        $intServiceId = $objLineRentProductComponent->getServiceID();

        $intProductComponentInstanceId = CProductComponent::getProductComponentInstance($intServiceId, 'WlrPayg');

        if (isset($intProductComponentInstanceId) && $intProductComponentInstanceId > 0) {
            $objProductComponent = CProductComponent::createInstance($intProductComponentInstanceId);
        } else {
            $objProductComponent = false;
        }

        return array($objProductComponent);
    } // function &getPaygObjectFromLineRentCompInstance($intLineRentComponentInstanceId)

    /**
     * Get the Payg Product Component Instance from the account CLI number
     *
     * <AUTHOR> Jones" <<EMAIL>>
     * @static
     * @access   public
     *
     * @param    string  CLI number of object to retrieve
     *
     * @return   object  CWlrPayg Object
     */
    public static function getPaygObjectFromCli($strCli)
    {
        $bolFalse = false; // Return variable

        $strCli = str_replace(' ', '', $strCli);

        $dbhRead = get_named_connection_with_db('userdata_reporting');

        $strPayg = 'SELECT ppg.intProductComponentInstanceID '
            . 'FROM userdata.tblProductComponentInstance ppg '
            . 'INNER JOIN dbProductComponents.tblProductComponent pc '
            . 'ON ppg.intProductComponentID = pc.intProductComponentID '
            . "AND pc.vchHandle = 'WlrPayg' "
            . 'INNER JOIN userdata.tblProductComponentInstance plr '
            . 'ON ppg.intComponentID = plr.intComponentID '
            . 'INNER JOIN userdata.tblConfigWlrLineRent clr '
            . 'ON plr.intProductComponentInstanceID = clr.intProductComponentInstanceID '
            . "WHERE clr.vchCli = '$strCli' "
            . 'ORDER BY ppg.dtmStart DESC LIMIT 1';

        if (false === ($resPayg = PrimitivesQueryOrExit($strPayg, $dbhRead, 'CWlrProduct::getPaygObjectFromCli{Payg}', false))) {
            // If we disable exit_on_failure then we should handle it gracefully here
            return $bolFalse;
        }

        if (PrimitivesNumRowsGet($resPayg) == 0) {
            // Sub-Component not found
            return $bolFalse;
        }

        $intProductComponentInstanceId = PrimitivesResultGet($resPayg, 'intProductComponentInstanceID');
        $objWlrPayg = CProductComponent::createInstance($intProductComponentInstanceId);

        return $objWlrPayg;
    }

    /**
     * Add credit to the PAYG Product Component
     *
     * <AUTHOR> Jones" <<EMAIL>>
     * @access   public
     *
     * @param    integer Amount of credit to add in pence
     *
     * @return   integer The DB ID for the credit just added (or false otherwise)
     */
    public function addPaygCredit($intAmountPence)
    {
        $intProductComponentInstanceId = CProductComponent::getProductComponentInstance(
            $this->getServiceID(), 'WlrPayg'
        );

        if (isset($intProductComponentInstanceId) && $intProductComponentInstanceId > 0) {
            $objProductComponent = CProductComponent::createInstance($intProductComponentInstanceId);

            return $objProductComponent->addCredit($intAmountPence);
        }

        return false;
    } // function addPaygCredit($intAmountPence)

    //
    // Line Rent Product Component Method Factories
    //

    /**
     *
     */
    public function getLineRentObjectFromPaygCompInstance($intPaygComponentInstanceId, $bolIncludeDestroyed = false)
    {
        $objPaygProductComponent = CProductComponent::createInstance($intPaygComponentInstanceId);
        $intServiceId = $objPaygProductComponent->getServiceID();

        $intProductComponentInstanceId = CProductComponent::getProductComponentInstance($intServiceId, 'WlrLineRent');

        // set it to failure by default
        $objProductComponent = false;

        if (isset($intProductComponentInstanceId) && $intProductComponentInstanceId > 0) {
            $objProductComponent = CProductComponent::createInstance($intProductComponentInstanceId);
        } elseif ($bolIncludeDestroyed === true) {
            // hmmm, we don't have a line rental object
            // Lets see if they have any that are destroyed

            $arrPanicStates = array('DESTROYED');

            $intProductComponentInstanceId = CProductComponent::getProductComponentInstance(
                $intServiceId, 'WlrLineRent', $arrPanicStates
            );

            if (isset($intProductComponentInstanceId) && $intProductComponentInstanceId > 0) {
                $objProductComponent = CProductComponent::createInstance($intProductComponentInstanceId);
            }
        }

        return $objProductComponent;
    } // function &getPaygObjectFromLineRentCompInstance($intLineRentComponentInstanceId)

    /**
     * @access   public
     */
    public function setWlrExpectedTransferDate($uxtExpectedDate)
    {
        $arrStates = array(
            'UNCONFIGURED',
            'ACTIVE',
            'QUEUED_ACTIVATE',
            'QUEUED_REACTIVATE',
            'QUEUED_DEACTIVATE',
            'QUEUED_DECONFIGURE',
            'DEACTIVE',
            'QUEUED_DESTROY'
        );

        // Get the Line Rent sub-component
        $intProductComponentInstanceId = CProductComponent::getProductComponentInstance(
            $this->getServiceID(), 'WlrLineRent', $arrStates
        );

        if (isset($intProductComponentInstanceId) && $intProductComponentInstanceId > 0) {
            $objWlrLineRentProdCompInstance = CProductComponent::createInstance(
                $intProductComponentInstanceId
            );

            return $objWlrLineRentProdCompInstance->updateExpectedTransferDate($uxtExpectedDate);
        }

        return false;
    } // function setWlrExpectedTransferDate($uxtExpectedDate)

    /**
     * @access   public
     */
    public function getWlrExpectedTransferDate()
    {
        $arrStates = array(
            'UNCONFIGURED',
            'ACTIVE',
            'QUEUED_ACTIVATE',
            'QUEUED_REACTIVATE',
            'QUEUED_DEACTIVATE',
            'QUEUED_DECONFIGURE',
            'DEACTIVE',
            'QUEUED_DESTROY'
        );

        // Get the Line Rent sub-component
        $intProductComponentInstanceId = CProductComponent::getProductComponentInstance(
            $this->getServiceID(), 'WlrLineRent', $arrStates
        );

        if (isset($intProductComponentInstanceId) && $intProductComponentInstanceId > 0) {
            $objWlrLineRentProdCompInstance = CProductComponent::createInstance(
                $intProductComponentInstanceId
            );

            return $objWlrLineRentProdCompInstance->getExpectedTransferDate();
        }

        return false;
    } // function getWlrExpectedTransferDate($uxtExpectedDate)

    /**
     * Return details of any existing scheduled cancellation for this WLR product (not cancelled or completed)
     *
     * @return mixed Array of the scheduled cancellation details or false
     */
    public function getScheduledCancellation()
    {
        $arrEvents = CCancellation::getExistingEvents($this->m_intComponentID, true);

        if ($arrEvents) {
            return $arrEvents[0];
        }

        return false;
    } // function getScheduledCancellation()

    /**
     * @access   public
     */
    public function cancelCancelOtherOrder()
    {
        // Get the Line Rent sub-component
        $intProductComponentInstanceId = CProductComponent::getProductComponentInstance(
            $this->getServiceID(), 'WlrLineRent'
        );

        if (isset($intProductComponentInstanceId) && $intProductComponentInstanceId > 0) {
            $objWlrLineRentProdCompInstance = CProductComponent::createInstance(
                $intProductComponentInstanceId
            );

            if (true === $objWlrLineRentProdCompInstance->cancelCancelOtherOrder()) {
                // Log this event
                $objEventLogger = $this->prvGetEventLogger();
                $objEventLogger->logWlrRetention();
                unset($objEventLogger);

                return true;
            } // if (true === $objWlrLineRentProdCompInstance->cancelCancelOtherOrder())
        } // if (isset($intProductComponentInstanceId) && $intProductComponentInstanceId > 0)

        return false;
    } // function cancelCancelOtherOrder()

    /**
     * @access   public
     */
    public function cancelAllOutstandingProductCancellation($intComponentID = false)
    {
        $bolReturn = false;
        if (!$intComponentID) {
            $intComponentID = $this->getComponentID();
        }

        $arrExistingEvents = CCancellation::getExistingEvents($intComponentID, true);

        if (count($arrExistingEvents) > 0) {
            foreach ($arrExistingEvents as $arrEventDetails) {
                $objExistingEvent = new CCancellation($arrEventDetails['intCancellationID']);
                $bolReturn = $objExistingEvent->cancel() ? true : false;
            } // foreach($arrExistingEvents as $arrEventDetails)
        } else {
            $bolReturn = true;
        }

        $strTicketBody = "Scheduled Cancellation was cancelled for {$this->getComponentName()} Product {$intComponentID}";

        tickets_ticket_add('Script', $this->getServiceID(), '', '', 'Closed', 0, $strTicketBody);

        return $bolReturn;
    }

    /**
     *
     */
    public static function getServiceIdFromLineRentProdCompInsId($intLineRentProdCompInsId, $arrStatus = array())
    {
        if (empty($arrStatus)) {
            // Retrieve any none destroyed components
            $arrStatus = array(
                'UNCONFIGURED',
                'QUEUED_ACTIVATE',
                'QUEUED_REACTIVATE',
                'ACTIVE',
                'QUEUED_DEACTIVATE',
                'QUEUED_DECONFIGURE',
                'DEACTIVE',
                'QUEUED_DESTROYED',
                'DESTROYED'
            );
        } // if (empty($arrStatus))

        $dbhRead = get_named_connection_with_db('userdata_reporting');

        $strServiceId = "SELECT com.service_id AS intServiceId \n" .
            "  FROM userdata.components com \n" .
            "    INNER JOIN userdata.tblProductComponentInstance plr \n" .
            "      ON com.component_id = plr.intComponentID \n" .
            "    INNER JOIN dbProductComponents.tblStatus st \n" .
            "      ON plr.intStatusID = st.intStatusID \n" .
            '        AND st.vchHandle IN (\'' . implode('\', \'', $arrStatus) . "')" .
            "  WHERE plr.intProductComponentInstanceID = '{$intLineRentProdCompInsId}' \n";

        if (false === ($resServiceId = PrimitivesQueryOrExit($strServiceId, $dbhRead, 'CWlrProduct::getServiceIdFromLineRentProdCompInsId{ServiceId}', true))) {
            // If we disable exit_on_failure above bail out more gracefully here
            return false;
        }

        $intRows = PrimitivesNumRowsGet($resServiceId);

        if ($intRows == 0) {
            return false;
        }

        $intServiceId = PrimitivesResultGet($resServiceId, 'intServiceId');

        return $intServiceId;
    } // function getServiceIdFromCli($strCli, $arrStatus = array())

    /**
     *
     */
    public function getServiceIdFromCli($strCli, $arrStatus = array())
    {
        if (empty($arrStatus)) {
            // Retrieve any none destroyed components
            $arrStatus = array(
                'UNCONFIGURED',
                'QUEUED_ACTIVATE',
                'QUEUED_REACTIVATE',
                'ACTIVE',
                'QUEUED_DEACTIVATE',
                'QUEUED_DECONFIGURE',
                'DEACTIVE'
            );
        } // if (empty($arrStatus))

        $strCli = str_replace(' ', '', $strCli);

        $dbhRead = get_named_connection_with_db('userdata_reporting');

        $strServiceId = "SELECT com.service_id AS intServiceId \n" .
            "  FROM userdata.components com \n" .
            "    INNER JOIN userdata.tblProductComponentInstance plr \n" .
            "      ON com.component_id = plr.intComponentID \n" .
            "    INNER JOIN userdata.tblConfigWlrLineRent clr \n" .
            "      ON plr.intProductComponentInstanceID = clr.intProductComponentInstanceID \n" .
            "    INNER JOIN dbProductComponents.tblStatus st \n" .
            "      ON plr.intStatusID = st.intStatusID \n" .
            '        AND st.vchHandle IN (\'' . implode('\', \'', $arrStatus) . "')" .
            "  WHERE clr.vchCli = '{$strCli}' \n" .
            "    AND clr.dtmEnd IS NULL";

        if (false === ($resServiceId = PrimitivesQueryOrExit($strServiceId, $dbhRead, 'CWlrProduct::getServiceIdFromCli{ServiceId}', true))) {
            // If we disable exit_on_failure above bail out more gracefully here
            return false;
        }

        $intRows = PrimitivesNumRowsGet($resServiceId);

        if ($intRows == 0) {
            return false;
        }

        $intServiceId = PrimitivesResultGet($resServiceId, 'intServiceId');

        return $intServiceId;
    } // function getServiceIdFromCli($strCli, $arrStatus = array())

    /**
     * Gets all the Line Rent Product Component Instances based on Service ID
     */
    public static function getAllLineRentInstanceIdFromServiceId($intServiceId)
    {
        $dbhRead = get_named_connection_with_db('userdata_reporting');

        $strQuery = "SELECT pci.intProductComponentInstanceId \n" .
            "  FROM tblProductComponentInstance pci \n" .
            "    INNER JOIN dbProductComponents.tblProductComponent pc \n" .
            "      ON pci.intProductComponentId = pc.intProductComponentId \n" .
            "        AND pc.vchHandle = 'WlrLineRent' \n" .
            "    INNER JOIN components com \n" .
            "      ON pci.intComponentId = com.component_id \n" .
            "  WHERE com.service_id = $intServiceId \n";

        if (false === ($resLineRent = PrimitivesQueryOrExit($strQuery, $dbhRead, 'CWlrProduct::getAllLineRentInstanceIdFromServiceId{Query}', false))) {
            // If we disable exit_on_failure above bail out more gracefully here
            return false;
        }

        $intRows = PrimitivesNumRowsGet($resLineRent);

        if ($intRows == 0) {
            return false;
        }

        $arrLineRentProdCompIns = PrimitivesResultsAsArrayGet($resLineRent);

        foreach ($arrLineRentProdCompIns as $intIdx => $arrRecord) {
            $arrLineRentProdCompInsId[] = $arrRecord['intProductComponentInstanceId'];
        }

        return $arrLineRentProdCompInsId;
    } // function getAllLineRentProdCompInsIdFromServiceId($intServiceId)

    /**
     * Gets a Line Rent object
     *
     * @static
     */
    public static function getLineRentObjectFromCli($strCli, $arrStatus = array(), $strDatabase = '_reporting')
    {

        // Problem:55556
        if ($strDatabase !== '_reporting' && $strDatabase !== '')
            $strDatabase = '_reporting';

        $bolFalse = false; // If we need to return false, use a variable

        if (empty($arrStatus)) {
            // Retrieve any none destroyed components
            $arrStatus = array(
                'UNCONFIGURED',
                'QUEUED_ACTIVATE',
                'QUEUED_REACTIVATE',
                'ACTIVE',
                'QUEUED_DEACTIVATE',
                'QUEUED_DECONFIGURE',
                'DEACTIVE'
            );
        } // if (empty($arrStatus))

        $strCli = str_replace(' ', '', $strCli);

        $dbhRead = get_named_connection_with_db('userdata' . $strDatabase);

        $strLineRent = "SELECT plr.intProductComponentInstanceID \n" .
            "  FROM userdata.tblProductComponentInstance plr \n" .
            "    INNER JOIN userdata.tblConfigWlrLineRent clr \n" .
            "      ON plr.intProductComponentInstanceID = clr.intProductComponentInstanceID \n" .
            "    INNER JOIN dbProductComponents.tblStatus st \n" .
            "      ON plr.intStatusID = st.intStatusID \n" .
            '        AND st.vchHandle IN (\'' . implode('\', \'', $arrStatus) . "')" .
            "  WHERE clr.vchCli = '{$strCli}' \n" .
            "  ORDER BY clr.dtmStart DESC\n" .
            "  LIMIT 1\n";

        if (false === ($resLineRent = PrimitivesQueryOrExit($strLineRent, $dbhRead, 'CWlrProduct::getLineRentObjectFromCli{LineRent}', true))) {
            // If we disable exit_on_failure above bail out more gracefully here
            return $bolFalse;
        }

        $intRows = PrimitivesNumRowsGet($resLineRent);

        if ($intRows == 0) {
            // try reading from master?
            if ($strDatabase == '_reporting') {
                return CWlrProduct::getLineRentObjectFromCli($strCli, $arrStatus, '');
            }

            return $bolFalse;
        }

        $intProductComponentInstanceId = PrimitivesResultGet($resLineRent, 'intProductComponentInstanceID');

        $objWlrLineRent = CProductComponent::createInstance($intProductComponentInstanceId);

        return $objWlrLineRent;
    } // function getLineRentObjectFromCli($strCli, $arrStatus = array())

    /**
     * Get the Call Plan ID based on the Service Component ID
     */
    public function getCallPlanIdFromServiceComponentId($intServiceComponentId)
    {
        $dbhRead = get_named_connection_with_db('product_reporting');
        $strCallPlan = 'SELECT cp.intCallPlanId ' .
            '  FROM dbProductComponents.tblCallPlan cp ' .
            '    INNER JOIN dbProductComponents.tblProductComponentConfig pcc ' .
            '      ON pcc.intProductComponentConfigId = cp.intProductComponentConfigId ' .
            '    INNER JOIN products.tblServiceComponentProduct scp ' .
            '      ON scp.intServiceComponentProductId = pcc.intServiceComponentProductId ' .
            '    INNER JOIN products.service_components sc ' .
            '      ON sc.service_component_id = scp.intServiceComponentId ' .
            '  WHERE cp.dtmEnd IS NULL ' .
            "  AND sc.service_component_id = {$intServiceComponentId} ";

        if (false === ($resCallPlan = PrimitivesQueryOrExit($strCallPlan, $dbhRead, 'CWlrProduct::getCallPlanIdFromServiceComponentId', true))) {
            return false;
        }

        if (PrimitivesNumRowsGet($resCallPlan) == 0) {
            return false;
        }

        return PrimitivesResultGet($resCallPlan, 'intCallPlanId');
    } // function getCallPlanIdFromServiceComponentId($intServiceComponentId)

    /**
     * Retrieve a list of calls made
     *
     * @param int $intServiceId  Service Id
     * @param int $intOffset     Offset
     * @param int $intPageLength Page length
     *
     * @return @array
     */
    public function getCallsMade($intServiceId, $intOffset = 0, $intPageLength = 10)
    {
        if ($intPageLength > PAGE_LENGTH_LIMIT) {
            $intPageLength = PAGE_LENGTH_LIMIT;
        }

        $arrWlrLineRentInsId = CWlrProduct::getAllLineRentInstanceIdFromServiceId($intServiceId);

        if ($arrWlrLineRentInsId === false || empty($arrWlrLineRentInsId)) {
            return false;
        }

        $strSQL = "    (SELECT callTable.vchDestinationCli AS name, " .
            "            callTable.dtmStart AS time, " .
            "            callTable.intRoundedDurationSeconds AS length, " .
            "            callTable.intCustomerPricePence AS price, " .
            "            d.vchDisplayName AS destination, " .
            "            IF(fnd.intDiscountPence IS NULL, 0, fnd.intDiscountPence) AS discount, " .
            "            'true' AS confirmed, " .
            "            CASE " .
            "            WHEN callTable.dtmQueried IS NOT NULL THEN 'true' " .
            "            ELSE 'false' " .
            "            END AS queried, " .
            "            IF(intSchedulePaymentId is null,'yes','no') AS paymentRequired " .
            "       FROM tblCall callTable " .
            " INNER JOIN dbProductComponents.tblCallDestination d " .
            "         ON callTable.intCallDestinationId = d.intCallDestinationId " .
            "  LEFT JOIN tblFavouriteNumbersDiscount fnd " .
            "         ON callTable.intCallId = fnd.intCallId " .
            "      WHERE callTable.intProductComponentInstanceID IN (" . implode(', ', $arrWlrLineRentInsId) . ")" .
            "     )UNION " .
            "    (SELECT fraud.vchDestinationCli AS name, " .
            "            fraud.dtmStart AS time, " .
            "            fraud.intRoundedDurationSeconds AS length, " .
            "            fraud.intEstimatedPricePence AS price, " .
            "            d.vchDisplayName as destination, " .
            "            0 AS discount, " .
            "            'false' as confirmed, " .
            "            'false' as queried, " .
            "            'no' as paymentRequired " .
            "       FROM tblFraudCall fraud " .
            " INNER JOIN dbProductComponents.tblCallDestination d " .
            "         ON d.intCallDestinationId = fraud.intCallDestinationId " .
            "      WHERE fraud.intProductComponentInstanceID IN (" . implode(', ', $arrWlrLineRentInsId) . ") " .
            "        AND fraud.bolObsolete=0) " .
            "   ORDER BY time DESC " .
            "      LIMIT $intOffset,$intPageLength";

        $dbhRead = get_named_connection_with_db('wlr_calls_reporting');

        if (false === ($resResult = PrimitivesQueryOrExit($strSQL, $dbhRead, 'CWlrProduct::getCallsMade', true))) {
            // If you disable ExitOnFailure then you may want to add something here to log the failure
            return false;
        }

        $arrResults = PrimitivesResultsAsArrayGet($resResult);

        foreach ($arrResults as $intKey => $arrData) {

            if ($arrData['name']) {

                $intHour = str_pad(floor($arrData['length'] / 3600), '2', '0', STR_PAD_LEFT);
                $intMins = str_pad(floor($arrData['length'] / 60) - ($intHour * 60), '2', '0', STR_PAD_LEFT);
                $intSec = str_pad($arrData['length'] - ($intHour * 3600) - ($intMins * 60), '2', '0', STR_PAD_LEFT);
                $strTotalDuration = "$intHour:$intMins:$intSec";

                $arrResults[$intKey]['length'] = $strTotalDuration;

            } else {

                $arrResults[$intKey]['name'] = preg_replace(
                    '/^.*destination type /', '', $arrData['destination']
                );
                $arrResults[$intKey]['length'] = 'N/A';
            }

        }
        //now get the total
        $strSQL = "SELECT SUM(count) AS count " .
            "  FROM (SELECT count(*) AS count " .
            "          FROM tblCall " .
            "         WHERE intProductComponentInstanceID IN (" . implode(', ', $arrWlrLineRentInsId) . ") " .
            "     UNION ALL " .
            "        SELECT count(*) AS count " .
            "          FROM tblFraudCall " .
            "         WHERE intProductComponentInstanceID IN (" . implode(', ', $arrWlrLineRentInsId) . ") " .
            "           AND bolObsolete=0) as tblFraudAndCalls;";

        $resResult = PrimitivesQueryOrExit($strSQL, $dbhRead);
        $arrResults['count'] = PrimitivesResultGet($resResult, 'count');

        return $arrResults;
    } // function getCallsMade

    /**
     * Find debt management feature pcid if we have one.
     *
     * There are two types of debt management available for use:
     *
     * - WlrCallBarringOCB
     * - WlrCallBarringRTCC
     *
     * These two call barring handles are not technically "features" in the
     * sense that a customer cannot add them to their account or buy them.
     *
     * Instead they are a "meta-feature" type of thing, whereby the system
     * uses these to track the status of debt management orders during the
     * provisioning process.
     *
     * @return int Product comoponent instance id of WlrCallBarringXXXX
     *             component, or false if none
     **/
    public function getDebtManagementFeaturePcid()
    {
        $dbh = get_named_connection_with_db('userdata');   // avoid replication lag

        $sql = "    SELECT pci.intProductComponentInstanceId " .
            "      FROM userdata.tblProductComponentInstance as pci " .
            "INNER JOIN dbProductComponents.tblProductComponent as pc ON pc.intProductComponentID = pci.intProductComponentID  " .
            "INNER JOIN dbProductComponents.tblStatus AS st ON st.intStatusID = pci.intStatusID " .
            "     WHERE intComponentId = " . $this->getComponentId() . " AND  pc.vchHandle IN ('WlrCallBarringOCB', 'WlrCallBarringRTCC') AND st.vchHandle != 'DESTROYED'  LIMIT 1";

        $result = PrimitivesQueryOrExit($sql, $dbh);
        $pcid = PrimitivesResultGet($result, 'intProductComponentInstanceId');

        if ((int)$pcid > 0) {
            return $pcid;
        }

        return false;

    }

    private function applyDebtManagement($bolAllowQueuedActivate = false)
    {
        // BPR 2008 - adding in optional flag to allow queued activate orders to have debt management applied, as we can now apply
        //            all calls barred from the contract start as an option on the line.
        if ($this->m_strStatus != 'queued-deactivate' &&
            $this->m_strStatus != 'deactive' &&
            ($this->m_strStatus != 'queued-activate' || (true === $bolAllowQueuedActivate && $this->m_strStatus == 'queued-activate'))
        ) {

            // WLR3 KCI -- If we've got a WLR account, then we send off the debt
            //             management order straight away to the Java tier.
            if ($this->provisionedOnWlr3()) {
                $this->addDebtManagementAndSendWlr3Order();
            }

            //Change the component status to be queued-deactivate
            return $this->prvSetStatus('queued-deactivate');
        }

        return false;   // not done; we haven't been put into a debt management state
    }

    /**
     * Add a debt management feature of the appropriate type and sends an
     * order to the Java layer for WLR3
     *
     * @return void
     **/
    private function addDebtManagementAndSendWlr3Order()
    {
        error_log("DJM: starting debt management");

        // Get the call barring type that's needed
        $callBarringType = $this->getCallBarringType();

        if ($callBarringType === false) {
            // Call barring isn't available.  getCallBarringType() will have already flagged this up, so we
            // simply abort silently
            return;
        }

        error_log("DJM: got call barring type: [$callBarringType]");

        $callFeatureHandle = "WlrCallBarring{$callBarringType}";

        $callFeature = CProductComponent::createInstanceFromComponentID(
            $this->getComponentID(),
            $callFeatureHandle,
            array('ACTIVE')
        );

        $productComponentId = CProductComponent::getProductComponentIDByHandle($callFeatureHandle);

        //Sanity check - create new Cf only if there is no active component of that type
        if (!is_object($callFeature)) {
            $callFeature = CProductComponent::create(
                $this->getServiceID(),
                $this->getComponentID(),
                $productComponentId,
                0,
                false,
                'WLR'
            );
        }

        $featureInstanceId = $callFeature->getProductComponentInstanceID();

        $this->sendWlr3DebtManagementOrder(
            $callFeatureHandle,
            $featureInstanceId,
            Wlr3_FeatureOrderTypes::ADD_DEBT_MANAGEMENT
        );
    }

    /**
     * Send WLR3 debt management order off to the Java tier
     *
     * @var string $callFeatureHandle Call feature handle
     * @var int    $featureInstanceId Product component instance id of debt
     *                                 management feature.
     * @var string $orderType         Add or Remove Debt Management (from Wlr3_FeatureOrderTypes)
     *
     * @return void
     **/
    protected function sendWlr3DebtManagementOrder(
        $callFeatureHandle,
        $featureInstanceId,
        $orderType
    ) {
        // Find the primary cli...
        $service = userdata_service_get($this->getServiceId());

        $wlrOrder = new Wlr3_PlaceOrder();
        $debtManagementOrder = new Wlr3_OrderTypeFeature(
            new Int($this->getServiceId()),
            new Int($this->getComponentID()),
            $orderType,
            new String($service['cli_number']),
            array($callFeatureHandle => $featureInstanceId)
        );

        try {

            $wlrOrder->addOrder($debtManagementOrder);
            $wlrOrder->sendOrder();

        } catch (Exception $e) {

            // Ignoring this because the parent class raises an auto-problem
            // and we want the process to continue.
        }
    }

    /**
     * Sends a cancel order request to the java tier.
     *
     * @param string $orderId       The order ID we wish to cancel
     * @param array  $errorMessages Array of error messages in the cancel order failed
     *
     * @return boolean Whether the cancel order request was successful
     */
    public function sendWlr3CancelOrder($orderId, &$errorMessages)
    {
        $wlr3HttpApi = new Wlr3_HttpApi(new BusTier_CurlHttpClient());

        try {

            // Send a cancel order request
            $response = $wlr3HttpApi->sendCancelOrder($orderId);

        } catch (Exception $e) {

            // Ignoring this because the parent class raises an auto-problem and we want the process to continue.
            $response = new Wlr3_Response_CancelOrder('');
        }

        return $response;
    }

    /**
     * Sends a request to the java tier to find out if there are any orders of the requested type that are
     * in progress.
     *
     * @param string $orderType     Order type (@see Wlr3_Response_OrderStatus)
     * @param string $action        Action (@see Wlr3_Response_OrderStatus)
     *                              not applicable for all order types
     * @param string $orderId       Order ID of the order in progress
     *
     * @return boolean whether an active order of the requested type is in progress
     */
    public function isWlr3OrderInProgress($orderType, $action, &$orderId)
    {
        $wlr3HttpApi = new Wlr3_HttpApi(new BusTier_CurlHttpClient());

        try {

            // Get any orders of the requested type that are in progress
            $response = $wlr3HttpApi->getOrders(
                $this->getServiceId(),
                $orderType,
                Wlr3_Response_OrderStatus::ACTIVE_ORDER_STATUS
            );

        } catch (Exception $e) {

            // An error occurred trying to get the order status so assume there is no order in progress.
            return false;
        }

        // Get the order statuses
        $orderStatuses = $response->getOrderStatuses();

        switch ($orderType) {
            case Wlr3_Response_OrderStatus::DEBT_MANAGEMENT_ORDER_TYPE:
                // For these order types we must check for the requested action
                if (count($orderStatuses) > 0) {

                    // Do any of the orders have the requested action?
                    foreach ($orderStatuses as $orderStatus) {

                        if ($orderStatus->getAction() == $action) {

                            // Return the order ID
                            $orderId = $orderStatus->getOrderId();

                            return true;
                        }
                    }
                }
                break;

            default:
                // For all other order types we are only expecting one order status.
                if (count($orderStatuses) == 1) {

                    $orderStatus = $orderStatuses[0];
                    $orderId = $orderStatus->getOrderId();

                    return true;
                }
                break;
        }

        return false;
    }

    /**
     * Sends a request to the java tier to find out if there are any active add debt management orders
     * in progress.
     *
     * @param string $orderId Order ID of the active add debt management order
     *
     * @return boolean whether an active add debt management order is in progress
     */
    protected function isWlr3AddDebtManagementOrderInProgress(&$orderId)
    {
        return $this->isWlr3OrderInProgress(
            Wlr3_Response_OrderStatus::DEBT_MANAGEMENT_ORDER_TYPE,
            Wlr3_Response_OrderStatus::ADD_ACTION,
            $orderId
        );
    }

    /**
     * Checks whether an add debt management order is still in progress, if it is then the order is
     * cancelled.  If it is not in progress then a remove debt management order is sent.
     *
     * @return void
     */
    private function removeWlr3DebtManagement()
    {
        $sendRemoveDebtManagementOrder = true;

        if ($this->isWlr3AddDebtManagementOrderInProgress($orderId)) {

            $sendRemoveDebtManagementOrder = false;

            // An add debt management order is still in progress so cancel it.
            $response = $this->sendWlr3CancelOrder($orderId, $errorMessages);

            // Check if the request was successful
            if (!$response->isSuccess()) {

                // An error occurred sending the cancel order request
                $message = 'Unable to cancel the debt management order for the following reasons: ' .
                    $response->getErrorsAsString();

                $message .= "\n Please place a Remove Debt Managament order once the Add Debt Mangement
                    order completes.";

                // Create ticket on account for WLR Rejections pool to handle.
                $teamId = phplibGetTeamIdByHandle('WLR3_REJECTIONS');
                $actionerId = 'zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz'; // System User
                $serviceId = $this->getServiceId();

                $ticketId = tickets_ticket_add(
                    'Script',       // The origin of the Question Internal, Portal or Script
                    $serviceId,     // The service identifier associated with the user
                    0,              // ID of the error class
                    0,              // ID of the error subclass
                    'Open',         // Question status 'Open', 'Closed', 'Assigned'
                    $actionerId,    // ID of the staff member creating the ticket, 0 if customer
                    $message,       // Body text to be added to the created contact
                    '',             // The service_id of the person this Question should be returned to
                    // (defaults to 0)
                    $teamId         // The id of the customer services team that should deal with the ticket,
                // should be -1 if Question is with the customer
                );
            }
        }

        // A remove debt management order must be sent only if the add debt management order has completed.
        if ($sendRemoveDebtManagementOrder) {

            // Send order to the Java tier to remove the debt management
            $callBarringType = $this->getCallBarringType();

            if ($callBarringType !== false) {
                $callFeatureHandle = "WlrCallBarring{$callBarringType}";

                $callFeature = CProductComponent::createInstanceFromComponentID(
                    $this->getComponentID(),
                    $callFeatureHandle,
                    array('QUEUED_ACTIVATE', 'ACTIVE', 'QUEUED_DEACTIVATE', 'UNCONFIGURED')
                );

                //Sanity check - create new Cf only if we don't have one..
                if (!is_object($callFeature)) {

                    $productComponentId = CProductComponent::getProductComponentIDByHandle($callFeatureHandle);

                    $callFeature = CProductComponent::create(
                        $this->getServiceID(),
                        $this->getComponentID(),
                        $productComponentId,
                        0,
                        false,
                        'WLR'
                    );
                }

                // If our instance isn't active for some reason (e.g. we've just created it above)
                // then activate it so everything's in the correct state
                if ($callFeature->getStatus() != 'ACTIVE') {
                    $callFeature->enable(
                        array('uxtEnableDate' => time(), 'bolTakeProRataPayment' => false)
                    );
                }

                $featureInstanceId = $callFeature->getProductComponentInstanceID();

                // Send a remove debt management order
                $this->sendWlr3DebtManagementOrder(
                    $callFeatureHandle,
                    $featureInstanceId,
                    Wlr3_FeatureOrderTypes::REMOVE_DEBT_MANAGEMENT
                );
            }
        }
    }

    /**
     * Returns the call type of call barring required based on the WLR product
     * we're provisioned on
     *
     * @return string or boolean false
     */
    public function getCallBarringType()
    {
        // Populate a mapping of serviceComponentId => call barring type
        if (empty($this->_callBarringMap)) {
            $query = '
     SELECT dmt.vchHandle, scp.intServiceComponentId
      FROM products.tblWlrDebtManagement AS dm
INNER JOIN products.tblServiceComponentProduct AS scp
        ON scp.intServiceComponentProductID = dm.intServiceComponentProductID
INNER JOIN products.service_components AS sc
        ON sc.service_component_id = scp.intServiceComponentID
INNER JOIN products.tblWlrDebtManagementType AS dmt
        ON dmt.intWlrDebtManagementTypeId = dm.intWlrDebtManagementTypeId
     WHERE dm.dteEffectiveFrom <= NOW()
       AND dm.dteEffectiveTo IS NULL';

            $dbhConn = get_named_connection_with_db('userdata_reporting');
            $result = PrimitivesQueryOrExit($query, $dbhConn);
            $types = PrimitivesResultsAsArrayGet($result);

            foreach ($types as $type) {
                $this->_callBarringMap[$type['intServiceComponentId']]
                    = $type['vchHandle'];
            }
        }

        $scID = $this->getServiceComponentID();
        $cbtID = (isset($this->_callBarringMap[$scID]) ? $this->_callBarringMap[$scID] : false);

        if ($cbtID === false) {
            // DJM2013/76347: if we're trying to apply call barring to a service component which doesn't actually
            // have call barring, this needs to be flagged up as an auto-problem.
            pt_raise_autoproblem(
                'WLRCallsProcessFilesException',
                'Unable to find call barring details for service component',

                "An attempt has been made to get the call barring type for service component [$scID]," .
                " however, this could not be found. This means that call barring cannot be applied to customer" .
                " [" . $this->getServiceID() . "] and component [" . $this->getComponentID() . "].\n" .
                " The component has been marked as being in the queued-deactive state; the customer will need to be" .
                " manually barred via the SI portal (the Workplace tool will NOT work!) and an investigation should" .
                " be carried out to identify why the service component does not have call barring enabled.\n" .
                " See " . __FILE__ . " - " . __METHOD__ . "() for further details."
            );
        }

        // This is a public method, but a search indicates that it is only used internally by this function
        // It also looks like an empty string can potentially be valid
        // We therefore use boolean false to indicate that the date could not be found
        return $cbtID;
    }

    /**
     *
     * <AUTHOR> Smith
     *
     * @param boolean $bolForceRemoval   - TRUE = force removal of call barring regardless
     *                                   of account failed billing state.
     *                                   FALSE = causes call barring only to be removed if
     *                                   the account is in a non-failed-billing state.
     *
     * $bolForceRemoval is *not* an optional parameter. This hopefully will help catch any
     * misuse of this method if it's assumed that it doesn't take any parameters. I want to
     * avoid having a default parameter value here, as neither assuming true or false would
     * be good depending on the context that the method was used.
     *
     * @return boolean - TRUE = call barring request allowed, and scheduled to be removed.
     *                   FALSE = call barring request denied, or error
     */

    public function removeDebtManagement($bolForceRemoval)
    {
        // If you *are* passing true to this method, think very carefully..:
        //
        // "is it *really* what I want to do?" :-)
        //
        // Quite possibly, it isn't.

        if ($bolForceRemoval !== true &&
            $bolForceRemoval !== false
        ) {
            // yep, I'm paranoid - it *MUST* be either true or false in order
            // to progress any further.
            return false;
        }

        if ($bolForceRemoval === false) {
            // As we're not forcing removal of debt management, it can only be done if they
            // haven't been in failed billing for too long.

            if (C_Financial_Failed_Billing::isAfterFailedBillingPeriod($this->getServiceID())) {

                // They've been in failed billing for far too long (>=14 days limit) and so
                // we can't remove debt management in this situation.
                return false;
            }
        }

        switch ($this->m_strStatus) {
            case 'deactive':

                $strNewStatus = 'queued-reactivate';
                break;

            case 'queued-deactivate':

                $arrCompIds = $this->listProductComponentInstanceIdByHandle();

                $intLineRentId = isset($arrCompIds['WlrLineRent']) ? $arrCompIds['WlrLineRent'] : null;

                if ($intLineRentId &&
                    (Wlr_OrderType_DebtManagement::IsDebtManagementOrderPending($intLineRentId, array('NEW')) > 0)
                ) {

                    $strNewStatus = 'queued-reactivate';

                } else {

                    $strNewStatus = 'active';
                }

                break;

            default:

                $strNewStatus = null;
        }

        if ($strNewStatus) {

            if ($this->provisionedOnWlr3()) {

                $this->removeWlr3DebtManagement();
            }

            // Set the component status
            return $this->prvSetStatus($strNewStatus);
        }

        return false;
    }

    /**
     * Gets the string modifier for email templates to send the wlr3 version
     * instead of the WLR2 version
     *
     * @param string $modifier The string to return if we've got a wlr3 account
     *                         Only required if the modifier is different to
     *                         wlr3_
     *
     * @return string Either a template modifier or empty string if WLR2
     **/
    public function getWlr3Modifier($modifier = 'wlr3_')
    {
        if ($this->provisionedOnWlr3()) {
            return $modifier;
        }

        return '';
    }


////////////////////////////////////////////////////////////////////////////////

    /*
        --------------------------------------------
        applyDebtManagementDueToCreditLimitReached()
        --------------------------------------------
    */

    public function applyDebtManagementDueToCreditLimitReached($intCreditLimitPence, $intOverLimit)
    {
        // Attempt to apply debt management. It might fail if it's already in
        // place, but that doesn't matter. We just give them some different wording.

        // If it's a wlr3 account, change the template we send for res...
        $wlr3Modifier = $this->getWlr3Modifier('3');

        if ($this->applyDebtManagement()) {

            $strEmailTemplate = "wlr{$wlr3Modifier}_credit_limitbreach";

            if (true == $this->isBusinessAccount()) {

                $strEmailTemplate = "wlr{$wlr3Modifier}_credit_limitbreach_business";
            }

        } else {

            $strEmailTemplate = "wlr{$wlr3Modifier}_credit_limitbreach_managed";
            if (true == $this->isBusinessAccount()) {

                $strEmailTemplate = "wlr{$wlr3Modifier}_credit_limitbreach_managed_business";
            }

        }

        // Whether we were actually able to apply debt management is moot - we still
        // need to let them know that they've gone over their credit limit.

        $arrParams = array(
            'strCallCredit'  => sprintf("%.2f", (($intCreditLimitPence + $intOverLimit) / 100)),
            'strCreditLimit' => sprintf("%.2f", ($intCreditLimitPence / 100))
        );

        EmailHandler::sendEmail($this->getServiceID(), $strEmailTemplate, $arrParams);

        $objEventLogger = $this->prvGetEventLogger();
        $objEventLogger->logWlrApplyDebtManagementDueToCreditLimitReached();
        unset($objEventLogger);
    }

////////////////////////////////////////////////////////////////////////////////

    /*
        ------------------------------------
        applyDebtManagementDueToFraudCalls()
        ------------------------------------
    */

    public function applyDebtManagementDueToFraudCalls()
    {

        if ($this->applyDebtManagement()) {
            // If it's a wlr3 account, change the template we send for res...
            $wlr3Modifier = $this->getWlr3Modifier();

            $intLineRentId = $this->getLineRentId();
            if (null == $intLineRentId) {
                return false;
            }

            $objLineRent = CProductComponent::createInstance($intLineRentId);
            $arrConfig = $objLineRent->getConfig();
            $arrMailVars = array('intCallBarringChargePence' => number_format($arrConfig['intCallBarringChargePence'] / 100, 2));

            if ($this->isBusinessAccount()) {
                $strEmailTemplate = "{$wlr3Modifier}fraud_outgoing_calls_stopped_business";
            } else {
                $strEmailTemplate = "{$wlr3Modifier}fraud_outgoing_calls_stopped";
            }

            EmailHandler::sendEmail($this->getServiceID(), $strEmailTemplate, $arrMailVars);

        }
    }

////////////////////////////////////////////////////////////////////////////////

    /*
        ---------------------------------------
        applyDebtManagementDueToNuisanceCalls()
        ---------------------------------------
    */

    public function applyDebtManagementDueToNuisanceCalls()
    {
        if ($this->applyDebtManagement()) {
            // If it's a wlr3 account, change the template we send for res...
            $wlr3Modifier = $this->getWlr3Modifier();

            $intLineRentId = $this->getLineRentId();
            if (null == $intLineRentId) {
                return false;
            }

            $objLineRent = CProductComponent::createInstance($intLineRentId);
            $arrConfig = $objLineRent->getConfig();
            $arrMailVars = array('intCallBarringChargePence' => number_format($arrConfig['intCallBarringChargePence'] / 100, 2));

            if ($this->isBusinessAccount()) {

                $strEmailTemplate = "{$wlr3Modifier}nuisance_call_outgoing_calls_stopped_business";

            } else {

                $strEmailTemplate = "{$wlr3Modifier}nuisance_call_outgoing_calls_stopped";
            }


            EmailHandler::sendEmail($this->getServiceID(), $strEmailTemplate, $arrMailVars);
        }
    }

////////////////////////////////////////////////////////////////////////////////

    /**
     * Apply call barring to an account (any vISP) in a generic fashion,
     * giving a generic (non-vISP-based) reason.
     *
     * @access public
     */

    public function applyDebtManagementGeneric()
    {
        if ($this->applyDebtManagement()) {

            // If it's a wlr3 account, change the template we send for res...
            $wlr3Modifier = $this->getWlr3Modifier();

            if ($this->isBusinessAccount()) {

                $strEmailTemplate = "{$wlr3Modifier}apply_call_barring_generic_business";

            } else {

                $strEmailTemplate = "{$wlr3Modifier}apply_call_barring_generic";
            }

            EmailHandler::sendEmail($this->getServiceID(), $strEmailTemplate);
        }
    }

////////////////////////////////////////////////////////////////////////////////

    /**
     * Trigger call barring to be applied and email account with appropriate message.
     *
     * @access public
     *
     * @param string   - the date that they should have initially paid. Supplied
     *                 purely for cosmetic purposes, in whatever form it's in.
     * @param integer  - number of days of grace they had. Most likely 14.
     **/
    public function applyDebtManagementDueToExceedingGrace($strBillingDate, $intDays)
    {
        $arrMailTemplateData = array(
            'strBillingDate' => $strBillingDate,
            'strGracePeriod' => $intDays
        );

        // If it's a wlr3 account, change the template we send for res...
        $wlr3Modifier = $this->getWlr3Modifier();

        if ($this->applyDebtManagement()) {

            // If we're able to request placing callbarring, then email them.

            // As per the flowchart: "a=14 days passed and calls barring action has been taken."

            $strEmailHandle = "{$wlr3Modifier}failed_billing_grace_exceeded_call_barring_applied";
            if ($this->isBusinessAccount()) {

                $strEmailHandle = "{$wlr3Modifier}failed_billing_grace_exceeded_call_barring_applied_business";
            }


        } else {

            // In this case, callbarring has already been applied for some reason
            // (could have been fraudulent activity, nuisance calls, or going over
            // credit limit) so we just inform them that the 14-day (or however long,
            // see the intDay parameter of this method) period has gone by and that
            // we won't be removing their call barring.

            // As per flowchart: "b=14 days have passed."

            $strEmailHandle = "{$wlr3Modifier}failed_billing_grace_exceeded_call_barring_still_in_place";

            if ($this->isBusinessAccount()) {

                $strEmailHandle = "{$wlr3Modifier}failed_billing_grace_exceeded_call_barring_still_in_place_business";
            }
        }
        EmailHandler::sendEmail($this->getServiceID(), $strEmailHandle, $arrMailTemplateData);

        $objEventLogger = $this->prvGetEventLogger();
        $objEventLogger->logWlrApplyDebtManagementDueToFailedBilling();
        unset($objEventLogger);
    }

    /**
     * Apply debt management call barring for business accounts as requested feature
     *
     * @access public
     */

    public function applyDebtManagementBusiness()
    {
        if ($this->applyDebtManagement(true)) {
            // Send email 4 from the DDD
            try {
                EmailHandler::sendEmail($this->getServiceID(), 'ocb_order_sent');
            } catch (EmailHandler_Exception $excEmailHandler) {

                pt_raise_autoproblem(
                    'EmailHandler generated an error',
                    'EmailHandler generated an error',
                    $excEmailHandler->getMessage(),
                    __FILE__
                );
            }
        }
    }

////////////////////////////////////////////////////////////////////////////////

    /**
     * Applies debt managment and logs this event
     */
    public function applyDebtManagementDueToFailedBilling()
    {
        $this->applyDebtManagement();

        $objEventLogger = $this->prvGetEventLogger();
        $objEventLogger->logWlrApplyDebtManagementDueToFailedBilling();

        unset($objEventLogger);
    }

////////////////////////////////////////////////////////////////////////////////

    /*
        -------------------------------------
        removeDebtManagementDueToFraudCalls()
        -------------------------------------
    */

    public function removeDebtManagementDueToFraudCalls()
    {
        if ($this->removeDebtManagement(false)) {

            if ($this->isBusinessAccount()) {

                $strMailTemplate = 'fraud_outgoing_calls_reactivated_business';

            } else {

                $strMailTemplate = 'fraud_outgoing_calls_reactivated';
            }

            EmailHandler::sendEmail($this->getServiceID(), $strMailTemplate);

        }
    }

////////////////////////////////////////////////////////////////////////////////

    /*
        -------------------------------------
        removeDebtManagementAndGiveNoReason()
        -------------------------------------
    */

    public function removeDebtManagementAndGiveNoReason()
    {
        // You could argue that this method is a waste of time as it's simply a wrapper for
        // removeDebtManagement(). However, should we later want to change this functionality,
        // it is already seperate from removeDebtManagement() which I'd prefer to keep
        // quasi-private.

        $this->removeDebtManagement(false);
    }

    /**
     * Remove call barring from an account (any vISP) in a generic fashion,
     * giving a generic (non-vISP-based) reason.
     *
     * @access public
     */

    public function removeDebtManagementGeneric()
    {
        if ($this->removeDebtManagement(false)) {

            if ($this->isBusinessAccount()) {

                $strEmailTemplate = 'remove_call_barring_generic_business';

            } else {

                $strEmailTemplate = 'remove_call_barring_generic';
            }

            EmailHandler::sendEmail($this->getServiceID(), $strEmailTemplate);
        }
    }

////////////////////////////////////////////////////////////////////////////////

    /**
     * Similar to the depreciated applyDebtManagementDueToFailedBilling() method
     * except this one doesn't apply debt management. It is invoked by when an account
     * fails to be billed, thus we inform them of what we *will* do with the phone
     * aspect of their account in X days' time.
     *
     * @access public
     *
     * @param array - array of data that can be displayed in the email template
     **/
    public function notifyDueToFailedBilling($arrCommonBillingParams)
    {

        // If it's a wlr3 account, change the template we send for res...
        $wlr3Modifier = $this->getWlr3Modifier();

        if ($this->isBusinessAccount()) {
            $strTextTemplate = "{$wlr3Modifier}phone_payment_failed_business";
        } else {
            $strTextTemplate = "{$wlr3Modifier}phone_payment_failed";
        }

        EmailHandler::sendEmail($this->getServiceID(), $strTextTemplate, $arrCommonBillingParams);
    }

    /**
     * Private Methods
     */

    /**
     * WLR Component status change logger.
     *
     * @access   private
     * <AUTHOR> Jones" <<EMAIL>>
     * @return
     */
    public function logStatusChange($strStatus)
    {
        $objEventLogger = $this->prvGetEventLogger();

        switch ($strStatus) {
            case 'ACTIVE':
                $objEventLogger->logStatusChange('WlrActivation');
                break;

            case 'DEACTIVE':
                $objEventLogger->logStatusChange('WlrDeactivation');
                break;

            case 'DESTROYED':
                $objEventLogger->logStatusChange('WlrDestruction');
                break;

            default:
                break;
        }
    } // function logStatusChange($strStatus)

    /**
     * Is this component a WLR component?
     *
     * Checks if a given component id is a WLR component
     *
     * <code>
     * $bolIsWlrProduct = CWlrProduct::isWlrProduct($intComponentID);
     * </code>
     *
     * @access public
     * @static
     * <AUTHOR>
     *
     * @param  int The component ID
     *
     * @return boolean true or false
     * @throws none none
     */
    public function isWlrProduct($intComponentID = 0)
    {
        if ($intComponentID < 1) {
            return false;
        }

        $dbhConn = get_named_connection_with_db('userdata');

        $intComponentID = addslashes(($intComponentID * 1));

        $strQuery = "SELECT count(*) as intCountPlusTalkComponents
                      FROM userdata.components c
                INNER JOIN products.tblServiceComponentProduct scp
                        ON c.component_type_id = scp.intServiceComponentID
                INNER JOIN products.tblServiceComponentProductType scpt
                        ON scp.intServiceComponentProductTypeID = scpt.intServiceComponentProductTypeID
                     WHERE c.component_id ='$intComponentID'
                       AND scpt.vchHandle = 'WLR'";

        $refResult = PrimitivesQueryOrExit($strQuery, $dbhConn);
        $intCount = PrimitivesResultGet($refResult, 'intCountPlusTalkComponents');

        return ($intCount > 0) ? true : false;
    }

    /**
     * Returns all chargable product components:
     * 1) subscription cost
     * 2) call features bundle (CFB) cost (if such exists)
     * 3) bundlable call features (CF) cost (if such exists)
     * 4) no budlable call features cost (if such exists)
     *
     * Can filter on Status e.g. ACTIVE for subscription components
     * Can filter on Product Status e.g. PRODUCT_COMPONENT_ACTIVE for Call Features
     *
     * @param  array   arrStatus             list of statuses to filter chargable components on
     * @param  array   arrCallFeatureStatus  list of product component statuses to filter on
     *
     * @access public
     * <AUTHOR>
     * @return array of objects
     */
    public function getChargableProductComponents($arrStatus = array('ACTIVE'),
                                                  $arrCallFeatureStatus = array(PRODUCT_COMPONENT_ACTIVE))
    {
        $strStatus = implode("','", $arrStatus);

        //lets collect all product components that have to be charged
        $arrChargableProductComponents = array();
        $objCFB = $this->getCallFeaturesBundle($arrCallFeatureStatus);
        if (is_object($objCFB)) {
            $arrChargableProductComponents[] = $objCFB;
        } else {
            //if there is no CFB on the account, there should be only one active bundable call feature
            //or there should be no bundlable call features
            $arrCF = $this->getBundlableCallFeaturesInstances($arrCallFeatureStatus);
            if (is_object($arrCF[0])) {
                $arrChargableProductComponents[] = $arrCF[0];
            }
        }
        //don't forget about nobundable call features
        $arrCF = $this->getNoBundlableCallFeaturesInstances($arrCallFeatureStatus);
        foreach ($arrCF as $objCF) {
            $arrChargableProductComponents[] = $objCF;
        }

        $arrChargableProductComponents = array_merge(
            $arrChargableProductComponents, $this->getSubsciptionProductComponents($arrStatus)
        );

        return $arrChargableProductComponents;
    }

    public function getSubsciptionProductComponents($arrStatus = array('ACTIVE'))
    {
        $strStatus = implode("','", $arrStatus);

        $arrChargableProductComponents = array();

        $intComponentID = $this->getComponentID();
        $intServiceComponentID = $this->getServiceComponentID();

        if ($intComponentID > 0 && $intServiceComponentID > 0) {
            //Get Chargaeable product components
            $strSql = "SELECT pci.intProductComponentInstanceID
                     FROM userdata.tblProductComponentInstance pci
                   INNER JOIN dbProductComponents.tblTariff t
                           ON t.intTariffID = pci.intTariffID
                   INNER JOIN dbProductComponents.tblProductComponent pc
                           ON pc.intProductComponentID = pci.intProductComponentID
                   INNER JOIN dbProductComponents.tblStatus s
                           ON s.intStatusID = pci.intStatusID
                   INNER JOIN dbProductComponents.tblProductComponentConfig pcc
                           ON pcc.intProductComponentID = pc.intProductComponentID
                   INNER JOIN products.tblServiceComponentProduct scp
                           ON scp.intServiceComponentProductID = pcc.intServiceComponentProductID
                        WHERE pci.intComponentID = '$intComponentID'
                          AND scp.intServiceComponentID = '$intServiceComponentID'
                          AND t.intCostIncVatPence > 0
                          AND pcc.dtmEnd IS NULL
                          AND s.vchHandle IN ('$strStatus')
                          AND pcc.intDefaultQuantity <> 0";

            $dbhConnection = get_named_connection_with_db('userdata');
            $resResult = PrimitivesQueryOrExit($strSql, $dbhConnection, 'Fetch chargeable product component instances', false);

            $arrProductComponentInstance = PrimitivesResultsAsListGet($resResult);
            foreach ($arrProductComponentInstance as $intProductComponentInstanceID) {
                $arrChargableProductComponents[] = CProductComponent::createInstance(
                    $intProductComponentInstanceID
                );
            }
        }

        return $arrChargableProductComponents;
    }

    /**
     * Method to generate prorata payment details during component activation
     *
     * @access public
     * <AUTHOR>
     * @return array
     */
    public function getActivationCostDetails($uxtProRataCalculationDate, $bolRetentionOffer = false)
    {
        $arrChargableProductComponents = $this->getChargableProductComponents();

        $intServiceID = $this->getServiceId();
        $arrSignupCostDetails = array();

        $arrService = userdata_service_get($intServiceID);

        $uxtMonthInterval = strtotime(date('Y-m-d', $uxtProRataCalculationDate) . ' +1 month');
        $uxtNextInvoice = strtotime($arrService['next_invoice']);

        // If the component is activated on the same day as the next invoice of BB, then we need to set the activation date as todays date as it messes up the
        // generateProRataPaymentDetails() as this gives a period of more than a month to calculate the prorata cost.
        // ($uxtNextInvoice > $uxtMonthInterval) -> this is for any reason if the edr has'nt run on any day and some accounts that needed to be activated has
        // their next_invoice moved

        if (($uxtNextInvoice > $uxtMonthInterval) || $uxtNextInvoice == strtotime(date('Y-m-d', time()))) {
            $uxtProRataCalculationDate = time();
        }

        foreach ($arrChargableProductComponents as $objProductComponent) {
            if ($bolRetentionOffer && $objProductComponent->getProductComponentID() == PRODUCT_COMPONENT_SUBSCRIPTION) {
                continue;
            }

            $intTariffID = $objProductComponent->getTariffID();
            $arrTariffDetails = CProduct::getTariffDetails($intTariffID);

            // Problem 69013
            // Line Rental Saver is (correctly) creating pro rata invoice items at 0.00
            //
            // The Line Rental Saver product component instance is a "special" meta-style feature
            // that customers don't explicitly purchase/add to their accounts.
            //
            // Because of this we don't want to include any form of pro-rata changes for this
            // type of feature.
            //
            // We can make this generic and take advantage of the payment frequency being set
            // to "NEVER".

            if ($arrTariffDetails['strPaymentFrequencyHandle'] == 'NEVER') {

                continue;
            }

            $arrProRataCostDetails = CProductComponent::generateProRataPaymentDetails(
                $intServiceID, $arrTariffDetails['intNextTariffID'],
                'WLR', $uxtProRataCalculationDate
            );

            $arrActivationCostDetails = array();
            $arrActivationCostDetails['arrProRataCostDetails'] = $arrProRataCostDetails;
            $arrActivationCostDetails['arrTariffDetails'] = $arrTariffDetails;
            $arrActivationCostDetails['intProductComponentInstanceId'] = $objProductComponent->getProductComponentInstanceID();
            $arrSignupCostDetails[] = $arrActivationCostDetails;
        }

        return $arrSignupCostDetails;
    }


    /**
     * Calculate the pro-rata activation cost of WLR.
     * This action has no side-effects on the account.
     *
     * @param int  $uxtProRataCalculationDate
     * @param bool $bolRetentionOffer
     *
     * @return array
     **/
    public function calculateProrataActivationCost($uxtProRataCalculationDate = 0, $bolRetentionOffer = false)
    {
        $arrProrataCostDetails = $this->getActivationCostDetails($uxtProRataCalculationDate, $bolRetentionOffer);

        $intTotalCost = 0;
        $arrInvoiceItems = array();

        foreach ($arrProrataCostDetails as $arrDetails) {

            $intTotalCost += $arrDetails['arrProRataCostDetails']['intProRataCost'];
            $arrInvoiceItems[] = array(
                'amount'      => $arrDetails['arrProRataCostDetails']['intProRataCost'] / 100,
                'description' => $arrDetails['arrProRataCostDetails']['strProRataDescription'],
                'gross'       => true
            );
        }

        return array(
            'arrProrataCostDetails' => $arrProrataCostDetails,
            'arrInvoiceItems'       => $arrInvoiceItems,
            'intTotalCost'          => $intTotalCost
        );
    }


    /**
     * Invoice an account for the pro-rata activation cost of WLR.
     * This could be done via DD or debit card, or even scheduled for
     * their next invoice date if the amount of money is below a
     * specific threshold.
     *
     * @param int   $uxtProRataCalculationDate
     *
     * @oaram int $uxtNextInvoiceDate
     *
     * @param bool  $bolRetentionOffer
     * @param array $arrProrataDetails
     *
     * @return bool
     **/
    protected function raiseInvoiceForProrataActivationCost($uxtProRataCalculationDate, $uxtNextInvoiceDate, $bolRetentionOffer, array $arrProrataDetails)
    {
        $intTotalCost = $arrProrataDetails['intTotalCost'];
        $arrProrataCostDetails = $arrProrataDetails['arrProrataCostDetails'];
        $arrInvoiceItems = $arrProrataDetails['arrInvoiceItems'];


        if ($uxtNextInvoiceDate == '') {

            $uxtNextInvoiceDate = UserdataGetNextMonthlyInvoiceDate($this->getServiceID());
        }


        $arrInvoicePeriod = UserdataGetMonthlyInvoicePeriod($this->getServiceID());


        // When does the period end?
        $uxtPeriodEndDate = $arrInvoicePeriod['uxtPeriodEnd'];


        //total cost equal or less than minimum pence charge we have to schedule the payment
        if ($intTotalCost <= WLR_MINIMUM_CHARGEABLE_AMOUNT) {
            //create scheduled payments for all required product components
            foreach ($arrProrataCostDetails as $arrDetails) {
                $objPaymentScheduler = new CProductComponentPaymentScheduler($arrDetails['intProductComponentInstanceId'], '');
                $objPaymentScheduler->addIncVatAmount(
                    $arrDetails['arrProRataCostDetails']['intProRataCost'],
                    $uxtNextInvoiceDate,
                    $arrDetails['arrProRataCostDetails']['strProRataDescription'],
                    $uxtProRataCalculationDate,
                    $uxtPeriodEndDate
                );

                $intSchedulePaymentID = $objPaymentScheduler->m_intScheduledPaymentID;

                if (!isset($intSchedulePaymentID) && $intSchedulePaymentID !== '') {
                    $strTicketBody = "A schedule payment could not be raised for &pound;" .
                        $arrDetails['arrProRataCostDetails']['floProRataCostDisplay'] / 100 .
                        " for the {$arrDetails['arrProRataCostDetails']['strProRataDescription']}. " .
                        "Please take this payment manually.";
                    $intTeamId = phplibGetTeamIdByHandle('CSC_BILLING');
                    tickets_ticket_add('Script', $this->getServiceID(), '', '', 'Open', 0, $strTicketBody, 0, $intTeamId);
                }
            }


            $bolResult = true;   // benefit of the doubt

        } else { //lets take payment immmidieatly
            //charge the full cost
            $strTotalProrataDescription = $this->getComponentName() . ' pro-rata charge';
            $intInvoiceID = CWlrProduct::takePayment(
                $this->getServiceID(), $arrInvoiceItems,
                $intTotalCost / 100, $strTotalProrataDescription
            );
            if (isset($intInvoiceID) && $intInvoiceID > 0) {
                //create scheduled payments for all required product components
                foreach ($arrProrataCostDetails as $arrDetails) {
                    $objPaymentScheduler = new CProductComponentPaymentScheduler($arrDetails['intProductComponentInstanceId'], '');
                    $objPaymentScheduler->addIncVatAmount(
                        $arrDetails['arrProRataCostDetails']['intProRataCost'],
                        time(),
                        $arrDetails['arrProRataCostDetails']['strProRataDescription'],
                        $uxtProRataCalculationDate,
                        $uxtPeriodEndDate
                    );
                    $intScheduledPaymentID = $objPaymentScheduler->m_intScheduledPaymentID;

                    if (($intScheduledPaymentID > 0) && ($intInvoiceID > 0)) {
                        $arrInvoiceDetails = financial_sales_invoice_get_full($intInvoiceID);
                        $intLineInvoiceId = (isset($arrInvoiceDetails['items'][0]['sales_invoice_item_id'])) ? $arrInvoiceDetails['items'][0]['sales_invoice_item_id'] : null;

                        $objScheduledPayment = new CProductComponentScheduledPayment($intScheduledPaymentID);
                        $objScheduledPayment->markAsInvoiced($intInvoiceID, $intLineInvoiceId);
                    }
                    $strTicketBody = "A payment was taken for &pound;" . ($arrDetails['arrProRataCostDetails']['floProRataCostDisplay']) .
                        " for the {$arrDetails['arrProRataCostDetails']['strProRataDescription']}. The component has been activated.";
                    tickets_ticket_add('Script', $this->getServiceID(), '', '', 'Closed', 0, $strTicketBody);

                }

                $bolResult = true;
            } else {

                $floTotalDisplayCost = number_format(($intTotalCost / 100), 2);

                $strTicketBody = "A payment could not be taken for &pound;$floTotalDisplayCost " .
                    "for the pro-rata cost of the activation of {$this->getComponentName()} component. " .
                    "The component has been activated, please take this payment manually.";
                $intTeamId = phplibGetTeamIdByHandle('CSC_BILLING');
                tickets_ticket_add('Script', $this->getServiceID(), '', '', 'Open', 0, $strTicketBody, 0, $intTeamId);
                $bolResult = false;
            }
        }

        return $bolResult;
    }


    /**
     * Charge the pro-rata activation cost on an account.
     * However it is billed is dependent on whether the account has an
     * active card or a Direct Debit instruction, and also whether the
     * amount of the pro-rata is above a particular threshold.
     *
     * @param int  $uxtProRataCalculationDate
     *
     * @oaram int $uxtNextInvoiceDate
     *
     * @param bool $bolRetentionOffer
     *
     * @return bool
     **/
    public function chargeProrataActivationCost($uxtProRataCalculationDate = 0, $uxtNextInvoiceDate = '', $bolRetentionOffer = false)
    {
        $arrProrataCostDetails = $this->calculateProrataActivationCost($uxtProRataCalculationDate, $bolRetentionOffer);

        if ($arrProrataCostDetails['intTotalCost'] <= 0)
            return true;

        return $this->raiseInvoiceForProrataActivationCost(
            $uxtProRataCalculationDate,
            $uxtNextInvoiceDate,
            $bolRetentionOffer,
            $arrProrataCostDetails
        );
    }

    /**
     * Gets customers current credit limit
     * @return int credit limit in pence
     */
    public static function getCreditLimitPence($intLineRentId)
    {
        $strQuery = 'SELECT IFNULL(lr.intCustomCreditLimitPence, cp.intCreditLimitPence) AS intCreditLimitPence
                   FROM tblConfigWlrLineRent AS lr
                   INNER JOIN dbProductComponents.tblCallPlan cp
                   ON cp.intCallPlanId = lr.intCallPlanId
                   WHERE lr.dtmEnd IS NULL ' .
            "AND lr.intProductComponentInstanceId = '{$intLineRentId}'";

        $dbhConn = get_named_connection_with_db('userdata');
        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConn);
        $arrCustomerCredit = PrimitivesResultGet($resResult);

        if (empty($arrCustomerCredit)) {
            return false;
        }

        return $arrCustomerCredit['intCreditLimitPence'];
    }

    /** used for calculation max credit limit to which CSC can raise
     *
     * @return bol
     */
    public function isBusinessAccount()
    {
        if (null === $this->bolIsBusinessAccount) {
            $strSector = CProduct::getCustomerSectorHandleFromServiceComponentID(
                $this->getServiceComponentID()
            );
            $this->bolIsBusinessAccount = $strSector == 'BUSINESS' ? true : false;
        }

        return $this->bolIsBusinessAccount;
    }

    /**
     * lazy-loading function for getting Line rental PCI id
     * @return int
     */
    public function getLineRentId()
    {
        if (empty($this->intLineRentId)) {

            $arrWlrComponent = $this->listProductComponentInstanceIdByHandle();

            if (false !== $arrWlrComponent)
                $this->intLineRentId = $arrWlrComponent['WlrLineRent'];
        }

        return $this->intLineRentId;
    }

    /**
     * Get max credit limit which CSC agent can set
     *
     * @param $strUserId string userid
     *
     * @return int credit limit in pence
     */
    public function getMaxCreditLimitForUser($strUserId)
    {

        $arrMediumCreditLimitGroups = array(
            self::CSC_TEAM_LEADERS,
            self::SA_CSC_MANAGERS,
            self::SA_CUSTOMER_SERVICE_TEAM_LEADERS,
            self::SA_TECHNICAL_SUPPORT_TEAM_LEADERS,
            self::CSC_TLS
        );
        $arrGroupsUserIsMemberOf = PHPLibGetGroupsUserIsMemberOf($strUserId);

        if (in_array(self::GROUP_FINANCE, $arrGroupsUserIsMemberOf)) {
            return self::MAX_CREDIT_LIMIT;

        } elseif (count(array_intersect($arrGroupsUserIsMemberOf, $arrMediumCreditLimitGroups)) > 0) {
            return self::MED_CREDIT_LIMIT;

        } elseif ($this->isBusinessAccount()) {
            return self::BASE_CREDIT_LIMIT_BUSINESS;

        } else {
            return self::BASE_CREDIT_LIMIT_RESIDENTIAL;
        }

    }

    /**
     * Set credit limit
     *
     * @param  int $intCreditLimitPence
     *
     * @return bool
     */
    public function setCreditLimitPence($intCreditLimitPence)
    {
        global $my_id;

        // Verify params
        if (!is_numeric($intCreditLimitPence) || 0 > $intCreditLimitPence)
            return false;

        //Check permissions
        // Up to medium limit anyone can change credit limit
        if ($intCreditLimitPence > $this->getMaxCreditLimitForUser($my_id))
            return false;

        // If we weren't able to get LineRentId
        if (null === $this->getLineRentId())
            return false;

        $strQuery = 'UPDATE tblConfigWlrLineRent ' .
            "SET intCustomCreditLimitPence = '" . mysql_real_escape_string($intCreditLimitPence) . "' " .
            "WHERE intProductComponentInstanceId = '" . mysql_real_escape_string($this->getLineRentId()) . "'";

        $dbhConn = get_named_connection_with_db('userdata');

        if (PrimitivesQueryOrExit($strQuery, $dbhConn)) {

            // Logging
            $arrUser = phplib_user_get($my_id);

            $strText = $this->getComponentName() . ' credit limit was changed to &pound;'
                . sprintf("%.2f", $intCreditLimitPence / 100)
                . ' by ' . $arrUser['username'];

            tickets_ticket_add('Script', $this->getServiceID(), 0, 0, 'Closed', $my_id, $strText);

            return true;
        }

        return false;
    }

////////////////////////////////////////////////////////////////////////////////

    /*
        ----------------------------------------
        getOverAllowedLimitAndCreditLimitPence()
        ----------------------------------------

        @access public static
    */

    public static function getOverAllowedLimitAndCreditLimitPence($intLineRentId, $bolReadFromMaster = true)
    {
        // This is a bit hackey. We might want to refactor it later.
        $intServiceId = CWlrProduct::getServiceIdFromLineRentProdCompInsId($intLineRentId);

        $arrWlrLineRentInsId = CWlrProduct::getAllLineRentInstanceIdFromServiceId($intServiceId);

        if ($arrWlrLineRentInsId === false || empty($arrWlrLineRentInsId)) {
            return false;
        }

        $strQuery = "
            SELECT
                SUM(redeemed_call.balance) AS callBalance
            FROM
                (
                    SELECT
                        c.intProductComponentInstanceId,
                        c.intCallId,
                        c.intCallPlanId,
                        c.intCustomerPricePence - SUM(ifnull(pcr.intCreditPenceRedeemed,0)) AS balance
                    FROM tblCall c
                    LEFT JOIN tblPrepaidCreditRedemption pcr
                        ON pcr.intCallId = c.intCallId
                    WHERE c.intSchedulePaymentId IS NULL
                        AND c.intProductComponentInstanceId IN (" . implode(', ', $arrWlrLineRentInsId) . ")
                    GROUP BY c.intCallId, c.intProductComponentInstanceId, c.intCallPlanId, c.intCustomerPricePence
                    ORDER BY NULL
                ) AS redeemed_call";

        // We probably only really want to use the master for when callprocessing is calling
        // this method, as it can be time-critical and we can't afford any form of replication
        // lag to get in the way of callprocessing.

        if ($bolReadFromMaster) {

            $strDbHandle = 'wlr_calls';

        } else {

            $strDbHandle = 'wlr_calls_reporting';
        }

        $dbhConn = get_named_connection_with_db($strDbHandle);
        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConn);
        $arrCustomerCredit = PrimitivesResultGet($resResult);

        if (empty($arrCustomerCredit)) {
            return false;
        }

        $arrCustomerCredit['intCreditLimitPence'] = CWlrProduct::getCreditLimitPence($intLineRentId);
        $arrCustomerCredit['creditLeft'] = $arrCustomerCredit['intCreditLimitPence'] - $arrCustomerCredit['callBalance'];
        $arrCustomerCredit['overLimit'] = ($arrCustomerCredit['creditLeft'] < 0) ? abs($arrCustomerCredit['creditLeft']) : 0;

        return $arrCustomerCredit;
    }

////////////////////////////////////////////////////////////////////////////////

    /**
     * Returns number of pence the customer is over his allowed credit limit
     * e.g. anytime+, 7500 limit, made calls for 10000, got into debt management, returns 2500
     *
     * @access public static
     * @return integer absolute value of pence over credit limit
     */
    public function getOverAllowedLimitPence($intLineRentId, $bolReadFromMaster = true)
    {
        $arrCustomerCredit = CWlrProduct::getOverAllowedLimitAndCreditLimitPence(
            $intLineRentId, $bolReadFromMaster
        );

        return ($arrCustomerCredit === false) ? false : $arrCustomerCredit['overLimit'];
    }

    /**
     * Returns total charge of cdr & fraud calls that is not redeemed and has no scheduled payment
     *
     * @access public static
     * @return integer absolute value of pence over credit limit
     */
    public function getOutstandingCallBalancePence($intLineRentId)
    {
        // Nasty hack to get all Line Rent Comp Ins IDs
        $intServiceId = CWlrProduct::getServiceIdFromLineRentProdCompInsId($intLineRentId);

        $arrWlrLineRentInsId = CWlrProduct::getAllLineRentInstanceIdFromServiceId($intServiceId);

        if ($arrWlrLineRentInsId === false || empty($arrWlrLineRentInsId)) {
            return false;
        }

        $strQuery = 'SELECT sum(balance) balance ' .
            'FROM ( SELECT c.intProductComponentInstanceId, c.intCallId, c.intCallPlanId, ' .
            '      intCustomerPricePence - SUM(ifnull(pcr.intCreditPenceRedeemed,0)) balance, ' .
            '      intCustomerPricePence ' .
            ' FROM tblCall c ' .
            ' LEFT JOIN tblPrepaidCreditRedemption pcr ON pcr.intCallId = c.intCallId ' .
            'WHERE intProductComponentInstanceId IN (' . implode(', ', $arrWlrLineRentInsId) . ') ' .
            '  AND intSchedulePaymentId IS NULL ' .
            'GROUP BY c.intProductComponentInstanceId, c.intCallId , c.intCallPlanId, ' .
            '         c.intCustomerPricePence ' .
            'HAVING intCustomerPricePence - SUM(ifnull(pcr.intCreditPenceRedeemed,0)) >= 0 ) d ';

        $dbhConn = get_named_connection_with_db('wlr_calls_reporting');
        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConn);
        $arrCustomerCredit = PrimitivesResultGet($resResult);

        if (!empty($arrCustomerCredit)) {
            return $arrCustomerCredit['balance'];

        } else {
            return false;
        }
    }

    /**
     * Creates a scheduled cancellation entry and calls onScheduleCancellation on each of this object's product
     * components
     *
     * @param  integer $uxtRequestedCancellationDate Unix timestamp of when the cancellation is due
     * @param  boolean $bolOnContractEnd             If cancellation must wait for end of contract or happen as
     *                                               soon as date due arrives
     * @param  boolean $bolAdjustDate                Should the cancellation date be adjusted?
     *
     * @return boolean success
     */
    public function scheduleCancellation(
        $uxtRequestedCancellationDate,
        $bolOnContractEnd = true,
        $bolAdjustDate = true
    ) {
        $arrInvoiceItems = array();
        $arrCancellationCharges = array();
        $bolCanOnNextInv = false;
        $arrScheduledPaymentID = array();
        $floAmount = 0;

        // Find out if there is an open cancellation for this WLR product (not completed or cancelled)
        $arrExistingCancellation = $this->getScheduledCancellation();

        $arrProductComponents = $this->getProductComponents();

        // Find the subscription product component
        foreach ($arrProductComponents as $objProductComponent) {

            if ('SUBSCRIPTION' == $objProductComponent->getHandle()) {

                if ($bolAdjustDate) {

                    $uxtNextInvoice = $objProductComponent->getNextInvoiceDate();

                    if ($uxtRequestedCancellationDate <= $uxtNextInvoice) {

                        $bolCanOnNextInv = true;
                        $uxtActualCancellationDate = $uxtNextInvoice;

                    } else {

                        // Fix for problem 35292.
                        // the cancellation date returned by getCancellationTime varies when the component
                        // is activated from the next invoice date.

                        $uxtCancellationTime = $objProductComponent->getCancellationTime();
                        if ($uxtCancellationTime < $uxtRequestedCancellationDate) {

                            $uxtActualCancellationDate = $uxtRequestedCancellationDate;

                        } else {

                            $uxtActualCancellationDate = $uxtCancellationTime;
                        }
                    }

                } else {

                    $uxtActualCancellationDate = $uxtRequestedCancellationDate;
                }

                // Set default for cancellation succeeded
                $cancellationSucceeded = true;

                if (empty($arrExistingCancellation)) {

                    // Add the schedule cancellation
                    $objCancellation = CCancellation::create(
                        $objProductComponent->getComponentID(),
                        $uxtActualCancellationDate,
                        $bolOnContractEnd,
                        $bolAdjustDate
                    );

                    if (!empty($objCancellation)) {

                        $strTicketBody = "Cancellation scheduled for {$this->getComponentName()} " .
                            "Product {$this->getComponentID()}, on " . date('d-m-Y', $uxtActualCancellationDate);

                    } else {

                        $strTicketBody = "Unable to schedule cancellation of {$this->getComponentName()} " .
                            "Product {$this->getComponentID()}, on " . date('d-m-Y', $uxtActualCancellationDate) . ". " .
                            "Please cancel this product manually on the date required.";

                        $cancellationSucceeded = false;
                    }

                } else {

                    // Update the existing scheduled cancellation
                    $objCancellation = new CCancellation($arrExistingCancellation['intCancellationID']);

                    if ($objCancellation instanceof CCancellation) {

                        $objCancellation->m_uxtDue = $uxtActualCancellationDate;
                        $objCancellation->m_bolOnContractEnd = $bolOnContractEnd;
                        $objCancellation->m_bolAdjustDate = $bolAdjustDate;
                        $cancellationSucceeded = $objCancellation->updateEvent();

                        $strTicketBody = "Scheduled cancellation date updated to " .
                            date('d-m-Y', $uxtActualCancellationDate) . " for {$this->getComponentName()} " .
                            "Product {$this->getComponentID()}";

                    } else {

                        $cancellationSucceeded = false;
                    }

                    if (!$cancellationSucceeded) {

                        $strTicketBody = "Scheduled cancellation date could not be updated to " .
                            date('d-m-Y', $uxtActualCancellationDate) . " for {$this->getComponentName()} " .
                            "Product {$this->getComponentID()}. Please update the cancellation date manually.";
                    }
                }

                // Raise a ticket
                if ($cancellationSucceeded) {

                    tickets_ticket_add('Script', $this->getServiceID(), 0, 0, 'Closed', 0, $strTicketBody);

                } else {

                    $ticketPool = TicketClient_DbTeam::getTeamByHandle('CSC_BILLING');
                    tickets_ticket_add(
                        'Script',
                        $this->getServiceID(),
                        '',
                        '',
                        'Open',
                        0,
                        $strTicketBody,
                        0,
                        $ticketPool->getTeamId()
                    );
                }
                break;
            }
        }

        if (empty($arrExistingCancellation)) {

            // Take cancellation charges for all product components if there is not an cancellation in progress.

            foreach ($arrProductComponents as $objProductComponent) {

                if ($objProductComponent->getHandle() == 'SUBSCRIPTION') {

                    $arrCancellationCharge = $objProductComponent->onScheduledCancellation($bolCanOnNextInv);

                } else {

                    $arrCancellationCharge = $objProductComponent->onScheduledCancellation();
                }

                if (is_array($arrCancellationCharge)) {

                    $arrCancellationCharges[] = $arrCancellationCharge;
                }

                $arrCancellationCharge = array();
            }

            foreach ($arrCancellationCharges as $arrCharges) {

                foreach ($arrCharges as $arrCharge) {

                    $floCancellationCharge = ($arrCharge['intAmountExVatPence'] + $arrCharge['intVatPence']) / 100;

                    if ($floCancellationCharge > 0) {

                        $arrInvoiceItems[] = array(
                            'amount'      => $floCancellationCharge,
                            'description' => $arrCharge['vchDescription'],
                            'gross'       => true
                        );
                        $arrScheduledPaymentID[] = $arrCharge['intScheduledPaymentID'];
                        $floAmount += $floCancellationCharge;
                    }
                }
            }

            if (!FeatureToggleManager::isOnFiltered(
                'RBM_MIGRATION_COMPLETE',
                null,
                null,
                null,
                $this->getServiceID()
            )
            ) {
                if (count($arrInvoiceItems) > 0) {

                    $intInvoiceID = CWlrProduct::takePayment(
                        $this->getServiceID(),
                        $arrInvoiceItems,
                        $floAmount,
                        $this->getComponentName() . ' Cancellation Charges',
                        PAYMENT_HOME_PHONE_CANCELLATION
                    );

                    if (isset($intInvoiceID) && $intInvoiceID > 0) {

                        foreach ($arrScheduledPaymentID as $intScheduledPaymentID) {

                            $objScheduledPayment = new CProductComponentScheduledPayment(
                                $intScheduledPaymentID
                            );
                            $objScheduledPayment->markAsInvoiced(
                                $intInvoiceID,
                                $arrInvoiceItems[0]['intLineItemId']
                            );
                        }

                    } else {

                        foreach ($arrScheduledPaymentID as $intScheduledPaymentID) {

                            $objScheduledPayment = new CProductComponentScheduledPayment(
                                $intScheduledPaymentID
                            );
                            $objScheduledPayment->cancel(time());
                        }

                        $strTicketBody = "A payment of &pound;" . $floAmount .
                            " could not be taken for schedule cancellation of WLR component. " .
                            "Please take this payment manually. A cancellation has been scheduled for " .
                            date('d-m-Y', $uxtActualCancellationDate);

                        $ticketPool = TicketClient_DbTeam::getTeamByHandle('CSC_WLR_CANCELLATIONS');
                        tickets_ticket_add(
                            'Script',
                            $this->getServiceID(),
                            '',
                            '',
                            'Open',
                            0,
                            $strTicketBody,
                            0,
                            $ticketPool->getTeamId()
                        );
                    }
                }
            }
            //FIX ME Add code for sending out emails
        }

        $objEventLogger = $this->prvGetEventLogger();
        $objEventLogger->logWlrCancellationRequest();
    } // function scheduleCancellation($uxtRequestedCancellationDate, $bolOnContractEnd=true)

    public static function takePaymentViaCard($intServiceID, $arrInvoiceItems,
                                              $floAmount, $strInvoiceDescription)
    {
        $strTransactionResult = '';
        $strTransactionError = '';
        $intTransactionID = financial_take_payment_with_status(
            $intServiceID, $floAmount, $strTransactionResult, $strTransactionError
        );

        $arrResult = CreditCardResult($strTransactionResult, $strTransactionError);

        $bolBillingSuccess = $arrResult['bolBillingSuccess'];
        $strError = $arrResult['strError'];

        if ($intTransactionID == 0) {
            return false;
        } else {
            $bolBillingSuccess = true;
        }

        // Bug workaround - error thrown if "optional" date argument not supplied
        $date = date('Y-m-d');

        $intInvoiceID = financial_generate_invoice(
            $intServiceID, $strInvoiceDescription,
            $arrInvoiceItems, $intTransactionID, $date
        );
        if (!$bolBillingSuccess) {
            $arrServiceDetails = userdata_service_get($intServiceID);
            $arrAccountDetails = userdata_account_get_by_user($arrServiceDetails['user_id']);
            $intAccountID = $arrAccountDetails['account_id'];

            // Cancel invoice
            userdata_account_balance_sub($intAccountID, ($floTotalCostIncVat));
            financial_invoice_balance_sub($intInvoiceID, ($floTotalCostIncVat));
            financial_sales_invoice_modify($intInvoiceID, 'fully_refunded');

            // Raise credit note
            require_once DATABASE_LIBRARY_ROOT . 'CoreObjects/Financial/CFinancialHelper.inc';
            $intCreditNoteID = financial_sales_invoice_add(
                $intAccountID, -($floTotalCostIncVat),
                VAT_NOW_IN_CFINANCIALHELPER,
                "Account Credit Note", null,
                $intInvoiceID
            );
            financial_sales_invoice_item_add(
                $intCreditNoteID, "Cancelled purchase (failed payment)",
                1, null, -($floTotalCostIncVat)
            );
            $arrCreditNoteReasons = financialGetCreditNoteType();
            financialUpdateCreditTypeID(
                $intCreditNoteID,
                $arrCreditNoteReasons['other']['usiCreditNoteTypeID']
            );
            financialUpdateCreditNoteOtherDescription(
                $intCreditNoteID, "Cancelled purchase (failed payment)"
            );
            financial_sales_invoice_modify($intCreditNoteID, 'fully_paid');

            return false;
        }

        return $intInvoiceID;
    }

    public static function takePayment($intServiceID, $arrInvoiceItems,
                                       $floAmount, $strInvoiceDescription, $intPaymentType = -1)
    {
        // work out whether paying by Direct Debit or not
        $intDirectDebitInstructionId = direct_debit_has_service_got_active_instruction(
            $intServiceID
        );
        if ($intDirectDebitInstructionId == -1) {
            // Raise a ticket on the account
            global $my_id;
            if (!isset($my_id) || is_null($my_id) || $my_id == '') {
                // The Script User
                $my_id = '********************************';
            }

            return CWlrProduct::takePaymentViaCard(
                $intServiceID, $arrInvoiceItems,
                $floAmount, $strInvoiceDescription
            );
        } else {
            $arrBillingService = userdata_service_get($intServiceID);

            $arrAccount = userdata_account_get_by_user($arrBillingService['user_id']);
            $intAccountId = $arrAccount['account_id'];

            $intNotificationDays = DirectDebitGetServiceNotificationDays($intServiceID);
            $uxtDaysInTheFuture = direct_debit_calculate_working_days($intNotificationDays);

            // create the transaction. This will add an invoice too.
            $arrDirectDebitTransaction = direct_debit_transaction_add(
                $intDirectDebitInstructionId, $intAccountId,
                $floAmount, $strInvoiceDescription, $strInvoiceDescription,
                0, 0, $arrInvoiceItems, null, $uxtDaysInTheFuture
            );

            $intTransactionId = $arrDirectDebitTransaction['transaction_id'];
            $intInvoiceId = $arrDirectDebitTransaction['invoice_id'];

            //Send advance notification email
            $objWlrProduct = CWlrProduct::getWlrProductFromServiceId($intServiceID);
            direct_debit_advance_notification_email_send(
                $intServiceID, $intDirectDebitInstructionId,
                $floAmount, $uxtDaysInTheFuture, $objWlrProduct,
                $intPaymentType
            );

            return $intInvoiceId;
        }

    }

    /**
     * getNoDdAdminCharge
     *
     * Function to return amount of pennies that you have to pay if you got no DD on account with WLR
     *
     * @param  integer $intServiceComponentId
     *
     * @return integer
     */

    public static function getNoDdAdminCharge($intServiceComponentId)
    {
        if (!ctype_digit($intServiceComponentId)) {
            return 0;
        }

        $strSql = "
        SELECT
            wlrc.intNoDDAdminChargesPence
        FROM
            tblWlrLineRentConfig wlrc INNER JOIN
            tblProductComponentConfig pcc ON wlrc.intProductComponentConfigId = pcc.intProductComponentConfigId INNER JOIN
            products.tblServiceComponentProduct scp ON scp.intServiceComponentProductID = pcc.intServiceComponentProductID
        WHERE
            scp.intServiceComponentID = $intServiceComponentId
        ";

        $dbhRead = get_named_connection_with_db('dbProductComponents_reporting');

        if (false === ($res = PrimitivesQueryOrExit($strSql, $dbhRead, 'CWlrProduct::getNoDdAdminCharge', true))) {
            return 0;
        }

        if (PrimitivesNumRowsGet($res) > 0) {
            return (int)PrimitivesResultGet($res, 'intNoDDAdminChargesPence');
        }

        return 0;
    }

    /**
     * Marks a component as a retention offer component
     *
     * <AUTHOR>
     *
     * @param               integer component id
     * @param unixtimestamp $uxtEndDate
     *
     * @static
     * @access public
     * @return boolean true/false
     */
    public static function markAsRetentionOffer($intComponentId, $uxtEndDate = '')
    {
        if ($intComponentId > 0 && is_numeric($intComponentId)) {
            $dbhWrite = get_named_connection_with_db('userdata');
            $intComponentId = mysql_real_escape_string($intComponentId, $dbhWrite['handle']);

            $strQuery = "INSERT INTO tblRetentionOfferComponent SET intComponentID = '$intComponentId' ";
            if ($uxtEndDate != '') {
                $uxtEndDate = mysql_real_escape_string($uxtEndDate, $dbhWrite['handle']);
                $strQuery .= ", dtmEnd = FROM_UNIXTIME('$uxtEndDate') ";
            }

            if (false === (PrimitivesQueryOrExit($strQuery, $dbhWrite, 'CWlrProduct::markAsRetentionOffer', true))) {
                return false;
            }

            return true;
        }

        return false;

    }

    /**
     * Gets the retention offer details of a component
     *
     *
     * @access public
     * <AUTHOR>
     *
     * @param  integer component id
     *
     * @static
     * @return array arrRetentionOfferDetails
     */
    public static function getRetentionOfferDetails($intComponentId)
    {
        $arrRetentionOfferDetails = array();
        if ($intComponentId > 0 && is_numeric($intComponentId)) {
            $dbhRead = get_named_connection_with_db('userdata_reporting');

            $strQuery = "SELECT intComponentId, dtmEnd, UNIX_TIMESTAMP(dtmEnd) AS uxtEnd FROM tblRetentionOfferComponent " .
                " WHERE intComponentId='" . mysql_real_escape_string($intComponentId, $dbhRead['handle']) . "'";

            if (false === ($resResult = PrimitivesQueryOrExit($strQuery, $dbhRead, 'CWlrProduct::getRetentionOfferDetails', false))) {
                return $arrRetentionOfferDetails;
            }

            $arrRetentionOfferDetails = PrimitivesResultGet($resResult);

        }

        return $arrRetentionOfferDetails;
    }

    /**
     * Updates the retention offer details of a component
     *
     * @access public
     * <AUTHOR>
     *
     * @param  integer component id
     * @param  integer uxtEndDate
     *
     * @static
     * @return boolean true/false
     */
    public static function updateRetentionOfferEndDate($intComponentID, $uxtEndDate)
    {
        if ($intComponentID > 0 && $uxtEndDate > 0) {
            $dbhWrite = get_named_connection_with_db('userdata');

            $intComponentID = mysql_real_escape_string($intComponentID, $dbhWrite['handle']);
            $uxtEndDate = mysql_real_escape_string($uxtEndDate, $dbhWrite['handle']);

            $strQuery = "UPDATE tblRetentionOfferComponent SET dtmEnd = FROM_UNIXTIME('$uxtEndDate') WHERE intComponentId = '$intComponentID' ";

            if (false === (PrimitivesQueryOrExit($strQuery, $dbhWrite, 'CWlrProduct::updateRetentionOfferEndDate', true))) {
                return false;
            }

            return true;
        }

        return false;

    }

    /**
     * Transfer retention offer from one WLR product to another
     *
     * @param object $objSourceWlrProduct   - the soruce WLR product of which
     *                                      we will read retention details from.
     *
     * @return boolean - TRUE on success, FALSE on failure/no retention to transfer across.
     **/
    public function transferRetentionOffer($objSourceWlrProduct)
    {
        $arrRetentionDetails = self::getRetentionOfferDetails(
            $objSourceWlrProduct->getComponentID()
        );

        if (empty($arrRetentionDetails)) {
            return false;
        }

        self::markAsRetentionOffer($this->getComponentID(), $arrRetentionDetails['uxtEnd']);

        return true;
    }

    /**
     * changeToRetentionOffer
     * changes Wlr component to retention offer component
     *
     * @access public
     * @return bool
     */
    public function changeToRetentionOffer()
    {

        $arrWlrComponent = $this->listProductComponentInstanceIdByHandle();
        $intProdCompSubInsId = $arrWlrComponent['SUBSCRIPTION'];
        if ($intProdCompSubInsId > 0) {
            $objProdCompSub = CProductComponent::createInstance($intProdCompSubInsId);

            //Get the end of retention offer
            $uxtRetentionOfferEnd = CProductComponent::calculateContractEnd(
                $objProdCompSub->m_uxtNextInvoice,
                $objProdCompSub->m_intTariffID,
                $objProdCompSub->m_intServiceID,
                true
            );
            if ($uxtRetentionOfferEnd > 0) {
                //Mark it retention offer in db
                $bolSuccess = CWlrProduct::markAsRetentionOffer(
                    $this->getComponentID(), $uxtRetentionOfferEnd + 86399
                );
                if ($bolSuccess) {

                    $objPaymentScheduler = new CProductComponentPaymentScheduler(
                        $intProdCompSubInsId, ''
                    );
                    $objPaymentScheduler->cancelAllOutstandingPayments();

                    return true;

                }
            }
        } else {
            return false;
        }
    }

    /**
     * Function to determine if service definition can have WLR
     *
     * @param $intServiceDefinitionId
     *
     * @return boolean
     */
    public static function canServiceDefinitionHaveWlr($intServiceDefinitionId)
    {
        if (!filter_var($intServiceDefinitionId, FILTER_VALIDATE_INT) || 0 >= $intServiceDefinitionId)
            return false;

        $strSql = 'SELECT DISTINCT 1 '
            . '  FROM products.service_component_config scc '
            . '  JOIN products.tblServiceComponentProduct scp '
            . '    ON scc.service_component_id = scp.intServiceComponentId '
            . '  JOIN products.tblServiceComponentProductType scpt '
            . '    ON scp.intServiceComponentProductTypeId = scpt.intServiceComponentProductTypeId '
            . ' WHERE scpt.vchHandle = \'WLR\' AND scc.service_definition_id =' . $intServiceDefinitionId;

        $dbhRead = get_named_connection_with_db('product_reporting');
        if (false === ($res = PrimitivesQueryOrExit($strSql, $dbhRead, __METHOD__, false)))
            return false;

        if (PrimitivesNumRowsGet($res) > 0)
            return true;

        return false;
    }

    /**
     * Function determins if there's an outstanding Provide order on WLR line
     *
     * @return bool
     */
    public function hasPendingProvideOrder()
    {
        require_once '/local/data/mis/database/application_apis/wlr/spg/Wlr_ProvideOrderDetails.class.php';
        $objOrder = Wlr_ProvideOrderDetails::getPendingProvideOrder($this->getLineRentId());

        return (null !== $objOrder);
    }

    /**
     * Function determins if there's an active all calls barred product component instance
     *
     * @return bool
     */
    public function hasActiveAllCallsBarred()
    {

        $objProductComponent = CProductComponent::createInstanceFromComponentID(
            $this->getComponentID(), 'WlrAllCallsBarred', array('ACTIVE')
        );

        if (false === $objProductComponent) {
            return false;
        }

        return true;

    }

    /**
     * Function lists all active wlr product components by handle.
     *
     * @param array - An array of feature states to return, eg array('ACTIVE', 'QUEUED-ACTIVATE')
     *
     * @return array - an array of product component instance handles, statuses and ids
     *
     */
    public function getProductComponentHandlesByStatus($arrFeatureStatuses)
    {

        $dbhConn = get_named_connection_with_db('userdata');

        $strFeatureStatuses = implode("', '", $arrFeatureStatuses);

        $strQuery = "SELECT
                  pc.vchHandle AS strProductComponentHandle,
                  st.vchHandle AS strProductComponentStatus,
                  pc.vchDisplayName,
                      pci.intProductComponentInstanceID as intProductComponentInstanceID
                   FROM
                      userdata.tblProductComponentInstance AS pci
                   INNER JOIN dbProductComponents.tblProductComponent pc
                      ON pc.intProductComponentID = pci.intProductComponentID
                   INNER JOIN dbProductComponents.tblStatus AS st
                      ON st.intStatusID = pci.intStatusID
                   INNER JOIN userdata.components AS c
                      ON c.component_id = pci.intComponentID
                   WHERE
                      pci.intComponentID = '{$this->m_intComponentID}' AND
                      st.vchHandle IN ('{$strFeatureStatuses}') ";

        $refResult = PrimitivesQueryOrExit(
            $strQuery, $dbhConn,
            "CwlrProduct::getActiveOptionalWlrProductComponents"
        );
        $arrProductComponentInstances = PrimitivesResultsAsArrayGet($refResult);

        $arrProductComponentInstancesByHandle = array();

        foreach ($arrProductComponentInstances as $arrRecord) {

            $arrProductComponentInstancesByHandle[$arrRecord['strProductComponentHandle']]
                = array(
                'strProductComponentStatus'     => $arrRecord['strProductComponentStatus'],
                'intProductComponentInstanceID' => $arrRecord['intProductComponentInstanceID'],
                'strDisplayName'                => $arrRecord['vchDisplayName']
            );
        }

        return $arrProductComponentInstancesByHandle;
    }

    /**
     * returns call plan including tariff price
     *
     * @param  int $intServiceComponentId
     *
     * @return array
     * @static
     */
    public static function getCallPlanDetailsFromServiceComponentId($intServiceComponentId)
    {
        if (!filter_var($intServiceComponentId, FILTER_VALIDATE_INT) || 0 >= $intServiceComponentId) {
            return array();
        }

        $dbhConn = get_named_connection_with_db('userdata_reporting');

        $strSql = 'SELECT cp.intCallPlanId, ' .
            'cp.vchDisplayName as strDisplayName, ' .
            'cp.intChargingIntervalSeconds, ' .
            't.intCostIncVatPence, ' .
            't.intTariffID, ' .
            'scp.intServiceComponentID, ' .
            'lrc.bolDisplaySplitCharge ' .
            'FROM products.tblServiceComponentProduct scp ' .
            'INNER JOIN dbProductComponents.tblProductComponentConfig pcc ' .
            '        ON scp.intServiceComponentProductId = pcc.intServiceComponentProductId ' .
            'INNER JOIN dbProductComponents.tblCallPlan cp ' .
            '        ON pcc.intProductComponentConfigId = cp.intProductComponentConfigId ' .
            'INNER JOIN dbProductComponents.tblTariff t ' .
            '        ON pcc.intProductComponentConfigID = t.intProductComponentConfigID ' .
            'INNER JOIN dbProductComponents.tblWlrLineRentConfig lrc ' .
            '        ON pcc.intProductComponentConfigId = lrc.intProductComponentConfigId ' .
            'WHERE cp.dtmEnd IS NULL AND scp.intServiceComponentID =' . $intServiceComponentId .
            ' AND t.dtmEnd IS NULL ';

        $objRes = PrimitivesQueryOrExit(
            $strSql, $dbhConn,
            'CWlrProduct::getCallPlanDetailsFromServiceComponentId',
            false
        );

        if (!$objRes) {
            return array();
        }

        return PrimitivesResultGet($objRes);
    }

    /**
     * Split up a CLI into segments, to aid readability.
     *
     * @static
     * @access public
     *
     * @param  string $strCli Phone number to split up
     *
     * @return string
     **/
    public static function splitNumberForPresentationPurposes($strCli)
    {
        // Possible todo: amend method to split in a variety of ways based
        // on area codes and how people traditionally write their numbers
        // based on them, e.g.
        // 0207 123 4567
        // 0208 123 4567
        // 0114 123 4567
        // 01909 123456
        return substr($strCli, 0, 5) . ' ' . substr($strCli, 5);
    }

    /**
     * Helper method to determine if the product has 'unlimited' inclusive
     * minutes. Basic idea is to hide away from calling code the mechanisms
     * used for implementing 'unlimited' inclusive minutes.
     *
     * @access public
     * @return boolean
     **/
    public function hasUnlimitedIncludedMinutes()
    {
        return ($this->getIncludedMinutes() == CWlrCalltime::UNLIMITED_INCLUSIVE_MINUTES);
    }

    /**
     * Function to return provisioned platform
     *
     * @param  int $intProductComponentInstanceID
     *
     * @return string $strSupplierPlatform
     */
    public static function getSupplierPlatform($intProductComponentInstanceID)
    {
        $objCwlrLineRent = new CWlrLineRent($intProductComponentInstanceID);
        $arrUserConfig = $objCwlrLineRent->getUserConfig();
        $strSupplierPlatform = $arrUserConfig['vchSupplierPlatform'];

        return $strSupplierPlatform;
    }

    /**
     * Raise a ticket to BOT Phone Prov incase of MAAF and Greenbee
     *
     * The ticket should detail that a WLR order
     * has been identified and list the required call features
     * that have been added from the sign up journey
     *
     * @param int                        $intServiceId         Service id
     * @param Wlr3_Engineer_Appointments $engineerAppointments Engineer appointments
     *
     * @return boolean
     */
    private static function raiseTicketForWlrProvisioning($intServiceId, $engineerAppointments)
    {
        global $my_id;

        $intTeamId = PHPLibTeamIdGetByName('BOT - New PSTN Provides');

        $strTicketBody = "The customer has signed up for the Home Phone service. " .
            "Please check dialogue services to see what products are on the line and place the appropriate order";

        if ($engineerAppointments instanceof Wlr3_Engineer_Appointments) {

            $appointmentText = "\n\nThe customer made the following engineering appointments: \n";

            $appointments = $engineerAppointments->getEngineerAppointments();
            if (is_array($appointments)) {
                foreach ($appointments as $appointment) {
                    $appointmentText .= $appointment->getDate() . " " . $appointment->getSlot() . "\n";
                }
            }
            if (trim($engineerAppointments->getEngineeringNotes()) != '') {
                $appointmentText .= "\nEngineer notes:\n";
                $appointmentText .= $engineerAppointments->getEngineeringNotes();
            }
            $strTicketBody .= $appointmentText;
        }

        return tickets_ticket_add(
            'Script', $intServiceId, 0, 0, 'Open',
            $my_id, $strTicketBody, 0, $intTeamId
        );
    }

    /**
     * Get a list of wlr products by visp
     *
     * @param string $isp Isp of the products we want
     *
     * @return array
     */
    public static function getProductsByVisp($isp = '')
    {
        if ('' == $isp) {
            return array();
        }

        $conn = get_named_connection_with_db('product_reporting');
        $isp = PrimitivesRealEscapeString($isp, $conn);

        $query = '
SELECT DISTINCT(TRIM(LEADING "0" FROM sc.service_component_id))
FROM products.service_components AS sc
INNER JOIN products.tblServiceComponentGenericType AS scgt
    ON sc.service_component_id = scgt.intServiceComponentId
INNER JOIN tblServiceComponentGenericTypeGroup AS sctpg
    ON scgt.intServiceComponentGenericTypeGroupId = sctpg.intServiceComponentGenericTypeGroupId
INNER JOIN products.service_component_config AS scc
    ON scc.service_component_id = sc.service_component_id
INNER JOIN products.service_definitions AS sd
    ON sd.service_definition_id = scc.service_definition_id
WHERE sctpg.vchHandle = "HOME_PHONE"
AND sd.isp = "' . $isp . '"';

        $res = PrimitivesQueryOrExit($query, $conn, __METHOD__, false);

        return PrimitivesResultsAsListGet($res);
    }

    /**
     * Sends Wlr Confirmation email to the customer
     *
     * @return void
     */
    public function sendCompletionEmail()
    {
        $arrServiceData = userdata_service_get($this->getServiceID());
        $arrProductData = product_get_service($arrServiceData['type']);

        if ($arrProductData['isp'] === 'johnlewis') {

            $strEmailTemplate = "phone2phone";
            $arrEmailData = array();
            $arrEmailData['strProduct'] = $this->getFullName();

            $arrCallPlan
                = $this->getCallPlanDetailsFromServiceComponentId($this->m_intServiceComponentID);

            $floCallPlanCost = $arrCallPlan['intCostIncVatPence'] / 100;
            $arrEmailData['strPrice'] = number_format($floCallPlanCost, 2);

            // obtain the subscription component for activating component
            $arrStates = array('UNCONFIGURED', 'QUEUED_ACTIVATE', 'ACTIVE', 'QUEUED_DEACTIVATE');

            $objComponentSubscription
                = CProductComponent::createInstanceFromComponentID(
                $this->getComponentId(), "SUBSCRIPTION", $arrStates
            );

            $floLineRental = 0;
            if (!empty($objComponentSubscription)) {
                $floLineRental = $objComponentSubscription->getCurrentCost() / 100;
            }
            $arrEmailData['strLineRental'] = number_format($floLineRental, 2);

            EmailHandler::sendEmail($this->getServiceID(), $strEmailTemplate, $arrEmailData);
        }
    }

    /**
     * Method to return the line rent sub component of this wlr product
     *
     * @return CWlrLineRent
     */
    public function getLineRent()
    {
        $lineRent = null;
        $lineRentId = $this->getLineRentId();

        if (!empty($lineRentId)) {

            $result = CProductComponent::createInstance($lineRentId);

            if (!empty($result) && ($result instanceof CWlrLineRent)) {

                $lineRent = $result;
            }
        }

        return $lineRent;
    }

    /**
     * Activate pending profit forgone contracts once the wlr product has activated
     *
     * @return boolean
     */
    private function _activateContract()
    {
        $serviceId = $this->getServiceID();
        $componentId = $this->getComponentID();

        if (empty($serviceId) || empty($componentId)) {
            // failed to activate contract
            Dbg_Dbg::write(
                __FILE__ . ' @ LINE :: ' . __LINE__ .
                ' - failed to activate contract for $serviceId: ' . $serviceId . ', $componentId: ' . $componentId,
                'LegacyCodebase.CWlrProduct.activateContract'
            );

            return false;
        }

        try {
            $contractsClient = BusTier_BusTier::getClient('contracts');
            $contractsClient->setServiceId($serviceId);
            $contractsClient->setRaiseServiceNotice(true);

            // set criteria for the contact search
            $contractsCriteria = array(
                'serviceId' => $serviceId
            );

            // get all contracts based on criteria
            $contracts = $contractsClient->getContracts($contractsCriteria);

            // check if contract for the account exists
            if (!empty($contracts) && is_array($contracts)) {

                foreach ($contracts as $contract) {

                    if ($contract instanceof \Plusnet\ContractsClient\Entity\Contract) {

                        // get getContractedServices for the contract
                        $contractedServices = $contract->getContractedServices();

                        // check if the contract has contractedServices
                        if (!empty($contractedServices) && is_array($contractedServices)) {

                            foreach ($contractedServices as $contractedService) {

                                if ($contractedService->getComponentId() == $componentId
                                    && $contractedService->getStatus() == InstanceInterface::STATUS_PENDING
                                ) {
                                    // activate all contracted services
                                    $contractsClient->activateContractedService(
                                        $contract, $contractedService, 'Wlr product enabled'
                                    );
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception $exception) {
            // failed to activate contract
            Dbg_Dbg::write(
                __FILE__ . ' @ LINE :: ' . __LINE__ .
                ' - failed to activate contract for $serviceId: ' . $serviceId . ', $componentId: ' . $componentId,
                'LegacyCodebase.CWlrProduct.activateContract'
            );

            return false;
        }

        return true;
    }

    /**
     * Returns the chargable/refundable statuses from operational perspective.
     *
     * @return array
     */
    public static function getActiveStatuses()
    {
        return array(
            'queued-deactivate',
            'queued-reactivate',
            'active',
            'deactive'
        );
    }
}

// Unless parts of the codebase are updated, the following WLR_*_COMPONENT_ID define
// aliases remain here for backward compatibility *only*.

// I propose that for new WLR products created in the future, (this includes the component
// for BPR '09) that they be defined in component-defines.inc instead, as opposed to having
// the same service component ID located in various other random places. (including here!)

configDefineIfMissing('WLR_ANYTIME_COMPONENT_ID', COMPONENT_WLR_ANYTIME);
configDefineIfMissing('WLR_ANYTIME_PLUS_COMPONENT_ID', COMPONENT_WLR_ANYTIME_PLUS);
configDefineIfMissing('WLR_EVENING_WEEKEND_COMPONENT_ID', COMPONENT_WLR_EVENING_WEEKEND);

configDefineIfMissing('WLR_TALK_COMPONENT_ID', COMPONENT_WLR_TALK);
configDefineIfMissing('WLR_TALK_ANYTIME_COMPONENT_ID', COMPONENT_WLR_TALK_ANYTIME);


configDefineIfMissing('WLR_GREENBEE_PHONE_COMPONENT_ID', COMPONENT_WLR_GREENBEE_PHONE);
configDefineIfMissing('WLR_GREENBEE_PHONE_BUNDLE_COMPONENT_ID', COMPONENT_WLR_GREENBEE_PHONE_BUNDLE);

configDefineIfMissing('WLR_ESSENTIAL_COMPONENT_ID', COMPONENT_WLR_ESSENTIAL);

configDefineIfMissing('WLR_PN_TALK_ANYTIME_COMPONENT_ID', COMPONENT_WLR_PN_TALK_ANYTIME);
configDefineIfMissing('WLR_PN_TALK_EVENING_WEEKEND_COMPONENT_ID', COMPONENT_WLR_PN_TALK_EVENING_WEEKEND);

// various misc parameters

configDefineIfMissing('WLR_MINIMUM_CHARGEABLE_AMOUNT', 300);
configDefineIfMissing('PAGE_LENGTH_LIMIT', 10);
configDefineIfMissing('PAYMENT_HOME_PHONE_CANCELLATION', 1);
