<?php
/**
 * HardwareBundle.class.php
 *
 * @package    application_apis
 * @subpackage HardwareBundle
 * <AUTHOR> <<EMAIL>>
 * @link       https://workplace.plus.net
 */
/**
 * Hardware Bundle class
 *
 * @package    application_apis
 * @subpackage HardwareBundle
 * <AUTHOR> <<EMAIL>>
 * @link       https://workplace.plus.net
 */
/**
 * Hardware Bundle class
 *
 * Modified to handle delayed RMA situation.
 * Check if the device is configured to behave delayed while doing RMA
 * Add the serial numbers to a text file while provisioning if
 * configured to have delayed RMA
 *
 * @category  Tr069
 * @package   Tr069
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2015 Plusnet
 * @link      Projects/Tr069/PDD
 */
class HardwareBundle extends HardwareBundleBase
{
    // Core member variables
    var $m_arrItems                          = array();
    var $m_intComponentID                    = 0;
    var $m_intServiceID                      = 0;
    var $m_arrStatuses                       = array();
    var $m_BundleType                        = null;
    var $m_Supplier                          = null;
    var $m_binIsCustomised                   = false;
    var $m_binConfigBundleID                 = 0;
    var $m_intDeliveryAddressID              = 0;
    var $m_datWhenOrdered                    = null;
    var $m_bolOverallOrderStatusChanged      = false;
    var $m_intOverallOrderStatusID           = 0;
    var $m_strOverallStatusTag               = '';
    var $m_strOverallStatusDisplayName       = '';
    var $m_strOverallStatusDescription       = '';
    var $m_strOverallStatusPortalDescription = '';
    var $m_arrOverallStatusHistory           = array();
    // Starts off as false to indicate that the information hasn't been fetched yet
    var $m_arrRmaProcessData                 = false;
    // Index from the above array containing the current RMA Process, false if there isn't one
    var $m_intCurrentRmaProcessIndex         = false;
    var $m_strTrackingOrderNumber            = '';
    var $m_intSalesInvoiceID                 = 0;
    var $m_uxtDatePurchased                  = 0;
    var $m_bolTr069Compatible                = false;
    var $m_strDefaultModelName               = ''; // Default model name

    // Associated records. These are retrieved when they're first asked for
    // and cached here.
    var $m_rDeliveryAddress = array();
    var $m_rComponent       = array();
    var $m_rService         = array();
    var $m_rUser            = array();

    /**
     * This config file contains the device types which should have delayed RMA behaviour.
     *
     * @var string
     */
    const CONFIG_FILE = '/local/data/mis/database/DelayedRMADeviceTypes.ini';

    /**
     * Constructor
     *
     * @return void
     */
    function HardwareBundle()
    {
        // Call base constructor
        $this->HardwareBundleBase();
    }


    //
    // Set Functions
    //

    /**
     * Creates a new bundle and attaches it to the component
     *
     * @param int $intComponentID ComponentID
     *
     * @return boolean
     */
    function CreateForComponent($intComponentID)
    {
        // Get the component being passed
        $rComponent = userdata_component_get($intComponentID);

        // Get the hardware bundle type
        $strClassName = $this->GetClassPrefix() . 'HardwareBundleType';

        $BundleType = new $strClassName();

        $BundleType->GetByComponentType($rComponent['component_type_id']);

        // Have we got a type object?
        if ($BundleType === false) {
            $this->SetInvalid();

            return false;
        }

        // Set the config data
        $this->m_BundleType      = $BundleType;
        $this->m_binIsCustomised = false;
        $this->m_intComponentID  = $intComponentID;

        //If there are currently no items in this bundle, add the default items
        if (empty($this->m_arrItems)) {
            // Populate the default items into the items
            $arrItems = $BundleType->GetDefaultItems();

            foreach ($arrItems as $rItem) {
                $rItem = array('intStatus'            => $rItem['intStartingStatusID'],
                               'intQuantity'          => $rItem['intDefaultQuantity'],
                               'intSupplierProductID' => $rItem['intSupplierProductID'],
                               'intOrderNumber'       => '',
                               'intInvoiceNumber'     => '',
                               'strWhenDispatched'    => null
                               );

                $this->m_arrItems[] = $rItem;
            }
        }

        // Set the overall status of the object
        $this->SetOverallStatusTag('not_ordered', 'false');

        // Mark as not in sync
        $this->m_binInSync = false;

    } // function CreateForComponent($intComponentID)

    /**
     * Add additional items to bundle (or increase quantity)
     * Need tag and quantity of additional item to add
     *
     * @param string $strTag      Tag
     * @param int    $intQuantity Quantity
     *
     * <AUTHOR> Kurylowicz
     *
     * @return bool
     */
    function CreateAdditionalItemForComponent($strTag, $intQuantity)
    {
        // Can't add aditional items to processed bundle
        if ('not_ordered' <> $this->GetStatusTag()) {

            $this->SetInvalid();

            return false;
        }

        // Get the component being passed
        $rComponent = $this->GetComponent();

        // Get the hardware bundle type
        $strClassName = $this->GetClassPrefix() . 'HardwareBundleType';

        $BundleType = new $strClassName();

        $BundleType->GetByComponentType($rComponent['component_type_id']);

        // Have we got a type object?
        if ($BundleType === false) {
            $this->SetInvalid();

            return false;
        }

        // Find bundle item details by tag, we can only add items that are defined in bundle
        $arrItems = $BundleType->GetDefaultItems();
        $arrItemToAdd = array();

        foreach ($arrItems as $arrItemDetails) {
            if ($strTag == $arrItemDetails['strTag']) {

                $arrItemToAdd = $arrItemDetails;
                break;
            }
        }
        if (0 <> count($arrItemToAdd)) {

            $bolAlreadyExist = false;

            foreach ($this->m_arrItems as $intItemIndex => $arrItemDetails) {
                if ($arrItemDetails['intSupplierProductID'] == $arrItemToAdd['intSupplierProductID']) {

                    $bolAlreadyExist = true;

                    // Increase quantity and recalculate const to customer
                    $this->m_arrItems[$intItemIndex]['intQuantity'] += $intQuantity;
                    $this->m_arrItems[$intItemIndex]['intCostToCustomer']
                        += round(
                            $arrItemToAdd['intCostToCustomer']/$arrItemToAdd['intDefaultQuantity']
                        ) *  $intQuantity;
                }
            }

            if (false == $bolAlreadyExist) {

                $rItem = array(
                    'intStatus'            => $arrItemToAdd['intStartingStatusID'],
                            'intQuantity'          => $intQuantity,
                            'intSupplierProductID' => $arrItemToAdd['intSupplierProductID'],
                    'intOrderNumber'       => '',
                    'intInvoiceNumber'     => '',
                    'strWhenDispatched'    => null
                    );

                    $this->m_arrItems[] = $rItem;
            }

            // Mark as not in sync
            $this->m_binInSync = false;
        }

        return true;
    }

    /**
     * Set the status to the passed status tag.
     * Pass -1 for first parameter to apply to all items. Note that this
     * also updates the Overall bundle status too.
     *
     * @param int    $intItemIndex  Item index
     * @param string $strStatusTag  StatusTag
     * @param array  $arrRmaItemIds RmaItemIds
     *
     * @return boolean
     */
    function SetStatusTag($intItemIndex, $strStatusTag, $arrRmaItemIds=false)
    {
        if ($arrRmaItemIds) {
               //if this is an RMA, only order the items that have been requested
            $arrRmaBundle = array();
            foreach ($this->m_arrItems as $item) {
                $intProductID = $item['intID'];
                if (in_array($intProductID, $arrRmaItemIds)) {
                    array_push($arrRmaBundle, $item);
                }
            }
            $this->m_arrItems = null;
            $this->m_arrItems = $arrRmaBundle;
        }

        $recStatus = HardwareBundleControl::GetStatus($strStatusTag);

        // Make damn sure we're not going to set the status to 0
        if ($recStatus == false) {
            return false;
        }

        $intStatusID                = $recStatus['intStatus'];
        $strStatusDisplayName       = $recStatus['strDisplayName'];
        $strStatusDescription       = $recStatus['strDescription'];
        $strStatusPortalDescription = $recStatus['strPortalDescription'];

        if ($intItemIndex == -1) {
            // Set the status for each item
            $intItemCount = count($this->m_arrItems);
            for ($intCurItem = 0; $intCurItem < $intItemCount; $intCurItem++) {
                $this->m_arrItems[$intCurItem]['intStatus']             = $intStatusID;
                $this->m_arrItems[$intCurItem]['strStatusTag']          = $strStatusTag;
                $this->m_arrItems[$intCurItem]['strStatusDisplayName']  = $strStatusDisplayName;
                $this->m_arrItems[$intCurItem]['strStatusDescription']  = $strStatusDescription;
                $this->m_arrItems[$intCurItem]['strStatusPortalDescription']  = $strStatusPortalDescription;
            }

            // Set the overall status
            $this->m_intOverallOrderStatusID           = $intStatusID;
            $this->m_strOverallStatusTag               = $strStatusTag;
            $this->m_strOverallStatusDisplayName       = $strStatusDisplayName;
            $this->m_strOverallStatusDescription       = $strStatusDescription;
            $this->m_strOverallStatusPortalDescription = $strStatusPortalDescription;

            // Record that the overall status has changed.
            $this->m_bolOverallOrderStatusChanged = true;
        } else {
            $this->m_arrItems[$intItemIndex]['intStatus']                  = $intStatusID;
            $this->m_arrItems[$intItemIndex]['strStatusTag']               = $strStatusTag;
            $this->m_arrItems[$intItemIndex]['strStatusDisplayName']       = $strStatusDisplayName;
            $this->m_arrItems[$intItemIndex]['strStatusPortalDescription'] = $strStatusPortalDescription;
        }

        $this->MarkModified();

    } // function SetStatusTag($intItemIndex, $strStatusTag)

    /**
     * Set the overall status of an order.
     * If $bolSetAllItems is true, the status of all items will also be set
     *
     * @param string  $strStatusTag   StatusTag
     * @param boolean $bolSetAllItems SetAllItems
     *
     * @return void
     */
    function SetOverallStatusTag($strStatusTag, $bolSetAllItems)
    {
        if ($bolSetAllItems == true) {
            $this->SetStatusTag(-1, $strStatusTag);
        } else {
            $recStatus = HardwareBundleControl::GetStatus($strStatusTag);

            $this->m_intOverallOrderStatusID           = $recStatus['intStatus'];
            $this->m_strOverallStatusTag               = $strStatusTag;
            $this->m_strOverallStatusDisplayName       = $recStatus['strDisplayName'];
            $this->m_strOverallStatusDescription       = $recStatus['strDescription'];
            $this->m_strOverallStatusPortalDescription = $recStatus['strPortalDescription'];

            // Record that the overall status has changed.
            $this->m_bolOverallOrderStatusChanged = true;
        }
    }

    /**
     * Set the order number of a bundle item
     * Pass -1 for first parameter to apply to all items
     *
     * @param int    $intItemIndex   ItemIndex
     * @param string $strOrderNumber OrderNumber
     *
     * @return void
     */
    function SetOrderNumber($intItemIndex, $strOrderNumber)
    {
        if ($intItemIndex == -1) {
            $intItemCount = count($this->m_arrItems);

            for ($intCurItem = 0; $intCurItem < $intItemCount; $intCurItem++) {
                $this->m_arrItems[$intCurItem]['strOrderNumber'] = $strOrderNumber;
            }
        } else {
            $this->m_arrItems[$intItemIndex]['strOrderNumber'] = $strOrderNumber;
        }

        $this->MarkModified();
    }

    /**
     * Set the Invoice number of a bundle item
     * Pass -1 for first parameter to apply to all items
     *
     * @param int    $intItemIndex     Item Index
     * @param string $strInvoiceNumber InvoiceNumber
     *
     * @return void
     */
    function SetInvoiceNumber($intItemIndex, $strInvoiceNumber)
    {
        if ($intItemIndex == -1) {
            $intItemCount = count($this->m_arrItems);

            for ($intCurItem = 0; $intCurItem < $intItemCount; $intCurItem++) {
                $this->m_arrItems[$intCurItem]['strInvoiceNumber'] = $strInvoiceNumber;
            }
        } else {
            $this->m_arrItems[$intItemIndex]['strInvoiceNumber'] = $strInvoiceNumber;
        }

        $this->MarkModified();
    }

    /**
     * Method added to get the model name from bundle id
     *
     * @param
     *
     * @return string
     */
    public function getDeviceType($bundleId){
        $handlerName = '';
        $db = Db_Manager::getAdaptor('Tr069', Db_Manager::DEFAULT_TRANSACTION);
        $deviceType = $db->getDeviceForConfigHardwareBundleId($bundleId);

        if (empty($deviceType)) {
            return $handlerName;
        }

        foreach($deviceType as $device) {
            if($device['bolEnabled']) {
                $handlerName = $db->getTr069AcsSupplierHandle($device['strModelName']);
                break;
            }
        }
        return $handlerName;
    }

    /**
     * Get the device types for which delayed RMA is applicable.
     *
     * @return array
     */
    protected function getDeviceTypesWithDelayedRMA()
    {
        if (!file_exists(self::CONFIG_FILE) || !is_readable(self::CONFIG_FILE)){
            return array();
        }

        if(!Plusnet\Feature\FeatureToggleManager::isOn('Hub0Retire')) {
            return array('tr069Motive');
        }

        return array_map('trim', file(self::CONFIG_FILE));
    }

    /**
     * Set the when ordered flag, pass nothing for now.
     *
     * @param string $strDate Date
     *
     * @return void
     */
    function SetWhenOrdered($strDate='')
    {
        if ($strDate == '') {
            $strDate = date('Y-m-d H:i:s');
        }

        if ($this->m_datWhenOrdered != $strDate) {
            $this->m_datWhenOrdered = $strDate;

            $this->MarkModified();

            //If the bundle is awaiting RMA - delete all current TR069 devices ready for the new device
            if ('awaiting_rma' == $this->m_strOverallStatusTag) {
                // Process the RMA only if the device type is not configured to be delayed RMA.
                if(!in_array($this->getdeviceType($this->m_intConfigHardwareBundleID), $this->getDeviceTypesWithDelayedRMA())){
                    $this->deleteTr069Devices();
                }
            }
        }
    }

    /**
     * SetWhenDispatched
     *
     * @param int $intItemIndex Item index
     * @param int $strDate      Date
     *
     * @return void
     */
    function SetWhenDispatched($intItemIndex=-1, $strDate='')
    {
        // Default to the current date if not explicitly passed
        if ($strDate == '') {
            $strDate = date(MYSQL_DATETIME);
        }

        $binModified = false;

        if ($intItemIndex == -1) {
            $intItemCount = $this->CountItems();

            for ($intCurItem = 0; $intCurItem < $intItemCount; $intCurItem++) {
                $this->m_arrItems[$intCurItem]['strWhenDispatched'] = $strDate;

                $binModified = true;
            }
        } else {
            $this->m_arrItems[$intItemIndex]['strWhenDispatched'] = $strDate;

            $binModified = true;
        }

        if ($binModified == true) {
            $this->MarkModified();
        }
    }

    /**
     * SetWhenLastUpdated
     *
     * @param int    $intItemIndex Item index
     * @param string $strDate      Date
     *
     * @return void
     */
    function SetWhenLastUpdated($intItemIndex=-1, $strDate='')
    {
        // Default to the current date if not explicitly passed
        if ($strDate == '') {
            $strDate = date(MYSQL_DATETIME);
        }

        $binModified = false;

        if ($intItemIndex == -1) {
            $intItemCount = $this->CountItems();

            for ($intCurItem = 0; $intCurItem < $intItemCount; $intCurItem++) {
                $this->m_arrItems[$intCurItem]['strWhenLastUpdated'] = $strDate;

                $binModified = true;
            }
        } else {
            $this->m_arrItems[$intItemIndex]['strWhenLastUpdated'] = $strDate;

            $binModified = true;
        }

        if ($binModified == true) {
        	$this->MarkModified();
        }
    }

    /**
     * Set a new delivery address ID for the bundle
     *
     * @param int $intDeliveryAddressID DeliveryAddressID
     *
     * @return boolean
     */
    function SetDeliveryAddressID($intDeliveryAddressID)
    {
        if ($intDeliveryAddressID == $this->m_intDeliveryAddressID) {
            // The object is already in sync.
            return true;
        } else if ($intDeliveryAddressID == 0) {
            // We really shouldn't be doing this...
            return false;
        } else {
            // Set the new address ID and mark the object as modified.
            $this->m_intDeliveryAddressID = $intDeliveryAddressID;

            $this->MarkModified();

            return true;
        }
    }

    /**
     * Set Hardware Order Tracking Order Number
     *
     * @param string $strTrackingOrderNumber TrackingOrderNumber
     *
     * @return boolean
     */
    function SetTrackingOrderNumber($strTrackingOrderNumber)
    {
        if ($strTrackingOrderNumber == $this->m_strTrackingOrderNumber) {
            // The object is already in sync - no need to update
            return false;
        } else if (strlen($strTrackingOrderNumber) > 32) {
            //The string is too long, this can't be a valid order number
            return false;
        } else {
            //set the new Tracking order number and mark the object as modified.
            $this->m_strTrackingOrderNumber = $strTrackingOrderNumber;

            $this->MarkModified();

            return true;
        }
    }//end of function: SetTrackingOrderNumber

    /**
     * Add a new item to the bundle.
     *
     * @param int    $intSupplierProductID SupplierProductID
     * @param int    $intQuantity          Quantity
     * @param string $strStatusTag         StatusTag
     *
     * @return void
     */
    function AddItem($intSupplierProductID, $intQuantity, $strStatusTag)
    {

        $recStatus = HardwareBundleControl::GetStatus($strStatusTag);

        $this->m_arrItems[] = array('intID'                => 0,
                                    'intStatus'            => $recStatus['intStatus'],
                                    'intQuantity'          => $intQuantity,
                                    'intSupplierProductID' => $intSupplierProductID,
                                    'intOrderNumber'       => '',
                                    'intInvoiceNumber'     => '',
                                    'strWhenDispatched'    => null);

        $this->MarkModified();
    }

    /**
     * Commit changes to the database
     *
     * @param array $arrItems Items
     *
     * @return boolean
     */
    function Commit($arrItems=null)
    {
        // if arrItems is not null, it's an RMA - but if m_arrItems is empty we've already
        // removed the original items so this part can be skipped.
        if ($arrItems != null && count($arrItems) > 0 && !empty($this->m_arrItems)) {
            unset($this->m_arrItems);
            $this->m_arrItems = $arrItems;
            // reset item ids to 0 to ensure that they are inserted rather than updated as
            // they have already been deleted
            for ($i=0;$i<count($this->m_arrItems);$i++) {
                $this->m_arrItems[$i]['intID'] = 0;
            }
        }
        // First see if the object needs committing
        if ($this->IsModified() == false) {
            // This isn't an error: the object is already in sync!
            return true;
        }

        // First, is this object tied to a configuration already in the database?
        if ($this->m_intID == 0) {
            // No, we need to add a new configuration

            // First do we need to default with the delivery
            // addresses to the customer's primary address?
            $hConnection = get_named_connection('userdata');
            if ($this->m_intDeliveryAddressID == 0) {
                error_log(
                    basename(__FILE__) . "@" . __LINE__ ." - The m_intDeliveryAddressID " .
                    "is set as zero for component/service: {$this->m_intComponentID}/{$this->m_intServiceID}"
                );
                // Yes, get the customer's primary address ID as default
                $this->GetServiceID();
                $service = userdata_service_get($this->m_intServiceID);
                error_log(
                    basename(__FILE__) . "@" . __LINE__ ." - The service id of the component " .
                    "{$this->m_intComponentID} is: {$this->m_intServiceID}"
                );
                //P 57417 : When you place hardware order through WP,
                //the delivery address will be partner's address if you fetch the address using customer_id
                if (isset($service['isp']) && $service['isp'] == 'partner') {
                    $strQuery = ' SELECT u.address_id '
                              . ' FROM services s '
                              . ' INNER JOIN users u ON s.user_id = u.user_id '
                              . ' WHERE s.service_id = '.mysql_real_escape_string($this->m_intServiceID);
                    error_log(
                        basename(__FILE__) . "@" . __LINE__ ." - Service isp of the user is: partner. " .
                        "So return end user address as delivery address for component {$this->m_intComponentID}"
                    );
                } else {
                    $strQuery = 'SELECT cu.primary_address_id '
                              . 'FROM components AS co '
                              . 'INNER JOIN services s ON co.service_id = s.service_id '
                              . 'INNER JOIN users u ON s.user_id = u.user_id '
                              . 'INNER JOIN customers cu ON cu.customer_id = u.customer_id '
                              . "WHERE co.component_id='{$this->m_intComponentID}'";
                    error_log(
                        basename(__FILE__) . "@" . __LINE__ ." - Service isp is not partner. So return partner " .
                        "primary address as deliver address for the component {$this->m_intComponentID}"
                    );
                }

                $hResult = mysql_query($strQuery, $hConnection)
                    or report_error(__FILE__, __LINE__, mysql_error($hConnection));

                if (mysql_num_rows($hResult) == 0) {
                    error_log(
                        __FILE__ . ':' . __LINE__ .
                        " - The account that component {$this->m_intComponentID} is attached to appears".
                        " to have no primary address!"
                    );

                    $this->SetInvalid();

                    return false;
                }

                $this->m_intDeliveryAddressID = mysql_result($hResult, 0, 0);
                error_log(
                    basename(__FILE__) . "@" . __LINE__ ." - The delivery address id set for component " .
                    "{$this->m_intComponentID} is: {$this->m_intDeliveryAddressID}"
                );
            } else {
                error_log(
                    basename(__FILE__) . "@" . __LINE__ ." - The delivery address id was already set for " .
                    "component component {$this->m_intComponentID}. It is {$this->m_intDeliveryAddressID}"
                );
            }

            // First, insert the config entry
            $strInsert = 'INSERT INTO config_hardware_bundles ( ' .
                                    ' component_id,        bundle_is_customised, ' .
                                    ' delivery_address_id, overall_hardware_order_status_id ) ' .
                              'VALUES ( ' .
                                    " '{$this->m_intComponentID}',       '{$this->m_binIsCustomised}', " .
                                    " '{$this->m_intDeliveryAddressID}', '{$this->m_intOverallOrderStatusID}' )";

            mysql_query($strInsert, $hConnection)
                or report_error(__FILE__, __LINE__, mysql_error($hConnection));

            $intID = mysql_insert_id($hConnection);

            error_log(
                basename(__FILE__) . "@" . __LINE__ ." - Added hardware bundle for the component " .
                "{$this->m_intComponentID} - delivery_address_id:{$this->m_intDeliveryAddressID} iniID:{$intID}"
            );
            // Now add the items
            $this->m_arrItems = $this->m_BundleType->GetDefaultItems();
            foreach ($this->m_arrItems as $intIndex => $rItem) {

                //Only add items that have a default quantity greater than 0
                if ($rItem['intDefaultQuantity'] > 0) {

                    $hConnection = get_named_connection('userdata');

                    $strInsert = 'INSERT INTO hardware_bundle_items ( ' .
                                            ' config_hardware_bundle_id, hardware_order_status_id, quantity, ' .
                                            ' hardware_supplier_product_id, order_number, when_last_updated, ' .
                                            ' invoice_number ) ' .
                                      'VALUES ( ' .
                                            " '$intID', '{$rItem['intStartingStatusID']}', " .
                                            " '{$rItem['intDefaultQuantity']}', " .
                                            " '{$rItem['intSupplierProductID']}', '', NOW(), '' ) ";

                    mysql_query($strInsert, $hConnection)
                        or report_error(__FILE__, __LINE__, mysql_error($hConnection));
                    $this->m_arrItems[$intIndex]['intID'] = mysql_insert_id($hConnection);
                }
            }

            //Insert an entry into the hardware tracking order table
            $dbhConn = get_named_connection_with_db('userdata');

            $strInsert = "INSERT INTO tblHardwareTracking (intConfigHardwareBundleId, vchTrackingOrderNumber)
                               VALUES ($intID,'{$this->m_strTrackingOrderNumber}')";

            PrimitivesQueryOrExit($strInsert, $dbhConn);

            // Set up the object
            $this->m_intID = $intID;
            // Mark the object as being back in sync
            $this->MarkUpToDate();
        } else {
            error_log(
                basename(__FILE__) . "@" . __LINE__ ." - The hardware order details existing on database [" .
                "config_hardware_bundles]. So we need to modify the details for component {$this->m_intComponentID}"
            );
            // Yes, update the existing configuration
            $hConnection = get_named_connection('userdata');

            $strQuery = "SELECT delivery_address_id FROM config_hardware_bundles " .
                "WHERE config_hardware_bundle_id='{$this->m_intID}'";
            $resultSet = mysql_query($strQuery, $hConnection)
                or report_error(__FILE__, __LINE__, mysql_error($hConnection));
            if (mysql_num_rows($resultSet) == 0) {
                error_log(
                    __FILE__ . ':' . __LINE__ .
                    " - The account that component {$this->m_intComponentID} is attached to appears".
                    " to have no primary address!"
                );
            } else {
            $existingDeliveryAddressID = mysql_result($resultSet, 0, 0);

                error_log(
                    basename(__FILE__) . "@" . __LINE__ ." - Existing dellivery address id:" .
                    "{$existingDeliveryAddressID}, new delivery address id:{$this->m_intDeliveryAddressID}" .
                    " of the component {$this->m_intComponentID}"
                );
            }
            // Update the bundle
            $strUpdate = 'UPDATE config_hardware_bundles '.
                            "SET when_ordered = '{$this->m_datWhenOrdered}', " .
                               " overall_hardware_order_status_id = '{$this->m_intOverallOrderStatusID}', " .
                               " delivery_address_id = '{$this->m_intDeliveryAddressID}' " .
                          "WHERE config_hardware_bundle_id='{$this->m_intID}'";
            mysql_query($strUpdate, $hConnection)
                or report_error(__FILE__, __LINE__, mysql_error($hConnection));
            // Store whether or not we need to change the status of the
            // component to active.
            $binMakeComponentActive = false;
            // Now update the items
            foreach ($this->GetItems() as $intIndex => $rItem) {
                //Only update items that have a quantity greater than 0
                    // Do we need to update or insert?
                    if ($rItem['intID'] > 0) {
                        if ($arrItems!=null) {
                            foreach ($this->m_arrItems AS $item) {
                                if ($item['intID'] == $rItem['intID']) {
                                    $strUpdate = 'UPDATE hardware_bundle_items ' .
                                                 "SET hardware_order_status_id='{$rItem['intStatus']}', " .
                                                 " hardware_supplier_product_id='{$rItem['intSupplierProductID']}', " .
                                                 " order_number='{$rItem['strOrderNumber']}', " .
                                                 " when_last_updated='{$rItem['strWhenLastUpdated']}', " .
                                                 " when_dispatched='{$rItem['strWhenDispatched']}', " .
                                                 " invoice_number='{$rItem['strInvoiceNumber']}', " .
                                                 " quantity = '{$rItem['intQuantity']}' " .
                                                 " WHERE hardware_bundle_item_id='{$rItem['intID']}'";
                                    mysql_query($strUpdate, $hConnection)
                                        or report_error(__FILE__, __LINE__, mysql_error($hConnection));
                                    $item_id = $rItem['intID'];
                                }
                            }

                        } else {
                                $strUpdate = 'UPDATE hardware_bundle_items ' .
                                                "SET hardware_order_status_id='{$rItem['intStatus']}', " .
                                                " hardware_supplier_product_id='{$rItem['intSupplierProductID']}', " .
                                                " order_number='{$rItem['strOrderNumber']}', " .
                                                " when_last_updated='{$rItem['strWhenLastUpdated']}', " .
                                                " when_dispatched='{$rItem['strWhenDispatched']}', " .
                                                " invoice_number='{$rItem['strInvoiceNumber']}', " .
                                                " quantity = '{$rItem['intQuantity']}' " .
                                                " WHERE hardware_bundle_item_id='{$rItem['intID']}'";
                                mysql_query($strUpdate, $hConnection)
                                    or report_error(__FILE__, __LINE__, mysql_error($hConnection));

                                $item_id = $rItem['intID'];
                        }
                    } else {
                        $strInsert = 'INSERT INTO hardware_bundle_items ( ' .
                                           ' hardware_order_status_id, hardware_supplier_product_id, ' .
                                           ' order_number, when_last_updated, ' .
                                           ' config_hardware_bundle_id, quantity, invoice_number ) ' .
                                     'VALUES ( ' .
                                           " '{$rItem['intStatus']}', '{$rItem['intSupplierProductID']}', " .
                                           " '', NOW(), " .
                                           " '{$this->m_intID}', '{$rItem['intQuantity']}', '' )";
                        mysql_query($strInsert, $hConnection)
                            or report_error(__FILE__, __LINE__, mysql_error($hConnection));
                        $this->m_arrItems[$intIndex]['intID'] = mysql_insert_id($hConnection);
                        $item_id = $this->m_arrItems[$intIndex]['intID'];

                    }//end of if/else ID = 0

                if (!empty($rItem['strSerialNumber'])) {
                    $strInsertSerialNo = 'INSERT INTO tblHardwareSerialNumber '.
                                     ' (intHardwareBundleItemId, vchSerialNumber) '.
                                     " VALUES ($item_id,  '{$rItem['strSerialNumber']}')";
                    mysql_query($strInsertSerialNo, $hConnection)
                    or report_error(__FILE__, __LINE__, mysql_error($hConnection));
                }
            }
            //end of foreach: item
            // Do we need to add to the overall status history log?
            if ($this->m_bolOverallOrderStatusChanged == true) {
                $strNow = date('Y-m-d H:i:s');

                // First make the existing history entry inactive
                $strUpdate = 'UPDATE tblOverallHardwBundleStatus ' .
                                "SET dtmEnd='$strNow' " .
                              "WHERE usiConfigHardwareBundleID='{$this->m_intID}' " .
                                'AND dtmEnd IS NULL';
                mysql_query($strUpdate, $hConnection)
                    or report_error(__FILE__, __LINE__, mysql_error($hConnection));

                // Now store the new history
                $strInsert = 'INSERT INTO tblOverallHardwBundleStatus (' .
                                        ' usiConfigHardwareBundleID, usiHardwareOrderStatusID, ' .
                                        ' dtmStart ) ' .
                                  'VALUES ( ' .
                                        " '{$this->m_intID}', '{$this->m_intOverallOrderStatusID}', " .
                                        " '$strNow' )";
                mysql_query($strInsert, $hConnection)
                    or report_error(__FILE__, __LINE__, mysql_error($hConnection));
            }

            //If the tracking order number is still blank don't bother trying to update it
            $strTrackingOrderNumber = trim($this->m_strTrackingOrderNumber);

            if ($strTrackingOrderNumber != '' && $strTrackingOrderNumber != 'Please Call for Details') {
                //Do we need to insert or update the hardware tracking number
                //Get a connection
                $dbhConn = get_named_connection_with_db("userdata");

                //Select any currently held number
                $strSelect = "SELECT *
                                FROM tblHardwareTracking
                               WHERE intConfigHardwareBundleId = '{$this->m_intID}'";

                $resResult = PrimitivesQueryOrExit($strSelect, $dbhConn);

                $arrResult = PrimitivesResultsAsArrayGet($resResult);

                //If an entry already exists - update it
                if (isset($arrResult[0]['vchTrackingOrderNumber'])) {
                    $strCurrentTracking = $arrResult[0]['vchTrackingOrderNumber'];

                    //If the tracking number has changed then it needs updating
                    if ($strCurrentTracking != $strTrackingOrderNumber) {
                        //Update the tracking number
                        $strUpdate = "UPDATE tblHardwareTracking
                                         SET vchTrackingOrderNumber = '$strTrackingOrderNumber'
                                       WHERE intConfigHardwareBundleId = '{$this->m_intID}'";

                        PrimitivesQueryOrExit($strUpdate, $dbhConn);
                    }
                    // else insert a new entry to hold the hardware tracking number
                } else {
                    //Create a new row to hold the tracking number
                    $strInsert = "INSERT INTO tblHardwareTracking (intConfigHardwareBundleId, vchTrackingOrderNumber)
                                       VALUES ('{$this->m_intID}','{$this->m_strTrackingOrderNumber}')";

                    PrimitivesQueryOrExit($strInsert, $dbhConn);
                }
            }//end of if tracking number not blank

            // Mark the object as being back in sync
            $this->MarkUpToDate();
        }
        return true;
    } // function Commit()

    /**
     * links a service event against a bundle
     *
     * @param int $intServiceEventID ServiceEventID
     *
     * @return boolean
     */
    function AddServiceEvent($intServiceEventID)
    {
        // make sure object is initialised
        if (!preg_match('/^[0-9]+$/', $this->m_intConfigHardwareBundleID)) {
            return false;
        }

        // make sure we've been passed a numeric event id
        if (!preg_match('/^[0-9]+$/', $intServiceEventID)) {
            return false;
        }

        $dbhConnection = get_named_connection_with_db('userdata');

        $strQuery = 'UPDATE config_hardware_bundles ' .
                       'SET service_event_id = ' . addslashes($intServiceEventID) . ' ' .
                     'WHERE config_hardware_bundle_id = ' . $this->m_intConfigHardwareBundleID;

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

        if (PrimitivesAffectedRowsGet($dbhConnection)) {
            return true;
        }

        return false;

    } // function AddServiceEvent()

    /**
     * links invoice against a bundle
     *
     * @param int $intInvoiceID InvoiceID
     *
     * @return boolean
     */
    function AddInvoiceLink($intInvoiceID)
    {
        // make sure object has been initialised
        if (!preg_match('/^[0-9]+$/', $this->m_intConfigHardwareBundleID)) {
            return false;
        }

        // make sure invoice ID is a number
        if (!preg_match('/^[0-9]+$/', $intInvoiceID)) {
            return false;
        }

        $dbhConnection = get_named_connection_with_db('userdata');

        $strQuery = 'UPDATE config_hardware_bundles ' .
                       'SET sales_invoice_id = ' . addslashes($intInvoiceID) . ' ' .
                     'WHERE config_hardware_bundle_id = ' . $this->m_intConfigHardwareBundleID;

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

        if (PrimitivesAffectedRowsGet($dbhConnection)) {
            return true;
        }

        return false;

    } // function AddInvoiceLink()

    //
    // Get Functions
    //
    /**
     * Initialise the object from the database using the passed
     * service id.
     *
     * @param int $intServiceID ServiceID
     *
     * @return boolean
     */
    function GetByServiceID($intServiceID)
    {
        // We're going to do this by finding the correct component and
        // then calling GetByComponentID
        $hConnection = get_named_connection('userdata');

        $strQuery = 'SELECT c.component_id AS intComponentID, ' .
                          ' c.status       AS strComponentStatus, ' .
                          ' hos.tag        AS strOrderStatusHandle ' .
                      'FROM components                       AS c, ' .
                          ' config_hardware_bundles          AS chb, ' .
                          ' products.hardware_order_statuses AS hos ' .
                     'WHERE c.component_id=chb.component_id ' .
                       'AND chb.overall_hardware_order_status_id=hos.hardware_order_status_id ' .
                       'AND c.status IN ("active", "queued-activate", "queued-reactivate", "unconfigured") ' .
                       "AND service_id='$intServiceID'";

        $hResult = mysql_query($strQuery, $hConnection)
            or report_error(__FILE__, __LINE__, mysql_error($hConnection));

        if (mysql_num_rows($hResult) == 0) {
            error_log(__FILE__ . ':' . __LINE__ . " Service '$intServiceID' doesn't have a hardware bundle component");

            $this->SetInvalid();

            $binRetVal = false;
        } else if (mysql_num_rows($hResult) > 1) {
            // We have more than one component. Run through them all and
            // find the one with the highest precedence
            // Order of precedence is as follows:
            // Order  Component Status   Hardware Status
            // 1      active             rma_in_progress
            // 2.     queued-activate    Anything other than dispatched, cancelled, rejected, rma_in_progress
            // 3.     active             dispatched

            // Arbitrary high number
            $intBestFoundPrecedence = 99999;
            $recBundleToUse         = false;

            while ($recRow = mysql_fetch_assoc($hResult)) {
                // Work out what precedence this is
                $intPrecedence = 99999;

                switch ($recRow['strComponentStatus']) {
                    case 'active':
                        switch ($recRow['strOrderStatusHandle']) {
                            case 'rma_in_progress':
                                $intPrecedence = 1;

                            case 'dispatched':
                                $intPrecedence = 3;
                        }

                        break;

                    case 'queued-activate':
                        switch ($recRow['strOrderStatusHandle']) {
                            case 'dispatched':
                            case 'cancelled':
                            case 'rejected':
                            case 'rma_in_progress':
                                $intPrecedence = 99999;

                            case 'dispatched':
                                $intPrecedence = 2;
                        }
                        break;
                }

                // Is this better than the current best?
                if ($intPrecedence <= $intBestFoundPrecedence) {
                    $intBestFoundPrecedence = $intPrecedence;

                    $recBundleToUse = $recRow;
                }
            }

            // Have we found one to use?
            if ($recBundleToUse === false) {
                error_log(
                    __FILE__ . ':' . __LINE__ .
                    " Service '$intServiceID' has more than one hardware bundle component. ".
                    "Can't figure out which to use so returning false."
                );

                $this->SetInvalid();

                $binRetVal = false;
            } else {

                $this->m_intServiceID = $intServiceID;

                $binRetVal = $this->GetByComponentID($recBundleToUse['intComponentID']);
            }
        } else {
            $this->m_intServiceID = $intServiceID;

            $intComponentID = mysql_result($hResult, 0, 0);

            $binRetVal = $this->GetByComponentID($intComponentID);
        }

        mysql_free_result($hResult);

        return $binRetVal;

    } // function GetByServiceID($intServiceID)

    /**
     * Initialise the object from the database using the passed
     * Hardware ID.
     *
     * @param int $intHardwareID HardwareID
     *
     * @return boolean
     */
    function GetByHardwareID($intHardwareID)
    {
        // We're going to do this by finding the correct component and
        // then calling GetByComponentID
        $hConnection = get_named_connection('userdata_reporting');

        // Sanity check hardware ID
        if (!preg_match("/^[0-9]+$/", $intHardwareID)) {
            $intHardwareID = preg_replace("/^0-/", "", $intHardwareID);
        }

        $strQuery = 'SELECT chb.component_id        AS intComponentID ' .
                      'FROM config_hardware_bundles AS chb ' .
                     "WHERE chb.config_hardware_bundle_id='$intHardwareID'";

        $hResult = mysql_query($strQuery, $hConnection)
            or report_error(__FILE__, __LINE__, mysql_error($hConnection));

        if (mysql_num_rows($hResult) == 0) {
            error_log(
                'HardwareBundle::GetByHardwareID:' . __LINE__ . " - Hardware bundle '$intHardwareID' doesn't exist"
            );

            $this->SetInvalid();

            $binRetVal = false;
        } else {
            $intComponentID = mysql_result($hResult, 0, 0);

            $binRetVal = $this->GetByComponentID($intComponentID);
        }

        mysql_free_result($hResult);

        return $binRetVal;

    } // function GetByHardwareID($intHardwareID)

    /**
     * GetRmaClassName
     *
     * @return string
     */
    function GetRmaClassName()
    {
        return $this->GetClassPrefix() . 'HardwareRmaProcess';
    }

    /**
     * GetServiceID
     *
     * @return number
     */
    function GetServiceID()
    {
        if (empty($this->m_intServiceID)) {
            if (empty($this->m_intComponentID)) {
                error_log(
                    basename(__FILE__) . "@" . __LINE__ .
                    " - Cannot obtain the serviceId. ComponentID not set on object"
                );
                return $this->m_intServiceID;
            }

            // Obtain the Service ID
            $dbhConnection = get_named_connection('userdata_reporting');

            $strQuery = 'SELECT service_id ' .
                          'FROM components ' .
                         "WHERE component_id='{$this->m_intComponentID}'";

            $resResult = mysql_query($strQuery, $dbhConnection)
                or report_error(__FILE__, __LINE__, mysql_error($dbhConnection));

            $this->m_intServiceID = PrimitivesResultGet($resResult, 'service_id');
        }

        if (empty($this->m_intServiceID)) {
            error_log(
                basename(__FILE__) . "@" . __LINE__ .
                    " - ServiceID is empty after GetServiceID() was called. ComponentId used was: {$this->m_intComponentID}"
            );
        }

        return $this->m_intServiceID;
    }

    /**
     * Initialise the object from the database using the passed
     * component id.
     *
     * @param int $intComponentID ComponentID
     * @param int $intRMA         RMA
     *
     * @return boolean
     */
    function GetByComponentID($intComponentID,$intRMA=0)
    {
        // Get the component being passed
        $Component = userdata_component_get($intComponentID);
        // Get the hardware bundle type
        $strClassName = $this->GetClassPrefix() . 'HardwareBundleType';

        $BundleType = new $strClassName();

        $BundleType->GetByComponentType($Component['component_type_id']);
        if ($BundleType === false) {
            $this->SetInvalid();

            return false;
        }

        $this->m_BundleType = $BundleType;

        // Get the config record
        $hConnection = get_named_connection('userdata_reporting');

        $strSelect = 'SELECT chb.config_hardware_bundle_id        AS intConfigHardwareBundleID, ' .
                           ' chb.bundle_is_customised             AS binIsCustomised, ' .
                           ' chb.delivery_address_id              AS intDeliveryAddressID, ' .
                           ' chb.when_ordered                     AS datWhenOrdered, ' .
                           ' chb.overall_hardware_order_status_id AS intOverallHardwareOrderStatus, ' .
                           ' hos.tag                              AS strOverallStatusTag, ' .
                           ' hos.display_name                     AS strOverallStatusDisplayName, ' .
                           ' hos.description                      AS strOverallStatusDescription, ' .
                           ' ht.vchTrackingOrderNumber            AS strTrackingOrderNumber, ' .
                           ' chb.service_event_id                 AS intPurchaseServiceEvent, ' .
                           ' chb.sales_invoice_id                 AS intSalesInvoiceID ' .
                       'FROM products.hardware_order_statuses AS hos, ' .
                           ' config_hardware_bundles          AS chb ' .
                 ' LEFT JOIN tblHardwareTracking              AS ht ' .
                        ' ON (ht.intConfigHardwareBundleId = chb.config_hardware_bundle_id) ' .
                      'WHERE chb.overall_hardware_order_status_id=hos.hardware_order_status_id ' .
                        "AND component_id='$intComponentID' ";

        $hResult = mysql_query($strSelect, $hConnection)
            or report_error(__FILE__, __LINE__, mysql_error($hConnection));

        if (mysql_num_rows($hResult) == false) {
            $this->SetInvalid();

            return false;
        }

        $rRow = mysql_fetch_assoc($hResult);
        mysql_free_result($hResult);

        $intConfigHardwareBundleID           = $rRow['intConfigHardwareBundleID'];
        $this->m_intConfigHardwareBundleID   = $rRow['intConfigHardwareBundleID'];
        $this->m_binIsCustomised             = $rRow['binIsCustomised'];
        $this->m_intDeliveryAddressID        = $rRow['intDeliveryAddressID'];
        $this->m_datWhenOrdered              = $rRow['datWhenOrdered'];
        $this->m_intOverallOrderStatusID     = $rRow['intOverallHardwareOrderStatus'];
        $this->m_strOverallStatusTag         = $rRow['strOverallStatusTag'];
        $this->m_strOverallStatusDisplayName = $rRow['strOverallStatusDisplayName'];
        $this->m_strOverallStatusDescription = $rRow['strOverallStatusDescription'];
        $this->m_strTrackingOrderNumber      = $rRow['strTrackingOrderNumber'];
        $this->m_intSalesInvoiceID           = $rRow['intSalesInvoiceID'];

        // Get purchase date from service event
        $strQuery = 'SELECT UNIX_TIMESTAMP(event_date) AS uxtPurchaseDate ' .
                      'FROM userdata.service_events ' .
                     'WHERE service_event_id = ' . $rRow['intPurchaseServiceEvent'];

        $resResult = mysql_query($strQuery, $hConnection)
                     or report_error(__FILE__, __LINE__, mysql_error($hConnection));

        if ($arrRow = mysql_fetch_assoc($resResult)) {
            $this->m_uxtDatePurchased = $arrRow['uxtPurchaseDate'];
        }

        mysql_free_result($resResult);

        //Added to handle RMAs, which should no longer include items that have a value of intAllowRMA = 0
        if ($intRMA === 1) {
            $strAllowRMASelectExtra = ' AND hsp.intAllowRMA > 0 ';
        } else {
            $strAllowRMASelectExtra = '';
        }

        // Get the items
        $strSelect = 'SELECT hbi.hardware_bundle_item_id      AS intID, '
                   .       ' hbi.hardware_order_status_id     AS intStatus, '
                   .       ' hbi.order_number                 AS strOrderNumber, '
                   .       ' hbi.invoice_number               AS strInvoiceNumber, '
                   .       ' hbi.quantity                     AS intQuantity, '
                   .       ' hbi.hardware_supplier_product_id AS intSupplierProductID, '
                   .       ' hbi.when_last_updated            AS strWhenLastUpdated, '
                   .       ' hbi.when_dispatched              AS strWhenDispatched, '
                   .       ' hos.display_name                 AS strStatusDisplayName, '
                   .       ' hos.tag                          AS strStatusTag, '
                   .       ' hos.description                  AS strStatusDescription, '
                   .       ' hos.portal_description           AS strStatusPortalDescription, '
                   .       ' hsos.order_can_be_cancelled      AS binOrderCanBeCancelled, '
                   .       ' hsp.display_name                 AS strDisplayName, '
                   .       ' hsp.product_code                 AS strProductCode, '
                   .       ' hs.supplier_name                 AS strSupplierName, '
                   .       ' hs.tag                           AS strSupplierTag, '
                   .       ' hpc.is_valuable                  AS intIsValuable, '
                   .       ' hpc.hardware_product_class_id    AS intHardwareProductClassID, '
                   .       ' hpc.tag                          AS intHardwareProductClassTag, '
                   .       ' (hsp.curCostToCustomerIncVatPence * hbi.quantity) AS intCostToCustomer, '
                   .       ' hsp.intAllowRMA                  AS intAllowRMA, '
                   .       ' m.intTr069ModelId, '
                   .       ' m.vchModelName                   AS strDefaultModelName '

                   .  ' FROM hardware_bundle_items            AS hbi '

                   .  ' JOIN products.hardware_order_statuses          AS hos '
                   .    ' ON hbi.hardware_order_status_id = hos.hardware_order_status_id '

                   .  ' JOIN products.hardware_supplier_order_statuses AS hsos '
                   .    ' ON hos.hardware_order_status_id = hsos.hardware_order_status_id '

                   .  ' JOIN products.hardware_supplier_products       AS hsp '
                   .    ' ON hbi.hardware_supplier_product_id = hsp.hardware_supplier_product_id '

                   .  ' JOIN products.hardware_suppliers               AS hs '
                   .    ' ON hsp.hardware_supplier_id  = hs.hardware_supplier_id '
                   .   ' AND hsos.hardware_supplier_id = hs.hardware_supplier_id '

                   .  ' JOIN products.hardware_product_classes         AS hpc '
                   .    ' ON hsp.hardware_product_class_id = hpc.hardware_product_class_id '

                   .  ' LEFT '
                   .  ' JOIN dbCommonADSL.tblTr069Model m '
                   .    ' ON hsp.intTr069ModelId = m.intTr069ModelId '

                   .  'WHERE hbi.config_hardware_bundle_id = ' . mysql_real_escape_string($intConfigHardwareBundleID, $hConnection)
                   .         $strAllowRMASelectExtra

                   . ' ORDER BY hsp.display_name';

        $hResult = mysql_query($strSelect, $hConnection)
            or report_error(__FILE__, __LINE__, mysql_error($hConnection));

        // Populate the items
        while ($rRow = mysql_fetch_assoc($hResult)) {

            $rRow['strSerialNumber'] = $this->getCurrentSerialNumber($rRow['intID']);

            $this->m_arrItems[] = $rRow;

            if (!empty($rRow['intTr069ModelId'])) {
                $this->m_bolTr069Compatible = true;
            }

            if (!empty($rRow['strDefaultModelName'])) {
                $this->m_strDefaultModelName = $rRow['strDefaultModelName'];
            }

            // Also store information about the item's status, for future reference
            if (!isset($this->m_arrStatuses[$rRow['strStatusTag']])) {
                $this->m_arrStatuses[$rRow['strStatusTag']] = array(
                    'intStatus'            => $rRow['intStatus'],
                    'strDisplayName'       => $rRow['strStatusDisplayName'],
                    'strDescription'       => $rRow['strStatusDescription'],
                    'strPortalDescription' => $rRow['strStatusPortalDescription']
                );
            }
        }

        mysql_free_result($hResult);

        // Set the config data
        $this->m_BundleType      = $BundleType;
        $this->m_binIsCustomised = false;
        $this->m_intComponentID  = $intComponentID;
        $this->m_intID           = $intConfigHardwareBundleID;

        $this->SetValid();

        $this->MarkUpToDate();

        return true;

    } // function GetByComponentID($intComponentID)

    /**
     * Retrieves the current serial number for a bundle item
     *
     * @return string
     */
    function getCurrentSerialNumber($intHardwareBundleItemId)
    {
        $db = get_named_connection_with_db('userdata');

        $strQuery = "SELECT vchSerialNumber
                     FROM userdata.tblHardwareSerialNumber
                     WHERE intHardwareBundleItemId = $intHardwareBundleItemId
                     ORDER BY dtmAdded DESC LIMIT 1";

        $result = PrimitivesQueryOrExit($strQuery, $db);
        return PrimitivesResultGet($result, 'vchSerialNumber');
    }

    /**
     * Get the primary model name (used for Tr069)
     *
     * @return model name
     */
    function getDefaultModelName()
    {
        return $this->m_strDefaultModelName;
    }

    /**
     * Does the bundle contain a Tr069 compatible device?
     *
     * @return boolean
     */
    function isTr069Compatible()
    {
        return $this->m_bolTr069Compatible;
    }

    /**
     * Get the hardware bundle name
     *
     * @return strName
     */
    function GetName()
    {
        return $this->m_BundleType->m_strName;
    }

    /**
     * Get the items
     *
     * @return multitype:
     */
    function GetItems()
    {
        return $this->m_arrItems;
    }

    /**
     * GetItem
     *
     * @param int $intItemIndex Item index
     *
     * @return multitype:
     */
    function GetItem($intItemIndex)
    {
        return $this->m_arrItems[$intItemIndex];
    }

    /**
     * Get cost to customer
     *
     * @return int
     */
    function GetCostToCustomer()
    {
        // Check for Reseller Discount
        $serviceId = (int) $this->GetServiceID();
        try {
            $actor = Auth_BusinessActor::getActorByExternalUserId($serviceId);
            $endUser = new Reseller_EndUser($actor->getActorId());

        } catch (Db_ObjectException $e) {
            // Not a Reseller EU.
            $intTotalCost = 0;

            foreach (array_keys($this->m_arrItems) as $intItemKey) {
                $intTotalCost += $this->m_arrItems[$intItemKey]['intCostToCustomer'];
            }

            return number_format($intTotalCost/100, 2);
        }

        $partner = new Reseller_Partner($endUser->getParentId());
        $product = new Reseller_HardwareProduct($this->GetComponentType());
        $discount = Reseller_HardwareDiscount::retrieve($partner, $product);
        $intTotalCost = $discount->getDiscountedCost()->getValue();

        return number_format($intTotalCost, 2);

    } // function GetCostToCustomer()

    /**
     * Get list of additional items in bundle (based on default quantity from bundle definition)
     *
     * <AUTHOR> Kurylowicz
     *
     * @return array
     */
    function GetAdditionalItems()
    {
        $arrDefaultItems = array();
        $arrAdditionalItems = array();

        if (false == ($this->m_BundleType instanceof HardwareBundleType)) {

            // Get the component being passed
            $arrComponent = $this->GetComponent();

            // Get the hardware bundle type
            $strClassName = $this->GetClassPrefix() . 'HardwareBundleType';

            $this->m_BundleType = new $strClassName();

            $this->m_BundleType->GetByComponentType($arrComponent['component_type_id']);

            // Have we got a type object?
            if ($this->m_BundleType === false) {

                $this->SetInvalid();
                return false;
            }
        }

        // Create indexed array
        foreach ($this->m_BundleType->getDefaultItems() as $arrDefaultItem) {
            $arrDefaultItems[$arrDefaultItem['intSupplierProductID']] = $arrDefaultItem;
        }

        // Now check for additional items
        foreach ($this->m_arrItems as $intItemKey => $arrItem) {
            $intSupplierProductID = $arrItem['intSupplierProductID'];

            if ($arrItem['intQuantity'] > $arrDefaultItems[$intSupplierProductID]['intDefaultQuantity']) {

                $arrNewItem = $arrItem;

                $arrNewItem['intQuantity'] -= $arrDefaultItems[$intSupplierProductID]['intDefaultQuantity'];
                $arrNewItem['intCostToCustomer']
                    = $arrDefaultItems[$intSupplierProductID]['intItemCost'] * $arrNewItem['intQuantity'];

                $arrAdditionalItems[] = $arrNewItem;
            }
        }

        return $arrAdditionalItems;
    }

    /**
     * Get cost of additional items in bundle
     *
     * <AUTHOR> Kurylowicz
     *
     * @return float
     */
    function GetCostOfAdditionalItemsToCustomer()
    {
        $intAdditionalCost = 0;
        $arrAdditionalItems = $this->GetAdditionalItems();

        if (false !== $arrAdditionalItems) {

            foreach ($arrAdditionalItems as $arrAdditionalItem) {
                $intAdditionalCost += $arrAdditionalItem['intCostToCustomer'];
            }
        }

        return number_format($intAdditionalCost / 100, 2);
    }

    /**
     * Get the bundle's status history
     *
     * @param boolean $bolIncludeCurrent IncludeCurrent
     * @param boolean $bolReverseOrder   ReverseOrder
     *
     * @return multitype:unknown
     */
    function GetOverallStatusHistory($bolIncludeCurrent, $bolReverseOrder)
    {
        // Have we already got this?
        if (count($this->m_arrOverallStatusHistory) == 0) {
            // No, so get it
            $dbhConnection = get_named_connection('userdata');

            $strQuery = 'SELECT ohbs.usiHardwareOrderStatusID AS intStatusID, ' .
                              ' hos.display_name              AS strStatusDisplayName, ' .
                              ' hos.description               AS strDescription, ' .
                              ' ohbs.dtmStart                 AS strStatusStart, ' .
                              ' ohbs.dtmEnd                   AS strStatusEnd ' .
                          'FROM tblOverallHardwBundleStatus      AS ohbs, ' .
                              ' products.hardware_order_statuses AS hos ' .
                         'WHERE ohbs.usiHardwareOrderStatusID=hos.hardware_order_status_id ' .
                           "AND usiConfigHardwareBundleID='{$this->m_intID}' " .
                      'ORDER BY ohbs.dtmStart';

            $resResult = mysql_query($strQuery, $dbhConnection)
                or report_error(__FILE__, __LINE__, mysql_error($dbhConnection));

            while ($recStatus = mysql_fetch_assoc($resResult)) {
                $this->m_arrOverallStatusHistory[] = $recStatus;
            }
        }

        // Filter out the current status?
        if ($bolIncludeCurrent !== true) {
            $arrReturn = array();

            foreach ($this->m_arrOverallStatusHistory as $recStatus) {
                if ($recStatus['intStatusID'] != $this->m_intHardwareOrderStatus) {
                    $arrReturn[] = $recStatus;
                }
            }
        } else {
            $arrReturn = $this->m_arrOverallStatusHistory;
        }

        // Reverse the order?
        if ($bolReverseOrder === true) {
            $arrReturn = array_reverse($arrReturn);
        }

        return $arrReturn;

    } // function GetOverallStatusHistory


    /**
     * GetTimeSinceDispatch
     * Gets the number of seconds that have passed since the bundle was
     * dispatched. Returns false if the bundle hasn't been dispatched.
     *
     * @return boolean|number
     */
    function GetTimeSinceDispatch()
    {
        if ($this->m_arrItems[0]['strWhenDispatched'] == false
            || $this->m_arrItems[0]['strWhenDispatched'] == '0000-00-00 00:00:00'
        ) {
            return false;
        } else {
            return time() - strtotime($this->m_arrItems[0]['strWhenDispatched']);
        }
    }

    /**
     * Gets the purchase date (unix timestamp)
     *
     * @return number
     */
    function GetDatePurchased()
    {
        return $this->m_uxtDatePurchased;
    }

    /**
     * Invoice ID for the order
     *
     * @return number
     */
    function GetInvoiceID()
    {
        return $this->m_intSalesInvoiceID;
    }

    /**
     * CountItems
     *
     * @return int
     */
    function CountItems()
    {
        return count($this->m_arrItems);
    }

    /**
     * Get the status display name
     *
     * @return string
     */
    function GetStatusDisplayName()
    {
        return $this->m_strOverallStatusDisplayName;
    }

    /**
     * Get the description of the current status
     * Pass -1 or nothing to get the current status, pass an item index
     * otherwise
     *
     * @param int $intItemIndex ItemIndex
     *
     * @return string
     */
    function GetStatusDescription($intItemIndex=-1)
    {
        if ($intItemIndex == -1) {
            return $this->m_strOverallStatusDescription;
        } else {
            return $this->m_arrItems[$intItemIndex]['strStatusDescription'];
        }
    }

    /**
     * Get the portal description of the current status
     * Pass -1 or nothing to get the current status, pass an item index
     * otherwise
     *
     * @param int $intItemIndex ItemIndex
     *
     * @return string
     */
    function GetStatusPortalDescription($intItemIndex=-1)
    {
        if ($intItemIndex == -1) {
            return $this->m_strOverallStatusDescription;
        } else {
            return $this->m_arrItems[$intItemIndex]['strStatusPortalDescription'];
        }
    }

    /**
     * Get when the passed item was ordered Pass nothing to get the date that the first item was ordered
     *
     * @param int $intItemIndex ItemIndex
     *
     * @return NULL
     */
    function GetWhenOrdered($intItemIndex=0)
    {
        return $this->m_datWhenOrdered;
    }

    /**
     * Get the status tag
     *
     * @param int $intItemIndex ItemIndex
     *
     * @return strStatusTag
     */
    function GetStatusTag($intItemIndex=-1)
    {
        if ($intItemIndex == -1) {
            return $this->m_strOverallStatusTag;
        } else {
            return $this->m_arrItems[$intItemIndex]['strStatusTag'];
        }
    }

    /**
     * GetComponentType
     *
     * @return Company type
     */
    function GetComponentType()
    {
        return $this->m_BundleType->GetComponentType();
    }

    /**
     * GetOrderNumber
     *
     * @param int $intItemID ItemID
     * @return array
     */
    function GetOrderNumber($intItemID=0)
    {
        return $this->m_arrItems[$intItemID]['strOrderNumber'];
    }

    /**
     * GetInvoiceNumber
     *
     * @param int $intItemID ItemID
     *
     * @return array
     */
    function GetInvoiceNumber($intItemID=0)
    {
        return $this->m_arrItems[$intItemID]['strInvoiceNumber'];
    }

    /**
     * GetBundleId
     *
     * @return ConfigHardwareBundleID
     */
    function GetBundleId()
    {
        return $this->m_intConfigHardwareBundleID;
    }

    /**
     * GetWhenLastUpdated
     *
     * @param int $intItemID Item Id
     *
     * @return array
     */
    function GetWhenLastUpdated($intItemID=0)
    {
        return $this->m_arrItems[$intItemID]['strWhenLastUpdated'];
    }

    /**
     * Can the order be cancelled. Pass an item index or nothing to just get the first one.
     *
     * @param int $intItemIndex ItemIndex
     *
     * @return array
     */
    function CanBeCancelled($intItemIndex=0)
    {
        return $this->m_arrItems[$intItemIndex]['binOrderCanBeCancelled'];
    }


    /**
     * Get the delivery address
     *
     * @return multitype:
     */
    function GetDeliveryAddress()
    {
        // See if we've already got it
        if (count($this->m_rDeliveryAddress) == 0) {
            // No, so we need to get it.
            $this->m_rDeliveryAddress = userdata_address_get($this->m_intDeliveryAddressID);
        }

        return $this->m_rDeliveryAddress;
    }

    /**
     * Get the billing address
     *
     * @return array
     */
    function GetBillingAddress()
    {
        // See if we've already got it
        if ($this->m_Supplier == null) {
            // No, so we need to get it.
            $strClassName = $this->GetClassPrefix() . 'HardwareBundleSupplier';

            $this->m_Supplier = new $strClassName();
        }

        return $this->m_Supplier->GetBillingAddress();
    }


    /**
     * GetComponent
     *
     * @return multitype:
     */
    function GetComponent()
    {
        if (count($this->m_rComponent) == 0) {
            $this->m_rComponent = userdata_component_get($this->m_intComponentID);
        }

        return $this->m_rComponent;
    }

    /**
     * Get the Service that this component is attached to
     *
     * @return multitype:
     */
    function GetService()
    {
        if (count($this->m_rService) == 0) {
            $rComponent = $this->GetComponent();

            $this->m_rService = userdata_service_get($rComponent['service_id']);
        }

        return $this->m_rService;
    }

    /**
     * Get the User that this component is attached to
     *
     * @return array user
     */
    function GetUser()
    {
        if (count($this->m_rUser) == 0) {
            $rService = $this->GetService();

            $this->m_rUser = userdata_user_get($rService['user_id']);
        }

        return $this->m_rUser;
    }

    /**
     * Get the hardware bundle ID attached to this component
     *
     * @return number
     */
    function GetHardwareBundleID()
    {
        return $this->m_intID;
    }

    /**
     * Get the hardware bundle ID attached to this component
     *
     * @return string
     */
    function GetTrackingOrderNumber()
    {
        return $this->m_strTrackingOrderNumber;
    }

    /**
     * GetHardwareShipperUrl
     * Find out which shipper we are using by looking at the Tracking Order Number
     *
     * @return string|row
     */
    function GetHardwareShipperUrl()
    {
        $strTrackingOrderNumber = $this->m_strTrackingOrderNumber;

        //If the tracking number is not set return an empty string
        if (!isset($strTrackingOrderNumber) || strlen($strTrackingOrderNumber) < 3) {
            return '';
        }

        $intHWSupplierID = $this->GetSupplierID();

        $dbhConnection = get_named_connection_with_db('product');

        $strSQL = 'SELECT vchHardwareShipperUrl '.
                  'FROM vblHardwareShippers HS, '.
                  'hardware_suppliers HSU '.
                  'WHERE HS.intHardwareShippersId = HSU.intHardwareShippersId '.
                  "AND HSU.hardware_supplier_id = '$intHWSupplierID'";

        $resResult = PrimitivesQueryOrExit($strSQL, $dbhConnection);

        $arrResults = PrimitivesResultsAsArrayGet($resResult);

        if (count($arrResults) > 0) {
            return $arrResults[0]['vchHardwareShipperUrl'];
        } else {
            //get the default address
            $arrHardwareShipper = $this->GetHardwareShipper();

            return $arrHardwareShipper['vchHardwareShipperUrl'];
        }

    }//end of function: GetHardwareShipperUrl();

    /**
     * GetHardwareShipper
     *
     * @param string $strTrackingOrderKey the tracking order number helps distinguidh the shipper
     *
     * @return row from vblHardwareShippers table
     */
    function GetHardwareShipper($strTrackingOrderKey='')
    {
        //Add slashes to strTrackingOrderKey - just incase
        $strTrackingOrderKey = addslashes($strTrackingOrderKey);

        //Get all info about the hardware shippers
        $dbConnection = get_named_connection_with_db('product');

        $strHardwareShippers = "SELECT *
                                  FROM vblHardwareShippers
                                 WHERE vchTrackingOrderKey = '$strTrackingOrderKey'";

        $resResults = PrimitivesQueryOrExit($strHardwareShippers, $dbConnection);

        $arrHardwareShipper = PrimitivesResultsAsArrayGet($resResults);

        return $arrHardwareShipper[0];

    }//end of function: GetHardwareShippers()

    /**
     * Get the type of the hardware (e.g. pci, usb etc) for use by the
     * portal in showing the correct chunk of content.
     *
     * @return string
     */
    function GetTypeForPortal()
    {
        $arrTypes = array('pci', 'usb', 'router');

        // This is a little hacky, but time is running out...
        $strType = '';

        foreach ($this->GetItems() as $recItem) {
            foreach ($arrTypes as $strCheckType) {
                if (stristr($recItem['intHardwareProductClassTag'], $strCheckType)) {
                    $strType = $strCheckType;

                    break;
                }
            }

            if ($strType != '') {
                // We've found it
                break;
            }

        }

        return $strType;
    }


    //
    // Process Functions
    //

    /**
     * Utility function to put an order into 'awaiting_ordering' state.
     *
     * @return boolean
     */
    function PlacePendingOrder()
    {
        $this->SetStatusTag(-1, 'awaiting_ordering');

        if ($this->Commit() == true) {
            $this->AddContact('Hardware Queued for ordering');

            return true;
        } else {
            return false;
        }
    }

    /**
     * Place Order. Virtual function that must be overridden
     *
     * @return void
     */
    function PlaceOrder()
    {
        report_error(__FILE__, __LINE__, 'HardwareBundle::PlaceOrder must be overridden!');
    }

    /**
     * Provision. Virtual function that must be overridden
     *
     * @return void
     */
    function ProvisionOrder()
    {
        report_error(__FILE__, __LINE__, 'HardwareBundle::ProvisionOrder must be overridden!');
    }

    /**
     * Provision. Virtual function that must be overridden
     *
     * @return void
     */
    function UpdateOrder()
    {
        report_error(__FILE__, __LINE__, 'HardwareBundle::UpdateOrder must be overridden!');
    }


    /**
     * Cancel the order
     *
     * @return boolean|void
     */
    function CancelOrder()
    {
        // See if we're allowed to cancel this order
        // if the account was in  queued-destroy status we can cancel the order
        if ($this->CanBeCancelled() == false) {
            $rService = $this->GetService();
            if ($rService['status'] == "queued-destroy") {
                userdata_component_set_status($this->m_intComponentID, 'destroyed');
            } else {
                return false;
            }
        } else if ($this->GetStatusTag() == 'not_ordered'
            || $this->GetStatusTag() == 'cancelled'
            || $this->GetStatusTag() == 'rejected'
        ) {
            // We can just forget about this order
            userdata_component_set_status($this->m_intComponentID, 'destroyed');
        } else {
            // Ask the component to cancel the order (this is done by the
            // specific Supplier instance of the object).
            return $this->DoCancelOrder();
        }
    }

    /**
     * PollForOrderUpdate
     *
     * @return boolean
     */
    function PollForOrderUpdate()
    {
        $strClassName = $this->m_strClassPrefix . 'HardwareBundleSupplier';

        // Should we be doing this?
        switch ($this->GetStatusTag()) {
            case 'not_ordered':
            case 'awaiting_processing':
            case 'rejected':
            case 'cancelled':
                // No. We're in a state that is before updates are valid,
                // so don't bother.
                return false;

                break;
            default:
                // Continue the update
        }

        // Find out what the current status of the order is.
        $Supplier = new $strClassName();

        $recSupplierStatus = $Supplier->FetchLatestOrderStatus($this);

        $strNewStatus = $recSupplierStatus['strStatusTag'];
        $strTrackingOrderNumber = $recSupplierStatus['strTrackingNumber'];

        //Make sure that the order number is not blank
        if ($strTrackingOrderNumber != '') {
            $bolResult = $this->SetTrackingOrderNumber($strTrackingOrderNumber);

            //If the order number has been updated, commit the changes
            if ($bolResult) {
                $this->Commit();
            }
        }

        // Set the invoice number of the items if it's been returned with the status.
        if (isset($recSupplierStatus['strInvoiceNumber'])) {
            $this->SetInvoiceNumber(-1, $recSupplierStatus['strInvoiceNumber']);
        }

        // Uncomment this to make the order update script set them all to dispatched.
        // Don't do this on LIVE!!!!
        //$strSupplierStatus = 'dispatched';

        $strCurrentStatus = $this->GetStatusTag();

        // Make sure we've been returned a sensible status, as we don't
        // want to mess this component up.
        if ($strNewStatus != '') {
            //P14073 - multiple hardware updates
            //Only update the status if it has been changed
            if ($strNewStatus != $strCurrentStatus) {
                // Update the object
                $this->SetStatusTag(-1, $strNewStatus);

                $this->SetWhenLastUpdated();

                // Set the When_Dispatched flag
                $this->SetWhenDispatched();

                // Save the new info
                $binResult = $this->Commit();

                if ($binResult == true) {
                    $contactMessage = "Status of hardware order changed from '$strCurrentStatus' to '$strNewStatus'";

                    // If the new status is 'dispatched' set the component to 'active'
                    if ($strNewStatus == 'dispatched') {
                        userdata_component_set_status($this->m_intComponentID, 'active');
                    }

                    $this->AddContact($contactMessage);
                }
            }
        } else if ($strNewStatus === false) {
            // We were unable to get a reply from the supplier. Not
            // unexpected, so just return false silently.
            return false;
        } else {
            // We shouldn't be here. All replies from the supplier should be recognisable, so we return false. Other
            // code will handle this as though the supplier just didn't reply.
            // DJM: 16/10/2012: error reporting removed as it was generating too much output in the error log
            // As per above, this error condition will be handled elsewhere in the code

            return false;
        }

    } // function PollForOrderUpdate

    /**
     * Actually cancel the order. Must be overridden by the child class.
     *
     * @return void
     */
    function DoCancelOrder()
    {
        report_error(__FILE__, __LINE__, 'HardwareBundle::DoCancelOrder must be overridden!');
    }

    //
    // Utility Functions
    //

    /**
     * Add a contact to the account that the hardware bundle belongs to
     *
     * @param string $strMessage Message
     *
     * @return void
     */
    function AddContact($strMessage)
    {
        global $my_id;

        if ($my_id == '') {
            $strSource = 'Script';
        } else {
            $strSource = 'Internal';
        }

        $rService = $this->GetService();

        $intServiceID = $rService['service_id'];

        tickets_ticket_add($strSource, $intServiceID, 0, 0, 'Closed', $my_id, $strMessage);
    }

    /**
     * Send hardware ordered SMS if necessary
     *
     * @return void
     */
    public function sendOrderedSms()
    {
        if ($this->shouldAccountGetSmsUpdate()) {
            $smsContent = "Hi from Plusnet. Your router has been ordered, it'll arrive within a few days";

            // Decide which SMS to send based on the status of the INTERNET_CONNECTION component
            try {
                $internetConnection = CInternetConnectionProduct::getInternetConnectionProductFromServiceId(
                    $this->GetServiceID()
                );

                if ($internetConnection instanceof CInternetConnectionProduct
                    && $internetConnection->getStatus() == 'queued-activate'
                ) {
                    $smsContent
                        = "We've ordered your router, it'll arrive within a few days. "
                        . "We'll text you again when your broadband is ready, "
                        . "please don't set up your router until then";
                }
            } catch (Exception $e) {
                // Unable to find component - use default SMS
            }

            try {
                $this->sendSms($smsContent);
            } catch (Exception $e) {
                // Customer does not have a mobile number, nothing to do here
            }
        }
    }

    /**
     * Is the account eligible to receive an SMS to confirm their hardware has been dispatched?
     *
     * @return void
     */
    protected function shouldAccountGetSmsUpdate()
    {
        if (CProduct::getServiceCustomerSectorHandle($this->GetServiceID()) != 'CONSUMER') {
            return false;
        }

        $service = $this->GetService();
        if ($service['isp'] != 'plus.net') {
            return false;
        }

        return true;
    }

    /**
     * Sends an SMS to the account that the hardware bundle belongs to
     *
     * @param string $messageContent Content of the SMS
     *
     * @return void
     */
    protected function sendSms($messageContent)
    {
        $texter = new Sms_Texter();
        $texter->send($this->GetServiceID(), $messageContent);
    }

    /**
     * Signals Tr069 device orders. If the hardware bundle is RMA'd, any Tr069 devices on this
     * bundle is marked as deleted.
     *
     * @return void
     */
    public function deleteTr069Devices()
    {
        try
        {
            Db_Manager::setUseMaster(true);

            Tr069_Object::setBusinessActor($this->getBusinessActor());

            $objHardwareClient = BusTier_BusTier::getClient('hardware');
            $objTr069DeviceArray = $objHardwareClient->getTr069HardwareForConfigHardwareBundleId(
                new Int($this->m_intConfigHardwareBundleID)
            );

            foreach ($objTr069DeviceArray as $objTr069Device) {
                if (!$objTr069Device->isRemoved()) {
                    //Remove the Tr069 device from ACS
                    $objTr069Device->delete();
                }
            }
        } catch (Exception $e) {
            error_log("Unable to deleteTr069Devices in HardwareBundle. Exception thrown:: " . print_r($e, true));
        }

        Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);
    }


    /**
     * Create (if required) or Enable a Tr069 Device for this bundle
     *
     * @param String $serialNumber Serial number of the device
     * @param String $ouiNumber    Oui of the device
     *
     * @return void
     */
    protected function configureTr069Device(String $serialNumber, String $ouiNumber)
    {

        Tr069_Object::setBusinessActor($this->getBusinessActor());

        if (!$this->enableTr069Device($serialNumber, $ouiNumber)) {
            $this->createTr069Device($serialNumber, $ouiNumber);

            // Record the serial number if the device is configured to delay RMA.
            if(is_writable(Tr069_ProcessDelayedRMA::TR069_SERIAL_NUMBERS) &&
                in_array($this->getdeviceType ($this->m_intConfigHardwareBundleID), $this->getDeviceTypesWithDelayedRMA())) {
                file_put_contents(Tr069_ProcessDelayedRMA::TR069_SERIAL_NUMBERS, PHP_EOL . $serialNumber, FILE_APPEND);
            }
        }
    }

    /**
     * Look for a device on the account and enable it
     *
     * @param String $serialNumber Serial number of the device
     *
     * @return boolean - enabled?
     */
    protected function enableTr069Device(String $serialNumber,String $ouiNumber)
    {
        $hardwareClient = BusTier_BusTier::getClient('hardware');

        $deviceArray = $hardwareClient->getTr069HardwareForSerialNumber($serialNumber);

        $bolEnabled = false;
        $configHardwareBundleId = new Int($this->m_intConfigHardwareBundleID);

        //Loop through any current devices we've found to delete or enable
        foreach ($deviceArray as $device) {
            //If not for this bundle - delete
            if ($device->getConfigHardwareBundleId() != $configHardwareBundleId) {
                //Remove the Tr069 device from ACS
                $device->delete();
            } elseif (!$device->isEnabled() AND !$device->isRemoved()) {
                //If for this bundle - enable it, if not already enabled and hasn't been marked as removed
                $device->enable(true);

                //We don't need to create one now
                $bolEnabled = true;
            }
        }

        return $bolEnabled;
    }

    /**
     * Update the TR069 Details
     *
     * @param array $arrStatus Status
     *
     * @return void
     */
    public function updateTr069Details($arrStatus)
    {
        $strSerialNumber = empty($arrStatus['strSerialNumber']) ? '' : $arrStatus['strSerialNumber'];
        $strOuiNumber = empty($arrStatus['strOuiNumber']) ? '' : $arrStatus['strOuiNumber'];

        if (!empty($strSerialNumber) AND !empty($strOuiNumber) AND $this->isTr069Compatible()) {
            try {
                Db_Manager::setUseMaster(true);

                $this->setMessage(date('H:i:s')." Configuring Tr069 Device\n");
                $this->configureTr069Device(new String($strSerialNumber), new String($strOuiNumber));
            } catch(Exception $e) {
                $this->setMessage(
                    date('H:i:s')." Unable to enable Tr069 Device in BTHardwareBundle Exception:: " .
                    print_r($e, true)."\n"
                );
                //Invalid Serial Number + Oui, can't update Tr069 device
                error_log("Unable to enable Tr069 Device in BTHardwareBundle. Exception:: " . print_r($e, true));
            }

            Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);
        } else {
            $this->setMessage(
                date('H:i:s')." Either Serial Number or Oui Number is empty OR Tr69 device is not compatible\n"
            );
        }

    }


    /**
     * Get the current Business Actor
     *
     * @throws Exception
     * @return Auth_BusinessActor
     */
    protected function getBusinessActor()
    {
        return Auth_BusinessActor::getActorByExternalUserId(SCRIPT_USER);

        throw new Exception("No current Auth user");
    }

    /**
     * Create a Tr069 Device
     *
     * @param String $serialNumber Serial number of device
     * @param String $ouiNumber    Oui of the device
     *
     * @return void
     */
    protected function createTr069Device(String $serialNumber, String $ouiNumber)
    {
        //No current device for this bundle, we need to create one.
        $deviceIdentifier = new Tr069_DeviceIdentifier(
            $serialNumber,
            $ouiNumber,
            new String($this->getDefaultModelName())
        );

        $device = new Tr069_Device($deviceIdentifier, false, null, new Int($this->m_intConfigHardwareBundleID));

        $arrService = $this->GetService();

        // Get the radius information we need
        $radiusUser = $this->getRadiusUser(new Int($arrService['service_id']));
        $decryptedPassword = $radiusUser->getDecryptedPassword();

        if ($radiusUser instanceof Core_RadiusUser) {

            $deviceAuth = new Tr069_DeviceAuthPN(
                new Val_Username($arrService['username']),
                $decryptedPassword,
                new String($radiusUser->getRealm())
            );

            $device->setDeviceAuth($deviceAuth);
            $device->create(true);
        }
    }

    /**
     * Getter wrapper for Core_RadiusUser
     *
     * @param Int $serviceId Service Id
     *
     * @return Core_RadiusUser
     */
    protected function getRadiusUser(Int $serviceId)
    {
        $radiusUser = Core_RadiusUser::inferRadiusUserByServiceId($serviceId);
        return $radiusUser;
    }

} // class HardwareBundle
