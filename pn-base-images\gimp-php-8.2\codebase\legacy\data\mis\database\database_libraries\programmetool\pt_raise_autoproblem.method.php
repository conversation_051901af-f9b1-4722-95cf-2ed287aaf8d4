<?php
/**
 * File header
 *
 * @package LegacyCodebase
 * <AUTHOR> <<EMAIL>>
 *
 */
require_once '/local/data/mis/database/database_libraries/alert-access.inc';
require_once '/local/data/mis/database/database_libraries/programme-tool-access.inc';

/**
 * Allows scripts to create their own problems, when a problem is detected. The problem definitions are defined
 * in support.pt_auto_problem_definitions. If an open instance of the problem already exists and the auto-problem
 * definition specifies that duplicate problems should not be created, a comment will be added to the existing problem.
 * In addition, if a "comment threshold" is set and this is exceeded, the problem will be reprioritised after the
 * comment is added.
 * Finally, if the existing problem has too many comments on it, a new one will be opened (and the old one left as-is)
 * See the userguide at database-access-common/documentation/howtos/autoproblems-howto.txt for more information
 * NOTE: if the auto-problem definition has a priority set of "-1", the auto-problem will NOT be raised.
 * Function will return one of the following:
 *  <int>: the id of the problem which has been created/updated
 *  -1:    indicates that the auto-problem has not been created as the priority has been set to -1
 *  false: indicates that the system was unable to create the auto-problem: check the error log for details
 *
 * @param str  $name                        the name of the problem type
 * @param str  $subject                     the subject line of the problem
 * @param str  $strDescription              the description of the problem
 * @param str  $strAdditionalComment        (optional) comment to add when appending to an existing problem
 * @param bool $bolForceUnique              (optional) force the system to create a new problem, rather than appending
 * @param int  $intCommentThreshold         (optional) max comments before the problem will be reprioritised
 * @param int  $intCommentThresholdInterval (optional) the time-range (minutes) used to check the comment threshold
 *
 * @return mix
 */

function split_pt_raise_autoproblem(
    $name,
    $subject,
    $strDescription,
    $strAdditionalComment = '',
    $bolForceUnique = false,
    $intCommentThreshold = 0,
    $intCommentThresholdInterval = 0
) {
    if (!defined('PT_STATUS_OPEN')) {
        error_log(__FUNCTION__ . "PT_STATUS_OPEN not defined - has defines.inc been included?");

        return false;
    }

    // The (arbitrary and semi-fuzzy) maximum number of comments which can be added to a single problem
    $maxCommentsPerProblem = 1000;

    // The "minutes of the hour" which the ETA for the problem-fix should be rounded to
    $roundingMinutes = 15;

    // The system user id
    $systemUser = 'zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz';

    // The default fix stage (taken from support.tblProblemFixStage); 5 = To Be Assigned
    $default_fixstage_id = 5;

    // Reformat $subject to ensure consistency across all auto-problems
    $subject = preg_replace('/auto.?problem:\s*/i', '', $subject);
    $subject = "[Auto Problem] $subject";

    $apDefinition = ptra_getDefinition($name);
    if (empty($apDefinition)) {
        error_log(__FUNCTION__ . ": could not find the problem definition for the auto_problem '$name'\n");

        return false;
    };

    if ($apDefinition['priority'] == -1) {
        // No action is to be taken - however, we do log the request in the error log, JIC
        error_log(
            "Request to raise auto-problem {$apDefinition['auto_problem_id']} ($name - $subject) rejected" .
            " as the auto-problem priority has been set to -1."
        );

        // We don't return false as this technically isn't an error...
        return -1;
    }

    // NOTE: billing and prov problems are always treated as a standard P1, rather than an auto-problem
    // (i.e. pt_problem.auto_problem_id is NOT set when the problem is created)
    $bolP1Problem = ptra_checkIfP1Problem($name);
    $problem = ptra_getExistingProblem($name, $subject, $apDefinition['auto_problem_id'], PT_STATUS_OPEN);

    $problem_exists = false;
    $problem_append = false;
    $oldProblemId = 0;

    if (!empty($problem)) {
        $problem_exists = true;
        $oldProblemId = $problem['problem_id'];

        if ($apDefinition['allow_duplicates'] == 'no' && !$bolForceUnique) {
            // If too many comments have been added to the problem, we need to raise a new one (*without* closing the
            // old one), to avoid overloading problems with thousands of comments.  However, we don't want to do
            // this if comments have been recently added, as opening the new problem will cause the "threshold" to
            // be reset, which could result in critical problems being missed...
            $intTotalCommentCount = pt_count_comments('problem', $oldProblemId, $systemUser, '', 1);
            $intNewCommentCount = 0;

            if ($intCommentThreshold > 0) {
                $intNewCommentCount = pt_count_comments(
                    'problem',
                    $oldProblemId,
                    $systemUser,
                    '',
                    1,
                    $intCommentThresholdInterval
                );
            }

            if ($intTotalCommentCount < $maxCommentsPerProblem
                || ($intCommentThreshold > 0 && $intNewCommentCount > 0)
            ) {
                $problem_append = true;
            }

            // If we've hit the threshold limit, then we may need to re-prioritise the problem before adding the comment
            if ($problem_append == true
                && $intCommentThreshold > 0
                && $intNewCommentCount >= $intCommentThreshold
            ) {
                $arrImpact = pt_get_impact_level(
                    pt_get_impact_from_system_and_priority($problem['intSystemId'], $problem['priority_id'])
                );

                if (strtolower($arrImpact['strName']) != "high") {
                    $arrUpdatedProblem = array(
                        'problem_id'  => $oldProblemId,
                        'priority_id' => pt_get_priority($problem['intSystemId'], '3')
                    );

                    pt_problem_write($arrUpdatedProblem);

                    $arrComment = array(
                        'type'         => 'problem',
                        'type_id'      => $oldProblemId,
                        'sub_type'     => 'step',
                        'sub_type_id'  => 5,
                        'comment_body' => "Problem impact has been set to High as $intCommentThreshold new comments " .
                            "have been added in the last $intCommentThresholdInterval minutes.",
                        'user_id'      => $systemUser
                    );

                    pt_comment_create($arrComment);
                }
            }
        }
    }

    // Generate additional information for the description/comment
    $timeData = ptra_calculateEta(time(), $apDefinition['eta_offset_hours'], $roundingMinutes);

    $strProblemDetails = ptra_getProblemDetails(
        $strDescription,
        $strAdditionalComment,
        $timeData,
        $bolP1Problem,
        $oldProblemId,
        $problem_append
    );

    if ($problem_append) {
        $array_comment = array(
            'type'         => 'problem',
            'type_id'      => $oldProblemId,
            'sub_type'     => 'step',
            'sub_type_id'  => 1,
            'comment_body' => $strProblemDetails,
            'user_id'      => $systemUser,
        );

        pt_comment_create($array_comment);

        ptra_reportProblemRaised($oldProblemId, $name, $subject, 'updated/commented on');

        return $oldProblemId;
    }

    $array_problem = array(
        'problem_id'           => 0,
        'problem_name'         => $subject,
        'problem_type'         => 'Incident',
        'problem_description'  => $strProblemDetails,
        'owner_id'             => $apDefinition['recipient_id'],
        'incident_owner_id'    => ProblemTool_Constants::OWNER__INCIDENT_MANAGEMENT_TEAM,
        'entity_owner'         => $apDefinition['entity_owner'],
        'status_id'            => PT_STATUS_OPEN,
        'priority_id'          => $apDefinition['priority'],
        'intSystemId'          => $apDefinition['intSystemId'],
        'highest_priority_id'  => $apDefinition['priority'],
        'created_by'           => ($bolP1Problem ? $apDefinition['recipient_id'] : $systemUser),
        'date_eta'             => $timeData['date_eta'],
        'date_closed'          => '0000-00-00 00:00:00',
        'touched'              => '0000-00-00 00:00:00',
        'problem_group_id'     => 0,
        'associate_project_id' => 0,
        'auto_problem_id'      => ($bolP1Problem ? null : $apDefinition['auto_problem_id']),
        'fixstage_id'          => $default_fixstage_id
    );

    $newProblemId = pt_problem_write($array_problem);

    // If we've moved on from the old problem, note this on both the old and the new problem
    if ($problem_exists && !$problem_append) {
        $arrComment = array(
            'type'         => 'problem',
            'type_id'      => $oldProblemId,
            'sub_type'     => 'step',
            'sub_type_id'  => 5,
            'comment_body' => "SYSTEM: " .
                "max auto-comment count ($maxCommentsPerProblem) exceeded on problem $oldProblemId. " .
                "Problem $newProblemId has been created to track future occurrences of this issue",
            'user_id'      => $systemUser
        );

        pt_comment_create($arrComment);

        $arrComment['type_id'] = $newProblemId;
        pt_comment_create($arrComment);
    }

    $strUrl = ptra_reportProblemRaised($newProblemId, $name, $subject, 'created');

    // Send out alerts to the relevant people (e.g. LCST) for high-priority issues
    // Note that we use the original description in the alert, not the "full debug" version
    // We could/should probably also raise these when an existing problem is escalated: leave for now
    if ($apDefinition['priority'] == 1 || $apDefinition['priority'] == 2) {
        $strText = <<<EOS
<b>Problem ID:</b>          $newProblemId<br><br>
<b>Priority:</b>            {$apDefinition['priority']}<br><br>
<b>Problem Name:</b>        $subject<br><br>
<b>Problem Description:</b> $strDescription <br><br>
<b>Raised By:</b>           System (auto-problem)<br><br>
<b>Owner:</b>               {$apDefinition['entity_owner']}<br><br>
EOS;

        $intAlertTypeId = ($apDefinition['priority'] == 1 ? ALERT_P1_PROBLEM : ALERT_P2_PROBLEM);

        alert_automated_alert_raise($intAlertTypeId, $strText, $strUrl);
    }

    return $newProblemId;
}

/**
 * Simple helper function to check if the auto-problem should be categorised as a P1, rather than as an auto-problem
 *
 * @param str $name the name of the auto-problem
 *
 * @return bool
 */
function ptra_checkIfP1Problem($name)
{
    $bolP1Problem = false;
    $p1List = array('PerformScheduledEventsFailure', 'BillingFailure', 'ADSL_PROV_FATAL_ERR');

    if (in_array($name, $p1List)) {
        return true;
    }

    return false;
}

/**
 * Simple helper function to report that the auto-problem has been raised
 * Returns the URL of the problem
 *
 * @param int $problemId the ID of the problem
 * @param str $name      the name of the auto-problem
 * @param str $subject   the subject applied to the auto-problem
 * @param str $action    used to indicate whether the problem was created or updated
 *
 * @return str
 */
function ptra_reportProblemRaised($problemId, $name, $subject, $action)
{
    $strUrl = "https://workplace.plus.net/programme_tool/problem.html?problem_id=$problemId";

    error_log("Auto-problem $problemId ($name - $subject) has been $action - see $strUrl");

    return $strUrl;
}

/**
 * Simple helper function to get the auto-problem definition
 *
 * @param str $name the name of the auto-problem
 *
 * @return array
 */
function ptra_getDefinition($name)
{
    $apDefinition = array();
    $conn_id = get_named_connection('support');

    $name = addslashes($name);
    $definition_query = "SELECT * FROM pt_auto_problem_definitions WHERE name='$name'";
    $res = mysql_query($definition_query, $conn_id);

    if (mysql_num_rows($res) > 0) {
        $apDefinition = mysql_fetch_array($res, MYSQL_ASSOC);
    };

    return $apDefinition;
}

/**
 * Simple helper function to get the most recently opened problem with the given subject
 * Note that we don't rely solely on the auto-problem ID, as we may wish to rename problems so as to force the
 * raising of a new problem, rather than the addition of a comment to the existing one
 *
 * @param str $name            the name of the auto-problem
 * @param str $subject         the subject of the auto-problem
 * @param int $auto_problem_id the ID of the auto-problem
 * @param int $pt_status_open  the "open" status ID for problems
 *
 * @return array
 */
function ptra_getExistingProblem($name, $subject, $auto_problem_id, $pt_status_open)
{
    $problem = array();
    $conn_id = get_named_connection('support');

    $subject = addslashes($subject);

    // Auto-problems which have been classed as a P1 will not have an auto_problem_id value set.
    // We therefore need to skip this check (at a slight performance cost) for these problems
    $strAutoIdCheck = '';
    if (!ptra_checkIfP1Problem($name)) {
        $strAutoIdCheck = "AND auto_problem_id = $auto_problem_id";
    }

    $problem_query = <<<EOQ
SELECT 
    problem_id,
    priority_id,
    intSystemId
FROM 
    pt_problem
WHERE 
    problem_name = '$subject'
    AND status_id = $pt_status_open
    $strAutoIdCheck
ORDER BY
    problem_id desc
LIMIT 1
EOQ;

    $res = mysql_query($problem_query, $conn_id) or report_error(__FILE__, __LINE__, mysql_error($conn_id));

    if (mysql_num_rows($res) > 0) {
        $problem = mysql_fetch_array($res, MYSQL_ASSOC);
    }

    return $problem;
}

/**
 * Simple helper function to calculate the ETA for fixing the problem
 *
 * @param int $ts_now          the time at which the problem was raised
 * @param int $offset_hours    the time (in hours) allowed
 * @param int $roundingMinutes the time (in minutes) which the ETA should be rounded (down) to
 *
 * @return array
 */
function ptra_calculateEta($ts_now, $offset_hours, $roundingMinutes)
{
    // Calculate the "eta" for the problem - this is rounded to the nearest 15 minutes.
    $offset_seconds = $offset_hours * 3600;
    $rounding_seconds = $roundingMinutes * 60;

    $ts_now = time();
    $ts_eta = $ts_now + $offset_seconds;
    $ts_eta = $ts_eta - ($ts_eta % $rounding_seconds);

    $date_now = date("Y-m-d H:i:s", $ts_now);
    $date_eta = date("Y-m-d H:i:s", $ts_eta);

    $data = array(
        'ts_now'   => $ts_now,
        'ts_eta'   => $ts_eta,
        'date_now' => $date_now,
        'date_eta' => $date_eta,
    );

    return $data;
}

/**
 * Simple helper function to produce debug information which can be included in the problem
 *
 * @param array $timeData a simple array holding datetime and timestamp information for the auto-problem
 *
 * @return string
 */
function ptra_getDebugInformation($timeData)
{
    // Global variables which are needed for the debug generation
    global $argv, $whoami, $service_id, $isp;

    return
        'Host: ' . rtrim(`hostname`) . "\n" .
        'Datestamp: ' . "{$timeData['date_now']} [timestamp: {$timeData['ts_now']}]" . "\n" .
        'Script: ' . (isset($_SERVER['SCRIPT_FILENAME']) ? $_SERVER['SCRIPT_FILENAME'] : $argv[0]) . "\n" .
        'Document root: ' . (isset($_SERVER['DOCUMENT_ROOT']) ? $_SERVER['DOCUMENT_ROOT'] : 'N/A') . "\n" .
        'Query string: ' . (isset($_SERVER['QUERY_STRING']) ? $_SERVER['QUERY_STRING'] : 'N/A') . "\n" .
        'PHP Session ID: ' . (isset($_COOKIE['PHPSESSID']) ? $_COOKIE['PHPSESSID'] : 'N/A') . "\n" .
        'User: ' . (isset($whoami) ? $whoami : 'N/A') . "\n" .
        'Service ID: ' . (isset($service_id) ? $service_id : 'N/A') . "\n" .
        'ISP: ' . (isset($isp) ? $isp : 'N/A');
}

/**
 * Simple helper function to generate the "final" data which will be written to the problem/comment
 *
 * @param str   $strDescription       the description of the auto-problem
 * @param str   $strAdditionalComment Secondary information about the auto-problem
 * @param array $timeData             datetime/timestamp information for the problem
 * @param bool  $bolP1Problem         Indicates that the problem is to be auto-escalated to a P1
 * @param int   $oldProblemId         the id of the existing problem (if any)
 * @param bool  $problem_append       indicates that the details of this issue are being written to an existing problem
 *
 * @return string
 */
function ptra_getProblemDetails(
    $strDescription,
    $strAdditionalComment,
    $timeData,
    $bolP1Problem,
    $oldProblemId,
    $problem_append
) {
    $strCommentCodicil = '';
    $strDescriptionCodicil = '';

    if ($bolP1Problem) {
        $strDescriptionCodicil = "\nNOTE: this does require a call out!";
    }

    if ($oldProblemId) {
        if (empty($strAdditionalComment)) {
            $strAdditionalComment = "THIS HAS HAPPENED AGAIN!\n";
        }

        if ($problem_append) {
            $strCommentCodicil = "NOTE: See previous comments on this problem for further details.";
        } else {
            $strCommentCodicil = "NOTE: See previous problem $oldProblemId for further details -" .
                " this problem is still open and should be closed if no longer required.";
        }
    }

    $strDebugInfo = ptra_getDebugInformation($timeData);

    $strProblemDetails =
        <<<EOS
Description:
$strDescription $strDescriptionCodicil
================================================================================
Comment:
$strAdditionalComment
$strCommentCodicil
================================================================================
Debug information:
$strDebugInfo
================================================================================
EOS;

    return $strProblemDetails;
}
