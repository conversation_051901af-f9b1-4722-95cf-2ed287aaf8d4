<?php
/**
* SoapSession
*
* Class for creating a SOAP session to Java using NuSOAP
* Extend this library when creating new SOAP integration. See Ellacoya for an example.
*
* @package  SoapSession
* <AUTHOR> <<EMAIL>>
* @version  $Id: SoapSession.class.php,v 1.11 2008-09-04 09:02:52 b<PERSON>ton Exp $
* @filesource
*/

// Include NuSOAP library
require_once '/local/data/mis/common_library_functions/common_application_apis/PHP5_NuSOAP/nusoap.php';

// Include CPackage library
require_once '/local/data/mis/database/CoreObjects/CPackage/CPackage.inc';

// Define the Soap Session Manager WSDL address.
configDefineIfMissing('SESSION_MANAGER_WSDL', 'http://session.businesstier.plus.net:8080/session-wsdl-fix/SessionManagerService.wsdl');

class SoapSession extends CPackage
{
    /**
    * @access    private
    * @var    string
    */
    var $m_strPackage = 'Soap Session Manager';

    /**
    * @access    private
    * @var    string
    */
    var $_strAction = '';

    /**
    * @access    private
    * @var    string
    */
    var $_strPartnerDataset;

    /**
    * @access    private
    * @var    object
    */
    var $_objClient;

    /**
    * @access    private
    * @var    array
    */
    var $_arrSession;

    /**
    * SoapSession Constructor
    *
    * Get a session for SOAP interaction for a sub-class
    *
    * <AUTHOR> Spencer" <<EMAIL>>
    * @access    public
    * @param    string    Handle for partner dataset to use
    */
    function SoapSession($strPartnerDataset = null)
    {
        $this->_strPartnerDataset = $strPartnerDataset;
        $this->_objClient = $this->getSoapClient(SESSION_MANAGER_WSDL);

        // Persistent session hack

        switch ($strPartnerDataset) {

            case 'PLUSNET':
            case 'REMOTE_INTERNET':
                // Construct the session that is passed to Java
                $this->_arrSession = self::getScriptUserDetails($strPartnerDataset);
            break;

            default:
                $this->_arrSession = $this->callSoapClient(
                    'getSessionID',
                    array(
                        Auth_ScriptUser::SESSION_MANAGER_USERNAME,
                        Auth_ScriptUser::SESSION_MANAGER_REALM,
                        $strPartnerDataset,
                        Auth_ScriptUser::SESSION_MANAGER_PASSWORD
                    )
                );
        } // switch($strPartnerDataset)
    } // function SoapSession($strPartnerDataset)


    /**
    * Public
    */

    /**
     * Returns ScriptUser details used with SessionManager
     *
     * @access public
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @static
     *
     * @param string $strPartnerDataset (PLUSNET|REMOTE_INTERNET)
     *
     * @return array
     */
    public static function getScriptUserDetails($strPartnerDataset)
    {
        $scriptUser = array();

        try {
            $scriptUser = Auth_ScriptUser::getScriptUserForPartner($strPartnerDataset);
        } catch (Exception $e) {
            // If the above throws the exception then we want to suppress it here and just
            // return the empty array. We write this to the error log too.
            trigger_error($e->getMessage() . '. Returning empty array!', E_USER_WARNING);
        }

        return $scriptUser;
    }

    /**
    * getPartnerDataset
    *
    * Return the partner dataset that is being used
    *
    * <AUTHOR> JOnes" <<EMAIL>>
    * @access    public
    * @return    string
    */
    function getPartnerDataset()
    {
        return $this->_strPartnerDataset;
    }

    /**
    * getSoapSessionClient
    *
    * Return the client object being used for the session
    *
    * <AUTHOR> Jones" <<EMAIL>>
    * @access    public
    * @return    object    'pn_soapclient'
    */
    function getSoapSessionClient()
    {
        return $this->_objClient;
    }

    /**
    * getSessionData
    *
    * Return an array of the session data (in Hungarian notation)
    *
    * <AUTHOR> Jones" <<EMAIL>>
    * @access    public
    * @return    array
    */
    function getSessionData()
    {
        return array('strSessionID'           => $this->_arrSession['sessionID'],
                     'strPartnerDataset'      => $this->_arrSession['partnerDataset'],
                     'strUsername'            => $this->_arrSession['username'],
                     'strAuthenticationRealm' => $this->_arrSession['authenticationRealm'],
                     'strUserType'            => $this->_arrSession['userType'],
                     'intActorID'             => $this->_arrSession['actorID'],
                     'strLocale'              => $this->_arrSession['locale']);
    } // function getSessionData()

    /**
    * eventRaise
    *
    * CPackage Event handler override. Just raises an auto-problem at the moment, but we
    * might want to write to a DB so we can attempt to reissue. Depends on what the
    * actual cause of failure is.
    *
    * <AUTHOR> Jones" <<EMAIL>>
    * @access    public
    * @param    array    Associative array of arguments to place on problem. Will also
    *                   recognise the following 'special' keys:
    *    strFile    The file where the event occurred
    *    intLine    The line number where the event was raised
    *    strProblemGroup    The auto problem group to raise the problem to
    *    strProblemTitle    The auto problem title - to append problem to existing one
    *    strProblemDescription    The problem description
    * @param    integer    Event number to raise (use internal pointer by default)
    * @return    void
    */
    function eventRaise($arrArgsExtra = NULL, $intEvent = NULL)
    {
        $intTotalEvents = count($this->m_arrEvent);

        if (isset($intEvent)) {
            // User supplied event number. Check valid
            if ($intEvent >= 0 && $intEvent <= $intTotalEvents - 1) {
                $intEventIndex = $intEvent;
            } else {
                return false;
            }
        } // if (isset($intEvent))
        else
        {
            // Use internal pointer
            if (($intEventIndex = $this->_nextEvent('RAISE')) === false) {
                return false;
            }
        } // !(if (isset($intEvent)))


        $arrEvent = $this->eventGet($intEventIndex);

        $strVerbose  = "Event Description:\n" . $arrEvent['strMessage'] . "\n\n";

        if (count($arrArgsExtra) > 0) {
            $strVerbose .= "\nExecutor Data:\n";

            foreach ($arrArgsExtra as $strKey => $mxdValue) {
                switch ($strKey) {
                    case 'strFile':
                        $strFile = $mxdValue;
                        break;

                    case 'intLine':
                        $intLine = $mxdValue;
                        break;

                    case 'strProblemGroup':
                        $strProblemGroup = $mxdValue;
                        break;

                    case 'strProblemTitle':
                        $strProblemTitle = $mxdValue;
                        break;

                    case 'strProblemDescription':
                        $strDescription = $mxdValue;
                        break;

                    default:
                        $strVerbose .= "$strKey: $mxdValue\n";
                        break;
                } // switch ($strKey)
            } // foreach ($arrArgs as $strKey => $mxdValue)

            if (isset($strFile)) { $strVerbose .= "File: $strFile\n"; }
            if (isset($intLine)) { $strVerbose .= "Line: $intLine\n"; }

            $strVerbose .= "\n";
        } // if ($count($arrArgsExtra) > 0)

        if (empty($strProblemTitle)) {

            $strProblemTitle = 'SOAP Session Error - ' . $this->m_strPackage;
        }

        if (empty($strDescription)) {

            $strDescription  = 'A SOAP Session Error occurred whilst attempting to interact with the ' . $this->m_strPackage . ".\nPlease ensure that the following information is updated on the correct platform:\n\n";
        }

        $strVerbose .= "Library Data:\n";

        foreach ($arrEvent['arrArgs'] as $strKey => $mxdValue) {
            $strVerbose .= "$strKey: $mxdValue\n";
        }

        $strVerbose .= "Library: " . $arrEvent['strFile'] . "\n" .
                       "Line: " . $arrEvent['intLine'] . "\n";

        // No problem group override.
        if (! isset($strProblemGroup) || $strProblemGroup == '') {
            if (isset($this->m_strProblemGroup)) {
                $strProblemGroup = $this->m_strProblemGroup;
            } else {
                $strProblemGroup = 'undefined';
            }
        }

        pt_raise_autoproblem($strProblemGroup, "AUTO-PROBLEM: " . $strProblemTitle, $strDescription, $strVerbose);

        return true;
    } // function raiseEvent($arrArgs, $strFile, $intLine)



    /**
    * Private
    */

    /**
    * getSoapClient
    *
    * Get a Soap Session client
    *
    * <AUTHOR> Spencer" <<EMAIL>>
    * @modifier    "Rupert Jones" <<EMAIL>>
    * @access    public
    * @param    string    WSDL address for operations
    * @return    object    Soap Session client
    */
    function getSoapClient($strWSDL)
    {
        $this->_strAction = 'GetSoapClient';

        $objClient = new pn_soapclient($strWSDL, true);

        $strError = $objClient->getError();

        if ($strError) {
            $this->eventAdd(array('strMessage' => 'SOAP client construction failed: ' . $strError,
                                  'strWSDL' => $strWSDL),
                            __FILE__, __LINE__);
            return false;
        } // if ($strError)

        return $objClient;
    } // function getSoapClient($strWSDL)

    /**
     * callSoapClientWithRetry
     *
     * The same as callSoapClient() except it allows for N-number of retries
     * with a delay of X-seconds between each failed attempt.
     *
     * @param string  $strCall                    WSDL operation to call
     * @param array   $arrParams                  Array of parameters to pass to WSDL operation
     * @param integer $intRetryCount              Maximum number of retries to attempt before giving up
     * @param integer $intSecsDelayBetweenRetries Delay (in seconds) between each failed attempt
     * @param object  $objClient                  Session Client to perform operation on
     *
     * <AUTHOR> Spencer" <<EMAIL>>
     * @modifier "Rupert Jones" <<EMAIL>>
     * @modifier "Stephen Smith" <<EMAIL>>
     *
     * @return   mixed   Whatever the WSDL operation returns
     */
    public function callSoapClientWithRetry(
        $strCall,
        $arrParams,
        $intRetryCount,
        $intSecsDelayBetweenRetries,
        $objClient = null
    ) {
        $this->_strAction = 'CallSoapClient';

        if (!isset($objClient) || !is_object($objClient)) {

            if (is_object($this->_objClient)) {

                $objClient = $this->_objClient;

            } else {

                $this->eventAdd(
                    array(
                        'strMessage' => "No valid SOAP Session client found for '$strCall'"
                    ),
                    __FILE__,
                    __LINE__
                );

                return false;
            }
        }

        // Check that the retry count/delay retry times are vaguely sensible.
        // Would like to use intval but IMO that function is broken.

        if (!is_numeric($intRetryCount)) {

            $this->eventAdd(
                array(
                    'strMessage' =>
                        "The supplied argument cannot be used as a retry " .
                        "count as it does not coerce into an integer " .
                        "value"
                ),
                __FILE__,
                __LINE__
            );

            return false;
        }

        if ($intRetryCount < 0 || $intRetryCount > 30) {

            $this->eventAdd(
                array(
                    'strMessage' => "Invalid/unsupported retry count ({$intRetryCount})"
                ),
                __FILE__,
                __LINE__
            );

            return false;
        }

        if (!is_numeric($intSecsDelayBetweenRetries)) {

            $this->eventAdd(
                array(
                    'strMessage' =>
                        "The supplied argument cannot be used as a delay " .
                        "in seconds because it does not coerce into an " .
                        "integer value"
                ),
                __FILE__,
                __LINE__
            );

            return false;
        }

        if ($intSecsDelayBetweenRetries < 0 || $intSecsDelayBetweenRetries > 10) {

            $this->eventAdd(
                array(
                    'strMessage' => "Invalid/unsupported seconds delay time ({$intSecsDelayBetweenRetries})"
                ),
                __FILE__,
                __LINE__
            );

            return false;
        }

        // In theory, the maximum wait time is 300 seconds,
        // which should be plenty.

        $errors = array();
        $currAttempt = 0;

        do {

            $unkReturn = $objClient->call($strCall, $arrParams);

            $failure = ($objClient->fault || ! $unkReturn);

            if ($failure) {

                $errors[] = "Fault from SOAP client when calling $strCall: " . $objClient->getError();

                $this->sleep($intSecsDelayBetweenRetries);
            }

            ++$currAttempt;

        } while ($failure && $currAttempt < $intRetryCount);

        if ($failure) {

            if ($intRetryCount == 0) {

                // Was never requested to retry, so we'll only ever have one
                // error message at this point.

                $message = $errors[0];

            } else {

                // We'll have N error messages for each failed attempt, thus
                // loop over them and build up a single error message.

                $message = '';

                for ($i = 1; ($fragment = array_shift($errors)) !== null; ++$i) {

                    $message .= "Attempt {$i}: {$fragment}\n";
                }
            }

            $this->eventAdd(
                array(
                    'strMessage' => $message
                ),
                __FILE__,
                __LINE__
            );

            return false;
        }

        return $unkReturn;
    }

    /**
     * callSoapClient
     *
     * Perform an operation using the client instantiated above (or provide your own)
     *
     * @param string $strCall   WSDL operation to call
     * @param array  $arrParams Array of parameters to pass to WSDL operation
     * @param object $objClient Session Client to perform operation on
     *
     * <AUTHOR> Spencer" <<EMAIL>>
     * @modifier "Rupert Jones" <<EMAIL>>
     * @access   public
     *
     * @return   mixed   Whatever the WSDL operation returns
     */
    function callSoapClient($strCall, $arrParams, $objClient = null)
    {
        return $this->callSoapClientWithRetry($strCall, $arrParams, 0, 0, $objClient);
    }

    /**
     * Wrapper method that abstracts the PHP sleep() command.
     * Useful for unit tests; prevents them from having to actually delay.
     *
     * @param integer $secs Number of seconds to delay for.
     *
     * @return void
     **/
    protected function sleep($secs)
    {
        sleep($secs);
    }
} // class SoapSession

