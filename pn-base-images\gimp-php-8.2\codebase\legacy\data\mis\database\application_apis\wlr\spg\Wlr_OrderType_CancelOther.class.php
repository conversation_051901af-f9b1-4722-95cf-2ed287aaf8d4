<?php
	require_once ('/local/data/mis/database/application_apis/wlr/spg/Wlr_OrderType.class.php');

	require_once (SQL_PRIMITIVES_LIBRARY);

	define ('WLR_CANCELOTHER_NEVER_CONTACTED',      0);
	define ('WLR_CANCELOTHER_MISLEAD',              1);
	define ('WLR_CANCELOTHER_NO_AUTH',              2);
	define ('WLR_CANCELOTHER_PURCHASED_DIFFERENT',  3);
	define ('WLR_CANCELOTHER_FAILURE_TO_CANCEL',    4);
	define ('WLR_CANCELOTHER_LINE_CEASE',           5);
	define ('WLR_CANCELOTHER_INDETERMINATE_REASON', 6);
	define ('WLR_CANCELOTHER_CPS',                  7);

	class Wlr_OrderType_CancelOther extends Wlr_OrderType
	{
		var $intCancelOtherReasonID = WLR_CANCELOTHER_FAILURE_TO_CANCEL;
		var $intCancelOtherOrderKeyID = 0; // This would be taken as the default reason for cancelling if SetCancelReasonID() is not called

////////////////////////////////////////////////////////////////////////////////
//
// Constructor
//

		function Wlr_OrderType_CancelOther($intServiceID, $strPostcode, $strCliNumber, $intComponentID, $bolIsBusiness)
		{
			$this->Wlr_OrderType($intServiceID, $strPostcode, $strCliNumber, $intComponentID, $bolIsBusiness);

			// this member is actually defined in Wlr_OrderType (this class' base class)
			// opted to not have a SetOrderType method to hint that this is an internal
			// structure *NOT* to be tinkered with.

			$this->intOrderType = WLR_ORDERTYPE_CANCEL_OTHER;
		}

////////////////////////////////////////////////////////////////////////////////
//
// Public static methods
//

		/*
			----------------------------
			IsCancelOtherOrderRequired()
			----------------------------
		*/

		function IsCancelOtherOrderRequired($intProductComponentInstanceID)
		{
			$strQuery = "SELECT
			                co.intCancelOtherOrderID
			             FROM
			                dbWLRProvisioning.tblCancelOtherOrder AS co
			             LEFT JOIN dbWLRProvisioning.tblWlrOrder AS o
			                ON o.intWlrOrderId = co.intOrderID
			             LEFT JOIN dbWLRProvisioning.tblWlrOrderHistory AS oh
			                ON oh.intWlrOrderId = o.intWlrOrderId
			             LEFT JOIN dbWLRProvisioning.tblWlrOrderStatus AS os
			                ON oh.intWlrOrderStatusId = os.intWlrOrderStatusId
			             LEFT JOIN
			             (
			                SELECT
			                   MAX(intWlrOrderHistoryId) AS intWlrOrderHistoryId
			                FROM
			                   dbWLRProvisioning.tblWlrOrderHistory AS oh2
			                INNER JOIN dbWLRProvisioning.tblWlrOrder AS o2
			                   ON o2.intWlrOrderId = oh2.intWlrOrderId
			                INNER JOIN dbWLRProvisioning.tblWlrOrderType AS ot2
			                   ON ot2.intWlrOrderTypeId = o2.intWlrOrderTypeId
			                INNER JOIN dbWLRProvisioning.tblOrderFeature AS of2
			                   ON of2.intOrderID = o2.intWlrOrderId
			                WHERE
			                   ot2.vchHandle = 'CANCEL_OTHER'
			                GROUP BY
			                   of2.intProductComponentInstanceId
			             ) AS cancelOtherOrder
			                ON cancelOtherOrder.intWlrOrderHistoryId = oh.intWlrOrderHistoryId
			             WHERE
			                co.intLineRentProductComponentInstanceID = '{$intProductComponentInstanceID}' AND
			                co.bolCustomerChoseToStay=1 AND
			                co.dtmEnd IS NULL AND
			                (co.intOrderID = 0 OR os.vchHandle IN ('BATCH_FAILED', 'RESCHEDULED'))";

			$db = get_named_connection_with_db('wlr_prov_reporting');

			$res = PrimitivesQueryOrExit($strQuery, $db, 'IsCancelOtherOrderRequired', FALSE);

			if (!$res)
			{
				return false;
			}

			$intOrderID = PrimitivesResultGet($res, 'intCancelOtherOrderID');

			return ($intOrderID === false) ? 0 : $intOrderID;
		}



		public static function getPotentialCancelOtherOrder($intProductComponentInstanceID)
		{
			$intProductComponentInstanceID = intval($intProductComponentInstanceID);

			$db = get_named_connection_with_db('wlr_prov_reporting');

			$strQuery = "SELECT * FROM tblCancelOtherOrder coo
			             LEFT JOIN tblWlrOrder o
			             ON o.intWlrOrderId = coo.intOrderID
			             AND (
			               SELECT os.vchHandle
			               FROM tblWlrOrderHistory oh
			               INNER JOIN tblWlrOrderStatus os USING(intWlrOrderStatusId)
			               WHERE oh.intWlrOrderId = o.intWlrOrderId
			               ORDER BY oh.intWlrOrderHistoryId DESC LIMIT 1
			             ) IN ('APPROVED', 'COMPLETED')

			             WHERE coo.intLineRentProductComponentInstanceID = $intProductComponentInstanceID
			             AND o.intWlrOrderId IS NULL
			             AND coo.dtmEnd IS NULL
			             ORDER BY coo.intCancelOtherOrderID DESC LIMIT 1";

			$res = PrimitivesQueryOrExit($strQuery, $db, 'getPotentialCancelOtherOrder', FALSE);

			if(!$res) return FALSE;


			return PrimitivesResultGet($res);
		}



		public static function insertPotentialCancelOtherOrder($intProductComponentInstanceID, $uxtCancelDate)
		{
			$intProductComponentInstanceID = intval($intProductComponentInstanceID);
			$dteCancelDate = date('Y-m-d', $uxtCancelDate);


			$db = get_named_connection_with_db('wlr_prov');


			$strQuery = "INSERT INTO tblCancelOtherOrder
			             (intLineRentProductComponentInstanceID, dteExpectedCancellationDate, dtmAdded)
			             VALUES ($intProductComponentInstanceID, '$dteCancelDate', NOW())";


			return PrimitivesQueryOrExit($strQuery, $db, 'insertCancelOtherOrder', FALSE);
		}



		public static function updatePotentialCancelOtherOrder($intCancelOtherOrderID, $uxtCancelDate)
		{
			$intCancelOtherOrderID = intval($intCancelOtherOrderID);
			$dteCancelDate = date('Y-m-d', $uxtCancelDate);

			$db = get_named_connection_with_db('wlr_prov');

			$strQuery = "UPDATE tblCancelOtherOrder
			             SET dteExpectedCancellationDate = '$dteCancelDate'
			             WHERE intCancelOtherOrderID = $intCancelOtherOrderID";

			return PrimitivesQueryOrExit($strQuery, $db, 'updateCancelOtherOrder', FALSE);
		}



		public static function getLatestCancelOtherEntry($intProductComponentInstanceID)
		{
			$intProductComponentInstanceID = intval($intProductComponentInstanceID);


			$db = get_named_connection_with_db('wlr_prov_reporting');

			$strQuery = "SELECT * FROM tblCancelOtherOrder
			             WHERE intLineRentProductComponentInstanceID = $intProductComponentInstanceID
			             ORDER BY intCancelOtherOrderID DESC LIMIT 1";

			$res = PrimitivesQueryOrExit($strQuery, $db, 'getLatestCancelOtherEntry', FALSE);

			if(!$res) return FALSE;


			return PrimitivesResultGet($res);
		}


////////////////////////////////////////////////////////////////////////////////
//
// Public methods
//

		function SetCancelReasonID($intCancelReasonID)
		{
			$this->intCancelOtherReasonID = $intCancelReasonID;
		}

////////////////////////////////////////////////////////////////////////////////

		function GetCancelReasonID()
		{
			return $this->intCancelOtherReasonID;
		}

////////////////////////////////////////////////////////////////////////////////

		function GetCancelReasonString()
		{
			// these are the possible cancellation reasons:
			// Should this array be located elsewhere instead, or be static?

			$arrCancellationReasons = array(
				WLR_CANCELOTHER_NEVER_CONTACTED      => '9150 SP Requested Cancel Other - Customer has never been contacted',
				WLR_CANCELOTHER_MISLEAD              => '9160 SP Requested Cancel Other - Deliberate attempt to mislead',
				WLR_CANCELOTHER_NO_AUTH              => '9120 SP Requested Cancel Other - No authorisation given to transfer',
				WLR_CANCELOTHER_PURCHASED_DIFFERENT  => '9170 SP Requested Cancel Other - Purchased a different product/service',
				WLR_CANCELOTHER_FAILURE_TO_CANCEL    => '9180 SP Requested Cancel Other - Failure to cancel',
				WLR_CANCELOTHER_LINE_CEASE           => '9140 SP Requested Cancel Other - Line cease',
				WLR_CANCELOTHER_INDETERMINATE_REASON => '9100 SP Requested Cancel Other - Indeterminate reason',
				WLR_CANCELOTHER_CPS                  => '9420 SMC Cancel Other - Cancelled due to CPS'
			);

			return $arrCancellationReasons[$this->intCancelOtherReasonID];
		}

////////////////////////////////////////////////////////////////////////////////

		function SetCancelOtherOrderKeyID($intCancelOtherOrderKeyID)
		{
			$this->intCancelOtherOrderKeyID = $intCancelOtherOrderKeyID;
		}

////////////////////////////////////////////////////////////////////////////////

		function GetCancelOtherOrderKeyID()
		{
			return $this->intCancelOtherOrderKeyID;
		}

////////////////////////////////////////////////////////////////////////////////

		/*
			--------------------------------------
			RecordCancelOtherOrderIDAgainstKeyID()
			--------------------------------------
		*/

		function RecordCancelOtherOrderIDAgainstKeyID()
		{
			$intOrderID = $this->GetOrderID();

			$strQuery = "UPDATE tblCancelOtherOrder
			             SET intOrderID='{$intOrderID}'
			             WHERE intCancelOtherOrderID='{$this->intCancelOtherOrderKeyID}'";

			$db = get_named_connection_with_db('wlr_prov');

			PrimitivesQueryOrExit($strQuery, $db, 'update Cancel Other order table', FALSE);
		}
						
////////////////////////////////////////////////////////////////////////////////

	}
?>
