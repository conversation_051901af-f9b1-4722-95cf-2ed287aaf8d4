<?php
	class Lib_PortalAlerting extends Util_LibrarySplitter 
	{
		protected static $objInstance;

		const CREDIT_CARD_EXPIRED_OR_IS_DUE_TO_EXPIRE = 1;
		const LAST_DIRECT_DEBIT_TRANSACTION_FAILED = 2;
		const LAST_TRANSACTION_FAILED = 3;

		protected function __construct()
		{
			$this->myName = __CLASS__;
			$this->myFile = __FILE__;
		}

		public static function singleton()
		{
			if (!isset(self::$objInstance))
			{
				$strClass = __CLASS__;
				self::$objInstance = new $strClass;
			}

			return self::$objInstance;
		} // public static function singleton()

	} // class Lib_PortalAlerting extends Util_LibrarySplitter 

?>
