<?php


	////////////////////////////////////////////////////////////////////////////////
	//
	// Name:      split_tickets_original_team_id_get
	// Purpose:   get the original team_id of a Question that is on hold
	// Arguments: $ticket_id (The ID of the ticket)
	// Returns:   $original_team_id The original team id of the ticket.
	// See also:  tickets_ticket_contact_add()
	//
	////////////////////////////////////////////////////////////////////////////////

	function split_tickets_original_team_id_get($ticket_id) {

		// Declare variables
		$ticket_id = addslashes($ticket_id);

		if(empty($ticket_id)) {
			error_log(Logging::getCallStackAsString());
			return false;
		}

		$connection = get_named_connection('tickets');

		// Select the team ids and contact ids for Question contacts that are on hold

		$query = 'SELECT  SQL_NO_CACHE  toh.original_team_id, toh.contact_id ' .
		         'FROM     ticket_contacts AS tc, ' .
		                  'tickets_on_hold AS toh ' .
		         'WHERE    toh.when_done IS NULL ' .
             'AND      toh.contact_id = tc.contact_id ' .
		         "AND      tc.ticket_id = '$ticket_id'";

		$result = mysql_query($query, $connection)
			or report_error(__FILE__, __LINE__, mysql_error($connection));

		$number_found = mysql_num_rows($result);

		// If there are no contacts, then the Question is not on hold

		if ($number_found == 0) {

			$original_team_id = false;
		}
		elseif ($number_found == 1) {

			$original_team_id = mysql_result($result, 0, 0);
		}

		elseif ($number_found > 1) {

			$array_contact_ids = array();

			while ($row = mysql_fetch_assoc($result)) {

				$array_contact_ids[] = $row['contact_id'];
			}

			$string_contact_ids = implode(',', $array_contact_ids);

			$original_team_id = mysql_result($result, 0, 0);
			error_log('Error in call to split_tickets_original_team_id_get:' .
			          "There is more than one contact on hold for the ticket: $ticket_id.\n\n" .
			          "\nContact IDs: $string_contact_ids");

		} // end elseif there's an error

		mysql_free_result($result);
		return $original_team_id;

	} // end original_team_id_get

?>
