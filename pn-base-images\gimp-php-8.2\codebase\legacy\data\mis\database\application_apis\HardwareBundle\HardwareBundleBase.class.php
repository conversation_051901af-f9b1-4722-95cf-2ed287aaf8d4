<?php
/**
 * HardwareBundleBase.class.php
 *
 * @package    application_apis
 * @subpackage HardwareBundle
 *
 */

/**
 * HardwareBundleBase class
 *
 * @package    application_apis
 * @subpackage HardwareBundle
 *
 */
class HardwareBundleBase
{
    // The database ID of this object, 0 if the object isn't linked to
    // anything in the database yet
    var $m_intID = 0;

    // true of the data in the class is in sync with the database
    var $m_binInSync = false;

    // true if the system should be run in test mode.
    var $m_binTestMode = false;

    // Holds the numeric ID of the object's supplier (if there is one)
    var $m_intSupplierID = 0;

    // Supplier tag, should be set by sub-class
    var $m_strSupplierTag = false;

    // Stores whether or not the object is valid
    var $m_binIsValid = false;

    // Stores the identifier of the partner (0 for PlusNet)
    var $m_intPartnerID = 0;

    // Static variable to hold supplier ID information
    static $supplierIdCache = null;

    protected $message = '';

    /**
     * Constructor
     *
     * @return void
     */
    function HardwareBundleBase()
    {
        // Set the Partner ID
        if (defined('PARTNER_ID')) {
            $this->m_intPartnerID = PARTNER_ID;
        }

        $this->m_intSupplierID = $this->GetSupplierID();
    }

    /**
     * Are we in test mode?
     *
     * @return boolean
     */
    function IsTestMode()
    {
        return $this->m_binTestMode;
    }

    /**
     * Get the prefix of the current class
     *
     * @return string
     */
    function GetClassPrefix()
    {
        if (isset($this->m_strClassPrefix)) {
            return $this->m_strClassPrefix;
        } else {
            return '';
        }
    }

    /**
     * Is the object's data modified (not in sync with the database)?
     *
     * @return boolean
     */
    function IsModified()
    {
        return !$this->m_binInSync;
    }

    /**
     * Is the object valid?
     *
     * @return boolean
     */
    function IsValid()
    {
        return $this->m_binIsValid;
    }

    /**
     * Mark the data in the mass as having been modified
     *
     * @return void
     */
    function MarkModified()
    {
        $this->m_binInSync = false;
    }

    /**
     * Set the object as being valid
     *
     * @return void
     */
    function SetValid()
    {
        $this->m_binIsValid = true;
    }

    /**
     * Set the object as being invalid
     *
     * @return void
     */
    function SetInvalid()
    {
        $this->m_binIsValid = false;
    }

    /**
     * Set the partner ID of the object (if different to the partner of this Workplace)
     *
     * @param int $intPartnerID Partner Id
     *
     * @return void
     */
    function SetPartnerID($intPartnerID)
    {
        $this->m_intPartnerID = $intPartnerID;
    }

    /**
     * Get the partner ID of the object
     *
     * @return number
     */
    function GetPartnerID()
    {
        return $this->m_intPartnerID;
    }

    /**
     * Mark the object as being up to date with the database
     *
     * @return void
     */
    function MarkUpToDate()
    {
        $this->m_binInSync = true;
    }

    /**
     * Read a file into a string
     *
     * @param string $strFileName Filename
     *
     * @return Ambigous <string, unknown>
     */
    function ReadInFile($strFileName)
    {
        $fpFile = fopen($strFileName, 'r');

        $strOutput = '';

        while (!feof($fpFile)) {
            $strBuffer = fgets($fpFile, 4096);

            $strOutput .= $strBuffer;
        }

        fclose($fpFile);

        return $strOutput;
    }

    /**
     * GetSupplierID
     *
     * @return int
     */
    function GetSupplierID()
    {
        if (empty(self::$supplierIdCache) && !is_array(self::$supplierIdCache)) {
            self::$supplierIdCache = array();
        }

        if ($this->m_intSupplierID == 0) {
            $strSupplierTag = $this->m_strSupplierTag;

            if (!empty($strSupplierTag) && !array_key_exists($strSupplierTag, self::$supplierIdCache)) {
                $conn     = get_named_connection('unprivileged_reporting');
                $strQuery = "SELECT hardware_supplier_id FROM products.hardware_suppliers WHERE tag='$strSupplierTag'";
                $res      = mysql_query($strQuery, $conn) or report_error(__FILE__, __LINE__, mysql_error($conn));

                // We default to zero if the supplier ID cannot be found
                self::$supplierIdCache[$strSupplierTag] = (mysql_num_rows($res) > 0 ? mysql_result($res, 0, 0) : 0);
            }

            $this->m_intSupplierID = self::$supplierIdCache[$strSupplierTag];
        }

        return $this->m_intSupplierID;
    }

    /**
     * Email information about the current object to someone. Includes a
     * dump of SERVER_VARS and the object itself
     *
     * @param string $strRecipient Recipient
     * @param string $strSubject   Subject
     *
     * @return boolean
     */
    function MailMe($strRecipient, $strSubject)
    {

        if ($strRecipient == '') {
            return false;
        }

        $strMessage = "\n\nTHE OBJECT:\n\n" . dump_structure_to_string($this);

        $strMessage .= "\n\nSERVER VARS:\n\n" .dump_structure_to_string($_SERVER);

        mail($strRecipient, $strSubject, $strMessage);

        return true;
    }

    /**
     * Set the message
     *
     * @param string $message message stored
     *
     * @return void
     */
    public function setMessage($message)
    {
        $this->message .= $message;
    }

    /**
     * Get the message
     *
     * @return string
     */
    public function getMessage()
    {
        return $this->message;
    }
}
