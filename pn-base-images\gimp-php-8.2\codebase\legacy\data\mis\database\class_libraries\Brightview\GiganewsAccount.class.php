<?php
require_once 'Account.class.php';

class Brightview_GiganewsAccount extends Brightview_Account
{
	const DEFAULT_NEWS_LIMIT = 10;

	protected $intUsage;
	
	/*
	 * Validation rules
	 * This function is used by save() but can also be used on itself
	 */
	public function isValid() {
		$strUserName = $this->getUserName();
		if (empty($strUserName)) {
			$this->addError("UserName not set");
		}
		if (!array_key_exists($this->getRealm(), self::getRealmPrefixMapping())) {
			$this->addError("Realm unknown: {$this->getRealm()}");
		}
		$strPassword = $this->getPassword();
		if (empty($strPassword)) {
			$this->addError("Password not set");
		}
		if (!($this->getParentId() > 0)) {
			$this->addError("Parent id not set");
		}
		$intNewsLimit = $this->getNewsLimit();
		if (empty($intNewsLimit)) {
			$this->addError("News Limit not set");
		}
		return parent::isValid();
	}
	
	/*
	 * Creates a new giganews account
	 */
	public static function create($intParentId) {
		$objAccount = Brightview_Account::findByMailId($intParentId);
		if(is_object($objAccount)) {
			$objGiganewsAccount = new self(
				array('username' => self::generateUsername($intParentId, self::getNewsRealm($objAccount->getRealm())),
				      'realm' =>	self::getNewsRealm($objAccount->getRealm()),
				      'fullname' => $objAccount->getFullName(),
				      'passwd' => self::generatePassword(),
				      'status' => 'active',
				      'modifydate' => date("Y-m-d H:i:s"),
				      'options' => '',
				      'news_limit' => self::DEFAULT_NEWS_LIMIT,
				      'parentid' => $intParentId));
			if ($objGiganewsAccount->save()) {
				return $objGiganewsAccount;
			}
			else {
				return new Exception("Could not create GiganewsAccount");
			}
		}
		else {
			return new Exception("Account id not found: $intParentId");
		}
	}
	
	/*
	 * generates a 8 char long password where
	 * the first 4 chars are letters
	 * and the last 4 chars are numbers
	 */
	public static function generatePassword() {
		
		$strChars = 'abdefhiknrstyz';
		$strNumbers = '********';
		$intCharsNum = strlen($strChars);
		$intNumbersNum = strlen($strNumbers);
		$strRet = '';
        for ($intIdx = 0; $intIdx < 4; $intIdx++) {
            $strRet .= substr($strChars, rand(1, $intCharsNum) - 1, 1);
        }
        for ($intIdx = 0; $intIdx < 4; $intIdx++) {
            $strRet .= substr($strNumbers, rand(1, $intNumbersNum) - 1, 1);
        }
		
        return $strRet;
	}

	public function getUsage() {
		if ($this->intUsage === NULL) {
			// not loaded, load it from the db
			$strQuery = "SELECT UsageBytes FROM GigaNews.GigaNewsUsage WHERE Username = :username";
			$stmStatement = BVDB::db()->prepare($strQuery);
			$stmStatement->bindValue(':username', strtoupper($this->getUsername()));
			$stmStatement->execute();
			if ($arrRow = $stmStatement->fetch()) {
				$this->intUsage = $arrRow['UsageBytes'];
			}
			// If not found set it to zero
			else {
				$this->intUsage = 0;
			}
			return $this->intUsage;
		}
		return $this->intUsage;
	}
	
	public static function getNewsRealm($strRealm) {
		if ($strRealm != '') {
			return 'news.'.$strRealm;
		}
		else {
			return new Exception("Realm is empty");
		}
	}
	
	private static function generateUsername($intParentId, $strNewsRealm) {
		if ($intParentId > 0 && $strNewsRealm != '' ) {
			
			$strPrefix = self::getPrefixForRealm($strNewsRealm);
			return $strPrefix.$intParentId;
		}
		else {
			return new Exception("Parent id and/or realm not set");
		}
	}
	private static function getPrefixForRealm($strRealm) {
		if ($strRealm != '') {
			
			$arrRealmToPrefix = self::getRealmPrefixMapping();
			if (array_key_exists($strRealm, $arrRealmToPrefix)) {
				return $arrRealmToPrefix[$strRealm];
			}
			else {
				// Return a default prefix for realms we don't know yet
				return 'ab';
			}
		}
		else {
			return new Exception("Realm not set");
		}
	}
	private static function getRealmPrefixMapping() {
		return array('news.madasafish.com' => 'mf',
		             'news.globalnet.co.uk' => 'gi',
		             'news.care4free.net' => 'cf',
		             'news.dialstart.net' => 'ds',
		             'news.easilybroadband.co.uk' => 'eb',
		             'news.freenetname.co.uk' => 'fn',
		             'news.ic24.net' => 'ic',
		             'news.jings.com' => 'jn',
		             'news.totalise.co.uk' => 'tz',
		             'news.waitrose.com' => 'wt',
		             'news.callnetuk.com' => 'cl',
		             'news.icscotland.net' => 'cs',
		             'news.totalserve.co.uk' => 'to',
			     'news.greenbee.net' => 'gb');
	}
}
