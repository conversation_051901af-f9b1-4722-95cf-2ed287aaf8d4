<?php
    /**
    * This file declares the CProductChange class
    *
    * @package    Core
    * @subpackage System Events
    * <AUTHOR>
    * @version    $Id: CProductChange.inc,v 1.7 2009-05-11 04:17:11 fzaki Exp $
    * @filesource
    */
    require_once('/local/data/mis/database/database_libraries/CoreObjects/SystemEvents/CScheduledEvent.inc');

    class CProductChange extends CScheduledEvent
    {
        /**
        * ProductChange ID
        *
        * @var integer
        * @access private
        */
        var $m_intProductChangeID = 0;

        /**
        * Component ID
        *
        * @var integer
        * @access private
        */
        var $m_intComponentID = 0;

        /**
        * New Component ID
        *
        * @var integer
        * @access private
        */
        var $m_intNewComponentID = 0;

        /**
         * Holds the contract end date, so we can keep it the same for product changes
         *
         * @var date
         */
        protected $uxtContractEnd = NULL;


        /**
        * Contructor for the CProductChange class
        *
        * @param  integer The component ID, or leave default for new empty instance
        * <AUTHOR>
        * @return boolean success
        */
        function CProductChange($intProductChangeID=0)
        {
            if($intProductChangeID < 1) {

                return true;
            }

            $dbhConn = get_named_connection_with_db('systemEvents');

            $strQuery = "SELECT pc.intProductChangeID,
                                pc.intComponentID,
                                pc.intNewComponentID,
                                pc.intScheduledEventID,
                                UNIX_TIMESTAMP(pc.uxtContractEnd) AS uxtContractEnd
                           FROM dbSystemEvents.tblProductChange pc
                          WHERE pc.intProductChangeID = '$intProductChangeID'";

            $refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Fetch details for a product change event instance');
            $arrEvent = PrimitivesResultsAsArrayGet($refResult);

            $arrEvent = $arrEvent[0];

            if (!isset($arrEvent['intProductChangeID']) || $arrEvent['intProductChangeID'] != $intProductChangeID) {

                return false;
            }

            $this->m_intProductChangeID = $arrEvent['intProductChangeID'];
            $this->m_intComponentID = $arrEvent['intComponentID'];
            $this->m_intNewComponentID = $arrEvent['intNewComponentID'];
            $this->m_intScheduledEventID = $arrEvent['intScheduledEventID'];
            $this->uxtContractEnd = $arrEvent['uxtContractEnd'];

            //Call parent constructor
            return $this->CScheduledEvent($this->m_intScheduledEventID);
        }


        //////////////
        // Accessors
        //////////////

        /**
        * Return the Component ID
        *
        *
        * @access public
        * <AUTHOR>
        * @return integer Component ID
        */
        function  getComponentID()
        {
            return $this->m_intComponentID;
        }

        /**
        * Return the New Component ID
        *
        *
        * @access public
        * <AUTHOR>
        * @return integer New Component ID
        */
        function  getNewComponentID()
        {
            return $this->m_intNewComponentID;
        }

        /**
         * Getter for the contract end date
         * Allows us to keep a customer on the same contract end date for product changes
         *
         * @access public
         * <AUTHOR>
         * @return date
         */
        public function getContractEndDate()
        {
            return $this->uxtContractEnd;
        }


        ///////////////////
        // Static Methods
        ///////////////////

        /**
        * Create a new CProductChange
        *
        *
        * @access public
        * @static
        * <AUTHOR>
        * @param  integer The id of the component to be changed
        * @param  integer The id of the new component to change to (this should already exists unconfigured)
        * @param  integer Unixtimestamp of the date to change on on
        * @param  boolean Should this even take place on a billing run after the due date or immediately after the due date.
        * @return object The newly created CProductChange object, or false
        */
        function &create($intComponentID, $intNewComponentID, $uxtDateDue=0, $bolOnContractEnd=true, $uxtContractEnd = NULL, $bolHousemove = false)
        {
            if($intComponentID < 1) {
                return false;
            }

            if($intNewComponentID < 1) {
                return false;
            }

            if($uxtDateDue == 0) {
                $uxtDateDue = time();
            }

            $objNewEvent = new CProductChange();

            $objNewEvent->m_intComponentID = $intComponentID;
            $objNewEvent->m_intNewComponentID = $intNewComponentID;
            $objNewEvent->m_uxtDue = $uxtDateDue;
            $objNewEvent->m_bolOnContractEnd = $bolOnContractEnd;
            $objNewEvent->uxtContractEnd = $uxtContractEnd;

            $objComponent = new CProduct($intComponentID);
            $strComponentType=$objComponent->getProductTypeHandle();
            unset($objComponent);
            switch($strComponentType)
            {
                case 'WLR':
                    $objNewEvent->m_intEventTypeID = CScheduledEvent::getEventTypeID('WLR_PRODUCT_CHANGE');
                break;
                default:
                    //it's not generic - it's PlusTalk !!
                    $objNewEvent->m_intEventTypeID = CScheduledEvent::getEventTypeID('PRODUCT_CHANGE');
                break;
            }

            if (!$bolHousemove) {
                $bolSuccess = $objNewEvent->insertEvent();
                if(!$bolSuccess) {
                    return false;
                }

                $bolSuccess = $objNewEvent->insertEventDetails();
                if(!$bolSuccess) {
                    return false;
                }
            }

            $objNewComponent = CComponent::createInstance($intNewComponentID);

            $objEventLogger = $objNewComponent->prvGetEventLogger();
            switch($strComponentType)
            {
                case 'WLR':
                    $objEventLogger->logWlrProductChangeRequest($intNewComponentID);
                break;
                default:
                    $objEventLogger->logPlusTalkProductChangeRequest($intNewComponentID);
                break;
            }


            unset($objEventLogger);
            return $objNewEvent;
        }

        /**
        *
        *
        * @access public
        * @static
        * <AUTHOR>
        * @param  integer The component ID
        * @param  boolean Include Events which are not yet due
        * @param  boolean Include Events which are cancelled
        * @param  boolean Include Events which are completed
        * @return array Existing product change events
        */
        public static function getExistingEvents($intComponentID, $bolIncludePending=false, $bolIncludeCancelled=false, $bolIncludeCompleted=false)
        {
            $dbhConn = get_named_connection_with_db('systemEvents');

            $strQuery = "SELECT pc.intProductChangeID as intProductChangeID,
                                pc.intComponentID,
                                pc.intNewComponentID,
                                IF((se.dtmCreated IS NULL), 0, UNIX_TIMESTAMP(se.dtmCreated)) as uxtCreated,
                                IF((se.dteDue IS NULL), 0, UNIX_TIMESTAMP(se.dteDue)) as uxtDue,
                                se.bolOnContractEnd,
                                IF((se.dtmCompleted IS NULL), 0, UNIX_TIMESTAMP(se.dtmCompleted)) as uxtCompleted,
                                IF((se.dtmCancelled IS NULL), 0, UNIX_TIMESTAMP(se.dtmCancelled)) as uxtCancelled,
                                se.intScheduledEventID
                           FROM tblProductChange pc
                     INNER JOIN tblScheduledEvent se
                             ON pc.intScheduledEventID = se.intScheduledEventID
                          WHERE pc.intComponentID = '$intComponentID'";


            if(!$bolIncludePending) {
                $strQuery .= ' AND se.dteDue <= NOW()';
            }

            if(!$bolIncludeCancelled) {
                $strQuery .= ' AND se.dtmCancelled IS NULL';
            }

            if(!$bolIncludeCompleted) {
                $strQuery .= ' AND se.dtmCompleted IS NULL';
            }

            $strQuery .= ' ORDER BY se.dteDue DESC LIMIT 1';

            $refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Fetch details of existing sceduled product change events');
            $arrEvents = PrimitivesResultsAsArrayGet($refResult, 'intProductChangeID');

            return $arrEvents;
        }

        /**
        * Get ALL product change events, ie not limited by component id
        *
        * For WLR components, we can also pick up deactive components.
        *
        * @access public
        * @static
        * <AUTHOR>
        * @return
        */
        public static function getAllExistingEvents($serviceIds = array())
        {
            $dbhConn = get_named_connection_with_db('systemEvents');

            $clause = '';
            if (!empty($serviceIds)) {
                $clause = 'AND c.service_id IN (' . implode(',', $serviceIds) . ')';
            }
            //'Order By' removed to improve the query performance and forced no cache as this runs once a day only.
            $strQuery = "SELECT SQL_NO_CACHE pc.intProductChangeID as intProductChangeID,
                                pc.intComponentID,
                                pc.intNewComponentID,
                                et.vchHandle as strEventHandle,
                                et.vchName as strEventName,
                                eg.vchHandle as strGroupHandle,
                                eg.vchName as strGroupName,
                                c.service_id,
                                IF((se.dtmCreated IS NULL), 0, UNIX_TIMESTAMP(se.dtmCreated)) as uxtCreated,
                                IF((se.dteDue IS NULL), 0, UNIX_TIMESTAMP(se.dteDue)) as uxtDue,
                                se.bolOnContractEnd,
                                IF((se.dtmCompleted IS NULL), 0, UNIX_TIMESTAMP(se.dtmCompleted)) as uxtCompleted,
                                IF((se.dtmCancelled IS NULL), 0, UNIX_TIMESTAMP(se.dtmCancelled)) as uxtCancelled,
                                ds.serviceid
                           FROM tblProductChange pc
                     INNER JOIN tblScheduledEvent se
                             ON pc.intScheduledEventID = se.intScheduledEventID
                     INNER JOIN dbSystemEvents.tblEventType et
                             ON se.intEventTypeID = et.intEventTypeID
                     INNER JOIN dbSystemEvents.tblEventGroup eg
                             ON et.intEventGroupID = eg.intEventGroupID
                     INNER JOIN userdata.components c
                             ON pc.intComponentID = c.component_id
                     INNER JOIN userdata.components nc
                             ON pc.intNewComponentID = nc.component_id
                     INNER JOIN products.tblServiceComponentProduct scp
                             ON nc.component_type_id = scp.intServiceComponentId
                     INNER JOIN products.tblServiceComponentProductType scpt
                             ON scp.intServiceComponentProductTypeId = scpt.intServiceComponentProductTypeId
                     LEFT JOIN userdata.HPRefreshDataSets ds
                             ON ds.intScheduleEventId = pc.intScheduledEventID
                          WHERE se.dteDue <= NOW()
                            AND se.dtmCancelled IS NULL
                            AND se.dtmCompleted IS NULL
                            AND nc.status NOT IN ('queued-destroy','destroyed')
                            AND (
                                  c.status = 'active'
                                  OR ( scpt.vchHandle = 'WLR' AND c.status IN ('active','deactive'))
                                )
                            {$clause}";

            $refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Fetch details of all due product changes');
            $arrEvents = PrimitivesResultsAsArrayGet($refResult);

            return $arrEvents;
        }


        //////////////////
        // Public Methods
        //////////////////

        /**
        * Insert Event Detials
        *
        * Inserts suplimentary details for this particular event type
        *
        * @access public
        * <AUTHOR>
        * @return boolean success
        */
        function insertEventDetails()
        {
            if(!CScheduledEvent::isValidScheduledEventID($this->m_intScheduledEventID)) {

                return false;
            }

            $dbhConn = get_named_connection_with_db('systemEvents');

            $strQuery = "INSERT INTO dbSystemEvents.tblProductChange
                                 SET intScheduledEventID = '{$this->m_intScheduledEventID}',
                                     intComponentID = '{$this->m_intComponentID}',
                                     intNewComponentID = '{$this->m_intNewComponentID}'";

            if (isset($this->uxtContractEnd)) {

                $strQuery .= ", uxtContractEnd = FROM_UNIXTIME('{$this->uxtContractEnd}')";
            }

            $refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Create a new product scheduled change event');

            $this->m_intProductChangeID = PrimitivesInsertIdGet($dbhConn);

            return ($this->m_intProductChangeID > 0) ? true : false;

        }

        /**
        * Cancel the Scheduled Product Change
        *
        *
        * @access public
        * <AUTHOR>
        * @return boolean success
        */
        function cancel()
        {
            //Cancel the new component.

            if($this->m_intComponentID > 0)
            {
                $objNewComponent = CProduct::createInstance($this->m_intNewComponentID);
                $objNewComponent->destroy();
            }

            return $this->cancelEvent();
        }


        //////////////////
        // Private Methods
        //////////////////
    }
?>
