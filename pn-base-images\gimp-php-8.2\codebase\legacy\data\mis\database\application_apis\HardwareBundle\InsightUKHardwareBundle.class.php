<?php 
	class InsightUKHardwareBundle extends HardwareBundle
	{
		var $m_strClassPrefix = 'InsightUK';
		var $m_strSupplierTag = 'insightuk';

		function InsightUKHardwareBundle()
		{
			$this->HardwareBundle();
		}


		// Handle order placing.
		function PlaceOrder()
		{
			// Just queue it.
			return $this->PlacePendingOrder();
		}


		// Send an order for this hardware bundle.
		function GenerateOrderString()
		{
			// First make sure we have a valid object here.
			if ($this->IsValid() == false)
			{
				return false;
			}

			// Generate a order string and pass to the supplier object to
			// actually place the order (since it knows about FTP servers etc).
			// Some variables needed for the order string
			$strMode          = $this->IsTestMode() == true ? 'T' : 'P';
			$datDate          = date('Ymd');
			$rService         = $this->GetService();
			$rUser            = $this->GetUser();
			$rDeliveryAddress = $this->GetDeliveryAddress();
			$rBillingAddress  = $this->GetBillingAddress();
			$intServiceID     = intval($rService['service_id']);
			$intBundleID      = $this->GetHardwareBundleID();
			$intPartnerID     = $this->GetPartnerID();

			// The first chunk of the order string
			$strOrderString = "PLUS|PLUS123|$strMode|850|$intPartnerID-$intBundleID|DS|$datDate|$datDate|LD|TWODAY";

			// The shipping address details part of the order string
			$strOrderString .= "|{$rUser['forenames']} {$rUser['surname']}";
			$strOrderString .= "|{$rDeliveryAddress['house']} {$rDeliveryAddress['street']}";
			$strOrderString .= "|"; // Deliberately left blank
			$strOrderString .= "|{$rDeliveryAddress['town']}";
			$strOrderString .= "|UK"; // Hard-coded: probably won't need to change
			$strPostCode     = format_postcode($rDeliveryAddress['postcode']);
			$strOrderString .= "|$strPostCode";
			$strOrderString .= "|{$rUser['forenames']} {$rUser['surname']}";

			// The billing address details part of the order string
			$strOrderString .= "|{$rUser['forenames']} {$rUser['surname']}";
			$strOrderString .= "|{$rBillingAddress['house']} {$rBillingAddress['street']}";
			$strOrderString .= "|"; // Deliberately left blank
			$strOrderString .= "|{$rBillingAddress['town']}";
			$strOrderString .= "|UK"; // Hard-coded: probably won't need to change
			$strPostCode     = format_postcode($rBillingAddress['postcode']);
			$strOrderString .= "|$strPostCode";
			$strOrderString .= "|{$rUser['forenames']} {$rUser['surname']}";

			// Output each of the items
			foreach ($this->GetItems() as $intIndex => $rItem)
			{
				$intLineNumber = $intIndex + 1;

				$strOrderString .= "|$intLineNumber";
				$strOrderString .= "|{$rItem['intQuantity']}";

				// The cost is 0.00 because we've already paid for the hardware:
				// in effect this order is in reality a request to dispatch.
				$strOrderString .= "|0.00";
				$strOrderString .= "|{$rItem['strProductCode']}";
			}

			return $strOrderString;

		} // function PlaceOrder()


		// Overridden function
		function DoCancelOrder()
		{
			userdata_component_set_status($this->m_intComponentID, 'destroyed');
		}

	} // InsightUKHardwareBundle
?>