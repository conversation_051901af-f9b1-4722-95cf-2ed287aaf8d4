server: itw
role: slave
rows: multiple
statement:

SELECT 
		s.service_id,
		s.user_id,
		s.isp,
		s.username,
		s.password,
		s.cli_number,
		s.type,
		s.status,
		s.enddate,
		s.next_invoice,
		s.invoice_period,
		s.next_invoice_warned,
		s.invoice_day,
		s.authorised_switch_payment
FROM 
		dbRemoteInternet.accounts a
		INNER JOIN dbRemoteInternet.customers c
			ON c.customer_id = a.customer_id
		INNER JOIN dbRemoteInternet.services s
			ON s.user_id = c.primary_user_id
WHERE
		a.account_id = :intAccountId
