<?php
/**
 * <p>Core_AccountDao</p>
 *
 * Data Access Object operating on <i>userdata.accounts</i> table
 *
 * @see Core_Account
 *
 * @see getAccountDao
 * @see insertAccountDao
 * @see updateAccountDao
 * @see deleteAccountDao
 *
 * @package    Core
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @version    $Id: AccountDao.class.php,v 1.2 2007-09-27 09:44:57 swestcott Exp $
 */
class Core_AccountDao extends Db_Object implements Core_IAccountDao
{
    /**
     * @access protected
     * @var string Primary key name
     */
    protected $strPrimaryKey = 'account_id';
    protected $account_id = null;
    protected $customer_id = null;
    protected $address_id = null;
    protected $balance = null;
    protected $terms = '';
    protected $credit_limit = null;
    protected $vchInvoiceType = null;

    protected $arrSetterFunctions = array(
        'customer_id' => 'CustomerId',
        'address_id' => 'AddressId',
        'balance' => 'Balance',
        'terms' => 'Terms',
        'credit_limit' => 'CreditLimit',
        'vchInvoiceType' => 'InvoiceType'
    );

    public function init()
    {}

    /**
     * Fetch a single row from <i>userdata.accounts</i> table
     *
     * @access public
     * @static
     *
     * @uses Db_Object::getObject()
     *
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @param int    $intAccountId
     * @param string $strTransaction
     *
     * @return Core_AccountDao
     */
    public static function get(
        $intAccountId,
        $strTransaction = Db_Manager::DEFAULT_TRANSACTION
    )
    {
        $strDebugSource = __CLASS__ . ' ' . __METHOD__;
        Dbg_Dbg::write(
            "$strDebugSource: Fetching account details for Id $intAccountId",
            'Core'
        );

        return parent::getObject(get_class(), $intAccountId, $strTransaction);
    }

    /**
     * Returns account ID
     *
     * @access public
     *
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @return int
     */
    public function getAccountId()
    {
        return $this->account_id;
    }

    /**
     * Returns address ID
     *
     * @access public
     *
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @return int
     */
    public function getAddressId()
    {
        return $this->address_id;
    }

    /**
     * Returns customers ID
     *
     * @access public
     *
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @return int
     */
    public function getCustomerId()
    {
        return $this->customer_id;
    }

    /**
     * Returns account balance
     *
     * @access public
     *
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @return float
     */
    public function getBalance()
    {
        return $this->balance;
    }

    /**
     * Returns terms handle
     *
     * @access public
     *
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @return string
     */
    public function getTerms()
    {
        return $this->terms;
    }

    /**
     * Returns credit limit
     *
     * @access public
     *
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @return int
     */
    public function getCreditLimit()
    {
        return $this->credit_limit;
    }

    /**
     * Returns invoice type
     *
     * @return string
     */
    public function getInvoiceType()
    {
        return $this->vchInvoiceType;
    }

    /**
     * Sets account ID
     *
     * @access public
     *
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @param int $intAccountId
     */
    public function setAccountId($intAccountId)
    {
        $this->account_id = $intAccountId;
    }

    /**
     * Sets customer ID
     *
     * @access public
     *
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @param int $intCustomerId
     */
    public function setCustomerId($intCustomerId)
    {
        $this->customer_id = $intCustomerId;
    }

    /**
     * Sets address ID
     *
     * @access public
     *
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @param int $intAddressId
     */
    public function setAddressId($intAddressId)
    {
        $this->address_id = $intAddressId;
    }

    /**
     * Sets account balance
     *
     * @access public
     *
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @param float $floBalance
     */
    public function setBalance($floBalance)
    {
        $this->balance = $floBalance;
    }

    /**
     * Sets terms string
     *
     * @access public
     *
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @param string $strTerms
     */
    public function setTerms($strTerms)
    {
        $this->terms = $strTerms;
    }

    /**
     * Sets credit limit
     *
     * @access public
     *
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @param int $intCreditLimit
     */
    public function setCreditLimit($intCreditLimit)
    {
        $this->credit_limit = $intCreditLimit;
    }

    /**
     * Sets invoice type
     *
     * @param string $invoiceType
     */
    public function setInvoiceType($invoiceType)
    {
        $this->vchInvoiceType = $invoiceType;
    }
}
