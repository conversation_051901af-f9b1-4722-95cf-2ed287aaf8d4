<?php
///////////////////////////////////////////////////////////////////////////
// Encapsulates a hardware bundle type
//

class HardwareBundleType extends HardwareBundleBase
{
	var $m_strInformationPage       = '';
	var $m_intComponentTypeID       = 0;
	var $m_intBundleConfigID        = 0;
	var $m_arrDefaultItems          = array();
	var $m_intDefaultStartingStatus = 0;

	function HardwareBundleType()
	{
		// Call base constructor
		$this->HardwareBundleBase();
	}


	//
	// Set up functions
	//

	// Initialise this object by the component type passed
	function GetByComponentType($intComponentTypeID)
	{
		$hConnection = get_named_connection('unprivileged_reporting');

		// First get the bundle type itself
		$strQuery = 'SELECT sc.name, chbc.information_page, ' .
		                  ' chbc.component_hardware_bundle_config_id ' .
		              'FROM products.component_hardware_bundle_config AS chbc, ' .
		                  ' products.service_components AS sc ' .
		             'WHERE chbc.service_component_id=sc.service_component_id ' .
		               "AND sc.service_component_id='$intComponentTypeID'";
		$hResult = mysql_query($strQuery, $hConnection)
			or report_error(__FILE__, __LINE__, mysql_error($hConnection));

		// Check that we got something back.
		if (mysql_num_rows($hResult) == 0)
		{
			// No config found. Return an error
			mysql_free_result($hResult);

			$this->SetInvalid();

			return false;
		}

		$arrRow = mysql_fetch_assoc($hResult);
		mysql_free_result($hResult);

		$intBundleConfigID          = $arrRow['component_hardware_bundle_config_id'];
		$this->m_strName            = $arrRow['name'];
		$this->m_intBundleConfigID  = $intBundleConfigID;
		$this->m_strInformationPage = $arrRow['information_page'];
		// Now get the items
		$strQuery = 'SELECT hsp.hardware_supplier_product_id, hbci.default_quantity, ' .
		                  ' hsp.display_name, hsp.product_code, hsp.hardware_supplier_id, ' .
				' hs.starting_hardware_order_status_id, ' .
				' (hsp.curCostToCustomerIncVatPence * hbci.default_quantity) AS intCostToCustomer, '.
				' hsp.tag, ' .
				' hsp.curCostToCustomerIncVatPence as intItemCost ' .  
		              'FROM products.hardware_bundle_config_items AS hbci, ' .
		                  ' products.hardware_supplier_products AS hsp, ' .
		                  ' products.hardware_suppliers AS hs ' .
		             'WHERE hsp.hardware_supplier_product_id=hbci.hardware_supplier_product_id ' .
		               'AND hsp.hardware_supplier_id=hs.hardware_supplier_id ' .
		               'AND (hbci.when_removed IS NULL OR hbci.when_removed > NOW()) '.
		               "AND hbci.component_hardware_bundle_config_id='$intBundleConfigID'";
		$hResult = mysql_query($strQuery, $hConnection)
			or report_error(__FILE__, __LINE__, mysql_error($hConnection));

		while ($rRow = mysql_fetch_assoc($hResult))
		{
			$rItem = array('intSupplierProductID'  => $rRow['hardware_supplier_product_id'],
			               'intDefaultQuantity'    => $rRow['default_quantity'],
			               'strDisplayName'        => $rRow['display_name'],
			               'strProductCode'        => $rRow['product_code'],
			               'intHardwareSupplierID' => $rRow['hardware_supplier_id'],
						   'intStartingStatusID'   => $rRow['starting_hardware_order_status_id'],
			               'intCostToCustomer'     => $rRow['intCostToCustomer'],
			               'strTag'     => $rRow['tag'],
			               'intItemCost'=>$rRow['intItemCost']
			               );

			$this->m_arrDefaultItems[] = $rItem;
		}

		// Some final bits of data
		$this->m_intID              = $intBundleConfigID;
		$this->m_intComponentTypeID = $intComponentTypeID;

		// Mark the object as being in sync
		$this->m_binInSync = true;

		return true;

	} // function GetByComponentType($intComponentTypeID)


	//
	// Get Functions
	//

	function GetComponentType()
	{
		return $this->m_intComponentTypeID;
	}


	// Get the items associated with this bundle
	function GetDefaultItems()
	{
		return $this->m_arrDefaultItems;
	}


	// Returns the number of items provided by default in this bundle type
	function GetDefaultItemCount()
	{
		$intCount = 0;

		foreach ($this->m_arrDefaultItems as $rItem)
		{
			$intCount += $rItem['intDefaultQuantity'];
		}

		return $intCount;
	}

	function GetName()
	{
		return $this->m_strName;

	}

	// Get cost to customer
	function GetCostToCustomer()
	{
		$intTotalCost = 0;

		foreach($this->m_arrDefaultItems as $arrItem)
		{
			$intTotalCost += $arrItem['intCostToCustomer'];
		}

		return number_format($intTotalCost/100, 2);

	} // function GetCostToCustomer()

} // class HardwareType
?>
