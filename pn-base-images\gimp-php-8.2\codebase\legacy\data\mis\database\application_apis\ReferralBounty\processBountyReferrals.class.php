<?php

/**
 * Class processBountyReferrals
 *
 * <AUTHOR> <<EMAIL>>
 * most functions were created by <PERSON><PERSON> <mja<PERSON><PERSON><PERSON>@plus.net>
 * @version $Id: processBountyReferrals.class.php,v 1.10 2008-07-16 13:09:47 ablagnys Exp $
 */


	require_once SQL_PRIMITIVES_LIBRARY;
	require_once TICKETS_ACCESS_LIBRARY;
	require_once USERDATA_ACCESS_LIBRARY;
	require_once SMARTY_LIBRARY;
	require_once '/local/data/mis/database/class_libraries/C_Refunds_Manager.php';
	require_once '/local/data/mis/database/application_apis/Referrals/Referrals.class.php';



	/**
	 * class processBountyReferrals
	 *
	 * <AUTHOR> <<EMAIL>>
	 * @version $id$
	 */
	abstract class processBountyReferrals
	{


		/**
		 * intMinDays
		 * amount of days which have to pass since account activation for
		 * referral to be awarded witha a bounty
		*
		 * @const int
		 * @access private
		 */
		var $intMinDays;


		/**
		 * intDaysBeforeWarning
		 * number of days after which unprocessed referral is
		 * treated as serious failure
		 *
		 * @const int
		 * @access private
		 */
		const intDaysBeforeWarning = 5;

		/**
		 * dteOfferEndDate
		 * endind date of promotion
		 * if set to NULL, will be be treated as valid indefinitely
		 *
		 * @var date
		 * @access private
		 */
		var $dteOfferEndDate;

		/**
		 * intSpecialOfferId
		 * Special Offer Id
		 *
		 * @var integer
		 * @access private
		 */
		var $intSpecialOfferId;

		/**
		 * intSpecialOfferTypeId
		 * Type id of the special offer
		 *
		 * @var integer
		 * @access private
		 */
		var $intSpecialOfferTypeId;

		/**
		 * intServiceId
		 * Service Id - if only processing one Service Id
		 *
		 * @var integer
		 * @access private
		 */
		var $intServiceId;

		/**
		 * dteOfferStartDate
		 * start date of promotion
		 *
		 * @var date
		 * @access private
		 */
		var $dteOfferStartDate;

		/**
		 * strPayMethod
		 *
		 * @var string
		 * @access public
		 */
		var $strPayMethod;

		/**
		 * arrErrors
		 *
		 * @var array
		 * @access private
		 */
		var $arrErrors = array();

		/**
		 * objSmarty
		 *
		 * @var object
		 * @access private
		 */
		var $objSmarty;

		/**
		 * arrReport
		 *
		 * @var array
		 * @access public
		 */
		var $arrReport = array();

		/**
		 * intReferralsProcessed
		 *
		 * @var integer
		 * @access public
		 */
		var $intReferralsProcessed = 0;

		/**
		 * intFailed
		 *
		 * @var integer
		 * @access public
		 */
		var $intFailed = 0;

		/**
		 * bolLive - running in live mode or test mode
		 *
		 * @var bool
		 * @access public
		 */
		var $bolLive = false;

		/**
		 * arrFailed
		 *
		 * @var array
		 * @access public
		 */
		var $arrFailed = array();

		/**
		 * intSkipped
		 *
		 * @var integer
		 * @access public
		 */
		var $intSkipped = 0;

		/**
		 * strTemplateFile
		 *
		 * @var string
		 * @access public
		 */
		var $strTemplateFile;


		const PAY_NO_ONE = 0;
		const PAY_REFERRER = 1;
		const PAY_REFERRED = 2;


		/**
		 * __construct
		 * Initializes variables
		 *
		 * @param int $intSpecialOfferId, int $intServiceId
		 * @access public
		 * @return void
		 */
		function __construct($intSpecialOfferId=0, $intServiceId=0)
		{
			if($intSpecialOfferId > 0)
			{
				$this->setValues($intSpecialOfferId);
				$this->intServiceId = $intServiceId;
			}
		}

		/**
		 * addReferral
		 *
		 * @param mixed $intReferredServiceId
		 * @param mixed $intReferrerServiceId
		 * @static
		 * @access public
		 * @return void
		 */
		static function addReferral($intReferredServiceId, $intReferrerServiceId)
		{
			$dbhConn = get_named_connection_with_db('userdata');

			$intReferrerServiceId = (int)$intReferrerServiceId;
			$intReferredServiceId = (int)$intReferredServiceId;

			$strQuery = "
				INSERT INTO userdata.tblBountyReferrals
				(intReferrerServiceId, intReferredServiceId, dtmReferredDate)
				VALUES
				('{$intReferrerServiceId}', '{$intReferredServiceId}', NOW())";

			PrimitivesQueryOrExit($strQuery, $dbhConn);
		}



		/**
		 * Associates a referred account with a different referrer
		 *
		 * @access public
		 * @static
		 * @param integer $intReferredServiceId - service ID of the account that was referred
		 * @param integer $intCurrentReferrerServiceId - the service ID of the referrer that currently
		 *                                               referrers the intReferredServiceId service ID
		 * @param integer $intNewReferrerServiceId - service ID of the (new) referrer that should be used
		 * @return boolean
		 */

		public static function setNewReferrer($intReferredServiceId, $intCurrentReferrerServiceId, $intNewReferrerServiceId)
		{
			if (!ctype_digit($intReferredServiceId) ||
			    !ctype_digit($intCurrentReferrerServiceId) ||
			    !ctype_digit($intNewReferrerServiceId)) {

				return FALSE;
			}


			$dbhConn = get_named_connection_with_db('userdata');

			if (FALSE == $dbhConn) {

				return FALSE;
			}


			// OK, we know they're numerical, but it's good practice, right? :)

			$intReferredServiceId = PrimitivesRealEscapeString($intReferredServiceId, $dbhConn);
			$intCurrentReferrerServiceId = PrimitivesRealEscapeString($intCurrentReferrerServiceId, $dbhConn);
			$intNewReferrerServiceId = PrimitivesRealEscapeString($intNewReferrerServiceId, $dbhConn);

			$strQuery = "UPDATE
			                tblBountyReferrals
			             SET
			                intReferrerServiceId = '{$intNewReferrerServiceId}'
			             WHERE
			                intReferrerServiceId = '{$intCurrentReferrerServiceId}'
			                AND intReferredServiceId = '{$intReferredServiceId}'";


			if (FALSE == PrimitivesQueryOrExit($strQuery, $dbhConn, __METHOD__, FALSE)) {

				return FALSE;
			}


			return TRUE;
		}



		/**
		 * setIsLive
		 * Sets the value of $this->bolLive
		 *
		 * @param bool $bolLive
		 * @access public
		 * @return void
		 */
		function setBolLive($bolLive)
		{
			$this->bolLive = $bolLive;
		}

		/**
		 * getSpecialOffers
		 * Gets all special offers with the given Type and if now is between dteOfferStartDay and dteOfferEndDay + intMinDays
		 *
		 * @param var $strSpecialOfferType
		 * @access public
		 * @return array - all offers intSpecialOfferId which matched the criteria
		 */
		function getSpecialOffers($strSpecialOfferType)
		{
			$dbhConnection = get_named_connection_with_db('userdata_reporting');
			$strSpecialOfferType = mysql_real_escape_string($strSpecialOfferType, $dbhConnection['handle']);
			$strQuery = "
    SELECT intSpecialOfferId FROM products.tblSpecialOffer so
INNER JOIN products.tblSpecialOfferType sot
        ON sot.intSpecialOfferTypeId = so.intSpecialOfferTypeId
     WHERE sot.vchSpecialOfferType = '{$strSpecialOfferType}'
       AND ((NOW() <= DATE_ADD(dtmOfferEndDate, INTERVAL intMinDays DAY)) OR dtmOfferEndDate IS NULL)
       AND NOW() >= dtmOfferStartDate";

			$resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection, 'query failed on ' . __METHOD__, false);
			$arrValues = PrimitivesResultsAsArrayGet($resResult);
			return $arrValues;
		}

		/**
		 * setValues
		 * Sets all values using data from database
		 *
		 * @param int $intSpecialOfferId
		 * @access protected
		 * @return void
		 */
		protected function setValues($intSpecialOfferId)
		{
			$dbhConnection = get_named_connection_with_db('userdata_reporting');
			$intSpecialOfferId = mysql_real_escape_string($intSpecialOfferId, $dbhConnection['handle']);
			$strQuery = '	SELECT intSpecialOfferId, intSpecialOfferTypeId, intMinDays, dtmOfferEndDate, dtmOfferStartDate ' .
			            '		FROM products.tblSpecialOffer ' .
			            "		WHERE intSpecialOfferId = '$intSpecialOfferId' LIMIT 1";
			$resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection, 'query failed on', false);
			$arrValues = PrimitivesResultsAsArrayGet($resResult);
			if(count($arrValues) == 1)
			{
				$this->intSpecialOfferId = $arrValues[0]['intSpecialOfferId'];
				$this->intSpecialOfferTypeId = $arrValues[0]['intSpecialOfferTypeId'];
				$this->intMinDays = $arrValues[0]['intMinDays'];
				$this->dteOfferEndDate = $arrValues[0]['dtmOfferEndDate'];
				$this->dteOfferStartDate = $arrValues[0]['dtmOfferStartDate'];
			} else
			{
				return false;
			}
		}

		/**
		 * markReferralProcessed
		 * marks referral as processed to prevent it being processed more than one time
		 *
		 * @param int $intReferralUid
		 * @access private
		 * @return bool true on success, false otherwise
		 */
		protected function markReferralProcessed($intReferralUid)
		{

			$dbhConnection = get_named_connection_with_db('userdata');

			$intSpecialOfferId = mysql_real_escape_string($this->intSpecialOfferId, $dbhConnection['handle']);
			$intReferralUid = mysql_real_escape_string($intReferralUid, $dbhConnection['handle']);

			$strQuery = ' INSERT INTO userdata.tblReferralsProcessed ' .
			            '             (intBountyReferralId, dtmDateProcessed, intSpecialOfferId) ' .
			            '      VALUES (\'' . $intReferralUid . '\', now(), \'' . $intSpecialOfferId. '\') ';

			$resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection, 'query failed on');
			$intNumRows = PrimitivesAffectedRowsGet($dbhConnection);

			if(1 !== $intNumRows)
			{

				$strErrorText = 'None or more than one entry inserted into database ' .
				                'when marking referral ' . $intReferralUid . ' as processed. ' .
				                'Number of inserted rows: ' . $intNumRows . "\n";

				report_error(__FILE__, __LINE__, $strErrorText . "\nSQL query:\n" . $strQuery);

				return false;
			} elseif($intNumRows > 0)
			{

				return true;
			}
		} //  function markReferralProcessed


		/**
		 * findLastInvoiceForSid
		 *
		 * @param int $intServiceId
		 * @access private
		 * @return mixed invoice id on success, false otherwise
		 */
		protected function findLastInvoiceForSid($intServiceId)
		{

			$dbhConnection = get_named_connection_with_db('financial_reporting');
			$intServiceId = mysql_real_escape_string($intServiceId, $dbhConnection['handle']);
			$strQuery = 'SELECT max(si.sales_invoice_id) intInvoiceId ' .
			        '  FROM financial.sales_invoices si ' .
			        ' INNER JOIN userdata.accounts ua using(account_id) ' .
			        ' INNER JOIN userdata.users uu using(customer_id) ' .
			        ' INNER JOIN userdata.services us ON uu.user_id = us.user_id ' .
			        ' INNER JOIN financial.sales_receipts fsr ON si.sales_invoice_id = fsr.sales_invoice_id ' .
			        ' INNER JOIN transactions.direct_debit_transactions ddt '.
			        '   ON ddt.invoice_id = si.sales_invoice_id '.
			        ' WHERE us.service_id = \'' . $intServiceId . '\' '.
			        '   AND (si.invoice_status = \'fully_paid\') ' .
			        '   AND original_invoice_id IS NULL ';
			$resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection, 'query failed on', false);
			$arrInvoiceId = PrimitivesResultsAsArrayGet($resResult);

			$intInvoiceId = $arrInvoiceId[0]['intInvoiceId'];

			if($intInvoiceId > 0 && $intInvoiceId != 'NULL')
			{
				return $intInvoiceId;
			} else
			{
				return false;
			}

		} // function findLastInvoiceForSid


		/**
		 * findRefundValueForSid - has to be defined in Child
		 *
		 * @param int $intServiceId
		 * @access protected
		 * @return mixed refund value on success, false on failure
		 */

		protected function findRefundValueForSid($intServiceId)
		{
			error_log('Child class must override findRefundValueForSid');
		}

		/**
		 * addReferralNoteToSid
		 *
		 * @param int $intBusinessRuleAction
		 * @param int $intReferrerServiceId
		 * @param flo $floRefundValue
		 * @access protected
		 * @return mixed added note ID on succes, false on failure
		 */
		protected function addReferralNoteToSid($intBusinessRuleAction, $intReferrerServiceId, $floRefundValue)
		{
			$arrVispData = UserdataGetVispConfigForServiceId($intReferrerServiceId);

			$this->initiateSmarty();


			// there is a slight wording difference about monthly referrals for non-MAAF accounts, hence
			// why we have two different blocks of text now

			$strTemplateHandle = userdataIsMaafUser($intReferrerServiceId) ? 'ReferralNoteMaaf' : 'ReferralNote';

			$this->objSmarty->assign('strTemplateHandle', $strTemplateHandle);
			$this->objSmarty->assign('decValue', $floRefundValue);
			$this->objSmarty->assign('brand', $arrVispData['short_name']);
			$this->objSmarty->assign('strPayMethod', $this->strPayMethod);
			$this->objSmarty->assign('portal_main_page_url', $arrVispData['portal_main_page_url']);

			$this->objSmarty->assign('bolPayReferrer', ($intBusinessRuleAction == self::PAY_REFERRER));
			$this->objSmarty->assign('bolPayReferred', ($intBusinessRuleAction == self::PAY_REFERRED));

			$strNoteText = addslashes($this->objSmarty->fetch($this->strTemplateFile));

			$objServiceNotice = CServiceNotice::Create();

			$arrServiceNoticeType = CServiceNoticeType::Retrieve('Script');

			$arrDetails = array('intServiceNoticeTypeID' => $arrServiceNoticeType[0]['intServiceNoticeTypeID'],
			                    'intServiceID' => $intReferrerServiceId,
			                    'strBody' => $strNoteText,
			                    'strActionerID' => SCRIPT_USER,
			                    'strDateRaised' => date('Y-m-d H:i:s'));

			$objServiceNotice->SetAll($arrDetails);

			$objServiceNotice->Save();

			if($objServiceNotice->GetID() > 0)
			{
				return $objServiceNotice->GetID();
			} else
			{
				return false;
			}
		}

		/**
		 * refundByDirectDebit
		 * refunds $floRefundValue to invoice $intInvoiceId by Direct Debit
		 *
		 * @param int $intServiceId
		 * @param int $intInvoiceId
		 * @param flo $floRefundValue
		 * @param int $intRefereeServiceId
		 * @access private
		 * @return bool true on success, false on failure
		 */
		protected function refundByDirectDebit($intBusinessRuleAction, $intServiceId, $intInvoiceId, $floRefundValue, $intRefereeServiceId)
		{
			$this->initiateSmarty();
			$strRefundReasonConfirm = 'Other';
			$arrCustomerDetails = userdata_service_get($intRefereeServiceId);

			$this->objSmarty->assign('strTemplateHandle', 'RefundReason');
			$this->objSmarty->assign('customer_name', $arrCustomerDetails['username']);

			$this->objSmarty->assign('bolPayReferrer', ($intBusinessRuleAction == self::PAY_REFERRER));
			$this->objSmarty->assign('bolPayReferred', ($intBusinessRuleAction == self::PAY_REFERRED));

			$strRefundReasonOther = $this->objSmarty->fetch($this->strTemplateFile);

			$strRefundType = 'referralbounty';

			$objRefundManager = new C_Refunds_Manager($intInvoiceId, SCRIPT_USER, $intServiceId);

			//setting max amount for the script to refund - 500 pounds
			$objRefundManager->setMaxAmount(500);
			$objRefundManager->setRefundAllowed(true);

			$bolHasRefundSucceeded = $objRefundManager->processRefund($floRefundValue,
			                                                          $strRefundReasonConfirm,
			                                                          $strRefundReasonOther,
			                                                          $strRefundType
			                                                         );


			if($bolHasRefundSucceeded)
			{
				$this->strPayMethod = 'DD';
				return true;
			} else
			{
				echo $objRefundManager->getErrors();
				$this->arrErrors[] = $objRefundManager->getErrors();
				return false;
			}
		} // protected function refundByDirectDebit($intBusinessRuleAction, $intServiceId, $intInvoiceId, $floRefundValue, $intRefereeServiceId)



		/**
		 * Minor refactoring of refundByCheque() as I don't know what changes are going
		 * to take place and don't want to clutter refundByCheque() with any logic not
		 * specific to actually refunding by cheque. (which is just a ticket really)
		 * @access private
		 * @return string
		 */

		private function generateRefundTicketText($intBusinessRuleAction, $floRefundValue, $intInvoiceId, $intServiceId, $intRefereeServiceId)
		{
			$this->initiateSmarty();

			$arrRefereeDetails = userdata_service_get($intServiceId);
			$arrCustomerDetails = userdata_service_get($intRefereeServiceId);

			if($intInvoiceId > 0)
			{
				$arrInvoiceDetails = financial_sales_invoice_get($intInvoiceId);
			} else
			{
				$arrInvoiceDetails = array();
			}


			$this->objSmarty->assign('strTemplateHandle', 'RefundTicket');
			$this->objSmarty->assign('decValue', $floRefundValue);
			$this->objSmarty->assign('intInvoiceId', $intInvoiceId);
			$this->objSmarty->assign('strPayMethod', $this->strPayMethod);
			$this->objSmarty->assign('strInvoiceDesc', $arrInvoiceDetails['invoice_desc']);
			$this->objSmarty->assign('strRefereeUsername', $arrRefereeDetails['username']);
			$this->objSmarty->assign('strReferredUsername', $arrCustomerDetails['username']);

			$this->objSmarty->assign('bolPayReferrer', ($intBusinessRuleAction == self::PAY_REFERRER));
			$this->objSmarty->assign('bolPayReferred', ($intBusinessRuleAction == self::PAY_REFERRED));

			return $this->objSmarty->fetch($this->strTemplateFile);
		}

		/**
		 * refundByBacs
		 * Writes refund to table so monthly script may pick it up and refund it by BACS
		 *
		 * @param int $intServiceId
		 * @param int $intInvoiceId
		 * @param flo $floRefundValue
		 * @param int $intRefereeServiceId
		 * @access private
		 * @return bool true on success, false on failure
		 */
		protected function refundByBacs($intBusinessRuleAction, $intServiceId, $intInvoiceId, $floRefundValue, $intRefereeServiceId)
		{
			$objReferrals = new Referrals($intServiceId);

			$intReferralCreditValue = $floRefundValue * 100;
			if ($objReferrals->addReferralCredit($intInvoiceId, $intReferralCreditValue))
			{
				// We need to add a credit note to reflect the credit that has been added
				$arrReferralCredit = array('intInvoiceId'           => $intInvoiceId,
				                           'intReferralCreditValue' => $intReferralCreditValue);
				$objReferrals->createCreditNote($arrReferralCredit);
			}
			else
			{
				$this->strPayMethod = 'BACS';
				$strTicketText = $this->generateRefundTicketText($intBusinessRuleAction, $floRefundValue, $intInvoiceId, $intServiceId, $intRefereeServiceId);

				$intTeamId = phplibGetTeamIdByHandle('ABS_CHEQUE_REFUNDS');
				if(empty($intTeamId))
				{
					//in case search failed, raise ticket to general CSC pool to prevent it from being lost
					$intTeamId = 1;
				}

				$intTicketId = tickets_ticket_add('Script',
				                                  $intServiceId,
				                                  0,
				                                  0,
				                                  'Open',
				                                  SCRIPT_USER,
				                                  $strTicketText,
				                                  0,
				                                  $intTeamId);

				if (false == $intTicketId)
				{
					return false;
				}
			} // !(if (false !== $resResult))

			return true;
		} // protected function refundByBacs($intServiceId, $intInvoiceId, $floRefundValue, $intRefereeServiceId)


		/**
		 * refundByCheque
		 * refunds $floRefundValue to invoice $intInvoiceId by cheque (raises ticket to A & BS Cheque Refunds)
		 *
		 * @param int $intBusinessRuleAction
		 * @param int $intServiceId
		 * @param int $intInvoiceId
		 * @param flo $floRefundValue
		 * @param int $intRefereeServiceId
		 * @access private
		 * @return bool true on success, false on failure
		 */
		protected function refundByCheque($intBusinessRuleAction, $intServiceId, $intInvoiceId, $floRefundValue, $intRefereeServiceId)
		{
			$strTicketText = $this->generateRefundTicketText($intBusinessRuleAction, $floRefundValue, $intInvoiceId, $intServiceId, $intRefereeServiceId);

			$intTeamId = phplibGetTeamIdByHandle('ABS_CHEQUE_REFUNDS');
			if(empty($intTeamId))
			{
				//in case search failed, raise ticket to general CSC pool to prevent it from being lost
				$intTeamId = 1;
			}


			$intTicketId = tickets_ticket_add('Script',
			                                  $intServiceId,
			                                  0,
			                                  0,
			                                  'Open',
			                                  SCRIPT_USER,
			                                  $strTicketText,
			                                  0,
			                                  $intTeamId);

			if($intTicketId)
			{
				return true;
			}

			return false;
		}


		/**
		 * Small refactoring of prepareRefund()
		 * @access private
		 */

		private function handlePlacementOfRefundFailedError($intReferrerServiceId, $intInvoiceId, $floRefundValue, $intRefereeServiceId)
		{
			$strErrorDescription  = "Placement of refund FAILED.\n";
			$strErrorDescription .= "Details are: \n";
			$strErrorDescription .= "intReferrerServiceId: $intReferrerServiceId,\n";
			$strErrorDescription .= "intInvoiceId: $intInvoiceId,\n";
			$strErrorDescription .= "floRefundValue: $floRefundValue,\n";
			$strErrorDescription .= "intRefereeServiceId: $intRefereeServiceId\n";

			echo $strErrorDescription;

			$this->intFailed++;


			// And now.. for your reading pleasure.. another wonderful 'Strange but true' fact!

			// Where's $arrReferral come from? :S

			// This is an existing bug on live before any of the BV refactorings, so will need
			// to be fixed as a seperate problem, or as part of the project if I can figure out
			// where it thinks it should get this variable from.

			if(isset($arrReferral['startdate']) && $arrReferral['startdate']!='')
			{
				$this->checkProcessingDelay($arrReferral['startdate'], $strErrorDescription);
			}
		}



		/**
		 * Small refactor of prepareRefund()
		 * @access private
		 */

		private function raiseProblemMissingInvoiceFailedBounty($intInvoiceId, $floRefundValue, $intReferrerServiceId, $intRefereeServiceId)
		{
			$strMessage =  '';
			$strMessage .= (false === $intInvoiceId) ? 'Failed to find last invoice id. ' : '';
			$strMessage .= (false === $floRefundValue) ? 'Failed to find bounty value. ' : '';
			$strMessage .= " I was trying to assign bounty to SID $intReferrerServiceId " .
			               " (referring sid $intRefereeServiceId) . Automatic refund impossible\n";

			echo $strMessage;

			$this->arrFailed[] = $intReferrerServiceId;
			$this->intFailed++;

			pt_raise_autoproblem('Referrals - Automatic refund',
			                     'Last Invoice not found',
			                     $strMessage,
			                     $strMessage);
		}



		/**
		 * Small refactor of prepareRefund()
		 * @access private
		 * @return boolean
		 */

		private function handleRefundByDirectDebit($intBusinessRuleAction, $intServiceIdWhoGetsRefunded, $intInvoiceId, $floRefundValue, $intRefereeServiceId)
		{
			if (FALSE !== $intInvoiceId) {

				echo "Last Invoice ID for SID $intServiceIdWhoGetsRefunded: $intInvoiceId\n";
				$arrInvoice = financial_sales_invoice_get($intInvoiceId);


				// Are we able to refund this by Direct Debit actually? The refund manager
				// has a 13-month cutoff rule in it, so we should check that first before
				// proceeding. If the invoice is too old then we should refund by cheque
				// instead. P1 problem 52371.

				if (NULL === C_Refund::isValidRefund($intInvoiceId)) {

					// NULL says there's no issue with the invoice age, thus proceed as normal.

					$bolResult = $this->refundByDirectDebit($intBusinessRuleAction, $intServiceIdWhoGetsRefunded, $intInvoiceId, $floRefundValue, $intRefereeServiceId);

				} else {

					// Bah, problem with invoice therefore refund by cheque instead.

					$bolResult = $this->refundByCheque($intBusinessRuleAction, $intServiceIdWhoGetsRefunded, $intInvoiceId, $floRefundValue, $intRefereeServiceId);
				}
			}

			if(false === $intInvoiceId || count($arrInvoice) == 0) {

				$strMessage =  '';
				$strMessage .= (false === $intInvoiceId) ? 'Failed to find last invoice id. ' : '';
				$strMessage .= (false === $floRefundValue) ? 'Failed to find bounty value. ' : '';
				$strMessage .= " I was trying to assign bounty to SID {$intServiceIdWhoGetsRefunded} " .
				               " (referring sid {$intRefereeServiceId}) . Refunding by BACS instead. \n";

				echo $strMessage;
				// This is a sort of refund by Direct Debit, since it uses the account direct
				// debit details, which then gets refunded via a generated BACS payment file
				$bolResult = $this->refundByBacs($intBusinessRuleAction, $intServiceIdWhoGetsRefunded, $intInvoiceId, $floRefundValue, $intRefereeServiceId);
			}

			return $bolResult;
		}	 // private function handleRefundByDirectDebit



		/**
		 * prepareRefund
		 *
		 * @param int $intServiceIdWhoGetsRefunded
		 * @param flo $floRefundValue
		 * @param int $intRefereeServiceId
		 * @access private
		 * @return bol true on success, false on failure
		 */
		protected function prepareRefund($intBusinessRuleAction, $intServiceIdWhoGetsRefunded, $floRefundValue, $intRefereeServiceId)
		{
			$strHasDirectDebit = direct_debit_has_service_got_instruction_with_status($intServiceIdWhoGetsRefunded, array('active'));

			$intInvoiceId = $this->findLastInvoiceForSid($intServiceIdWhoGetsRefunded);

			if('passed' == $strHasDirectDebit) {

				$bolResult = $this->handleRefundByDirectDebit($intBusinessRuleAction, $intServiceIdWhoGetsRefunded, $intInvoiceId, $floRefundValue, $intRefereeServiceId);

			} else if (false !== referralsGetBankDetails($intServiceIdWhoGetsRefunded)) {

				$bolResult = $this->refundByBacs($intBusinessRuleAction,
				                                 $intServiceIdWhoGetsRefunded,
				                                 $intInvoiceId,
				                                 $floRefundValue,
				                                 $intRefereeServiceId);

			} else {

				$bolResult = $this->refundByCheque($intBusinessRuleAction, $intServiceIdWhoGetsRefunded, $intInvoiceId, $floRefundValue, $intRefereeServiceId);
			}


			if (!$bolResult) {

				$this->handlePlacementOfRefundFailedError($intServiceIdWhoGetsRefunded, $intInvoiceId, $floRefundValue, $intRefereeServiceId);
			}


			return $bolResult;
		} // protected function prepareRefund

		/**
		 * initiateSmarty
		 *
		 * @access private
		 * @return void
		 */
		protected function initiateSmarty()
		{
			$this->objSmarty = new Smarty();
			$this->objSmarty->compile_dir = SMARTY_COMPILE_DIR;
			$this->objSmarty->cache_dir = SMARTY_CACHE_DIR;
			$this->objSmarty->template_dir = './';
			$this->strTemplateFile = '/local/data/mis/database/application_apis/ReferralBounty/processBountyReferrals.tpl';
		}

		/**
		 * processRun
		 *
		 * @param array $arrReferrals
		 * @access private
		 * @return bool
		 */
		function processRun($arrReferrals)
		{

			$this->initiateSmarty();

			foreach($arrReferrals as $arrReferral)
			{
				$intReferrerServiceId = $arrReferral['referral_from_sid'];
				$intRefereeServiceId = $arrReferral['referral_signedup_sid'];
				echo "Processing Referral ID " .$arrReferral['referral_uid']. " (SID $intRefereeServiceId)\n";

				$arrService = userdata_service_get($intRefereeServiceId);
				$arrProduct = product_get_account_attributes($arrService['type']);

				if(userdata_service_is_free($intRefereeServiceId) || ((count($arrProduct) <= 0) || empty($arrProduct)))
				{

					echo "Service ID $intRefereeServiceId downgraded to free account. " .
					     "Bounty cannot be assigned.\n";

					$this->intSkipped++;

					if($this->bolLive) {

						$bolReferralProcessed = $this->markReferralProcessed($arrReferral['referral_uid']);
					}

					continue;
				}

				$bolReferralProcessed = $this->processSingleReferral($arrReferral);

			}

			return $bolReferralProcessed;
		}



		private function raiseProblemReferralNotMarkedAsProcessed($intReferralUid)
		{
			// And now.. for your reading pleasure.. another wonderful 'Strange but true' fact!

			// The following variable isn't ever initialised from anywhere within its scope,
			// even before the refactoring work.

			// This problem already exists on live, so making a note of it here so it'll
			// be fixed either in this project, or as a seperate problem.

			$intInvoiceId = "'FIXME - existing live bug'";	// QA, you reading this? :-D

			$strDescription = 'Refund order against invoice ' . $intInvoiceId . ' placed, ' .
			                  'but referral (' . $intReferralUid . ') was not marked as processed ' .
			                  'and will be processed again. ';

			pt_raise_autoproblem('Referrals - Incorrect tokens',
			                     'Referral not marked as processed',
			                     $strDescription,
			                     $strDescription);
		}



		private function processSingleReferralEntity($intBusinessRuleAction, $intTargetServiceId, $intOtherServiceId, $floRefundValue, $intReferralUid)
		{
			$bolReferralProcessed = TRUE;

			if ($this->bolLive) {

				if ($this->prepareRefund($intBusinessRuleAction, $intTargetServiceId, $floRefundValue, $intOtherServiceId)) {

					$this->addReferralNoteToSid($intBusinessRuleAction, $intTargetServiceId, $floRefundValue);
				}

			} else {

				echo "Test run: on live I'd try to process referral id {$intReferralUid} (for service ID {$intTargetServiceId}) and refund {$floRefundValue}\n";
			}


			return $bolReferralProcessed;
		}



		/**
		 * processSingleReferral
		 *
		 * @param array $arrReferral
		 * @access private
		 * @return void
		 */
		function processSingleReferral($arrReferral)
		{
			$intRefereeServiceId = $arrReferral['referral_signedup_sid'];


			// the bounty value will always be based on the referred (aka referee) account

			$arrRefundValues = $this->findRefundValueForSid($intRefereeServiceId);

			if ($arrRefundValues['decReferralValue'] > 0) {

				// assume that we're only paying the referrer now

				$intMode = self::PAY_REFERRER;

				if ($arrRefundValues['decReferredValue'] > 0) {

					// we have a referred amount too, so technically speaking we'll be
					// refunding both parties, each with their own different bounty value

					$intMode = self::PAY_REFERRED;
				}

			} else {

				// if we don't have a referrer amount, then we're not refunding anyone

				$intMode = self::PAY_NO_ONE;
			}


			$intReferrerServiceId = $arrReferral['referral_from_sid'];
			$intReferralUid = $arrReferral['referral_uid'];

			switch ($intMode) {

				case self::PAY_REFERRED:

					// first of all lets pay the referred (aka referee)

					$this->processSingleReferralEntity($intMode,
					                                   $intRefereeServiceId,
					                                   $intReferrerServiceId,
					                                   $arrRefundValues['decReferredValue'],
					                                   $intReferralUid);


					// fall through, as *referrer* is common to both situations


				case self::PAY_REFERRER:

					$this->processSingleReferralEntity(self::PAY_REFERRER,
					                                   $intReferrerServiceId,
					                                   $intRefereeServiceId,
					                                   $arrRefundValues['decReferralValue'],
					                                   $intReferralUid);

					break;


				case self::PAY_NO_ONE:
				default:

					$this->intSkipped++;
					$strMessage = "Product SID $intRefereeServiceId is on or has signed to isn't eligible for bounty. Skipping.\n";
					echo $strMessage;

					return;

			} // switch


			if ($this->markReferralProcessed($intReferralUid)) {

				$this->intReferralsProcessed++;

			} else {

				$this->raiseProblemReferralNotMarkedAsProcessed($intReferralUid);
			}
		}
	} // abstract class processBountyReferrals
