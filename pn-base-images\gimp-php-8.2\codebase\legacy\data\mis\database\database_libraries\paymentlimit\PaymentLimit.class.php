<?php

class Lib_PaymentLimit extends Util_LibrarySplitter
{
	protected static $objInstance;

	protected function __construct()
	{
		$this->myName = __CLASS__;
		$this->myFile = __FILE__;
	}

	public static function singleton()
	{
		if (!isset(self::$objInstance))
		{
			$strClass = __CLASS__;
			self::$objInstance = new $strClass;
		}

		return self::$objInstance;
	} // public static function singleton()

	/**
	 * Sets an instance of the object
	 *
	 * To be used only for unit tests purposes (mocking legacy functions)
	 *
	 * @access public
	 * @static
	 *
	 * @param Lib_PaymentLimit $objInstance
	 */
	public static function setInstance(Lib_PaymentLimit $objInstance)
	{
		self::$objInstance = $objInstance;
	}

	/**
	 * Resets an instance of the object
	 *
	 * To be used only for unit tests purposes (mocking legacy functions)
	 *
	 * @access public
	 * @static
	 */
	public static function resetInstance()
	{
		self::$objInstance = null;
	}
}
