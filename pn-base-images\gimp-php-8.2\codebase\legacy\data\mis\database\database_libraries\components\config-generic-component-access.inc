<?php

	/**
	* Access library for generic component.  Contains component configurator and component class
	*
	* @package    Core
	* @subpackage WLR
	* @access     public
	* @version    $Id: config-generic-component-access.inc,v 1.4 2009-03-23 14:13:03 pfraszczak Exp $
	* @filesource
	*/

	require_once('/local/data/mis/database/database_libraries/components/component-defines.inc');
	require_once('/local/data/mis/database/database_libraries/sql_primitives.inc');
	require_once('/local/data/mis/database/database_libraries/components/CComponent.inc');

	require_once('/local/codebase2005/modules/Framework/Libraries/bootstrap.inc.php');
	/* Globally executed code, make configurator entries for all generic component configs */

	$dbhConn = get_named_connection_with_db('unprivileged_reporting');

	$strQuery = "SELECT scp.intServiceComponentID
	               FROM products.tblServiceComponentProduct scp
	         INNER JOIN products.tblServiceComponentProductType scpt
	                 ON scp.intServiceComponentProductTypeID = scpt.intServiceComponentProductTypeID
	              WHERE scpt.vchHandle IN ('GENERIC_COMPONENT', 'GENERIC_DISCOUNT_COMPONENT', 'GENERIC_NONCHARGEABLE_COMPONENT','MAX_PREMIUM')";

	$refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Make generic component configurator entries');
	$arrWlrConfigurators = PrimitivesResultsAsArrayGet($refResult);

	foreach($arrWlrConfigurators as $arrConfigurator)
	{
		$global_component_configurators[(int)$arrConfigurator['intServiceComponentID']] = "config_generic_component_configurator";
	}

	// Hack to insert the component configurator array into PHP5's global scope
	// if it's not already there
	if(!isset($GLOBALS['global_component_configurators']))
	{
		$GLOBALS['global_component_configurators'] = $global_component_configurators;
	}
	else
	{
		foreach($global_component_configurators as $intIndex => $strConfigurator)
		{
			$GLOBALS['global_component_configurators'][$intIndex] = $strConfigurator;
		}
	}

	unset($arrWlrConfigurators);


    /**
    * Component configurator
    *
    * Follows standard component configurator patter.  Do not modify name or param list.
    *
    * @access public
    * @global
    * @param integer component id
    * @param string action
    * @return integer -1 if the component does not exist, otherwise the result of the action performed
    */
    function config_generic_component_configurator($intComponentID, $strAction, $options = array())
    {
        $component = CComponent::createInstance($intComponentID);

        if (!$component) {
            return (-1);
        }

        switch($strAction) {
            case 'auto_configure':
                if ($component->m_strStatus == "active") {
                    return;
                }
                // component exists so make the call to configure
                Components_Api::activate($intComponentID, $options);
                // all is well - need to commit the default framework transaction
                Db_Manager::commit();
                break;

            case "auto_disable":
                // Nothing to do
                break;

            case "auto_enable":
                // Nothing to do
                break;

            case "auto_refresh":
                // Nothing to do
                break;

            case "auto_destroy":
                if ($component->m_strStatus == "destroyed") {
                    return;
                }
                Components_Api::destroy($intComponentID, $options);
                Db_Manager::commit();
                break;
        }
    }
