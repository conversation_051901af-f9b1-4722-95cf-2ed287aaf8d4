<?php
/**
 * phpcs DOCBLOCK header
 *
 * @package LegacyCodebase
 * <AUTHOR> <<EMAIL>>
 *
 */

/**
 * Function to retrieve the ADSL Activation Cost from the database. This value is stored *EXCLUDING* VAT
 * so remember to add VAT on if you need to.  Value is returned in pounds as a float (e.g. £10.99)
 * Data is cached - there's only ever one record to store!
 *
 * @return float
 */
function split_adslGetBtActivationCost()
{
    static $floBtCost = -1;

    if ($floBtCost == -1) {
        $query = <<<EOQ
SELECT
    intCustomerCost / 100 AS floBTCost
FROM
    products.tblBTCost
WHERE
    vchBTCostHandle = "ACTIVATION"
    AND dteEffectiveTo IS NULL
ORDER BY
    dteEffectiveFrom DESC
LIMIT 1
EOQ;
        $conn      = get_named_connection_with_db('unprivileged_reporting');
        $resResult = PrimitivesQueryOrExit($query, $conn, __FUNCTION__);
        $floBtCost = PrimitivesResultGet($resResult, 'floBTCost');

        $floBtCost = (!empty($floBtCost) ? (float) $floBtCost : 0);
    }

    return $floBtCost;
}
