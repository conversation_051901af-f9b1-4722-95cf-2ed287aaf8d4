<?php

	/////////////////////////////////////////////////////////////////////
	// File        : config-ecommerce-access.inc
	// Purpose     : Access mini-library for NetAnnounce
	// Last change : $Id: config-ecommerce-access.inc,v 1.5 2007-02-13 08:15:58 cblack Exp $
	/////////////////////////////////////////////////////////////////////

	/////////////////////////////////////////////////////////////////////
	// Data

	$global_component_configurators[COMPONENT_PLUS_ECOMMERCE_BASIC] = "config_ecommerce_configurator";
	$global_component_configurators[COMPONENT_PLUS_ECOMMERCE_ADVANCED] = "config_ecommerce_configurator";

	// Data
	/////////////////////////////////////////////////////////////////////
	
	// Hack to insert the component configurator array into PHP5's global scope
	// if it's not already there
	if(!isset($GLOBALS['global_component_configurators']))
	{
		$GLOBALS['global_component_configurators'] = $global_component_configurators;
	}
	else
	{
		foreach($global_component_configurators as $intIndex => $strConfigurator)
		{
			$GLOBALS['global_component_configurators'][$intIndex] = $strConfigurator;
		}
	}



	/////////////////////////////////////////////////////////////
	// Function:  config_ecommerce_auto_destroy
	// Purpose:   * -> 'queued-destroy' state
	//            transition handler for auto-destruction
	// Arguments: $component_id
	/////////////////////////////////////////////////////////////

	function config_ecommerce_auto_destroy ($component_id) {

		$component = userdata_component_get ($component_id);

		switch ($component["status"]) {

		    case "active":
		    case "deactive":
		    case "queued-deactivate":
		    case "queued-reactivate":
		    case "queued-deconfigure":
			// Has configuration, queue it for destruction
			userdata_component_set_status ($component_id, "queued-destroy");
			break;

		    case "queued-activate":
		    case "unconfigured":
		    case "destroyed":
			// No configuration there, just kill it
			userdata_component_set_status ($component_id, "destroyed");
			break;

		    case "queued-destroy":
			// We're doing it already...
			break;

		    default:
			// The sky is falling!
			break;

		}

	}


	function config_ecommerce_configurator ($component_id, $action) {

		switch ($action) {

		    case "auto_configure":
			// Do nothing. User must request activation.
			break;

		    case "auto_disable":
			// FIXME
			break;

		    case "auto_enable":
			// FIXME
			break;

		    case "auto_refresh":
			// Nothing to do here
			break;

		    case "auto_destroy":
			config_ecommerce_auto_destroy ($component_id);
			break;

		    default:
			break;

		}

	}


?>
