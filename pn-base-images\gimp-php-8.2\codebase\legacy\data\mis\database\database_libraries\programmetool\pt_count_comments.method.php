<?php

/**
 * Function to count the number of comments in a problem/project/programme.
 *
 * <AUTHOR>
 *
 * @param $strType
 * @param $intTypeId
 * @param $strUserId
 * @param $strSubType
 * @param $intSubTypeId
 * @param $intTimeInterval
 *
 */
function split_pt_count_comments($strType, $intTypeId, $strUserId='zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz', $strSubType='', $intSubTypeId=0, $intTimeInterval=0)
{
	if(is_numeric($intTypeId) && $intTypeId > 0)
	{
		$dbhConnection = get_named_connection_with_db('project_tool');
		$strQuery = " SELECT "
		          . "  count(*) intCommentsCount "
		          . " FROM pt_comment "
		          . " WHERE type_id = $intTypeId "
		          . " AND type = '".PrimitivesRealEscapeString($strType,$dbhConnection)."' "
		          . " AND user_id = '".PrimitivesRealEscapeString($strUserId,$dbhConnection)."' ";
		if(trim($strSubType) != '')
		{
			$strQuery .= " AND sub_type LIKE '".PrimitivesRealEscapeString(trim($strSubType),$dbhConnection)."' ";
		}
		if(is_numeric($intSubTypeId) && $intSubTypeId > 0)
		{
			$strQuery .= " AND sub_type_id =  $intSubTypeId ";
		}
		if(is_numeric($intTimeInterval) && $intTimeInterval > 0)
		{
			$strQuery .= " AND date_created > DATE_SUB(NOW(),INTERVAL $intTimeInterval MINUTE) ";
		}

		$resResults = PrimitivesQueryOrExit($strQuery, $dbhConnection);
		return PrimitivesResultGet($resResults , 'intCommentsCount');
	}
	return false;
}
