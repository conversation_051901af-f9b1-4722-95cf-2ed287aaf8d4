<?php

/**
 * CancellationPayment Class File
 *
 * @category LegacyCodebase
 * @package  LegacyCodebase
 * <AUTHOR> <<EMAIL>>
 * @link     link
 */
include '/local/data/mis/database/database_libraries/financial/DirectDebitTransactionCode.class.php';
/**
 * CancellationPayment Class
 *
 * @category LegacyCodebase
 * @package  LegacyCodebase
 * <AUTHOR> <<EMAIL>>
 * @link     link
 */

class CancellationPayment
{
    /**
     * @var Integer
     */
    private $_serviceId = null;

    /**
     * Helper object provides an interface to untestable code.
     *
     * @var CancellationPaymentHelper
     */
    private $_helper;

    /**
     * CalculateProRataCost takes a next invoice date (as timestamp),
     * a cancellation date (as timestamp) and a float
     * representing a monthly charge. It should return the daily charge multiplied by
     * the difference in days. This will be positive if the cancellation date is after
     * the next invoice, and negative (i.e. a refund) if the cancellation date is before the next invoice.
     *
     * @param int   $nextInvoice   timestamp
     * @param int   $cancellation  timestamp
     * @param float $monthlyCharge cost for a month
     *
     * @return float cost for days betwen cancellation and next invoice.
     */
    public static function calculateProRataCost($nextInvoice, $cancellation, $monthlyCharge)
    {
        $daysDiff = floor(($cancellation - $nextInvoice) / 3600 / 24);
        $costPerDay = $monthlyCharge / 30;
        return $costPerDay * $daysDiff;
    }

    /**
     * return if the Invoice is one of BBCR cancellation invoices
     *
     * @param integer $invoiceId Invoice Id
     *
     * @return boolean
     */
    public static function isInvoiceCancellationInvoice($invoiceId)
    {
        $dbhConnection = get_named_connection_with_db('financial_reporting')
        or report_error(__FILE__, __LINE__, 'Failed to connect to database');
        $invoiceId = PrimitivesRealEscapeString($invoiceId, $dbhConnection);
        $strQuery = "SELECT
        count(intInvoiceId) as intCount
        FROM
        financial.tblBbcrCancellationInvoice
        WHERE
        intInvoiceId = {$invoiceId}";
        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);
        $arrResult =  PrimitivesResultGet($resResult);
        if ($arrResult['intCount'] > 0) {
            return true;
        }
        return false;
    }

    /**
     * Constructs the class with service id
     *
     * @param integer $serviceId service id of the user
     */
    public function __construct($serviceId)
    {
        $this->_serviceId = $serviceId;
    }

    /**
     * Takes payment using the appropriate method
     *
     * @param int                       $invoiceId       The invoice id
     * @param array                     $arrEmailData    Array with email template variables
     * @param bool                      $bolFinalPayment Add DD transaction as final payment (cancels DD instructions)
     * @param CancellationPaymentHelper $helper          Helper object for interacting with legacy functions
     *
     * @return boolean    True if payment was successful otherwise false
     */
    public function takePayment(
        $invoiceId, $arrEmailData, $bolFinalPayment, CancellationPaymentHelper $helper
    ) {
        $this->_helper = $helper;
        $this->_helper->loadLegacyFiles();
        $arrInvoice = $this->_helper->getInvoice($invoiceId);
        $arrAccount = $this->_helper->getUserdata($this->_serviceId);
        $strPaymentMethod  = $this->_getPaymentMethod($arrAccount['card_type'], $arrAccount['account_id']);

        $arrEmailData['bolFinalPayment'] = $bolFinalPayment;

        $bolPaymentSuccessful = false;

        switch ($strPaymentMethod)
        {
            case 'directdebit':
                $bolPaymentSuccessful = $this->_takePaymentUsingDirectDebit(
                    $arrInvoice['gross_value'],
                    $invoiceId,
                    $arrEmailData,
                    $bolFinalPayment
                );
                break;

            case 'cheque':
                $bolPaymentSuccessful = $this->_takePaymentUsingCheque($arrEmailData);
                break;

            case 'card':
                $bolPaymentSuccessful = $this->_takePaymentUsingCard(
                    $arrInvoice['gross_value'],
                    $invoiceId,
                    $arrEmailData
                );
                break;

            case 'not_valid': //Credit Card details on account are not valid (not set or expired)
            default:
                //Notify customer about outstanding charges via email and create failed payment ticket
                $this->_helper->sendEmail($this->_serviceId, 'migrating_os_fee_cc', $arrEmailData);
                $this->_sendPaymentFailedTicket($arrEmailData['floTotalOutstandingCharges']);
        }

        return $bolPaymentSuccessful;
    }

    /**
     * Work out how to take payment for the invoice..
     *
     * Order of preference:
     *  -- take direct debit if instruction exists.
     *  -- if not, take credit card payment or raise a ticket for cheque payment if that is the card type.
     *  -- if we can't take credit card payment, or dd has failed for some reason, then
     *     raise a ticket to take manual payment.
     *
     * @param string $cardType  The card type, from credit_details (could be empty)
     * @param int    $accountId The account id.
     *
     * @return string - directdebit | card | cheque
     */
    private function _getPaymentMethod($cardType, $accountId)
    {
        // Let's see if we've got direct debit instructions, as dd payment is a first preference
        $objDirectDebitDetails = $this->_helper->getDirectDebitDetails($this->_serviceId);

        $strPaymentMethod = 'not_valid';

        if (null === $objDirectDebitDetails) {

            // No direct debit details, so we need to attempt credit card or cheque payment.....
            if ('CHEQUE' == strtoupper($cardType)) {

                $strPaymentMethod = 'cheque';

            } else {

                // Validate the card details for this account, and only allow card payment if
                // it's going to work.
                // If not, then the payment type will default to '', i.e. manual payment.
                if (false !== $this->_helper->getValidCardDetails($accountId)) {
                    $strPaymentMethod = 'card';
                }
            }

        } else {

            // Direct debit details found - process the payment this way.
            $strPaymentMethod = 'directdebit';
        }

        return $strPaymentMethod;
    }


    /**
     * Attempts to take payment using a credit card.
     * If this is successful, then it deactivates the account, and schedules cancellation.
     * If not, then it raises a ticket to take the payment manually.
     *
     * @param float $total     The total amount invoiced (to take payment for).
     * @param int   $invoiceId The invoice id just generated.
     * @param array $emailData Array with email template variables
     *
     * @return boolean    True if payment successful otherwise false
     */
    private function _takePaymentUsingCard($total, $invoiceId, $emailData)
    {
        // Take credit card payment
        $intTransactionID = $this->_helper->payWithCard($this->_serviceId, $total, $invoiceId);
        $this->_helper->sendEmail($this->_serviceId, 'migrating_os_fee_cc', $emailData);

        if (isset($intTransactionID) && $intTransactionID > 0) {
            // payment is successful.
            $this->_helper->sendEmail($this->_serviceId, 'os_fee_payment_successful', $emailData);
            $strTicketBody
                = "We have now successfully taken payment of £{$total}.".
                "The payment covers the outstanding charges on your account".
                " as part of your broadband move.\n\n". "Thank you for your time and your custom.";

            $this->_helper->addTicket($this->_serviceId, $strTicketBody, 0, 'Closed');
            return true;
        }
        // If we're here, then credit card payment has failed.
        $this->_sendPaymentFailedTicket($total);
        return false;
    }

    /**
     * Handles the sending of a generic failed payment ticket - goes to DEBT_RECOVERY_REQUESTS pool
     *
     * @param float $totalOutstandingCharges The total amount we failed to take payment for.
     *
     * @return integer - the ticket id (or boolean false if raising the ticket failed).
     */
    private function _sendPaymentFailedTicket($totalOutstandingCharges)
    {
        // Send a ticket to CSC failed billing pool - separate pools for business and residential..
        $arrService = $this->_helper->getAccountDetails($this->_serviceId);
        $strSector = $this->_helper->getCustomerSector($this->_serviceId);

        $ticketPoolHandle = 'DEBT_RECOVERY_REQUESTS';

        $ticketBody = "We've not been able to take a cancellation charge for this customer:\n\n "
        . "Account username: {$arrService['username']} \n\n "
        . "Amount outstanding: &pound; ".sprintf("%.2f", $totalOutstandingCharges)."\n\n "
        . "Payment may have failed because the customer has no card details stored on the account,"
        ." or the details need updating. Please check and update customer.\n";

        $teamId = $this->_helper->getTicketPoolId($ticketPoolHandle);
        $intTicketId = $this->_helper->addTicket($this->_serviceId, $ticketBody, $teamId, 'Open');

        return $intTicketId;
    }

    /**
     * Attempts to take payment using a direct debit.
     * If this is successful, then return a status of AWAITINGDDPAYMENT, otherwise send a ticket to take
     * the payment manually.
     *
     * @param float $totalOutstandingCharges The total amount invoiced (to take payment for).
     * @param int   $invoiceId               The invoice id just generated.
     * @param array $arrEmailData            Array with email template variables
     * @param bool  $bolFinalPayment         Add DD transaction as a final payment which will cancle DD instructions.
     *
     * @return string The final status of the scheduled payment, AWAITINGDDPAYMENT or COMPLETED
     */
    private function _takePaymentUsingDirectDebit(
        $totalOutstandingCharges, $invoiceId, $arrEmailData, $bolFinalPayment = false
    ) {
        $objDirectDebitDetails = $this->_helper->getDirectDebitDetails($this->_serviceId);

        $codeHandle = null;

        if (true === $bolFinalPayment) {
            $codeHandle = DirectDebitTransactionCode::FINAL_TRANSACTION;
        }

        $intNotificationDays = $this->_helper->getServiceNotificationDays($this->_serviceId);
        $timestamp = $this->_helper->calculateWorkingDays($intNotificationDays);

        // Attempt to add the transaction..
        $instructionId = $objDirectDebitDetails->getDirectDebitInstructionId();
        $arrTransactionData = $this->_helper->addTransaction(
            $instructionId, $totalOutstandingCharges, $invoiceId, $timestamp, $codeHandle
        );

        $arrEmailData['date'] = date('d/m/Y', $timestamp);
        $arrEmailData['DD_account_name'] = $objDirectDebitDetails->getName();
        $arrEmailData['DD_reference_number'] = $objDirectDebitDetails->getOurReference() .
        '-' . $objDirectDebitDetails->getInstructionVersion();

        //send DD billing and cancellation charges notification email
        $this->_helper->sendEmail($this->_serviceId, 'migrating_os_fee_dd', $arrEmailData);

        if ($arrTransactionData === false) {
            // Direct debit payment setup failed for some reason, we'll have to raise a ticket.
            $this->_sendPaymentFailedTicket($totalOutstandingCharges);

            return false;
        }

        return true;
    }

    /**
     * For cheque payers, sends a ticket to take the payment.
     *
     * @param array $arrEmailData Array with email template variables
     *
     * @return boolean    Returns true
     */
    private function _takePaymentUsingCheque($arrEmailData)
    {
        $this->_sendChequePaymentNotification($arrEmailData);

        return true;
    }

    /**
     * Send emial and create ticket about payment for cheque payers
     *
     * @param array $arrEmailData Array with email template varibales
     *
     * @return int Ticket Id
     */
    private function _sendChequePaymentNotification($arrEmailData)
    {
        $strSector = $this->_helper->getCustomerSector($this->_serviceId);

        $strEmailHandle = 'migrating_os_fee_cheque';
        $strTicketPoolHandle = 'CSC_DSL_FAILED_BILLING_RES';

        if ('BUSINESS' == strtoupper(trim($strSector))) {
            $strEmailHandle = 'migrating_os_fee_cheque_business';
            $strTicketPoolHandle = 'CSC_DSL_FAILED_BILLING_BUSINESS';
        }

        // Send an email notification
        $this->_helper->sendEmail($this->_serviceId, $strEmailHandle, $arrEmailData);

        $strTicketBody = "Before you cancel your broadband, you'll need to pay some outstanding fees on your account."
        . "\n\n "
        . "Amount payable: £{$arrEmailData['floTotalOutstandingCharges']}\n\n "
        . "Please send a cheque, writing your username clearly on the reverse, via recorded delivery to:"
        . "\n\n "
        . "Plusnet,\n"
        . "The Balance,\n"
        . "2 Pinfold St,\n"
        . "Sheffield, S1 2GU.\n\n"
        . "Thank you for your time and your custom. \n";

        $intTeamId = $this->_helper->getTicketPoolId($strTicketPoolHandle);
        $intTicketId = $this->_helper->addTicket($this->_serviceId, $strTicketBody, $intTeamId, 'Closed');

        return $intTicketId;
    }

    /**
     * Insert the BBCR cancellation invoice into the financial.tblBbcrCancellationInvoice
     *
     * @param int $invoiceId Invoice Id
     *
     * @return boolean
     */
    public function insertBbcrCancellationInvoice($invoiceId)
    {
        $strQuery = "INSERT INTO tblBbcrCancellationInvoice SET intInvoiceId = {$invoiceId}";

        return $this->executeSql($strQuery, false);
    }

    /**
     * Execute the query
     *
     * @param string $strQuery        query
     * @param bool   $bolFetchDetails its given false for update and insert statements
     *
     * @return array
     */
    protected function executeSql($strQuery, $bolFetchDetails = true)
    {
        if ($bolFetchDetails) {
            $dbhConnection = get_named_connection_with_db('financial_reporting')
                         or report_error(__FILE__, __LINE__, 'Failed to connect to database');
        } else {
            $dbhConnection = get_named_connection_with_db('financial')
                         or report_error(__FILE__, __LINE__, 'Failed to connect to database');
        }

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

        if ($bolFetchDetails) {
            return PrimitivesResultGet($resResult);
        } else {
            return true;
        }
    }
}
