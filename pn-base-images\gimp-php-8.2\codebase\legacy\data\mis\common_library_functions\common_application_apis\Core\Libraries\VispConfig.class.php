<?php
	/**
	 * Enter description here...
	 *
	 */
	class Core_VispConfig
	{
		private $objDao = null;

		/**
		 * 
		 */
		public function __construct($strIsp = null, $strTransactionName = Db_Manager::DEFAULT_TRANSACTION)
		{
			if (isset($strIsp))
			{
				$this->objDao = Core_VispConfigDao::get($strIsp, $strTransactionName);
			}
		}

		/**
		 * Takes care of all calls to setters and getters of DAO object
		 *  
		 * @access public
		 *  
		 * @uses Core_VispConfig::$objDao
		 * 
		 * <AUTHOR> <<EMAIL>>
		 * <AUTHOR> <d<PERSON><PERSON><PERSON>@plus.net>
		 * 
		 * @param string    $strMethod Method name
		 * @param array     $arrParams Params passed to method called
		 */		
		public function __call($strMethod, $arrParams)
		{
			if(preg_match('/^(get|set)/', $strMethod)) {

				return call_user_func_array(array($this->objDao, $strMethod), $arrParams);
			}
	
			throw new Exception("Method does not exist: ".get_class($this).'::'.$strMethod);
		}

		/**
		 * Calls write method of DAO object
		 *  
		 * @access public
		 *  
		 * @uses Core_VispConfig::$objDao
		 * 
		 * <AUTHOR> Marek" <<EMAIL>>
		 */		
		public function write()
		{
			return $this->objDao->write();
		}

	}
