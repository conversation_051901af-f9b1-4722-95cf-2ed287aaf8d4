<?php
/**
 * Core BillingDate
 *
 * @package   Core
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2010 PlusNet
 * @version   git
 * @since     File available since 2010-04-30
 */

/**
 * Core BillingDate
 *
 * Class representation of billing date, next period, and pro-rata.
 * This entity should be completely independent of the service itself.
 * In the following public method descriptions we will make reference to the following scenario:
 *
 * |15/01/2010         |15/02/2010         |15/03/2010         |15/04/2010
 * |Last Period        |Current Period     |Next Period        |Period After       |
 * +-------------------+-------------------+-------------------+-------------------+
 * |                   |         ^         |                   |                   |
 * |                   |         |Now      |                   |                   |
 * |                   |         |01/03/2010                   |                   |
 * |                   |<===a===>|<===p===>|                   |                   |
 *     a = ante-prorata period
 *     p = post-prorata period
 *
 * Our next invoice is 15/03/2010, and invoice day is 15. Invoice period is monthly'
 * The current billing period is 15/02/2010 to 14/03/2010
 * The date today is 01/03/2010
 *
 * Before you alter anything in this class think very carefully about the purpose of what
 * you're altering. This class is not concerned with any kind of business logic, so if you're
 * changing it to account for some kind of bizarre business rule you probably don't want to
 * do it here at all, but at a higher level where it may take the output and manipulate it
 * afterwards, or take the input and manipulate it before feeding it into this class.
 *
 * @package   Core
 * <AUTHOR> Jones <<EMAIL>>
 *
 * @copyright 2010 PlusNet
 */
class Core_BillingDate
{
    /**
     * When performing get*BillingDate tells us we're going forwards
     *
     * @var integer
     */
    const NEXT = 1;

    /**
     * When performing get*BillingDate tells us we're going backwards
     *
     * @var integer
     */
    const PREV = -1;

    /**
     * Flag for alignDateToBillingDate - will align to the billing day of the same month
     *
     * @var integer
     */
    const ALIGN_MONTH = 0;

    /**
     * Flag for alignDateToBillingDate - will find the billing date immediately before the specified date
     *
     * @var integer
     */
    const ALIGN_BACKWARD = -1;

    /**
     * Current billing date, probably from services.next_invoice
     *
     * @var I18n_Date
     */
    private $_billingDate;

    /**
     * Billing period as numerical value. 1 = monthly, 3 = quarterly, etc. (services.invoice_period)
     *
     * @var UnsignedInt
     */
    private $_billingPeriod;

    /**
     * The service period for billing
     *
     * @var Core_ServicePeriod
     */
    private $_billingServicePeriod;

    /**
     * Normal billing day. Particularly, if it's 29, 30 or 31 then the billingDate day could be
     * the last day of the month (services.invoice_day)
     *
     * @var integer
     */
    private $_billingDay;

    /**
     * Billing dates which are considered to be a null or empty value
     *
     * @var array
     */
    protected static $_nullBillingDates = array(
        null,
        '',
        '0000-00-00',
        '1970-01-01',
        '9999-09-09'
    );



    /**
     * Returns the invoice period as an integer based on the string
     * handle from the userdata.services table
     *
     * @param string $period String handle of the period
     *
     * @return UnsignedInt
     */
    public static function getBillingPeriodFromHandle($period)
    {
        // If $period can't be cast to string causes a PHP error
        $period = (string) $period;
        $periodLength = array(
            'monthly'     => new UnsignedInt(1),
            'quarterly'   => new UnsignedInt(3),
            'half-yearly' => new UnsignedInt(6),
            'yearly'      => new UnsignedInt(12)
        );

        // There's a possibility that what we're passing in is a payment frequency handle from
        // a tariff, so merge in these tariffs
        if (!array_key_exists($period, $periodLength)) {
            $paymentFrequency = ProductComponent_Tariff::getPaymentFrequencyByHandle($period);
            if (!empty($paymentFrequency)) {
                $periodLength[$period] = new UnsignedInt($paymentFrequency);
            }
        }

        return (isset($periodLength[$period]))
            ? $periodLength[$period]
            : new UnsignedInt(1);
    }

    /**
     * Creates an instance of Core_BillingDate based on date from the services table.
     * If $date isn't set to something valid (i.e. recognised as a null date) then the
     * object returned will be classes as a 'null' object, and isValid will return false.
     *
     * @param string  $date   Date as a string (from services.next_invoice)
     * @param string  $period Period as a string (from services.invoice_period)
     * @param integer $day    Billing day as integer (from services.invoice_day)
     *
     * @return Core_BillingDate
     */
    public static function getBillingDateFromDateAndPeriod($date, $period, $day)
    {
        $billingDate = null;
        if (!in_array($date, self::$_nullBillingDates)) {
            $billingDate = I18n_Date::fromString($date);
        }

        if (!is_null($day)) {
            $billingDay = new UnsignedInt($day);
        }

        return new Core_BillingDate(
            $billingDate,
            self::getBillingPeriodFromHandle($period),
            $billingDay
        );
    }



    /**
     * Create either a populated or empty instance of a BillingDate object.
     * Empty instances can be used to represent null billing dates. For example,
     * when the service next_invoice date is set to '9999-09-09'
     *
     * @param I18n_Date   $billingDate   the billing date
     * @param UnsignedInt $billingPeriod the billing period
     * @param UnsignedInt $billingDay    the billing day
     */
    public function __construct(
        I18n_Date   $billingDate = null,
        UnsignedInt $billingPeriod = null,
        UnsignedInt $billingDay = null
    ) {
        if (!in_array($billingDate, self::$_nullBillingDates)) {
            $this->_billingDate   = $billingDate;
        }

        $this->_billingPeriod = $billingPeriod;
        $this->_billingDay    = $billingDay;
    }

    /**
     * String representation of billing date. This is a MySQL type date.
     *
     * @return string
     */
    public function __toString()
    {
        $stringDate = '';

        if ($this->_billingDate instanceof I18n_Date) {
            $date = $this->_billingDate->toMySql();
            $stringDate = substr($date, 0, strpos($date, ' '));
        }

        return $stringDate;
    }



    /**
     * Returns whether the billing date is valid or not, based on whether the billingDate
     * member variable is an instance of I18n_Date.
     *
     * @return boolean
     */
    public function isValid()
    {
        return ($this->_billingDate instanceof I18n_Date);
    }

    /**
     * Return the billing date
     *
     * @return I18n_Date
     */
    public function getBillingDate()
    {
        return $this->_billingDate;
    }

    /**
     * Get the billing period as a numerical value
     *
     * @return UnsignedInt
     */
    public function getBillingPeriod()
    {
        return $this->_billingPeriod;
    }

    /**
     * Get the actual day to bill
     *
     * @return UnsignedInt
     */
    public function getBillingDay()
    {
        return $this->_billingDay;
    }

    /**
     * Get the invoice date after the current invoice date
     * Basically, current invoice date plus invoice period months
     *
     * next_invoice = '2010-03-15', invoice_period = 'monthly', invoice_day = '15'
     * |15/02/2010         |15/03/2010         |15/04/2010         |
     * +-------------------+-------------------+-------------------+
     * |                   |                   |^^^^^^^^^^^^^^^^^^^|
     *
     * In this situation "getNextBillingDate()" will return the Core_BillingDate object
     * for 15/04/2010
     *
     * This is expected to be month-day safe, so if the input is '2010-01-31', with invoice_day '31', then
     * getNextBillingDate() will give you '2010-02-28'. If the input were '2012-01-31' then the the output
     * would be '2012-02-29'
     *
     * @return Core_BillingDate|null
     */
    public function getNextBillingDate()
    {
        return self::getFutureBillingDate(self::NEXT);
    }

    /**
     * Gets the billing date before the current billing date
     *
     * next_invoice = '2010-03-15', invoice_period = 'monthly', invoice_day = '15'
     * |15/02/2010         |15/03/2010         |15/04/2010i        |
     * +-------------------+-------------------+-------------------+
     * |^^^^^^^^^^^^^^^^^^^|                   |                   |
     *
     * In this situation "getPrevBillingDate()" will return the Core_BillingDate object
     * for 15/02/2010
     *
     * This is expected to be month-day safe, so if the input is '2010-03-31', with invoice_day '31', then
     * getPrevBillingDate() will give you '2010-02-28'. If the input were '2012-03-31' then the the output
     * would be '2012-02-29'
     *
     * @return Core_BillingDate|null
     */
    public function getPrevBillingDate()
    {
        return self::getAdjustedBillingDate(self::PREV);
    }

    /**
     * Get the invoice date in the future by the number of months entered
     * Basically, current invoice date plus invoice period months
     *
     * next_invoice = '2010-03-15', invoice_period = 'monthly', invoice_day = '15'
     * |15/02/2010         |15/03/2010         |15/04/2010         |
     * +-------------------+-------------------+-------------------+
     * |                   |                   |^^^^^^^^^^^^^^^^^^^|
     *
     * In this situation "getNextBillingDate()" will return the Core_BillingDate object
     * for 15/04/2010
     *
     * This is expected to be month-day safe, so if the input is '2010-01-31', with invoice_day '31', then
     * getNextBillingDate() will give you '2010-02-28'. If the input were '2012-01-31' then the the output
     * would be '2012-02-29'
     *
     * @param int $months the number of months to add to the billing day
     *
     * @return Core_BillingDate|null
     */
    public function getFutureBillingDate($months = self::NEXT)
    {
        return self::getAdjustedBillingDate($months);
    }

    /**
     * Generates the payment dates for the specified number of months
     *
     * next_invoice = '2010-01-15', invoice_period = 'monthly', invoice_day = '15'
     * |15/01/2010         |15/02/2010         |15/03/2010         |
     * +-------------------+-------------------+-------------------+
     * |^^^^^^^^^^^^^^^^^^^|^^^^^^^^^^^^^^^^^^^|^^^^^^^^^^^^^^^^^^^|
     *
     * getBillingDateForMonths(new UnsignedInt(3)) will return an array of Core_BillingDate objects for the dates:
     *    '2010-01-15', '2010-02-15', and '2010-03-15'
     *
     * If the billing period is greater than a month then the number of elements returned will be the number of
     * months requested divided by the billing period value. E.G. quarterly => 3, requesting 12 months will return
     * 12/3 = 4 months. In this situation, if 13 or 14 months were requested it would be the same as if 12 months
     * were requested.
     *
     * @param UnsignedInt $months Number of months to generate billing dates for
     *
     * @return array
     */
    public function getBillingDatesForMonths(UnsignedInt $months)
    {
        // The first one is the current one
        $billingDates[] = $billingDate = $this;

        // Work out the rest
        for ($month =  $this->_billingPeriod->getValue();
             $month <  $months->getValue();
             $month += $this->_billingPeriod->getValue()) {
            $billingDates[] = $billingDate = $billingDate->getNextBillingDate();
        }

        return $billingDates;
    }

    /**
     * Get the Core_ServicePeriod for this billing date service period
     *
     * next_invoice = '2010-03-15', invoice_period = 'monthly', invoice_day = '15'
     * |15/02/2010         |15/03/2010         |
     * |Current Period     |Next Period        |
     * +-------------------+-------------------+
     * |                   |<=================>|
     *
     * getBillingServicePeriod() will return the service period, to the second, for the billing date specified, so the
     * start date will be '2010-03-15 00:00:00' and the end date will be '2010-04-14 23:59:59'. Where we don't care
     * about the specific time (e.g. product component contract dates), these will get truncated to just the date part.
     *
     * @return Core_ServicePeriod
     */
    public function getBillingServicePeriod()
    {
        if (!isset($this->_billingServicePeriod)) {
            $endDate = $this->getNextBillingDate()->getBillingDate();
            $endDate->modify(-1, I18n_Date::SECONDS);

            $this->_billingServicePeriod = new Core_ServicePeriod($this->_billingDate, $endDate);
        }

        return $this->_billingServicePeriod;
    }

    /**
     * Gets the Core_ServicePeriod for the data specified. This uses funky maths to figure
     * out the right start and end dates, accounting for the billing period (frequency) and making
     * the service period generated relative to the next_invoice date.
     *
     * next_invoice = '2010-03-15', invoice_period = 'quarterly', invoice_day = '15'
     * |15/12/2009              |15/03/2010              |...|15/06/2011
     * |Current Period          |Next Period             |...|Period In Future        |
     * +------------------------+------------------------+---+------------------------+
     * |                        |                        |...|        ^               |
     * |                        |                        |...|        |20/07/2011     |
     * |                        |                        |...|        |Date specified |
     * |                        |                        |...|<======================>|
     *
     * A call to getBillingServicePeriodForDate(I18n_Date::fromString('2011-07-20')) will
     * return a Core_ServicePeriod object with a start date of '2011-06-15 00:00:00' and an
     * end date of '2011-09-14 23:59:59'.
     *
     * @param I18n_Date $date Date that falls within the period we want
     *
     * @return Core_ServicePeriod
     */
    public function getBillingServicePeriodForDate(I18n_Date $date)
    {
        $date = $this->alignDateToBillingDay($date, self::ALIGN_BACKWARD);

        // Adjust the date
        $monthOffset  = (date('m', $this->_billingDate->getTimestamp()) - 1) % $this->_billingPeriod->getValue();
        $adjust = (date('m', $date->getTimestamp()) - 1) % $this->_billingPeriod->getValue();
        $offset = (($monthOffset -= $adjust) > 0) ? ($monthOffset - $this->_billingPeriod->getValue()) : $monthOffset;

        if ($offset <> 0) {
            $date->modify($offset, I18n_Date::MONTHS);

            // If after getting the modified date it doesn't match the billing date we
            // probably landed in a month with less days than the billing day, so the
            // end result ended up carrying into the following month. We have to align
            // back to the right billing day
            if (date('d', $date->getTimestamp()) != $this->_billingDay->getValue()) {
                $date = $this->alignDateToBillingDay($date, self::ALIGN_BACKWARD);
            }
        }

        $billingDate = new Core_BillingDate($date, $this->_billingPeriod, $this->_billingDay);

        return $billingDate->getBillingServicePeriod();
    }

    /**
     * Gets a billing service period aligned to the particualar date, but doesn't attempt to make
     * the service period generated relative to the invoice date.
     * This is particularly important for new ADSL signups where a different lead price is offered
     * for the first three months of the subscription - the next invoice will be for one month ahead
     * but the 3 month contract will need to start immediately in order to generate the correct
     * scheduled payments.
     *
     * N.B.: The provided date will always shift backwards the invoice day preceding the provided date:
     *   e.g.: 20/07/2011 will shift to 15/07/2011, but 10/07/2011 will shift to 15/06/2011
     *
     * next_invoice = '2010-03-15', invoice_period = 'quarterly', invoice_day = '15'
     * |15/12/2009              |15/03/2010              |...|15/06/2011
     * |Current Period          |Next Period             |...|Period In Future        |
     * +------------------------+------------------------+---+------------------------+
     * |                        |                        |...|        ^               |
     * |                        |                        |...|        |20/07/2011     |
     * |                        |                        |...|        |Date specified |
     *                                                              |15/07/2011              |
     *                                                              |<======================>|
     *                                                              |Shifted Period          |
     *
     * A call to getBillingServicePeriodForDate(I18n_Date::fromString('2011-07-20')) will
     * return a Core_ServicePeriod object with a start date of '2011-07-15 00:00:00' and an
     * end date of '2011-10-14 23:59:59'.
     *
     * @param I18n_Date $date the date
     *
     * @return void
     */
    public function getBillingServicePeriodAlignToDate(I18n_Date $date)
    {
        // Get the previous billing date
        $startBillingDate = new self(
            $this->alignDateToBillingDay($date, self::ALIGN_BACKWARD),
            $this->_billingPeriod,
            $this->_billingDay
        );

        $endDate = $startBillingDate->getNextBillingDate()->getBillingDate()->getModified(-1, I18n_Date::SECONDS);

        return new Core_ServicePeriod($startBillingDate->getBillingDate(), $endDate);
    }

    /**
     * Gets the pro-rata period from the date specified. Use the strict flag to make it the exact time.
     * Non-strict will take it from midnight. Regardless of the next_invoice date provided it will figure
     * out the service period that the provided date falls into.
     *
     * If the date provided, or in non-strict mode the rounded date, is the same as the end date of the service period
     * then the whole service period will be returned.
     *
     * next_invoice = '2010-03-15', invoice_period = 'monthly', invoice_day = '15'
     * |15/02/2010         |15/03/2010
     * |Current Period     |Next Period        |
     * +-------------------+-------------------+
     * |         ^         |                   |
     * |         |Now      |                   |
     * |         |01/03/2010                   |
     * |         |<===p===>|                   |
     *     p = post-prorata period
     *
     * getProrataServicePeriodFromDate(I18n_Date::fromString('2010-03-01')) will return a Core_ServicePeriod
     * with a start date of '2010-03-01 00:00:00' and an end date of '2010-03-14 23:59:59'
     *
     * @param I18n_Date $fromDate Date to take the pro-rata period from
     * @param boolean   $strict   Match the period to the exact time
     *
     * @return Core_ServicePeriod
     */
    public function getProrataServicePeriodFromDate(I18n_Date $fromDate, $strict = false)
    {
        if (!$strict) {
            $ts       = $fromDate->getTimestamp();
            // Not strict, realign the date to midnight
            $fromDate = I18n_Date::fromTimestamp(mktime(0, 0, 0, date('m', $ts), date('d', $ts), date('Y', $ts)));
        }

        // First we get the whole period this date falls into
        $billingServicePeriod = $this->getBillingServicePeriodForDate($fromDate);

        // If we ask for the prorata period from the exact end of the billing period (i.e. they're
        // the same) then actually there is no pro-rata. This is an intelligent guess at what should
        // actually be the start date
        $prorataStart = ($billingServicePeriod->getEnd()->getTimestamp() == $fromDate->getTimestamp())
            ? $billingServicePeriod->getStart()
            : $fromDate;

        // Then the pro-rata service period is from this date until the end of the actual service period
        return new Core_ServicePeriod($prorataStart, $billingServicePeriod->getEnd());
    }

    /**
     * Gets the pro-rata period upto the date specified. Use the strict flag to make it the exact time.
     * Non-strict will take it upto midnight of the previous day. Regardless of the next_invoice date provided it will
     * figure out the service period that the provided date falls into.
     *
     * If the date provided, or in non-strict mode the rounded date, is the same as the start date of the service
     * period then the whole service period will be returned.
     *
     * next_invoice = '2010-03-15', invoice_period = 'monthly', invoice_day = '15'
     * |15/02/2010         |15/03/2010
     * |Current Period     |Next Period        |
     * +-------------------+-------------------+
     * |         ^         |                   |
     * |         |Now      |                   |
     * |         |01/03/2010                   |
     * |<===a===>|         |                   |
     *     a = ante-prorata period
     *
     * getProrataServicePeriodUptoDate(I18n_Date::fromString('2010-03-01')) will return a Core_ServicePeriod
     * with a start date of '2010-02-15 00:00:00' and an end date of '2010-02-28 23:59:59'
     *
     * @param I18n_Date $uptoDate Date to take the pro-rata period from
     * @param boolean   $strict   Match the period to the exact time
     *
     * @return void
     */
    public function getProrataServicePeriodUptoDate(I18n_Date $uptoDate, $strict = false)
    {
        if (!$strict) {
            $ts       = $uptoDate->getTimestamp();
            $uptoDate = I18n_Date::fromTimestamp(mktime(0, 0, 0, date('m', $ts), date('d', $ts), date('Y', $ts)));
        }

        // First we get the whole period this date falls into
        $billingServicePeriod = $this->getBillingServicePeriodForDate($uptoDate);

        // If we ask for the prorata period upto the exact start of the billing period (i.e. they're
        // the same) then actually there is no pro-rata. This is an intelligent guess at what should
        // actually be the end date
        $prorataEnd = ($billingServicePeriod->getStart()->getTimestamp() == $uptoDate->getTimestamp())
            ? $billingServicePeriod->getEnd()
            : $uptoDate->getModified(-1, I18n_Date::SECONDS);

        // Then the pro-rata service period is from the original start of the billing service period
        // upto this date i.e.: immediately before
        return new Core_ServicePeriod($billingServicePeriod->getStart(), $prorataEnd);
    }



    /**
     * Adjusts a billing date forwards or backwards accordingly
     *
     * @param integer $direction Whether to get next or previous billing date
     *
     * @return Core_BillingDate
     */
    protected function getAdjustedBillingDate($direction = self::NEXT)
    {
        $adjustBillingDate = null;

        if ($this->_billingDate instanceof I18n_Date) {
            // Weird date calculation because we need to make sure days correlate where necessary
            $thisBillingTs = $this->_billingDate->getTimestamp();
            // This bit figures out what year-month we're supposed to be in
            // Direction should be (+/- 1), so val * -1 deducts it instead
            $adjustBillingMonth = I18n_Date::fromTimestamp(
                mktime(
                    0,
                    0,
                    0,
                    date('n', $thisBillingTs) + ($this->_billingPeriod->getValue() * $direction),
                    1,
                    date('Y', $thisBillingTs)
                )
            );

            $adjustBillingDate = new Core_BillingDate(
                $this->alignDateToBillingDay($adjustBillingMonth),
                $this->_billingPeriod,
                $this->_billingDay
            );
        }

        return $adjustBillingDate;
    }

    /**
     * Align a date to the billing day for the same month. This will cause the given date
     * to move forwards or backwards.
     *
     * @param I18n_Date $date  Date to adjust
     * @param int       $align align
     *
     * @return I18n_Date
     */
    public function alignDateToBillingDay(I18n_Date $date, $align = self::ALIGN_MONTH)
    {
        // Now we see if the month we're going to has enough days
        $dateTs = $date->getTimestamp();

        $adjust = 0;
        if ($align == self::ALIGN_BACKWARD &&
               date('d', $dateTs) < min($this->_billingDay->getValue(), date('t', $dateTs))
        ) {
            $adjust = -1;
        }

        $monthTs = mktime(0, 0, 0, (date('m', $dateTs) + $adjust), 1, date('Y', $dateTs));
        return I18n_Date::fromTimestamp(
            mktime(
                0,
                0,
                0,
                date('m', $monthTs),
                min(date('t', $monthTs), $this->_billingDay->getValue()),
                date('Y', $monthTs)
            )
        );
    }
}
