<?php
	
	/**
	* Access library for PlusTalk Radius User.
	* 
	* 
	*
	* @package    Core
	* @subpackage VoIP 
	* @access     public
	* <AUTHOR> <<EMAIL>>
	* @version    $Id: CPlusTalkRadiusUser.inc,v 1.6 2007-08-10 08:27:17 <PERSON><PERSON><PERSON><PERSON> Exp $
	* @filesource
	*/

	
	require_once("/local/data/mis/database/database_libraries/CoreObjects/CObject/CObject.inc");
	require_once '/local/data/mis/database/crypt_config.inc';

	//Warn when there are less than 1000 aliases left

	define('PLUSTALK_USERNAME',       'plustalk');
	define('PLUSTALK_ALIAS_WARN_LEVEL',   299000);



/**
* PlusTalk Radius User object 
* 
* 
*
* @access     public
* <AUTHOR> <<EMAIL>>
*/
class CPlusTalkRadiusUser extends CObject
{
	/**
	* Service ID	
	*
	* @var integer
	* @access private
	*/
	var $m_intServiceID;


	/**
	* User details 
	*
	* @var array
	* @access private
	*/
	var $m_arrDetails = array();

	
	/**
	* Array of PlusTalk assigned ranges for aliases
	*
	* These are alias ranges which have been assigned to us for use.
	* To add more when we run out, add another start-end pair at the next free index
	* Always check for exclusion when assigning an aliase (see m_arrReservedAliases)
	* A problem will be raised when there are less than PLUSTALK_ALIAS_WARN_LEVEL aliases remaining
	*
	* @var array
	* @access private
	*/
	var $m_arrAliasRanges = array(
	                              '0' => array('intStart'     => 200000,
	                                           'intEnd'       => 299999)  /*6 digit numbers begining with 2 are all ours*/
	                             );



	/**
	* Array of reserved ranges for aliases. A subset of our overall allowed ranges
	*
	* To add more reserved block in future, create another start-end pair to this array
	*
	* @var array
	* @access private
	*/
	var $m_arrReservedAliases = array(
	                                  array('intStart' => 200000,
	                                        'intEnd'   => 200999)  /*First 1000 reserved for internal staff numbers*/
	                                 );


	
	

	////////////////
	// Constructor
	////////////////

	
	/**
	* Contructor for the CPlusTalkRadiusUser class
	* 
	* @param int The service ID of the user
	* @return boolean success
	*/
	function CPlusTalkRadiusUser($intServiceID)
	{
		if($intServiceID <= 0) {
			return false;
		}
	
		$this->m_intServiceID = $intServiceID;
	
		$this->m_arrDetails = $this->prvGetRadiusUserDetails();

		return true;
	}



	//////////////
	// Accessors
	//////////////


	/**
	* Get the Radius User id
	*
	*
	* @access public
	* <AUTHOR>
	* @return integer The radius user's Id, or false if it cannot be found
	*/
	function getUserID()
	{
		if(! $this->exists()) {
			return false;
		}
		
		$dbhConn = get_named_connection_with_db('radius_voip');
		
		$strQuery="SELECT userid as intUserID
		             FROM voipusers
		            WHERE ispid = '{$this->m_arrDetails['intISPID']}'
		              AND username = '{$this->m_arrDetails['strUsername']}'";

		$refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Retrieve User id from voipusers');
		$intUserID = PrimitivesResultGet($refResult, 'intUserID');
	
		return ($intUserID > 0) ? $intUserID : false;
	}

	
	/**
	* Returns the SIP Address for this component
	*
	* @access public
	* @return string the SIP address
	*/
	function getSIPAddress()
	{
		$arrUserDetails = $this->prvGetSerUserDetails();
	
		return $arrUserDetails['strSIPAddress'];
	} 
	
	
	/**
	* Returns the SIP ID for this component
	*
	* @access public
	* @return string the SIP ID, or boolean false if one does not exist
	*/
	function getSIPID()
	{
		//Retrieve the SIP ID (SER alias)
		$arrDetails = $this->prvGetSerUserDetails();
		$strSerAlias = $this->prvSerAliasExists($arrDetails);
	
		return $strSerAlias;
	} 

	/////////////////////
	// Static Methods
	////////////////////



	////////////////////////
	// Public Methods
	////////////////////////
	

	/**
	* Create a user entry in RADIUS voip.voipusers
	*
	* If an entry already exists, it will be flagged active
	*
	* @access public
	* <AUTHOR>
	* @return boolean true on success, false on failure
	*/
	function createOrReactivate()
	{
		$uxtNow = time();


		$bolSuccess = $this->prvCreateSerUser();

		if(!$bolSuccess) {
			$this->setError(__FILE__, __LINE__, "Failed to reactivate user in SER");
			return (-1);
		}
		
		
		//If user exists, reactivate
		if($this->exists()) {
			if(! $this->prvIsActive()) {
				$this->prvReactivate();
			}
		} else {
			//No user exists - create a new one
			$dbhConn = get_named_connection_with_db('radius_voip');

			$intISPID     = $this->m_arrDetails['intISPID'];
			$strUsername  = addslashes($this->m_arrDetails['strUsername']);
			$intServiceID = $this->m_intServiceID;
			$strEncryptedPassword  = Crypt_Crypt::encrypt($this->m_arrDetails['strPassword'], 'voipusers');

			$strQuery = 'INSERT INTO voip.voipusers ' .
			            " SET ispid = '$intISPID', " .
			            "     username = '$strUsername', " .
			            "     password = '', " .
			            "     vchEncryptedPassword = '$strEncryptedPassword', " .
			            "     service_id = '$intServiceID', " .
			            '     active = "Y"';

			PrimitivesQueryOrExit($strQuery, $dbhConn, 'Insert a new user into RADIUS voip.voipusers');
		}

		$strAlias = $this->prvCreateRadiusAlias();
		
		return true;	
	}

	/**
	* Update user details
	*
	* Gets latest user details from userdata and updates SER user and RADIUS user
	*
	* @access public
	* <AUTHOR>
	* @return boolean true on success, false on failure with m_objError set
	*/
	function updateUserDetails()
	{
		//Update SER database
		
		$arrSerUser = $this->prvGetSerUserDetails();

		if(! $this->prvSerUserExists($arrSerUser))
		{
			$this->setError(__FILE__, __LINE__, "Cannot update.  User does not exist in ser.subscriber");
			return false;
		}
		
		//User found, so update
		$dbhConn = get_named_connection_with_db('ser');
		$uxtNow = time();

		$strQuery = "UPDATE ser.subscriber
		                SET password = '',
		                    vchEncryptedPassword = '".addslashes(Crypt_Crypt::encrypt($arrSerUser['strPassword'], 'subscriber'))."',
		                    first_name = '".addslashes($arrSerUser['strFirstName'])."',
		                    last_name = '".addslashes($arrSerUser['strLastName'])."',
		                    datetime_modified = FROM_UNIXTIME('$uxtNow')
		              WHERE username = '{$arrSerUser['strUsername']}'
		                AND domain = '{$arrSerUser['strDomain']}'";

		PrimitivesQueryOrExit($strQuery, $dbhConn, 'Update SER subscriber with latest data from userdata');


		//Update RADIUS voip database
		$objRadiusUser = new CPlusTalkRadiusUser($this->m_intServiceID);

		//If user exists, reactivate
		if(! $objRadiusUser->exists())
		{
			//Log error, but not critical for Phase1 so return success
			error_log("Cannot find radius user to update.");
			return true;
		}
		
		$objRadiusUser->update();
	
		return true;
	}
	
	
	
	/**
	* Deactivate a voip user entry in RADIUS
	*
	*
	* @access public
	* <AUTHOR>
	* @return true on success, exits on failure
	*/
	function deactivate()
	{
		$dbhConn = get_named_connection_with_db('radius_voip');
		
		$strQuery = "UPDATE voip.voipusers
						SET active = 'N'
		              WHERE ispid = '{$this->m_arrDetails['intISPID']}'
		                AND username = '{$this->m_arrDetails['strUsername']}'";

		PrimitivesQueryOrExit($strQuery, $dbhConn, 'Deactivate Radius user in voipusers');
		
		return true;
	}

	/**
	* Refresh radius user details from userdata
	*
	* @access public
	* <AUTHOR>
	* @return
	*/
	function update()
	{
		//User found, so update
		$dbhConn = get_named_connection_with_db('radius_voip');

		$strQuery = "UPDATE voip.voipusers
			SET password = '', 
			vchEncryptedPassword = '". Crypt_Crypt::encrypt($this->m_arrDetails['strPassword'], 'voipusers') ."'
			WHERE ispid = '{$this->m_arrDetails['intISPID']}'
			AND username = '{$this->m_arrDetails['strUsername']}'";

		PrimitivesQueryOrExit($strQuery, $dbhConn, 'Update RADIUS voip.voipusers with latest data from userdata');

	}

	/**
	* Check if there is a user entry in RADIUS voipusers
	*
	*
	*
	* @access public
	* <AUTHOR>
	* @param  array User details to check
	* @return boolean true if user exists, or false if not found
	*/
	function exists()
	{
		$dbhConn = get_named_connection_with_db('radius_voip');
		
		$strQuery="SELECT count(*) as intCount
		             FROM voipusers
		            WHERE ispid = '{$this->m_arrDetails['intISPID']}'
		              AND username = '{$this->m_arrDetails['strUsername']}'";

		$refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Check if voipusers entry exists');
		$intCount = PrimitivesResultGet($refResult, 'intCount');
	
		return ($intCount > 0) ? true : false;
	}



	//////////////////
	// Private Methods
	//////////////////
	
	
	//
	// SER user functions
	//


	/**
	* Get an array of user details for creating a new user in SER
	*
	*
	* @access private
	* <AUTHOR>
	* @return array User details for creating a new user in SER
	*/
	function prvGetSerUserDetails()
	{
		$arrSerNewUserDetails = array();

		//Get details for this service
		$arrService = userdata_service_get($this->m_intServiceID);
		$arrUser    = userdata_user_get($arrService['user_id']); 

		$strDomain = $this->prvGetUserDomain();
		
		if(CValidator::isEmailAddressFormat($arrUser['email']))
		{
			$strEmail = $arrUser['email'];
		}
		else
		{
			$strEmail = userdata_get_default_email(array('service_id' => $this->m_intServiceID));
		}

		//Now use it to create the data we need for SER
		$arrSerNewUserDetails['strUsername']      = PLUSTALK_USERNAME;
		$arrSerNewUserDetails['strDomain']        = $strDomain;
		$arrSerNewUserDetails['strPassword']      = $arrService['password'];
		$arrSerNewUserDetails['strSIPAddress']    = 'sip:'. PLUSTALK_USERNAME. '@'. $strDomain;
		$arrSerNewUserDetails['strFirstName']     = $arrUser['forenames'];
		$arrSerNewUserDetails['strLastName']      = $arrUser['surname'];
		$arrSerNewUserDetails['strEmailAddress']  = $strEmail;

		return $arrSerNewUserDetails;
	}


	
	/**
	* Fetch the user's domian from visp config for this service
	*
	* Returns the domain in the format 'jbloggs.plus.com'
	*
	* @access private
	* <AUTHOR>
	* @return string The user's domain for this service
	*/
	function prvGetUserDomain ()
	{
		$dbhConn = get_named_connection_with_db('userdata');
	
		$strQuery = "SELECT CONCAT( SUBSTRING( REPLACE( default_customer_email_address, 'username', s.username), LOCATE('@', default_customer_email_address) + 1) )
		                 AS strDomain
		               FROM userdata.services s
		         INNER JOIN products.visp_config vc
		                 ON s.isp = vc.isp
		                AND s.service_id = '{$this->m_intServiceID}'";

		$refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Fetch domain from visp config for this service');
		$strDomain = PrimitivesResultGet($refResult, 'strDomain');

		return $strDomain;

	}



	/**
	* Check if there is a user entry in SER
	*
	*
	*
	* @access private
	* <AUTHOR>
	* @return boolean true if user exists, or false if not found
	*/
	function prvSerUserExists($arrSerNewUserDetails)
	{
		$dbhConn = get_named_connection_with_db('ser');
		
		$strQuery="SELECT count(*) as intCount
		             FROM ser.subscriber s
		            WHERE s.username = '{$arrSerNewUserDetails['strUsername']}' 
		              AND s.domain = '{$arrSerNewUserDetails['strDomain']}'";

		$refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Check if subscribers entry exists');
		$intCount = PrimitivesResultGet($refResult, 'intCount');
	
		return ($intCount > 0) ? true : false;
	}




	/**
	* Create a user entry in SER
	*
	*
	*
	* @access private
	* <AUTHOR>
	* @return boolean true on success, false on failure
	*/
	function prvCreateSerUser()
	{
		$arrNewUser = $this->prvGetSerUserDetails();
		$uxtNow = time();
		
		//If user exists, reactivate
		if(! $this->prvSerUserExists($arrNewUser))
		{
			//No user exists - create a new one

			$dbhConn = get_named_connection_with_db('ser');

			$strQuery = "INSERT INTO ser.subscriber
								 SET phplib_id = '". md5(uniqid(rand())) ."',
									 username = '".addslashes($arrNewUser['strUsername'])."',
									 domain = '".addslashes($arrNewUser['strDomain'])."',
									 vchEncryptedPassword = '".Crypt_Crypt::encrypt($arrNewUser['strPassword'], 'subscriber')."',
									 password = '',
									 first_name = '".addslashes($arrNewUser['strFirstName'])."',
									 last_name = '".addslashes($arrNewUser['strLastName'])."',
									 email_address = '".addslashes($arrNewUser['strEmailAddress'])."',
									 datetime_created = FROM_UNIXTIME('$uxtNow'),
									 datetime_modified = FROM_UNIXTIME('$uxtNow')";

			PrimitivesQueryOrExit($strQuery, $dbhConn, 'Insert a new user into SER subscriber');

		}

		$strAlias = $this->prvCreateSerAlias($arrNewUser);

		if($strAlias === false)
		{	
			report_error($this->m_objError->getErrorMessage());
		}

		$this->prvCreateSerVoicemailUser($strAlias);
		
		$this->prvCreateSerDomain($arrNewUser);

		$this->prvCreateSerSpeedDial($arrNewUser['strUsername'], $arrNewUser['strDomain']);
		
		return true;
	}
	
	/**
	* Check if an alias already exists for a given user
	*
	*
	* @access private
	* <AUTHOR>
	* @param  array User details
	* @return string the existing alias, or boolean false if not found
	*/
	function prvSerAliasExists($arrUserDetails)
	{
		$dbhConn = get_named_connection_with_db('ser');
		
		$strQuery="SELECT username as strAlias
		             FROM ser.aliases
		            WHERE contact = '{$arrUserDetails['strSIPAddress']}'";

		$refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Check if alias entry exists for SIP address');
		$strAlias = PrimitivesResultGet($refResult, 'strAlias');
	
		return ($strAlias != '') ? $strAlias : false;
	}

	/**
	* Add entries for speed dials
	* 
	* @private
	* <AUTHOR>
	* @param  str Username
	* @param  str Domain
	*/
	function prvCreateSerSpeedDial($strUsername, $strDomain)
	{
		$dbhConnection = get_named_connection_with_db('ser');

		$strUsername = addslashes($strUsername);

		$strDomain = addslashes($strDomain);

		for ($intCount = 0; $intCount <= 99; ++$intCount)
		{
			$strCount = str_pad($intCount, 2, '0', STR_PAD_LEFT);

			$strQuery = 'SELECT count(*) as intNumRows ' .
			            ' FROM speed_dial ' .
			            " WHERE username = '$strUsername' " .
			            " AND domain = '$strDomain' " .
			            " AND sd_username = '$strCount'";

			$resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection, "Check for speed dial entry '$strCount' for $strUsername@$strDomain");

			$intNumRows = PrimitivesResultGet($resResult, 'intNumRows');

			if (!(isset($intNumRows) && $intNumRows > 0))
			{
				$strQuery = 'INSERT INTO speed_dial ' .
				            '    VALUES ("", ' .
				            "            '$strUsername', " .
				            "            '$strDomain', " .
				            "            '$strCount', " .
				            "            '$strDomain', " .
				            '            "", ' .
				            '            "", ' .
				            '            "", ' .
				            '            "")';

				PrimitivesQueryOrExit($strQuery, $dbhConnection, "Inserting speed dial entry '$strCount' for $strUsername@$strDomain");

			} // End if

		} // End for

	} // End of function prvCreateSerSpeedDial

	/**
	* Insert an alias for a SER user 
	*
	* Checks for existing alias before creating a new one
	*
	* @access private
	* <AUTHOR>
	* @param  array User details
	* @return string The new or existing alias, or boolean false on error
	*/
	function prvCreateSerAlias($arrUserDetails)
	{
		// Check if an entry already exists
		
		$strExistingSerAlias = $this->prvSerAliasExists($arrUserDetails);
		
		if($strExistingSerAlias !== false)
		{
			return $strExistingSerAlias;
		}
		
		//No existing entry, so create a new one
		
		$dbhConn = get_named_connection_with_db('ser');
		
		//Make other instances of this code wait til we get the latest ID and insert a new row.
		$bolLocked = primitives_get_lock('MAX_ALIAS_USERNAME', $dbhConn, 5, false);
	
		if(!$bolLocked)
		{
			$this->setError(__FILE__, __LINE__, "Failed to lock ser.aliases table with MySQL string lock 'MAX_ALIAS_USERNAME'");
			return false;
		}
	
		$strQuery = "SELECT MAX(username) as strMax FROM ser.aliases";

		$refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Get current maximum alias name');
		$strMax = PrimitivesResultGet($refResult, 'strMax');

		$intNextAlias = $this->prvGetNextUnreservedAlias(intval($strMax));
		$strUsername = sprintf("%d", $intNextAlias);
		
		$strQuery = "INSERT INTO ser.aliases
		                     SET username = '".addslashes($strUsername)."',
		                         domain = '".addslashes($arrUserDetails['strDomain'])."',
		                         contact = '".addslashes($arrUserDetails['strSIPAddress'])."'";

		PrimitivesQueryOrExit($strQuery, $dbhConn, 'Insert a new alias into SER aliases');

		$bolUnlocked = primitives_release_lock('MAX_ALIAS_USERNAME', $dbhConn, false);
	
		if(!$bolUnlocked)
		{
			//Log error, but keep going
			error_log("Failed to release MySQL string lock 'MAX_ALIAS_USERNAME' in ". __FILE__ . ' @ '. __LINE__);
		}
	
		return $strUsername;
	}


	/**
	* Check if a domain already exists for a given user
	*
	*
	* @access private
	* <AUTHOR>
	* @param  array User details
	* @return boolean true if found or false if not found
	*/
	function prvSerDomainExists($arrUserDetails)
	{
		$dbhConn = get_named_connection_with_db('ser');
		
		$strQuery="SELECT count(*) as intCount
		             FROM ser.domain
		            WHERE domain = '{$arrUserDetails['strDomain']}'";

		$refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Check if a domain entry exists for user');
		$intCount = PrimitivesResultGet($refResult, 'intCount');
	
		return ($intCount == 0) ? false : true;
	}


	/**
	* Insert a domain for a SER user 
	*
	* Checks for existing domain before creating a new one
	*
	* @access private
	* <AUTHOR>
	* @param  array User details
	* @return boolean true on success, exits on error
	*/
	function prvCreateSerDomain($arrUserDetails)
	{
		// Check if an entry already exists
		
		$bolDomainExists = $this->prvSerDomainExists($arrUserDetails);
		
		if($bolDomainExists)
		{
			return true;
		}
		
		//No existing entry, so create a new one
		
		$dbhConn = get_named_connection_with_db('ser');
		
		$strQuery = "INSERT INTO ser.domain
		                     SET domain = '".addslashes($arrUserDetails['strDomain'])."',
		                         last_modified = NOW()";

		PrimitivesQueryOrExit($strQuery, $dbhConn, 'Insert a new domain into SER domain');
	
		return true;
	}

	/**
	* 
	*
	* @access private
	* <AUTHOR>
	* @param  integer The current maximum alias in use in the database
	* @return integer The next available alias which is not reserved.
	*/
	function prvGetNextUnreservedAlias($intMax)
	{
		$intAlias = $intMax + 1;

		
		// Now check the range and any reserved blocks
		$bolAliasNeedsChecked = true;

		while($bolAliasNeedsChecked)
		{
			//Set the default range index
			$intRangeIndex = 0;

			if(count($this->m_arrAliasRanges) > 1)
			{
				//Check which number range to assign from
				foreach($this->m_arrAliasRanges as $k => $arrRange)
				{
					if ($intAlias >= $arrRange['intStart'] && $intAlias <= $arrRange['intEnd'])
					{
						//We are currently assigning from this range
						$intRangeIndex = $k;
						break;
					}
					elseif( isset($this->m_arrAliasRanges[$k+1]) && ($intAlias > $arrRange['intEnd'] && $intAlias < $this->m_arrAliasRanges[$k+1]['intStart']));
					{
						//We may have fallen between ranges so lets use the next one
						$intRangeIndex = $k + 1;
						break;	
					}
				}
			}

		
			//Just one pass through here, unless anything goes wrong.
			$bolAliasNeedsChecked = false;
			
			if($intAlias < $this->m_arrAliasRanges[$intRangeIndex]['intStart'])
			{
				//Go to the minimum start value
				$intAlias = $this->m_arrAliasRanges[$intRangeIndex]['intStart'];
			}

			//Check for reserved blocks
			foreach($this->m_arrReservedAliases as $arrBlock)
			{
				if($intAlias >= $arrBlock['intStart'] && $intAlias <= $arrBlock['intEnd'])
				{
					//Clashes with a reserved block. Try another one...
					$intAlias = $arrBlock['intEnd'] + 1;

					//retest
					$bolAliasNeedsChecked = true;
					break;
				}
			}
		}

		if( $intAlias >= PLUSTALK_ALIAS_WARN_LEVEL)
		{
			// We're running low on aliases
			// Raise a problem
			$strProblemComment = wordwrap("In ".__FILE__." on line ".__LINE__.
							  " the PlusTalk component has detected that there may be a low number of unused aliases remaining in the current range of aliases.");
			$strAutoProblemTag = 'plustalk_aliases_running_out';
			$strProblemTitle = 'PlusTalk Aliases running low in the current range';
			pt_raise_autoproblem($strAutoProblemTag, $strProblemTitle, $strProblemComment);
		}

		return $intAlias;
		
	}

	
	



	
	/**
	* Check if a SER user has a voicemail user entry 
	*
	*
	* @access private
	* <AUTHOR>
	* @param  string The alias to look up
	* @return boolean true if exists, false if not
	*/
	function  prvSerVoicemailUserExists($strAlias='')
	{
		if($strAlias == '')
		{
			return false;
		}
	
		$dbhConn = get_named_connection_with_db('ser');
		
		$strQuery="SELECT count(*) as intCount
		             FROM users
		            WHERE mailbox = '$strAlias'";

		$refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Check if users (voicemail) entry exists');
		$intCount = PrimitivesResultGet($refResult, 'intCount');
	
		return ($intCount > 0) ? true : false;
	}
	
	
	/**
	* Create an entry in the voicemail users table in SER 
	*
	*
	* @access private
	* <AUTHOR>
	* @param  string The alias to create an entry for
	* @return boolean true on success, false on error
	*/
	function prvCreateSerVoicemailUser($strAlias)
	{
		// Check if exists
		if(! $this->prvSerVoicemailUserExists($strAlias))
		{
			$dbhConn = get_named_connection_with_db('ser');
			
			$strQuery = "SELECT a.username as strUsername,
								s.email_address as strEmailAddress,
								CONCAT(CONCAT(s.first_name,' '), s.last_name) as strFullName
						   FROM ser.aliases a
					 INNER JOIN ser.subscriber s
							 ON a.contact = concat('sip:',concat(concat(s.username,'@'), s.domain))
						  WHERE a.username = '$strAlias'";

			$refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Fetch data for SER voicemail user');
			$arrVMUser = PrimitivesResultGet($refResult);
			
			if(!is_array($arrVMUser) || count($arrVMUser) == 0)
			{
				return false;
			}
	
			//Calculate context.  This must be changed every 5000 users to get round some linux limits on 14000 inodes.

			$strQuery = "SELECT COUNT(*) as intCount
			               FROM users";

			$refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Fetch number of voicemail users for context change');
			$intCount = PrimitivesResultGet($refResult, 'intCount');
						
			$strCount = (floor($intCount / 5000) > 0) ? sprintf("%d", floor($intCount / 5000)) : '';
			$strContext = 'default'. $strCount;
			
			$strEncryptedPin = Crypt_Crypt::encrypt(rand(1000, 9999), 'users');
			
			$strQuery = "INSERT INTO users
								 SET mailbox  = '".addslashes($arrVMUser['strUsername'])."',
									 password = '".addslashes($strEncryptedPin)."',
									 fullname = '".addslashes($arrVMUser['strFullName'])."',
									 context  = '".addslashes($strContext)."',
									 email    = '".addslashes($arrVMUser['strEmailAddress'])."'";

			PrimitivesQueryOrExit($strQuery, $dbhConn, 'Insert a new user into SER voicemail users');
		}

		return true;
	}

	/**
	* Get an array of user details for creating a new voip user in RADIUS
	*
	*
	* @access private
	* <AUTHOR>
	* @return array User details for creating a new voip user in RADIUS
	*/
	function prvGetRadiusUserDetails()
	{
		$arrUserDetails = array();

		//Get details for this service
		$arrService = userdata_service_get($this->m_intServiceID);
		$arrUser    = userdata_user_get($arrService['user_id']); 

		$strDomain = $this->prvGetDomain();
		
		//Get ISP id from voip.voipisp
		$dbhConn = get_named_connection_with_db('radius_voip');
		
		$strQuery = "SELECT ispid as intISPID
		               FROM voipisp
		              WHERE domain = '$strDomain'";

		$refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Fetch data for Radius voicemail user');
		$intISPID  = PrimitivesResultGet($refResult, 'intISPID');

		if($intISPID < 1)
		{
			error_log("Cannot find domain '$strDomain' in RADIUS voip.voipisp");
			
			$this->m_objError = new CError(__FILE__, __LINE__, "Cannot find domain '$strDomain' in RADIUS voip.voipisp. This MUST be added.");
			return false;
		}

		//Now create the data we need for RADIUS
		$arrUserDetails['intISPID']    = $intISPID;
		$arrUserDetails['strDomain']   = $strDomain;
		$arrUserDetails['strUsername'] = $arrService['username'];
		$arrUserDetails['strPassword'] = $arrService['password'];

		return $arrUserDetails;
	}

	/**
	 * Fetch the domian from visp config for this service
	 *
	 * Returns the domain in the format 'plus.com'
	 *
	 * @access private
	 * <AUTHOR>
	 * @return string The domain for this service
	 */
	function prvGetDomain ()
	{
		$dbhConn = get_named_connection_with_db('userdata');

		$strQuery = "SELECT CONCAT( SUBSTRING( default_customer_email_address, LOCATE('@username', default_customer_email_address) + 10) )
		                 AS strDomain
		               FROM userdata.services s
		         INNER JOIN products.visp_config vc
		                 ON s.isp = vc.isp
		                AND s.service_id = '{$this->m_intServiceID}'";

		$refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Fetch domain from visp config for this service');
		$strDomain = PrimitivesResultGet($refResult, 'strDomain');

		return $strDomain;

	}



	/**
	* Check if a voip user entry in RADIUS is active
	*
	* Checks the active flag
	*
	* @access private
	* <AUTHOR>
	* @param  array User details to check
	* @return boolean true if active, false if not active
	*/
	function prvIsActive()
	{
		$dbhConn = get_named_connection_with_db('radius_voip');
		
		$strQuery="SELECT active as strActive
		             FROM voip.voipusers
		            WHERE ispid = '{$this->m_arrDetails['intISPID']}'
		              AND username = '{$this->m_arrDetails['strUsername']}'";

		$refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Check if voipusers entry exists');
		$strActive = PrimitivesResultGet($refResult, 'strActive');
	
		return ($strActive == 'Y') ? true : false;
	}

	/**
	* Reactivate a voip user entry in RADIUS
	*
	*
	* @access private
	* <AUTHOR>
	* @param  array The details of the user to reactivate
	* @return boolean true on succes, exits on failure
	*/
	function prvReactivate()
	{
		$dbhConn = get_named_connection_with_db('radius_voip');
		
		$strQuery = "UPDATE voip.voipusers
						SET active = 'Y'
		              WHERE ispid = '{$this->m_arrDetails['intISPID']}'
		                AND username = '{$this->m_arrDetails['strUsername']}'";

		PrimitivesQueryOrExit($strQuery, $dbhConn, 'Reactivate Radius user in voipusers');
		
		return true;
	}


	/**
	* Create a user alias in RADIUS
	*
	*
	* @access private
	* <AUTHOR>
	* @param  array The details of the user to create an alias for
	* @return boolean true on succes, false on failure
	*/
	function prvCreateRadiusAlias()
	{
		$dbhConn = get_named_connection_with_db('radius_voip');

		$strQuery="SELECT userid as intUserID
		             FROM voip.voipusers
		            WHERE ispid = '{$this->m_arrDetails['intISPID']}'
		              AND username = '{$this->m_arrDetails['strUsername']}'";

		$refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Check if voipusers entry exists');
		$intUserID = PrimitivesResultGet($refResult, 'intUserID');
		
		if($intUserID < 1)
		{
			$this->m_objError = new CError(__FILE__, __LINE__,
			                              "Failed to find RADIUS voip user id for user '{$this->m_arrDetails['strUsername']}', domain '{$this->m_arrDetails['strDomain']}'");
			return false;
		}

		//See if an alias exists
		$strAlias = $this->prvGetRadiusAliasByUserID($intUserID);

		if($strAlias == false)
		{
			//Get the SER alias and copy it into the RADIUS alias table
			$arrSerUser = $this->prvGetSerUserDetails();
			$strAlias = $this->prvSerAliasExists($arrSerUser);
		
			if($strAlias === false)
			{
				$this->m_objError = new CError(__FILE__, __LINE__,
			                                   "Failed to find alias in SER aliases for u-ser '{$arrSerUser['strUsername']}', domain '{$arrSerUser['strDomain']}'");
				return false;
			}
	
		$strQuery = "REPLACE INTO voip.voip_aliases
								 SET userid  = '$intUserID',
			                         alias = '".addslashes($strAlias)."'";

			PrimitivesQueryOrExit($strQuery, $dbhConn, 'Insert a new alias into RADIUS voip.voip_aliases');
		}
		
		return true;
	}

	
	/**
	* Look up a radius alias by user id
	*
	*
	* @access private
	* <AUTHOR>
	* @param  integer The user id to look up
	* @return integer The alias if found, or boolean false if not found
	*/
	function prvGetRadiusAliasByUserID($intUserID)
	{
		if($intUserID < 1) {
			return false;
		}
	
		$dbhConn = get_named_connection_with_db('radius_voip');
		
		$strQuery="SELECT alias as intAlias
		             FROM voip.voip_aliases
		            WHERE userid = '$intUserID'";

		$refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Check if voip_aliases entry exists');
		$intAlias = PrimitivesResultGet($refResult, 'intAlias');
	
		return ($intAlias > 0) ? $intAlias : false;
	}

		/**
		* Update the voip database with the quota details 
		*
		* @access private
		* <AUTHOR>
		* @param  int Total Voicemail Storage Capacity (bytes)
		* @param  int Max PSTN (num concurrent lines)
		* @return bool
		*/
		function prvWriteVoipQuota($intTotalVoicemailStorageCapacity, $intMaxPSTN)
		{
			$arrVoipVoicemailQuota = $this->prvGetVoipQuota();

			if (isset($arrVoipVoicemailQuota['intVM']) && isset($arrVoipVoicemailQuota['intMaxPSTN']))
			{
				// No need to update if same
				if ($arrVoipVoicemailQuota['intVM'] == $intTotalVoicemailStorageCapacity &&
				    $arrVoipVoicemailQuota['intMaxPSTN'] == $intMaxPSTN)
				{
					return true;
				}

				return $this->prvUpdateVoipQuota($intTotalVoicemailStorageCapacity, $intMaxPSTN);
			}
			else
			{
				return $this->prvInsertVoipQuota($intTotalVoicemailStorageCapacity, $intMaxPSTN);
			}

		} // End of function prvWriteVoipQuota

		/**
		* Add an entry to the  Update the voip database with the Credit Details
		*
		* @access private
		* <AUTHOR>
		* @param  int Total Voicemail Storage Capacity (bytes)
		* @param  int Max PSTN (num concurrent lines)
		* @return bool
		*/
/*		function prvWriteVoipQuota($intPriority,
		                           $unkCredit,
		                           $uxtStart,
		                           $uxtEnd,
		                           $unkUsedCredit,
		                           $strActive, 
		                           $intVendorID,
		                           $strInclusive,
		                           $intRegion,
		                           $intPromotionID,
		                           $strCreditTypeHandle)
		{
			$intUserID = $this->getUserID();

			if (isset($intUserID) && $intUserID > 0)
			{
				// Check which credit type it is
				switch ($strCreditTypeHandle)
				{
					case 'PAYG' :

						$intCreditID = $this->getActiveVoipPAYGCreditID();

						if (isset($intCreditID) && $intCreditID > 0)
						{
							return $this->updateActiveVoipPAYGCredit($intCreditID, $unkCredit);
						}
						else
						{
							return $this->insertVoipCreditTable(
						}

					$intVendorID    = 0;
					$intRegion      = 0;
					$intPromotionID = 0;

				case 'INCLUDED_MINUTES' :

				case 'PROMO' :

			$arrVoipVoicemailQuota = $this->prvGetVoipQuota();

			if (isset($arrVoipVoicemailQuota['intVM']) && isset($arrVoipVoicemailQuota['intMaxPSTN']))
			{
				// No need to update if same
				if ($arrVoipVoicemailQuota['intVM'] == $intTotalVoicemailStorageCapacity &&
				    $arrVoipVoicemailQuota['intMaxPSTN'] == $intMaxPSTN)
				{
					return true;
				}

				return $this->prvUpdateVoipQuota($intTotalVoicemailStorageCapacity, $intMaxPSTN);
			}
			else
			{
				return $this->prvInsertVoipQuota($intTotalVoicemailStorageCapacity, $intMaxPSTN);
			}

		} // End of function prvWriteVoipQuota
*/
		/**
		* Get the quota details from the voip db
		*
		* <AUTHOR>
		* @access private
		* @return arr Quota details
		*/
		function prvGetVoipQuota()
		{
			$intUserID = $this->getUserID();

			if (isset($intUserID) && $intUserID > 0)
			{
				// Check whether user has a record or not
				$dbhConnection = get_named_connection_with_db('radius_voip');

				$strQuery = 'SELECT intVM, intMaxPSTN ' .
				            ' FROM tblQuota ' .
				            " WHERE userid = '$intUserID'";

				$resQuota = PrimitivesQueryOrExit($strQuery, $dbhConnection, 'Get the quota settings');

				$arrQuota = PrimitivesResultGet($resQuota);

				return $arrQuota;

			} // end if

			return false;

		} // End of function prvGetVoipQuota

		/**
		* Insert a new record to the voip quota table
		*
		* <AUTHOR>
		* @access private
		* @param  int Total Voicemail Storage Capacity (bytes)
		* @param  int Max PSTN
		* @return bool Success or Failure
		*/
		function prvInsertVoipQuota($intTotalVoicemailStorageCapacity, $intMaxPSTN)
		{
			$intUserID = $this->getUserID();

			if (isset($intUserID) && $intUserID > 0)
			{
				// Check whether user has a record or not
				$dbhConnection = get_named_connection_with_db('radius_voip');

				$strQuery = 'INSERT INTO tblQuota ' .
				            '           (userid, ' .
				            '            intVM, ' .
				            '            intMaxPSTN) ' .
				            "    VALUES ('$intUserID', " .
				            "            '$intTotalVoicemailStorageCapacity', " .
				            "            '$intMaxPSTN')";

				PrimitivesQueryOrExit($strQuery, $dbhConnection, 'Insert the voip quota settings');

				$intResult = PrimitivesAffectedRowsGet($dbhConnection);
				
				$this->prvWriteVoipUsageVoicemail();

				return $intResult;

			} // end if

			return false;

		} // End of function prvInsertVoipQuota

		/**
		* Update the voip quota settings 
		*
		* <AUTHOR>
		* @access private
		* @param  int Total Voicemail Storage Capacity (bytes)
		* @param  int Max PSTN
		* @return bool Success or Failure
		*/
		function prvUpdateVoipQuota($intTotalVoicemailStorageCapacity, $intMaxPSTN)
		{
			$intUserID = $this->getUserID();

			if (isset($intUserID) && $intUserID > 0)
			{
				// Check whether user has a record or not
				$dbhConnection = get_named_connection_with_db('radius_voip');

				$strQuery = 'UPDATE tblQuota ' .
				            "   SET intVM = '$intTotalVoicemailStorageCapacity', " .
				            "       intMaxPSTN = '$intMaxPSTN' " .
				            " WHERE userid = '$intUserID'";

				PrimitivesQueryOrExit($strQuery, $dbhConnection, 'Update the voip quota settings');

				$intResult = PrimitivesAffectedRowsGet($dbhConnection);
				
				$this->prvWriteVoipUsageVoicemail();

				return $intResult;

			} // end if

			return false;

		} // End of function prvUpdateVoipQuota

		/**
		* Set the voip database with the total voicemail storage capacity
		*
		* @access private
		* <AUTHOR>
		* @param  int Total Voicemail Storage Capacity (bytes)
		* @return bool
		*/
		function prvWriteVoipQuotaVoicemail($intTotalVoicemailStorageCapacity)
		{
			$arrVoipVoicemailQuota = $this->prvGetVoipQuota();

			if (isset($arrVoipVoicemailQuota['intVM']) && isset($arrVoipVoicemailQuota['intMaxPSTN']))
			{
				// No need to update if same
				if ($arrVoipVoicemailQuota['intVM'] == $intTotalVoicemailStorageCapacity)
				{
					return true;
				}

				return $this->prvUpdateVoipQuotaVoicemail($intTotalVoicemailStorageCapacity);
			}
			else
			{
				return $this->prvInsertVoipQuota($intTotalVoicemailStorageCapacity, 1);
			}

		} // End of function prvWriteVoipQuota

		/**
		* Update the voip quota settings voicemail setting
		*
		* <AUTHOR>
		* @access private
		* @param  int Total Voicemail Storage Capacity (bytes)
		* @return bool Success or Failure
		*/
		function prvUpdateVoipQuotaVoicemail($intTotalVoicemailStorageCapacity)
		{
			$intUserID = $this->getUserID();

			if (isset($intUserID) && $intUserID > 0)
			{
				// Check whether user has a record or not
				$dbhConnection = get_named_connection_with_db('radius_voip');

				$strQuery = 'UPDATE tblQuota ' .
				            "   SET intVM = '$intTotalVoicemailStorageCapacity' " .
				            " WHERE userid = '$intUserID'";

				PrimitivesQueryOrExit($strQuery, $dbhConnection, 'Update the voip quota settings');

				$intResult = PrimitivesAffectedRowsGet($dbhConnection);
				
				$this->prvWriteVoipUsageVoicemail();

				return $intResult;

			} // end if

			return false;

		} // End of function prvUpdateVoipQuota

		/**
		* Update the product id in the voipusers table
		*
		* @access private
		* <AUTHOR>
		* @param  int Service Component ID
		* @return bool Success or Failure
		*/
		function updateVoipUserProductID($intServiceComponentID)
		{
			$intUserID = $this->getUserID();

			if (isset($intUserID) && $intUserID > 0)
			{
				// Check whether user has a record or not
				$dbhConnection = get_named_connection_with_db('radius_voip');

				$strQuery = 'UPDATE voipusers ' .
				            "   SET intVPrdID = '$intServiceComponentID' " .
				            " WHERE userid = '$intUserID'";

				PrimitivesQueryOrExit($strQuery, $dbhConnection, 'Update the voipusers product id (service component id)');

				$intNumRows = PrimitivesAffectedRowsGet($dbhConnection);

				if (isset($intNumRows) && $intNumRows > 0)
				{
					return true;
				}

			} // end if

			return false;

		} // End of function updateVoipUserProductID

		
		/**
		* Get the usage details from the voip db
		*
		* <AUTHOR>
		* @access private
		* @return arr Usage details
		*/
		function prvGetVoipUsageVoicemail()
		{
			$intUserID = $this->getUserID();

			if (isset($intUserID) && $intUserID > 0)
			{
				// Check whether user has a record or not
				$dbhConnection = get_named_connection_with_db('radius_voip');

				$strQuery = 'SELECT intVM, intMaxPSTN ' .
				            ' FROM tblUsage ' .
				            " WHERE userid = '$intUserID'";

				$resUsage = PrimitivesQueryOrExit($strQuery, $dbhConnection, 'Get the usage settings');

				$arrUsage = PrimitivesResultGet($resUsage);

				return $arrUsage;

			} // end if

			return false;

		} // End of function prvGetVoipUsageVoicemail

		/**
		* Insert a new record to the voip usage table
		*
		* <AUTHOR>
		* @access private
		* @param  int Total Usage Usage Capacity (bytes)
		* @param  int Max PSTN
		* @return bool Success or Failure
		*/
		function prvInsertVoipUsageVoicemail($intTotalVoicemailUsageCapacity, $intMaxPSTN)
		{
			$intUserID = $this->getUserID();

			if (isset($intUserID) && $intUserID > 0)
			{
				// Check whether user has a record or not
				$dbhConnection = get_named_connection_with_db('radius_voip');

				$strQuery = 'INSERT INTO tblUsage ' .
				            '           (userid, ' .
				            '            intVM, ' .
				            '            intMaxPSTN) ' .
				            "    VALUES ('$intUserID', " .
				            "            '$intTotalVoicemailUsageCapacity', " .
				            "            '$intMaxPSTN')";

				PrimitivesQueryOrExit($strQuery, $dbhConnection, 'Insert the voip usage settings');

				return PrimitivesAffectedRowsGet($dbhConnection);

			} // end if

			return false;

		} // End of function prvInsertVoipUsageVoicemail

		/**
		* Set the voip database with the total voicemail storage capacity (Usage table)
		*
		* @access private
		* <AUTHOR>
		* @param  int Total Voicemail Usage Capacity (bytes)
		* @return bool
		*/
		function prvWriteVoipUsageVoicemail($intTotalVoicemailUsageCapacity = 0)
		{
			$arrVoipVoicemailUsage = $this->prvGetVoipUsageVoicemail();

			if (!(isset($arrVoipVoicemailUsage['intVM']) && isset($arrVoipVoicemailUsage['intMaxPSTN'])))
			{
				//set initial settings of usage
				return $this->prvInsertVoipUsageVoicemail(0, 0);
			}

		} // End of function prvWriteVoipUsageVoicemail

	} // End of class CPlusTalkRadiusUser

?>
