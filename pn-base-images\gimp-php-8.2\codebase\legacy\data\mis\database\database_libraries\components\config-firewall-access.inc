<?php

/**
 * Firewall configurator
 *
 * @package Components
 * <AUTHOR> <<EMAIL>>
 * @link    http://www.plus.net/
 */

require_once '/local/data/mis/common_library_functions/class_libraries/Firewall/CFirewallComponentController.php';

$global_component_configurators["423"] = "config_firewall_configurator";
$global_component_configurators["591"] = "config_firewall_configurator";

/**
 * Firewall configurator
 *
 * @param integer $intComponentID Component Id
 * @param string  $strAction      Action
 * @param string  $strHandle      Handle
 *
 * @return void
 */
function config_firewall_configurator($intComponentID, $strAction, $strHandle = '')
{
    $objFirewall = new CFirewallComponentController($intComponentID);

    switch ($strAction) {
        case "auto_configure":
            $objFirewall->autoConfigure();
            break;
        case "auto_disable":
            $objFirewall->autoDisable();
            break;
        case "auto_enable":
            $objFirewall->autoEnable();
            break;
        case "auto_refresh":
            $objFirewall->autoRefresh();
            break;
        case "auto_destroy":
            $objFirewall->autoDestroy();
            break;
        default:
            break;
    }
}
