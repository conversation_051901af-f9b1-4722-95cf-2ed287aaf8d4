<?php

/**
 * PlusNet Codebase Classes - CPage.php
 * @version $Id: CPage.php,v 1.3 2005-11-17 18:36:49 adoroshko Exp $
 * @see http://dokuwiki.internal.plus.net/
 *
 * @copyright PlusNet plc, www.plus.net
 *
 * This file is part of PlusNet Codebase Classes project.
 * Generated with ArgoUML PRE-0.19.5 on 07.11.2005, 12:59:34
 *
 * <AUTHOR> <<EMAIL>>
 */

/**
 * @include Smarty.php
 */

/* user defined includes */
// section 127-0-0-1-252b0afe:1076ab571c4:-7fed-includes begin
// section 127-0-0-1-252b0afe:1076ab571c4:-7fed-includes end

/* user defined constants */
// section 127-0-0-1-252b0afe:1076ab571c4:-7fed-constants begin
// section 127-0-0-1-252b0afe:1076ab571c4:-7fed-constants end

/**
 * TODO: Describe class CPage
 *
 * @access public
 * <AUTHOR> <<EMAIL>>
 */
class CSmartyPage
	extends Smarty
{
	// --- ATTRIBUTES ---

	// --- OPERATIONS ---

	/**
	 * TODO: Describe method CPage
	 *
	 * @access public
	 * <AUTHOR> Szulc, <<EMAIL>>
	 * @param string
	 * @return void
	 */
	function CSmartyPage($strTemplateDir = '')
	{
		// section 127-0-0-1-252b0afe:1076ab571c4:-7fe4 begin

		$this->Smarty();
		$this->compile_dir = SMARTY_COMPILE_DIR;
		$this->cache_dir = SMARTY_CACHE_DIR;
		$this->template_dir = $strTemplateDir;

		// section 127-0-0-1-252b0afe:1076ab571c4:-7fe4 end
	}

} /* end of class CPage */

?>
