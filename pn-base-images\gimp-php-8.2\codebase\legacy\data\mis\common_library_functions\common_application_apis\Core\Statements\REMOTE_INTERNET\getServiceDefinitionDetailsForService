server: itw
role: slave
rows: single
statement:

SELECT
    s.service_id,
    sd.service_definition_id,
    sd.name,
    sd.isp,
    sd.minimum_charge,
    sd.initial_charge,
    sd.type,
    sd.password_visible_to_support,
    sd.requires,
    sd.date_created,
    sd.end_date,
    sd.signup_via_portal,
    sd.blurb,
    IFNULL(ap.vchContract, 'MONTHLY') AS vchContract,
    NULL AS decLeadPrice,
    provProfile.vchName AS provisioningProfile
FROM  dbRemoteInternet.services AS s
INNER JOIN dbRemoteInternet.service_definitions AS sd
    ON (sd.service_definition_id = s.type)
LEFT JOIN dbRemoteInternet.adsl_product AS ap
    ON sd.service_definition_id = ap.service_definition_id
LEFT JOIN dbRemoteInternet.tblProvisioningProfile AS provProfile
    ON provProfile.intProvisioningProfileID = ap.intProvisioningProfileID
WHERE
    s.service_id = :intServiceId
