<?php
	
	/**
	* Abstract object base class - the root of all object heirachies.
	* 
	* Everything extends from this class
	*
	* @package    Core
	* @access     public
	* <AUTHOR> <<EMAIL>>
	* @version    $Id: CObject.inc,v 1.4 2006-07-04 04:52:33 fzaki Exp $
	* @filesource
	*/

	require_once('/local/data/mis/database/database_libraries/CoreObjects/CError/CError.inc');


/**
* CObject
* 
* Abstract object base class
*
* @access     public
* <AUTHOR> <<EMAIL>>
*/
class CObject
{
	/**
	* Error Object
	*
	* Created and set in the event of an error.
	*
	* @var CError
	* @access private
	*/
	var $m_objError = null;

	////////////////
	// Constructor
	////////////////

	
	function CObject()
	{
	
	}

	//////////////
	// Accessors
	//////////////

	function getError()
	{
		if(is_null($this->m_objError)) {
			return false;
		}
	
		return $this->m_objError;
	}

	function setError($strFile, $intLine, $strErrorMessage)
	{
		if($this->m_objError == null) {
			$this->m_objError = new CError($strFile, $intLine, $strErrorMessage);
		} else {
			$objError = $this->m_objError;
			$objError->setError($strFile, $intLine, $strErrorMessage);
		}
	}

	
}  


?>
