server: coredb
role: master
rows: single
statement:

UPDATE		userdata.services
SET
			user_id = :user_id,
			isp = :isp,
			username = :username,
			password = :password,
			cli_number = :cli_number,
			type = :type,
			status = :status,
			enddate = :enddate,
			next_invoice = :next_invoice,
			invoice_period = :invoice_period,
			next_invoice_warned = :next_invoice_warned,
			invoice_day = :invoice_day,
			authorised_switch_payment = :authorised_switch_payment,
			bolMailOptOut = :bolMailOptOut
WHERE
			service_id = :service_id