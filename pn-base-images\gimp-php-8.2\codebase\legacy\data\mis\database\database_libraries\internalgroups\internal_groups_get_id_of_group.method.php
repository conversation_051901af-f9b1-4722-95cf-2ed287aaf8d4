<?php

	/////////////////////////////////////////////////////////////////////
	// Function  : split_internal_groups_get_id_of_group
	// Purpose   : Gets the id of the specified group
	// Arguments : int $group_name
	// Returns   : intto $group_id
	/////////////////////////////////////////////////////////////////////
	function split_internal_groups_get_id_of_group($group_name, $thirdparty='php_lib')
	{
		$connection = get_named_connection("phplib");

		if($connection)
		{
			$query = mysql_query("SELECT group_id
			                        FROM ".$thirdparty.".group_details
			                       WHERE group_name = '$group_name'
			                     ", $connection);

			$result = mysql_fetch_array($query, MYSQL_ASSOC);

			if($result)
			{
				$group_id = $result["group_id"];
				return $group_id;
			} else {
				return false;
			}
		}
	}

?>