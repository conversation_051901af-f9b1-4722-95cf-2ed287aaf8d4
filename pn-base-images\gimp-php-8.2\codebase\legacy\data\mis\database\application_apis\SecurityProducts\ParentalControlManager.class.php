<?php
require_once SQL_PRIMITIVES_LIBRARY;
require_once PRODUCT_ACCESS_LIBRARY;
require_once USERDATA_ACCESS_LIBRARY;
require_once COMPONENT_DEFINES_LIBRARY;
require_once SECURE_TRANSACTION_ACCESS_LIBRARY;
require_once SMARTY_LIBRARY;

require_once dirname(__FILE__) . '/ParentalControlManagerException.class.php';
require_once DATABASE_LIBRARY_ROOT . 'components/CProduct.inc';
require_once dirname(__FILE__) . '/ParentalControlProductComponent.class.php';
require_once PRODUCT_ACCESS_LIBRARY;

require_once APPLICATION_API_ROOT . 'EmailHandler/EmailHandler.class.php';
require_once DATABASE_LIBRARY_ROOT . 'programme-tool-access.inc';

class ParentalControlManager
{

	/**
	 * Failure reporting ticket pool
	 *
	 */
	const CSC_TICKET_POOL = 'Customer Support Centre';

	/**
	 * Product component state
	 *
	 */
	const STATE_ACTIVE = 4;

	const STATE_DEACTIVE = 7;

	// To be modified.
	const EMAIL_SUBJECT = 'Parental Control account details';

	/**
	 * Component Id
	 *
	 * @var integer
	 */
	protected $intComponentId;

	/**
	 * Service Id
	 *
	 * @var integer
	 */
	protected $intServiceId;

	/**
	 * Array containing component information
	 *
	 * @var array
	 */
	protected $arrComponent = array();

	/**
	 * Array containing service information
	 *
	 * @var array
	 */
	protected $arrService = array();

	/**
	 * Base template directory
	 *
	 * @var string
	 */
	protected $strTemplateBaseDir = '/local/data/mis/portal_modules/BrightviewMyaccount/templates';

	/**
	 * Template object
	 *
	 * @var object
	 */
	protected $objTemplate;

	/**
	 * Array of component objects
	 *
	 * @var array
	 */
	private $arrComponentObjects = array();

	/**
	 * Array of product objects
	 *
	 * @var array
	 */
	private $arrProductObjects = array();

	/**
	 * Array of user details.
	 *
	 * @var array
	 */
	public $arrUserData;

	/**
	 * Constructor
	 *
	 */
	public function __construct() {
		$this->intComponentId = 0;
		$this->intServiceId = 0;
	}

	/**
	 * Set the Service Id
	 *
	 * @param integer $intServiceId Service Id
	 */
	public function setServiceId($intServiceId)
	{
		$this->intServiceId = $intServiceId;
	}

	/**
	 * Get the Service Id
	 *
	 * @return integer $intServiceId
	 */
	public function getServiceId()
	{
		return $this->intServiceId;
	}

	/**
	 * Set the component Id
	 *
	 * @param integer $intComponentId Component Id
	 */
	public function setComponentId($intComponentId)
	{
		$this->intComponentId = $intComponentId;
	}

	/**
	 * Get the service array
	 *
	 * @return array $arrService
	 */
	public function getService()
	{
		return $this->arrService;
	}

	/**
	 * Return customer security product
	 *
	 * @param int $intComponentId
	 *
	 * @return CSecurityProduct
	 * @throws BullGuardManagerException
	 */
	public function &getProduct($intComponentId = 0)
	{
		if (0 == $intComponentId) {

			$intComponentId = $this->intComponentId;
		}

		if (false == is_numeric($intComponentId) || 0 == $intComponentId) {

			throw new ParentalControlManagerException('Can\'t determine component id', ParentalControlManagerException::ERR_MISSING_DATA);
		}

		if (true == is_null($this->arrProductObjects[$intComponentId])) {

			$this->arrProductObjects[$intComponentId] = CComponent::createInstance($intComponentId);

			if (false == ($this->arrProductObjects[$intComponentId] instanceof ParentalControlProduct)) {

				throw new ParentalControlManagerException('Can\'t create ParentalControlProduct instance', ParentalControlManagerException::ERR_PRODUCT);
			}
		}

		return $this->arrProductObjects[$intComponentId];
	}

	/**
	 * Returns the component array
	 *
	 * @return array
	 */
	public function &getComponent($intComponentId = 0)
	{
		if (0 == $intComponentId) {

			$intComponentId = $this->intComponentId;
		}

		if (true == is_null($this->arrComponentObjects[$intComponentId])) {

			foreach ($this->getProduct($intComponentId)->getProductComponents() as $objComponent)
			{
				if (true == ($objComponent instanceof ParentalControlProductComponent)) {

					$this->arrComponentObjects[$intComponentId] = $objComponent;
					break;
				}
			}
		}

		return $this->arrComponentObjects[$intComponentId];
	}

	/**
	 * Get smarty template object
	 *
	 * @return CPage
	 */
	protected function getTemplate()
	{
		if(is_null($this->objTemplate)) {
			$this->objTemplate = new Smarty();
			$this->objTemplate->compile_dir = SMARTY_COMPILE_DIR;
			$this->objTemplate->compile_id = get_class($this);
			$this->objTemplate->template_dir = $this->strTemplateBaseDir;

			if($this->arrService) {
				$arrVispConfig = product_visp_config_get($this->arrService['isp']);
				$this->objTemplate->assign('strIspPortal', $arrVispConfig['portal_main_page_url']);
				$this->objTemplate->assign('strSupportHelpline', 'support_phone_number');
			}
		}

		return $this->objTemplate;
	}

	/**
	 * Create parental control account, before calling this user must set either service id or component id.
	 *
	 * @access public
	 * @return bool  true - Successful creation.
	 * @throws ParentalControlManagerException
	 */
	public function createAccount()
	{
		$objParentalControlProduct = null;
		$strStatus = 'noaccount';

		if (0 < $this->intServiceId) {

			$arrCriteria = array(
			'service_id'=> $this->intServiceId,
			'type'  	   => array (COMPONENT_PARENTAL_CONTROL),
			'status'	   => array (
					'queued-activate',
					'active',
					'queued-reactivate',
					'queued-deactivate',
					'deactive'
					)
			);

			$arrComponents = userdata_component_find($arrCriteria);

			if (empty($arrComponents)) {

				$strStatus = 'noaccount';
			}
			else {

				$strStatus = $arrComponents[0]['status'];
				$this->intComponentId = $arrComponents[0]['component_id'];
			}

		}
		elseif (0 < $this->intComponentId) {

			$this->populateDataByComponent($this->intComponentId);
			$strStatus = $this->arrComponent['status'];
		}
		else {

			throw new ParentalControlManagerException('Set either Service Id or compoment Id', ParentalControlManagerException::ERR_MISSING_DATA);
		}

		switch ($strStatus) {

			case 'active':
				throw new ParentalControlManagerException('User already have active parental control component.', ParentalControlManagerException::ERR_COMPONENT_EXIST);
			case 'unconfigured':
			case 'queued-activate':
			case 'queued-reactivate':
			case 'queued-deactivate':
			case 'deactive':
				$objParentalControlProduct = CProduct::createInstance($this->intComponentId);
				break;
			case 'noaccount':
				$objParentalControlProduct = CProduct::create($this->intServiceId, COMPONENT_PARENTAL_CONTROL);
				break;
			default:
				return false;
		}

		if (false == $objParentalControlProduct) {
			throw new ParentalControlManagerException('Can\'t create parental control product', ParentalControlManagerException::ERR_PRODUCT);
		}
		$this->intComponentId = $objParentalControlProduct->getComponentID();

		if (false == $objParentalControlProduct->enable()) {

			throw new ParentalControlManagerException('Can\'t configure parental control product', ParentalControlManagerException::ERR_PRODUCT);
		}

		return true;

	}

	/**
	 * Cancel parental control account, before calling this user must set either service id or component id.
	 *
	 * @access public
	 * @return bool  true - Successful cancellation.
	 * @throws ParentalControlManagerException
	 */
	public function cancelAccount()
	{
		$strStatus = '';

		if (0 < $this->intServiceId) {

			$arrCriteria = array(
			'service_id'=> $this->intServiceId,
			'type'  	   => array (COMPONENT_PARENTAL_CONTROL),
			'status'	   => array (
					'active',
					'queued-deactivate'
					)
			);

			$arrComponents = userdata_component_find($arrCriteria);

			if (empty($arrComponents)) {

				throw new ParentalControlManagerException('Products not in active or queued -deactive state', ParentalControlManagerException::ERR_MISSING_DATA);
			}
			else {

				$strStatus = $arrComponents[0]['status'];
				$this->intComponentId = $arrComponents[0]['component_id'];
			}
		}
		elseif (0 < $this->intComponentId) {

			$this->populateDataByComponent($this->intComponentId);
			$strStatus = $this->arrComponent['status'];
		}
		else {
			throw new ParentalControlManagerException('Set either Service Id or compoment Id', ParentalControlManagerException::ERR_MISSING_DATA);
		}

		switch ($strStatus) {

			case 'active':
			case 'queued-deactivate':
				$objParentalControlProduct = CProduct::createInstance($this->intComponentId);
				return $objParentalControlProduct->disable();
			case 'unconfigured':
			case 'queued-activate':
			case 'queued-reactivate':
			case 'deactive':
				throw new ParentalControlManagerException('Product not in active state', ParentalControlManagerException::ERR_MISSING_DATA);
			default:
				return false;
		}
	}

	/**
	 * Retrieve created parental control account information
	 *
	 * @access public
	 * @return array Associative array containing Parental control account information.
	 *
	 */
	public function getPCAccountInformation($bolGetDeactiveAccount = false)
	{
		$arrPCAccountInfo = array();

		if (0 == $this->intServiceId) {
			throw new ParentalControlManagerException('Service id not set', ParentalControlManagerException::ERR_MISSING_DATA);
		}
		elseif (0 == $this->intComponentId) {
			throw new ParentalControlManagerException('Component id not set', ParentalControlManagerException::ERR_MISSING_DATA);
		}

		$arrService = userdata_service_get($this->intServiceId);

		if (empty($arrService)) {
			throw new ParentalControlManagerException('No data found in service table', ParentalControlManagerException::ERR_MISSING_DATA);
		}

		$arrUser = userdata_user_get($arrService['user_id']);

		if ($bolGetDeactiveAccount = false) {
			$arrProductComponentInstanceIds = CProduct::getProductComponentInstanceIDs(array(), array(self::STATE_ACTIVE), $this->intComponentId);
		}
		else {
			$arrProductComponentInstanceIds = CProduct::getProductComponentInstanceIDs(array(), array(self::STATE_ACTIVE, self::STATE_DEACTIVE), $this->intComponentId);
		}
		if (empty($arrProductComponentInstanceIds)) {
			throw new ParentalControlManagerException('Parental Control component is not yet active', ParentalControlManagerException::ERR_MISSING_DATA);
		}
		$intProductComponentInstanceId =  isset($arrProductComponentInstanceIds[0]) ? $arrProductComponentInstanceIds[0] : 0;
		$objPCProductComponent = CProductComponent::createInstance($intProductComponentInstanceId);

		$arrPCAccountInfo['strUserName'] = $arrUser['email'];
		$arrPCAccountInfo['strPassword'] = $objPCProductComponent->getPassword();
		$arrPCAccountInfo['strLicenceKey'] = $objPCProductComponent->getLicenceKey();
		$arrPCAccountInfo['uxtContractEnd'] = $objPCProductComponent->getContractEnd();

		return $arrPCAccountInfo;

	}
	/**
	 * Get object related data by using service id.
	 *
	 * @access public
	 * @param integer $intServiceId
	 * @return bool
	 * @throws ParentalControlManagerException
	 */
	public function populateDataByService($intServiceId)
	{
		if (true == is_numeric($intServiceId) && 0 < $intServiceId) {

			$this->arrService = userdata_service_get($intServiceId);

			if(false == empty($this->arrService)) {

				$this->intServiceId = $this->arrService['service_id'];

				$arrCriteria = array(
					'service_id'	=> $this->intServiceId,
					'type'  		=> array(COMPONENT_PARENTAL_CONTROL),
					'status'		=> array (
							'unconfigured',
							'queued-activate',
							'queued-reactivate',
							'active',
							'queued-deactivate',
							'queued-deconfigure',
							'deactive',
							'queued-destroy',
							'destroyed',
							'invalid'
							)
					);
				$arrComponents = userdata_component_find($arrCriteria);
				$arrActiveStatuses = array('queued-activate', 'active');

				// Try find active product component
				foreach ($arrComponents as $arrComponent)
				{
					if (COMPONENT_PARENTAL_CONTROL == $arrComponent['component_type_id'] && true == in_array($arrComponent['status'], $arrActiveStatuses)) {

						$this->intComponentId = $arrComponent['component_id'];
						$this->arrComponent = $arrComponent;
						return true;
					}
				}

				// Next find the newest product component in any state
				foreach ($arrComponents as $arrComponent)
				{
					if (COMPONENT_PARENTAL_CONTROL == $arrComponent['component_type_id'] && (int)$this->arrComponent['uxtCreationDate'] < $arrComponent['uxtCreationDate']) {

						$this->intComponentId = $arrComponent['component_id'];
						$this->arrComponent = $arrComponent;
					}
				}

				if (false == empty($this->arrComponent)) {
					return true;
				}
				return false;
			}
		}

		throw new ParentalControlManagerException('Incorrect service id passed.', ParentalControlManagerException::ERR_MISSING_DATA);
	}

	/**
	 * Renews the Parental control component
	 *
	 * @access public
	 * @return bool. True when renewal successfully completed.
	 * @throws ParentalControlManagerException
	 */
	public function renewAccount()
	{
		if (0 == $this->intServiceId) {
			throw new ParentalControlManagerException('Service id not set', ParentalControlManagerException::ERR_MISSING_DATA);
		}

		$arrCriteria = array(
			'service_id'=> $this->intServiceId,
			'type'  	   => array (COMPONENT_PARENTAL_CONTROL),
			'status'	   => array (
					'active',
					'destroyed'
					)
			);

		$arrComponents = userdata_component_find($arrCriteria);
		$this->intComponentId = $arrComponents[0]['component_id'];
		$objParentalControlProduct = CProduct::createInstance($this->intComponentId);

		if (!$objParentalControlProduct->renew()) {
			return false;
		}
		return true;
	}

	/**
	 * Check whether Parental control can be activated
	 *
	 * @access public
	 * @return bool. True if parental control can be activated on the account.
	 */
	public function canActivateParentalControl()
	{
		if (($this->arrService['status'] != 'active') || (0 == product_service_is_allowed_component($this->arrService['type'], COMPONENT_PARENTAL_CONTROL))) {

			return false;
		}
		if (true == empty($this->arrComponent) || (!in_array($this->arrComponent['status'], array('active')))) {

		    return true;
		}

		return false;
	}

	/**
	 * Check whether Parental control can be deactivated
	 *
	 * @access public
     *
	 * @return bool. True if parental control can be deactivated on the account.
	 */
	public function canDeactivateParentalControl()
	{
		if (($this->arrService['status'] != 'active')) {
			return false;
		}

        if (!isset($this->arrComponent) || !isset($this->arrComponent['component_type_id'])) {
            return false;
        }

		if ($this->arrComponent['component_type_id'] == COMPONENT_PARENTAL_CONTROL
            && in_array($this->arrComponent['status'], array('active', 'queued-activate', 'queued-reactivate'))
        ) {
			return true;
		}

		return false;
	}

	/**
	 * Populate data across the object using component id, find exact one component.
	 *
	 * @access protected
	 * @param int $intComponentId
	 * @return bool
	 */
	public function populateDataByComponent($intComponentId)
	{
		if (true == is_numeric($intComponentId) && 0 < $intComponentId) {

			$this->arrComponent = userdata_component_get($intComponentId);

			if (false == empty($this->arrComponent)) {

				$this->intComponentId = $this->arrComponent['component_id'];
				$this->intServiceId = $this->arrComponent['service_id'];
				$this->arrService = userdata_service_get($this->arrComponent['service_id']);

				return true;
			}
		}
		return false;
	}

	/**
	 * Email the PC details to customers after creating the PC component
	 *
	 */
	public function emailPCAccountDetails($arrPCAccountInfo)
	{
		$strMailTemplate = 'ni_purchase_welcome';

		$arrExtraParams = array();
		$arrExtraParams['strNiLicence'] = $arrPCAccountInfo['strLicenceKey'];
		$arrExtraParams['strUsername'] = $arrPCAccountInfo['strUserName'];
		$arrExtraParams['strParentalPassword'] = $arrPCAccountInfo['strPassword'];

		try
		{
			$bolResult = EmailHandler::sendEmail($this->getServiceId(), $strMailTemplate, $arrExtraParams);
		}

		catch (EmailHandler_Exception $excEmailHandler)
		{
			pt_raise_autoproblem('EmailHandler generated an error', 
			                     'EmailHandler generated an error',
			                     $excEmailHandler->getMessage(),
			                     __FILE__);
			$bolResult = false;
		}

		return $bolResult;
	}
}
