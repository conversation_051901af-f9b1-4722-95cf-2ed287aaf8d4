<?php
/**
 * User DAO class
 * 
 * @package    Core
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <a<PERSON><PERSON><PERSON><EMAIL>>
 * 
 * @copyright  2008 PlusNet
 * @version    CVS: $Id: UserDao.class.php,v 1.3 2008-05-19 05:43:00 swestcott Exp $
 * @since      File available since 18/02/2007
 */

/**
 * User DAO class
 * 
 * <code>
 * <i>userdata.users</i> table
 * +---------------  +--------------------------+------+-----+-------------------+----------------+
 * | Field           | Type                     | Null | Key | Default           | Extra          |
 * +---------------  +--------------------------+------+-----+-------------------+----------------+
 * | user_id         | int(8) unsigned zerofill |      | PRI | NULL              | auto_increment |
 * | address_id      | int(8)                   |      | MUL | 0                 |                |
 * | customer_id     | int(8) unsigned          |      | MUL | 0                 |                |
 * | telephone       | varchar(30)              |      | MUL |                   |                |
 * | strEveningPhone | varchar(30)              | YES  |     | NULL              |                |
 * | fax             | varchar(20)              | YES  |     | NULL              |                |
 * | mobile          | varchar(20)              | YES  |     | NULL              |                |
 * | email           | varchar(255)             |      | MUL |                   |                |
 * | position        | varchar(30)              | YES  |     | NULL              |                |
 * | salutation      | varchar(6)               |      |     |                   |                |
 * | forenames       | varchar(40)              |      |     |                   |                |
 * | surname         | varchar(60)              |      | MUL |                   |                |
 * | hint_question   | varchar(255)             | YES  |     | NULL              |                |
 * | hint_answer     | varchar(100)             | YES  |     | NULL              |                |
 * | db_src          | varchar(4)               |      |     |                   |                |
 * | timestamp       | timestamp                | YES  |     | CURRENT_TIMESTAMP |                |
 * +---------------  +--------------------------+------+-----+-------------------+----------------+
 * </code>
 * 
 * @package    Core
 * <AUTHOR> Marek <<EMAIL>>
 * <AUTHOR> Kurylowicz <<EMAIL>>
 * @copyright  2008 PlusNet
 */

class Core_UserDao extends Db_Object implements Core_IUserDao
{
	/**
	 * @access protected
	 * @var string Primary key name
	 */
	protected $strPrimaryKey = 'user_id';

	protected $user_id = null;

	protected $address_id = null;

	protected $customer_id = null;

	protected $telephone = null;

	protected $strEveningPhone = null;

	protected $fax = null;

	protected $mobile = null;

	protected $email = null;

	protected $position = null;

	protected $salutation = null;

	protected $forenames = null;

	protected $surname = null;

	protected $hint_question = null;

	protected $hint_answer = null;

	protected $arrSetterFunctions = array(
		'user_id'         => 'UserId',
		'address_id'      => 'AddressId',
		'customer_id'     => 'CustomerId',
		'telephone'       => 'Telephone',
		'strEveningPhone' => 'EveningPhone',
		'fax'             => 'Fax',
		'mobile'          => 'Mobile',
		'email'           => 'Email',
		'position'        => 'Position',
		'salutation'      => 'Salutation',
		'forenames'       => 'Forenames',
		'surname'         => 'Surname',
		'hint_question'   => 'HintQuestion',
		'hint_answer'     => 'HintAnswer'
		);
		

	public function init() {}

	/**
	 * Fetch a single row from <i>userdata.users</i> table
	 * 
	 * @access public
	 * @static
	 *  
	 * @uses Db_Object::getObject()
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @param int    $intUserId
	 * @param string $strTransaction
	 * 
	 * @return Core_UserDao
	 */
	public static function get($intUserId, $strTransaction = Db_Manager::DEFAULT_TRANSACTION)
	{
		$strDebugSource = __CLASS__ . ' ' . __METHOD__;
		Dbg_Dbg::write("$strDebugSource: Fetching User details for Id $intUserId", 'Core');
		
		return parent::getObject(get_class(), $intUserId, $strTransaction);
	}

	/**
	 * Sets user ID
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @param int $intUserId
	 */
	public function setUserId($intUserId)
	{
		$this->user_id = $intUserId;
	}

	/**
	 * Sets address ID
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @param int $intAddressId
	 */
	public function setAddressId($intAddressId)
	{
		$this->address_id = $intAddressId;
	}

	/**
	 * Sets customer ID
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @param int $intCustomerId
	 */
	public function setCustomerId($intCustomerId)
	{
		$this->customer_id = $intCustomerId;
	}

	/**
	 * Sets daytime telephone number
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @param string $strTelephone
	 */
	public function setTelephone($strTelephone)
	{
		$this->telephone = $strTelephone;
	}

	/**
	 * Sets evening telephone number
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @param string $strTelephone
	 */
	public function setEveningPhone($strTelephone)
	{
		$this->strEveningPhone = $strTelephone;
	}

	/**
	 * Sets fax
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @param string $strFax
	 */
	public function setFax($strFax)
	{
		$this->fax = $strFax;
	}
	
	/**
	 * Sets mobile number
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @param string $strMobile
	 */
	public function setMobile($strMobile)
	{
		$this->mobile = $strMobile;
	}

	/**
	 * Sets email
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @param string $strEmail
	 */
	public function setEmail($strEmail)
	{
		$this->email = $strEmail;
	}

	/**
	 * Sets position
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @param string $strPosition
	 */
	public function setPosition($strPosition)
	{
		$this->position = $strPosition;
	}

	/**
	 * Sets salutation
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @param string $strSalutation
	 */
	public function setSalutation($strSalutation)
	{
		$this->salutation = $strSalutation;
	}

	/**
	 * Sets forenames
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @param string $strForenames
	 */
	public function setForenames($strForenames)
	{
		$this->forenames = $strForenames;
	}

	/**
	 * Sets surname
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @param string $strSurname
	 */
	public function setSurname($strSurname)
	{
		$this->surname = $strSurname;
	}

	/**
	 * Sets hint question
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @param string $strHintQuestion
	 */
	public function setHintQuestion($strHintQuestion)
	{
		$this->hint_question = $strHintQuestion;
	}

	/**
	 * Sets question's answer
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @param string $strHintAnswer
	 */
	public function setHintAnswer($strHintAnswer)
	{
		$this->hint_answer = $strHintAnswer;
	}

	/**
	 * Returns user ID
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @return int
	 */
	public function getUserId()
	{
		return $this->user_id;
	}

	/**
	 * Returns address ID
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @return int
	 */
	public function getAddressId()
	{
		return $this->address_id;
	}

	/**
	 * Returns customer ID
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @return int
	 */
	public function getCustomerId()
	{
		return $this->customer_id;
	}

	/**
	 * Returns daytime telephone number
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @return string
	 */
	public function getTelephone()
	{
		return $this->telephone;
	}

	/**
	 * Returns evening telephone number
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @return string
	 */
	public function getEveningPhone()
	{
		return $this->strEveningPhone;
	}

	/**
	 * Returns fax number
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @return string
	 */
	public function getFax()
	{
		return $this->fax;
	}

	/**
	 * Returns mobile number
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @return string
	 */
	public function getMobile()
	{
		return $this->mobile;
	}

	/**
	 * Returns email
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @return string
	 */
	public function getEmail()
	{
		return $this->email;
	}

	/**
	 * Returns position
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @return string
	 */
	public function getPosition()
	{
		return $this->position;
	}

	/**
	 * Returns salutation
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @return string
	 */
	public function getSalutation()
	{
		return $this->salutation;
	}

	/**
	 * Returns forenames
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @return string
	 */
	public function getForenames()
	{
		return $this->forenames;
	}

	/**
	 * Returns surname
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @return string
	 */
	public function getSurname()
	{
		return $this->surname;
	}

	/**
	 * Returns hint question
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @return string
	 */
	public function getHintQuestion()
	{
		return $this->hint_question;
	}

	/**
	 * Returns question's answer
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @return string
	 */
	public function getHintAnswer()
	{
		return $this->hint_answer;
	}
}
