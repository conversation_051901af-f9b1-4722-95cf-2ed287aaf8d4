<?php

interface Core_IAccountDao
{
    public function getAccountId();

    public function getCustomerId();

    public function getAddressId();

    public function getBalance();

    public function getTerms();

    public function getCreditLimit();

    public function getInvoiceType();

    public function setAccountId($intAccountId);

    public function setCustomerId($intCustomerId);

    public function setAddressId($intAddressId);

    public function setBalance($floBalance);

    public function setTerms($strTerms);

    public function setCreditLimit($intCreditLimit);

    public function setInvoiceType($invoiceType);
}