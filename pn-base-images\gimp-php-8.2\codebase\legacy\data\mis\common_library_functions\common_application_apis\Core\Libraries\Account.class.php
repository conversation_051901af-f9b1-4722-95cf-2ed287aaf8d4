<?php
/**
 * <p>Core_Account</p>
 * <p>Base Account class</p>
 *
 * @uses Core_AccountDao
 *
 * @package    Core
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @version    $Id: Account.class.php,v 1.2 2007-09-27 09:44:56 swestcott Exp $
 */
class Core_Account
{
    /**
     * .
     * @var Core_AccountDao
     */
    private $_accountDao = null;
    const ERR_ACCOUNT_DAO_EMPTY = 1;
    const ERR_ACCOUNTID_NOT_FOUND = 2;

    /**
     * Active card associated with the account
     *
     * @var IspPayments_CreditCardDetails
     */
    private $_creditDetails = null;

    /**
     * <p>Constructor</p>
     * <p>When <i>$intAccountId</i> provided it initialize
     * {@link Core_Account::$objDao} DAO
     * </p>
     *
     * @uses Financial_InvoiceDao::get()
     *
     * <AUTHOR> <<EMAIL>>
     *
     * @param int    $accountId
     * @param string $transactionName
     */
    public function __construct (
        $accountId = null,
        $transactionName = Db_Manager::DEFAULT_TRANSACTION
    )
    {
        if (isset($accountId)) {

            $this->_accountDao = Core_AccountDao::get(
                $accountId,
                $transactionName
            );
        }
    }

    /**
     * Gives the active credit details object. If no active credit card details
     * are present it will return null
     *
     * @param string $transactionName
     * @return IspPayments_CreditCardDetails
     */
    public function getCreditDetails($transactionName = Db_Manager::DEFAULT_TRANSACTION)
    {
        if (empty($this->_creditDetails)) {

            $creditDetailsId = IspPayments_CreditCardDetails::getActiveCreditCardDetailsIdByAccountId(
                $this->_accountDao->getAccountId()
            );

            if (empty($creditDetailsId)) {

                return null;
            }

            $this->_creditDetails = new IspPayments_CreditCardDetails($creditDetailsId, $transactionName);
        }

        return $this->_creditDetails;
    }

    /**
     * Takes care of all calls to setters and getters of DAO object
     *
     * @uses Core_Account::_accountDao
     *
     * <AUTHOR> Marek" <<EMAIL>>
     * <AUTHOR> Borodaenko" <<EMAIL>>
     *
     * @param string    $method Method name
     * @param array     $params Params passed to method called
     */
    public function __call ($method, $params)
    {
        if (preg_match('/^(get|set)/', $method)) {

            return call_user_func_array(array($this->_accountDao, $method),
            $params);
        }

        throw new Exception(
            "Method does not exist: " . get_class($this) . '::' . $method
        );
    }

    /**
     * Calls write method of DAO object
     *
     * @param Core_IAccountDao
     */
    public function write ()
    {
        return $this->_accountDao->write();
    }

    /**
     * Sets {@link Core_Account::_accountDao} DAO
     *
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @param Core_IAccountDao $accountDao
     */
    public function setAccountDao(Core_IAccountDao $accountDao)
    {
        $this->_accountDao = $accountDao;
    }

    /**
     * Reduces account balance with value specified in <i>$floReductionAmount</i>
     *
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @param float $reductionAmount
     */
    public function reduceBalance($reductionAmount)
    {
        $currentBalance = $this->_accountDao->getBalance();
        $this->_accountDao->setBalance($currentBalance - $reductionAmount);
    }

    /**
     * Gets account ID for given user ID
     *
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @uses Core_Account::getAccountIdUsingAddress()
     * @uses Core_Account::getAccountIdWithoutAddress()
     *
     * @param int $userId
     * @param string $dbTransactionName
     *
     * @return int
     * @throws Core_AccountException
     */
    static public function getAccountIdByUserId(
        $userId,
        $dbTransactionName = Db_Manager::DEFAULT_TRANSACTION
    )
    {
        $debugSource = __CLASS__ . ' ' . __METHOD__;
        Dbg_Dbg::write(
            "$debugSource: Looking for a valid account ID for user $userId",
            'Core'
        );

        $accountId = self::getAccountIdUsingAddress(
            $userId,
            $dbTransactionName
        );

        if (empty($accountId)) {

            $accountId = self::getAccountIdWithoutAddress(
                $userId,
                $dbTransactionName
            );
        }

        if (empty($accountId)) {

            throw new Core_AccountException(
                "$debugSource: Cannot find account ID for the user $userId",
                self::ERR_ACCOUNTID_NOT_FOUND
            );
        }

        return $accountId;
    }

    /**
     * Gets account ID for given user ID without checking
     * address relation in database
     *
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @uses Db_Manager::getAdaptor()
     * @uses getAccountIdWithoutAddress
     * @param int $userId
     * @param string $dbTransactionName
     *
     * @return int
     */
    static private function getAccountIdWithoutAddress(
        $userId,
        $dbTransactionName = Db_Manager::DEFAULT_TRANSACTION
    )
    {
        $dbAdaptor = Db_Manager::getAdaptor('Core', $dbTransactionName);

        $accountId = $dbAdaptor->getAccountIdWithoutAddress($userId);

        $debugSource = __CLASS__ . ' ' . __METHOD__;
        Dbg_Dbg::write(
            "$debugSource: Account ID founded: " .
            (empty($accountId) ? 'No' : $accountId),
            'Core'
        );

        return $accountId;
    }

    /**
     * Gets account ID for given user ID checking address relation in database
     *
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @uses Db_Manager::getAdaptor()
     * @uses getAccountIdUsingAddress
     *
     * @param int $userId
     * @param string $dbTransactionName
     *
     * @return int
     */
    static private function getAccountIdUsingAddress(
        $userId,
        $dbTransactionName = Db_Manager::DEFAULT_TRANSACTION
    )
    {
        $accountId = null;

        $adaptor = Db_Manager::getAdaptor('Core', $dbTransactionName);
        $accountIds = $adaptor->getAccountIdUsingAddress($userId);

        $isEmptyResult = empty($accountIds);

        if (!$isEmptyResult) $accountId = $accountIds[0];

        $debugSource = __CLASS__ . ' ' . __METHOD__;
        Dbg_Dbg::write(
            "$debugSource: Account ID founded: " .
            ($isEmptyResult ? 'No' : $accountId),
            'Core'
        );

        return $accountId;
    }
}
