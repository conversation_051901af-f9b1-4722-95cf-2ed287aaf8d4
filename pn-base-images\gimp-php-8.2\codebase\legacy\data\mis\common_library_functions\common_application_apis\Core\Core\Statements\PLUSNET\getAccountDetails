server: coredb
role: slave
rows: single
statement:

SELECT
    a.account_id,
    a.customer_id,
    a.address_id,
    cd.credit_details_id,
    cd.card_type,
    cd.card_expiry_date AS expiry_date,
    a.balance,
    a.terms,
    a.credit_limit,
    a.vchInvoiceType,
    cd.vchCheckDigits,
    cd.intPaymentInstrumentRef
FROM
    userdata.accounts a
LEFT JOIN dbCreditDetails.credit_details cd
    ON a.account_id = cd.intAccountId 
AND cd.intCreditDetailsStatusId = (SELECT intCreditDetailsStatusId FROM dbCreditDetails.tblCreditDetailsStatus WHERE vchHandle = 'ACTIVE')
WHERE
    a.account_id = :intAccountId
