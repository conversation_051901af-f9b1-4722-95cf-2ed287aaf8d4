<?php
/**
 *
 * Inside the mailauth database, we have a table that describes per-alias configuration options,
 * such as whether the service is paid for, whether webmail is enabled, etc.
 * 
 * These options are configured at high-level by the Mail_BrightviewApi class and by the Mail_MailAuth
 * class at a low-level.
 * 
 * To avoid lots of copy-paste code in the higher-level classes, the class Mail_MailAuthOptionIterator
 * is used to iterate over each mail alias and configure an appropriate option (or set of options)
 * 
 * The Mail_MailAuthOptionIterator class then uses whichever object was supplied to it during
 * instantiation to configure those options.
 * 
 * The Mail_MailAuthOptionIterator class has no idea how to configure each option (as each one has
 * it's own unique way of doing it) so it asks the object that *does know* to perform
 * this action instead.
 * 
 * This is the purpose of the Mail_IMailAuthOptionConfig interface. Classes that need
 * to configure the mail options which are invoked by Mail_MailAuthOptionIterator implement
 * the Mail_IMailAuthOptionConfig, so that they expose one public method: configure()
 *
 */
interface Mail_IMailAuthOptionConfig
{
	public function configure(Mail_Service $service, Mail_MailAuth $mailAuth, Mail_MailDelivery $mailDelivery, Int $authId);
}

