<?php

	define('SMARTY_COMPILE_DIR', SMARTY_ENGINE_DIR . 'SmartyCompile/');
	define('SMARTY_CACHE_DIR', SMARTY_ENGINE_DIR . 'SmartyCache/');
	define('SMARTY_TEMPLATES_DIR', SMARTY_ENGINE_DIR . 'SmartyTemplates/');
	define('SMARTY_CONFIG_DIR', SMARTY_ENGINE_DIR . 'SmartyConfigs/');

	/*if (!class_exists('Smarty'))
	{
		require_once SMARTY_DIR . 'Smarty.class.php';
	}*/

	if (!(isset($bolFrameworkInclude) && $bolFrameworkInclude === true))
	{
		require_once '/local/data/mis/database/class_libraries/Templates/CPage.php';
		require_once '/local/data/mis/database/class_libraries/Templates/CPortalPage.php';
		require_once '/local/data/mis/database/class_libraries/Templates/CWpPage.php';

		// Include vISP-specific versions of the Smarty class here so that it's as transparent
		// as possible with still using ' require_once(SMARTY_LIBRARY) '
		require_once COMMON_LIBRARY_ROOT . 'Smarty/CMetronetSmarty.class.php';
	}

?>
