<?php
	/**
	* This file declares the CEvent class 
	*
	* @package    Core 
	* @subpackage Error Logging
	* <AUTHOR>
	* @version    $Id: CEvent.inc,v 1.3 2005-09-28 09:01:39 cblack Exp $ 
	* @filesource
	*/

	class CEvent
	{

		/**
		* Array of Event types
		*
		* @var array
		* @access private
		*/
		var $m_arrEventTypes = array();


		/**
		* Contructor for the CEvent class
		* 
		* <AUTHOR>
		* @return boolean success
		*/
		
		function CEvent()
		{
		
			return true;
		}

	
		/**
		* Returns the event types set
		*
		* @access public
		* <AUTHOR>
		* @return array of event types set
		*/
		function getEventTypes()
		{
			if(count($this->m_arrEventTypes) == 0)
			{
				return FALSE;
			}
			
			return $this->m_arrEventTypes;
		}


	}  

?>
