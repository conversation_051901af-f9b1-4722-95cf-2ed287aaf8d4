<?php
require_once(USERDATA_ACCESS_LIBRARY);
require_once(DOMAIN_ACCESS_LIBRARY);
require_once '/local/data/mis/database/crypt_config.inc';

/**
 * MailDb database gateway
 * This class is responsible for talking to Mail Database on the Network's side
 * All the mailbox administration processes that require Mail DB to be performed
 * are described as methods in this class
 */

class Mail_MailApi
{
    const ALIAS_PATTERN = '/^[a-z0-9_.-]{1,45}$/';

    const EMAIL_PATTERN = '/^[-0-9a-zA-Z._+&]+@([-0-9a-zA-Z]+[.])+[a-zA-Z]{2,6}$/';

    /**
     * String ironport localpart entry for its extra catchall entry
     */
    const IRONPORT_CATCHALL_LOCALPART = 'catchall.email';

    const IRONPORT_SAVE = 1;

    const IRONPORT_DELETE = 0;

    /**
     * As part of the mail platform migration the filesystem is being changed
     * this is a permamnent change that requires the tblMailboxes.GID and
     * tblMailboxes.UID to be set to 25 for ALL users across ALL visps
     */
    const GID = 'mail';
    const UID = 'mail';

    /**
     * Used to define what we class as a large customer that we
     * cannot but into Ldap/IronPort
     *
     * @var int
     */
    const LDAP_THRESHOLD = 300;

    /**
     * arrOrgs - Maps filter id's to orgs. This will only be the first section, the TLK will be left off
     * The TLK will be appended to end of these.
     * @var array
     * @access protected
     */
    protected $arrOrgs = array(
        '1'  => 'pnn_', //Spam Off - BSB OFF
        '2'  => 'pn_', //Spam Off - BSB ON
        '3'  => 'pnqn_', //Spam On - Quarantine On  - BSB Off
        '4'  => 'pnn_', //Spam On - deliver to mailbox  - BSB Off
        '5'  => 'pnn_', //Spam On - Deliver spam folder -  BSB Off
        '6'  => 'pnqb_', //Spam On - Quarantine On - BSB ON
        '7'  => 'pn_', ///Spam On - Deliver to mailbox  BSB ON
        '8'  => 'pn_', //Spam On - Deliver to spam folder - BSB ON
    );


    protected $strDefaultSpamUser = 'postini.default';

    /**
     * arrApprovedSenders
     *
     * @var array
     * @access protected
     */
    protected $arrApprovedSenders = array();

    /**
     * Mailbox object
     */
    protected $_objMailbox = null;

    /**
     * Db access object
     */
    protected $_objDb = null;

    private $_intMailboxId = null;
    private $_intMailUserId = null;
    private $_arrVirtualDomains = array();

    /**
     * MailDB Mailbox types
     */
    private $_arrMailboxTypes = null;

    /**
     * A mapping between userdata.config_email.type and dbMailDelivery.tblMailboxTypes
     */

    private $_arrMapping = array('default'  => 'mailbox',
        'mailbox'  => 'mailbox',
        'redirect' => 'redirect',
        'list'     => 'mailinglist');

    private $_arrSpamFilters = array('OFF'    => 0,
        'MARK'   => 1,
        'MOVE'   => 2,
        'DELETE' => 3);

    /**
     * Has user been migrated to Ironport yet
     * REMOVE AFTER IRONPORT MIGRATION COMPLETION
     */
    private $_bolIronPortUser = NULL;

    /**
     * array - ironport friendly domain spam settings
     */
    private $_arrIronPortDomainSettings = NULL;

    /**
     * Array of IronPort_MailBox Instance
     */
    private $arrObjIronPortMailboxes = array();

    /**
     * boolean used to store whether we are including new signups in ironport
     */
    private $_bolNewIronPortUser = NULL;

    /**
     * boolean used to store whether user has a large (unmanageable) amount of configs in ironport
     */
    private $_bolLargeIronPortUser = FALSE;

    /**
     * stores original data that should be in ldap for large user
     */
    private $_arrLargeUserIronPortConfigs = NULL;

    /**
     * array of handle=>id indicating what type of spam checking a user has (ironport,none,etc)
     */
    private $_arrMailFilterTypes = array();

    /**
     * service id of the reseller of end users where the reseller owns a domain
     */
    protected $_intPartnerDomainOwnerServiceID = NULL;

    /**
     * Constructor, initializes Mailbox object and Db Access Object
     *
     * @param Mail_MailDb mailbox
     */
    public function __construct(Mail_Mailbox $objMailbox)
    {
        $this->_objMailbox = $objMailbox;
        $this->_objDb = Mail_Db::getInstance();
        if (!is_object($this->_objDb)) {
            throw new Mail_Exception('Db object cannot be created');
        }

        // Get the mailbox types
        $strQuery = "SELECT vchHandle,MailboxTypeID FROM vblMailboxTypes";
        $arrTypes = $this->_objDb->getArray($strQuery, 'maildelivery');
        if (is_array($arrTypes)) {

            foreach ($arrTypes as $arrType) {

                $this->_arrMailboxTypes[$arrType['vchHandle']] = $arrType['MailboxTypeID'];
            }
        }

        // is there a reseller domain?
        if ($this->_objMailbox->isp == 'partner') {

            if (!is_null(Reseller_DomainHelper::getByServiceId(new Int($this->getServiceId())))) {

                $objCustomerHelper = Reseller_CustomerHelper::getByServiceId(new Int($this->getServiceId()));
                if (!is_null($objCustomerHelper) && !is_null($objCustomerHelper->getPartnerServiceId())) {
                    $this->_intPartnerDomainOwnerServiceID = $objCustomerHelper->getPartnerServiceId()->getValue();
                } else {
                    // maybe they are a pending actor due this being called from signup??!!
                    $signuphelper = Reseller_SignupHelper::findPendingActor(new Val_Username($this->_objMailbox->__get('username')));
                    if (!is_null($signuphelper)) {
                        $this->_intPartnerDomainOwnerServiceID = $signuphelper->getExternalUserId();
                    } else {
                        error_log("MailApi Warning: couldnt find Partner Service ID for a Partner End User (End User Service id = {$this->getServiceId()} End User's Partner Service id = ?)");
                        $this->_intPartnerDomainOwnerServiceID = $this->getServiceId();
                    }
                }
            }
        }

        // check if user has been migrated to ironport yet
        // REMOVE AFTER IRONPORT MIGRATION COMPLETION
        $this->_setMailFilterTypes();
        $this->_setIsIronPortUser();

    }

    /**
     * _setMailFilterTypes sets object member variable with handle=>id indicating what type of spam checking a user has (ironport,none,etc)
     *
     * @access protected
     * @return void
     */
    protected function _setMailFilterTypes()
    {
        $strQuery = 'SELECT intMailFilterTypeId,vchHandle FROM tblMailFilterType';
        $arrTypes = $this->_objDb->getArray($strQuery, 'maildelivery');
        foreach ($arrTypes as $arrType) {
            $this->_arrMailFilterTypes[$arrType['vchHandle']] = $arrType['intMailFilterTypeId'];
        }
    }

    /**
     * getIsIronPortUser check whether user has been migrated from Postini to Ironport
     * REMOVE AFTER IRONPORT MIGRATION COMPLETION
     *
     * @access public
     * @return boolean
     */
    public function getIsIronPortUser()
    {
        return $this->_bolIronPortUser;
    }

    /**
     * setIsIronPortUser check whether user has been migrated from Postini to Ironport
     * REMOVE AFTER IRONPORT MIGRATION COMPLETION
     *
     * @access protected
     * @param Boolean Optional $bolForce default FALSE - whether to get a fresh copy of users settings
     * @return void
     */
    protected function _setIsIronPortUser($bolForce = FALSE)
    {
        if (FALSE === isset($this->_bolIronPortUser) || TRUE === $bolForce) {

            $intServiceId = $this->getDomainOwnerServiceId();
            $strQuery = 'SELECT ' .
                '  COUNT(1) as intIsIronPortUser ' .
                'FROM ' .
                '  dbMailDelivery.tblMailFilterSettings mfs ' .
                'INNER JOIN dbMailDelivery.tblVirtualDomains vd ' .
                '  ON mfs.intVirtualdomainId = vd.VirtualDomainID ' .
                'WHERE ' .
                '  vd.ServiceID = ' . $intServiceId . ' ' .
                '  AND vd.ISP = "' . $this->getISP() . '" ' .
                '  AND mfs.intMailFilterTypeId IN(' . $this->_arrMailFilterTypes['PN_IRONPORT'] .
                '  , ' . $this->_arrMailFilterTypes['CLOUDMARK'] . ')';

            $this->_bolIronPortUser = $this->_objDb->getSingleValue($strQuery, 'intIsIronPortUser', 'maildelivery') >= 1 ? TRUE : FALSE;

            // uncomment this to allow new signups to be entered into ironport
            $strQuery = 'SELECT ' .
                '  COUNT(1) as intIsIronPortUser ' .
                'FROM ' .
                '  dbMailDelivery.tblMailFilterSettings mfs ' .
                'INNER JOIN dbMailDelivery.tblVirtualDomains vd ' .
                '  ON mfs.intVirtualdomainId = vd.VirtualDomainID ' .
                'WHERE ' .
                '  vd.ServiceID = ' . $intServiceId . ' ' .
                '  AND vd.ISP = "' . $this->getISP() . '" ';

            $this->_bolNewIronPortUser = $this->_objDb->getSingleValue($strQuery, 'intIsIronPortUser', 'maildelivery') == 0 ? TRUE : FALSE;
        }
    }

    /**
     * _setIronPortDomainSettings populate ironport friendly domain spam settings
     *
     * @access protected
     * @return void
     */
    protected function _setIronPortDomainSettings()
    {
        // get all user's domain ids
        $arrVDomIDs = $this->_getVirtualDomains();

        if (count($arrVDomIDs) < 1) {
            $this->_arrIronPortDomainSettings = array();
            return;
        }

        $intServiceId = $this->getDomainOwnerServiceId();

        // get all Address Filters by domain
        $strQuery = 'SELECT ' .
            '  vd.Domain, ' .
            '  af.vchName, ' .
            '  CASE(bolApprove) ' .
            '    WHEN 0 THEN "BLACKLIST" ' .
            '    ELSE "WHITELIST" ' .
            '  END AS vchFilterType ' .
            'FROM ' .
            '  tblAddressFilter af ' .
            'INNER JOIN tblVirtualDomains vd ' .
            '  ON af.intVirtualDomainID = vd.VirtualDomainID ' .
            'WHERE ' .
            '  vd.VirtualDomainID IN (' . implode(',', $arrVDomIDs) . ') ' .
            '  AND vd.ServiceID = ' . $intServiceId . ' ' .
            '  AND vd.Blackhole = 0 ';

        $arrFilters = $this->_objDb->getArray($strQuery, 'maildelivery');

        $arrTmpMailFilters = array();

        if (count($arrFilters) > 0) {

            foreach ($arrFilters as $arrFilter) {

                $arrTmpMailFilters[$arrFilter['Domain']][$arrFilter['vchFilterType']][] = $arrFilter['vchName'];
            }
        }

        // get basic spam/virus settings per domin, per mailbox
        $strQuery = 'SELECT ' .
            '  CASE(mfs.bolSpam) ' .
            '    WHEN 1 THEN "FALSE" ' .
            '    ELSE "TRUE" ' .
            '  END AS AmavisBypassSpamChecks, ' .
            '  CASE(mfs.bolAntiVirus) ' .
            '    WHEN 1 THEN "FALSE" ' .
            '    ELSE "TRUE" ' .
            '  END AS AmavisBypassVirusChecks, ' .
            '  bolBsb AS AmavisSpamKillLevel, ' .
            '  CASE(mfs.bolQuarantine) ' .
            '    WHEN 1 THEN mfs.intAgressiveness+10 ' .
            '    ELSE mfs.intAgressiveness ' .
            '  END AS GidNumber, ' .
            '  vd.Domain as Cn, ' .
            '  "" as CatchallMailRoutingAddress, ' .
            '  CASE mb.LocalPart ' .
            '    WHEN " " THEN CONCAT(mb.Username, "@", vd.Domain) ' .
            '    ELSE CONCAT(mb.LocalPart, "@", vd.Domain) ' .
            '  END AS EmailAddress, ' .
            '  CASE mb.LocalPart ' .
            '    WHEN " " THEN mb.Username ' .
            '    ELSE LocalPart ' .
            '  End AS LocalPart, ' .
            '  mb.Username, ' .
            '  mb.ServiceID AS UidNumber, ' .
            '  MailboxTypeID, ' .
            '  mb.Maildir, ' .
            '  mb.VirtualDomainID ' .
            'FROM ' .
            '  tblMailFilterSettings mfs ' .
            'INNER JOIN tblVirtualDomains vd ' .
            '  ON mfs.intVirtualdomainId = vd.VirtualDomainID ' .
            'INNER JOIN tblMailboxes mb ' .
            '  ON vd.VirtualDomainID = mb.VirtualDomainID ' .
            'WHERE ' .
            '  vd.VirtualDomainID IN (' . implode(',', $arrVDomIDs) . ') ' .
            '  AND mb.ServiceID = ' . $this->getServiceId() . ' ' .
            '  AND mb.MailboxTypeID IN (1,2,3,8,4,5) ' .
            '  AND vd.Blackhole = 0 ' .
            '  AND mfs.intMailFilterTypeId IN(' . $this->_arrMailFilterTypes['PN_IRONPORT'] . ', ' .
               $this->_arrMailFilterTypes['CLOUDMARK'] . ')' .
            'UNION ' .
            'SELECT ' .
            '  CASE(mfs.bolSpam) ' .
            '    WHEN 1 THEN "FALSE" ' .
            '    ELSE "TRUE" ' .
            '  END AS AmavisBypassSpamChecks, ' .
            '  CASE(mfs.bolAntiVirus) ' .
            '    WHEN 1 THEN "FALSE" ' .
            '    ELSE "TRUE" ' .
            '  END AS AmavisBypassVirusChecks, ' .
            '  bolBsb AS AmavisSpamKillLevel, ' .
            '  CASE(mfs.bolQuarantine) ' .
            '    WHEN 1 THEN mfs.intAgressiveness+10  ' .
            '    ELSE mfs.intAgressiveness ' .
            '  END AS GidNumber, ' .
            '  vd.Domain as Cn, ' .
            '  CASE mb.LocalPart ' .
            '    WHEN " " THEN CONCAT(mb.Username, "@", vd.Domain) ' .
            '    ELSE CONCAT(mb.LocalPart, "@", vd.Domain) ' .
            '  END AS CatchallMailRoutingAddress, ' .
            '  CONCAT("' . self::IRONPORT_CATCHALL_LOCALPART . '@", vd.Domain) AS EmailAddress, ' .
            '  "' . self::IRONPORT_CATCHALL_LOCALPART . '" AS LocalPart, ' .
            '  mb.Username, ' .
            '  mb.ServiceID AS UidNumber, ' .
            '  MailboxTypeID, ' .
            '  mb.Maildir, ' .
            '  mb.VirtualDomainID ' .
            'FROM ' .
            '  tblMailFilterSettings mfs ' .
            'INNER JOIN tblVirtualDomains vd ' .
            '  ON mfs.intVirtualdomainId = vd.VirtualDomainID ' .
            'INNER JOIN tblMailboxes mb ' .
            '  ON vd.VirtualDomainID = mb.VirtualDomainID ' .
            'WHERE ' .
            '  vd.VirtualDomainID IN (' . implode(',', $arrVDomIDs) . ') ' .
            '  AND mb.ServiceID =  ' . $this->getServiceId() . ' ' .
            '  AND mb.MailboxTypeID IN (1) ' .
            '  AND vd.Blackhole = 0 ' .
            '  AND mfs.intMailFilterTypeId IN(' . $this->_arrMailFilterTypes['PN_IRONPORT'] .
            '  , ' . $this->_arrMailFilterTypes['CLOUDMARK'] . ') ' .
            'ORDER BY CHAR_LENGTH(LocalPart) DESC, MailboxTypeID DESC ';

        $arrSettings = $this->_objDb->getArray($strQuery, 'maildelivery');

        if (count($arrSettings) >= self::LDAP_THRESHOLD) {

            $this->_bolLargeIronPortUser = TRUE;

            // determines wether we need to delete ldap configs as they where wrong, if so it sets account up for refresh
            $this->_arrLargeUserIronPortConfigs = $arrSettings;
            $this->_deleteLargeUserIronportSettings();

            // If we are loook at a large customer we need to redefine
            // what arrSettings is otherwise Ldap falls over
            // This code is relating to Ps 56165, 56166
            $arrSettings = $this->_getIronPortDomainSettingsForLargeUsers();
        }

        $arrDomainMailboxSettings = array();

        if (TRUE === is_array($arrSettings)) {
            foreach ($arrSettings as $arrMailboxSetting) {

                if (FALSE === isset($arrDomainMailboxSettings[$arrMailboxSetting['Cn']][$arrMailboxSetting['LocalPart']])) {
                    $arrTmp = array(
                        'AmavisBypassSpamChecks' => $arrMailboxSetting['AmavisBypassSpamChecks'],
                        'GidNumber' => $arrMailboxSetting['GidNumber'],
                        'AmavisBypassVirusChecks' => $arrMailboxSetting['AmavisBypassVirusChecks'],
                        'UidNumber' => $arrMailboxSetting['UidNumber'],
                        'Cn' => $arrMailboxSetting['Cn'],
                        'Sn' => $arrMailboxSetting['EmailAddress'],
                        'Uid' => $arrMailboxSetting['EmailAddress'],
                        'HomeDirectory' => '/var/tmp',
                        'AmavisSpamKillLevel' => $arrMailboxSetting['AmavisSpamKillLevel']
                    );

                    $arrTmp['UserPassword'] = $this->_getPasswdForIronPort($arrMailboxSetting['Username'], $arrMailboxSetting['LocalPart'], $arrMailboxSetting['Maildir'], $arrMailboxSetting['MailboxTypeID']);

                    if (isset($arrTmpMailFilters[$arrMailboxSetting['Cn']]) &&
                        isset($arrTmpMailFilters[$arrMailboxSetting['Cn']]['BLACKLIST']) &&
                        count($arrTmpMailFilters[$arrMailboxSetting['Cn']]['BLACKLIST']) > 0) {
                            $arrTmp['AmavisBlacklistSender'] = $arrTmpMailFilters[$arrMailboxSetting['Cn']]['BLACKLIST'];
                    }

                    if (isset($arrTmpMailFilters[$arrMailboxSetting['Cn']]) &&
                        isset($arrTmpMailFilters[$arrMailboxSetting['Cn']]['WHITELIST']) &&
                        count($arrTmpMailFilters[$arrMailboxSetting['Cn']]['WHITELIST']) > 0) {
                            $arrTmp['AmavisWhitelistSender'] = $arrTmpMailFilters[$arrMailboxSetting['Cn']]['WHITELIST'];
                    }

                    if ($arrMailboxSetting['MailboxTypeID'] == 8) {

                        $arrTmp['MailRoutingAddress'] = $this->_getMailRoutingAddressLocalPart($arrMailboxSetting['VirtualDomainID'], $arrMailboxSetting['Maildir']) . '@' . $arrMailboxSetting['Cn'];
                    } elseif ($arrMailboxSetting['LocalPart'] == self::IRONPORT_CATCHALL_LOCALPART) {

                        if (!$this->_bolLargeIronPortUser) {

                            $arrTmp['MailRoutingAddress'] = $arrMailboxSetting['CatchallMailRoutingAddress'];
                            $arrTmp['Mail'] = $this->_getMailCatchallAttributes($arrMailboxSetting['VirtualDomainID']);
                        } else {

                            $arrTmp['MailRoutingAddress'] = $arrMailboxSetting['CatchallMailRoutingAddress'];
                        }
                    } elseif ($arrMailboxSetting['MailboxTypeID'] == 3) {

                        //create -list
                        $arrTmpList = $arrTmp;
                        $arrTmpList['Sn'] = $arrMailboxSetting['LocalPart'] . '-list@' . $arrMailboxSetting['Cn'];
                        $arrTmpList['Uid'] = $arrMailboxSetting['LocalPart'] . '-list@' . $arrMailboxSetting['Cn'];
                        $arrTmpList['UserPassword'] = $this->_getPasswdForIronPort('', self::IRONPORT_CATCHALL_LOCALPART, '', 0);
                        $arrDomainMailboxSettings[$arrMailboxSetting['Cn']][$arrMailboxSetting['LocalPart'] . '-list'] = $arrTmpList;

                        //create -subscribe
                        $arrTmpSubscribe = $arrTmp;
                        $arrTmpSubscribe['Sn'] = $arrMailboxSetting['LocalPart'] . '-subscribe@' . $arrMailboxSetting['Cn'];
                        $arrTmpSubscribe['Uid'] = $arrMailboxSetting['LocalPart'] . '-subscribe@' . $arrMailboxSetting['Cn'];
                        $arrTmpSubscribe['UserPassword'] = $this->_getPasswdForIronPort('', self::IRONPORT_CATCHALL_LOCALPART, '', 0);
                        $arrDomainMailboxSettings[$arrMailboxSetting['Cn']][$arrMailboxSetting['LocalPart'] . '-subscribe'] = $arrTmpSubscribe;

                        //create -unsubscribe
                        $arrTmpUnsubscribe = $arrTmp;
                        $arrTmpUnsubscribe['Sn'] = $arrMailboxSetting['LocalPart'] . '-unsubscribe@' . $arrMailboxSetting['Cn'];
                        $arrTmpUnsubscribe['Uid'] = $arrMailboxSetting['LocalPart'] . '-unsubscribe@' . $arrMailboxSetting['Cn'];
                        $arrTmpUnsubscribe['UserPassword'] = $this->_getPasswdForIronPort('', self::IRONPORT_CATCHALL_LOCALPART, '', 0);
                        $arrDomainMailboxSettings[$arrMailboxSetting['Cn']][$arrMailboxSetting['LocalPart'] . '-unsubscribe'] = $arrTmpUnsubscribe;
                    }

                    $arrDomainMailboxSettings[$arrMailboxSetting['Cn']][$arrMailboxSetting['LocalPart']] = $arrTmp;
                }
            }
            foreach ($arrVDomIDs as $intVDomId) {

                $arrDomain = $this->getVirtualDomainById($intVDomId);
                if (TRUE === $this->_hasMailingList($intVDomId) &&
                    FALSE === isset($arrDomainMailboxSettings[$arrDomain['Domain']][self::IRONPORT_CATCHALL_LOCALPART]) /*there is no catchall*/) {

                        $arrLocalParts = array_keys($arrDomainMailboxSettings[$arrDomain['Domain']]);
                        $arrMLCASettings = $arrDomainMailboxSettings[$arrDomain['Domain']][$arrLocalParts[0]]; //pick any localpart as ironport setting are set on a domain basis
                        //create catchall for mailing list with no mailrouting attribute
                        unset($arrMLCASettings['MailRoutingAddress']);
                        $arrMLCASettings['Mail'] = $this->_getMailCatchallAttributes($intVDomId);
                        $arrMLCASettings['Sn'] = self::IRONPORT_CATCHALL_LOCALPART . '@' . $arrDomain['Domain'];
                        $arrMLCASettings['Uid'] = self::IRONPORT_CATCHALL_LOCALPART . '@' . $arrDomain['Domain'];
                        $arrMLCASettings['UserPassword'] = $this->_getPasswdForIronPort('', self::IRONPORT_CATCHALL_LOCALPART, '', 0);
                        $arrDomainMailboxSettings[$arrDomain['Domain']][self::IRONPORT_CATCHALL_LOCALPART] = $arrMLCASettings;
                }
            }

        } else {
            $this->_arrIronPortDomainSettings = array();
        }

        $this->_arrIronPortDomainSettings = $arrDomainMailboxSettings;
    }

    /**
     * delete all ldap entrie for large users where the configs
     * are not correctly set up for large users
     *
     * @return boolean
     */
    protected function _deleteLargeUserIronportSettings()
    {
        $arrDomains = $this->getVirtualDomainsDetails();

        foreach ($arrDomains as $arrDomain) {

            if ($arrDomain['Blackhole'] == 0) {

                // We want to delete settings for this users if
                // 1. there is no catchall for this domain
                // 2. there is a catchall and...
                //    2.1 the catchall's mail attribute is an array
                //    2.2 the number of elements in the mail attr array is > 0
                //
                // n.b. trying to access a mail attribute of an Ironport mail config that does not have a mail attribute will cause an exception to be thrown
                //      this is caught and dealt with as you can see below
                //set up account for a refresh
                Components_Api::queueSignal($this->getComponentId(), 'REFRESH');
                Db_Manager::commit();
                return;
            }
        }
    }

    /**
     * Get the catchall iron port domain settings for large customers
     * so that Ldap doesn't fall over
     *
     * @return array
     */
    protected function _getIronPortDomainSettingsForLargeUsers()
    {
        $arrSettings = array();

        $strSettingsQuery = '
            SELECT
            CASE(mfs.bolSpam)
            WHEN 1 THEN "FALSE"
            ELSE "TRUE"
            END AS AmavisBypassSpamChecks,
            CASE(mfs.bolAntiVirus)
            WHEN 1 THEN "FALSE"
            ELSE "TRUE"
            END AS AmavisBypassVirusChecks,
            bolBsb AS AmavisSpamKillLevel,
            CASE(mfs.bolQuarantine)
            WHEN 1 THEN mfs.intAgressiveness+10
            ELSE mfs.intAgressiveness
            END AS GidNumber,
            vd.Domain as Cn,
            CONCAT("catchall.email@", vd.Domain) AS EmailAddress,
            "catchall.email" AS LocalPart,
            vd.Username,
            vd.ServiceID AS UidNumber,
            vd.VirtualDomainID,
            "1" AS MailboxTypeID,
            CONCAT("/share/isp/", vd.isp, "/mail/", LEFT(vd.Username, 2) ,"/", vd.Username, "/Maildir/") AS Maildir,
            vd.ISP AS Isp
            FROM
            tblMailFilterSettings AS mfs
            INNER JOIN tblVirtualDomains vd
            ON mfs.intVirtualdomainId = vd.VirtualDomainID
            WHERE
            vd.ServiceID = ' . $this->getServiceId() . '
            AND vd.Blackhole = 0
            AND mfs.intMailFilterTypeId =  1';

        $arrSettings = $this->_objDb->getArray($strSettingsQuery, 'maildelivery');

        $strLocalPartQuery = '
            SELECT LocalPart
            FROM tblMailboxes
            WHERE
            username = "' . $arrSettings[0]['Username'] . '"
            AND isp = "' . $arrSettings[0]['Isp'] . '"
            AND Maildir = "' . $arrSettings[0]['Maildir'] . '"
            AND MailboxTypeID IN (1,2)
            ORDER BY
            CHAR_LENGTH(LocalPart) DESC,
                MailboxTypeID DESC LIMIT 1';

        $arrLocalPart = $this->_objDb->getSingleRow($strLocalPartQuery, 'maildelivery');

        if (empty($arrLocalPart)) {

            $arrLocalPart['LocalPart'] = 'postmaster';
        }

        foreach ($arrSettings as &$arrSetting) {

            $arrSetting['CatchallMailRoutingAddress'] = $arrLocalPart['LocalPart'] . "@" . $arrSetting['Cn'];
        }

        return $arrSettings;
    }

    /**
     * _getPasswdForIronPort
     * get the mailbox's password and format it for IronPort
     *
     * @access protected
     * @param $strUsername String Account Username
     * @param $strLocalPart String Localpart of email address
     * @param $strMaildir String Directory where mail is stored - used to assess whether mailbox is of type Default
     * @param $intMailboxTypeId Integer 1-8 depending on mailbox type
     * @return String
     */
    protected function _getPasswdForIronPort($strUsername, $strLocalPart, $strMaildir, $intMailboxTypeId)
    {
        if ($this->_isPaidForProduct() == 0 || $intMailboxTypeId == 8 || $strLocalPart == 'catchall.email' || $intMailboxTypeId == 4 || $intMailboxTypeId == 5) {
            $strRandom = '';
            for ($i=0;$i<10;$i++) {
                $strRandom .= chr(rand(33, 126));
            }
            return '{CRYPT}' . crypt($strRandom);
        } else {

            if ($strLocalPart == '' || FALSE !== strpos($strMaildir, '/Maildir/')) {
                $strMBUsername = $strUsername;
            } else {
                $strMBUsername = $strUsername . '+' . $strLocalPart;
            }
            $strQuery = "SELECT passwd FROM " . $this->getMailusersTbl() . " "
                . "WHERE username = '" . $strMBUsername . "'";
            $strPassword = $this->_objDb->getSingleValue($strQuery, 'passwd', 'mailauth');
            if ($strPassword == '') {
                $strQuery = "SELECT passwd FROM {$this->getMailusersTbl()} "
                    . "WHERE username = '{$strUsername}'";
                $strPassword = $this->_objDb->getSingleValue($strQuery, 'passwd', 'mailauth');
            }
            return '{CRYPT}' . $strPassword;
        }
    }

    /**
     * _getMailCatchallAttributes return return a list of all email addresses on the domain
     *
     * @access protected
     * @param Integer User's virtual domain ID
     * @return array
     */
    protected function _getMailCatchallAttributes($intVirtualDomainId)
    {
        $strQuery = "SELECT " .
            '  CASE mb.LocalPart ' .
            '    WHEN " " THEN CONCAT(mb.Username, "@", vd.Domain) ' .
            '    ELSE CONCAT(mb.LocalPart, "@", vd.Domain) ' .
            '  END AS vchEmailAddress, ' .
            '  mb.LocalPart, ' .
            '  vd.Domain, ' .
            '  mb.MailboxTypeID ' .
            "FROM " .
            "  tblMailboxes mb " .
            "INNER JOIN tblVirtualDomains vd " .
            "  ON mb.VirtualDomainID = vd.VirtualDomainID " .
            "WHERE " .
            "  vd.VirtualDomainID = " . $intVirtualDomainId . " " .
            "  AND vd.Blackhole = 0 " .
            "GROUP BY " .
            "  vchEmailAddress," .
            "  mb.LocalPart, " .
            "  vd.Domain," .
            "  mb.MailboxTypeID";
        $arrMailAttributes = $this->_objDb->getArray($strQuery, 'maildelivery');
        $arrMailReturn = array();
        foreach ($arrMailAttributes as $arrAddresses) {

            $arrMailReturn[] = $arrAddresses['vchEmailAddress'];
            if ($arrAddresses['MailboxTypeID'] == 3) {

                $arrMailReturn[] = $arrAddresses['LocalPart'] . '-subscribe@' . $arrAddresses['Domain'];
                $arrMailReturn[] = $arrAddresses['LocalPart'] . '-unsubscribe@' . $arrAddresses['Domain'];
                $arrMailReturn[] = $arrAddresses['LocalPart'] . '-list@' . $arrAddresses['Domain'];
            }
        }
        return $arrMailReturn;
    }

    /**
     * _getMailRoutingAddressLocalPart return the email address of where an alias should is to be delivered to
     *
     * @access protected
     * @param Integer User's virtual domain ID
     * @param String Directory where mail is stored - used to asses which mailbox the mail will be routed to
     * @return String
     */
    protected function _getMailRoutingAddressLocalPart($intVirtualDomainId, $strMaildir)
    {
        $strQuery = "SELECT " .
            '  CASE LocalPart ' .
            '    WHEN " " THEN Username ' .
            '    ELSE LocalPart ' .
            '  END AS LocalPart ' .
            "FROM " .
            "  tblMailboxes " .
            "WHERE " .
            "  VirtualDomainID=" . $intVirtualDomainId . " AND " .
            "  Maildir = '" . $strMaildir . "' " .
            "  AND MailboxTypeID in (1,2) " .
            "ORDER BY CHAR_LENGTH(LocalPart) DESC, MailboxTypeID DESC " .
            "LIMIT 1";
        return $this->_objDb->getSingleValue($strQuery, 'LocalPart', 'maildelivery');
    }

    /**
     * _getIronPortDomainSettings return ironport friendly domain spam settings
     *
     * @access protected
     * @param Boolean Optional $bolForce default FALSE - whether to get a fresh copy of users settings
     * @return array
     */
    protected function _getIronPortDomainSettings($bolForce = FALSE)
    {
        if (FALSE === isset($this->_arrIronPortDomainSettings) || TRUE === $bolForce) {

            $this->_setIronPortDomainSettings();
        }
        return $this->_arrIronPortDomainSettings;
    }

    /**
     * _refreshIronPortMailboxSettings reload this mailboxes settings in ironport
     * this will also refresh the ironport catchall record if the current mailbox
     * is of type 1 (catchall)
     *
     * @access protected
     * @return void
     */
    protected function _refreshIronPortMailboxSettings()
    {

        $arrDomainSettings = $this->_getIronPortDomainSettings(TRUE);
        /**
        * [Problem ID: 58162]Confirmed that the Ironport/LDAP functionality  is no longer required;
        * Ironport servers have been decommissioned and the LDAP system is no longer used.
        * So removing its related code.
        */
        $arrVirtualDomains = $this->_getVirtualDomains();
        foreach ($arrVirtualDomains as $intVirtualDomainId) {
            $arrDomain = $this->getVirtualDomainById($intVirtualDomainId);

            if (FALSE === isset($arrDomainSettings[$arrDomain['Domain']])) {
                $this->checkDomainSettings($intVirtualDomainId);
                // check if user has been migrated to ironport yet
                // REMOVE AFTER IRONPORT MIGRATION COMPLETION
                $this->_setIsIronPortUser(TRUE);
                $this->_getIronPortDomainSettings(TRUE);
            }
            /**
            * [Problem ID: 58162]Confirmed that the Ironport/LDAP functionality  is no longer required;
            * Ironport servers have been decommissioned and the LDAP system is no longer used.
            * So removing its related code.
            */
        }
    }

    /**
     * _setIronportMailboxAttributes
     * Convineince function to set all parameters on the IronPort_Mailbox object
     * otherwise this code would be duplicated in a lot of places
     *
     * @access protected
     * @param $arrSettings Array - settings to be applied to instance of IronPort_Mailbox
     * @param $objIpMb IronPort_Mailbox  - instance of IronPort_Mailbox to apply the settings to
     * @return void
     */
    protected function _setIronportMailboxAttributes($arrSettings, $objIpMb)
    {
        foreach ($arrSettings as $strAttr => $strValue) {
            $strFunctionCall = 'set'.$strAttr;
            $objIpMb->$strFunctionCall($strValue);
        }
    }

    /**
     * isValidAlias check whether alias is valid
     *
     * @access public
     * @param string $strAlias
     * @return boolean
     */
    public function isValidAlias($strAlias)
    {
        return preg_match(self::ALIAS_PATTERN, $strAlias);
    }

    /**
     * Returns mailbox type id specified by the handle
     *
     * Returns mailbox type id for a particular handle, eg. 'catchall', 'mailbox'
     *
     * @access public
     * @param string handle
     * @return id mailbox type id
     */
    public function getTypeByHandle($strHandle)
    {
        if (is_array($this->_arrMailboxTypes)) {
            if (array_key_exists($strHandle, $this->_arrMailboxTypes)) {
                return $this->_arrMailboxTypes[$strHandle];
            }
        }
        return false;
    }

    /**
     * Returns a type of the current mailbox
     *
     * Returns a type of the current mailbox, eg. 'default', 'mailbox'
     *
     * @access public
     * @param none
     * @return string mailbox type
     */
    public function getMailboxType()
    {
        return $this->_objMailbox->type;
    }

    /**
     * Returns type id of the current mailbox
     * eg. currently it will return 1 for a 'default' mailbox, 2 for 'mailbox' and so on
     *
     * @access public
     * @param none
     * @return string mailbox type
     */
    public function getMailboxTypeId()
    {
        return $this->getTypeByHandle($this->_arrMapping[$this->getMailboxType()]);
    }

    /**
     * Returns redirect_to value for the current mailbox
     *
     * @access public
     * @param none
     * @return string redirect_to
     */
    public function getRedirectTo()
    {
        return $this->_objMailbox->redirect_to;
    }

    /**
     * Returns isp value for the current mailbox
     *
     * @access public
     * @param none
     * @return string isp
     */
    public function getIsp()
    {
        return self::getAuthIsp($this->_objMailbox->isp);
    }

    /**
     * Returns mailusers table name for current isp
     *
     * @access public
     * @param none
     * @return string table name
     */
    public function getMailusersTbl()
    {
        if ($this->getIsp()) {
            return 'mailusers_'.$this->getIsp();
        }
    }

    /**
     * Returns mailmgt table name for current isp
     *
     * @access public
     * @param none
     * @return string table name
     */
    public function getMailmgtTbl()
    {
        if ($this->getIsp()) {
            return 'mailmgt_'.$this->getIsp();
        }
    }

    /**
     * Returns username (main account) of the mailbox
     * eg. <EMAIL> would return johnsmith
     *
     * @access public
     * @param none
     * @return string username
     */
    public function getUsername()
    {
        return $this->_objMailbox->username;
    }

    /**
     * Returns mailbox (+optional submailbox) username
     * eg. <EMAIL> would return johnsmith+john
     *
     * @access public
     * @param none
     * @return string mailbox username + optional submailbox
     */
    public function getMailboxUsername()
    {
        return self::convertMailboxUsername(
            $this->getMailboxType(),
            $this->getUsername(),
            $this->getLocalPart(),
        $this->getIsp()
        );
    }

    /**
     * Static method to join main mailbox username + optional submailbox username
     * if necessary (eg. if it's a submailbox)
     *
     * @access public
     * @param string mailbox type
     * @param string main mailbox username
     * @param string submailbox name
     * @param string isp name
     * @return string mailbox username
     */
    static public function convertMailboxUsername($strType, $strUsername, $strLogin, $strIsp)
    {
        if($strIsp == 'vodafone') {
        return $strLogin;
    } else if (in_array($strType, array('mailbox', 'redirect'))) {
            return $strUsername . '+' . $strLogin;
        } else {
            return $strUsername;
        }
    }

    /**
     * Returns cleartext password for the current mailbox
     *
     * @access public
     * @param none
     * @return string password
     */
    public function getPassword()
    {
        return $this->_objMailbox->password;
    }

    /**
     * Returns a string used as LocalPart in MailDB
     *
     * @access public
     * @param none
     * @return string local part
     */
    public function getLocalPart()
    {
        return $this->_objMailbox->login;
    }

    /**
     * Returns sub login used in Maildir columns in tblMailboxes
     * @access public
     * @param none
     * @return string sub login
     */
    public function getSubLogin()
    {
        if ($this->getMailboxType() == 'mailbox') {
            return '-'.preg_replace("/\./", ":", $this->getLocalPart());
        }
        return '';
    }
    /**
     * Returns user domain
     * eg. <EMAIL> would have johnsmith.plus.com user domain
     * but in the case of partner eu where the partner has a domain, we
     * return that partners mail domain
     *
     * @access public
     * @param none
     * @return string user domain
     */
    public function getUserDomains()
    {
        // if eu is owned by a reseller with a domain
        $intDomainOwnerServiceId = $this->getDomainOwnerServiceId();
        if ($intDomainOwnerServiceId != $this->getServiceId()) {

            $strQuery = "SELECT
                vd.Domain
                FROM
                tblVirtualDomains vd
                INNER JOIN tblResellerDomain rd
                ON vd.VirtualDomainID = rd.intVirtualDomainId
                INNER JOIN tblReseller r
                ON rd.intResellerId = r.intResellerId
                WHERE r.intServiceId = $intDomainOwnerServiceId";
            return array($this->_objDb->getSingleValue($strQuery, 'Domain', 'maildelivery'));
        }

        // not a partner eu account who's partner owns a domain
        $arrDomains = Mail_Mailbox::getIspExtension($this->_objMailbox->isp, false);
        $arrUserDomains = array();
        foreach ($arrDomains as $strDomain) {
            if($this->_objMailbox->isp == 'vodafone') {
                $arrUserDomains[] = $this->_objMailbox->login. '.' .$strDomain;
            } else {
                $arrUserDomains[] = $this->_objMailbox->username. '.' .$strDomain;
            }
        }
        return $arrUserDomains;
    }

    /**
     * Returns component id of the current mailbox
     *
     * @access public
     * @param none
     * @return integer component id
     */
    public function getComponentId()
    {
        return $this->_objMailbox->component_id;
    }

    /**
     * Returns service id associated with current mailbox's component id
     *
     * @access public
     * @param none
     * @return integer service id
     */
    public function getServiceId()
    {
        $strQuery = "SELECT service_id from components "
            . "WHERE component_id = '{$this->_objMailbox->component_id}'";
        return $this->_objDb->getSingleValue($strQuery, 'service_id', 'userdata');
    }

    /**
     * Returns service id associated with current mailbox's component id
     *
     * @access public
     * @param none
     * @return integer service id
     */
    public function getDomainOwnerServiceId()
    {
        return (!is_null($this->_intPartnerDomainOwnerServiceID)) ? $this->_intPartnerDomainOwnerServiceID : $this->getServiceId();
    }

    /**
     * Converts the ISP name to be compatible with MAil DB convention
     * (It only converts plus.net to plusnet)
     * @params $strIsp
     * @returns $strIsp
     */
    static public function getAuthIsp($strIsp)
    {
        switch($strIsp) {
        case 'plus.net':
            return 'plusnet';
        default:
            return $strIsp;
        }
    }

    /**
     * Creates new mailbox
     * @param none
     * @return $intMailboxId
     */
    public function createMailbox()
    {
        $this->_createMailUser($this->_getMailUserId());
        // Create VDomains
        $this->_createVirtualDomain();
        $arrVirtualDomains = $this->_getVirtualDomains();
        if (!empty($arrVirtualDomains)) {
            foreach ($arrVirtualDomains as $intVirtualDomainId) {
                $intMailboxId = $this->_createMailbox($intVirtualDomainId);
            }
        }
        return $intMailboxId;
    }

    /**
     * Destroys the current mailbox
     *
     * @param none
     * @return $intMailboxId
     * @throws Mail_Exception
     */
    public function destroyMailbox()
    {
        $arrVirtualDomains = $this->_getVirtualDomains();
        if (!empty($arrVirtualDomains)) {
            foreach ($arrVirtualDomains as $intVirtualDomainId) {
                $intMailboxId = $this->_getMailboxId($intVirtualDomainId);
                if ($intMailboxId) {
                    $strQuery = "DELETE FROM tblMailboxes WHERE MailboxID = '$intMailboxId'";
                    $this->_objDb->query($strQuery, 'maildelivery');
                    $strQuery = "DELETE FROM tblRedirects WHERE MailboxID = '$intMailboxId'";
                    $this->_objDb->query($strQuery, 'maildelivery');
                    $strQuery = "DELETE FROM tblAutoResponder WHERE MailboxID = '$intMailboxId'";
                    $this->_objDb->query($strQuery, 'maildelivery');
                    /**
                    * [Problem ID: 58162]Confirmed that the Ironport/LDAP functionality  is no longer required;
                    * Ironport servers have been decommissioned and the LDAP system is no longer used.
                    * So removing its related code.
                    */
                }
            }
        }
        $intMailUserId = $this->_getMailUserId();
        if ($intMailUserId) {
            $this->destroyMailUser($intMailUserId);
        }
    }

    /**
     * Destroys the current mailbox
     *
     * @param none
     * @return $intMailboxId
     * @throws Mail_Exception
     */
    public function destroyMailboxesByVirtualDomainId($intVirtualDomainId)
    {
        if (empty($intVirtualDomainId) || false === is_numeric($intVirtualDomainId)) {
            return false;
        }

        $arrDomain = $this->getVirtualDomainById($intVirtualDomainId);

        $strQuery = "SELECT MailboxID FROM tblMailboxes WHERE VirtualDomainID = '{$intVirtualDomainId}' AND ServiceID = {$this->getServiceId()}";
        $arrMailboxes = $this->_objDb->getArray($strQuery, 'maildelivery');
        foreach ($arrMailboxes as $arrMailbox) {
            $strQuery = "DELETE FROM tblMailboxes WHERE MailboxID = '{$arrMailbox['MailboxID']}'";
            $this->_objDb->query($strQuery, 'maildelivery');
            $strQuery = "DELETE FROM tblRedirects WHERE MailboxID = '{$arrMailbox['MailboxID']}'";
            $this->_objDb->query($strQuery, 'maildelivery');
            $strQuery = "DELETE FROM tblAutoResponder WHERE MailboxID = '{$arrMailbox['MailboxID']}'";
            $this->_objDb->query($strQuery, 'maildelivery');

            /**
            * [Problem ID: 58162]Confirmed that the Ironport/LDAP functionality  is no longer required;
            * Ironport servers have been decommissioned and the LDAP system is no longer used.
            * So removing its related code.
            */
        }
        return true;
    }


    /** Refreshes the mailbox
     *  It sets the created flag in mailmgt_visp to be 0
     *  which results in the mailbox creation process being run again (passwords also)
     *  If the mailbox is already created no data will be lost
     */
    public function refreshMailbox()
    {
        $this->_refreshMailUser();
        $this->_refreshDomains();
        $this->_refreshMailboxes();
        $this->_refreshIronPortMailboxSettings();
    }
    protected function _refreshMailUser()
    {
        $intMailuserId = $this->_getMailUserId();
        // Create the mailuser if it does not exist
        if (!$intMailuserId) {

            $intMailuserId = $this->_createMailUser();
        }
        // Refresh the mailuser
        // Mark the mailbox to be recreated
        if ($intMailuserId) {
            $strQuery = "REPLACE INTO {$this->getMailmgtTbl()} " .
                "SET created='0', destroyed='0', intServiceID='{$this->getServiceId()}', id = '{$intMailuserId}'";
            $this->_objDb->query($strQuery, 'mailauth');
            $this->_setPaidFlag($intMailuserId);
        }
    }

    /**
     * Update virtual domains to be in sync with DCS
     */
    protected function _refreshDomains()
    {
        // Try to create the main domain if it doesn't exist
        $arrUserDomains = $this->getUserDomains();

        foreach ($arrUserDomains as $strUserDomain) {
            $this->_createVirtualDomainFromParams(array('domainname' => $strUserDomain));
        }

        $arrActiveDomains = domain_find($this->getServiceId(), null, null, null, null, 'active', null, null);
        $arrActiveDomains = $arrActiveDomains['domains'];

        // Remove all virtual domains and associated mailboxes that are still in Mail DB but are missing in DCS
        // 1. First get all vdomains
        // 2. Substract all user domains
        // 3. Substract active DCS domains
        // 4. If anything is left it means it shouldn't be there so delete it

        $arrDomainsToRemove = $this->getVirtualDomainsDetails();

        foreach ($arrDomainsToRemove as $intKey => $arrDomain) {

            foreach ($arrUserDomains as $strUserDomain) {
                if ($strUserDomain == $arrDomain['Domain']) {
                    unset($arrDomainsToRemove[$intKey]);
                    continue 2; // foreach ($arrDomainsToRemove)
                }
            }

            foreach ($arrActiveDomains as $intActiveKey => $arrActiveDomain) {
                if ($arrActiveDomain['domainname'] == $arrDomain['Domain']) {

                    // drop it from maildb only if the domain
                    // is not using our maildelivery platform

                    $arrRecords = domain_get_full($arrActiveDomain['domain_uid']);

                    if (TRUE === $this->_isMailManaged($arrRecords['mailservice'])) {
                        unset($arrDomainsToRemove[$intKey]);
                        continue 2; // foreach ($arrDomainsToRemove)
                    } else {
                        unset($arrActiveDomains[$intActiveKey]);
                        continue 2; // foreach ($arrDomainsToRemove)
                    }
                }
            }
        }

        if (count($arrDomainsToRemove) > 0) {
            foreach ($arrDomainsToRemove as $arrDomainToRemove) {

                $this->destroyMailboxesByVirtualDomainId($arrDomainToRemove['VirtualDomainID']);
                $this->destroyVirtualDomainById($arrDomainToRemove['VirtualDomainID']);
            }
        }

        // Try to recreate all domains present in Core DB domainsystem database

        foreach ($arrActiveDomains as $arrActiveDomain) {

            $this->_createVirtualDomainFromParams($arrActiveDomain);
        }
    }

    protected function _refreshMailboxes()
    {
        // Refresh mailboxes
        // Create the mailbox in tblMailboxes if it doesn't exist
        $arrVirtualDomains = $this->_getVirtualDomains();
        foreach ($arrVirtualDomains as $intVirtualDomainId) {
            if (!$this->_getMailboxId($intVirtualDomainId, false)) {
                $intMailboxId = $this->_createMailbox($intVirtualDomainId);
                if ('redirect' == $this->getMailboxType()) {
                    $this->_createRedirect($intMailboxId, $this->getRedirectTo());
                }
            }
        }
    }

    protected function _isMailManaged($strMailService)
    {
        if ($strMailService == 'pop3') {
            return true;
        } else {
            return false;
        }
    }

    protected function _setPaidFlag($intMailuserId)
    {
        $strQuery = "UPDATE {$this->getMailusersTbl()} SET intPaidFlag = {$this->_isPaidForProduct()} WHERE id = '{$intMailuserId}'";
        $this->_objDb->query($strQuery, 'mailauth');
    }

    /**
     * create or update virtual domain entry
     */
    protected function _createVirtualDomainFromParams($arrDomain)
    {
        if ($this->getDomainOwnerServiceId() != $this->getServiceId()) {
            return;
        }
        // Find current settings based on our default domain
        // the new domain should reflect settings for the current domain(s)
        $strDefaultDomain = $this->getUserDomains();
        $strDefaultDomain = $strDefaultDomain[0];

        $strQuery = "SELECT Blackhole, Monitor, AntiSpam, AntiVirus, TLK, RelayTypeID, RelayHost " .
            "FROM tblVirtualDomains " .
            "WHERE ServiceID = {$this->getServiceId()} AND " .
            "Domain = '{$strDefaultDomain}' AND " .
            "ISP = '{$this->getIsp()}' AND " .
            "Username = '{$this->getUsername()}' AND " .
            "Active = 1 " .
            "LIMIT 1";
        $arrCurrentSettings = $this->_objDb->getArray($strQuery, 'maildelivery');

        if (is_array($arrCurrentSettings) && !empty($arrCurrentSettings)) {

            $arrCurrentSettings = $arrCurrentSettings[0];

        } else {

            // If there are no settings use the defaults (problem 43179)
            $arrCurrentSettings = array('Blackhole' => 0,
                'Monitor' => 0,
                'AntiSpam' => 2,
                'AntiVirus' => 0,
                'RelayTypeID' => 1,
                'RelayHost' => '');
        }

        // Figure out mail delivery method

        // Fix for problem 44898
        if (FALSE === isset($arrDomain['mailservice']) || empty($arrDomain['mailservice'])) {

            $arrDomain['mailservice'] = isset($this->_objMailbox->delivery_method) ? $this->_objMailbox->delivery_method : 'pop3';

        }

        if ('pop3' == $arrDomain['mailservice']) {
            $intRelayTypeId = 1;

        } else {
            $intRelayTypeId = 2;
        }

        // Check for an existing entry
        $strQuery = "SELECT VirtualDomainID, RelayTypeID from tblVirtualDomains " .
            "WHERE Domain = '{$arrDomain['domainname']}'";
        $arrAllDomains = $this->_objDb->getArray($strQuery, 'maildelivery');
        $bolDomainInOurControl = TRUE;
        if ($arrDomain['domain_uid'] > 0) {
            $arrRecords = domain_get_full($arrDomain['domain_uid']);
            $bolDomainInOurControl = $this->_isMailManaged($arrRecords['mailservice']);
        }

        if (is_array($arrAllDomains)) {

            // For existing entries, only update RelayTypeID
            // If there are duplicates, update them all

            $arrUpdateDomains = array();

            foreach ($arrAllDomains as $arrVirtualDomain) {

                if ($arrVirtualDomain['RelayTypeID'] != $intRelayTypeId) {
                    $arrUpdateDomains[] = (int) $arrVirtualDomain['VirtualDomainID'];
                }
            }

            if ($arrUpdateDomains) {

                $strQuery = "UPDATE tblVirtualDomains
                    SET RelayTypeID = '{$intRelayTypeId}'
                    WHERE VirtualDomainID IN (" . join(', ', $arrUpdateDomains) . ")";
                $this->_objDb->query($strQuery, 'maildelivery');
                $this->_flushVirtualDomains();
            }

        } elseif (TRUE === $bolDomainInOurControl) {

            // Insert new row
            $strQuery = "INSERT INTO tblVirtualDomains SET " .
                "ServiceID = {$this->getServiceId()}, " .
                "Domain = '{$arrDomain['domainname']}', " .
                "Blackhole = '{$arrCurrentSettings['Blackhole']}', " .
                "Monitor = '{$arrCurrentSettings['Monitor']}', " .
                "AntiSpam = '{$arrCurrentSettings['AntiSpam']}', " .
                "AntiVirus = '{$arrCurrentSettings['AntiVirus']}', " .
                "Username = '{$this->getUsername()}', " .
                "ISP = '{$this->getIsp()}', " .
                "TLK = LEFT('{$this->getUsername()}', 2), " .
                "RelayTypeID = '{$intRelayTypeId}', " .
                "RelayHost = 0, " .
                "Active = 1";
            $this->_objDb->query($strQuery, 'maildelivery');
            $intNewVirtualDomainID = $this->_objDb->getSingleValue("select LAST_INSERT_ID() as VirtualDomainID", 'VirtualDomainID', 'maildelivery');
            $this->_flushVirtualDomains();
            $this->checkDomainSettings($intNewVirtualDomainID);

            /**
            * [Problem ID: 58162]Confirmed that the Ironport/LDAP functionality  is no longer required;
            * Ironport servers have been decommissioned and the LDAP system is no longer used.
            * So removing its related code.
            */
        }
    }

    public function createRedirect($strRedirectTo)
    {
        if (!preg_match(self::EMAIL_PATTERN, $strRedirectTo)) {
            throw new Mail_Exception("Invalid characters in redirect create");
        }
        $arrVirtualDomains = $this->_getVirtualDomains();
        if (!empty($arrVirtualDomains)) {
            foreach ($arrVirtualDomains as $intVirtualDomainId) {
                $intMailboxId = $this->_createMailbox($intVirtualDomainId);
                $this->_createRedirect($intMailboxId, $strRedirectTo);
            }
        } else {
            $this->_createVirtualDomain();
            $arrVirtualDomains = $this->_getVirtualDomains();
            foreach ($arrVirtualDomains as $intVirtualDomainId) {
                $intMailboxId = $this->_createMailbox($intVirtualDomainId);
                $this->_createRedirect($intMailboxId, $strRedirectTo);
            }
        }
    }

    /**
     * Destroys All Virtual Domains
     *
     */
    public function destroyVirtualDomain()
    {
        if ($this->getDomainOwnerServiceId() != $this->getServiceId()) {

            return false; //never destroy a domain if this user doesnt own it
        }

        $arrVirtualDomains = $this->_getVirtualDomains();
        $this->_getIronPortDomainSettings();
        if (!empty($arrVirtualDomains)) {
            foreach ($arrVirtualDomains as $intVirtualDomainId) {
                if ($intVirtualDomainId) {
                    $arrDomain = $this->getVirtualDomainById($intVirtualDomainId);
                    $strQuery = "DELETE FROM tblVirtualDomains WHERE VirtualDomainID = '{$intVirtualDomainId}'";
                    $this->_objDb->query($strQuery, 'maildelivery');
                    $this->_flushVirtualDomains();

                    /**
                    * [Problem ID: 58162]Confirmed that the Ironport/LDAP functionality  is no longer required;
                    * Ironport servers have been decommissioned and the LDAP system is no longer used.
                    * So removing its related code.
                    */
                }
            }

        }
        $this->_getIronPortDomainSettings(TRUE);

        return true;
    }

    /**
     * Destroys All Virtual Domains
     *
     */
    public function destroyVirtualDomainById($intVirtualDomainId)
    {
        if (empty($intVirtualDomainId) || !is_numeric($intVirtualDomainId) || $this->getDomainOwnerServiceId() != $this->getServiceId()) {
            return false;
        }
        $this->_getIronPortDomainSettings();
        $arrDomain = $this->getVirtualDomainById($intVirtualDomainId);
        $strQuery = "DELETE FROM tblVirtualDomains WHERE VirtualDomainID = '{$intVirtualDomainId}' LIMIT 1";
        $this->_objDb->query($strQuery, 'maildelivery');
        $this->_flushVirtualDomains();
        return true;
    }

    public function destroyMailUser()
    {
        $intMailUserId = $this->_getMailUserId();

        if ($intMailUserId) {

            // we set created to 1 here just to save component_add time, if it hasnt already been
            // created then why bother letting component_add create it before component_destroy
            // gets to it, this basically gets rid of this race condition
            $strQuery = "REPLACE INTO {$this->getMailmgtTbl()} SET destroyed = '1', created = '1', id = '{$intMailUserId}', intServiceID = '{$this->getServiceId()}'";
            $this->_objDb->query($strQuery, 'mailauth');
            return true;
        }
        return false;
    }

    /**
     * Changes the login of the current mailbox
     *
     * @param $strNewLogin
     * @return true on success
     */
    public function changeLogin($strNewLogin)
    {
        $arrVirtualDomains = $this->_getVirtualDomains();
        $strCurrentLogin = $this->getLocalPart();
        $this->_getIronPortDomainSettings();

        if (!empty($arrVirtualDomains)) {
            foreach ($arrVirtualDomains as $intVirtualDomainId) {

                $intMailboxId = $this->_getMailboxId($intVirtualDomainId);
                if (!$intMailboxId) {
                    throw new Mail_Exception('MailApi: intMailboxId not set');
                }

                $strQuery = "UPDATE tblMailboxes "
                    . "SET LocalPart = '" . mysql_real_escape_string($strNewLogin) . "' "
                    . "WHERE MailboxID = '{$intMailboxId}'";

                $this->_objDb->query($strQuery, 'maildelivery');

                /**
                * [Problem ID: 58162]Confirmed that the Ironport/LDAP functionality  is no longer required;
                * Ironport servers have been decommissioned and the LDAP system is no longer used.
                * So removing its related code.
                */
            }
        }
    }

    /**
     * Changes the password
     *
     * @param $strNewPassword
     * @return true on success
     */
    public function changePassword($strNewPassword)
    {
        $intMailUserId = $this->_getMailUserId();
        if ($intMailUserId) {

            $strCryptPassword = mysql_real_escape_string(Crypt_Crypt::encrypt($strNewPassword, 'mailauth'));

            $strQuery = "UPDATE {$this->getMailusersTbl()} "
                . "SET passwd = '$strCryptPassword' "
                . "WHERE id = '{$intMailUserId}'";
            $this->_objDb->query($strQuery, 'mailauth');
            $this->_setPaidFlag($intMailUserId);

            /**
            * [Problem ID: 58162]Confirmed that the Ironport/LDAP functionality  is no longer required;
            * Ironport servers have been decommissioned and the LDAP system is no longer used.
            * So removing its related code.
            */
            return TRUE;
        }
        return FALSE;
    }

    /**
     * Changes the redirect_to
     *
     * @access public
     * @param $strRedirectTo
     * @return true on success
     */
    public function changeRedirectTo($strRedirectTo)
    {
        if (!preg_match(self::EMAIL_PATTERN, $strRedirectTo)) {
            throw new Mail_Exception("Invalid characters in redirect change");
        }
        $arrVirtualDomains = $this->_getVirtualDomains();
        if (!empty($arrVirtualDomains)) {
            foreach ($arrVirtualDomains as $intVirtualDomainId) {
                $intMailboxId = $this->_getMailboxId($intVirtualDomainId);
                $strQuery = "UPDATE tblRedirects SET "
                    . "Destination = '{$strRedirectTo}' "
                    . "WHERE MailboxID = '{$intMailboxId}'";
                $this->_objDb->query($strQuery, 'maildelivery');
            }
        }
        return true;
    }

    /**
     * Updated delivery method
     *
     *
     *
     * @access public
     * @param $strDeliveryMethod
     * @param @strSmtpDeliveryIp
     *
     */
    public function changeDeliveryMethod($strDeliveryMethod, $strSmtpDeliveryIp, $strDomainName = NULL)
    {
        if ($strDeliveryMethod == 'smtp' || $strDeliveryMethod == 'semiauto') {
            $intRelayTypeId = 2;

            $strQuery = "
                UPDATE
                tblMailFilterSettings as mfs
                INNER JOIN
                tblVirtualDomains as vd
                ON
                vd.VirtualDomainId = mfs.intVirtualDomainId
                SET
                mfs.bolSpam = 0,
                mfs.bolAntivirus = 0,
                mfs.bolDnsProcessed = 0,
                mfs.bolPostiniOnlyEmail = 0
                WHERE
                vd.ServiceID = '{$this->getServiceId()}'
                ";

            if (NULL != $strDomainName) {

                $strQuery = "
                    AND vd.Domain = '{$strDomainName}'";
            }

            $this->_objDb->query($strQuery, 'maildelivery');

        } else {
            $intRelayTypeId = 1;
        }

        $arrUserDomains = $this->getUserDomains();
        if (NULL != $strDomainName) {

            $strQuery = "
                UPDATE
                tblVirtualDomains
                SET
                RelayTypeID = '{$intRelayTypeId}'
                WHERE
                Domain = '{$strDomainName}'
                ";

            $this->_objDb->query($strQuery, 'maildelivery');
        } elseif (!empty($arrUserDomains)) {

            foreach ($arrUserDomains as $strUserDomain) {

                $strQuery = "UPDATE
                    tblVirtualDomains
                    SET
                    RelayTypeID = '{$intRelayTypeId}'
                    WHERE
                    Domain = '{$strUserDomain}'
                    AND
                    ServiceID = '{$this->getServiceId()}' LIMIT 1";

                $this->_objDb->query($strQuery, 'maildelivery');
            }
        }

        return true;
    }
    /**
     * Disables Catch All
     * Updates the status of the current mailbox to be SUBMAILBOX and set its LocalPart
     *
     * @access public
     * @param none
     * @return boolean true on success
     * @throws Mail_Exception
     */
    public function disableCatchAll()
    {
        $arrVirtualDomains = $this->_getVirtualDomains();
        if (!empty($arrVirtualDomains)) {
            foreach ($arrVirtualDomains as $intVirtualDomainId) {
                $intMailboxId = $this->_getMailboxId($intVirtualDomainId);
                if (!$intMailboxId) {
                    throw new Mail_Exception('MailApi: intMailboxId not set');
                }
                $strQuery = "UPDATE tblMailboxes "
                    . "SET MailboxTypeId = '" . $this->getTypeByHandle('mailbox') . "', "
                    . "LocalPart = '{$this->getLocalPart()}' "
                    . "WHERE MailboxID = '{$intMailboxId}'";
                $this->_objDb->query($strQuery, 'maildelivery');
            }

            /**
            * [Problem ID: 58162]Confirmed that the Ironport/LDAP functionality  is no longer required;
            * Ironport servers have been decommissioned and the LDAP system is no longer used.
            * So removing its related code.
            */
            if (TRUE === $this->getIsIronPortUser()) {
                $this->_getIronPortDomainSettings(TRUE);
            }
        }
    }

    /**
     * Enables Catch All
     * Updates the status of the current mailbox to be CATCHALL
     *
     * @access public
     * @param none
     * @return boolean true on success
     * @throws Mail_Exception
     */
    public function enableCatchAll()
    {
        $arrVirtualDomains = $this->_getVirtualDomains();
        if (!empty($arrVirtualDomains)) {
            foreach ($arrVirtualDomains as $intVirtualDomainId) {
                $intMailboxId = $this->_getMailboxId($intVirtualDomainId);
                if (!$intMailboxId) {
                    // Don't throw error, just continue
                    continue;
                }
                //remove all other Catch All mailboxes
                $strQuery = "UPDATE tblMailboxes "
                    . "SET MailboxTypeId = '" . $this->getTypeByHandle('mailbox') . "' "
                    . "WHERE VirtualDomainID = '{$intVirtualDomainId}' "
                    . "AND MailboxTypeId = '" . $this->getTypeByHandle('catchall') . "'";
                $this->_objDb->query($strQuery, 'maildelivery');

                $strQuery = "UPDATE tblMailboxes "
                    . "SET MailboxTypeId = '" . $this->getTypeByHandle('catchall') . "' "
                    . "WHERE MailboxID = '{$intMailboxId}'";
                $this->_objDb->query($strQuery, 'maildelivery');

               /**
                * [Problem ID: 58162]Confirmed that the Ironport/LDAP functionality  is no longer required;
                * Ironport servers have been decommissioned and the LDAP system is no longer used.
                * So removing its related code.
                */
                if (TRUE === $this->getIsIronPortUser()) {
                    $this->_getIronPortDomainSettings(TRUE);
                }
            }
        }
    }


    /**
     * Checks whether mailbox has catch all turned on
     *
     * @access public
     * @param none
     * @return boolean true if this is a cathall mailbox
     */
    public function isCatchAll()
    {
        $arrVirtualDomains = $this->_getVirtualDomains();
        if (!empty($arrVirtualDomains)) {
            foreach ($arrVirtualDomains as $intVirtualDomainId) {
                $intMailboxId = $this->_getMailboxId($intVirtualDomainId);
                if (!$intMailboxId) {
                    continue;
                }

                $strQuery = "SELECT MailboxID FROM tblMailboxes "
                    . "WHERE VirtualDomainID = '{$intVirtualDomainId}' "
                    . "AND Username = '{$this->getUsername()}' "
                    . "AND MailboxTypeID = '" . $this->getTypeByHandle('catchall') . "' "
                    . "AND MailboxID = '{$intMailboxId}'";

                return (bool)$this->_objDb->getSingleValue($strQuery, 'MailboxID', 'maildelivery');
            }
        }
        return false;
    }

    /**
     * Creates an alias for the current mailbox in Mail Db
     *
     * @access p
     * @param string $strName Alias name
     * @return int Alias ID
     *
     */
    function createAlias($strName)
    {
        if (!$strName) {
            throw new Mail_Exception("MailApi: strAlias name not set");
        }
        if (!$this->isValidAlias($strName)) {
            throw new Mail_Exception("Invalid characters present is alias create");
        }
        $arrVirtualDomains = $this->_getVirtualDomains();
        foreach ($arrVirtualDomains as $intVirtualDomainId) {
            $arrAliases = null;
            $strSubLogin = $this->getSubLogin();
            $arrDomain = $this->getVirtualDomainById($intVirtualDomainId);

            // check if the alias already exists
            $strQuery = "SELECT MailboxID from tblMailboxes " .
                "WHERE " .
                "ServiceID = '{$this->getServiceId()}' AND " .
                "VirtualDomainID = '{$intVirtualDomainId}' AND " .
                "MailboxTypeID = '".$this->getTypeByHandle('mailboxalias')."' AND " .
                "Username = '{$this->getUsername()}' AND " .
                "LocalPart = '{$strName}' AND " .
                "ISP = '{$this->getIsp()}'";
            $arrAliases = $this->_objDb->getList($strQuery, 'maildelivery');

            if (!$arrAliases) {
                $strQuery = "INSERT INTO tblMailboxes (ServiceID, VirtualDomainID, MailboxTypeID, "
                    . "Username, LocalPart, ISP, UID, GID, Maildir) "
                    . "VALUES ('{$this->getServiceId()}', '{$intVirtualDomainId}', "
                    . "'".$this->getTypeByHandle('mailboxalias')."', '{$this->getUsername()}', "
                    . "'{$strName}', '{$this->getIsp()}', "
                    . "'{$this->getUID()}', "
                    . "'{$this->getGID()}', "
                    . "CONCAT('/share/isp/', '{$this->getIsp()}', '/mail/', "
                    . "LEFT('{$this->getUsername()}', 2), '/', '{$this->getUsername()}', "
                    . "'/Maildir{$strSubLogin}/'))";
                $this->_objDb->insert($strQuery, 'maildelivery');
            }

            /**
             * [Problem ID: 58162]Confirmed that the Ironport/LDAP functionality  is no longer required;
             * Ironport servers have been decommissioned and the LDAP system is no longer used.
             * So removing its related code.
             */
        }
    }

    /**
     * Moves the alias from one mailbox to another
     * @param
     */
    public function moveAlias($strName)
    {
        $arrVirtualDomains = $this->_getVirtualDomains();
        $strSubLogin = $this->getSubLogin();
        if (!empty($arrVirtualDomains)) {
            foreach ($arrVirtualDomains as $intVirtualDomainId) {
                $strQuery = "SELECT MailboxID FROM tblMailboxes "
                    . "WHERE Username = '{$this->getUsername()}' "
                    . "AND ISP = '{$this->getIsp()}' "
                    . "AND LocalPart = '{$strName}' "
                    . "AND VirtualDomainID = '$intVirtualDomainId'";

                $intMailboxId = $this->_objDb->getSingleValue($strQuery, 'MailboxID', 'maildelivery');

                if ($intMailboxId && $intVirtualDomainId) {
                    $strQuery = "UPDATE tblMailboxes SET " .
                        "Maildir = CONCAT('/share/isp/', '{$this->getIsp()}', '/mail/', " .
                        "LEFT('{$this->getUsername()}', 2), '/', '{$this->getUsername()}', " .
                        "'/Maildir{$strSubLogin}/') ".
                        "WHERE MailboxID = {$intMailboxId} AND VirtualDomainID = {$intVirtualDomainId} LIMIT 1";
                    $this->_objDb->query($strQuery, 'maildelivery');

                   /**
                    * [Problem ID: 58162]Confirmed that the Ironport/LDAP functionality  is no longer required;
                    * Ironport servers have been decommissioned and the LDAP system is no longer used.
                    * So removing its related code.
                    */
                }
            }
        }

    }

    /**
     * Destroys the alias
     * @param string $strName Alias name
     */
    public function destroyAlias($strName)
    {
        if (!$strName) {
            throw new Mail_Exception("MailApi: strAlias name not set");
        }
        if (!$this->isValidAlias($strName)) {
            throw new Mail_Exception("Invalid characters present in alias destroy");
        }
        $arrVirtualDomains = $this->_getVirtualDomains();
        if (!empty($arrVirtualDomains)) {
            foreach ($arrVirtualDomains as $intVirtualDomainId) {
                $strQuery = "SELECT MailboxID FROM tblMailboxes "
                    . "WHERE Username = '{$this->getUsername()}' "
                    . "AND ISP = '{$this->getIsp()}' "
                    . "AND LocalPart = '{$strName}' "
                    . "AND VirtualDomainID = '$intVirtualDomainId' "
                    . "AND MailboxTypeID = '{$this->getTypeByHandle('mailboxalias')}'";

                $intMailboxId = $this->_objDb->getSingleValue($strQuery, 'MailboxID', 'maildelivery');

                if ($intMailboxId && $intVirtualDomainId) {
                    $strQuery = "DELETE FROM tblMailboxes "
                        . "WHERE MailboxID = '{$intMailboxId}' "
                        . "AND VirtualDomainID = '{$intVirtualDomainId}' "
                        . "AND Username = '{$this->getUsername()}' "
                        . "AND LocalPart = '{$strName}' LIMIT 1";
                    $this->_objDb->query($strQuery, 'maildelivery');

                /**
                 * [Problem ID: 58162]Confirmed that the Ironport/LDAP functionality  is no longer required;
                 * Ironport servers have been decommissioned and the LDAP system is no longer used.
                 * So removing its related code.
                 */
                }
            }
        }
    }

    // remove the extra option param after ironport migration is complete
    public function checkDomainSettings($intVirtualDomainId)
    {
        //Not interested in doing this for ztest
        if (strpos($this->getUsername(), 'ztest') === 0 && FALSE === $this->getIsIronPortUser() && FALSE === $this->_bolNewIronPortUser) {
            return;
        }
        $arrDomain = $this->_objMailbox->getVirtualDomainById($intVirtualDomainId);

        // if user is not on iron port
        // REMOVE ALL THIS AFTER IRONPORT MIGRATION COMPLETION
        if (FALSE === $this->getIsIronPortUser() && FALSE === $this->_bolNewIronPortUser) {

            //lets check if the domain is with postini.
            $strDefaultOrg = $this->arrOrgs['4'] . substr($this->getUsername(), 0, 2);

            $objPostiniClient = BusTier_BusTier::getClient('postini');
            $strPostiniUser = $this->strDefaultSpamUser.'@'.$arrDomain['Domain'];

            //check to see if the domain actually exists first.
            $objDomainData = $objPostiniClient->displaydomain($arrDomain['Domain']);

            //domain doesn't exist. Lets create!
            if ('unknown' == $objDomainData->org) {

                $objPostiniClient->adddomain($strDefaultOrg, array('domain' => $arrDomain['Domain']));
            }

            //soap throws an exception if user is not valid...
            //which means their is no user.
            try{
                $arrUserData = $objPostiniClient->displayuser($strPostiniUser);
            }
            catch(SoapFault $objException) {

                //capture the message from the soap exception...
                $strExceptionMessage = $objException->getMessage();

                //message indicating no user exists. So lets create one.
                if ("No user '".$strPostiniUser."'." == $strExceptionMessage) {

                    //additional check
                    $bolNoUser = TRUE;
                } else {
                    //else we best throw a new exception.
                    throw new Exception($strExceptionMessage);
                }
            }
        } //if its an ironport user we will hanld e this after we are sure the domain spam settings exist!

        //postini/ironport knows nothing, so lets create a new user.
        // REMOVE ALL OF THIS AFTER IRONPORT MIGRATION COMPLETION
        if (FALSE === $this->getIsIronPortUser() && TRUE === $bolNoUser && FALSE === $this->_bolNewIronPortUser) {

            //and finally add the user.
            $objPostiniClient->adduser($strPostiniUser, array('org' => $strDefaultOrg));


            //and then update the default spam settings
            $arrDefaultSettings = array(
                'filter_offers'   => '12',
                'filter_getrich'  => '12',
                'filter_adult'    => '12',
                'filter_racial'   => '12',
                'filter_bulk'     => '12',
                'junkmail_filter' => 'on'
            );

            //and submit the modify request
            $objPostiniClient->modifyuser($strPostiniUser, $arrDefaultSettings);
        }

        //lets check we have a settings row set.
        $strQuery = "
            SELECT
            intVirtualDomainId
            FROM
            tblMailFilterSettings
            WHERE
            intVirtualDomainId = '".$intVirtualDomainId."'
            ";

        $intDomainId = $this->_objDb->getSingleValue($strQuery, 'intVirtualDomainId', 'maildelivery');

        //if domains entry doesn't exist, we need to insert a new one and set dns processed to 0.
        if (!is_numeric($intDomainId) && empty($intDomainId)) {

            $strQuery = "
                INSERT INTO
                tblMailFilterSettings
                SET
                bolBsb = 1,
                bolSpam = 1,
                bolQuarantine = 0,
                bolPostiniOnlyEmail = 1,
                intAgressiveness = 1,
                bolDnsProcessed = 0,
                bolMarkSpam = 1,
                bolQNotification = 0,
                bolAntiVirus = 1,
                bolMoveToInbox = 0,
                intVirtualDomainId = '{$intVirtualDomainId}'
                ";
            /**
            * [Problem ID: 58162]Confirmed that the Ironport/LDAP functionality  is no longer required;
            * Ironport servers have been decommissioned and the LDAP system is no longer used.
            * So removing its related code.
            */

            $this->_objDb->query($strQuery, 'maildelivery');
        }

        /**
         * [Problem ID: 58162]Confirmed that the Ironport/LDAP functionality  is no longer required;
         * Ironport servers have been decommissioned and the LDAP system is no longer used.
         * So removing its related code.
         */

    }

    /**
     * setSpamFilter
     *
     * @param integer $intSettingId
     * @param integer $intAggressiveness
     * @param boolean $bolQNotification
     * @param boolean $bolAntiVirus
     * @param boolean $bolSpam
     * @param boolean $bolBsb
     * @param mixed $unkVirtualDomain
     * @param boolean $bolSpamTag
     * @param array $arrMailbox
     * @param string $strNotificationEmail
     * @param boolean $bolBlackhole
     * @access public
     * @return void
     */
    public function setSpamFilter($intSettingId,$intAggressiveness,$bolQNotification,$bolAntiVirus,$bolSpam,
        $bolBsb,$unkVirtualDomain,$bolSpamTag,$arrMailbox,$strNotificationEmail,$bolBlackhole)
    {
        //not interested in doing this for ztest
        if ((strpos($this->getUsername(), 'ztest') === 0 && FALSE === $this->getIsIronPortUser() && FALSE === $this->_bolNewIronPortUser) || $this->getDomainOwnerServiceId() != $this->getServiceId()) {
            return;
        }

        $intServiceId = $this->getServiceId();
        if (!$intServiceId) {
            throw new Mail_Exception(
                'Service id not found for component_id = ' .
                $this->_objMailbox->component_id
            );
        }

        if (2 == $intSettingId && !is_numeric($arrMailbox['0']['id'])) {

            throw new Mail_Exception('Mailbox given is is not valid for setting id 2');
        }

        $arrVirtualDomainsIds = $this->_getVirtualDomains();
        $unkVirtualDomain = mysql_real_escape_string($unkVirtualDomain);

        if (!in_array($unkVirtualDomain, $arrVirtualDomainsIds) && 'all' != $unkVirtualDomain ) {
            throw new Mail_Exception(
                'Invalid Virtual Domain ' . $unkVirtualDomain .
                ' for Service ID ' . $intServiceId
            );
        }

        //get user details
        $arrUserData = userdata_service_get($intServiceId);

        if ($unkVirtualDomain != 'all') {
            //get domain details.
            $arrDomain = $this->_objMailbox->getVirtualDomainById($unkVirtualDomain);
        }



        //Lets work out the correct filter ID based on what the customer is setting.
        //this will then map to the correct org.
        if (TRUE == $bolSpam) {

            //set to false first
            $bolMoveToInbox = FALSE;
            //set to null by default.
            $arrPostiniData['notice_address'] = '';
            //turn mail filter on
            $arrPostiniData['junkmail_filter'] = 'on';
            //moves spam to corresponding mailbox
            $bolMoveToInbox = 0;
            //disable user access to web app when quarantine not on
            $arrPostiniData['weblocked'] = 1;

            //Edge protection set to On.
            if (TRUE == $bolBsb) {

                switch($intSettingId) {

                case "1": //move spam to normal inbox
                    $intFilterId = 7;
                    $bolMoveToInbox = 1;
                    break;

                case "2": //Spam On - Deliver to mailbox BSB ON
                    $intFilterId = 7;
                    break;

                case "3": //Spam On - Deliver to spam folder - BSB ON
                    $intFilterId = 8;
                    break;

                case "4": //Spam On - Quarantine On - BSB ON
                    $intFilterId = 6;

                    $bolQuarantine = 1;
                    $bolSpamTag = 0;
                    $arrPostiniData['weblocked'] = 0;

                    if (TRUE == $bolQNotification) {

                        if (isset($strNotificationEmail) && !empty($strNotificationEmail)) {

                            $arrPostiniData['notice_address'] = $strNotificationEmail;
                        } else {

                            $arrPostiniData['notice_address'] = 'postmaster@'.$arrDomain['Domain'];
                        }
                    }
                    break;
                }

            } else {
            //else bsb off
                switch($intSettingId) {

                case "1"://Move spam to normal inbox7
                    $intFilterId = 4;
                    $bolMoveToInbox = 1;
                    break;

                case "2": //Spam On - deliver to mailbox - BSB Off
                    $intFilterId = 4;
                    break;

                case "3": //Spam On - Deliver spam folder - BSB Off
                    $intFilterId = 5;
                    break;

                case "4": //Spam On - Quarantine On - BSB Off
                    $intFilterId = 3;

                    $bolQuarantine = 1;
                    $bolSpamTag = 0;
                    $arrPostiniData['weblocked'] = 0;

                    if (TRUE == $bolQNotification) {

                        if (isset($strNotificationEmail) && !empty($strNotificationEmail)) {

                            $arrPostiniData['notice_address'] = $strNotificationEmail;
                        } else {

                            $arrPostiniData['notice_address'] = 'postmaster@'.$arrDomain['Domain'];
                        }
                    }
                    break;

                }

            }//end if (TRUE == $bolBsb)
        } else {
        //else spam is off.

            //set spam filter to on (this is required for bsb, no spam tagging will actually be done though.. )
            $arrPostiniData['junkmail_filter'] = 'on';

            $bolMoveToInbox   = 1;
            $bolQNotification = 0;
            $bolSpam          = 0;
            $bolSpamTag       = 0;
            if (FALSE === $this->getIsIronPortUser() && FALSE === $this->_bolNewIronPortUser) {
                $bolAntiVirus     = 0;
            }

            //disable user access to web app when quarantine not on
            $arrPostiniData['weblocked'] = 1;

            //lets work out which org we need to set.
            if (TRUE == $bolBsb) {

                $intFilterId = 2; //Spam Off - BSB ON
            } else {

                $intFilterId = 1; //Spam Off - BSB OFF
            }

        }//end if (TRUE == $bolSpam)

        $arrPostiniData['password'] = $arrUserData['password'];

        try {

            //get the customers org
            $arrPostiniData['orgid'] = $this->arrOrgs[$intFilterId] . substr($this->getUsername(), 0, 2);

            // if user is on iron port
            // REMOVE ONLY "IF" STMT AFTER IRONPORT MIGRATION COMPLETION
            if (FALSE === $this->getIsIronPortUser() && FALSE === $this->_bolNewIronPortUser) {

                //submit the data to postini, if no exceptino is thrown then continue and update locally.
                $objPostiniClient = BusTier_BusTier::getClient('postini');
            }

            //customer has selected settings be applied across all domains.
            if ('all' == $unkVirtualDomain) {

                foreach ($arrVirtualDomainsIds as $intVirtualDomainId) {

                    //initalise domainis before updating settings.
                    $this->checkDomainSettings($intVirtualDomainId);
                    $this->_setIsIronPortUser(TRUE);

                    $arrDomain = $this->_objMailbox->getVirtualDomainById($intVirtualDomainId);
                    $arrDomains[] = $this->_objMailbox->getVirtualDomainById($intVirtualDomainId);

                    if (FALSE === $this->getIsIronPortUser()) {

                        //if notifcation is enabled then we want to set the default notice
                        //address to the postmaster for each domain, If not then set to null.
                        $arrPostiniData['notice_address'] = '';
                        if (TRUE == $bolQNotification) {

                            if (isset($strNotificationEmail) && !empty($strNotificationEmail)) {

                                $arrPostiniData['notice_address'] = $strNotificationEmail;
                            } else {

                                $arrPostiniData['notice_address'] = 'postmaster@'.$arrDomain['Domain'];
                            }
                        }


                        $strPostiniUser = $this->strDefaultSpamUser.'@'.$arrDomain['Domain'];
                        $arrUserDetails = $objPostiniClient->modifyuser($strPostiniUser, $arrPostiniData);

                        $objPostiniClient->modifydomain(
                            $arrDomain['Domain'],
                            array('neworg'      => $arrPostiniData['orgid'],
                            'domainname'  => $arrDomain['Domain'],
                            'domainid'    => '',
                            'alias'       => '',
                            'aliasedto'   => '',
                            'substrip'    => '',
                            'org'         => '',
                            'aliasedfrom' => '')
                        );
                    }

                }
            } else {

                $arrDomains[] = $this->_objMailbox->getVirtualDomainById($unkVirtualDomain);

                //initalise domainis before updating settings.
                $this->checkDomainSettings($unkVirtualDomain);
                $this->_setIsIronPortUser(TRUE);

                // if user is not on iron port
                // REMOVE AFTER IRONPORT MIGRATION COMPLETION
                if (FALSE === $this->getIsIronPortUser()) {

                    $strPostiniUser = $this->strDefaultSpamUser.'@'.$arrDomain['Domain'];
                    $arrUserDetails = $objPostiniClient->modifyuser($strPostiniUser, $arrPostiniData);

                    $objPostiniClient->modifydomain(
                        $arrDomain['Domain'],
                        array('neworg'      => $arrPostiniData['orgid'],
                        'domainname'  => $arrDomain['Domain'],
                        'domainid'    => '',
                        'alias'       => '',
                        'aliasedto'   => '',
                        'substrip'    => '',
                        'org'         => '',
                        'aliasedfrom' => '')
                    );
                }
            }

        }
        catch(Exception $objException) {

            throw new Mail_Exception("Unable to update user details to postini, Trace: \n" . print_r($objException, 1));
        }

        foreach ($arrDomains as $arrDomain) {

            $arrMailboxDetails = $this->getMailbox($arrMailbox, $arrDomain['VirtualDomainID']);

            //save black hole settings for doamins.
            $this->toggleBlackhole(
                $arrDomain['VirtualDomainID'],
                $bolBlackhole
            );

            //get the mailbox id then insert for the domanin.
            $strQuery = "
                UPDATE
                tblMailFilterSettings AS mfs
                INNER JOIN
                tblVirtualDomains as vd
                ON
                vd.VirtualDomainID = mfs.intVirtualDomainId
                SET
                mfs.bolBsb = '".$bolBsb."',
                mfs.bolSpam = '".$bolSpam."',
                mfs.bolQuarantine = '".$bolQuarantine."',
                mfs.intAgressiveness = '".$intAggressiveness."',
                mfs.bolMarkSpam = '".$bolSpamTag."',
                mfs.bolQNotification = '".$bolQNotification."',
                mfs.bolAntiVirus = '".$bolAntiVirus."',
                mfs.bolMoveToInbox = '".$bolMoveToInbox."',
                mfs.vchNotificationEmail = '".$strNotificationEmail."',
                mfs.bolDnsProcessed = 0,
                ";

            if ($intSettingId == 2) {

                $strQuery .= "
                    mfs.intSpamMailboxId = '".$arrMailboxDetails['MailboxID']."'
                    ";
            } else {

                $strQuery .= "
                    mfs.intSpamMailboxId = NULL
                    ";
            }

            $strQuery .= "
                WHERE
                mfs.intVirtualDomainId = '".$arrDomain['VirtualDomainID']."'";

            $this->_objDb->query($strQuery, 'maildelivery');

            /**
             * [Problem ID: 58162]Confirmed that the Ironport/LDAP functionality  is no longer required;
             * Ironport servers have been decommissioned and the LDAP system is no longer used.
             * So removing its related code.
             */

        }
    }

    /**
     * getMailbox
     *
     * @param array $arrMailbox
     * @param integer $intVirtualDomainId
     * @param integer $intMailboxId
     * @access public
     * @return void
     */
    public function getMailbox($arrMailbox = NULL, $intVirtualDomainId, $intMailboxId = NULL)
    {
        //query to get the mailbox by either the mailbox name or the mailbox id.
        //The virtual domain id MUST be submitted aswell.
        $strQuery = "
            SELECT

            MailboxID,
            LocalPart
            FROM
            tblMailboxes
            WHERE
            VirtualDomainID = '{$intVirtualDomainId}'
            AND
            ";

        if ('default' == $arrMailbox['0']['type'] && NULL == $intMailboxId) {

            $strQuery .= "
                ( LocalPart = '' OR LocalPart = '{$arrMailbox['0']['login']}' )
                ";
        } elseif ('default' != $arrMailbox['0']['type'] && NULL == $intMailboxId) {

            $strQuery .= "
                LocalPart = '{$arrMailbox['0']['login']}'
                ";
        } else {

            $strQuery .= "
                MailboxID = '{$intMailboxId}'
                ";
        }

        $arrMailboxDetails = $this->_objDb->getArray($strQuery, 'maildelivery');

        return $arrMailboxDetails['0'];
    }


    /*
     * Gets the spam filter type
     * @param $intVirtualDomainId
     * @throws Mail_Exception
     * @return string, any of the ones defined in $this->_arrSpamFilters
     */
    public function getSpamFilter($intVirtualDomainId)
    {
        $intServiceId = $this->getServiceId();
        if (!$intServiceId) {

            throw new Mail_Exception(
                'Service id not found for component_id = '
                . $this->_objMailbox->component_id
            );
        }

        if (empty($intVirtualDomainId) || !is_numeric($intVirtualDomainId)) {
            throw new Mail_Exception('Virtual Domain Id is invalid ' . $intVirtualDomainId);
        }

        $intVirtualDomainId = mysql_real_escape_string($intVirtualDomainId);
        $strQuery = "
            SELECT
            bolBsb,
            bolSpam,
            bolQuarantine,
            bolPostiniOnlyEmail,
            intAgressiveness,
            bolDnsProcessed,
            bolMarkSpam,
            bolQNotification,
            bolAntiVirus,
            intSpamMailboxId,
            bolMoveToInbox,
            vchNotificationEmail
            FROM
            tblMailFilterSettings
            WHERE
            intVirtualDomainId = '{$intVirtualDomainId}'";

        $arrSpamSettings = $this->_objDb->getArray($strQuery, 'maildelivery');

        $arrMailbox = $this->getMailbox(NULL, $intVirtualDomainId, $arrSpamSettings['0']['intSpamMailboxId']);

        $arrSpamSettings['0']['arrMailbox'] = $arrMailbox;

        return $arrSpamSettings;
    }

    /**
     * clearEmailList - clears the approved/denied mail list with postini for chosen customer or ALL domains.
     *
     * @param mixed $unkVirtualDomain
     * @access public
     * @return void
     */
    public function clearEmailList($unkVirtualDomain)
    {
        $arrVirtualDomainsIds = $this->_getVirtualDomains();
        $unkVirtualDomain = mysql_real_escape_string($unkVirtualDomain);

        if (!in_array($unkVirtualDomain, $arrVirtualDomainsIds) && 'all' != $unkVirtualDomain ) {

            throw new Mail_Exception(
                'Invalid Virtual Domain ' . $unkVirtualDomain .
                ' for Service ID ' . $intServiceId
            );
        }

        try {

            if (FALSE === $this->getIsIronPortUser()) {
                $objPostiniClient = BusTier_BusTier::getClient('postini');
            }

            if ('all' == $unkVirtualDomain) {

                foreach ($arrVirtualDomainsIds as $intVirtualDomainId) {

                    //get the domain details
                    $arrDomain = $this->_objMailbox->getVirtualDomainById($intVirtualDomainId);
                    $arrApprovedSenders = array();
                    $arrBlockedSenders = array();

                    if (FALSE === $this->getIsIronPortUser()) {
                        $strPostiniUser = $this->strDefaultSpamUser.'@'.$arrDomain['Domain'];

                        $arrUser = $objPostiniClient->displayuser($strPostiniUser);

                        $arrApprovedSenders = explode(',', $arrUser->approved_senders);
                        $arrBlockedSenders  = explode(',', $arrUser->blocked_senders);

                        foreach ($arrApprovedSenders as $intKey => $strApprovedSender) {

                            $arrApprovedSenders[$intKey] = '-'.trim($strApprovedSender);
                        }

                        foreach ($arrBlockedSenders as $intKey => $strBlockedSender) {

                            $arrBlockedSenders[$intKey] = '-'.trim($strBlockedSender);
                        }
                    }

                    $strApprovedSenders = implode(',', $arrApprovedSenders);
                    $strBlockedSenders  = implode(',', $arrBlockedSenders);

                    if (FALSE === $this->getIsIronPortUser()) {
                        $this->setApprovedEmailList($strApprovedSenders, $intVirtualDomainId);
                        $this->setBlockedEmailList($strBlockedSenders, $intVirtualDomainId);
                    }

                }
            } else {

                $arrDomain = $this->_objMailbox->getVirtualDomainById($unkVirtualDomain);
                $arrApprovedSenders = array();
                $arrBlockedSenders = array();

                if (FALSE === $this->getIsIronPortUser()) {
                    $strPostiniUser = $this->strDefaultSpamUser.'@'.$arrDomain['Domain'];

                    $arrUser = $objPostiniClient->displayuser($strPostiniUser);

                    $strApprovedSenders = $arrUser->approved_senders;
                    $strBlockedSenders = $arrUser->blocked_senders;

                    if ($strApprovedSenders != 'empty' && $strApprovedSenders != '') {

                        $arrApprovedSenders = explode(',', $strApprovedSenders);
                        foreach ($arrApprovedSenders as $intKey => $strApprovedSender) {

                            $arrApprovedSenders[$intKey] = '-'.trim($strApprovedSender);
                        }
                        $strApprovedSenders = implode(',', $arrApprovedSenders);
                        $this->setApprovedEmailList($strApprovedSenders, $unkVirtualDomain);
                    }

                    if ($strBlockedSenders != 'empty' && $strBlockedSenders != '') {

                        $arrBlockedSenders  = explode(',', $strBlockedSenders);
                        foreach ($arrBlockedSenders as $intKey => $strBlockedSender) {

                            $arrBlockedSenders[$intKey] = '-'.trim($strBlockedSender);
                        }
                        $strBlockedSenders  = implode(',', $arrBlockedSenders);
                        $this->setBlockedEmailList($strBlockedSenders, $unkVirtualDomain);
                    }
                }
            }

        } catch(Exception $objException) {

            if (strpos($this->getUsername(), 'ztest') !== 0) {
                throw new Mail_Exception(
                    "Unable to clear the email list with postini. Trace: \n" .
                    print_r($objException, true)
                );
            }
        }
    }

    /**
     * setApprovedEmailList
     *
     * @param string $strApprovedSenders
     * @access public
     * @return void
     */
    public function setApprovedEmailList($strApprovedSenders = NULL,$unkVirtualDomain)
    {
        $intServiceId = $this->getServiceId();

        if (!$intServiceId) {

            throw new Mail_Exception(
                'Service id not found for component_id = '
                . $this->_objMailbox->component_id
            );
        }

        $arrVirtualDomainsIds = $this->_getVirtualDomains();
        $unkVirtualDomain = mysql_real_escape_string($unkVirtualDomain);

        if (!in_array($unkVirtualDomain, $arrVirtualDomainsIds) && 'all' != $unkVirtualDomain ) {

            throw new Mail_Exception(
                'Invalid Virtual Domain ' . $unkVirtualDomain .
                ' for Service ID ' . $intServiceId
            );
        }

        if (FALSE === $this->getIsIronPortUser()) {
            //try to update postini the approved recipients.
            try{

                if ('all' == $unkVirtualDomain) {

                    foreach ($arrVirtualDomainsIds as $intVirtualDomainId) {

                        $arrPostiniData['approved_senders'] = '';

                        //get the domain details
                        $arrDomain = $this->_objMailbox->getVirtualDomainById($intVirtualDomainId);

                        $objPostiniClient = BusTier_BusTier::getClient('postini');
                        $strPostiniUser = $this->strDefaultSpamUser.'@'.$arrDomain['Domain'];

                        if (!empty($this->arrApprovedSenders) && is_array($this->arrApprovedSenders)) {

                            //add pre-approved senders and new approved senders provided by customer.
                            $arrPostiniData['approved_senders'] = implode(',', $this->arrApprovedSenders).',';
                        }

                        $arrPostiniData['approved_senders']    .= $strApprovedSenders;

                        $arrUserDetails = $objPostiniClient->modifyuser($strPostiniUser, $arrPostiniData);

                    }
                } else {
                    //get the domain details
                    $arrDomain = $this->_objMailbox->getVirtualDomainById($unkVirtualDomain);

                    $objPostiniClient = BusTier_BusTier::getClient('postini');
                    $strPostiniUser = $this->strDefaultSpamUser.'@'.$arrDomain['Domain'];

                    if (!empty($this->arrApprovedSenders) && is_array($this->arrApprovedSenders)) {

                        //add pre-approved senders and new approved senders provided by customer.
                        $arrPostiniData['approved_senders'] = implode(',', $this->arrApprovedSenders).',';
                    }

                    $arrPostiniData['approved_senders'] .= $strApprovedSenders;

                    $arrUserDetails = $objPostiniClient->modifyuser($strPostiniUser, $arrPostiniData);
                }
            }
            catch(Exception $objException) {

                if (strpos($this->getUsername(), 'ztest') !== 0) {
                    throw new Mail_Exception("Unable to set the approved recipients list with postini. Trace: \n".print_r($objException, 1));
                }
            }
        }

        if (NULL != $strApprovedSenders) {

            $strApprovedSenders = str_replace("\n", ",", $strApprovedSenders);
            $strApprovedSenders = str_replace("\r", "", $strApprovedSenders);
            $arrApprovedSenders = explode(',', $strApprovedSenders);
        }

        //clean approve list as we have a new list provided by customer.
        $strQuery = "
            DELETE FROM
            tblAddressFilter
            WHERE
            bolApprove = 1
            AND
            ";

        if ('all' == $unkVirtualDomain) {

            $strQuery .= " intServiceID = '{$intServiceId}'";
        } else {

            $strQuery .= " intVirtualDomainID = '{$unkVirtualDomain}'";
        }

        $arrInClause = array();
        foreach ($arrApprovedSenders as $strApproveName) {

            $arrInClause[] = "'".$strApproveName."'";
        }

        if (count($arrInClause) > 0) {
            $strQuery .= " AND vchName NOT IN (" . implode(",", $arrInClause) . ") ";
        }
        $this->_objDb->query($strQuery, 'maildelivery');

        //cycle through all approved senders and insert into the database.
        //if we are setting for ALL domains then we need to cycle through the domains also.
        foreach ($arrApprovedSenders as $strApprovedSender) {

            //if all then lets setup approved to all domains
            if ('all' == $unkVirtualDomain) {

                foreach ($arrVirtualDomainsIds as $intVirtualDomainId) {

                    $strCheck = "SELECT COUNT(1) AS CNT FROM tblAddressFilter WHERE
                        vchName = '{$strApprovedSender}' AND
                        intServiceID = '{$intServiceId}' AND
                        intVirtualDomainID = '{$intVirtualDomainId}' AND
                        bolApprove = '1'";

                    $arrCheck = $this->_objDb->getArray($strCheck, 'maildelivery');
                    if ($arrCheck[0]['CNT'] == 0 ) {
                        $strQuery = "
                            INSERT INTO
                            tblAddressFilter
                            SET
                            vchName = '{$strApprovedSender}',
                            intServiceID = '{$intServiceId}',
                            intVirtualDomainID = '{$intVirtualDomainId}',
                            bolApprove = '1'";

                        $this->_objDb->query($strQuery, 'maildelivery');
                    }
                }

            } else {
            //else lets just set it to the chosen domain

                $strCheck = "SELECT COUNT(1) AS CNT FROM tblAddressFilter WHERE
                    vchName = '{$strApprovedSender}' AND
                    intServiceID = '{$intServiceId}' AND
                    intVirtualDomainID = '{$unkVirtualDomain}' AND
                    bolApprove = '1'";

                $arrCheck = $this->_objDb->getArray($strCheck, 'maildelivery');
                if ($arrCheck[0]['CNT'] == 0 ) {
                    $strQuery = "
                        INSERT INTO
                        tblAddressFilter
                        SET
                        vchName = '{$strApprovedSender}',
                        intServiceID = '{$intServiceId}',
                        intVirtualDomainID = '{$unkVirtualDomain}',
                        bolApprove = '1'";

                    $this->_objDb->query($strQuery, 'maildelivery');
                }
            }
        }

       /**
        * [Problem ID: 58162]Confirmed that the Ironport/LDAP functionality  is no longer required;
        * Ironport servers have been decommissioned and the LDAP system is no longer used.
        * So removing its related code.
        */
    }

    /**
     * setBlockedEmailList
     *
     * @param string $strBlockedSenders
     * @param mixed $unkVirtualDomain
     * @access public
     * @return void
     */
    public function setBlockedEmailList($strBlockedSenders = NULL, $unkVirtualDomain)
    {
        $intServiceId = $this->getServiceId();

        if (!$intServiceId) {

            throw new Mail_Exception(
                'Service id not found for component_id = '
                . $this->_objMailbox->component_id
            );
        }

        $arrVirtualDomainsIds = $this->_getVirtualDomains();
        $unkVirtualDomain = mysql_real_escape_string($unkVirtualDomain);

        if (!in_array($unkVirtualDomain, $arrVirtualDomainsIds) && 'all' != $unkVirtualDomain ) {

            throw new Mail_Exception(
                'Invalid Virtual Domain ' . $unkVirtualDomain .
                ' for Service ID ' . $intServiceId
            );
        }

        if (FALSE === $this->getIsIronPortUser()) {
            //try to update the postini blocked recipients.
            try{

                if ('all' == $unkVirtualDomain) {

                    foreach ($arrVirtualDomainsIds as $intVirtualDomainId) {

                        //get the domain details
                        $arrDomain = $this->_objMailbox->getVirtualDomainById($intVirtualDomainId);

                        $objPostiniClient = BusTier_BusTier::getClient('postini');
                        $strPostiniUser = $this->strDefaultSpamUser.'@'.$arrDomain['Domain'];

                        $arrPostiniData['blocked_senders'] = $strBlockedSenders;

                        $arrUserDetails = $objPostiniClient->modifyuser($strPostiniUser, $arrPostiniData);

                    }
                } else {

                    //get the domain details
                    $arrDomain = $this->_objMailbox->getVirtualDomainById($unkVirtualDomain);
                    $objPostiniClient = BusTier_BusTier::getClient('postini');
                    $strPostiniUser = $this->strDefaultSpamUser.'@'.$arrDomain['Domain'];

                    $arrPostiniData['blocked_senders'] = $strBlockedSenders;

                    $arrUserDetails = $objPostiniClient->modifyuser($strPostiniUser, $arrPostiniData);
                }
            }
            catch(Exception $objException) {

                if (strpos($this->getUsername(), 'ztest') !== 0) {
                    throw new Mail_Exception("Unable to set the approved recipients list with postini. Trace: \n".print_r($objException, 1));
                }
            }
        }

        if (NULL != $strBlockedSenders) {

            $strBlockedSenders = str_replace("\n", ",", $strBlockedSenders);
            $strBlockedSenders = str_replace("\r", "", $strBlockedSenders);
            $arrBlockedSenders = explode(',', $strBlockedSenders);
        }

        //clean approve list as we have a new list provided by customer.
        $strQuery = "
            DELETE FROM
            tblAddressFilter
            WHERE
            bolApprove = 0
            AND
            ";

        if ('all' == $unkVirtualDomain) {

            $strQuery .= " intServiceID = '{$intServiceId}'";
        } else {

            $strQuery .= " intVirtualDomainID = '{$unkVirtualDomain}'";
        }

        $arrInClause = array();
        foreach ($arrBlockedSenders as $strBlockedName) {

            $arrInClause[] = "'".$strBlockedName."'";
        }

        if (count($arrInClause) > 0) {
            $strQuery .= " AND vchName NOT IN (" . implode(",", $arrInClause) . ") ";
        }

        $this->_objDb->query($strQuery, 'maildelivery');

        if (NULL != $strBlockedSenders) {

            $arrBlockedSenders = explode(',', $strBlockedSenders);
        }

        //cyc\n\t\t\nle through all approved senders and insert into the database.
        //if we are setting for ALL domains then we need to cycle through the domains also.
        foreach ($arrBlockedSenders as $strBlockedSender) {

            //if all then lets setup approved to all domains
            if ('all' == $unkVirtualDomain) {

                foreach ($arrVirtualDomainsIds as $intVirtualDomainId) {

                    $strCheck = "SELECT COUNT(1) AS CNT FROM tblAddressFilter WHERE
                        vchName = '{$strBlockedSender}' AND
                        intServiceID = '{$intServiceId}' AND
                        intVirtualDomainID = '{$intVirtualDomainId}' AND
                        bolApprove = '0'";

                    $arrCheck = $this->_objDb->getArray($strCheck, 'maildelivery');
                    if ($arrCheck[0]['CNT'] == 0 ) {
                        $strQuery = "
                            INSERT INTO
                            tblAddressFilter
                            SET
                            vchName = '{$strBlockedSender}',
                            intServiceID = '{$intServiceId}',
                            intVirtualDomainID = '{$intVirtualDomainId}',
                            bolApprove = '0'";

                        $this->_objDb->query($strQuery, 'maildelivery');
                    }
                }

            } else {
            //else lets just set it to the chosen domain

                $strCheck = "SELECT COUNT(1) AS CNT FROM tblAddressFilter WHERE
                    vchName = '{$strBlockedSender}' AND
                    intServiceID = '{$intServiceId}' AND
                    intVirtualDomainID = '{$unkVirtualDomain}' AND
                    bolApprove = '0'";

                $arrCheck = $this->_objDb->getArray($strCheck, 'maildelivery');
                if ($arrCheck[0]['CNT'] == 0 ) {
                    $strQuery = "
                        INSERT INTO
                        tblAddressFilter
                        SET
                        vchName = '{$strBlockedSender}',
                        intServiceID = '{$intServiceId}',
                        intVirtualDomainID = '{$unkVirtualDomain}',
                        bolApprove = '0'";

                    $this->_objDb->query($strQuery, 'maildelivery');
                }
            }

        }

        /**
        * [Problem ID: 58162]Confirmed that the Ironport/LDAP functionality  is no longer required;
        * Ironport servers have been decommissioned and the LDAP system is no longer used.
        * So removing its related code.
        */
    }


    /**
     * getEmailList
     *
     * @param integer $intVirtualDomainId
     * @access public
     * @return void
     */
    public function getEmailList($intVirtualDomainId)
    {
        $intServiceId = $this->getServiceId();

        if (!$intServiceId) {

            throw new Mail_Exception(
                'Service id not found for component_id = '
                . $this->_objMailbox->component_id
            );
        }

        if (empty($intVirtualDomainId) || !is_numeric($intVirtualDomainId)) {

            throw new Mail_Exception('Virtual Domain Id is invalid ' . $intVirtualDomainId);
        }

        $intVirtualDomainId = mysql_real_escape_string($intVirtualDomainId);

        $strQuery = "SELECT
            vchName,
            bolApprove
            FROM
            tblAddressFilter
            WHERE
            intServiceID = '{$intServiceId}'
            AND
            intVirtualDomainID = '{$intVirtualDomainId}'";

        $arrEmailList = $this->_objDb->getArray($strQuery, 'maildelivery');
        $arrReturnList = array();
        //lets split the emails into approved and blocked.
        if (TRUE === is_array($arrEmailList)) {
            foreach ($arrEmailList as $arrEmail) {

                if (1 == $arrEmail['bolApprove']) {

                    $arrReturnList['arrApproved'][] = $arrEmail['vchName'];
                } else {

                    $arrReturnList['arrBlocked'][] = $arrEmail['vchName'];
                }
            }
        }

        return $arrReturnList;
    }

    /**
     * toggleBlackhole changes Blackhole setting for virtual domain
     *
     * @param mixed $intVirtualDomainId
     * @access public
     * @return boolean
     * @throws Mail_Exception
     */
    public function toggleBlackhole($intVirtualDomainId, $bolBlackhole)
    {
        $intVirtualDomainId = mysql_real_escape_string($intVirtualDomainId);
        if (TRUE === $this->getIsIronPortUser() && $bolBlackhole == 1) {
            $this->_getIronPortDomainSettings();
        }

        if (1 == $bolBlackhole || TRUE == $bolBlackhole) {

            $bolBlackhole = '1';
        } else {

            $bolBlackhole = '0';
        }

        $strQuery = 'UPDATE tblVirtualDomains '.
            "SET Blackhole = '$bolBlackhole' ".
            "WHERE VirtualDomainID = '$intVirtualDomainId'";

        $this->_objDb->query($strQuery, 'maildelivery');

        /**
        * [Problem ID: 58162]Confirmed that the Ironport/LDAP functionality  is no longer required;
        * Ironport servers have been decommissioned and the LDAP system is no longer used.
        * So removing its related code.
        */
    }

    /**
     * Reads cached value of MailboxId
     * @param key
     */
    protected function _readMailboxId($strKey)
    {
        $strHash = md5($strKey);
        if (isset($this->_intMailboxId[$strHash])) {
            return $this->_intMailboxId[$strHash];
        }
    }

    /**
     * Saves cached value of
     */
    protected function _saveMailboxId($intId, $strKey)
    {
        $this->_intMailboxId[md5($strKey)] = $intId;
    }

    /**
     * Reads cached value of MailboxId
     * @param $strKey
     * @return mixed it returns integer (VirtualDomainID) or array (row from DB)
     */
    protected function _readMailUserId($strKey)
    {
        $strHash = md5($strKey);
        if (isset($this->_intMailUserId[$strHash])) {
            return $this->_intMailUserId[$strHash];
        }
    }

    /**
     * _flushVirtualDomains - flushes cached virtual domains
     *
     * @access protected
     * @return void
     */
    protected function _flushVirtualDomains()
    {
        $this->_arrVirtualDomains = array();
    }

    /**
     * Saves cached value of MailboxId
     */
    protected function _saveMailUserId($intId, $strKey)
    {
        $this->_intMailUserId[md5($strKey)] = $intId;
    }

    /**
     * Reads cached value of VirtualDomains
     * @param key
     */
    protected function _readVirtualDomains($strKey)
    {
        $strHash = md5($strKey);
        if (isset($this->_arrVirtualDomains[$strHash])) {
            return $this->_arrVirtualDomains[$strHash];
        }
    }
    /**
     * Saves cached value of VirtualDomains
     */
    protected function _saveVirtualDomains($arrIds, $strKey)
    {
        $this->_arrVirtualDomains[md5($strKey)] = $arrIds;
    }

    /**
     * Gets the mail user ID for a user from the mail auth DB
     *
     * @access public
     * @param none
     * @return int The mail user ID
     */
    public function _getMailUserId()
    {
        $strQuery = "SELECT id FROM {$this->getMailusersTbl()} "
            . "WHERE username = '{$this->getMailboxUsername()}'";
        $intMailUserId = $this->_readMailUserId($strQuery);
        if (!$intMailUserId) {
            $intMailUserId = $this->_objDb->getSingleValue($strQuery, 'id', 'mailauth');
            $this->_saveMailUserId($intMailUserId, $strQuery);
        }
        return $intMailUserId;
    }

    /**
     * Creates a mail user in the mail auth DB if one does not already exist
     *
     * @access public
     * @param integer $intMailUserId Id of the user in auth database
     * @return int The mail user ID
     *
     */
    protected function _createMailUser($intMailUserId = null)
    {
        if (!$intMailUserId) {
            $strCryptPassword = mysql_real_escape_string(Crypt_Crypt::encrypt($this->getPassword(), 'mailauth'));

            $strQuery = "REPLACE INTO {$this->getMailusersTbl()} "
                . "(username, passwd, sysuser, homedir, accessed) "
                . "VALUES ('{$this->getMailboxUsername()}', "
                . "'$strCryptPassword', "
                . "'{$this->getUID()}', "
                . "CONCAT('/share/isp/', '{$this->getIsp()}', '/mail/', "
                . "LEFT('{$this->getUsername()}', 2), '/', '{$this->getUsername()}'), "
                . "NOW())";

            $intMailUserId = $this->_objDb->insert($strQuery, 'mailauth');

            $this->_setPaidFlag($intMailUserId);

            $strQuery = "REPLACE INTO {$this->getMailmgtTbl()} "
                . "(id, intServiceID, created, destroyed) "
                . "VALUES ('{$intMailUserId}', '{$this->getServiceId()}', 0, 0)";

            $this->_objDb->query($strQuery, 'mailauth');

        } else {

            // Q. If the mailid does exist then why are we trying to re-create it?
            // A. It will be because the user has just deleted the mailbox and re-added
            //    it before component_destroy has had a chance to run
            //    so what do we need to do?
            //    1. update mailmgt_isp.destroyed to 0
            //    2. set payed flag (important that we do this before resetting password as changepassword() uses paid flag when changing password in ironport)
            //    3. the user may also have entered a different password the second time around, so reset that too

            $strQuery = "UPDATE {$this->getMailmgtTbl()} SET destroyed=0 "
                . "WHERE id =  $intMailUserId";
            $this->_objDb->query($strQuery, 'mailauth');
            $this->_setPaidFlag($intMailUserId);
            $this->changePassword($this->getPassword());
        }
        return $intMailUserId;
    }

    /**
     * Returns 1 when the service is paid for else 0 when free
     * NB: Updated from problem 52640 - only return 1 when account is active or has been active so as not to allow smtp auth prior to account activation
     *
     * @access protected
     * @return int
     */
    protected function _isPaidForProduct()
    {
        $strQuery = "SELECT " .
            "  CASE " .
            //This case has been added to reject all dial up users external smtp auth. Requested by Dan K.
            "    WHEN (sd.signup_via_portal = 'Y' AND sd.requires LIKE '%friaco%' AND sd.isp NOT IN('freeonline', 'force9')) THEN 0 " .
            "    WHEN sd.type = 'free' THEN 0 " .
            "    WHEN s.status IN ('queued-reactivate','active','queued-deactivate') THEN 1 " .
            "    ELSE 0 " .
            "  END AS paidfor " .
            "FROM " .
            "  service_definitions sd " .
            "INNER JOIN userdata.services s ON " .
            "  sd.service_definition_id = s.type " .
            "WHERE " .
            "  s.service_id = {$this->getServiceId()}";

        return $this->_objDb->getSingleValue($strQuery, 'paidfor', 'product');
    }

    /**
     * Gets the virtual domain Id for a user domain
     *
     * @return array
     */
    protected function _getVirtualDomains()
    {
        $intServiceId = $this->getDomainOwnerServiceId();

        $strQuery = 'SELECT vd.VirtualDomainID '
            . 'FROM tblVirtualDomains vd '
            . 'LEFT JOIN tblResellerDomain rd ON vd.VirtualDomainID = rd.intVirtualDomainId '
            . 'LEFT JOIN tblReseller r ON rd.intResellerId = r.intResellerId '
            . "WHERE (vd.ServiceID = $intServiceId AND r.intServiceId IS NULL) "
            . "OR (r.intServiceID = $intServiceId AND (vd.ServiceID = $intServiceId OR vd.ServiceID = 0))";

        $arrVirtualDomains = $this->_readVirtualDomains($strQuery);
        if (!$arrVirtualDomains) {
            $arrVirtualDomains = $this->_objDb->getArray($strQuery, 'maildelivery');

            $arrTmp = array();
            if (is_array($arrVirtualDomains)) {
                foreach ($arrVirtualDomains as $arrVirtualDomain) {
                    $arrTmp[] = $arrVirtualDomain['VirtualDomainID'];
                }
            }

            if (!empty($arrTmp)) {
                $this->_saveVirtualDomains($arrTmp, $strQuery);
            }

            $arrVirtualDomains = $arrTmp;
        }

        return $arrVirtualDomains;
    }

    /**
     * getVirtualDomainsDetails retrieves all virtual domains from DB
     *
     * @access public
     * @return array
     */
    public function getVirtualDomainsDetails()
    {
        $arrVirtualDomains = array();
        $intServiceId = $this->getDomainOwnerServiceId();
        $strQuery = 'SELECT vd.VirtualDomainID, vd.Domain, vd.Blackhole '
            . 'FROM tblVirtualDomains vd '
            . 'LEFT JOIN tblResellerDomain rd ON vd.VirtualDomainID = rd.intVirtualDomainId '
            . 'LEFT JOIN tblReseller r ON rd.intResellerId = r.intResellerId '
            . "WHERE "
            . " (vd.ServiceID = $intServiceId"
            . "  AND r.intServiceId IS NULL"
            . " ) OR (r.intServiceID = $intServiceId "
            . "       AND (vd.ServiceID = $intServiceId "
            . "            OR vd.ServiceID = 0))";

        $arrVirtualDomains = $this->_readVirtualDomains($strQuery);

        if (!is_array($arrVirtualDomains)) {
            $arrVirtualDomains = $this->_objDb->getArray($strQuery, 'maildelivery');

            if (is_array($arrVirtualDomains)) {
                $this->_saveVirtualDomains($arrVirtualDomains, $strQuery);
            }
        }

        return $arrVirtualDomains;
    }

    /**
     * getVirtualDomainByName - looks for virtual domain id based on domains name
     *
     * @param string $strName
     * @access public
     * @return array
     */
    public function getVirtualDomainByName($strName)
    {
        if (empty($strName)) {
            throw new Mail_Exception('Invalid name of the domain.');
        }

        $strName = mysql_real_escape_string($strName);
        $intServiceId = $this->getDomainOwnerServiceId();

        if (!is_numeric($intServiceId) && $intServiceId > 0) {
            throw new Mail_Exception('Invalid service Id');
        }

        $strQuery = 'SELECT vd.VirtualDomainID, vd.Domain, vd.Blackhole, vd.AntiSpam ' .
            'FROM tblVirtualDomains vd '.
            'LEFT JOIN tblResellerDomain rd ON vd.VirtualDomainID = rd.intVirtualDomainId ' .
            'LEFT JOIN tblReseller r ON rd.intResellerId = r.intResellerId ' .
            "WHERE vd.Domain = '$strName' AND " .
            " ((vd.ServiceID = $intServiceId" .
            "   AND r.intServiceId IS NULL" .
            " ) OR (r.intServiceID = $intServiceId " .
            "       AND (vd.ServiceID = $intServiceId " .
            "            OR vd.ServiceID = 0)))";

        $arrResult = $this->_objDb->getArray($strQuery, 'maildelivery');

        if (!is_array($arrResult)) {
            throw new Mail_Exception("Service ID $intServiceId does not have domain $strName");
        }

        return $arrResult[0];
    }

    /**
     * isLocalPartAvailableOnDomain
     *
     * @param string $strLocalPart
     * @access public
     * @return boolean
     */
    public function isLocalPartAvailableOnDomain($strLocalPart)
    {
        $intServiceId = $this->getDomainOwnerServiceId();
        $strLocalPart = mysql_real_escape_string($strLocalPart);

        $strQuery = 'SELECT ' .
            '  COUNT(1) as CNT ' .
            'FROM ' .
            '  tblMailboxes mb ' .
            'INNER JOIN tblVirtualDomains vd ' .
            '  ON mb.VirtualDomainID = vd.VirtualDomainID ' .
            'LEFT JOIN tblResellerDomain rd ON vd.VirtualDomainID = rd.intVirtualDomainId ' .
            'LEFT JOIN tblReseller r ON rd.intResellerId = r.intResellerId ' .
            'WHERE ' .
            "  ( (vd.ServiceID = $intServiceId " .
            "     AND r.intServiceId IS NULL" .
            "    ) OR (r.intServiceID = $intServiceId " .
            "          AND (vd.ServiceID = $intServiceId " .
            "               OR vd.ServiceID = 0))" .
            "  ) AND mb.LocalPart = '$strLocalPart'";

        return $this->_objDb->getSingleValue($strQuery, 'CNT', 'maildelivery') == 0 ? FALSE : TRUE;


    }

    /**
     * getVirtualDomainById
     *
     * @param integer $intVirtualDomainId
     * @access public
     * @return array
     */
    public function getVirtualDomainById($intVirtualDomainId)
    {
        if (empty($intVirtualDomainId) || !is_numeric($intVirtualDomainId)) {
            throw new Mail_Exception('Invalid ID of the domain. Passed value: '. $intVirtualDomainId);
        }

        $intVirtualDomainId = mysql_real_escape_string($intVirtualDomainId);
        $intServiceId = $this->getDomainOwnerServiceId();

        if (!is_numeric($intServiceId) && $intServiceId > 0) {
            throw new Mail_Exception('Invalid service Id');
        }

        $strQuery = 'SELECT vd.VirtualDomainID, vd.Domain, vd.Blackhole, vd.AntiSpam ' .
            'FROM tblVirtualDomains vd '.
            'LEFT JOIN tblResellerDomain rd ON vd.VirtualDomainID = rd.intVirtualDomainId ' .
            'LEFT JOIN tblReseller r ON rd.intResellerId = r.intResellerId ' .
            "WHERE vd.VirtualDomainID = '$intVirtualDomainId' AND " .
            "  ( (vd.ServiceID = $intServiceId " .
            "     AND r.intResellerId IS NULL " .
            "    ) OR (r.intServiceID = $intServiceId " .
            "          AND (vd.ServiceID = $intServiceId " .
            "               OR vd.ServiceID = 0))" .
            "  )";

        $arrResult = $this->_objDb->getArray($strQuery, 'maildelivery');

        if (!is_array($arrResult)) {
            throw new Mail_Exception("Service ID $intServiceId does not have domain with Id: " . $intVirtualDomainId);
        }

        return $arrResult[0];
    }

    /**
     * Creates a virtual domain in the mail delivery DB for a given user
     *
     */
    protected function _createVirtualDomain()
    {
        // Iterate over all needed domains, if non existent - create it
        $arrUserDomains = $this->getUserDomains();
        foreach ($arrUserDomains as $strUserDomain) {
            $strQuery = "SELECT VirtualDomainID FROM tblVirtualDomains " .
                "WHERE Domain = '{$strUserDomain}'";
            $bolExists = $this->_objDb->getSingleValue($strQuery, 'VirtualDomainID', 'maildelivery');
            //if the domain already exists dont bother creating it :)
            if ($bolExists!='') {
                continue;
            }
            $strQuery = "INSERT INTO tblVirtualDomains "
                . "(ServiceID, Domain, Username, ISP, TLK, RelayHost) "
                . "VALUES ('{$this->getServiceId()}', "
                . "'{$strUserDomain}', "
                . "'{$this->getUsername()}', "
                . "'{$this->getIsp()}', "
                . "LEFT('{$this->getUsername()}', 2),0)";
            $intVirtualDomainId = $this->_objDb->insert($strQuery, 'maildelivery');
            $this->_flushVirtualDomains();

            /**
            * [Problem ID: 58162]Confirmed that the Ironport/LDAP functionality  is no longer required;
            * Ironport servers have been decommissioned and the LDAP system is no longer used.
            * So removing its related code.
            */


        }
    }

    /**
     * Gets the mailbox Id of the mailbox for a customer.
     *
     */
    protected function _getMailboxId($intVirtualDomainId, $bolBlackholed = true)
    {

        if ($this->getMailboxType() == 'default') {
            $strLP = "AND (LocalPart = ' ' OR LocalPart = '{$this->getLocalPart()}') ";
        } else {
            $strLP = "AND LocalPart = '{$this->getLocalPart()}' ";
        }
        // The following constraint is needed for redirects to correctly
        // identify a redirect mailbox in case there's a name clash with an alias
        if ($this->getMailboxType() == 'redirect') {
            $strMailboxType = "AND MailboxTypeID = '{$this->getMailboxTypeId()}' ";
        } else {
            $strMailboxType = "";
        }

        $strQuery = "SELECT MailboxID FROM tblMailboxes "
            . "WHERE Username = '{$this->getUsername()}' "
            . "AND ISP = '{$this->getIsp()}' "
            . $strLP
            . "AND VirtualDomainID = '$intVirtualDomainId'";
        //. "AND MailboxTypeID = '{$this->getMailboxTypeId()}'";

        $intMailboxId = $this->_readMailboxId($strQuery);
        if (!$intMailboxId) {
            $intMailboxId = $this->_objDb->getSingleValue($strQuery, 'MailboxID', 'maildelivery');
            if (!$intMailboxId && $bolBlackholed) {
                // default Mailbox not found, try to find a blackholed 'postmaster' mailbox
                // but only if we want the blackholed mailbox
                $strQuery = "SELECT MailboxID FROM tblMailboxes " .
                    "WHERE Username = '{$this->getUsername()}' " .
                    "AND LocalPart = 'postmaster' " .
                    "AND VirtualDomainID = '$intVirtualDomainId' " .
                    "LIMIT 1";

                $intMailboxId = $this->_objDb->getSingleValue($strQuery, 'MailboxID', 'maildelivery');
            }
            $this->_saveMailboxId($intMailboxId, $strQuery);
        }
        return $intMailboxId;
    }

    protected function getUID()
    {
        return self::UID;
    }

    protected function getGID()
    {
        return self::GID;
    }

    /**
     * Create the mailbox in Mail DB
     *
     */
    protected function _createMailbox($intVirtualDomainId = null)
    {
        $strSubLogin = $this->getSubLogin();

        if (empty($intVirtualDomainId)) {
            throw new Mail_Exception('VirtualDomaindId must be non-zero');
        }

        $floInsertStart = microtime(true);
        $strQuery = "INSERT INTO tblMailboxes (ServiceID, MailboxTypeID, VirtualDomainID, Username, "
            . "LocalPart, ISP, UID, GID, Maildir) "
            . "VALUES ('{$this->getServiceId()}', "
            . "'{$this->getMailboxTypeId()}', "
            . "'{$intVirtualDomainId}', "
            . "'{$this->getUsername()}', "
            . "'{$this->getLocalPart()}', "
            . "'{$this->getIsp()}', "
            . "'{$this->getUID()}', "
            . "'{$this->getGID()}', "
            . "CONCAT('/share/isp/', '{$this->getIsp()}', '/mail/', "
            . "LEFT('{$this->getUsername()}', 2), '/', "
            . "'{$this->getUsername()}', '/Maildir{$strSubLogin}/'))";

        $intMailboxId = $this->_objDb->insert($strQuery, 'maildelivery');
        $floInsertEnd = microtime(true);
        $intDurationMs = ($floInsertStart * 1000) - ($floInsertEnd * 1000);

        // P66781 - Appears to sometimes return 0 even when the insert
        // succeeded. For Mailboxes this doesn't matter, but for Redirects
        // it causes the foreign key to fail.
        if (!$intMailboxId) {

            // In the first instance try a SELECT to get the last inserted id..
            $strMailboxQuery = "SELECT MailboxID FROM tblMailboxes WHERE "
                             . "ServiceID = '{$this->getServiceId()}' AND "
                             . "MailboxTypeID = '{$this->getMailboxTypeId()}' AND "
                             . "VirtualDomainID = '{$intVirtualDomainId}' AND "
                             . "Username = '{$this->getUsername()}' AND "
                             . "LocalPart = '{$this->getLocalPart()}' AND "
                             . "ISP = '{$this->getIsp()}' AND "
                             . "UID = '{$this->getUID()}' AND "
                             . "GID = '{$this->getGID()}' AND "
                             . "Maildir = CONCAT('/share/isp/', '{$this->getIsp()}', '/mail/', "
                             . "LEFT('{$this->getUsername()}', 2), '/', "
                             . "'{$this->getUsername()}', '/Maildir{$strSubLogin}/')";

            $intMailboxId =  $this->_objDb->getSingleValue(
                $strMailboxQuery, 'MailboxID', 'maildelivery'
            );

            if (!$intMailboxId) {
                // Report via autoproblem - this should *never* happen.
                // Do not throw an Exception else it will impair customer experience.
                pt_raise_autoproblem(
                    'Inserted Mailbox ID returned 0',
                    'Inserted Mailbox ID returned 0 when creating mailbox',
                    'When creating a Mailbox in dbMailDelivery, the last insert ID was returned as 0 although the query did not cause an exception to be thrown.',
                    "$strQuery\n\nCompleted in $intDurationMs ms\n\n".print_r(debug_backtrace(), true)
                );
            }
        }

        /**
        * [Problem ID: 58162]Confirmed that the Ironport/LDAP functionality  is no longer required;
        * Ironport servers have been decommissioned and the LDAP system is no longer used.
        * So removing its related code.
        */

        return $intMailboxId;
    }

    protected function _createRedirect($intMailboxId, $strRedirectTo)
    {
        $strQuery = "INSERT INTO tblRedirects (MailboxID, Destination) "
            . "VALUES ('$intMailboxId', '$strRedirectTo')";
        return $this->_objDb->insert($strQuery, 'maildelivery');
    }

    protected function _changeRedirectTo($intMailboxId, $strRedirectTo)
    {
        $strQuery = "UPDATE tblRedirects SET Destination = '{$strRedirectTo}' "
            . "WHERE MailboxID = '{$intMailboxId}'";
        $this->_objDb->query($strQuery, 'maildelivery');
    }

    protected function _hasMailingList($intVirtualDomainId)
    {
        $strQuery = "SELECT COUNT(1) as CNT FROM tblMailboxes WHERE VirtualDomainID = $intVirtualDomainId AND MailboxTypeID = 3";
        return $this->_objDb->getSingleValue($strQuery, 'CNT', 'maildelivery') > 0 ? TRUE : FALSE;
    }


}
