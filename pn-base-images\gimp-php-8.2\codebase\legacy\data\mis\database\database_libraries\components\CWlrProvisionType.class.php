<?php

/**
 * Retrieving supplier platform id for supplier platform handle.
 *
 * @package       LegacyCodebase
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2010 Plusnet
 * @since      File available since 2010-03-09
 */
/**
 * CWlrProvisionType class
 *
 * @package       LegacyCodebase
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2010 Plusnet
 */
class CWlrProvisionType
{
    /**
     * WLR2
     * Constant which maps to the handle created in tblSupplierPlatform for Wlr2
     *
     * @const String
     */
    const WLR2 = 'WLR2';

    /**
     * WLR3
     * Constant which maps to the handle created in tblSupplierPlatform for Wlr3
     *
     * @const String
     */
    const WLR3 = 'WLR3';

    /**
     * Function to get supplier platform id form handle
     *
     * @param String $strHandle
     *
     * @retrun int $intSupplierPlatformId
     */
    public function getSupplierPlatformIdFromHandle($strHandle)
    {
        $dbCon = get_named_connection_with_db('product_reporting');

        $strQuery = "SELECT
                     intSupplierPlatformID
                     FROM
                     products.tblSupplierPlatform
                     WHERE
                     vchHandle = '%s'";

        $strQuery = sprintf(
            $strQuery,
            PrimitivesRealEscapeString($strHandle, $dbCon)
        );

        $result = PrimitivesQueryOrExit($strQuery, $dbCon);

        $intSupplierPlatformId = PrimitivesResultGet($result, 'intSupplierPlatformID');

        return $intSupplierPlatformId;
    }
}
