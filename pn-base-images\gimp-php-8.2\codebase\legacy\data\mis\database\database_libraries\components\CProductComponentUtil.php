<?php
/**
 * Moving some static functions out of CProductComponent to make them testable.
 *
 **/
class CProductComponentUtil
{
    /**
     * Calculate the end date of a contract given the start date and the tariff
     *
     * @access private
     * <AUTHOR>
     *
     * @param int     $uxtContractStart  The start of the contract we want to calculate end date for
     * @param int     $intTariffID       Tariff Id of the contract
     * @param int     $intServiceId      The service we are trying to deal with
     * @param boolean $bolRetentionOffer Is the contract a retention offer
     *
     * @return int
     */
    public function calculateContractEnd($uxtContractStart, $intTariffID, $intServiceId, $bolRetentionOffer = false)
    {
        if (!$uxtContractStart || empty($intTariffID)) {
            return false;
        }

        // We need to get a refreshed instance, as during account activation
        // the details in this array will have changed.
        // Cannot use Core_Service due to the transaction issue
        $service = $this->userdataServiceGet($intServiceId);

        $arrTariff   = $this->getTariffDetails($intTariffID);

        // Set up a BillingDate object
        $billingDate = Core_BillingDate_Facade::getBillingDate(
            new UnsignedInt($intServiceId),
            I18n_Date::fromString($service['next_invoice']),
            new UnsignedInt(
                ProductComponent_Tariff::getContractLengthByHandle($arrTariff['strContractLengthHandle'])
                + ($bolRetentionOffer ? 2 : 0)
            ),
            new UnsignedInt($service['invoice_day'])
        );

        // The following will get the service period that the passed-in date falls in, so the end of the service
        // period will be the day before the next invoice (or what the next invoice would be after the date
        // passed in). This will be the real contract end date
        $servicePeriod =
            $billingDate->getBillingServicePeriodAlignToDate(I18n_Date::fromTimestamp($uxtContractStart));

        return (int) $servicePeriod->getEnd()->getTimestamp();
    }



    /**
     * Wrapper to userdata_service_get
     *
     * @param int $serviceId Service id
     *
     * @return array
     **/
    protected function userdataServiceGet($serviceId)
    {
        return userdata_service_get($serviceId, true);
    }


    /**
     * Get the record for a tariff id.  Data is cached
     *
     * @param int $intTariffID the Tariff ID
     *
     * @return array
     */
    public function getTariffDetails($intTariffID)
    {
        static $dataCache = array();

        $intTariffID = (int) $intTariffID;

        if (!array_key_exists($intTariffID, $dataCache)) {
            $query = <<< EOQ
SELECT
    t.intTariffID,
    t.intContractLengthID,
    cl.vchHandle                                            AS strContractLengthHandle,
    cl.vchDisplayName                                       AS strContractLengthDisplayName,
    t.intPaymentFrequencyID,
    pf.vchHandle                                            AS strPaymentFrequencyHandle,
    t.intQuantityFrom,
    t.intQuantityTo,
    t.intCostIncVatPence,
    t.bolAutoRenew,
    t.intNextTariffID,
    t.intNoticePeriodDays,
    IF(t.dtmStart IS null, 0, UNIX_TIMESTAMP(t.dtmStart))   AS uxtStart,
    IF(t.dtmEnd IS null, 0, UNIX_TIMESTAMP(t.dtmEnd))       AS uxtEnd
FROM
    tblTariff                       AS t
    INNER JOIN tblContractLength    AS cl ON t.intContractLengthID = cl.intContractLengthID
    INNER JOIN tblPaymentFrequency  AS pf ON t.intPaymentFrequencyID = pf.intPaymentFrequencyID
WHERE
    intTariffID = $intTariffID
EOQ;

            $conn = get_named_connection_with_db('dbProductComponents_reporting');
            $resResult = PrimitivesQueryOrExit($query, $conn, 'Get the tariff details');

            $dataCache[$intTariffID] = PrimitivesResultGet($resResult);
        }

        return $dataCache[$intTariffID];
    }

}
