<?php

/**
 * Class MarketExchangeHelper
 * <AUTHOR> <<EMAIL>>
 */
class Core_MarketExchangeHelper
{
    /**
     * Updating the customer market and exchange info
     *
     * @param String                       $exchangeCode   exchange code to set
     * @param String                       $serviceId      service id for the info
     * @param AccountChange_ExchangeHelper $exchangeHelper the exchange helper from account change
     * @param String                       $marketId       market id
     *
     * @return void
     */
    public function updateCustomerExchangeData($exchangeCode, $serviceId, $exchangeHelper, $marketId = null)
    {
        if ($marketId === null) {
            $marketId = LineCheck_Market::getMarketFromExchange($exchangeCode)->getMarketId();
        }
        $exchangeHelper->maintainCustomerExhangeData($serviceId, $marketId, $exchangeCode);
    }

    /**
     * Validates the line check is the correct type and valid
     *
     * @param LineCheck_Result $lineCheckResult the result of the line check
     *
     * @return bool
     */
    public function isLineCheckValid($lineCheckResult)
    {
        return $lineCheckResult instanceof LineCheck_Result && $lineCheckResult->isValidResult();
    }

    /**
     * Fetches the line check info from the database
     *
     * @param String $serviceId service id
     *
     * @return LineCheck_Result
     */
    public function getLineCheckFromDatabase($serviceId)
    {
        return LineCheck_Result::getLatestResultByServiceId($serviceId);
    }

    /**
     * Perform a linecheck
     *
     * @param String $service service data
     *
     * @return LineCheck_IRequest
     *
     * @throws LineCheck_RequestManagerException
     */
    public function getLineCheckRequest($service)
    {
        return LineCheck_RequestManager::factory(
            LineCheck_RequestManager::LINE_CHECK_TYPE_TELNO,
            $service->getCliNumber(),
            null,
            null,
            null
        );
    }

    /**
     * Instantiates a line check object
     *
     * @param LineCheck_IRequest $request the request
     *
     * @return LineCheck_LineCheck
     */
    public function buildLineCheck($request)
    {
        return new LineCheck_LineCheck($request);
    }

    /**
     * Instantiates a line check result
     *
     * @return LineCheck_Result
     */
    public function buildLineCheckResult()
    {
        return new LineCheck_Result();
    }

    /**
     * Execute a primitives query
     *
     * @param String $query  query for legacy db stuff
     * @param String $dbName name of the db connection
     *
     * @throws Exception
     * @return void
     */
    public function executePrimitivesQuery($query, $dbName)
    {
        $dbConnection = $this->getNamedConnection($dbName);
        PrimitivesQueryOrExit($query, $dbConnection);
    }

    /**
     * Wrapping a legacy function
     *
     * @param String $dbName name of db connection
     *
     * @return array|mixed|null
     */
    private function getNamedConnection($dbName)
    {
        return get_named_connection_with_db($dbName);
    }
}
