<?php
/*
 * Created on Mar 2, 2005
 *
 * To change the template for this generated file go to
 * Window - Preferences - PHPeclipse - PHP - Code Templates
 */

/**
 * The currently logged in users details and preferances
 *
 * The currently logged in users details and preferances 
 *
 * @package    Internationalisation
 * @access     public
 *
 * <AUTHOR>
 *
 * @version    $Id: CCurrentUser.inc,v 1.2 2005-05-04 11:56:29 gfraser Exp $
 * @filesource
 *
 */

class CCurrentUser
{
// Member variables

        /**
		 * Array of user preferences
		 *
		 *
		 * @access private
		 * @var    array A simple array for containing user preferences, will
		 * eventually be pulled from a database
		 */
		
		
		function prvUserPreferences($bolIndex ='',$uknNewValue = null)
		{
			static $m_arrUserPreferences = array();
			
			if($bolIndex=='' &&  is_null($uknNewValue))
			{
				return $m_arrUserPreferences;
			}
			
			if($bolIndex=='' && is_array($uknNewValue))
			{
				$m_arrUserPreferences = $uknNewValue;
				return true;
			}
			
			if($bolIndex!='' && is_null($uknNewValue))
			{
				if(!isset($m_arrUserPreferences[$bolIndex]))
				{
					return null;
				}
				
				return $m_arrUserPreferences[$bolIndex];
			}
			
			if($bolIndex!='' && (!is_null($uknNewValue)))
			{
				$m_arrUserPreferences[$bolIndex] = $uknNewValue;
				return true;
			}
			
			return false;
			
		} // func: prvUserPreferences

// Constructor(s)
		
	// This class contains only static calls
		

// Getters

		/**
		 * Get the users preferance setting for a particular option
		 *
		 * Get the users preferance setting for a particular option
		 * For each preference is referanced by a unique handle.
		 * 
		 * <code>
		 * $strNestingStyle = CCurrentUser::GetPreference('FORUM_POST_NESTING_STYLE');
		 * </code>
		 *
		 * <AUTHOR>
		 * @access public
		 * @static
		 *
		 * @param str The handle of the preferance type sought
		 *
		 * @return string The value of the choosen preferance
		 * @throws none none
		 *
		 */
		
		function GetPreferance($strPreferanceHandle)
		{
				$arrUserPreferances = CCurrentUser::prvUserPreferences();
		        if(count($arrUserPreferances) < 1)
		        {
		        	CCurrentUser::prvRetrieveUserPreferences(CCurrentUser::GetCurrentLoginType(),CCurrentUser::GetCurrentUserID());
		        }
				
				
				if(!is_null($unkResult = CCurrentUser::prvUserPreferences($strPreferanceHandle)))
				{
					return $unkResult;
				}

				if($strPreferanceHandle != 'LOCALE')
				{
					return false;
				}
					
				/*
				 * The locale is not set so we default to (in preferance order)
				 * 1.) The first available type requested by the users browser
				 * 2.) The default locale
				 * 
				 */
				
				
				if(!is_null($unkResult = CCurrentUser::prvUserPreferences('AUTONEGOTIATED_LOCALE')))
				{
					return $unkResult;
				}
					
				$strDefaultLocale = CCurrentUser::GetDefaultLocale();

				if(class_exists('CPage') && CPage::bolInPage())
				{
					if(($strAutoNegotiatedLocale = CPage::BrowserLanguageNegotiation(CCurrentUser::prvGetLocalesAvailable(),$strDefaultLocale)) !='')
					{
						CCurrentUser::prvUserPreferences('AUTONEGOTIATED_LOCALE',$strAutoNegotiatedLocale);
						return $strAutoNegotiatedLocale;
					}
				}
					
				return $strDefaultLocale;
				
		} // func: GetPreferance

		function GetDefaultLocale()
		{
			return 'en_UK.UTF-8';
		} // func: GetDefaultLocale
				
		/**
		 * Get the type of user currently accessing our functionality
		 * 
		 * Known types of users are currently: Script, Staff and customer
		 * 
		 * <code>
		 * $strUserType = CCurrentUser::GetCurrentLoginType();
		 * </code>
		 *
		 * <AUTHOR>
		 * @access public
		 * @static
		 * 
		 * @return string The handle of the requested user type
		 * 
		 * @throws none none
		 *
		 */
		
		function SetTargetUser($strLoginTypeHandle,$intLoginID)
		{
			CCurrentUser::prvTargetUser(array('strLoginTypeHandle' => $strLoginTypeHandle, 
			                                  'intLoginID'         => $intLoginID));               
			return true;
			
		} // func: SetTargetUser
		
		function ClearTargetUser()
		{
			return CCurrentUser::prvTargetUser(array(),true);
		} // func: SetTargetUser
		
		function GetTargetUser()
		{
			return CCurrentUser::prvTargetUser();

		} // func: GetTargetUser
		
		function prvTargetUser($arrUserDetailsToStore = array(),$bolDestructiveRead =false)
		{
				static $arrTargetUsers =array();
				
				if(count($arrUserDetailsToStore)>0)
				{
					$arrTargetUsers[] =  $arrUserDetailsToStore;
					return true;
				}
				
				if(count($arrTargetUsers) < 1)
				{
					return array();
				}
				
				$arrCurrentTargetUser = array_pop($arrUserDetailsToStore);
				
				if(!$bolDestructiveRead)
				{
					$arrTargetUsers[] = $arrCurrentTargetUser;
				}
				
				return $arrCurrentTargetUser;
				
		} // func : prvTargetUser
		
		function GetCurrentLoginType($bolUseTargetUserIfSetID = true)
		{
			if( $bolUseTargetUserIfSetID && (count($arrTargetUser = CCurrentUser::GetTargetUser()) > 0))
			{
				return $arrTargetUser['strLoginTypeHandle'];
			}		
			
			if(class_exists('CPage') && CPage::bolInPage())
			{
				return CPage::GetCurrentLoginType();
			}
			
			// In a script
			return 'SCRIPT';
			
		} // func:GetCurrentLoginType

		/**
		 * Get the Login ID of the user currently accessing our functionality
		 * 
		 * Must be used in conjunction with Login Type to be unique
		 * 
		 * <code>
		 * $strLoginID = CCurrentUser::GetCurrentUserID();
		 * </code>
		 *
		 * <AUTHOR>
		 * @access public
		 * @static
		 * 
		 * @param  boolean If tthe user being worked on has been set vaule instead 
		 * @return string  The unqiue ID of the requested user
		 * 
		 * @throws none none
		 *
		 */
		
		function GetCurrentUserID($bolUseTargetUserIfSetID = true)
		{
			if( $bolUseTargetUserIfSetID && (count($arrTargetUser = CCurrentUser::GetTargetUser()) > 0))
			{
				return $arrTargetUser['intLoginID'];
			}		
			
			if(class_exists('CPage') && CPage::bolInPage())
			{
				return CPage::GetCurrentLoginID();
			}
			
			// TODO : no script class currently exists so this call in GetCurrentUserID must fail
			
			if(class_exists('CScript') && CScript::bolScriptRunning())
			{	
				return Script::GetCurrentLoginID();
			}
			
			return 0;
			
		} // func: GetCurrentUserID


		/**
		 * Get the locales available in our current context
		 * 
		 * Get the locales available in our current context
		 * 
		 * <AUTHOR>
		 * @access private
		 * @static
		 * 
		 * @return array The list of locales our current application
		 * context supports
		 * 
		 * @throws none none
		 *
		 */
		
		
		function prvGetLocalesAvailable()
		{
			
			if(class_exists('CPage') && CPage::bolInPage())
			{
				$objCurrentCPage = &CPage::GetCurrentlyExecutingPage();
				return $objCurrentCPage->GetLocalesAvailable();
			}
			
			// TODO : no script class currently exists so this call in GetCurrentUserID must fail
			
			if(class_exists('CScript') && CScript::bolScriptRunning())
			{
				$objCurrentScript = &CScript::GetCurrentlyExecutingScript();
				return $objCurrentScript->GetLocalesAvailable();
			}

			return array('en_UK.UTF-8','en_UK','de_DE.UTF-8','de_DE');

		} // func: GetLocalesAvailable
		
		/**
		 * Get the Users current preferences
		 * 
		 * Get the Users current preferances
		 * 
		 * <AUTHOR>
		 * @access private
		 * @static
		 * 
		 * @return array The users current preferences
		 * 
		 * @throws none none
		 *
		 */
				
		function prvRetrieveUserPreferences($strUserType,$strUserID)
		{
			// TODO: Currently not backed to a DB, no preferances other than locale are supported
			//       and that only works through auto-negotiation
			
			return array();
			
		} // func: prvRetrieveUserPreferences
		
// Setters

} // class: CCurrentUser
?>
