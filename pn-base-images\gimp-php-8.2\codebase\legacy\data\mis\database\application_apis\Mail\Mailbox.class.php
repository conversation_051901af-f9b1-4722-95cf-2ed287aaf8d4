<?php
require_once('/local/data/mis/database/application_apis/Mail/Exception.class.php');
require_once('/local/data/mis/database/application_apis/Mail/Alias.class.php');
require_once('/local/data/mis/database/application_apis/Mail/MailApi.class.php');
require_once('/local/data/mis/database/application_apis/Mail/Db.class.php');
require_once('/local/data/mis/database/database_libraries/mail-class.inc');
require_once('/local/data/mis/database/database_libraries/mailer_functions.inc');
require_once '/local/data/mis/database/crypt_config.inc';

/**
 * Mailbox class created for NextGen Email Project
 * Responsible for handling Mailbox creation and changes
 *
 * Use a constructor to create a new mailbox if you want to add it
 * You can pass an array with properties to the constructor or set them
 * after the object is created using magic __set() method
 *
 * Creation of mailbox is a 2 stage process
 * first entries in userdata are created with status queued-activate
 * then enable() is called which inserts data into auth and maildelivery
 * and changes status to active
 */
class Mail_Mailbox
{
    /**
     * @var array of Mail_Alias objects belonging to this mailbox
     */
    protected $_arrAliases = array();

    /**
     * @var array of values loaded from userdata.config_email or set by __set()
     */
    protected $_arrMailboxData = array();

    /**
     * @var array relevant fields from config_email for type mailbox
     */
    protected $_arrFields = array(
        'email_id',
        'component_id',
        'box_number',
        'type',
        'isp',
        'username',
        'login',
        'password',
        'delivery_method',
        'status',
        'smtp_delivery_ip'
    );

    /**
     * @var Mail_Db instace
     */
    protected $_objDb;

    /**
     * @var Mail_MailApi object responsible for talking to maildb database
     */
    public $_objMailApi = null;

    /**
     * @var array translation table for isp=>domain
     */
    protected static $_arrIsp = array(
        'plus.net' => array('plus.com'),
        'freeonline' => array(
            'free-online.co.uk',
            'idps.co.uk'
        ),
        'force9' => array(
            'force9.co.uk',
            'f9.co.uk',
            'force9.net'
        ),
        'searchpro' => array('searchpro.co.uk'),
        'dabsol' => array('dabsol.co.uk'),
        'provider' => array('here.co.uk'),
        'partner' => array('pndsl.co.uk'),
        'johnlewis' => array(
            'john-lewis.com',
            'johnlewisbroadband.com'
        ),
        'vodafone' => array('vodafoneemail.co.uk')
    );

    /**
     * Constructor creates new Mailbox, use it you want to add a mailbox
     * Properties can be populated by passing an array
     *
     * @param array $arrData optional row from config_email
     * @throws Mail_Exception
     */
    public function __construct($row = null)
    {
        $this->_objDb = Mail_Db::getInstance();

        if (isset($row['password']) && isset($row['email_id'])) {
            $row['password'] = Crypt_Crypt::decrypt($row['password'], 'config_email');
        }

        if (!empty($row)) {
            //pick only relevant data from the array
            foreach ($this->_arrFields as $field) {
                $this->$field = $row[$field];
            }
        }

        //instantiate maildb gateway
        $this->_objMailApi = new Mail_MailApi($this);

        //load aliases for existing mailbox
        if (isset($this->_arrMailboxData['email_id'])) {
            $this->_arrAliases = Mail_Alias::findByEmailId($this->email_id);
        }
    }

    /**
     * getIsIronPortUser returns whether user has ironport config
     * TO BE REMOVED AFTER IRONPORT MIGRATION HAS COMPLETED
     * @return boolean
     */
    public function getIsIronPortUser()
    {
        return $this->_objMailApi->getIsIronPortUser();
    }

    /**
     * Translates isp to email domain
     *
     * @param string $isp
     * @throws Mail_Exception
     */
    public static function getIspExtension($isp, $bolFirstOnly = true)
    {
        if (isset(self::$_arrIsp[$isp])) {
            $arrIsp = self::$_arrIsp[$isp];
        } else {
            throw new Mail_Exception("Isp '$isp' not recognised");
        }

        if ($bolFirstOnly) {
            return $arrIsp[0];
        } else {
            return $arrIsp;
        }
    }

    /**
     * Adds entry for new mailbox in config_email
     *
     * @throws Mail_Exception
     */
    public function create()
    {
        try {
            // Get the last box_number and increment it
            $strQuery = "SELECT max(box_number) as last_box_number FROM config_email "
                . "WHERE component_id = $this->component_id";
            $intNextBoxNumber = $this->_objDb->getSingleValue($strQuery, 'last_box_number', 'userdata') + 1;

            $strEncryptedPassword = addslashes(Crypt_Crypt::encrypt($this->password, 'config_email'));

            $this->status = 'queued-activate';
            $strQuery = "INSERT INTO config_email SET "
                . "component_id = $this->component_id, "
                . "type = '$this->type', "
                . "isp = '$this->isp', "
                . "username = '$this->username', "
                . "login = '$this->login', "
                . "password = '$strEncryptedPassword', "
                . "status = '$this->status', "
                . "redirect_to = '$this->redirect_to', "
                . "mlist_options = '$this->mlist_options', "
                . "smtp_delivery_ip = '$this->smtp_delivery_ip', "
                . "box_number = '$intNextBoxNumber' ";
            $this->email_id = $this->_objDb->insert($strQuery, 'userdata');
        } catch (Mail_Exception $e) {
            throw new Mail_Exception("Creating mailbox {$this->__toString()} failed");
        }
    }

    /**
     * Changes status of the mailbox to active and adds entries into maildb
     *
     * @throws Mail_Exception
     */
    public function enable()
    {
        if ('queued-activate' !== $this->status) {
            throw new Mail_Exception("Trying to enable mailbox with status $this->status");
        }

        try {
            $this->_objDb->begin('userdata', 'mailauth', 'maildelivery');

            //create mailbox in mail db and update status
            $this->_objMailApi->createMailbox();
            $this->status = 'active';
            $this->save();

            $this->_objDb->commit('userdata', 'mailauth', 'maildelivery');

            if ($this->type == 'default') {
                $this->emailMailboxChanged('defaultmailbox_added');
            } else {
                $this->emailMailboxChanged('submailbox_added');
            }
        } catch (Mail_Exception $e) {
            $this->_objDb->rollback('userdata', 'mailauth', 'maildelivery');
            throw new Mail_Exception("Enabling mailbox {$this->__toString()} failed");
        } catch (Exception $e) {
            // P66781 - It seems that sometimes new Mailboxes are rolled back
            // and no exception is raised in the error log.
            // Use a catch all and raise an autoproblem in case it gets here.
            if (!$intMailboxId) {
                pt_raise_autoproblem(
                    'Mailbox creation failed',
                    'Mailbox creation failed due to unknown exception',
                    'When creating a Mailbox in dbMailDelivery, an unrecognised exception was thrown.',
                    print_r($e, true)
                );
            }
        }
    }

    /**
     * Updates existing mailbox
     *
     * @throws Mail_Exception
     */
    public function save()
    {
        if (isset($this->_arrMailboxData['email_id'])) {

            $strEncryptedPassword = addslashes(Crypt_Crypt::encrypt($this->password, 'config_email'));

            $strQuery = "UPDATE config_email SET "
                . "component_id = $this->component_id, "
                . "type = '$this->type', "
                . "isp = '$this->isp', "
                . "username = '$this->username', "
                . "login = '$this->login', "
                . "password = '$strEncryptedPassword', "
                . "status = '$this->status', "
                . "redirect_to = '$this->redirect_to', "
                . "mlist_options = '$this->mlist_options', "
                . "smtp_delivery_ip = '$this->smtp_delivery_ip', "
                . "delivery_method = '$this->delivery_method' "
                . "WHERE email_id = $this->email_id";
            $this->_objDb->query($strQuery, 'userdata');
            return $this->email_id;
        } else {
            throw new Mail_Exception('Use save() only to update existing mailbox');
        }
    }

    /**
     * Destroys the mailbox and aliases
     *
     * @throws Mail_Exception
     */
    public function destroy($bolDestroyAll = false)
    {
        $this->status = 'queued-destroy';
        $this->save();

        //try to remove aliases first
        if (!empty($this->_arrAliases)) {
            foreach ($this->_arrAliases as $name => $objAlias) {
                $this->removeAlias($name);
            }
        }

        try {

            $this->_objDb->begin('userdata', 'mailauth', 'maildelivery');

            // Problem 42695 don't delete mail db entries until we're sure there
            // are no more config_email entries for the login.
            // copes with bad data (multiple config_email records for one login)
            // from before this incarnation of the email handling code.
            $strQuery = "SELECT email_id FROM config_email \n"
                . "  WHERE isp = '{$this->isp}' \n"
                . "    AND username = '{$this->username}' \n"
                . "    AND login = '{$this->login}' \n"
                . "    AND type = 'mailbox' \n"
                . "    AND status = 'active'";

            $res = $this->_objDb->query($strQuery, 'userdata');

            // send the "deleted your mailbox" message before deleting the
            // mailbox. otherwise we won't be able to find who we're sending the
            // message to.

            if (PrimitivesNumRowsGet($res) > 0) {
                $this->emailMailboxChanged('submailbox_dupe_deleted');
            } else {
                // last mailbox with this login. ok to destroy mail/auth db
                $this->_objMailApi->destroyMailbox();
                if ($bolDestroyAll) {
                    $this->_objMailApi->destroyVirtualDomain();
                    $this->_objMailApi->destroyMailUser();
                }
                $this->emailMailboxChanged('submailbox_deleted');
            }

            $this->_objDb->commit('userdata', 'mailauth', 'maildelivery');

            $this->status = 'destroyed';
            $this->save();

        } catch (Mail_Exception $e) {
            $this->_objDb->rollback('userdata', 'mailauth', 'maildelivery');
            throw new Mail_Exception("Destroying mailbox {$this->__toString()} failed");
        }
    }

    /**
     * Refreshes the mailbox
     * It will trigger the whole mailbox creation process again
     * If the mailbox is already created, no data will be lost
     * It will just go through all the steps again resulting in a "refreshed" mailbox
     * Password will also be refreshed
     *
     * @throws Mail_Exception
     */
    public function refresh()
    {
        $this->status = 'queued-reactivate';
        $this->save();
        try {
            $this->_objDb->begin('userdata', 'mailauth', 'maildelivery');

            if ($this->password && $this->password!='') {
                $this->_objMailApi->changePassword($this->password);
            }
            if($this->isp != 'vodafone') {
                $this->_objMailApi->refreshMailbox($this->type);
                $this->status = 'active';
                $this->save();
                // Try to refresh aliases

                $arrAliases = $this->getAliases();
                if (!empty($arrAliases)) {
                    foreach ($this->getAliases() as $objAlias) {
                        $objAlias->refresh($this->_objMailApi);
                    }
                }
            } else {
                $this->status = 'active';
                $this->save();
            }

            $this->_objDb->commit('userdata', 'mailauth', 'maildelivery');

        } catch (Mail_Exception $e) {
            $this->_objDb->rollback('userdata', 'mailauth', 'maildelivery');
            throw new Mail_Exception("Refreshing mailbox {$this->__toString()} failed");
        }
    }

    /**
     * Adds new alias to the mailbox
     *
     * @param string name
     * @throws Mail_Exception
     */
    public function addAlias($strName)
    {
        if (!isset($this->_arrAliases[$strName]) && 'johnlewis' != $this->isp) {

            $objAlias = new Mail_Alias();
            $objAlias->vchName = $strName;
            $objAlias->create($this->email_id);
            $objAlias->enable($this->_objMailApi);
            $this->_arrAliases[$strName] = $objAlias;
            $this->emailMailboxChanged('alias_added', $strName);

        } else {
            return true;
        }
    }

    /**
     * Associates the alias specified with strName with this mailbox
     * @param string name
     *
     */
    public function moveAlias($strName)
    {
        $this->_objMailApi->moveAlias($strName);
    }
    /**
     * Removes alias from the mailbox
     *
     * @param string name
     * @throws Mail_Exception
     */
    public function removeAlias($strName)
    {
        if ($this->_arrAliases[$strName] instanceof Mail_Alias) {

            $this->_arrAliases[$strName]->destroy($this->_objMailApi);
            unset($this->_arrAliases[$strName]);
            $this->emailMailboxChanged('alias_deleted', $strName);
        } else {
            throw new Mail_Exception("Alias $strName doesn't exists on {$this->__toString()} mailbox");
        }
    }

    /**
     * Returns allias object by name
     *
     * @param string $strName
     * @return Mail_Alias
     * @throws Mail_Exception
     */
    public function getAlias($strName)
    {
        if (isset($this->_arrAliases[$strName])) {
            return $this->_arrAliases[$strName];
        }
    }

    /**
     * Returns all aliases for this mailbox
     *
     * @return array of Mail_Alias
     */
    public function getAliases()
    {
        return $this->_arrAliases;
    }

    /**
     * Enables catch all option for this mailbox
     *
     * @throws Mail_Exception
     */
    public function enableCatchAll()
    {
        $this->_objMailApi->enableCatchAll();
    }

    /**
     * Disables catch all option for this mailbox
     *
     * @throws Mail_Exception
     */
    public function disableCatchAll()
    {
        $this->_objMailApi->disableCatchAll();
    }

    /**
     * Checks whether this is a catch all mailbox
     *
     * @return boolean
     */
    public function isCatchAll()
    {
        return $this->_objMailApi->isCatchAll();
    }

    /**
     * Updates mailbox with new login and saves it
     *
     * @param string $strNewLogin
     * @throws Mail_Exception
     */
    public function changeLogin($strNewLogin)
    {
        $this->_objMailApi->changeLogin($strNewLogin);
        $this->login = $strNewLogin;
        $this->save();
    }

    /**
     * Updates mailbox with new password and saves it
     *
     * @param string $strNewPassword
     * @throws Mail_Exception
     */
    public function changePassword($strNewPassword)
    {
        $this->password = $strNewPassword;
        $this->status = 'queued-reactivate';
        $this->save();
        $this->_objMailApi->changePassword($strNewPassword);
        $this->status = 'active';
        $this->save();
    }

    public function changeDeliveryMethod($strDomainName = NULL)
    {
        $this->_objMailApi->changeDeliveryMethod($this->delivery_method, $this->smtp_delivery_ip, $strDomainName);
    }
    /**
     * Returns simplified message list by connecting to mailbox via imap
     *
     * @return array
     */
    public function getContents()
    {
        if ($this->isp == "plus.net") {
            $strHost = "mail.plus.net";
        } else {
            $strQuery = "SELECT mail_host FROM mail.mail_servers WHERE isp = '$this->isp'";
            $strHost =  $this->_objDb->getSingleValue($strQuery, 'mail_host', 'userdata');
        }
        if ($this->type == 'default') {
            $strLogin = $this->username;
        } else {
            $strLogin = "$this->username+$this->login";
        }
        $resMailbox = imap_open('{' . $strHost . '/pop3:110}INBOX', $strLogin, $this->password);
        if (!$resMailbox) {
            throw new Mail_Exception("Failed to retrieve mailbox {$this->__toString()} contents");
        }
        $arrReturn = array();
        $intMessageCount = imap_num_msg($resMailbox);
        for ($i = 1; $i <= $intMessageCount; $i++) {
            $objMessage= imap_headerinfo($resMailbox, $i);
            $arrReturn[$i]['size'] = isset($objMessage->Size) ? $objMessage->Size : 'n/a';
            $arrReturn[$i]['date'] = isset($objMessage->Date) ?
                date('d M Y H:i:s', strtotime($objMessage->MailDate)) : 'n/a';
        }
        imap_close($resMailbox);
        return $arrReturn;
    }

    public function emailMailboxChanged($strType, $strName = null)
    {
        if ($strType=='') {
            return false;
        }
        if ($this->email_id) {

            // Get customer details
            $strQuery = 'SELECT s.service_id, s.username, ' .
                '       i.portal_main_page_url, i.url_domain, i.full_name as visp, ' .
                '       u.forenames, u.surname, ' .
                '       ce.redirect_to, ce.login as alias_to ' .
                '  FROM config_email ce ' .
                ' LEFT JOIN components c ON ce.component_id = c.component_id ' .
                ' LEFT JOIN services s ON c.service_id = s.service_id ' .
                ' INNER JOIN products.visp_config i ON i.isp = s.isp ' .
                ' INNER JOIN userdata.users u ON s.user_id = u.user_id ' .
                " WHERE ce.email_id = '{$this->email_id}'";

            $arrAccount = $this->_objDb->getArray($strQuery, 'userdata');
            $arrAccount = $arrAccount[0];

            //get pop/smtp details
            $arrService = userdata_service_get($arrAccount['service_id']);
            $arrVispConfig = product_visp_config_get($arrService['isp']);
            $arrAccount['pop3server'] = $arrVispConfig['customer_pop_server'];
            $arrAccount['smtpserver'] = $arrVispConfig['customer_smtp_server'];
            $arrAccount['domain'] = self::$_arrIsp[$arrService['isp']]['0'];

            $bolCCRecipient = false;

            switch($strType)
            {
                case 'defaultmailbox_added':
                    $strTemplate = 'defaultmailbox_added.txt';
                    $arrAccount['mailbox_name'] = $this->login;
                    $bolCCRecipient = true;
                    break;
                case 'submailbox_added':
                    $strTemplate = 'submailbox_added.txt';
                    $arrAccount['mailbox_name'] = $this->login;
                    $bolCCRecipient = true;
                    break;
                case 'submailbox_deleted':
                    $strTemplate = 'submailbox_deleted.txt';
                    $arrAccount['mailbox_name'] = $this->login;
                    break;
                case 'submailbox_dupe_deleted':
                    $strTemplate = 'submailbox_dupe_deleted.txt';
                    $arrAccount['mailbox_name'] = $this->login;
                    break;
                case 'alias_added':
                    $strTemplate = 'alias_added.txt';
                    $arrAccount['alias_name'] = $strName;
                    break;
                case 'alias_deleted':
                    $strTemplate = 'alias_deleted.txt';
                    $arrAccount['alias_name'] = $strName;
                    break;
                case 'redirect_added':
                    $strTemplate = 'redirect_added.txt';
                    $arrAccount['redirect_name'] = $strName;
                    break;
                case 'redirect_deleted':
                    $strTemplate = 'redirect_deleted.txt';
                    $arrAccount['redirect_name'] = $strName;
                    break;
                default:
                    break;
            }

            $strCCRecipient =
                ($bolCCRecipient == true)
                ? "{$arrAccount['mailbox_name']}@{$arrAccount['username']}.{$arrAccount['domain']}"
                : '';

            // we dont want these emails sent for plusnet residential signups
            $bolIsPlusnetCustomer = ('plus.net' == $arrService['isp']);
            $bolIsResidentialCustomer = ('residential' == $arrService['strReportSector']);
            $bolSendEmail = !($bolIsPlusnetCustomer &&
                $bolIsResidentialCustomer && 'config_email_auto_configure' == Mail_Manager::getContext());

            //beginning of P61158 fix
            if (in_array($arrService['isp'], array('partner', 'johnlewis'))) {
                $bolSendEmail=false;
            }
            //end of P61158
            if ($bolSendEmail) {
                mailer_send_customer_mail(
                    $arrAccount['service_id'],
                    dirname(__FILE__) . "/templates/{$strTemplate}",
                    $arrAccount,
                    true,
                    true,
                    NULL,
                    NULL,
                    '',
                    $strCCRecipient
                );
            }

        }
    }

    /**
     * getSpamFilter - Returns spam filter type for domain
     *
     * @access public
     * @param integer $intVirtualDomainId
     * @return string
     */
    public function getSpamFilter($intVirtualDomainId)
    {
        return $this->_objMailApi->getSpamFilter($intVirtualDomainId);
    }

    /**
     * setSpamFilter
     *
     * @param integer $intSettingId
     * @param integer $intAggressiveness
     * @param boolean $bolQNotification
     * @param boolean $bolAntiVirus
     * @param boolean $bolSpam
     * @param boolean $bolBsb
     * @param mixed $unkDomain
     * @param boolean $bolSpamTag
     * @param array $arrMailbox
     * @param string $strNotificationEmail
     * @param boolean $bolBlackhole
     * @access public
     * @return void
     */
    public function setSpamFilter(
        $intSettingId,
        $intAggressiveness,
        $bolQNotification,
        $bolAntiVirus,
        $bolSpam,
        $bolBsb,
        $unkDomain,
        $bolSpamTag,
        $arrMailbox,
        $strNotificationEmail,
        $bolBlackhole)
    {
        $this->_objMailApi->setSpamFilter(
            $intSettingId,
            $intAggressiveness,
            $bolQNotification,
            $bolAntiVirus,
            $bolSpam,
            $bolBsb,
            $unkDomain,
            $bolSpamTag,
            $arrMailbox,
            $strNotificationEmail,
            $bolBlackhole
        );
    }

    /**
     * clearEmailList
     *
     * @param mixed $unkVirtualDomain
     * @access public
     * @return void
     */
    public function clearEmailList($unkVirtualDomain)
    {
        $this->_objMailApi->clearEmailList($unkVirtualDomain);
    }

    /**
     * setApprovedEmailList
     *
     * @param string $strApprovedSenders
     * @access public
     * @return void
     */
    public function setApprovedEmailList($strApprovedSenders = NULL, $unkDomain)
    {
        $this->_objMailApi->setApprovedEmailList($strApprovedSenders, $unkDomain);
    }

    /**
     * setApprovedEmailList
     *
     * @param string $strApprovedSenders
     * @param mixed $unkDomain
     * @access public
     * @return void
     */
    public function setBlockedEmailList($strBlockedSenders = NULL, $unkDomain)
    {
        $this->_objMailApi->setBlockedEmailList($strBlockedSenders, $unkDomain);
    }

    /**
     * getEmailList
     *
     * @param integer $intVirtualDomainId
     * @access public
     * @return void
     */
    public function getEmailList($intVirtualDomainId)
    {
        return $this->_objMailApi->getEmailList($intVirtualDomainId);
    }

    /**
     * toggleBlackhole changes blackhole settings for virtual domain
     *
     * @param integer $intVirtualDomainId
     * @param integer $intBlackhole
     * @access public
     * @return void
     */
    public function toggleBlackhole($intVirtualDomainId, $intBlackhole)
    {
        $this->_objMailApi->toggleBlackhole($intVirtualDomainId, $intBlackhole);
    }

    /**
     * isValidAlias check whether Alias is valid
     *
     * @access public
     * @param string $strAlias
     * @return string
     */
    public function isValidAlias($strAlias)
    {
        return $this->_objMailApi->isValidAlias($strAlias);
    }

    /**
     * Gets the virtual domains details for a user
     *
     * @access public
     * @return array
     */
    public function getVirtualDomainsDetails()
    {
        return $this->_objMailApi->getVirtualDomainsDetails();
    }

    /**
     * getVirtualDomainByName return domain details based on its name
     *
     * @param string $strName
     * @access public
     * @return array
     */
    public function getVirtualDomainByName($strName)
    {
        return $this->_objMailApi->getVirtualDomainByName($strName);
    }

    /**
     * getVirtualDomainById
     *
     * @param integer $intVirtualDomainId
     * @access public
     * @return void
     */
    public function getVirtualDomainById($intVirtualDomainId)
    {
        return $this->_objMailApi->getVirtualDomainById($intVirtualDomainId);
    }

    /**
     * isLoginAvailableOnDomain
     *
     * @param string $strLocalPart
     * @access public
     * @return boolean
     */
    public function isLoginAvailableOnDomain($strLocalPart)
    {
        return $this->_objMailApi->isLocalPartAvailableOnDomain($strLocalPart);
    }


    /**
     * Allows only predefined values to be assigned to _arrMailboxData
     *
     * @throws Mail_Exception
     */
    public function __set($name, $value)
    {
        if (in_array($name, $this->_arrFields)) {
            $this->_arrMailboxData[$name] = $value;
        } else {
            throw new Mail_Exception("Trying to set invalid property: $name");
        }
    }

    /**
     * Returns values from _arrMailboxData
     * Returns null if the value doesn't exists, but throws exception if you try to
     * access predefined property without assigning it first
     *
     * @throws Mail_Exception
     */
    public function __get($name)
    {
        //don't allow to retrieve empty variable if it's mandatory
        if (in_array($name, $this->_arrFields) and !isset($this->_arrMailboxData[$name])) {
            throw new Mail_Exception("Property: $name has not been set and it's mandatory");
        }
        if (isset($this->_arrMailboxData[$name])) {
            return $this->_arrMailboxData[$name];
        }
    }

    /**
     * Returns string representation of mailbox object
     *
     * @return string
     */
    public function __toString()
    {
        if ($this->isp == 'johnlewis') {
            return $this->username . '@' . self::getIspExtension($this->isp);
        } else if($this->isp == 'vodafone') {
            return $this->login . '@' . $this->login . '.' .
                self::getIspExtension($this->isp);
        } else {
            return $this->login . '@' . $this->username . '.' .
                self::getIspExtension($this->isp);
        }
    }
}
