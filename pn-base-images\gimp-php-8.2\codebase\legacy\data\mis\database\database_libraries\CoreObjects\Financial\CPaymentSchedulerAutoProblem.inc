<?php
/**
 * Class for handling auto-problems raised from the payment scheduler.
 * Works on the basis of raising against a service ID and details of the SP
 *
 * N.B.: This can be generalised
 *
 * @package
 * @version $id$
 * @copyright PlusNet
 * <AUTHOR> <<EMAIL>>
 * @license PHP License Version 3.01 {@link http://www.php.net/license/3_01.txt}
 */
class CPaymentSchedulerAutoProblem
{
    const FLUSH_THRESHOLD = 100;

    /**
     * Singleton instance based on auto-problem handle
     *
     * @var CPaymentSchedulerAutoProblem
     */
    private static $instance = array();

    /**
     * A counter to see if we've passed the threshold above
     *
     * @var integer
     */
    protected static $counter = 0;

    /**
     * Handle of the auto-problem to raise off this
     *
     * @var string
     */
    protected $autoProblemHandle;

    /**
     * Array of services that have failed scheduled payments
     *
     * @var array
     */
    protected $service = array();




    /**
     * Allow dependency injection for mocking
     *
     * @return null
     */
    public static function setInstance(CPaymentSchedulerAutoProblem $instance, $autoProblemHandle)
    {
        if (!is_string($autoProblemHandle)) {
            throw new Exception(__METHOD__ . ': autoProblemHandle must be a string; type \'' .
               get_type($autoProblemHandle) . '\' given');
        }
        self::$instance[$autoProblemHandle] = $instance;
    }

    /**
     * getInstance
     *
     * @return CPaymentSchedulerAutoProblem
     */
    public static function getInstance($autoProblemHandle)
    {
        if (!is_string($autoProblemHandle)) {
            throw new Exception(__METHOD__ . ': autoProblemHandle must be a string; type \'' .
               get_type($autoProblemHandle) . '\' given');
        }
        if (empty(self::$instance[$autoProblemHandle])) {
            self::$instance[$autoProblemHandle] = new self($autoProblemHandle);
        }

        return self::$instance[$autoProblemHandle];
    }

    /**
     * Handy for unit testing. Not expected to be called in a live situation
     *
     * @return void
     */
    public static function resetInstance($autoProblemHandle)
    {
        if (!is_string($autoProblemHandle)) {
            throw new Exception(__METHOD__ . ': autoProblemHandle must be a string; type \'' .
               get_type($autoProblemHandle) . '\' given');
        }
        unset(self::$instance[$autoProblemHandle]);
    }

    /**
     * Instantiate the object with the right handle to use
     *
     * @param mixed $autoProblemHandle
     * @return void
     */
    public function __construct($autoProblemHandle)
    {
        if (!is_string($autoProblemHandle)) {
            throw new Exception(__METHOD__ . ': autoProblemHandle must be a string; type \'' .
               get_type($autoProblemHandle) . '\' given');
        }
        $this->autoProblemHandle = $autoProblemHandle;
    }

    /**
     * Add a scheduled payment failure
     *
     * @param integer $serviceId
     * @param array $data
     * @param string $trace
     *
     * @return void
     */
    public function add($serviceId, $data, $trace)
    {
        $this->service[$serviceId]['data'][] = $data;
        // This replaces any previous trace. It would take a lot of space, and probably be
        // fairly useless, to have the trace for every failed scheduled payment
        $this->service[$serviceId]['trace'] = htmlentities($trace);

        $this->counter++;

        // Past the flush threshold. Write out and reset the stack
        if ($this->counter % self::FLUSH_THRESHOLD == 0) {
            $this->writeToAp();
            unset($this->service);
            $this->service = array();
        }
    }

    /**
     * Write the service details to an auto-problem
     *
     * @return void
     */
    protected function writeToAp()
    {
        $autoProblemClient = BusTier_BusTier::getClient('autoproblem');

        $scriptUser = SoapSession::getScriptUserDetails(Partner_Partner::getDataset());
        $actor = Auth_BusinessActor::get($scriptUser['actorId']);

        foreach ($this->service as $serviceId => $data) {

            $data['serviceId'] = $serviceId;
            $autoProblem = $autoProblemClient->prepareAutoProblem($this->autoProblemHandle, $data, $actor);
            $autoProblemId = $autoProblem->raiseProblem();
        }
    }

    /**
     * Finish off by writing whatever's left
     *
     * @return void
     */
    public function __destruct()
    {
        $this->writeToAp();
    }
}
