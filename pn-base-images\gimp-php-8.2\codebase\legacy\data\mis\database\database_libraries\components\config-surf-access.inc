<?php

	require_once('/local/data/mis/database/database_libraries/userdata-access.inc');

	/////////////////////////////////////////////////////////////////////
	// File:     config-surf-access.inc
	// Purpose:  Access mini-library for configuring P2P & Binary Newsfeed access
	//           Activating the component stops access, unconfiguring gives access
	/////////////////////////////////////////////////////////////////////

	/////////////////////////////////////////////////////////////////////
	// Functions
	//
	// Write functions
	// ---------------
	//
	// config_surf_restrict
	//
	// Read functions
	// --------------
	//
	// Modify Functions
	// ----------------
	//
	// Delete functions
	// ----------------
	//
	// config_surf_unrestrict
	//
	/////////////////////////////////////////////////////////////////////


	/////////////////////////////////////////////////////////////////////
	// Data

	$global_component_configurators['312'] = 'config_surf_configurator';

	// Data
	/////////////////////////////////////////////////////////////////////
	// Hack to insert the component configurator array into PHP5's global scope
	// if it's not already there
	if(!isset($GLOBALS['global_component_configurators']))
	{
		$GLOBALS['global_component_configurators'] = $global_component_configurators;
	}
	else
	{
		foreach($global_component_configurators as $intIndex => $strConfigurator)
		{
			$GLOBALS['global_component_configurators'][$intIndex] = $strConfigurator;
		}
	}

	/////////////////////////////////////////////////////////////////////
	// Library functions

	/////////////////////////////////////////////////////////////
	// Function:  config_surf_auto_destroy
	// Purpose:   * -> 'queued-destroy' state
	//            transition handler for auto-destruction
	// Arguments: $intComponentID
	/////////////////////////////////////////////////////////////

	function config_surf_auto_destroy ($intComponentID)
	{
		userdata_component_set_status ($intComponentID, 'destroyed');

	} // function config_surf_auto_destroy

	/////////////////////////////////////////////////////////////
	// Function:  config_surf_auto_destroy
	// Purpose:   * -> 'queued-destroy' state
	//            transition handler for auto-destruction
	// Arguments: $intComponentID
	/////////////////////////////////////////////////////////////

	function config_surf_configurator ($intComponentID, $strCompAction)
	{
		// Only allow auto destorying of component
		switch ($strCompAction)
		{
			case 'auto_destroy' :

				config_surf_auto_destroy ($intComponentID);

				break;

			case 'auto_configure' :
			case 'auto_refresh' :
			case 'auto_disable' :
			case 'auto_enable' :
			default :

				// Do nothing
				break;
		}

	} // function config_surf_configurator

	/////////////////////////////////////////////////////////////
	// Function:  CheckIsSurfActive
	// Purpose:   Check if Surf component is active and return true if so 
	// Arguments: $intComponentID
	/////////////////////////////////////////////////////////////

	function CheckIsSurfActive ($intServiceID)
	{
		$arrUserComponents = userdata_component_get_by_service ($intServiceID);

		foreach($arrUserComponents as $arrComponent)
		{
			if($arrComponent['component_type_id'] == COMPONENT_SURF)
			{
				$intSurfComponentID = $arrComponent['component_id'];

				$bolComponentResult = UserdataGetComponentStatus($intSurfComponentID);
					
				if ($bolComponentResult === true)
				{
					return true;
				}
			}
		}

		return false;
	}

	/////////////////////////////////////////////////////////////
	// Function:  GetRestrictedSurfGroupID 
	// Purpose:   Get the restricted Dial up Group ID
	// Arguments: $intServiceComponentID
	/////////////////////////////////////////////////////////////

	function GetRestrictedSurfGroupID($intServiceComponentID)
	{
		// Get status of component
                $dbhConnection = get_named_connection_with_db('product');

                $strSQL = "SELECT intSurfRestrictedGroupID 
                             FROM component_radius_config
                            WHERE service_component_id = $intServiceComponentID";

                $resResult = PrimitivesQueryOrExit($strSQL, $dbhConnection);

		$intSurfRestrictedGroupID = PrimitivesResultGet($resResult,'intSurfRestrictedGroupID');
		
		return $intSurfRestrictedGroupID;

	}


	// Library functions
	/////////////////////////////////////////////////////////////////////


?>
