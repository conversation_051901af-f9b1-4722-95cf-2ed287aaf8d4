<?php
/**
 * Premise base class
 *
 * @package    Core
 * @subpackage Premise
 * <AUTHOR> <<EMAIL>>
 * @link       http://www.plus.net/
 */
/**
 * Premise base class
 *
 * @package    Core
 * @subpackage Premise
 * <AUTHOR> <<EMAIL>>
 * @link       http://www.plus.net/
 */
class Core_Premise
{
    /**
     * @access private
     * @var Core_PremiseDao
     */
    private $_objDao = null;

    const STATUS_ACTIVE   = 'ACTIVE';
    const STATUS_INACTIVE = 'INACTIVE';

    const TYPE_DELIVERY        = 'DELIVERY';
    const TYPE_AUTHORISED_USER = 'AUTHORISED_USER';

    /**
     * Default constructor
     *
     * @param int    $premiseId          Premise Id
     * @param string $strTransactionName Transaction name
     *
     * @return void
     */
    public function __construct($premiseId = null, $strTransactionName = Db_Manager::DEFAULT_TRANSACTION)
    {
        if (isset($premiseId)) {
            $this->setDao(Core_PremiseDao::get($premiseId, $strTransactionName));
        }
    }

    /**
     * Takes care of all calls to setters and getters of DAO object
     *
     * @param string $method Method name
     * @param array  $params Params passed to method called
     *
     * @throws Exception
     * @return mixed
     */
    public function __call($method, $params)
    {
        if (preg_match('/^(get|set)/', $method)) {
            return call_user_func_array(array($this->_objDao, $method), $params);
        }

        throw new BadMethodCallException('Method does not exist: '.get_class($this).'::'.$method);
    }

    /**
     * Calls write method of DAO object
     *
     * @return bool
     */
    public function write()
    {
        return $this->_objDao->write();
    }

    /**
     * Sets DAO
     *
     * @param Core_PremiseDao $premiseDao Premise DAO
     *
     * @return void
     */
    public function setDao(Core_PremiseDao $premiseDao)
    {
        $this->_objDao = $premiseDao;
    }

    /**
     * Checks if address is a delivery address
     *
     * @return bool
     */
    public function isDeliveryAddress()
    {
        return ($this->getTypeHandle() == self::TYPE_DELIVERY);
    }
}
