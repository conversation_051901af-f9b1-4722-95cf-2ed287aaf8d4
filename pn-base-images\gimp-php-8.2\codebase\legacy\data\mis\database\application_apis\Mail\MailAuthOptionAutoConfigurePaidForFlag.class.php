<?php

require_once '/local/data/mis/database/application_apis/Mail/IMailAuthOptionConfig.interface.php';


class Mail_MailAuthOptionAutoConfigurePaidForFlag implements Mail_IMailAuthOptionConfig
{
	private $servicePaid;
	private static $disallowedStatuses = array('presignup', 'invalid', 'unconfigured', 'destroyed', 'deactive');
	private $isAllowedWebmailAndIMAP;

	public function __construct(Mail_Service $service, CComponent $componentDetails)
	{
		$this->servicePaid = self::isPaidForService($service, $componentDetails);
		$this->isAllowedWebmailAndIMAP = self::isAllowedWebmailAndIMAP($service, $componentDetails);
	}



	private static function isPaidForService(Mail_Service $service, CComponent $componentDetails)
	{
		if (in_array($componentDetails->getStatus(), self::$disallowedStatuses) ||
		    in_array($service->getStatus(), self::$disallowedStatuses)) {

			return new Bool(FALSE);
		}


		return $service->isPaidForService();
	}

	private static function isAllowedWebmailAndIMAP(Mail_Service $service, CComponent $componentDetails)
	{
		if (in_array($componentDetails->getStatus(), self::$disallowedStatuses) ||
		    in_array($service->getStatus(), self::$disallowedStatuses)) {

			return new Bool(FALSE);
		}

		return $service->isAllowedWebmailAndIMAP();
	}

	public function configure(Mail_Service $service, Mail_MailAuth $mailAuth, Mail_MailDelivery $mailDelivery, Int $authId)
	{
		$mailAuth->setPaidFlagState($service, $authId, $this->servicePaid);
		$mailAuth->setWebmailState($service, $authId, $this->isAllowedWebmailAndIMAP);
		$mailAuth->setImapState($service, $authId, $this->isAllowedWebmailAndIMAP);
	}
}

