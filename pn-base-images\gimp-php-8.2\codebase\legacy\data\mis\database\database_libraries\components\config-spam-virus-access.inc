<?php
	require_once('/local/data/mis/database/application_apis/antivirus-api.inc');
	require_once('/local/data/mis/database/application_apis/spam-protection-api.inc');
	require_once('/local/data/mis/database/database_libraries/groupware-access.inc');


	/////////////////////////////////////////////////////////////////////
	// File:     config-spam-virus-access.inc
	// Purpose:  Access mini-library for config_webspace
	/////////////////////////////////////////////////////////////////////

	/////////////////////////////////////////////////////////////////////
	// Functions
	//
	// Write functions
	// ---------------
	//
	//
	// Read functions
	// --------------
	//
	//
	// Delete functions
	// ----------------
	//
	//
	// Update functions
	// ----------------
	//
	/////////////////////////////////////////////////////////////////////


	/////////////////////////////////////////////////////////////////////
	// Data
		$global_component_configurators['235']  = 'ConfigSpamVirusConfigurator';
	// Data
	/////////////////////////////////////////////////////////////////////
	
	// Hack to insert the component configurator array into PHP5's global scope
	// if it's not already there
	if(!isset($GLOBALS['global_component_configurators']))
	{
		$GLOBALS['global_component_configurators'] = $global_component_configurators;
	}
	else
	{
		foreach($global_component_configurators as $intIndex => $strConfigurator)
		{
			$GLOBALS['global_component_configurators'][$intIndex] = $strConfigurator;
		}
	}


	/////////////////////////////////////////////////////////////////////
	// Library functions


	/////////////////////////////////////////////////////////////
	// Function:  ConfigSpamVirusAutoConfigure
	// Purpose:   Add spam virus groupware data for a component
	// Arguments: $component_id (The component ID)
	//            $array_bundles (Array of data in the following form:)
	//
	//            array(array('bundle_type_id' => $bundle_type_id,
	//                        'license_count'  => $number_of_licenses));
	//                  ...
	// Returns  : The ID of the group created in the process.
	/////////////////////////////////////////////////////////////

	function ConfigSpamVirusAutoConfigure($component_id, $array_bundles, $defer = false)
	{
		$arrComponent = userdata_component_get($component_id);

		switch ($arrComponent['status'])
		{
			case 'unconfigured':

				$intServiceID = $arrComponent['service_id'];
				$strNewStatus = 'inactive';

				$arrServiceDetails = userdata_service_get($intServiceID, $get_new=false);
			
				$intServiceDefID = $arrServiceDetails['type'];

                                $arrProductDetails = product_get_service($intServiceDefID);

                                $strProdName = $arrProductDetails['name'];

                                if (stristr($strProdName,'easystart'))
                                {
                                        $strAccountType = 'easystart';
                                }
                                elseif (stristr($strProdName, 'broadband home lite'))
                                {
                                        $strAccountType = 'broadbandhomelite';
                                }
                                else
                                {
                                        $strAccountType = 'other';
				}                                

				// Get username of account and use as group name
				$strName = $arrServiceDetails['username'];

				// Initialise the component
				userdata_component_set_status($component_id, 'queued-activate');

				// Add the group
				$group_id = groupware_group_add($component_id, $strName);

				// Add the default collection
				groupware_collection_add($group_id, 'All', true);

				// Add the owning member
				$owning_member_id = groupware_group_member_add($group_id, $intServiceID, false);

				// Set the owning member
				groupware_group_owner_set($group_id, $owning_member_id);

				// Add the bundles
				foreach ($array_bundles as $bundle)
				{
					$int_bundle_type_id = $bundle['bundle_type_id'];
					$license_count = $bundle['license_count'];

					groupware_group_bundle_add($group_id, $int_bundle_type_id, $license_count, $defer, $strAccountType);
				}

				$arrDetails = groupware_group_member_bundles_get($owning_member_id);

				$group_member_bundle_id = $arrDetails[0]['group_member_bundle_id'];

				// Update the component
				userdata_component_set_status($component_id, 'active');
				userdata_component_set_config_id($component_id, $group_id);

				// Set AntiVirus status to active
				update_antivirus_status($intServiceID, $owning_member_id, $strNewStatus, $strAccountType);

				// Set Spam level to medium and status to queued-reactivate
				spam_protection_validate_setup($group_member_bundle_id, $intSpamSecurity = 5, $intSpamStatus = 3);

				return $group_id;

			break;
			
			case 'deactive':
				userdata_component_set_status($component_id, 'active');
			break;
			
			default:
			break;
		}
	}

	/////////////////////////////////////////////////////////////
	// Function:  ConfigSpamVirusAutoEnable
	// Purpose:   transition handler for auto-enabling
	// Arguments: $component_id
	/////////////////////////////////////////////////////////////

	function ConfigSpamVirusAutoEnable($intComponentID)
	{
		$arrComponent = userdata_component_get($intComponentID);

		switch ($arrComponent['status'])
		{
			case 'deactive':
			userdata_component_set_status($intComponentID, 'active');
			break;

			default:
			break;
		}
	}

	/////////////////////////////////////////////////////////////
	// Function:  ConfigSpamVirusAutoDestroy
	// Purpose:   * -> 'queued-destroy' state
	//            transition handler for auto-destruction
	// Arguments: $component_id
	/////////////////////////////////////////////////////////////

	function ConfigSpamVirusAutoDestroy($component_id)
	{
		$arrComponent = userdata_component_get($component_id);

		switch ($arrComponent['status'])
		{
			// Free pass to "destroyed"
			case 'unconfigured':
				userdata_component_set_status($component_id, 'destroyed');
				break;

			// Have or may have configuration, pass to the reaper
			case 'active':
			case 'deactive':
			case 'queued-reactivate':
			case 'queued-deactivate':
			case 'queued-activate':
			case 'queued-deconfigure':
			case 'queued-destroy':
				$arrGroup = groupware_group_get_by_component_id($component_id);

				// Set status to signal that the group is being destroyed.
				userdata_component_set_status($component_id, 'queued-destroy');

				// Delete the group
				groupware_group_delete($arrGroup['group_id']);

				// Fininsh the job
				userdata_component_set_status($component_id, 'destroyed');

				break;

				// Already there
			case 'destroyed':
				break;

			default:
				// The sky is falling!
				break;
		}

	} // ConfigSpamVirusAutoDestroy

	/////////////////////////////////////////////////////////////
	// Function : ConfigSpamVirusConfigurator
	// Purpose  : Groupware component configurator
	// Arguments: $component_id
	//            $action
	/////////////////////////////////////////////////////////////

	function ConfigSpamVirusConfigurator($component_id, $action)
	{
		$array_bundles = array(array('bundle_type_id' => 9, 'license_count' => 1));
		
		switch ($action)
		{
			case 'auto_configure':
				ConfigSpamVirusAutoConfigure($component_id, $array_bundles, $defer = false);
				break;

			case 'auto_disable':
				break;

			case 'auto_enable':
				ConfigSpamVirusAutoEnable($component_id);
				break;

			case 'auto_refresh':
				// Nothing to do here
				break;

			case 'auto_destroy':
				ConfigSpamVirusAutoDestroy($component_id);
				break;

			default:
				break;
		}

	} // ConfigSpamVirusConfigurator

	// Library functions
	/////////////////////////////////////////////////////////////////////

?>
