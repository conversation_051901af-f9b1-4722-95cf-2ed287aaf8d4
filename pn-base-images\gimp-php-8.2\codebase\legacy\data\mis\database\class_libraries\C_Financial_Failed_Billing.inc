<?php
/**
 * Class - C_Financial_Failed_Billing
 *
 * Class for retrieving details for failed billing
 * This uses billing_report_by_customer, which is not
 * inherently a 'failed billing' table.
 * However, since its entries comprise transactions which failed billing,
 * it serves our purposes adequately.
 *
 * @package    Financial
 * <AUTHOR>
 * @version    $Id: C_Financial_Failed_Billing.inc,v 1.9 2008-12-19 18:45:32 ssmith Exp $
 * @filesource
 */
use Plusnet\FailedPayment\Entity\FailedPaymentInstance;
use Plusnet\Feature\FeatureToggleManager;
use Plusnet\FailedPayment\Exceptions\NoFailedPaymentDetailsException;

class C_Financial_Failed_Billing
{
    // Instantiate the class with `empty` defaults
    public $intBillingReportID = 0;
    public $intBillingReportSummaryID = 0;
    public $intServiceID = 0;
    public $strUsername = '';
    public $intInvoiceID = 0;
    public $floBillingAmount = 0.00;
    public $floRefundAmount = 0.00;
    public $floInvoiceAmount = 0.00;
    public $strEmployeeName = '';
    public $bolSuccessFlag = 0;
    public $strReasonContactUnsuccessful = '';
    public $strAccountType = '';
    public $strFailureReason = '';
    public $bolBusinessResidentialFlag = '';
    public $bolAccountDeactivated = 0;
    public $intTicketPhraseID = 0;
    public $dtmBillingDate = '0000-00-00 00:00:00';
    public $intBillingTypeID = 0;
    public $strBillingTypeName = '';
    public $strBillingHandle = '';
    public $bolMoveInvoiceDate = 0;

    const FAILED_BILLING_GRACE_PERIOD_DAYS = 14;

    /**
     * Constructor - C_Financial_Failed_Billing -
     * Retrieves the customer's most recent failed billing entry
     *
     * recBillingRecord    - will assign the billing record identified by
     * recBillingRecord into the object
     *
     * @access    public
     *
     * @param mixed $mxdArg       Argument for object instantiation, which can be any of the following:
     *
     * @param int   $intInvoiceID Will retrieve the most recent record identified by intInvoiceID
     *
     * @return void
     */
    public function C_Financial_Failed_Billing($mxdArg = null, $intInvoiceID = 0)
    {
        // Work out what the argument is and take appropriate action
        if (!isset($mxdArg)) {
            return;
        }

        // Is array - assign by array
        if (is_array($mxdArg)) {
            $this->assignByArray($mxdArg);

            return;
        }

        // Is a numerical value - get by Service ID
        if (is_numeric($mxdArg)) {
            $this->intServiceID = $mxdArg;
            // first try to get data based on invoice id
            if (!$this->getByServiceIDAndInvoiceID($intInvoiceID)) {
                // if not found try to get data based on SID
                if (!$this->getByServiceID()) {
                    // otherwise get the data by SID and date restrictions
                    $this->getByServiceIDAndDate();
                }
            }

            return;
        }

        return;
    }

    /**
     * getByServiceID - retrieve the most recent billing record
     * identified by intServiceID
     *
     * @return boolean        true on success, false on failure
     * @access public
     */
    public function getByServiceID()
    {
        $strBillingRecordQuery = $this->getSelectStructure();
        $strBillingRecordQuery .= "  WHERE br.br_service_id = ";
        $strBillingRecordQuery .= $this->intServiceID . "\n";
        $strBillingRecordQuery .= "  AND   br.br_invoice_id = 0\n";

        $dbhFinancial = get_named_connection_with_db('financial');

        $resResult = PrimitivesQueryOrExit(
            $strBillingRecordQuery,
            $dbhFinancial
        );

        $recBillingRecord = PrimitivesResultGet($resResult);

        // if not found return false
        if (empty($recBillingRecord)) {
            return false;
        }

        $this->assignByArray($recBillingRecord);
        unset($recBillingRecord);

        return true;
    }

    /**
     * getByServiceIDAndInvoiceID - retrieve the most recent billing record
     * identified by intServiceID and InvoiceID (if is set)
     *
     * <AUTHOR> Marek" <<EMAIL>>
     * @access public
     *
     * @param int $intInvoiceID The Invoice ID of the record to get
     *
     * @return boolean TRUE on success, FALSE on failure
     */
    public function getByServiceIDAndInvoiceID($intInvoiceID)
    {
        if (empty($intInvoiceID) || $intInvoiceID <= 0) {
            return false;
        }

        $strBillingRecordQuery = $this->getSelectStructure();
        $strBillingRecordQuery .= "  WHERE br.br_service_id = ";
        $strBillingRecordQuery .= $this->intServiceID . "\n";
        $strBillingRecordQuery .= "  AND   br.br_invoice_id = $intInvoiceID\n";

        $dbhFinancial = get_named_connection_with_db('financial');

        $resResult = PrimitivesQueryOrExit(
            $strBillingRecordQuery,
            $dbhFinancial
        );

        $recBillingRecord = PrimitivesResultGet($resResult);

        // if not found return false
        if (empty($recBillingRecord)) {
            return false;
        }

        $this->assignByArray($recBillingRecord);
        unset($recBillingRecord);

        return true;
    }

    /**
     * Retrieves the most recent billing record identified by intServiceID
     * based by date of failed billing event.
     *
     * It is a fix for problem 34483. Try to find a failed billing date for
     * an account which is in failed billing state AND hasn't been reactivated
     * after billing date
     *
     * <AUTHOR> Marek" <<EMAIL>>
     * @access public
     *
     * @return boolean
     */
    public function getByServiceIDAndDate()
    {
        // Take date of the last reactivation
        $arrServiceEventTypes = array(
            SERVICE_EVENT_FAILED_BILLING_RESTRICTIONS_REMOVED_BY_PAYMENT,
            SERVICE_EVENT_FAILED_BILLING_RESTRICTIONS_REMOVED_BY_CSC,
            SERVICE_EVENT_FAILED_BILLING_REACTIVATED_BY_PAYMENT,
            SERVICE_EVENT_FAILED_BILLING_REACTIVATED_BY_CSC
        );

        $strLastReactivationDate =
            $this->getLastServiceEventDate($arrServiceEventTypes, $this->intServiceID);

        if ($strLastReactivationDate) {
            $strQueryWhereStatement = ' AND brs.br_date_of_billing_run > "';
            $strQueryWhereStatement .= $strLastReactivationDate . '"';
        } else {
            $strQueryWhereStatement = ' ORDER BY brs.br_date_of_billing_run ';
            $strQueryWhereStatement .= 'DESC LIMIT 1';
        }
        $strBillingRecordQuery = $this->getSelectStructure();
        $strBillingRecordQuery .= ' INNER JOIN ';
        $strBillingRecordQuery .= 'financial.billing_report_summary brs ';
        $strBillingRecordQuery .= 'ON (brs.br_summary_id = br.br_summary_id) ';
        $strBillingRecordQuery .= 'WHERE br.br_service_id = ';
        $strBillingRecordQuery .= $this->intServiceID;
        $strBillingRecordQuery .= ' AND br.br_invoice_id = -1 ';
        $strBillingRecordQuery .= $strQueryWhereStatement;

        $dbhFinancial = get_named_connection_with_db('financial');

        $resResult = PrimitivesQueryOrExit(
            $strBillingRecordQuery,
            $dbhFinancial
        );

        $recBillingRecord = PrimitivesResultGet($resResult);

        $this->assignByArray($recBillingRecord);
        unset($recBillingRecord);

        return true;
    }

    /**
     * listByBillingReportSummaryID - Retrieve all billing records
     * identified by $intBillingReportSummaryID
     *
     * @param int $intBillingReportSummaryID The Summary ID to search on (or use from property if NULL)
     *
     * @access    public
     *
     * @return    array|false array of C_Financial_Failed_Billing objects|
     */
    public function listByBillingReportSummaryID($intBillingReportSummaryID = null)
    {
        // No argument supplied. Use object property instead
        if (!isset($intBillingReportSummaryID)) {
            $intBillingReportSummaryID = $this->intBillingReportSummaryID;
        }

        // Better do a validity check
        if (!is_numeric($intBillingReportSummaryID)) {
            return false;
        }

        $strBillingRecordQuery = $this->getSelectStructure();
        $strBillingRecordQuery .= "  WHERE br.br_service_id = ";
        $strBillingRecordQuery .= $this->intServiceID . "\n";
        $strBillingRecordQuery .= "    AND br.br_summary_id = ";
        $strBillingRecordQuery .= "$intBillingReportSummaryID\n";
        $strBillingRecordQuery .= "  ORDER BY br.br_id DESC\n";

        $dbhFinancial = get_named_connection_with_db('financial');
        $resResult = PrimitivesQueryOrExit(
            $strBillingRecordQuery,
            $dbhFinancial
        );

        $arrBillingRecords = PrimitivesResultsAsArrayGet($resResult);

        foreach ($arrBillingRecords as $recBillingRecord) {
            $arrBillingRecordList[] = new C_Financial_Failed_Billing(
                $recBillingRecord
            );
        }

        return $arrBillingRecordList;
    }

    /**
     * _getSelectStructure - Return the SELECT structure
     *
     * @access private
     *
     * @return string The SELECT structure
     */
    private function getSelectStructure()
    {
        $strBillingRecordQuery = "SELECT br.br_id AS intBillingReportID,\n";
        $strBillingRecordQuery .= "       br.br_summary_id AS intBillingReportSummaryID,\n";
        $strBillingRecordQuery .= "       br.br_service_id AS intServiceID,\n";
        $strBillingRecordQuery .= "       br.br_username AS strUsername,\n";
        $strBillingRecordQuery .= "       br.br_invoice_id AS intInvoiceID,\n";
        $strBillingRecordQuery .= "       br.br_amount_invoiced AS floBillingAmount,\n";
        $strBillingRecordQuery .= "       br.br_refund_amount AS floRefundAmount,\n";
        $strBillingRecordQuery .= "       br.br_invoice_amount AS floInvoiceAmount,\n";
        $strBillingRecordQuery .= "       br.br_employee_name AS strEmployeeName,\n";
        $strBillingRecordQuery .= "       br.br_success_flag AS bolSuccessFlag,\n";
        $strBillingRecordQuery .= "       br.br_reason_contact_unsuccessful AS strReasonContactUnsuccessful,\n";
        $strBillingRecordQuery .= "       br.br_account_type AS strAccountType,\n";
        $strBillingRecordQuery .= "       br.br_failure_reason AS strFailureReason,\n";
        $strBillingRecordQuery .= "       br.br_business_residential_flag AS bolBusinessResidentialFlag,\n";
        $strBillingRecordQuery .= "       br.br_account_deactivated AS bolAccountDeactivated,\n";
        $strBillingRecordQuery .= "       br.br_ticket_phrase_id AS intTicketPhraseID,\n";
        $strBillingRecordQuery .= "       br.dtmBillingDate,\n";
        $strBillingRecordQuery .= "       br.intBillingTypeID,\n";
        $strBillingRecordQuery .= "       bt.vchBillingTypeName AS strBillingTypeName,\n";
        $strBillingRecordQuery .= "       bt.vchBillingHandle AS strBillingHandle,\n";
        $strBillingRecordQuery .= "       bt.bolMoveInvoiceDate\n";
        $strBillingRecordQuery .= "  FROM financial.billing_report_by_customer br\n";
        $strBillingRecordQuery .= "    LEFT JOIN financial.vblBillingType bt ";
        $strBillingRecordQuery .= "           ON br.intBillingTypeID = bt.intBillingTypeID\n";

        return $strBillingRecordQuery;
    }

    /**
     * _assignByArray - assign associative array (record) into object
     *
     * @param array $recBillingRecord Billing Record, as retrieved from the database
     *
     * @access private
     *
     * @return boolean Not much can go wrong.
     */
    private function assignByArray(&$recBillingRecord)
    {
        $this->intBillingReportID = (integer)$recBillingRecord['intBillingReportID'];
        $this->intBillingReportSummaryID = (integer)$recBillingRecord['intBillingReportSummaryID'];
        $this->intServiceID = (integer)$recBillingRecord['intServiceID'];
        $this->strUsername = (string)$recBillingRecord['strUsername'];
        $this->intInvoiceID = (integer)$recBillingRecord['intInvoiceID'];
        $this->floBillingAmount = (double)$recBillingRecord['floBillingAmount'];
        $this->floRefundAmount = (double)$recBillingRecord['floRefundAmount'];
        $this->floInvoiceAmount = (double)$recBillingRecord['floInvoiceAmount'];
        $this->strEmployeeName = (string)trim($recBillingRecord['strEmployeeName']);
        $this->bolSuccessFlag = (boolean)$recBillingRecord['bolSuccessFlag'];
        $this->strReasonContactUnsuccessful = (string)trim($recBillingRecord['strReasonContactUnsuccessful']);
        $this->strAccountType = (string)trim($recBillingRecord['strAccountType']);
        $this->strFailureReason = (string)trim($recBillingRecord['strFailureReason']);
        $this->bolBusinessResidentialFlag = (boolean)$recBillingRecord['bolBusinessResidentialFlag'];
        $this->bolAccountDeactivated = (boolean)$recBillingRecord['bolAccountDeactivated'];
        $this->intTicketPhraseID = (integer)$recBillingRecord['intTicketPhraseID'];
        $this->dtmBillingDate = (string)$recBillingRecord['dtmBillingDate'];
        $this->intBillingTypeID = (integer)$recBillingRecord['intBillingTypeID'];
        $this->strBillingTypeName = (string)trim($recBillingRecord['strBillingTypeName']);
        $this->strBillingHandle = (string)trim($recBillingRecord['strBillingHandle']);
        $this->bolMoveInvoiceDate = (boolean)$recBillingRecord['bolMoveInvoiceDate'];

        return true;
    }

    /**
     * Retrieves the last service event date for an account of specified type(s)
     *
     * <AUTHOR> Marek" <<EMAIL>>
     * @access private
     *
     * @param array $arrServiceEventTypes List of service events types
     * @param int   $intServiceId         The Service ID of the record to get
     *
     * @return mixed (date on success, false on failure)
     */
    private static function getLastServiceEventDate($arrServiceEventTypes, $intServiceId)
    {
        $strServiceEventTypes = implode(',', $arrServiceEventTypes);
        $dbhConnection = get_named_connection_with_db('userdata');

        $strQuery = 'SELECT
                            MAX(se.event_date) AS event_date
                     FROM
                            service_events se
                     WHERE
                            se.service_id = ' . $intServiceId . '
                       AND  se.event_type_id IN (' . $strServiceEventTypes . ')';

        $resResults = PrimitivesQueryOrExit($strQuery, $dbhConnection);
        $strDate = PrimitivesResultGet($resResults, 'event_date');

        return $strDate;
    }

    /**
     * Updates invoice id for given service in
     * financial.billing_report_by_customer table
     *
     * <AUTHOR> Marek" <<EMAIL>>
     * @access public
     *
     * @param int $intInvoiceID The Invoice ID
     *
     * @return mixed (row count on success, false on failure)
     */
    public function updateInvoiceId($intInvoiceID)
    {
        $dbhConnection = get_named_connection_with_db('financial');

        $strQuery = 'UPDATE billing_report_by_customer
                     SET br_invoice_id = ' . $intInvoiceID . '
                     WHERE br_service_id = ' . $this->intServiceID . '
                     AND br_id = ' . $this->intBillingReportID;

        PrimitivesQueryOrExit($strQuery, $dbhConnection);

        return PrimitivesAffectedRowsGet($dbhConnection);
    }

    /**
     * Check if product name is the same now and when customer
     * got into the failed billing state
     *
     * It is a fix for problem 34483.
     *
     * <AUTHOR> Marek" <<EMAIL>>
     * @access public
     *
     * @param string $strProductName Product name to check
     *
     * @return boolean
     */
    public function isProductNameCorrect($strProductName)
    {
        $bolProductNameCorrect = true;
        if ($this->strAccountType <> $strProductName) {
            $bolProductNameCorrect = false;

            // Take date of the last reactivation
            $arrServiceEventTypes = array(
                SERVICE_EVENT_FAILED_BILLING_RESTRICTIONS_REMOVED_BY_PAYMENT,
                SERVICE_EVENT_FAILED_BILLING_RESTRICTIONS_REMOVED_BY_CSC,
                SERVICE_EVENT_FAILED_BILLING_REACTIVATED_BY_PAYMENT,
                SERVICE_EVENT_FAILED_BILLING_REACTIVATED_BY_CSC
            );

            $strLastReactivationDate = $this->getLastServiceEventDate(
                $arrServiceEventTypes,
                $this->intServiceID
            );

            $stmLastReactivationDate = strtotime($strLastReactivationDate);

            // Take date of the last failed billing state
            $arrServiceEventTypes = array(
                SERVICE_EVENT_FAILED_BILLING_STATE_ENTERED
            );

            $strLastFailedBillingDate = $this->getLastServiceEventDate(
                $arrServiceEventTypes,
                $this->intServiceID
            );

            $stmLastFailedBillingDate = strtotime($strLastFailedBillingDate);

            // Take date of the last account type change or product refresh
            $arrServiceEventTypes = array(
                SERVICE_EVENT_TYPE_CHANGE,
                SERVICE_EVENT_CHANGE_PRODUCT_FOR_PRODUCT_REFRESH_2006
            );

            $strLastAccountTypeChangeDate = $this->getLastServiceEventDate(
                $arrServiceEventTypes,
                $this->intServiceID
            );

            $stmLastAccountTypeChangeDate = strtotime(
                $strLastAccountTypeChangeDate
            );

            if ($stmLastReactivationDate < $stmLastFailedBillingDate &&
                $stmLastAccountTypeChangeDate > $stmLastFailedBillingDate
            ) {
                $bolProductNameCorrect = true;
            }
        }

        return $bolProductNameCorrect;
    }

    /**
     * Check if customer has been sent for debt recovery
     *
     * <AUTHOR> Marek" <<EMAIL>>
     * @access public
     * @static
     *
     * @param int $intServiceId Service ID to check
     *
     * @return boolean
     */
    public static function IsSentToDebtRecovery($intServiceId)
    {
        if (FeatureToggleManager::isOnFiltered('RBM_MIGRATION_COMPLETE', null, null, null, $intServiceId)) {
            try {
                $failedPaymentInstance =
                    FailedPaymentInstance::fetchInDebtRecoveryByServiceId(
                        $intServiceId
                    );

                $endDate = $failedPaymentInstance->getEndDate();

                $endDate = !empty($endDate)
                    ? \I18n_Date::fromDateTime($endDate) : null;

                $instance = new FailedBilling_Instance(
                    $failedPaymentInstance->getBusinessActor(),
                    new PositiveInt($failedPaymentInstance->getInstanceId()),
                    new Int($failedPaymentInstance->getInvoiceId()),
                    new PositiveInt($failedPaymentInstance->getIspReportSectorId()),
                    new PositiveInt($failedPaymentInstance->getFailedBillingDay()),
                    \I18n_Date::fromDateTime($failedPaymentInstance->getStartDate()),
                    $endDate
                );
            } catch (NoFailedPaymentDetailsException $e) {
                // Do Nothing
            }

        } else {
            try {
                $instance = FailedBilling_Instance::fetchInstanceInDebtRecoveryByServiceId(
                    new PositiveInt($intServiceId)
                );
            } catch (FailedBilling_NoDataException $e) {
                //Do nothing
                //Continue with the FBV1 checking
            }
        }

        if (isset($instance) && $instance instanceof FailedBilling_Instance) {
            return true;
        }

        // Take date of the last reactivation
        $arrServiceEventTypes = array(
            SERVICE_EVENT_FAILED_BILLING_RESTRICTIONS_REMOVED_BY_PAYMENT,
            SERVICE_EVENT_FAILED_BILLING_RESTRICTIONS_REMOVED_BY_CSC,
            SERVICE_EVENT_FAILED_BILLING_REACTIVATED_BY_PAYMENT,
            SERVICE_EVENT_FAILED_BILLING_REACTIVATED_BY_CSC
        );

        $strLastReactivationDate =
            C_Financial_Failed_Billing::getLastServiceEventDate(
                $arrServiceEventTypes,
                $intServiceId
            );

        $stmLastReactivationDate =
            (!$strLastReactivationDate) ? 0 : strtotime(
                $strLastReactivationDate
            );

        // Take date of the last failed billing state
        $arrServiceEventTypes = array(
            SERVICE_EVENT_FAILED_BILLING_STATE_ENTERED
        );

        $strLastFailedBillingDate =
            C_Financial_Failed_Billing::getLastServiceEventDate(
                $arrServiceEventTypes,
                $intServiceId
            );

        $stmLastFailedBillingDate =
            (!$strLastFailedBillingDate) ? 0 : strtotime(
                $strLastFailedBillingDate
            );

        // Take date of the last sent to debt recovery state
        $arrServiceEventTypes = array(SERVICE_EVENT_SENT_TO_DEBT_RECOVERY);

        $strLastSetToDebtRecoverDate =
            C_Financial_Failed_Billing::getLastServiceEventDate(
                $arrServiceEventTypes,
                $intServiceId
            );

        $stmLastSetToDebtRecoverDate =
            (!$strLastSetToDebtRecoverDate) ? 0 : strtotime(
                $strLastSetToDebtRecoverDate
            );

        if ($stmLastReactivationDate < $stmLastFailedBillingDate &&
            $stmLastSetToDebtRecoverDate > $stmLastFailedBillingDate
        ) {
            return true;
        }

        return false;
    }

    /**
     * Get last failed billing event times
     *
     * @param int $intServiceId Service id
     *
     * @return array
     */
    private static function getLastFailedBillingEventTimes($intServiceId)
    {
        // Take date of the last reactivation
        $arrServiceEventTypes = array(
            SERVICE_EVENT_FAILED_BILLING_RESTRICTIONS_REMOVED_BY_PAYMENT,
            SERVICE_EVENT_FAILED_BILLING_RESTRICTIONS_REMOVED_BY_CSC,
            SERVICE_EVENT_FAILED_BILLING_REACTIVATED_BY_PAYMENT,
            SERVICE_EVENT_FAILED_BILLING_REACTIVATED_BY_CSC
        );

        $strLastReactivationDate =
            C_Financial_Failed_Billing::getLastServiceEventDate(
                $arrServiceEventTypes,
                $intServiceId
            );

        $stmLastReactivationDate =
            (!$strLastReactivationDate) ? 0 : strtotime(
                $strLastReactivationDate
            );

        // Take date of the last failed billing state
        $arrServiceEventTypes = array(
            SERVICE_EVENT_FAILED_BILLING_STATE_ENTERED
        );

        $strLastFailedBillingDate =
            C_Financial_Failed_Billing::getLastServiceEventDate(
                $arrServiceEventTypes,
                $intServiceId
            );

        $stmLastFailedBillingDate =
            (!$strLastFailedBillingDate) ? 0 : strtotime(
                $strLastFailedBillingDate
            );

        return array(
            'uxtFailedBilling' => $stmLastFailedBillingDate,
            'uxtReactivation'  => $stmLastReactivationDate
        );
    }

    /**
     * Check if customer is in failed billing state
     *
     * <AUTHOR> Marek" <<EMAIL>>
     * @access public
     * @static
     *
     * @param int $intServiceId Service ID to check
     *
     * @return boolean
     */
    public static function IsInFailedBillingState($intServiceId)
    {
        $arrBillingEvents = self::getLastFailedBillingEventTimes(
            $intServiceId
        );

        $inFailedBilling = ($arrBillingEvents['uxtReactivation'] < $arrBillingEvents['uxtFailedBilling']);
        if (false == $inFailedBilling) {
            if (FeatureToggleManager::isOnFiltered('RBM_MIGRATION_COMPLETE', null, null, null, $intServiceId)) {
                $inFailedBilling = Plusnet\FailedPayment\FailedPaymentHandler::isFbVersionTwoJourney($intServiceId);
            } else {
                $inFailedBilling = BusTier_BusTier::getClient('failed_billing')->isFbVersionTwoJourney(
                    new PositiveInt($intServiceId)
                );
            }
        }

        return $inFailedBilling;
    }

    /**
     * Is after failed billing period
     *
     * @param int $intServiceId Service id
     *
     * @return bool
     */
    public static function isAfterFailedBillingPeriod($intServiceId)
    {
        $instance = null;

        if (FeatureToggleManager::isOnFiltered('RBM_MIGRATION_COMPLETE', null, null, null, $intServiceId)) {
            try {
                $failedPaymentInstance =
                    FailedPaymentInstance::fetchByServiceId($intServiceId);

                $endDate = $failedPaymentInstance->getEndDate();

                $endDate = !empty($endDate)
                    ? \I18n_Date::fromDateTime($endDate) : null;

                $instance = new FailedBilling_Instance(
                    $failedPaymentInstance->getBusinessActor(),
                    new PositiveInt($failedPaymentInstance->getInstanceId()),
                    new Int($failedPaymentInstance->getInvoiceId()),
                    new PositiveInt($failedPaymentInstance->getIspReportSectorId()),
                    new PositiveInt($failedPaymentInstance->getFailedBillingDay()),
                    \I18n_Date::fromDateTime($failedPaymentInstance->getStartDate()),
                    $endDate
                );
            } catch (NoFailedPaymentDetailsException $e) {
                // There is no failed payment data
            }
        } else {
            try {
                $instance = BusTier_BusTier::getClient('failed_billing')
                    ->fetchInstanceByServiceId(new PositiveInt($intServiceId));

            } catch (FailedBilling_NoDataException $e) {
                //Reaching here means there is no active FBV2 instance for the customer,
                //as FailedBilling_NoDataException is thrown only when there is FBV2 data
                //returned from a adaptor query
                //so do nothing here, go for checking FBV1
            }
        }

        if ($instance instanceof FailedBilling_Instance &&
            in_array(
                $instance->getFbStageHandle(),
                array(
                    FailedPaymentInstance::FP_STAGE_TWO_HANDLE,
                    FailedPaymentInstance::FP_STAGE_THREE_HANDLE
                )
            )
        ) {
            return true;
        }


        $arrBillingEvents = self::getLastFailedBillingEventTimes(
            $intServiceId
        );

        // If there's no failed billing event, they're not in failed billing
        if (0 == $arrBillingEvents['uxtFailedBilling']) {
            return false;
        }

        // If latest reactivation is after latest failed billing event,
        // they're not in failed billing
        if ($arrBillingEvents['uxtReactivation'] > $arrBillingEvents['uxtFailedBilling']) {
            return false;
        }

        // Getting here must mean that:
        // 1. reactivation date is before failed billing date.
        // 2. there is no reactivation date.
        //   1 or 2 is true, therefore they must be in failed billing.
        $uxtFailedBillingCutoffPeriod =
            strtotime(
                '+' . self::FAILED_BILLING_GRACE_PERIOD_DAYS . ' day',
                $arrBillingEvents['uxtFailedBilling']
            );

        $uxtNow = time();

        // If now is greater than the date of failed billing
        // plus our grace period,
        // they must be 'after' this failed billing period.
        return ($uxtNow > $uxtFailedBillingCutoffPeriod);
    }
}
