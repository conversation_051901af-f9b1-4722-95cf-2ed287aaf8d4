<?php

/**
* Payment Scheduler class
*
*
*
* @package    Core
* @subpackage Financial
* @access     public
* <AUTHOR> <<EMAIL>>
* @version    $Id: CPaymentScheduler.inc,v 1.6 2008-12-01 06:46:00 nwood Exp $
* @filesource
*/

require_once DATABASE_LIBRARY_ROOT.'sql_primitives.inc';

require_once '/local/data/mis/database/database_libraries/CoreObjects/CObject/CObject.inc';
require_once '/local/data/mis/database/database_libraries/CoreObjects/Utility/CValidator.inc';
require_once '/local/data/mis/database/database_libraries/CoreObjects/Financial/CFinancialHelper.inc';
require_once '/local/data/mis/database/database_libraries/CoreObjects/Financial/CScheduledPayment.inc';
require_once '/local/data/mis/database/database_libraries/CoreObjects/Financial/CPaymentSchedulerAutoProblem.inc';
require_once '/local/data/mis/common_library_functions/class_libraries/Logging/Logging.class.php';

/**
* Payment Scheduler class
*
* Abstract.
*
* @access     public
* <AUTHOR> <<EMAIL>>
*/
class CPaymentScheduler extends CObject
{
    /**
    * Account ID
    *
    * @var integer
    * @access private
    */
    var $m_intAccountID = 0;


    /**
    * Actioner ID
    *
    * @var integer
    * @access private
    */
    var $m_strActionerID = '';


    /**
    * ID of type of payment
    *
    * @var integer
    * @access private
    */
    var $m_intScheduledPaymentTypeID = 0;



    ////////////////
    // Constructor
    ////////////////


    /**
    * Contructor for the CPaymentScheduler class
    *
    * @access protected
    * <AUTHOR>
    * @param  integer
    * @return boolean success
    */
    function CPaymentScheduler($strActionerID)
    {
        $this->m_strActionerID = $strActionerID;
        $this->m_intAccountID = $this->getAccountID();

        return true;
    }

    //////////////
    // Accessors
    //////////////



    /////////////////////
    // Static Methods
    ////////////////////



    //////////////////////////////
    // Static Validator Methods
    //////////////////////////////



    ////////////////////////
    // Public Methods
    ////////////////////////


    /**
    * Create a scheduled payment entry for an amount exclusive of VAT
    *
    *
    * @access public
    * <AUTHOR>
    * @param  integer Amount in pence, ex vat.  Must be > 0
    * @param  integer Date due as a unix timestamp.  Will be converted to a date.
    * @param  string  Description of the charge.  This will appear as a line item description on the invoice.
    * @param  integer Unix timestamp of the start date of the period covered by this payment (optional)
    * @param  integer Unix timestamp of the end date of the period covered by this payment (optional)
    * @return integer The ID of the inserted row, or boolean false on error with the error object set.
    */
    function addExVatAmount($intAmountExVatPence, $uxtDateDue, $strDescription, $uxtStartPeriod=0, $uxtEndPeriod=0)
    {
        //Calc vat element on ExVat amount
        $arrAmountsPence = CFinancialHelper::calcVat($intAmountExVatPence, false);

        return $this->createPayment($arrAmountsPence['exvat'], $arrAmountsPence['vat'], $uxtDateDue, $strDescription, $uxtStartPeriod, $uxtEndPeriod, $arrAmountsPence['vatrate']);
    }


    /**
    * Create a scheduled payment entry for an amount inclusive of VAT
    *
    *
    *
    * @access public
    * <AUTHOR>
    * @param  integer Amount in pence, inc vat.  Must be > 0
    * @param  integer Date due as a unix timestamp.  Will be converted to a date.
    * @param  string  Description of the charge.  This will appear as a line item description on the invoice.
    * @param  integer Unix timestamp of the start date of the period covered by this payment (optional)
    * @param  integer Unix timestamp of the end date of the period covered by this payment (optional)
    * @return integer The ID of the inserted row, or boolean false on error with the error object set.
    */
    function addIncVatAmount($intAmountIncVatPence, $uxtDateDue, $strDescription, $uxtStartPeriod=0, $uxtEndPeriod=0)
    {
        //Split vat element from IncVat amount
        $arrAmountsPence = CFinancialHelper::splitVAT($intAmountIncVatPence, false);

        return $this->createPayment($arrAmountsPence['exvat'], $arrAmountsPence['vat'], $uxtDateDue, $strDescription, $uxtStartPeriod, $uxtEndPeriod, $arrAmountsPence['vatrate']);
    }


    /**
    * Get all scheduled payments
    *
    * Abstract.  Must be overridden
    *
    * @access public
    * <AUTHOR>
    * @return array Scheduled payments
    */
    function getScheduledPayments()
    {
        die('Override CPaymentScheduler::getScheduledPayments()');
    }


    ////////////////////////
    // Private Methods
    ////////////////////////


    /**
    * Create a Scheduled Payment entry
    *
    *
    * @access private
    * <AUTHOR>
    * @param  integer Amount in pence, ex vat.  Must be > 0
    * @param  integer VAT ammount to apply in pence
    * @param  integer Date due as a unix timestamp.  Will be converted to a date.
    * @param  string  Description of the charge.  This will appear as a line item description on the invoice.
    * @param  integer Unix timestamp of the start date of the period covered by this payment (optional)
    * @param  integer Unix timestamp of the end date of the period covered by this payment (optional)
    * @return integer The ID of the inserted row, or boolean false on error with the error object set.
    */
    function createPayment($intAmountExVatPence, $intVatPence, $uxtDateDue, $strDescription, $uxtStartPeriod=0, $uxtEndPeriod=0, $decVatRate=null)
    {
        //Validation

        if(strlen($strDescription) < 1) {
            $this->setError(__FILE__, __LINE__, "Cannot create scheduled payment: Description not set.");
            return false;
        }

        // Don't create the scheduled payment if the due date is more than three months in the past
        $uxtDateThreshold = strtotime('-3 month', mktime(0, 0, 0));
        if ($uxtDateDue < $uxtDateThreshold) {

            $objCoreService = Core_Service::getServiceByAccountId($this->m_intAccountID);
            if ($objCoreService->getIsp() != 'partner') {
                $intServiceId = $objCoreService->getServiceId();
                $arrData = array(
                    'intAmountExVatPence' => $intAmountExVatPence,
                    'intVatPence'         => $intVatPence,
                    'dteDateDue'          => date('Y-m-d', $uxtDateDue),
                    'uxtDateDue'          => $uxtDateDue,
                    'strDescription'      => $strDescription,
                    'dteStartPeriod'      => date('Y-m-d', $uxtStartPeriod),
                    'dteEndPeriod'        => date('Y-m-d', $uxtEndPeriod),
                    'strReason'           => 'Due date is more than three months in the past'
                );
                CPaymentSchedulerAutoProblem::getInstance('scheduledPaymentInvalid')
                    ->add($intServiceId, $arrData, Logging::getCallStackAsString());
                $this->setError(
                    __FILE__, __LINE__,
                    "Cannot create a scheduled payment with a due date more than three months in the past"
                );
                return false;
            }
        }

        $strStartPeriod = ($uxtStartPeriod > 0) ? "FROM_UNIXTIME($uxtStartPeriod)" : "NULL";
        $strEndPeriod   = ($uxtEndPeriod > 0)   ? "FROM_UNIXTIME($uxtEndPeriod)"   : "NULL";

        if(!isset($decVatRate)) {

            $decVatRate = CFinancialHelper::getCurrentVatRate();
        }

        //Create the payment
        $dbhConn = get_named_connection_with_db('financial');

        $strQuery = "INSERT INTO financial.tblScheduledPayment
                             SET intAccountID = '{$this->m_intAccountID}',
                                 intScheduledPaymentTypeID = '{$this->m_intScheduledPaymentTypeID}',
                                 vchDescription = '". addslashes($strDescription). "',
                                 vchActionerID = '". addslashes($this->m_strActionerID). "',
                                 intAmountExVatPence = '$intAmountExVatPence',
                                 intVatPence = '$intVatPence',
                                 dteDue = FROM_UNIXTIME($uxtDateDue),
                                 dteStartPeriodCovered = $strStartPeriod,
                                 dteEndPeriodCovered = $strEndPeriod,
                                 dtmInvoiced = NULL,
                                 decVatRate = $decVatRate";

        PrimitivesQueryOrExit($strQuery, $dbhConn, 'Insert a scheduled payment for a product component');

        $this->m_intScheduledPaymentID = PrimitivesInsertIdGet($dbhConn);

        if(! $this->createPaymentConfig()) {
            $this->setError(__FILE__, __LINE__, "Unable to insert config entry for scheduled payment id '{$this->m_intScheduledPaymentID}'");
        }

        return $this->m_intScheduledPaymentID;

    }


    /**
    * Create a Scheduled Payment entry
    *
    * @param  int $intScheduledPaymentID 
    *
    * @return boolean if the update was sucsessful or not
    */
    public function cancelPayment($intScheduledPaymentID)
    {
        if(is_numeric($intScheduledPaymentID) == false) {
            $this->setError(__FILE__, __LINE__, "Cannot cancel scheduled payment: with id $intScheduledPaymentID");
            return false;
        }

        $dbhConn = get_named_connection_with_db('financial');

        $strQuery = "update financial.tblScheduledPayment
                             SET dtmCancelled = NOW()
                        WHERE
                            intScheduledPaymentID = $intScheduledPaymentID";

        PrimitivesQueryOrExit($strQuery, $dbhConn, 'Cancel a scheduled payment');

        return true;
    }

    /**
    * Find the Account ID for this payment
    *
    * Abstract.  Must be overridden.
    *
    * @access private
    * <AUTHOR>
    * @return integer Account ID
    */
    function getAccountID()
    {
        die('Error: You must override CPaymentScheduler::getAccountID()');
    }


    /**
    * Insert the details into the config for a scheduled payment
    *
    * Abstract.  Must be overridden.
    *
    * @access private
    * <AUTHOR>
    * @return integer Account ID
    */
    function createPaymentConfig()
    {
        die('Error: You must override CPaymentScheduler::createPaymentConfig()');
    }

}

?>
