<?php
/**
 * Premise DAO class
 * 
 * @package    Core
 * @subpackage Premise
 * <AUTHOR> <<EMAIL>>
 * @link       http://www.plus.net/
 */
/**
 * Premise DAO class
 *
 * <code>
 * <i>userdata.tblPremise</i> table
 * +--------------------+---------------------+------+-----+-------------------+----------------+
 * | Field              | Type                | Null | Key | Default           | Extra          |
 * +--------------------+---------------------+------+-----+-------------------+----------------+
 * | intPremiseId       | int(10) unsigned    |      | PRI | NULL              | auto_increment |
 * | intPremiseStatusId | tinyint(3) unsigned | YES  | MUL | NULL              |                |
 * | vchHouse           | varchar(255)        | YES  |     | NULL              |                |
 * | vchStreet          | varchar(255)        | YES  |     | NULL              |                |
 * | vchTown            | varchar(255)        | YES  |     | NULL              |                |
 * | vchCounty          | varchar(255)        | YES  |     | NULL              |                |
 * | vchPostcode        | varchar(10)         | YES  | MUL | NULL              |                |
 * | vchCountry         | char(3)             | YES  |     | NULL              |                |
 * | vchActorId         | varchar(32)         | YES  |     | NULL              |                |
 * | stmLastUpdate      | timestamp           | YES  |     | CURRENT_TIMESTAMP |                |
 * +--------------------+---------------------+------+-----+-------------------+----------------+
 *
 * <i>userdata.tblPremiseStatus</i> table
 * +--------------------+---------------------+------+-----+-------------------+----------------+
 * | Field              | Type                | Null | Key | Default           | Extra          |
 * +--------------------+---------------------+------+-----+-------------------+----------------+
 * | intPremiseStatusId | tinyint(3) unsigned |      | PRI | NULL              | auto_increment |
 * | vchHandle          | varchar(32)         |      | UNI |                   |                |
 * | stmLastUpdate      | timestamp           | YES  |     | CURRENT_TIMESTAMP |                |
 * +--------------------+---------------------+------+-----+-------------------+----------------+
 * </code>
 *
 * <i>userdata.tblPremiseHasType</i> table
 * +------------------+---------------------+------+-----+-------------------+-------+
 * | Field            | Type                | Null | Key | Default           | Extra |
 * +------------------+---------------------+------+-----+-------------------+-------+
 * | intPremiseId     | int(10) unsigned    |      | PRI | 0                 |       |
 * | intPremiseTypeId | tinyint(3) unsigned |      | PRI | 0                 |       |
 * | stmLastUpdate    | timestamp           | YES  |     | CURRENT_TIMESTAMP |       |
 * +------------------+---------------------+------+-----+-------------------+-------+
 *
 * <i>userdata.tblPremiseType</i> table
 * +------------------+---------------------+------+-----+-------------------+----------------+
 * | Field            | Type                | Null | Key | Default           | Extra          |
 * +------------------+---------------------+------+-----+-------------------+----------------+
 * | intPremiseTypeId | tinyint(3) unsigned |      | PRI | NULL              | auto_increment |
 * | vchHandle        | varchar(32)         |      | UNI |                   |                |
 * | vchDisplayName   | varchar(64)         |      |     |                   |                |
 * | stmLastUpdate    | timestamp           | YES  |     | CURRENT_TIMESTAMP |                |
 * +------------------+---------------------+------+-----+-------------------+----------------+
 * </code>
 *
 * @package    Core
 * @subpackage Premise
 * <AUTHOR> Filak <<EMAIL>>
 * @link       http://www.plus.net/
 */
class Core_PremiseDao extends Db_Object implements Core_IAddressDao
{
    /**
     * @access protected
     * @var string Primary key name
     */
    protected $strPrimaryKey = 'intPremiseId';

    protected $intPremiseId;

    protected $vchHouse = null;

    protected $vchStreet = null;

    protected $vchTown = null;

    protected $vchCounty = null;

    protected $vchPostcode = null;

    protected $vchCountry = null;

    protected $vchActorId = null;

    protected $vchStatusHandle = null;

    protected $vchTypeHandle = null;

    protected $vchTypeName = null;

    protected $arrSetterFunctions = array(
        'intPremiseId'    => 'PremiseId',
        'vchHouse'        => 'House',
        'vchStreet'       => 'Street',
        'vchTown'         => 'Town',
        'vchCounty'       => 'County',
        'vchPostcode'     => 'Postcode',
        'vchCountry'      => 'Country',
        'vchActorId'      => 'ActorId',
        'vchStatusHandle' => 'StatusHandle',
        'vchTypeHandle'   => 'TypeHandle',
        'vchTypeName'     => 'TypeName'
    );

    /**
     * Fetch a single row from <i>userdata.tblPremise</i> table
     *
     * @param int    $intPremiseId   Premise Id
     * @param string $strTransaction Transaction name
     *
     * @return Core_PremiseDao
     */
    public static function get($intPremiseId, $strTransaction = Db_Manager::DEFAULT_TRANSACTION)
    {
        $strDebugSource = __CLASS__ . ' ' . __METHOD__;
        Dbg_Dbg::write($strDebugSource.': Fetching Premise details for Id '.$intPremiseId, 'Core');

        return parent::getObject(get_class(), $intPremiseId, $strTransaction);
    }

    /**
     * Sets Premise Id
     *
     * @param int $premiseId Premise Id
     *
     * @return void
     */
    public function setPremiseId($premiseId)
    {
        $this->intPremiseId = $premiseId;
    }

    /**
     * Sets house name/number
     *
     * @param string $house House name/number
     *
     * @return void
     */
    public function setHouse($house)
    {
        $this->vchHouse = $house;
    }

    /**
     * Sets street name
     *
     * @param string $street Street name
     *
     * @return void
     */
    public function setStreet($street)
    {
        $this->vchStreet = $street;
    }

    /**
     * Sets town/city name
     *
     * @param string $town Town/city name
     *
     * @return void
     */
    public function setTown($town)
    {
        $this->vchTown = $town;
    }

    /**
     * Sets country name
     *
     * @param string $county Country name
     *
     * @return void
     */
    public function setCounty($county)
    {
        $this->vchCounty = $county;
    }

    /**
     * Sets postcode
     *
     * @param string $postcode Postcode
     *
     * @return void
     */
    public function setPostcode($postcode)
    {
        $this->vchPostcode = $postcode;
    }

    /**
     * Sets country name
     *
     * @param string $country Country name
     *
     * @return void
     */
    public function setCountry($country)
    {
        $this->vchCountry = $country;
    }

    /**
     * Sets Actor Id
     *
     * @param string $actorId Actor Id of the actioner
     *
     * @return void
     */
    public function setActorId($actorId)
    {
        $this->vchActorId = $actorId;
    }

    /**
     * Sets premise status handle
     *
     * @param string $handle Premise status handle
     *
     * @return void
     */
    public function setStatusHandle($handle)
    {
        $this->vchStatusHandle = $handle;
    }

    /**
     * Sets premise type handle
     *
     * @param string $handle Premise type handle
     *
     * @return void
     */
    public function setTypeHandle($handle)
    {
        $this->vchTypeHandle = $handle;
    }

    /**
     * Sets premise type display name
     *
     * @param string $name Premise type display name
     *
     * @return void
     */
    public function setTypeName($name)
    {
        $this->vchTypeName = $name;
    }

    /**
     * Returns Premise Id
     *
     * @return int
     */
    public function getPremiseId()
    {
        return $this->intPremiseId;
    }

    /**
     * Returns house name/number
     *
     * @return string
     */
    public function getHouse()
    {
        return $this->vchHouse;
    }

    /**
     * Returns street name
     *
     * @return string
     */
    public function getStreet()
    {
        return $this->vchStreet;
    }

    /**
     * Returns town/city name
     *
     * @return string
     */
    public function getTown()
    {
        return $this->vchTown;
    }

    /**
     * Returns county name
     *
     * @return string
     */
    public function getCounty()
    {
        return $this->vchCounty;
    }

    /**
     * Returns postcode
     *
     * @return string
     */
    public function getPostcode()
    {
        return $this->vchPostcode;
    }

    /**
     * Returns country name
     *
     * @return string
     */
    public function getCountry()
    {
        return $this->vchCountry;
    }

    /**
     * Returns Actor Id
     *
     * @return string
     */
    public function getActorId()
    {
        return $this->vchActorId;
    }

    /**
     * Returns premise status handle
     *
     * @return string
     */
    public function getStatusHandle()
    {
        return $this->vchStatusHandle;
    }

    /**
     * Returns premise type handle
     *
     * @return string
     */
    public function getTypeHandle()
    {
        return $this->vchTypeHandle;
    }

    /**
     * Returns premise type display name
     *
     * @return string
     */
    public function getTypeName()
    {
        return $this->vchTypeName;
    }
}