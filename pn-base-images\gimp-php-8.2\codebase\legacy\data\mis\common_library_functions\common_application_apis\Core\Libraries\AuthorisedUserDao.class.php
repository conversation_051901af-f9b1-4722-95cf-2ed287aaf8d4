<?php
/**
 * Authorised User DAO class
 *
 * @package    Core
 * @subpackage AuthorisedUser
 * <AUTHOR> <<EMAIL>>
 * @link       http://www.plus.net/
 */
/**
 * Authorised User DAO class
 *
 * <code>
 * <i>userdata.tblAuthorisedUser</i> table
 * +---------------------+------------------+------+-----+-------------------+----------------+
 * | Field               | Type             | Null | Key | Default           | Extra          |
 * +---------------------+------------------+------+-----+-------------------+----------------+
 * | intAuthorisedUserId | int(10) unsigned |      | PRI | NULL              | auto_increment |
 * | intServiceId        | int(10) unsigned | YES  | MUL | NULL              |                |
 * | vchName             | varchar(255)     | YES  |     | NULL              |                |
 * | dtmStart            | datetime         | YES  |     | NULL              |                |
 * | dtmEnd              | datetime         | YES  |     | NULL              |                |
 * | vchActorId          | varchar(32)      | YES  |     | NULL              |                |
 * | stmLastUpdate       | timestamp        | YES  |     | CURRENT_TIMESTAMP |                |
 * +---------------------+------------------+------+-----+-------------------+----------------+
 * </code>
 *
 * @package    Core
 * @subpackage AuthorisedUser
 * <AUTHOR> Filak <<EMAIL>>
 * @link       http://www.plus.net/
 */

class Core_AuthorisedUserDao extends Db_Object implements Core_IUserDao
{
    /**
     * @access protected
     * @var string Primary key name
     */
    protected $strPrimaryKey = 'intAuthorisedUserId';

    protected $intAuthorisedUserId = null;

    protected $intServiceId = null;

    protected $vchName = null;

    protected $dtmStart = null;

    protected $dtmEnd = null;

    protected $vchActorId = null;

    protected $arrSetterFunctions = array(
        'intAuthorisedUserId' => 'AuthorisedUserId',
        'intServiceId'        => 'ServiceId',
        'vchName'             => 'Name',
        'dtmStart'            => 'StartDate',
        'dtmEnd'              => 'EndDate',
        'vchActorId'          => 'ActorId',
    );

    /**
     * Fetch a single row from <i>userdata.tblAuthorisedUser</i> table
     *
     * @param int    $intAuthorisedUserId Authorised User Id
     * @param string $strTransaction      Transaction name
     *
     * @return Core_AuthorisedUserDao
     */
    public static function get($intAuthorisedUserId, $strTransaction = Db_Manager::DEFAULT_TRANSACTION)
    {
        $strDebugSource = __CLASS__ . ' ' . __METHOD__;
        Dbg_Dbg::write($strDebugSource.': Fetching Authorised User details for Id '.$intAuthorisedUserId, 'Core');

        return parent::getObject(get_class(), $intAuthorisedUserId, $strTransaction);
    }

    /**
     * Sets Authorised User Id
     *
     * @param int $authorisedUserId Authorised User Id
     *
     * @return void
     */
    public function setAuthorisedUserId($authorisedUserId)
    {
        $this->intAuthorisedUserId = $authorisedUserId;
    }

    /**
     * Sets Service Id
     *
     * @param int $serviceId Service Id
     *
     * @return void
     */
    public function setServiceId($serviceId)
    {
        $this->intServiceId = $serviceId;
    }

    /**
     * Sets Name
     *
     * @param string $name Name
     *
     * @return void
     */
    public function setName($name)
    {
        $this->vchName = $name;
    }

    /**
     * Sets Start date
     *
     * @param date $startDate Start date
     *
     * @return void
     */
    public function setStartDate($startDate)
    {
        $this->dtmStart = $startDate;
    }

    /**
     * Sets End date
     *
     * @param date $endDate End date
     *
     * @return void
     */
    public function setEndDate($endDate)
    {
        $this->dtmEnd = $endDate;
    }

    /**
     * Sets Actor Id
     *
     * @param string $actorId Actor Id of the actioner
     *
     * @return void
     */
    public function setActorId($actorId)
    {
        $this->vchActorId = $actorId;
    }

    /**
     * Returns Authorised User Id
     *
     * @return int
     */
    public function getAuthorisedUserId()
    {
        return $this->intAuthorisedUserId;
    }

    /**
     * Returns Service Id
     *
     * @return int
     */
    public function getServiceId()
    {
        return $this->intServiceId;
    }

    /**
     * Returns Name
     *
     * @return string
     */
    public function getName()
    {
        return $this->vchName;
    }

    /**
     * Returns Start Date
     *
     * @return date
     */
    public function getStartDate()
    {
        return $this->dtmStart;
    }

    /**
     * Returns End Date
     *
     * @return date
     */
    public function getEndDate()
    {
        return $this->dtmEnd;
    }

    /**
     * Returns Actor Id
     *
     * @return string
     */
    public function getActorId()
    {
        return $this->vchActorId;
    }
}
