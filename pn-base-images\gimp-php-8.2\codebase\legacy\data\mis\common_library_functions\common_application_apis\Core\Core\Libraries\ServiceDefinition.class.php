<?php
/**
 * ServiceDefinition class
 *
 * @package   Core
 * <AUTHOR> <k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@plus.net>
 *
 * @copyright 2009 PlusNet
 * @version   CVS: $Id: ServiceDefinition.class.php,v 1.2.2.2 2009/07/02 12:46:14 mstarbuck Exp $
 * @link      Link
 * @since     File available since 16/03/2009
 */

/**
 * ServiceDefinition class
 *
 * @package   Core
 * <AUTHOR> <kp<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@plus.net>
 * @copyright 2009 PlusNet
 * @link      Link
 */

class Core_ServiceDefinition
{
    /**
     * Const for VALUE products family
     *
     * @var string
     */
    const FAMILY_VALUE = 'VALUE';

    /**
     * Const for VALUE products family
     *
     * @var string
     */
    const FAMILY_VALUE_SOLUS = 'VALUE_SOLUS';

    /**
     * Const for VALUE products family
     *
     * @var string
     */
    const FAMILY_VALUE_DUALPLAY = 'VALUE_DUALPLAY';

    /**
     * Const for BPR09 products family
     *
     * @var string
     */
    const FAMILY_BPR09 = 'BPR09';

    /**
     * Const for BBYW products family
     *
     * @var string
     */
    const FAMILY_BBYW = 'BBYW';

    /**
     * Const for Payemnt Services products family
     *
     * @var string
     */
    const FAMILY_PAYMENT_SERVICES = 'PAYMENT_SERVICES';

    /**
     * Const for John Lewis product family
     *
     * @var string
     */
    const FAMILY_JOHNLEWIS = 'JOHNLEWIS2011';

    /**
     * Const for John Lewis product family
     *
     * @var string
     */
    const FAMILY_RES2012 = 'PNRESRPR12';

    /**
     * Default discount length in months
     *
     * @var int
     */
    const DEFAULT_DISCOUNT_LENGTH = 3;

    /**
     * Const for business account types
     *
     * @var string
     */
    const BUSINESS_ACCOUNT = 'business';

    /**
     * Const for business account types
     *
     * @var string
     */
    const RESIDENTIAL_ACCOUNT = 'residential';

    /**
     * Const for partner isp
     *
     * @var string
     */
    const PARTNER_ISP = 'partner';

    const JOHN_LEWIS_ISP = 'johnlewis';
    const WAITROSE_ISP = 'waitrose';
    const GREENBEE_ISP = 'greenbee';

    private static $johnLewisVisps = array(self::JOHN_LEWIS_ISP, self::WAITROSE_ISP, self::GREENBEE_ISP);

    /**
     * Data access object
     *
     * @var Core_ServiceDefinitionDao
     */
    private $_objDao = null;

    /**
     * Constructor
     *
     * @param int    $intServiceDefinitionId Service definition id
     * @param string $strTransactionName     Transaction name
     *
     * @return none
     */
    public function __construct($intServiceDefinitionId = null, $strTransactionName = Db_Manager::DEFAULT_TRANSACTION)
    {
        if (isset($intServiceDefinitionId)) {
            $intServiceDefinitionId = (int) $intServiceDefinitionId;
            $this->_objDao = Core_ServiceDefinitionDao::get($intServiceDefinitionId, $strTransactionName);
        }
    }

    /**
     * Static function to get class instance
     *
     * @param int    $intServiceDefinitionId Service definition id
     * @param string $strTransactionName     Transaction name
     *
     * @return self
     */
    public static function instance(
        $intServiceDefinitionId = null,
        $strTransactionName = Db_Manager::DEFAULT_TRANSACTION
    ) {
        return new self($intServiceDefinitionId, $strTransactionName);
    }

    /**
     * Takes care of calls to getters of DAO object
     *
     * @param string $strMethod Method name
     * @param array  $arrArgs   Arguments
     *
     * @return unknown
     */
    public function __call($strMethod, $arrArgs)
    {
        if (preg_match('/^(get)/', $strMethod)) {
            return call_user_func_array(array($this->_objDao, $strMethod), $arrArgs);
        }

        throw new BadMethodCallException("Method does not exist: ".get_class($this).'::'.$strMethod);
    }

    /**
     * Returns true if service definition belongs to value family products
     *
     * @return boolean
     */
    public function isValueFamilyProduct()
    {
        $valueFamilies = array(self::FAMILY_VALUE, self::FAMILY_VALUE_SOLUS, self::FAMILY_VALUE_DUALPLAY);
        return in_array($this->_objDao->getProductFamily(), $valueFamilies);
    }

    /**
     * Returns true if the service is a business account
     *
     * @return boolean
    */
    public function isBusiness()
    {
        return self::BUSINESS_ACCOUNT== $this->_objDao->getType();
    }

    /**
     * Returns true if the service is a business account
     *
     * @return boolean
    */
    public function isResidential()
    {
        return self::RESIDENTIAL_ACCOUNT == $this->_objDao->getType();
    }

    /**
     * Returns true if service definition belongs to BPR09 family products
     *
     * @return boolean
     */
    public function isBpr09FamilyProduct()
    {
        return self::FAMILY_BPR09 == $this->_objDao->getProductFamily();
    }

    /**
     * Returns true if service definition belongs to the John Lewis family
     *
     * @return boolean
     */
    public function isJohnLewisFamilyProduct()
    {
        return $this->_objDao->getProductFamily() == self::FAMILY_JOHNLEWIS;
    }

    /**
     * Returns true if service definition belongs to the Res2012 family
     *
     * @return boolean
     */
    public function isRes2012FamilyProduct()
    {
        return $this->_objDao->getProductFamily() == self::FAMILY_RES2012;
    }

    /**
     * Returns true if service definition is an ADSL product
     *
     * @return boolean
     */
    public function isAdsl()
    {
        return '1' == $this->_objDao->getBolAdsl();
    }

    /**
     * Returns true if service definition belongs to Payment Services family products
     *
     * @return boolean
     */
    public function isPaymentServicesFamilyProduct()
    {
        return self::FAMILY_PAYMENT_SERVICES == $this->_objDao->getProductFamily();
    }

    /**
     * Returns true if the isp is partner
     *
     * @return boolean
     */
    public function isPartnerIsp()
    {
        return self::PARTNER_ISP == $this->_objDao->getIsp();
    }

    /**
     * Checks if the customer is John Lewis
     *
     * @return bool
     */
    public function isJohnLewis()
    {
        return in_array($this->getIsp(), self::$johnLewisVisps);
    }

    /**
     * Gets the ISP
     *
     * @return string
     */
    private function getIsp()
    {
        return $this->_objDao->getIsp();
    }
}
