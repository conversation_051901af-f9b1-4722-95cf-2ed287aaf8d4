<?php
require_once (COMMON_DATE_API);
require_once '/local/data/mis/database/application_apis/EmailHandler/EmailHandler.class.php';

define ('WLR_ORDERTYPE_ERROR',             -1);
define ('WLR_ORDERTYPE_UNKNOWN',            0);
define ('WLR_ORDERTYPE_DEBT_MGMT',          3);
define ('WLR_ORDERTYPE_DEBT_MGMT_START',    4);
define ('WLR_ORDERTYPE_DEBT_MGMT_STOP',     5);
define ('WLR_ORDERTYPE_CANCEL_OTHER',       6);
define ('WLR_ORDERTYPE_COMMON_CEASE',       7);

class Wlr_OrderType
{
    // members set by the constructor
    var $intServiceID;
    var $strPostcode;
    var $strCliNumber;
    var $intComponentID;
    var $bolIsBusiness;

    // members set by either methods or subclasses
    var $dteAppointmentDate;
    var $intOrderType;
    var $intOrderID;
    var $intLineRentProductComponentInstanceID;

///////////////////////////////////////////////////////////////////////////////
//
// Constructor
//

    function Wlr_OrderType($intServiceID, $strPostcode, $strCliNumber, $intComponentID, $bolIsBusiness = false)
    {
        $this->intServiceID = $intServiceID;
        $this->strPostcode = $strPostcode;
        $this->strCliNumber = $strCliNumber;
        $this->intComponentID = $intComponentID;
        $this->bolIsBusiness = $bolIsBusiness;

        $this->intOrderType = WLR_ORDERTYPE_UNKNOWN;
        $this->dteAppointmentDate = '';
        $this->intOrderID = 0;
        $this->intLineRentProductComponentInstanceID = 0;
    }

////////////////////////////////////////////////////////////////////////////////
//
// Public methods
//

    function GetServiceID()
    {
        return $this->intServiceID;
    }

////////////////////////////////////////////////////////////////////////////////

    function GetPostcode()
    {
        return $this->strPostcode;
    }

////////////////////////////////////////////////////////////////////////////////

    function GetCLI()
    {
        return $this->strCliNumber;
    }

////////////////////////////////////////////////////////////////////////////////

    function SetOrderID($intOrderID)
    {
        $this->intOrderID = $intOrderID;
    }

////////////////////////////////////////////////////////////////////////////////

    function GetOrderID()
    {
        return $this->intOrderID;
    }

////////////////////////////////////////////////////////////////////////////////

    function IsBusiness()
    {
        return $this->bolIsBusiness;
    }
////////////////////////////////////////////////////////////////////////////////

    /*
        --------------------
        SetAppointmentDate()
        --------------------

        Sets the appointment date to be N days in the future or past from a given
        start date (expressed as a Unix timestamp)

        $intWorkingDaysInTheFuture >= 0 --> N days in the future
        $intWorkingDaysInTheFuture <  0 --> N days in the past

        This working day offset is worked from $uxtOptionalStartOffsetDate, which
        if not specified will cause the code to default to use *today*

        The extension to allow "negative working days" was for the sake of Change
        Order requests, as depending on feature selection we need to be able to select
        N working days *before* their billing date. Thus, for this, $intWorkingDaysInTheFuture
        will be negative and $uxtOptionalStartOffsetDate will be their next billing date.
    */

    function SetAppointmentDate($intWorkingDaysInTheFuture, $uxtOptionalStartOffsetDate = 0)
    {
        $this->dteAppointmentDate = date("Y-m-d", $this->GetNextWorkingDate($uxtOptionalStartOffsetDate, $intWorkingDaysInTheFuture));
    }

    /**
     * Wrapper function to enble the use of Mock Objects in PHPUnit
     *
     * @param $uxtOptionalStartOffsetDate timestamp
     * @param $intWorkingDaysInTheFuture integer
     * @returns timestamp
     */
    function GetNextWorkingDate($uxtOptionalStartOffsetDate, $intWorkingDaysInTheFuture)
    {
        return GetNextWorkingDate($uxtOptionalStartOffsetDate, $intWorkingDaysInTheFuture);
    }
///////////////////////////////////////////////////////////////////////////////

    /*
        ---------------------
        SetAppointmentDate2()
        ---------------------

        Sets the appointment date to a specific date (expressed as Unix timestamp)
    */

    function SetAppointmentDate2($uxtAppointmentDate)
    {
        $this->dteAppointmentDate = date("Y-m-d", $uxtAppointmentDate);
    }

///////////////////////////////////////////////////////////////////////////////

    function GetAppointmentDate()
    {
        return $this->dteAppointmentDate;
    }

///////////////////////////////////////////////////////////////////////////////

    function GetOrderType()
    {
        return $this->intOrderType;
    }

///////////////////////////////////////////////////////////////////////////////

    /*
        ---------------------------------------
        SetLineRentProductComponentInstanceID()
        ---------------------------------------

        A semi-low-level sort of method that is required because once a Wlr_OrderType*
        object is processed by the order submission system, it needs to always (almost)
        have knowledge about the Line Rent product component instance.
    */

    function SetLineRentProductComponentInstanceID($intLineRentProductComponentInstanceID)
    {
        $this->intLineRentProductComponentInstanceID = $intLineRentProductComponentInstanceID;
    }

///////////////////////////////////////////////////////////////////////////////

    function GetLineRentProductComponentInstanceID()
    {
        return $this->intLineRentProductComponentInstanceID;
    }

///////////////////////////////////////////////////////////////////////////////

    function GetComponentID()
    {
        return $this->intComponentID;
    }

//////////////////////////////////////////////////////////////////////////////

    /*
        -----------------------------------------
        CalculateAppointmentDateBasedOnFeatures()
        -----------------------------------------

        On error, returns false.

        On success, it returns back the appointment date as a Unix timestamp

        The decisions are purely based on _added_ or _removed_ features. (not those already there, etc)
    */

    function CalculateAppointmentDateBasedOnFeatures()
    {
        $arrDateInfo = getdate();
        $uxtToday = $arrDateInfo[0];

        $arrAddedAndRemovedFeatures = $this->GetSelectedFeatures();

        if (sizeof($arrAddedAndRemovedFeatures) == 0)
        {

            // BBR Refresh 2008 - if this is a renumber order, then we don't necessarily have any features to change,
            // so in this case return a blank timestamp (as we will never use it in an email)
            if (FALSE !== Wlr_RenumberHelper::isPendingRenumberOrderByComponentId($this->GetComponentID())) {
                return GetNextWorkingDate($uxtToday, 3);
            }

            // no features = can't make any judgements. :-S
            return false;
        }


        // Determine if we've added/removed any features, and if so, work out
        // the lead time accordingly.

        // This array should be ordered in greatest lead time first -> least leadtime last
        // at all times, unless the selection algorithm is changed.

        $arrLeadTimes = array(
           array('featureHandle' => 'WlrVoiceMail',       'before1500' => 0, 'after1500' => 1),
           array('featureHandle' => 'WlrVoiceMailExtra',  'before1500' => 0, 'after1500' => 1),
           array('featureHandle' => 'WlrVoiceMailPlus',   'before1500' => 0, 'after1500' => 1),
           array('featureHandle' => 'WlrCallerDisplay',   'before1500' => 0, 'after1500' => 1),
           array('featureHandle' => 'WlrCallWaiting',     'before1500' => 0, 'after1500' => 1),
           array('featureHandle' => 'WlrCallDiversion',   'before1500' => 0, 'after1500' => 1),
           array('featureHandle' => 'WlrReminderCall',    'before1500' => 0, 'after1500' => 1),
           array('featureHandle' => 'WlrRingBack',        'before1500' => 0, 'after1500' => 1),
           array('featureHandle' => 'WlrAnonymousCall',   'before1500' => 0, 'after1500' => 1),
           array('featureHandle' => 'Wlr3WayCalling',     'before1500' => 0, 'after1500' => 1),
           array('featureHandle' => 'WlrCallProtect',     'before1500' => 0, 'after1500' => 1),
           // additional features for MAAF project
           array('featureHandle' => 'Wlr1471Extra',          'before1500' => 0, 'after1500' => 1),
           array('featureHandle' => 'WlrChooseToRefuse',     'before1500' => 0, 'after1500' => 1),
           array('featureHandle' => 'WlrSmartDivert',        'before1500' => 0, 'after1500' => 1),
           array('featureHandle' => 'WlrCallBarringPRS',     'before1500' => 0, 'after1500' => 1),
           array('featureHandle' => 'WlrCallBarringPRS_IC',  'before1500' => 0, 'after1500' => 1),
           // BBR Refresh - add call barring OCB as a feature
           array('featureHandle' => 'WlrCallBarringOCB',     'before1500' => 0, 'after1500' => 1),
           // BPR - Care level
           array('featureHandle' => 'WlrCareLevel1',  'before1500' => 0, 'after1500' => 1),
           array('featureHandle' => 'WlrCareLevel2',  'before1500' => 0, 'after1500' => 1),
           array('featureHandle' => 'WlrCareLevel3',  'before1500' => 0, 'after1500' => 1)
        );

        // The problem that we have here I think is that we're prone to problems when the order is
        // to be submitted *near* 3pm. Wherever we set the appointment date, (here, or even later)
        // it doesn't stop the order from actually hitting BTs system *after* 3pm when actually
        // it was a few moments BEFORE 3pm when the order was prepared, thus affecting the lead time.

        // From previous live runs of the order submission script, it took 3m11sec for 284 orders.
        // I suspect that if the order submission script is set to run in cron at half-past the
        // hour, this will bridge over the 3pm boundary nicely. (it does appear to take it a while
        // to build up the list of orders, then a seperate period of time to communicate with BT)

        // go through each of the lead time entries - longest first.
        foreach ($arrLeadTimes as $thisLeadTime)
        {
            // for each lead time, see if they have that feature in an added/removed state.
            foreach ($arrAddedAndRemovedFeatures as $thisFeature)
            {
                if ($thisFeature['strFeatureHandle'] == $thisLeadTime['featureHandle'])
                {
                    return GetNextWorkingDate($uxtToday, $thisLeadTime[ ($arrDateInfo['hours'] < 15) ? 'before1500' : 'after1500' ]);
                }
            }
        }

        throw new Exception(__CLASS__ . __LINE__." Unable to determine appointment date based on call features - possible bug or missing functionality." );
    }



    /**
     * Helper emailing method used by classes that extend from this one, those being:
     *
     *  - Wlr_OrderType_CancelOther
     *  - Wlr_OrderType_CommonCease (neither is this one - but used to be)
     *  - Wlr_OrderType_DebtManagement
     *
     * @param $strTemplate email template filename, be it either newfangled
     *                     EmailHandler material or legacy mailer_functions.inc stuff.
     * @param $arrData - arbitrary data used to populate fields within the email content.
     * @param $bolSendOldSchool - is the email we're sending to use the EmailHandler or
     *                            the mailer_functions.inc code.
     * @return boolean Was an email sent successfully?
     **/

    protected function sendEmail($strTemplate, $arrData=array(), $bolSendOldSchool=false)
    {
        if (!$bolSendOldSchool)
        {
            $bolSent = EmailHandler::sendEmail($this->getServiceID(), $strTemplate, $arrData);
        }
        else
        {
            $bolSent = mailer_send_customer_mail(
                    $this->GetServiceID(),
                    $strTemplate,
                    $arrData,
                    true,
                    true, // actually sends the email
                    false);
        }
        return $bolSent;
    }



    /**
     * Select the product type that may be appropriate for the order
     * which extends from this class.
     *
     * It can either be a business or residential product type.
     *
     * @access public
     * @return string
     **/

    public function getProductType()
    {
        if ($this->IsBusiness()) {

            return 'Wholesale Access Business Single Line';
        }


        return 'Wholesale Access Residential Single Line';
    }
}
