<?php

	/////////////////////////////////////////////////////////////////////
	// File:     config-cbc-payg-access.inc
	// Purpose:  Access mini-library for configuring the customers CBC PAYG component
	/////////////////////////////////////////////////////////////////////

	/////////////////////////////////////////////////////////////////////
	// Functions
	//
	// Write functions
	// ---------------
	//
	// config_cbc_flex_restrict
	//
	// Read functions
	// --------------
	//
	// Modify Functions
	// ----------------
	//
	// Delete functions
	// ----------------
	//
	/////////////////////////////////////////////////////////////////////

	/////////////////////////////////////////////////////////////////////
	// Requires

	require_once '/local/data/mis/database/database_libraries/product-access.inc';
	require_once '/local/data/mis/database/database_libraries/userdata-access.inc';
	require_once '/local/data/mis/database/database_libraries/cbc-access.inc';
	require_once '/local/data/mis/database/database_libraries/view-my-broadband-usage-access.inc';
	require_once '/local/data/mis/database/database_libraries/programme-tool-access.inc';
	require_once '/local/data/mis/database/database_libraries/payment-limit-access.inc';
	require_once '/local/data/mis/database/database_libraries/mailer_functions.inc';
	require_once '/local/data/mis/database/database_libraries/components/component-defines.inc';

	require_once '/local/data/mis/common_library_functions/common_application_apis/SoapSession/SoapSession.class.php';
	require_once '/local/data/mis/common_library_functions/common_application_apis/SoapSession/EllacoyaManager.class.php';

	require_once '/local/data/mis/database/application_apis/Cbc/PaygBwMaintenance.class.php';


	// Requires
	/////////////////////////////////////////////////////////////////////

	/////////////////////////////////////////////////////////////////////
	// Data

	$global_component_configurators[COMPONENT_CBC_PAYG_BANDWIDTH]      = 'ConfigCBCPAYGConfigurator';
	$global_component_configurators[COMPONENT_CBC_PAYG_CALENDAR_MONTH] = 'ConfigCBCPAYGConfigurator';
	$global_component_configurators[COMPONENT_CBC_PAYG_END_OF_MONTH] = 'ConfigCBCPAYGConfigurator';

	// Data
	/////////////////////////////////////////////////////////////////////

	// Hack to insert the component configurator array into PHP5's global scope
	// if it's not already there
	if(!isset($GLOBALS['global_component_configurators']))
	{
		$GLOBALS['global_component_configurators'] = $global_component_configurators;
	}
	else
	{
		foreach($global_component_configurators as $intIndex => $strConfigurator)
		{
			$GLOBALS['global_component_configurators'][$intIndex] = $strConfigurator;
		}
	}

	/////////////////////////////////////////////////////////////////////
	// Library functions

	/////////////////////////////////////////////////////////////
	//
	//
	function ConfigCBCPAYGInsert($intComponentID, $intTotalBandwidthBytes, $intChargedBandwidthBytes, $floAdditionalCost)
	{
		$dbhConnection = get_named_connection_with_db('userdata');

		$strQuery = 'INSERT INTO tblConfigCBCPAYG ' .
		            '           (intComponentID, ' .
		            '            intTotalBandwidthBytes, ' .
		            '            intChargedBandwidthBytes, ' .
		            '            decAdditionalCost, ' .
		            '            vchDBSrc) ' .
		            "    VALUES ('$intComponentID', " .
		            "            '$intTotalBandwidthBytes', " .
		            "            '$intChargedBandwidthBytes', " .
		            "            '$floAdditionalCost', " .
		            '            "CBl")';

		PrimitivesQueryOrExit($strQuery, $dbhConnection);

        $intConfigID = PrimitivesInsertIdGet($dbhConnection);

		// Update the component with the new config id
		$strQuery = 'UPDATE components ' .
		            "   SET config_id = '$intConfigID', " .
		            '       db_src = "CBl" ' .
		            " WHERE component_id = '$intComponentID'";

		PrimitivesQueryOrExit($strQuery, $dbhConnection);

		$intAffectedRows = PrimitivesAffectedRowsGet($dbhConnection);

		// Update the components description to reflect the included bandwidth
		ConfigCBCPAYGUpdateDescription($intComponentID, $intChargedBandwidthBytes);

		return $intConfigID;

	} // function ConfigInsertCBCPAYG

	function ConfigCBCPAYGUpdate($intComponentID, $intTotalBandwidthBytes, $intChargedBandwidthBytes, $floAdditionalCost)
	{
		$dbhConnection = get_named_connection_with_db('userdata');

		$strQuery = 'UPDATE tblConfigCBCPAYG ' .
		            "   SET intTotalBandwidthBytes = '$intTotalBandwidthBytes', " .
		            "       intChargedBandwidthBytes = '$intChargedBandwidthBytes', " .
		            "       decAdditionalCost = '$floAdditionalCost', " .
		            '       vchDBSrc = "CBl" ' .
		            " WHERE intComponentID = '$intComponentID'";

		PrimitivesQueryOrExit($strQuery, $dbhConnection);

		$intAffectedRows = PrimitivesAffectedRowsGet($dbhConnection);

		// Update the components description to reflect the included bandwidth
		ConfigCBCPAYGUpdateDescription($intComponentID, $intChargedBandwidthBytes);

		if ($intAffectedRows > 0)
		{
			return true;
		}

		return false;

	} // function ConfigUpdateCBCPAYG

	function ConfigCBCPAYGGetConfigID($intComponentID)
	{
		$dbhConnection = get_named_connection_with_db('userdata');

		$strQuery = 'SELECT intConfigID ' .
		            '  FROM tblConfigCBCPAYG ' .
		            " WHERE intComponentID = '$intComponentID'";

		$resConfigID = PrimitivesQueryOrExit($strQuery, $dbhConnection);

		$intConfigID = PrimitivesResultGet($resConfigID, 'intConfigID');

		return $intConfigID;

	} // function ConfigCBCPAYGGetConfigID

	function ConfigCBCPAYGUpdateDescription($intComponentID, $intBandwidthBytes)
	{
		$dbhConnection = get_named_connection_with_db('userdata');

		$intSteppingReal = getSteppingByComponentID($intComponentID, false);

		if ($intBandwidthBytes > 0)
		{
			$intBandwidthUsage = ($intSteppingReal * floor($intBandwidthBytes / $intSteppingReal)) + $intSteppingReal;
		}
		else
		{
			$intBandwidthUsage = 0;
		}

		$strDescription = addslashes(GetCustomerFriendlyBytes($intBandwidthUsage) . ' Extra Bandwidth');

		$strQuery = 'UPDATE components ' .
		            "   SET description = '$strDescription', " .
		            '       db_src = "CBl" ' .
		            " WHERE component_id = '$intComponentID'";

		PrimitivesQueryOrExit($strQuery, $dbhConnection);

		$intAffectedRows = PrimitivesAffectedRowsGet($dbhConnection);

		if ($intAffectedRows > 0)
		{
			return true;
		}

		return false;

	} // function ConfigCBCPAYGUpdateDescription

	function ConfigCBCPAYGSet($intComponentID, $intTotalBandwidthBytes, $intChargedBandwidthBytes, $floAdditionalCost)
	{
		// Check whether a record has been already added for this component
		$intCBCPAYGConfigID = ConfigCBCPAYGGetConfigID($intComponentID);

		if ($intCBCPAYGConfigID > 0)
		{
			$bolUpdated = ConfigCBCPAYGUpdate($intComponentID, $intTotalBandwidthBytes, $intChargedBandwidthBytes, $floAdditionalCost);

			if ($bolUpdated == true)
			{
				return true;
			}
		}
		else
		{
			$intConfigID = ConfigCBCPAYGInsert($intComponentID, $intTotalBandwidthBytes, $intChargedBandwidthBytes, $floAdditionalCost);

			if ($intConfigID > 0)
			{
				return true;
			}
		}

		return false;

	} // function ConfigCBCPAYGSet

	function ConfigCBCPAYGDelete($intComponentID)
	{
		$dbhConnection = get_named_connection_with_db('userdata');

		// Delete the config entry for this component
		$strQuery = 'DELETE FROM tblConfigCBCPAYG ' .
		            " WHERE intComponentID = '$intComponentID'";

		PrimitivesQueryOrExit($strQuery, $dbhConnection);

		$intAffectedRows = PrimitivesAffectedRowsGet($dbhConnection);

		// If row was removed then upadte the component details to reflect it
		if ($intAffectedRows > 0)
		{
			$strQuery = 'UPDATE components ' .
			            '   SET config_id = "-1", ' .
			            '       description = "" ' .
			            " WHERE component_id = '$intComponentID'";

			PrimitivesQueryOrExit($strQuery, $dbhConnection);

			$intAffectedRows = PrimitivesAffectedRowsGet($dbhConnection);

			if ($intAffectedRows > 0)
			{
				return true;
			}
		}

		return false;

	} // function ConfigCBCPAYGDelete

	function ConfigCBCPAYGActivate($intComponentID)
	{
		$arrComponent = userdata_component_get($intComponentID);

		switch ($arrComponent['status'])
		{
			case 'unconfigured' :

				$bolSet = ConfigCBCPAYGSet($intComponentID, 0, 0, 0);

				// Activate the component
				if ($bolSet == true)
				{
					userdata_component_set_status($intComponentID, 'active');

					return true;
				}

				break;

			default :

				// Do nothing
				break;
		}

		return false;

	} // function ConfigCBCPAYGActivate

	function ConfigCBCPAYGValidateUsage($intServiceDefinitionID, $intTotalBandwidthBytes, $intChargedBandwidthBytes, $intNumDays=1)
	{
		$dbhConnection = get_named_connection_with_db('product');

		$strQuery = 'SELECT intMaximumDailyBandwidth ' .
		            '  FROM adsl_product ' .
		            " WHERE service_definition_id = '$intServiceDefinitionID'";

		$resMaximumDailyBandwidth = PrimitivesQueryOrExit($strQuery, $dbhConnection);

        $intMaximumDailyBandwidth = PrimitivesResultGet($resMaximumDailyBandwidth, 'intMaximumDailyBandwidth');

		if (isset($intMaximumDailyBandwidth) && $intMaximumDailyBandwidth > 0)
		{
			$intMaximumPeriodBandwidth = $intMaximumDailyBandwidth * $intNumDays;

			if ($intTotalBandwidthBytes <= $intMaximumPeriodBandwidth && $intChargedBandwidthBytes <= $intMaximumPeriodBandwidth)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		return false;

	} // function ConfigCBCPAYGValidateUsage

	function ConfigCBCPAYGGetDetails($intComponentID)
	{
		$dbhConnection = get_named_connection_with_db('userdata');

		$strQuery = 'SELECT intConfigID, intTotalBandwidthBytes, intChargedBandwidthBytes, decAdditionalCost as floAdditionalCost ' .
		            '  FROM tblConfigCBCPAYG ' .
		            " WHERE intComponentID = '$intComponentID'";

		$resCBCPAYGDetails = PrimitivesQueryOrExit($strQuery, $dbhConnection);

		$arrCBCPAYGDetails = PrimitivesResultGet($resCBCPAYGDetails);

		return $arrCBCPAYGDetails;

	} // function ConfigCBCPAYGGetDetails


	function ConfigCBCPAYGCalculateAdditionalCost($intServiceDefinitionID, $intChargedBandwidthBytes)
	{
		if (isset($intChargedBandwidthBytes) && $intChargedBandwidthBytes > 0)
		{
			$intStepping = getSteppingByServiceDefinitionID($intServiceDefinitionID);

			// Get cost settings
			$arrCBCSpeedDetails = GetCBCServiceDefinitionSpeedDetails($intServiceDefinitionID);

			if (isset($arrCBCSpeedDetails) && is_array($arrCBCSpeedDetails) && sizeof($arrCBCSpeedDetails) > 0)
			{
				// Round the chargeable bandwidth to next step up
				$intChargedBandwidthBytes = ceil((($arrCBCSpeedDetails['intCBCExtraBandwidthStepBytes']
				                                  * floor($intChargedBandwidthBytes / $arrCBCSpeedDetails['intCBCExtraBandwidthStepBytes']))
				                                 + $arrCBCSpeedDetails['intCBCExtraBandwidthStepBytes'])
				                                / $intStepping);

				$floAdditionalCost = round(($arrCBCSpeedDetails['floCBCExtraGigabytePrice'] * $intChargedBandwidthBytes) + 0.001, 2);

				if (isset($arrCBCSpeedDetails['floCBCCapPrice']) && $arrCBCSpeedDetails['floCBCCapPrice'] > 0 && $floAdditionalCost > $arrCBCSpeedDetails['floCBCCapPrice'])
				{
					return $arrCBCSpeedDetails['floCBCCapPrice'];
				}

				$floAdditionalCost = ($floAdditionalCost < 0) ? 0 : $floAdditionalCost;

				return $floAdditionalCost;
			}
			else
			{
				return false;
			}
		}

		return 0;

	} // function ConfigCBCPAYGCalculateAdditionalCost

	function InsertCBCPAYGServiceLog($intServiceID, $intPAYGBandwidthBytes)
	{
		$dbhConnection = get_named_connection_with_db('userdata');

		$strQuery = 'INSERT INTO tblCBCPAYGServiceLog ' .
		            '           (intServiceID, ' .
		            '            intPAYGBandwidthBytes, ' .
		            '            dtmAdded) ' .
		            "    VALUES ('$intServiceID', " .
		            "            '$intPAYGBandwidthBytes', " .
		            '            NOW())';

		PrimitivesQueryOrExit($strQuery, $dbhConnection);

		$intCBCPAYGServiceLogID = PrimitivesInsertIdGet($dbhConnection);

		Cbc_PaygBwMaintenance::autoSetCbcBandwidthThreshold($intServiceID);

		return $intCBCPAYGServiceLogID;

	} // function InsertCBCPAYGEmailLog

	function ConfigCBCPAYGRefresh($intComponentID, $bolForceRefresh = false)
	{
		$arrComponent = userdata_component_get($intComponentID);
		if(!$arrComponent) return FALSE;

		$objProcessor = new Cbc_PaygBwMaintenance();
		$objProcessor->run($arrComponent['service_id'], $bolForceRefresh, TRUE);

		$arrErrors = $objProcessor->getErrors();

		if($arrErrors) {

			$strMessage = "The following errors when refreshing CBC PAYG component $intComponentID:\n\n" .
			               implode("\n", $arrErrors);

			$intProblemId = pt_raise_autoproblem('CBC Billing Problem',
			                                     'AUTOPROBLEM: Error refreshing CBC PAYG component',
			                                     $strMessage, $strMessage);

			return FALSE;

		} else {

			return TRUE;
		}

	} // function ConfigCBCPAYGRefresh

	function ConfigCBCPAYGDestroy($intComponentID)
	{
		$arrComponent = userdata_component_get($intComponentID);

		switch ($arrComponent['status'])
		{
			case 'active' :

				// Remove component settings and clear component fields
				$bolDelete = ConfigCBCPAYGDelete($intComponentID);

				if ($bolDelete == true)
				{
					userdata_component_set_status($intComponentID, 'destroyed');

					return true;
				}

				break;

			case 'unconfigured' :

				userdata_component_set_status($intComponentID, 'destroyed');

				return true;

				break;

			default :

				// Do nothing
				break;
		}

		return false;

	} // function ConfigCBCPAYGDestroy

	/////////////////////////////////////////////////////////////
	// Function:   ConfigCBCPAYGConfigurator
	// Purpose:    Auto-matically performs actions
	// Arguments:  $intComponentID
	//             $strAction
	// Returns:    bool
	/////////////////////////////////////////////////////////////
	function ConfigCBCPAYGConfigurator($intComponentID, $strAction)
	{
		$bolConfigured = false;

		switch ($strAction)
		{
			case 'auto_enable' :

				$bolConfigured = ConfigCBCPAYGActivate($intComponentID);

				break;

			case 'auto_configure' :

				$arrComponent = userdata_component_get($intComponentID);

				if ($arrComponent['status'] == 'unconfigured')
				{
					$bolConfigured = ConfigCBCPAYGActivate($intComponentID);
				}
				elseif ($arrComponent['status'] == 'active')
				{
					$bolConfigured = ConfigCBCPAYGRefresh($intComponentID);
				}

				break;

			case 'auto_refresh' :

				$bolConfigured = ConfigCBCPAYGRefresh($intComponentID);

				break;

			case 'auto_destroy' :

				$bolConfigured = ConfigCBCPAYGDestroy($intComponentID);

				break;

			case 'auto_disable' :
			default :

				// Do nothing
				break;
		}

		if ($bolConfigured == true)
		{
			return true;
		}

		return false;

    } // function ConfigCBCPAYGConfigurator

	// Library functions
	/////////////////////////////////////////////////////////////////////

	function checkLimitExists($intServiceID, $intBytes) {
		$arrService = userdata_service_get($intServiceID);
		$dtmNextBillingDate = $arrService ['next_invoice'];

		//failed billing
		if(strtotime($dtmNextBillingDate) < strtotime(date('Y-m-d'))) {
			return true;
		}

		$dtmPreviousCBCBillingDate = strtotime($dtmNextBillingDate - "1 month");
		$dbhConnection = get_named_connection_with_db('userdata');
		$strQuery = "SELECT count(*) AS intNum  FROM tblCBCBandwidthUsage WHERE intServiceID = '".$intServiceID."' ";
		$strQuery.= "AND dtmDate > '".$dtmPreviousCBCBillingDate."' AND intGigaBytesUsed = ".$intBytes;


		$resResults = PrimitivesQueryOrExit($strQuery, $dbhConnection);
		$arrResults = PrimitivesResultGet($resResults);

		if( !($arrResults['intNum'] > 0)) {
			$strQuery = "INSERT INTO tblCBCBandwidthUsage (intServiceID, dtmDate, intGigaBytesUsed) ";
			$strQuery.= "VALUES ('".$intServiceID."', '".date('Y-m-d')."', '".$intBytes."')";
			PrimitivesQueryOrExit($strQuery, $dbhConnection);
			return false;
		}
		return true;
	}
?>
