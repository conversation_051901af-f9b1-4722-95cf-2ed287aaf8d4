<?php

	/////////////////////////////////////////////////////////////////////
	// File:     config-frontpage-access.inc
	// Purpose:  Access mini-library for Frontpage
	/////////////////////////////////////////////////////////////////////

	/////////////////////////////////////////////////////////////////////
	// Data

	$global_component_configurators["9"]  = "config_frontpage_configurator"; // plus.net
	$global_component_configurators["27"] = "config_frontpage_configurator"; // force9
	$global_component_configurators["49"] = "config_frontpage_configurator"; // searchpro
	$global_component_configurators["82"] = "config_frontpage_configurator"; // freeonline

	// Data
	/////////////////////////////////////////////////////////////////////

	// Hack to insert the component configurator array into PHP5's global scope
	// if it's not already there
	if(!isset($GLOBALS['global_component_configurators']))
	{
		$GLOBALS['global_component_configurators'] = $global_component_configurators;
	}
	else
	{
		foreach($global_component_configurators as $intIndex => $strConfigurator)
		{
			$GLOBALS['global_component_configurators'][$intIndex] = $strConfigurator;
		}
	}

	/////////////////////////////////////////////////////////////
	// Function:  config_frontpage_auto_configure
	// Purpose:   'unconfigured' -> 'queued-activate' state
	//	    transition handler for auto-configuration
	// Arguments: $component_id
	/////////////////////////////////////////////////////////////

	function config_frontpage_auto_configure ($component_id) {

		// Do nothing - user must request activation

	}


	/////////////////////////////////////////////////////////////
	// Function:  config_frontpage_auto_disable
	// Purpose:   Queue for backend disable
	// Arguments: $component_id
	/////////////////////////////////////////////////////////////

	function config_frontpage_auto_disable ($component_id) {

		$component = userdata_component_get ($component_id);

		switch ($component["status"]) {

		    case "active":
		    case "queued-reactivate":
			userdata_component_set_status ($component_id, "queued-deactivate");
			break;

		    default:
			// Anything else none of our business
			break;

		}

	}


	/////////////////////////////////////////////////////////////
	// Function:  config_frontpage_auto_enable
	// Purpose:   Queue for backend reenable
	// Arguments: $component_id
	/////////////////////////////////////////////////////////////

	function config_frontpage_auto_enable ($component_id) {

		$component = userdata_component_get ($component_id);

		switch ($component["status"]) {

		    case "queued-deactivate":
		    case "deactive":
			userdata_component_set_status ($component_id, "queued-reactivate");
			break;

		    default:
			// Anything else none of our business
			break;

		}

	}


	/////////////////////////////////////////////////////////////
	// Function:  config_frontpage_auto_refresh
	// Purpose:   Queue for password update
	// Arguments: $component_id
	/////////////////////////////////////////////////////////////

	function config_frontpage_auto_refresh ($component_id) {

		$component = userdata_component_get ($component_id);

		switch ($component["status"]) {

		    case "active":
			userdata_component_set_status ($component_id, "queued-reactivate");
			break;

		    default:
			// Anything else none of our business
			break;

		}

	}


	/////////////////////////////////////////////////////////////
	// Function:  config_frontpage_auto_destroy
	// Purpose:   * -> 'queued-destroy' state
	//	    transition handler for auto-destruction
	// Arguments: $component_id
	/////////////////////////////////////////////////////////////

	function config_frontpage_auto_destroy ($component_id)
	{
		userdata_component_set_status ($component_id, "destroyed");
	}


	function config_frontpage_configurator ($component_id, $action) {

		switch ($action) {

		    case "auto_configure":
			config_frontpage_auto_configure ($component_id);
			break;

		    case "auto_disable":
			config_frontpage_auto_disable ($component_id);
			break;

		    case "auto_enable":
			config_frontpage_auto_enable ($component_id);
			break;

		    case "auto_refresh":
			config_frontpage_auto_refresh ($component_id);
			break;

		    case "auto_destroy":
			config_frontpage_auto_destroy ($component_id);
			break;

		    default:
			break;

		}

	}

	/////////////////////////////////////////////////////////////////
	// Function : config_frontpage_find_missing_qactivate_components
	// Purpose  : Find all components which are frontpage components
	// 	      but do not have a matching recod in config_frontpage
	//	      which are queued activated or reactivated
	// Arguments: None
	// Returns  : Array of all frontpage component_ids
	/////////////////////////////////////////////////////////////////
 
	function config_frontpage_find_missing_qactivate_components ($array_frontpage_components)
	{
		$connection = get_named_connection('userdata');
 
		for ($count = 0; $count < count($array_frontpage_components); ++$count)
		{
			$type_id = addslashes($array_frontpage_components[$count]);
 
			if (($count+1) == count($array_frontpage_components))
			{
				$type_list = $type_list . $type_id;
			}
			else
			{
				$type_list = $type_list . $type_id . ",";
			}
		}
 
		$query = "SELECT components.component_id, config_frontpage.frontpage_id
			    FROM components
			    LEFT JOIN config_frontpage
			      ON components.config_id      = config_frontpage.frontpage_id
			   WHERE components.component_type_id in ($type_list)
			     AND components.status	    in ('queued-activate', 'queued-reactivate')
			  HAVING config_frontpage.frontpage_id is null";
 
		$result = mysql_query ($query, $connection)
				   or report_error (__FILE__, __LINE__, mysql_error($connection));
 
		$missing_frontpage_components = array();
 
		while ($row = mysql_fetch_row($result))
		{
			$missing_frontpage_components[] = $row[0];
		}
 
		mysql_free_result($result);
 
		return $missing_frontpage_components;
	}

	/////////////////////////////////////////////////////////////////
	// Function : config_frontpage_find_missing_qdestroy_components
	// Purpose  : Find all components which are frontpage components
	// 	      but do not have a matching recod in config_frontpage
	//	      which are queued to be destroyes
	// Arguments: array of component types to look for
	// Returns  : Array of all frontpage component_ids
	/////////////////////////////////////////////////////////////////
 
	function config_frontpage_find_missing_qdestroy_components ($array_frontpage_components)
	{
		$connection = get_named_connection('userdata');
 
		for ($count = 0; $count < count($array_frontpage_components); ++$count)
		{
			$type_id = addslashes($array_frontpage_components[$count]);
 
			if (($count+1) == count($array_frontpage_components))
			{
				$type_list = $type_list . $type_id;
			}
			else
			{
				$type_list = $type_list . $type_id . ",";
			}
		}
 
		$query = "SELECT components.component_id, config_frontpage.frontpage_id
			    FROM components
			    LEFT JOIN config_frontpage
			      ON components.config_id	  = config_frontpage.frontpage_id
			   WHERE components.component_type_id in ($type_list)
			     AND components.status	     = 'queued-destroy'
			  HAVING config_frontpage.frontpage_id is null";
 
		$result = mysql_query ($query, $connection)
				   or report_error (__FILE__, __LINE__, mysql_error($connection));
 
		$missing_frontpage_components = array();
 
		while ($row = mysql_fetch_row($result))
		{
			$missing_frontpage_components[] = $row[0];
		}
 
		mysql_free_result($result);
 
		return $missing_frontpage_components;
	}

	/////////////////////////////////////////////////////////////////
	// Function : config_frontpage_find_by_username_isp
	// Purpose  : Find ig there is a frontpage id already for given
	//	    usermame or isp
	// Arguments: $uername, $isp
	// Returns  : $frontpage_id
	/////////////////////////////////////////////////////////////////
 
	function config_frontpage_find_by_username_isp ($username, $isp)
	{
		$connection = get_named_connection ("userdata");
 
		$username = addslashes($username);
		$isp      = addslashes($isp);
 
		$query = "SELECT frontpage_id
			    FROM config_frontpage
			   WHERE username = '$username'
			     AND isp = '$isp'";
 
		$result = mysql_query ($query, $connection)
				   or report_error (__FILE__, __LINE__, mysql_error($connection));
 
		$details = mysql_fetch_array($result);
 
		$frontpage_id = $details['frontpage_id'];
 
		return $frontpage_id;
 
	}
?>
