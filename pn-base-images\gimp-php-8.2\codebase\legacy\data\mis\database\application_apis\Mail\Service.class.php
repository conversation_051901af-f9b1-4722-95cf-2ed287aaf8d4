<?php

require_once SQL_PRIMITIVES_LIBRARY;

require_once '/local/data/mis/database/application_apis/Mail/ServiceException.class.php';


/**
 * Service data that is salient to the mail API
 */
class Mail_Service
{
	private $serviceId;
	private $isp;
	private $username;
	private $type;
	private $status;

	/**
	 * Factory to create an object from a service array, eg from userdata.services
	 */
	public static function fromServiceArray(array $service)
	{
		return new self(new Int($service['service_id']),
		                new String($service['isp']),
		                new String($service['username']),
		                new Int($service['type']),
		                new String($service['status']));
	}

	public function __construct(Int $serviceId, String $isp, String $username, Int $type, String $status)
	{
		$this->serviceId = $serviceId;
		$this->isp = $isp;
		$this->username = $username;
		$this->type = $type;
		$this->status = $status;
	}

	public function getServiceId()
	{
		return $this->serviceId;
	}

	public function getIsp()
	{
		return $this->isp;
	}

	public function getUsername()
	{
		return $this->username;
	}
	
	public function getStatus()
	{
		return $this->status;
	}

	public function getTlk()
	{
		return substr($this->username, 0, 2);
	}

	public function getSysUser()
	{
		return $this->getTlk() . '_' . $this->getIsp();
	}

	/**
	 * Retrieve the fully-qualified path of where email sits for
	 * a user on the mail filesystem,
	 *
	 * @access public
	 * @param $alternativeUsername - optional string that will be used instead
	 *                               of their default username.
	 * @return string
	 **/

	public function getHomeDir(String $alternativeUsername = NULL)
	{
		$strPath = "/share/isp/" . $this->getIsp() . "/mail/" . $this->getTlk() . "/";

		if ($alternativeUsername === NULL) {

			$strPath .= $this->getUsername();

		} else {

			$strPath .= $alternativeUsername;
		}


		return strtolower($strPath);
	}

	/**
	 * Determine if a service is paid for, or not.
	 * Probably not 100% foolproof, so add to it as required.
	 * Used to determine whether we should have the "paid for" flag
	 * set in the maildb, which does various networky magic including
	 * SMTP auth.
	 *
	 * @access public
	 * @return boolean
	 * @throws Mail_ServiceException
	 **/

	public function isPaidForService()
	{

		$dbhConn = get_named_connection_with_db('product_reporting');
		
		$strQuery = "SELECT minimum_charge
		             FROM service_definitions
		             WHERE service_definition_id = {$this->type}";

		$resResult = PrimitivesQueryOrExit($strQuery, $dbhConn, __METHOD__, FALSE);

		if (FALSE == $resResult) {

			throw new Mail_ServiceException("failed to identify minimum charge for service definition {$this->type}");
		}


		$floMinCharge = PrimitivesResultGet($resResult, 'minimum_charge');

		if (FALSE === $floMinCharge) {

			throw new Mail_ServiceException("no row found for service definition {$this->type}");
		}

		$bolAttemptOne = $floMinCharge > 0 ? TRUE : FALSE;

		$strQuery = "SELECT
		                COUNT(*) AS n
		             FROM
		                dbProductComponents.tblTariff AS t
		             INNER JOIN userdata.tblProductComponentInstance AS pci
		                ON pci.intTariffID = t.intTariffID
		             INNER JOIN userdata.components AS c
		                ON c.component_id = pci.intComponentID
		             INNER JOIN dbProductComponents.tblStatus AS st
		                ON st.intStatusID = pci.intStatusID
		             WHERE
		                c.service_id = {$this->getServiceId()}
		                AND st.vchHandle NOT IN ('DESTROYED', 'QUEUED_DESTROY')
		                AND pci.dtmEnd IS NULL
		                AND t.intCostIncVatPence > 0";

		$resResult = PrimitivesQueryOrExit($strQuery, $dbhConn, __METHOD__, FALSE);

		if (FALSE == $resResult) {

			throw new Mail_ServiceException("failed to determine if service ID {$this->getServiceId()} has any chargeable tariffs");
		}


		$bolAttemptTwo =  PrimitivesResultGet($resResult, 'n') > 0 ? TRUE : FALSE;
		
		if($bolAttemptOne || $bolAttemptTwo) {
			$query = "SELECT COUNT(1) AS CNT 
				  FROM financial.sales_invoices si
				  INNER JOIN userdata.accounts a ON si.account_id = a.account_id
				  INNER JOIN userdata.customers c ON a.customer_id = c.customer_id
				  INNER JOIN userdata.services s ON c.primary_user_id = s.user_id
				  WHERE si.invoice_type = 1
				  AND si.invoice_status = 'fully_paid'
				  AND si.gross_value > 0
				  AND s.service_id = " . $this->getServiceId();
			return (PrimitivesResultGet(PrimitivesQueryOrExit($query, $dbhConn), 'CNT') > 0) ? new Bool(TRUE) : new Bool(FALSE);
		}
		
		return new Bool(FALSE);
	}

	public function isAllowedWebmailAndIMAP() 
	{
		$arrEnableWebmailIsps = array(
		                              6681 , // Waitrose Pay as you Go
		                              6656   // Madasafish PAYG
		                             );
		if(in_array($this->type->getValue(),$arrEnableWebmailIsps))
		{
			return new Bool(TRUE);
		} 
		else 
		{ 
			return $this->isPaidForService(); 
		}
	}
}
