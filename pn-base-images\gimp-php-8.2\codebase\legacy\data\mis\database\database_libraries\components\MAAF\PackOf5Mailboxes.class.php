<?php
/**
 * Webmail product component
 *
 * @package components.MAAF
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @version $Id: PackOf5Mailboxes.class.php,v 1.2 2007-12-03 07:52:16 swestcott Exp $
 */

/**
 * Needed library
 */
require_once '/local/data/mis/database/database_libraries/components/CProductComponent.inc';
require_once '/local/data/mis/database/database_libraries/userdata-access.inc';
require_once '/local/data/mis/database/database_libraries/components/MAAF/Mailboxes.CProductComponent.class.php';

/**
 * Class for product component that deals with pack of 5 mailboxes
 *
 * <AUTHOR> <<EMAIL>>
 *
 */
class MAAF_CProductComponent_PackOf5Mailboxes extends MAAF_CProductComponent_Mailboxes
{
    /**
     * Number of mailboxes
     */
    const MAILBOXES_NUMBER = 5;

    /**
     * Status event raiser
     *
     * @var string
     */
    protected $strStatusEventRaiser = 'PackOf5Mailboxes';

    /**
     * Method returns number of mailboxes
     *
     * <AUTHOR> <<EMAIL>>
     *
     * @return integer
     */
    protected function getNumberOfAllowedMailboxes()
    {
        return self::MAILBOXES_NUMBER;
    } // end of method getNumberOfMailboxes()

    /**
     * Method returns number of component instances
     *
     * <AUTHOR> Bargiel <<EMAIL>>
     *
     * @throw Exception
     *
     * @return integer
     */
    public static function getNumberOfInstances($intComponentID)
    {
        $resDBHandle = get_named_connection_with_db('userdata');

        $strQuery = 'SELECT COUNT(intProductComponentInstanceId) AS intNumberOfProductComponent '
            . 'FROM userdata.tblProductComponentInstance '
            . 'WHERE intComponentID=' . mysql_real_escape_string($intComponentID)
            . ' AND intProductComponentID='
            . mysql_real_escape_string(PRODUCT_COMPONENT_MAAF_WEBMAIL_PACKOF5MAILBOXES);

        $arrResult = self::doQuery($strQuery, $resDBHandle);

        return $arrResult['intNumberOfProductComponent'];
    } // end of method getNumberOfInstances()

} // end of class MAAF_CProductComponent_PackOf5Mailboxes
