<?php
	require_once(dirname(__FILE__) . '/BullGuardComponent.class.php');

	class CBullGuardTrialComponent extends CBullGuardComponent
	{
		const TRIAL_PERIOD_DAYS = 30;

		public function setExpiryDate($utxDate = 0)
		{
			// Update component description
			if (true == parent::setExpiryDate($utxDate)) {

				$strDate = date('d/m/Y', $this->getExpiryDate());
				$this->setComponentDesctiption("Active free trial until {$strDate}");

				return true;
			}

			return false;
		}

		/**
		 * Override for interface methods
		 *
		 */

		public function configure()
		{
			if (true == parent::configure()) {

				// Set instalation date if not set
				if (false === $this->getInstalationDate()) {

					$this->setInstalationDate();
				}

				// Set default subscription expiry for one mounth from configuration if date wasn't already set
				if (false === $this->getExpiryDate()) {

					$utxExpiry = mktime(
						23,
						59,
						59,
						date('m'),
						date('d') + self::TRIAL_PERIOD_DAYS,
						date('Y')
						);

					$this->setExpiryDate($utxExpiry);
				}

				return true;
			}

			return false;
		}

		/**
		 * Component configurator function. Disables the component.
		 * When disabling trial component set expiry date to current
		 *
		 * @return bool
		 */
		public function disable()
		{
			if ($this->getExpiryDate() > time()) {

				$this->setExpiryDate();
			}

			return parent::disable();
		}

		/**
		 * Component configurator function. Destroys the component.
		 * When destroying trial component set expiry date to current
		 *
                 * @param  array $arrArgs Unused (required to override function in base class without causing an E_Strict error)
		 * @return bool
		 */
		public function destroy($arrArgs = array())
		{
			if ($this->getExpiryDate() > time()) {

				$this->setExpiryDate();
			}

			return parent::destroy();
		}

		/**
		 * Billing methods
		 *
		 */
		public function renew()
		{
			return true;
		}
	}
