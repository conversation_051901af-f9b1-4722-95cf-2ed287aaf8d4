<?php
	/**
	* This file declares the CRadiusSubscription class 
	*
	* @package    Core
	* @subpackage Radius
	* <AUTHOR>
	* @version    $Id: CRadiusSubscription.inc,v 1.4 2006-03-08 19:21:33 khosiawa Exp $
	* @filesource
	*/

	/**
	* Requires
	*/
	
	require_once('/local/data/mis/database/database_libraries/CDAO.inc');

	/**
	* This class declares the RADIUS Subscription class.
	* Extends functionality in CDAO (Data Access Object)
	*
	* @package  Core
	* @access   public
	*/
	
        class CRadiusSubscription extends CDAO
	{
		//
		// Private member variables
		//
		
		
		/**
		* An array of variables that are valid for this class
		*
		* @access private
		* @var    array
		*/
		var $m_arrValidVariables = array('intRadiusID',
		                                 'intGroupID',
		                                 'bolActive',
		                                 'strCalled',
		                                 'intQuotaID',
		                                 'bolAllowCLIOrPassword',
		                                 'intOmitFlags',
		                                 'intAdditionalFlags',
		                                 'strIPAddress',
		                                 'strNetmask');

		
		/**
		*
		* @var integer
		* @access private
		*/
		var $m_intRadiusID = 0;

		
		/**
		*
		* @var integer
		* @access private
		*/
		var $m_intGroupID = 1;	

		
		/**
		*
		* @var boolean
		* @access private
		*/
		var $m_bolActive = true;	

		
		/**
		*
		* @var string
		* @access private
		*/
		var $m_strCalled = 'ALL in GROUP';	

		
		/**
		*
		* @var integer
		* @access private
		*/
		var $m_intQuotaID = 1;	
		
		
		/**
		*
		* @var boolean
		* @access private
		*/
		var $m_bolAllowCLIOrPassword = false;	
		
		
		/**
		*
		* @var integer
		* @access private
		*/
		var $m_intOmitFlags = 0;


		/**
		*
		* @var integer
		* @access private
		*/
		var $m_intAdditionalFlags = 0;	
		
		
		/**
		*
		* @var string
		* @access private
		*/
		var $m_strIPAddress = 'pool';	

		
		/**
		*
		* @var string
		* @access private
		*/
		var $m_strNetmask = '***************';	

	
		
		//
		// STATIC Methods
		//

		/**
		* Create the new object
		*
		* Creates a new instance of a CRadiusSubscription object
		*
		* <code>$objRadiusSubscription = &CRadiusSubscription::Create(2);</code>
		*
		* @access public
		* @static
		* @param the radius id of the user if you wish to instantiate an existing user. Defaults to 0 (new user).
		* @returns CRadiusSubscription instance
		*/ 
		public static function Create($intID=0)
		{
			$objNewObject = new CRadiusSubscription();

			if ($intID > 0) {
				$objNewObject->SetID($intID);
				$objNewObject->Refresh();
			}

			return $objNewObject;

		}


		//
		// Public Methods
		//

		
		/*
		* Retrieve a subscription by radius id and group id
		*
		* <code>
		* // Want to forget the changes made to this object
		* $objObject->Retrieve($intRadiusID,  $intGroupID);
		* </code>
		*
		* @access public
		* @param 
		* @param 
		* @return boolean true if found + populates object members, false if does not exist
		*/

		function Retrieve ($intRadiusID, $intGroupID)
		{
			
                        if ($intRadiusID=='' || $intGroupID=='')
                        {
                            return false;
                        }
                        
                        $dbConnID = get_named_connection_with_db('radius');

			//Construct Query
			$strQuery = "SELECT subscription_id as intSubscriptionID,
			                    radius_id as intRadiusID,
			                    group_id as intGroupID,
			                    called as strCalled,
			                    quota_id as intQuotaID,
			                    IF(allow_cli_or_password = 'Y', 'true', 'false') as bolAllowCLIOrPassword,
			                    omit_flags as intOmitFlags,
			                    additional_flags as intAdditionalFlags,
			                    ip_address as strIPAddress,
			                    ip_netmask as strNetmask
			               FROM subscription
			              WHERE radius_id = $intRadiusID
			                AND group_id = $intGroupID";
			
			$resResult = PrimitivesQueryOrExit($strQuery, $dbConnID);

			$arrResult = PrimitivesResultsAsArrayGet($resResult);

			if(count($arrResult) == 0)
			{
				return false;
			}

			//Set all of the Member Variables
			$this->SetAll($arrResult[0]);
			$this->SetID($arrResult[0]['intSubscriptionID']);

			return true;
		}



		/*
		* Refresh the object's data from the database
		*
		* This method discards the object's current data (if any) and
		* re-retrieves the information from the database.
		* For example:
		* <code>
		* // Want to forget the changes made to this object
		* $objObject->Refresh();
		* </code>
		*
		* @access public
		* @void
		*
		*/

		function Refresh ()
		{
			if ( $this->m_intID == 0 )
			{
				return false;
			}

			$dbConnID = get_named_connection_with_db('radius');

			//Construct Query
			$strQuery = "SELECT radius_id as intRadiusID,
			                    group_id as intGroupID,
			                    called as strCalled,
				    	    quota_id as intQuotaID,
			                    IF(allow_cli_or_password = 'Y', 'true', 'false') as bolAllowCLIOrPassword,             
			                    omit_flags as intOmitFlags,
			                    additional_flags as intAdditionalFlags,
				  	    ip_address as strIPAddress,
				   	    ip_netmask as strNetmask
			               FROM subscription
			              WHERE subscription_id = '{$this->m_intID}'";
						  
			$resResult = PrimitivesQueryOrExit($strQuery, $dbConnID);
			$arrResult = PrimitivesResultsAsArrayGet($resResult);

			if(count($arrResult) == 0)
			{
				return false;			
			}

			//Set all of the Member Variables
			$this->SetAll($arrResult[0]);
			
			return true;
		}


		/**
		* Save the object's data to the database
		*
		* Saves the current state of the object to the database
		*
		* @access public
		*/
		function Save()
		{
			if($this->m_intID == 0)
			{
				return $this->prvSaveInsert();
			}
			else
			{
				return $this->prvSaveUpdate();
			}
		}

		//
		// Private Functions
		//

		/**
		* Insert a new user
		*
		* Insert a new user
		*
		* @access private
		* @return new ID or false
		*/
		function prvSaveInsert()
		{
			$dbConnID = get_named_connection_with_db('radius');

			// I know the insert ignore followed by update looks strange, 
			// it must be done this way because of the replication method 
			// used on the RADIUS databases
			$strQuery ="INSERT IGNORE INTO subscription SET ".
				"ip_address='pool', ip_netmask='***************', ".
				"radius_id ='{$this->m_intRadiusID}', group_id = '{$this->m_intGroupID}', allow_cli_or_password='N'";

			$resResult = PrimitivesQueryOrExit($strQuery, $dbConnID, 'Add new RADIUS subscription');
			$intSubscriptionID = PrimitivesInsertIdGet($dbConnID);
			
			if($intSubscriptionID > 0)
			{
				$this->SetID($intSubscriptionID);
				//Because of the way radius replicates, we need to upate too.
				$this->prvSaveUpdate();
				return true;
			}
			else
			{
				return false;
			}

		} // func: prvSaveInsert


		/**
		* Update an existing subscription
		*
		* Update changes made to this object to subscription
		* NOTE:  Incomplete. Will only update some fields.
		* If you need other fields updating, add them here, but check first
		* that it is legal to update them!!
		*
		* @access private
		* @return true or false
		*/
		function prvSaveUpdate()
		{
			$dbConnID = get_named_connection_with_db('radius');

			// This query is incomplete.  It only contains some of the updatable fields.
			// If you need to update a field which is not here, check that is it allowable then add the SET entry here.

			//Construct Query
			$strQuery ="UPDATE subscription SET ".
				"ip_address='pool', ip_netmask='***************' ".
				"WHERE radius_id ='$intRadiusID' AND group_id = '$intDialupGroupID' ";

			$resResult = PrimitivesQueryOrExit($strQuery, $dbConnID, 'Update an exisiting RADIUS subscription');

			//Data has changed so refresh
			return $this->Refresh();
		} // func: prvSaveUpdate


		

		/**
		* Delete an existing subscription
		*
		* Use wisely
		*
		* @access public
		* @return true or false
		*/
		function DeleteSubscription($arrGroupIDs = array())
		{
			if(count($arrGroupIDs) == 0)
			{
				//Delete this object's subscription
			
				if($this->m_intID < 1)
				{
					return false;
				}

				$dbConnID = get_named_connection_with_db('radius');

				//Construct Query
				$strQuery = "DELETE FROM subscription WHERE subscription_id = '{$this->m_intID}'";

				$resResult = PrimitivesQueryOrExit($strQuery, $dbConnID, 'Delete a RADIUS subscription');

				return true;
			}
			else
			{
				//Delete all in array
			
				$dbConnID = get_named_connection_with_db('radius');
				
				if($this->m_intRadiusID == 0)
				{
					return false;
				}
				
				//Construct Query
				$strGroupIDs = implode(',',$arrGroupIDs);
				$strQuery = "DELETE FROM subscription WHERE radius_id = '{$this->m_intRadiusID}' AND group_id IN ($strGroupIDs)";
				
				$resResult = PrimitivesQueryOrExit($strQuery, $dbConnID, 'Delete all RADIUS subscriptions for a radius_id and group list');
			}
		}

		
	}

?>
