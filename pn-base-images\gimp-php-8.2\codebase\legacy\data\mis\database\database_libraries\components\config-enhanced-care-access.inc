<?php

require_once USERDATA_ACCESS_LIBRARY;
require_once PRODUCT_ACCESS_LIBRARY;
require_once SECURE_TRANSACTION_ACCESS_LIBRARY;
require_once FINANCIAL_ACCESS_LIBRARY;
require_once TICKETS_ACCESS_LIBRARY;
require_once APPLICATION_API_ROOT . 'datetime-formats-api.inc';
require_once APPLICATION_API_ROOT . 'AdslServiceCare/AdslServiceCareLevelException.php';
require_once '/local/data/mis/database/database_libraries/components/component-defines.inc';
require_once '/local/data/mis/database/application_apis/EmailHandler/EmailHandler.class.php';

$GLOBALS['global_component_configurators'][COMPONENT_ENHANCED_CARE] = 'configure_enhanced_care_component';
$GLOBALS['global_component_configurators'][COMPONENT_ENHANCED_CARE_INCLUSIVE] = 'configure_enhanced_care_component';
define('ENHANCED_CARE_BBSCOPE_NOTICE_PERIOD_DAYS', 0);
define('ENHANCED_CARE_PARTNER_NOTICE_PERIOD_DAYS', 30);
/**
 * configure_enhanced_care_component
 *
 * @param integer $intComponentId     component id
 * @param string  $strAction          action
 * @param object  $paymentRequestData object of GenericImmediatePaymentApplication_PaymentResponseData
 *
 * @access public
 * @return void
 */
function configure_enhanced_care_component($intComponentId, $strAction, $paymentRequestData)
{
    switch ($strAction) {
        case "auto_configure":
            return enhanced_care_auto_configure($intComponentId);
            break;

        case "auto_enable":
            return enhanced_care_auto_enable($intComponentId, $paymentRequestData);
            break;

        case "auto_destroy":
            return enhanced_care_auto_destroy($intComponentId);
            break;

        default:
            return false;
    }
}

/**
 * enhanced_care_auto_configure
 *
 * @param integer $intComponentId component id
 *
 * @access public
 * @return void
 */
function enhanced_care_auto_configure($intComponentId)
{
    if (!is_numeric($intComponentId) || 0 >= $intComponentId) {
        return false;
    }

    $dbhConnection = get_named_connection_with_db('userdata');

    // check if the config exists already
    $strQuery = 'SELECT intComponentId from tblConfigAdslServiceCareLevel ' .
        "WHERE intComponentId='$intComponentId'";

    $objResult = PrimitivesQueryOrExit($strQuery, $dbhConnection, '', false);

    if (false == $objResult || 0 == PrimitivesNumRowsGet($objResult)) {

        $strQuery = 'INSERT INTO tblConfigAdslServiceCareLevel SET intAdslServiceCareLevelId = "2", ' .
            "intComponentId = '$intComponentId'";

        if (!PrimitivesQueryOrExit($strQuery, $dbhConnection, 'query failed on', false)) {
            ErrorLog::log(
                'SQL Query ' . str_replace("\t", "", $strQuery) .
                ' failed. Reason: ' . mysql_error(), __FILE__, __LINE__
            );

            return false;
        }
    }

    userdata_component_set_status($intComponentId, 'queued-activate');

    return true;
}

/**
 * enhanced_care_auto_enable_complete
 *
 * completes a call to enhanced care auto enable if the original
 * call returned a redirect url to the thistle payment system.
 *
 * @param object $paymentResponseData GenericImmediatePaymentApplication_PaymentResponseData
 *
 * @throws Exception
 * @return boolean
 */
function enhanced_care_auto_enable_complete($paymentResponseData)
{

    if (!($paymentResponseData instanceof GenericImmediatePaymentApplication_PaymentResponseData)) {
        throw new Exception(
            'paymentResponseData is not instance of GenericImmediatePaymentApplication_PaymentResponseData in '
            . __FILE__ . ' on line ' . __LINE__
        );
    }

    $data = $paymentResponseData->getCallbackData();
    if (!is_array($data)) {
        throw new Exception(
            'paymentResponseData does not contain callback data in '
            . __FILE__ . ' on line ' . __LINE__
        );
    }

    $intComponentId = array_key_exists('intComponentId', $data) ? $data['intComponentId'] : null;
    $floAmountToCharge = array_key_exists('floAmountToCharge', $data) ? $data['floAmountToCharge'] : null;

    if (is_null($intComponentId)) {
        throw new Exception(
            'paymentResponseData callback data does not contain intComponentId in '
            . __FILE__ . ' on line ' . __LINE__
        );
    }

    if (is_null($floAmountToCharge)) {
        throw new Exception(
            'paymentResponseData callback data does not contain floAmountToCharge in '
            . __FILE__ . ' on line ' . __LINE__
        );
    }

    $arrComponent = userdata_component_get($intComponentId);

    if (!$paymentResponseData->success) {
        raiseBillingTicket(
            $arrComponent['service_id'],
            'FAILED_PAYMENT',
            array('intAmountToCharge' => $floAmountToCharge)
        );

        raiseBillingTicket(
            $arrComponent['service_id'],
            'UNSUCCESSFUL_BILLING',
            array('intInvoiceId' => $paymentResponseData->invoiceId)
        );
    }

    // set component to active
    userdata_component_set_status($intComponentId, 'active');
    // Send email to customer
    EmailHandler::sendEmail($arrComponent['service_id'], 'enhanced-care-confirm-activation-business');

    return true;
}

/**
 * enhanced_care_auto_enable
 *
 * @param int    $intComponentId     component id
 * @param object $paymentRequestData object of GenericImmediatePaymentApplication_PaymentRequestData
 *
 * @throws Exception
 * @return boolean|string
 */
function enhanced_care_auto_enable($intComponentId, $paymentRequestData)
{
    $thistle = false;
    if (($paymentRequestData !== null)
        && ($paymentRequestData instanceof GenericImmediatePaymentApplication_PaymentRequestData)
    ) {
        $thistle = true;
    }

    if (!is_numeric($intComponentId) || 0 >= $intComponentId) {
        return false;
    }

    $dbhConnection = get_named_connection_with_db('userdata');
    $strQuery = 'UPDATE tblConfigAdslServiceCareLevel SET dtmStartDate = NOW() ' .
        "WHERE intComponentId = '" . mysql_real_escape_string($intComponentId) . "'";

    if (false == PrimitivesQueryOrExit($strQuery, $dbhConnection, '', false)) {

        $arrComponent = userdata_component_get($intComponentId);
        $intServiceId = (!empty($arrComponent) && isset($arrComponent['service_id']) ?
            $arrComponent['service_id'] : 'NOT FOUND');

        $objException = new AdslServiceCareLevelException(
            'EncancedCare Activation Exception for SID (' . $intServiceId . '): '
            . 'Expected to find a record for ComponentId "' . $intComponentId . '" in tblConfigAdslServiceCareLevel. '
            . 'Exception raised in: ' . __FILE__ . ', line ' . __LINE__, 10
        );

        return false;
    }

    // charge user pro-rata
    $arrComponent = userdata_component_get($intComponentId);
    if ($arrComponent['component_type_id'] == COMPONENT_ENHANCED_CARE) {
        $arrService = userdata_service_get($arrComponent['service_id']);

        $uxtNextInvoiceDate = strtotime($arrService['next_invoice']);
        $intDaysToCharge = DaysBetweenTimestamps(time(), $uxtNextInvoiceDate);
        $floMonthlyCharge = product_component_monthly_charge_get(COMPONENT_ENHANCED_CARE, 'active');

        if (0 < $intDaysToCharge) {
            // pro-rota the payment until the next invoice date
            $floCostPerDay = sprintf("%.2f", (($floMonthlyCharge * 12) / 365));
            $floAmountToCharge = sprintf("%.2f", (($floCostPerDay * $intDaysToCharge)));

            $arrInvoiceItems = array();
            $arrInvoiceItems[] = array(
                'description'  => 'Enhanced Care Service Level',
                'amount'       => $floAmountToCharge,
                'gross'        => true,
                'component_id' => $intComponentId
            );

            if ($arrService['isp'] != 'partner') {
                if ($thistle) {
                    $paymentRequestData->paymentAmountInPence = $floAmountToCharge * 100;
                    // this should be set further up in the chain
                    $paymentRequestData->returnUrl = $paymentRequestData->returnUrl;
                    $paymentRequestData->requestType = GenericImmediatePaymentApplication_RequestType::TAKE_PAYMENT;
                    // should this be set further up in the chain? what are the other options???
                    $paymentRequestData->decoratorClass = "CustomerDetails_Decorator";
                    // $paymentRequestData->additionalTemplateVars = array(); // what goes in here?
                    $paymentRequestData->paymentMethodsAllowed = array(
                        // which of these should we allow in this instance?
                        GenericImmediatePaymentApplication_PaymentMethodsAllowed::EXISTING_CARD,
                        GenericImmediatePaymentApplication_PaymentMethodsAllowed::NEW_CARD,
                        GenericImmediatePaymentApplication_PaymentMethodsAllowed::EXISTING_DIRECT_DEBIT,
                    );
                    $paymentRequestData->invoiceDetails = array(
                        'title'               => 'Enhanced Care Service Level Pro-Rata Charge',
                        'items'               => $arrInvoiceItems,
                        'amountInPenceIncVat' => (float)$floCostPerDay * $intDaysToCharge
                    );
                    $callbackData = is_array($paymentRequestData->callbackData) ?
                        $paymentRequestData->callbackData : array();
                    $callbackData['floAmountToCharge'] = $floAmountToCharge;
                    $callbackData['intComponentId'] = $intComponentId;

                    $paymentRequestData->callbackData = $callbackData;
                    try {
                        $redirectUrl = GenericImmediatePaymentApplication_ImmediatePaymentHelper::getRedirectToPaymentApp($arrComponent['service_id'], $paymentRequestData);
                    } catch (Exception $e) {
                        throw new Exception('failed to get redirect URL  in ' . __FILE__ . ' on line ' . __LINE__);
                    }

                    return $redirectUrl;
                } elseif (!\Plusnet\Feature\FeatureToggleManager::isOnFiltered(
                    'RBM_MIGRATION_COMPLETE',
                    null,
                    null,
                    null,
                    $arrService['service_id']
                )
                ) {
                    $productFamily = ProductFamily_Factory::getFamily($arrService['type']);
                    if (!$productFamily->hasAutoContracts()) {

                        // check for valid CC details
                        $arrAccount = userdata_account_get_by_user($arrService['user_id']);
                        $intCreditId = intval($arrAccount['credit_details_id']);
                        $arrCardDetails = transactions_credit_details_get($intCreditId);

                        $arrCardErrors = array();
                        $arrCardErrors = transactions_validateCC_details($arrCardDetails);

                        if (!$arrCardErrors) {

                            require_once '/local/www/database-admin/phplib/db_mysql.inc';
                            require_once '/local/www/database-admin/phplib/ct_sql.inc';
                            require_once '/local/www/database-admin/phplib/session.inc';
                            require_once '/local/www/database-admin/phplib/auth.inc';
                            require_once '/local/www/database-admin/phplib/perm.inc';
                            require_once '/local/www/database-admin/phplib/user.inc';
                            require_once '/local/www/database-admin/phplib/local.inc';
                            require_once '/local/www/database-admin/phplib/page.inc';

                            global $auth;
                            $auth = new Example_Auth();
                            $arrScriptUser = SoapSession::getScriptUserDetails('PLUSNET');
                            $objSession = new Auth_BusTierSession($arrScriptUser);
                            $auth->objBusTierSession = $objSession;

                            $intInvoiceId = financial_generate_invoice(
                                $arrComponent['service_id'],
                                'Enhanced Care Service Level Pro-Rata Charge',
                                $arrInvoiceItems
                            );

                            // take pro-rata payment
                            $strTransactionResult = '';
                            $strTransactionError = '';
                            $intTransactionId = financial_take_payment_with_status(
                                $arrComponent['service_id'],
                                $floAmountToCharge,
                                $strTransactionResult,
                                $strTransactionError,
                                IspPayments_PaywareManager::TERMINAL_TYPE_MOTO,
                                $intInvoiceId,
                                'default',
                                false,
                                true,
                                $intCreditId
                            );

                            $bolBillingSuccess = false;
                            if (0 == $intTransactionId) {

                                raiseBillingTicket(
                                    $arrComponent['service_id'],
                                    'FAILED_PAYMENT',
                                    array('intAmountToCharge' => $floAmountToCharge)
                                );

                            } else {

                                $bolBillingSuccess = true;
                            }

                            if (!$bolBillingSuccess) {

                                raiseBillingTicket(
                                    $arrComponent['service_id'],
                                    'UNSUCCESSFUL_BILLING',
                                    array('intInvoiceId' => $intInvoiceId)
                                );
                            }

                        } else { // try direct debit

                            $intDirectDebitInstructionId = direct_debit_has_service_got_active_instruction(
                                $arrComponent['service_id']
                            );

                            if ($intDirectDebitInstructionId == -1) {
                                // No active direct debit

                                raiseBillingTicket(
                                    $arrComponent['service_id'],
                                    'FAILED_PAYMENT',
                                    array('intAmountToCharge' => $floAmountToCharge)
                                );

                            } else {

                                $arrDirectDebitTransaction = direct_debit_transaction_add(
                                    $intDirectDebitInstructionId,
                                    $arrAccount['account_id'],
                                    $floAmountToCharge,
                                    'Pro-rata charge',
                                    'Adsl Enhanced Care pro-rata charge',
                                    0,
                                    0,
                                    $arrInvoiceItems
                                );
                            }
                        }
                    }
                }//
            } // partner
        }
        if ($arrService['isp'] == 'partner') {
            $intResellerId = userdata_service_is_resold($arrComponent['service_id']);
            if (!empty($intResellerId)) {
                $businessActor = Auth_BusinessActor::getActorByExternalUserId((int)$intResellerId);

                $intBusinessActorId = $businessActor->getActorId();
                $intMatchingCount =
                    Db_Manager::getAdaptor('ResellerMyAccount')->getIsBBScopePartner($intBusinessActorId);

                if ($intMatchingCount == 1) {

                    $intTariffId = getTariffForEnhancedCare(ENHANCED_CARE_BBSCOPE_NOTICE_PERIOD_DAYS);

                } else {

                    $intTariffId = getTariffForEnhancedCare(ENHANCED_CARE_PARTNER_NOTICE_PERIOD_DAYS);
                }

                Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);

                if ($intTariffId != false) {

                    $bolResult = createInstanceForEnhancedCare($arrComponent, $arrService, $intTariffId);
                    if (!$bolResult) {
                        return false;
                    }

                } else {

                    return false;
                }
            }
        }
    }

    // set component to active
    userdata_component_set_status($intComponentId, 'active');

    EmailHandler::sendEmail($arrComponent['service_id'], 'enhanced-care-confirm-activation-business');

    return true;
}

/**
 * enhanced_care_auto_destroy
 *
 * @param integer $intComponentId component id
 *
 * @access public
 * @return boolean
 */
function enhanced_care_auto_destroy($intComponentId)
{

    if (!is_numeric($intComponentId) || 0 >= $intComponentId) {
        return false;
    }

    $arrComponent = userdata_component_get($intComponentId);
    $arrService = userdata_service_get($arrComponent['service_id']);

    if ((empty($arrComponent) || !isset($arrComponent['status'])
            || 'active' != $arrComponent['status']) && empty($arrService['enddate'])
    ) {

        $ticketText = "As you've requested to remove enhanced care from your account " .
            "we need to place an order to remove this. " .
            "You don't need to do anything, we'll handle it from here.\n\n";

        $ticketText .= "[INTERNAL]\n\n" . "Enhanced care has been removed from this account.
            A modify order needs to be raised in ECO/ECO plus to remove it.";

        $teamId = $arrService['isp'] == 'partner' ? phplibGetTeamIdByHandle('PARTNER_SUPPORT') :
            phplibGetTeamIdByHandle('DSL_REGRADES');

        $result = tickets_ticket_add(
            'Script', $arrComponent['service_id'], 0,
            0, 'OPEN', SCRIPT_USER, $ticketText, 0, $teamId
        );

        if (!$result) {
            throw new AdslServiceCareLevelException('TICKET_NOT_CREATED', 10);
        }

        return false;
    }

    $dbhConnection = get_named_connection_with_db('userdata');
    $strQuery = 'UPDATE tblConfigAdslServiceCareLevel SET dtmEndDate = NOW() ' .
        "WHERE intComponentId = '" . mysql_real_escape_string($intComponentId) . "'";
    PrimitivesQueryOrExit($strQuery, $dbhConnection);

    // Send the email
    EmailHandler::sendEmail($arrComponent['service_id'], 'enhanced-care-cancelled-business');

    if ($arrService['isp'] == 'partner') {
        $productComponent = new ProductComponent_SubComponent();
        $productComponent->destroy($intComponentId);
    }
    // set component to active
    userdata_component_set_status($intComponentId, 'destroyed');

    return true;
}

/**
 * raiseBillingTicket
 *
 * @param integer $intServiceId service id
 * @param string  $strHandle    handle
 * @param array   $arrData
 *
 * @access public
 * @return void
 */
function raiseBillingTicket($intServiceId, $strHandle, $arrData = array())
{
    try {
        if (!is_numeric($intServiceId) || 0 >= $intServiceId) {
            throw new AdslServiceCareLevelException('INVALID SERVICE ID. Passed value: ' . $intServiceId, 10);
        }

        $strFileName = '';
        switch ($strHandle) {
            case 'FAILED_PAYMENT':
                $strFileName = 'FailedToTakePayment.tpl';
                break;

            case 'UNSUCCESSFUL_BILLING':
                $strFileName = 'UnsuccessfulBilling.tpl';
                break;

            default:
                throw new AdslServiceCareLevelException(
                    'INVALID HANDLE. Script failed ' .
                    'to find template file for handle: ' . $strHandle, 10
                );
        }

        $strPath = '/local/data/mis/database/database_libraries/components/templates/';

        if (!file_exists($strPath . $strFileName)) {
            throw new AdslServiceCareLevelException(
                'INVALID TEMPLATE FILE. Script failed ' .
                'to load template file. Path: ' . $strPath . $strFileName, 10
            );
        }

        $strText = '';
        $strText = file_get_contents($strPath . $strFileName);

        if (count($arrData) > 0) {
            // parse ticket content
            foreach ($arrData as $strKey => $unkValue) {
                $strText = str_replace('{' . $strKey . '}', $unkValue, $strText);
            }
        }

        $bolResult = tickets_ticket_add(
            'Script', $intServiceId, 0, 0, 'OPEN', SCRIPT_USER, $strText,
            0, phplibGetTeamIdByHandle('CSC_BILLING')
        );


        if (!$bolResult) {
            throw new AdslServiceCareLevelException('TICKET_NOT_CREATED', 10);
        }
    } catch (AdslServiceCareLevelException $e) {

        // auto problem is raised
        return;
    }
}

/**
 * function to create instance for enhanced care component
 *
 * @param array   $arrComponent array of component details
 * @param array   $arrService   array of service details
 * @param integer $intTariffId  tariff id
 *
 * @return boolean
 */
function createInstanceForEnhancedCare($arrComponent, $arrService, $intTariffId)
{
    $pcids = CProduct::getDefaultProductComponentIDs($arrComponent['component_type_id']);

    foreach ($pcids as $pcid) {
        $uxtStart = strtotime($arrService['next_invoice']);
        $subComponent = CProductComponent::create(
            $arrComponent['service_id'], $arrComponent['component_id'], $pcid,
            $intTariffId, $uxtStart
        );
        $arrArgs = array('uxtEnableDate' => $uxtStart);
        if (!is_object($subComponent)) {
            return false;
        }
        $subComponent->enable($arrArgs);
        $bolResult = chargeProrataCostForEnhancedCare($arrService, $intTariffId);
    }

    return $bolResult;
}

/**
 * Function to get the tariff for enhanced care.
 *
 * @param integer $intNoticePeriodDays notice period days
 *
 * @return integer
 */
function getTariffForEnhancedCare($intNoticePeriodDays)
{
    $dbConnection = get_named_connection_with_db('userdata_reporting');
    $strQuery = "SELECT t.intTariffID
            FROM dbProductComponents.tblTariff t
            INNER JOIN dbProductComponents.tblProductComponentConfig pcc
            ON pcc.intProductComponentConfigID = t.intProductComponentConfigID
            INNER JOIN products.tblServiceComponentProduct scp
            ON scp.intServiceComponentProductID = pcc.intServiceComponentProductID
            INNER JOIN products.tblServiceComponentProductType scpt
            ON scpt.intServiceComponentProductTypeID = scp.intServiceComponentProductTypeID
            WHERE
            scpt.vchHandle ='" . ENHANCED_CARE . "'
            AND
            t.intNoticePeriodDays =" . $intNoticePeriodDays;

    $resResult = PrimitivesQueryOrExit($strQuery, $dbConnection, '', false);
    if ($resResult != false) {
        $intTariffID = PrimitivesResultGet($resResult, 'intTariffID');

        return $intTariffID;
    }

    return false;
}

/**
 * Function to charge the pro rata cost for enhanced care
 *
 * @param array   $arrService  array of service details
 * @param integer $intTariffId tariff id
 *
 * @return boolean
 */
function chargeProrataCostForEnhancedCare($arrService, $intTariffId)
{
    $uxtProRataCalculationDate = time();
    if (empty($arrService) || !isset($arrService['service_id'])) {
        return false;
    }
    $intServiceId = $arrService['service_id'];
    $arrInvoicePeriod = UserdataGetMonthlyInvoicePeriod($intServiceId);
    $uxtNextInvoiceDate = UserdataGetNextMonthlyInvoiceDate($intServiceId);

    $arrProrataCostDetails = getCostDetails($uxtProRataCalculationDate, $arrService, $intTariffId);
    $arrInvoiceItems = array();

    $intTotalCost = $arrProrataCostDetails['intProRataCost'];

    $helper = Reseller_DiscountHelper::getByServiceId(new Int($intServiceId));

    if ($helper instanceof Reseller_DiscountHelper) {

        // get discount helper for specific product type
        $discount = $helper->getDiscount(new Int($arrService['type']));

        // get the discounted price in pence
        $objDiscountedPrice = $discount->applyDiscountPence(new Int($intTotalCost));
        $intTotalCost = $objDiscountedPrice->getValue();
    }

    if ($intTotalCost <= 0) {
        return true;
    }

    $arrInvoiceItems[] = array(
        'amount'      => $intTotalCost / 100,
        'description' => $arrProrataCostDetails['strProRataDescription'],
        'gross'       => true
    );

    $floTotalDisplayCost = number_format(($intTotalCost / 100), 2);

    // When does the period end?
    $uxtPeriodEndDate = $arrInvoicePeriod['uxtPeriodEnd'];

    //create scheduled payments for all required product components
    $objPaymentScheduler = new CProductComponentPaymentScheduler(
        $arrProrataCostDetails['intProductComponentInstanceId'], ''
    );
    $objPaymentScheduler->addIncVatAmount(
        $intTotalCost,
        $uxtNextInvoiceDate,
        $arrProrataCostDetails['strProRataDescription'],
        $uxtProRataCalculationDate,
        $uxtPeriodEndDate
    );
    $intSchedulePaymentID = $objPaymentScheduler->m_intScheduledPaymentID;

    if (!isset($intSchedulePaymentID) && $intSchedulePaymentID != '') {
        $strTicketBody = "A scheduled payment could not be raised for " .
            "&pound;{$floTotalDisplayCost}" .
            "for the {$arrProrataCostDetails['strProRataDescription']}." .
            "Please take this payment manually.";

        tickets_ticket_add('Script', $intServiceId, '', '', 'Open', 0, $strTicketBody);
    }

    return true;
}

/**
 * Method to get the cost details
 *
 * @param integer $uxtProRataCalculationDate pro-rata calculation date
 * @param array   $arrService                array of service details
 * @param integer $intTariffID               Tariff Id
 *
 * @return array
 */
function getCostDetails($uxtProRataCalculationDate, $arrService, $intTariffID)
{
    $uxtMonthInterval = strtotime(date('Y-m-d', $uxtProRataCalculationDate) . ' +1 month');
    $uxtNextInvoice = strtotime($arrService['next_invoice']);

    if (($uxtNextInvoice > $uxtMonthInterval) || $uxtNextInvoice == strtotime(date('Y-m-d', time()))) {
        $uxtProRataCalculationDate = time();
    }
    $intSubscriptionId = CProductComponent::getProductComponentInstance(
        $arrService['service_id'], 'SUBSCRIPTION', array('ACTIVE'), 'ENHANCED_CARE'
    );

    $objSubscription = CProductComponent::createInstance($intSubscriptionId);

    $arrTariffDetails = CProduct::getTariffDetails($intTariffID);
    $intDaysToCharge = DaysBetweenTimestamps(strtotime(date('Y-m-d')), $uxtNextInvoice);
    $intMonthlyCharge = $arrTariffDetails['intCostIncVatInPence'];

    if (0 < $intDaysToCharge) {
        $intCostPerDay = ($intMonthlyCharge * 12) / 365;
        $intAmountToCharge = $intCostPerDay * $intDaysToCharge;
        $intAmountToCharge = round($intAmountToCharge, 0);
    }
    $uxtProRataNextInvoice = CProductComponent::generateProRataNextInvoiceDate(
        $arrService['service_id'], $intTariffID, $uxtProRataCalculationDate
    );
    $strProRataDescription = CProductComponent::generateProRataDescription(
        $uxtProRataNextInvoice, $intTariffID, $uxtProRataCalculationDate
    );

    // Using legacy functions as to not cause transaction issues
    // Transaction issue not present for partner, so instantiate
    // core service object in the if statement
    if ('partner' == $arrService['isp']) {
        $objService = new Core_Service($arrService['service_id']);
        // include enduser secondary ID (e.g. original ID for migrated BBScope endusers) in invoice description
        if (Reseller_EndUser::isPartnerEndUser($objService->getUsername())) {
            // prepend slash, trimming to nothing if secondary ID is empty or blanks
            $strSecondaryId = rtrim(
                '/' . Reseller_EndUser::getEndUserByUsername($objService->getUsername())
                    ->getSecondaryId(), '/ '
            );
        }

        $strProRataDescription .= " ({$objService->getUsername()}$strSecondaryId)";

        $objTagHelper = ResellerTag_TagHelper::getByUsername(new String($objService->getUsername()));
        $strProRataDescription .= $objTagHelper->getInvoiceItemDescriptionModifier();
    }

    return array(
        'intProRataCost'                => $intAmountToCharge,
        'strProRataDescription'         => $strProRataDescription,
        'arrTariffDetails'              => $arrTariffDetails,
        'intProductComponentInstanceId' => $objSubscription->getProductComponentInstanceID()
    );
}
