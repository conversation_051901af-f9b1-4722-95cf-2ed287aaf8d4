<?php
/**
* Helper function for CInternetConnectionProductHelper
*
* @package    Core
* @subpackage WLR
* <AUTHOR> <<EMAIL>>
*/
class CInternetConnectionProductHelper
{
    /**
     * Sets the cancelled date in $arrArgs if applicable
     *
     * @param array  $args     array of Arguments
     * @param object $billingDate billingDate Object
     *
     * @return array
     */
    public function setCancelledDate($args, $billingDate)
    {
        if ($billingDate->isValid()) {
            $prorataServicePeriod =
                $billingDate->getProrataServicePeriodUptoDate(I18n_Date::fromTimestamp(time()));
            $args['uxtCancelled'] = $prorataServicePeriod->getEnd()->getTimestamp();
        }
        return $args;
    }
}
