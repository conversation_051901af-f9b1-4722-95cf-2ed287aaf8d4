<?php
class ParentalControlManagerException extends Exception
{
	/**
	 * Erro code for missing data
	 *
	 */
	const ERR_MISSING_DATA = 1;

	/**
	 * Error code for component exist
	 *
	 */
	const ERR_COMPONENT_EXIST = 2;

	/**
	 * Error code for product exist
	 *
	 */
	const ERR_PRODUCT = 3;

	/**
	 * Constructor
	 *
	 * @param string $message Error message
	 * @param integer $code Error code
	 */
	public function __construct($strMessage, $intCode = 0) {
		parent::__construct($strMessage, $intCode);
	}
}