<?php

// ----------------- //

	require_once '/local/data/mis/database/application_apis/CommunitySite.class.php';

	// Create Global configurator
	$global_component_configurators[COMPONENT_COMMUNITY_SITE] = "config_community_site_configurator";

	if(!isset($GLOBALS['global_component_configurators']))
	{
		$GLOBALS['global_component_configurators'] = $global_component_configurators;
	}
	else
	{
		foreach($global_component_configurators as $intIndex => $strConfigurator)
		{
			$GLOBALS['global_component_configurators'][$intIndex] = $strConfigurator;
		}
	}
	
	/**
	 * config_community_site_configurator
	 * 
	 * Performs a refresh on the component
	 */
	function config_community_site_configurator($intComponentId, $strAction, $strUsername = '')
	{
		// The only thing we care about is auto_refresh. This should let us update
		// the account type.
		
		if ('auto_configure' === $strAction)
		{

			if ('' !== $strUsername)
			{
				// Store the username in the database
				$dbhConn = get_named_connection_with_db('userdata');

				$strQuery = 'INSERT INTO tblConfigCommunitySite (intComponentID, vchUsername) VALUES (' .
					    "'" . mysql_real_escape_string($intComponentId) . "', '" .
					    mysql_real_escape_string($strUsername) . "')";

				PrimitivesQueryOrExit($strQuery,$dbhConn);

				$intConfigId = PrimitivesInsertIdGet($dbhConn);

				// Update component config
				userdata_component_set_config_id($intComponentId,$intConfigId);

				// Update Component Status
				userdata_component_set_status($intComponentId, 'active');
			}

		}
		
		if ('auto_refresh' === $strAction)
		{
			// Do a refresh of the data on the community site.

			// Need to establish username and account type
			$strUsername = CommunitySite::getUsernameFromComponentID($intComponentId);
			$arrComponent = userdata_component_get($intComponentId);
			$bolIsFree = userdata_service_is_free($arrComponent['service_id']);
			
			$objCommunity = new CommunitySite();
			$objCommunity->initialize();

			$objCommunity->refresh($strUsername, $bolIsFree);
			unset($objCommunity);
		
		}

		if ('auto_destroy' === $strAction)
		{

			// Destroying component should not delete the username details in this phase.
			// This is because the ports will still exist on the community site, and we
			// may need to be able to track it back to a user.
			userdata_component_set_status($intComponentId, 'destroyed');

		}

		if ('auto_enable' === $strAction)
		{

			$arrComponent = userdata_component_get($intComponentId);

			if ('deactive' === $arrComponent['status'])
			{

				userdata_component_set_status($intComponentId, 'active');

			}

		}

		if ('auto_disable' === $strAction)
		{
			
			if ('active' === $arrComponent['status'])
			{

				userdata_component_set_status($intComponentId, 'deactive');

			}

		}

	}

// ---------------- //
?>
