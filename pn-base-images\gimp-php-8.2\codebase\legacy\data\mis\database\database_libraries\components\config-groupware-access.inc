<?php

	/////////////////////////////////////////////////////////////////////
	// File:     config-groupware-access.inc
	// Purpose:  Access mini-library for config_webspace
	/////////////////////////////////////////////////////////////////////

	/**
	 * Variables
	 */
	$global_component_configurators['181']  = 'config_groupware_configurator';

	// Hack to insert the component configurator array into PHP5's global scope
	// if it's not already there
	if(!isset($GLOBALS['global_component_configurators']))
	{
		$GLOBALS['global_component_configurators'] = $global_component_configurators;
	}
	else
	{
		foreach($global_component_configurators as $intIndex => $strConfigurator)
		{
			$GLOBALS['global_component_configurators'][$intIndex] = $strConfigurator;
		}
	}

	/////////////////////////////////////////////////////////////////////
	// Library functions


	/////////////////////////////////////////////////////////////
	// Function:  config_groupware_manual_configure
	// Purpose:   Add groupware data for a component
	// Arguments: $component_id (The component ID)
	//            $name         (The name of the group)
	//            $array_groupware (Array of data in the following form:)
	//
	//            array(array('bundle_type_id' => $bundle_type_id,
	//                        'license_count'  => $number_of_licenses),
	//                  array('bundle_type_id' => $bundle_type_id_2,
	//                        'license_count'  => $number_of_licenses_2),
	//                  ...
	// Returns  : The ID of the group created in the process.
	/////////////////////////////////////////////////////////////
	function config_groupware_manual_configure($component_id, $name, $array_bundles, $defer = false)
	{
		$component  = userdata_component_get($component_id);
		$service_id = $component['service_id'];

		// Initialise the component
		userdata_component_set_status($component_id, 'queued-activate');

		// Add the group
		$group_id = groupware_group_add($component_id, $name);

		// Add the default collection
		groupware_collection_add($group_id, 'All', true);

		// Add the owning member
		$owning_member_id = groupware_group_member_add($group_id, $service_id, false);

		// Set the owning member
		groupware_group_owner_set($group_id, $owning_member_id);

		// Add the bundles
		foreach ($array_bundles as $bundle)
		{
			extract($bundle);

			groupware_group_bundle_add($group_id, $bundle_type_id, $license_count, $defer);
		}

		// Update the component
		userdata_component_set_status($component_id, 'active');
		userdata_component_set_config_id($component_id, $group_id);

		return $group_id;
	}

	/////////////////////////////////////////////////////////////
	// Function:  config_groupware_auto_destroy
	// Purpose:   * -> 'queued-destroy' state
	//            transition handler for auto-destruction
	// Arguments: $component_id
	/////////////////////////////////////////////////////////////
	function config_groupware_auto_destroy($component_id)
	{
		$component = userdata_component_get($component_id);

		switch ($component['status'])
		{
			// Free pass to "destroyed"
			case 'unconfigured':
				userdata_component_set_status($component_id, 'destroyed');
				break;

			// Have or may have configuration, pass to the reaper
			case 'active':
			case 'deactive':
			case 'queued-reactivate':
			case 'queued-deactivate':
			case 'queued-activate':
			case 'queued-deconfigure':
			case 'queued-destroy':
				$group = groupware_group_get_by_component_id($component_id);

				// Set status to signal that the group is being destroyed.
				userdata_component_set_status($component_id, 'queued-destroy');

				// Delete the group
				groupware_group_delete($group['group_id']);

				// Finish the job
				userdata_component_set_status($component_id, 'destroyed');

				break;

			// Already there
			case 'destroyed':
				break;

			default:
				// The sky is falling!
				break;
		}

	} // config_groupware_auto_destroy

	///////////////////////////////////////////////
	// Function : configGroupwareAutoEnable
	// Purpose : Make the groupware componet actve
	// Argument : component Id
	//////////////////////////////////////////////////
	function configGroupwareAutoEnable($intComponentId)
	{
		userdata_component_set_status($intComponentId, 'active');
	} // End configGroupwareAutoEnable

	/////////////////////////////////////////////////////////////
	// Function : configGroupwareAutoDisable
	// Purpose : Make the groupware component deactive
	// Argument : component ID
	/////////////////////////////////////////////////////////
	function configGroupwareAutoDisable($intComponentId)
	{
		userdata_component_set_status($intComponentId, 'deactive');
	} //End configGroupwareAutoDisable


	/**
	 * Auto configure the groupware component to setup Essential which is the
	 * chargeable aspect of Plusnet Mail
	 *
	 * @param int $intComponentId
	 */
	function configGroupwareAutoConfigure($intComponentId)
	{
		$arrBundleConfig = array();
		$arrGroupwareBundles = groupware_bundles_types_get();

		foreach($arrGroupwareBundles as $arrGroupwareBundle)
		{
			if ($arrGroupwareBundle['name'] == 'Essential')
			{
				$arrBundleConfig[] = array(
						'bundle_type_id' => $arrGroupwareBundle['bundle_type_id'],
						'license_count'  => 1
					);

				$arrComponent = userdata_component_get($intComponentId);
				$arrService = userdata_service_get($arrComponent['service_id']);

				config_groupware_manual_configure($intComponentId, $arrService['username'], $arrBundleConfig);
			}
		}
	}

	/////////////////////////////////////////////////////////////
	// Function : config_groupware_configurator
	// Purpose  : Groupware component configurator
	// Arguments: $component_id
	//            $action
	/////////////////////////////////////////////////////////////
	function config_groupware_configurator($component_id, $action)
	{
		switch ($action)
		{
			case 'auto_configure':
				// Decision was made to auto configure to setup Plusnet Mail
				configGroupwareAutoConfigure($component_id);
				break;

			case 'auto_disable':
				configGroupwareAutoDisable($component_id);
				break;

			case 'auto_enable':
				configGroupwareAutoEnable($component_id);
				break;

			case 'auto_refresh':
				// Nothing to do here
				break;

			case 'auto_destroy':
				config_groupware_auto_destroy($component_id);
				break;

			default:
				break;
		}
	} // config_groupware_configurator