<?php
require_once 'BVDB.class.php';
require_once '/local/data/mis/database/database_libraries/userdata-access.inc';
require_once '/local/data/mis/database/crypt_config.inc';

class Brightview_ConfigSoulstone
{
    // DB columns attrs
	protected $intServiceId;
	protected $intMailId;
	protected $intAdslMailId;

    public function setServiceId($intServiceId) {$this->intServiceId = $intServiceId;}
    public function setMailId($intMailId) {$this->intMailId = $intMailId;}
	public function setAdslMailId($intAdslMailId) {$this->intAdslMailId = $intAdslMailId;}

    public function getServiceId() {return $this->intServiceId;}
    public function getMailId() {return $this->intMailId;}
	public function getAdslMailId() {return $this->intAdslMailId;}
    

    public function __construct($arrVars = null) {

    	if(is_array($arrVars)) {
            $this->setServiceId($arrVars['intServiceId']);
            $this->setMailId($arrVars['intMailId']);
            $this->setAdslMailId($arrVars['intAdslMailId']);
        }

    }

    public static function findByServiceId($intServiceId) {

    	if (empty($intServiceId) || !is_numeric($intServiceId)) {

    		return false;
    	}

		$dbhConnection = get_named_connection_with_db('userdata_reporting');

		$strQuery = "SELECT
		                   intServiceId,
		                   intMailId,
		                   intAdslMailId
		             FROM
		                   tblConfigSoulstone
		             WHERE
		                   intServiceId = $intServiceId";

		$resResults = PrimitivesQueryOrExit($strQuery, $dbhConnection, 'Query failed on ', false);

		if (!$resResults) {

			return false;
		}

		$arrResults = PrimitivesResultGet($resResults);

		if (is_array($arrResults) && !empty($arrResults)) {

			return new self($arrResults);
		}

		return false;
    }

    public function save() {

        if(!$this->isValid()) {

        	throw new Exception("Soulstone config data are invalid");
        }

        $intAffectedRows = $this->insert();

        if (!$intAffectedRows) {

        	throw new Exception("Soulstone config not saved. Error occurred. Check error log.");
        }

        return $intAffectedRows;
    }

    public function isValid() {

		if (empty($this->intServiceId) || !is_numeric($this->intServiceId) || 
		    empty($this->intMailId) || !is_numeric($this->intMailId) ||
		    !empty($this->intAdslMailId) && !is_numeric($this->intAdslMailId)) {

		    return false;
		}

		return true;
    }

    private function insert() {

    	$dbhConnection = get_named_connection_with_db('userdata');

		$intServiceId  = $this->intServiceId;
		$intMailId     = $this->intMailId;
		$intAdslMailId = (empty($this->intAdslMailId)) ? 'NULL' : $this->intAdslMailId;

		$strQuery = "INSERT INTO tblConfigSoulstone
		             SET intServiceId = $intServiceId,
		                 intMailId = $intMailId,
		                 intAdslMailId = $intAdslMailId";

		$resResults = PrimitivesQueryOrExit($strQuery, $dbhConnection, 'Query failed on ', false);

		if (!$resResults) {

			return false;
		}

		$intAffectedRows = PrimitivesAffectedRowsGet($dbhConnection);

		return $intAffectedRows;
    }

}
