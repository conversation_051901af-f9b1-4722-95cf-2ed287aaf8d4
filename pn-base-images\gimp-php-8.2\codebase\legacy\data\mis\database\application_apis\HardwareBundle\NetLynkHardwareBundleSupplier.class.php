<?php
/**
 * NetLynkHardwareBundleSupplier file
 *
 * @package HardwareBundle
 * <AUTHOR> <<EMAIL>>
 */

/** 
 * NetLynkHardwareBundleSupplier class
 *
 * @package HardwareBundle
 * <AUTHOR> <<EMAIL>>
 */
class NetLynkHardwareBundleSupplier extends HardwareBundleSupplier
{
    /**
     * @var string
     */
    public $m_strClassPrefix = 'NetLynk';

    /**
     * @var string
     */
    public $m_strSupplierTag = 'NetLynk';

    /**
     * Holds status of hardware order on failure
     */
    const FAILURE_STATUS = 'FAIL';

    /**
     * Holds rescode of hardware order on failure due to XML document incorrectly formed.
     */
    const INVALID_RESCODE = 'INVALIDXML';

    /**
     * Holds status of hardware order on success
     */
    const SUCCESS_STATUS = 'SUCCESS';

    /**
     * Holds the URL for the NETLYNK gatewat
     */
    const NETLYNK_GATEWAY = 'https://www.yaurl.com/generic_edi/inbound_request.aspx';

    /**
     * Holds the URL for the NETLYNK trck request
     */
    const NETLYNK_TRACK_REQUEST = 'https://www.yaurl.com/generic_edi/track_request.aspx';

    /**
     * The URL for the XSD file used to validate the XML
     */
    const NETLYNK_XSD_FILE = 'http://www.yaurl.com/generic_edi/xsd/order_query.xsd';

    /**
     * Default OUI for MPS devices ordered from NetLynk
     */
    const DEFAULT_OUI = 'MPSOUI';

    /**
     * Holds NETLYNK supplier tag
     */
    const NETLYNK_SUPPLIER_TAG = 'NETLYNK';

    /**
     * The directory where XSD files should be cached to
     */
    const XSD_CACHE_DIR = '/tmp';

    /**
     * The lifetime for a cached file
     */
    const XSD_CACHE_LIFETIME_HOURS = 24;

    /**
     * The number of attempts which should be made to download the XSD before aborting
     */
    const XSD_CACHE_DOWNLOAD_RETRIES = 3;

    /**
     * Return the order status as dispatched.
     *
     * @param NetLynkHardwareBundle $hardwareBundle the hardware bundle
     *
     * @return array
     */
    public function FetchLatestOrderStatus($hardwareBundle)
    {
        $dom = new DOMDocument('1.0', 'iso-8859-1');

        $search = $dom->createElement('Search');
        $dom->appendChild($search);

        //<Version>
        $version = $dom->createElement('Version', '1');
        $search->appendChild($version);
        //</Version>

        //<CustomerInformation>
        $customerInformation
            = $hardwareBundle->GenerateCustomerInformation($dom);
        $search->appendChild($customerInformation);
        //<CustomerInformation>

        $intReference = $hardwareBundle->GetHardwareBundleID();

        //<Criteria>
        $criteria = $dom->createElement('Criteria', $intReference);
        $search->appendChild($criteria);
        //<Criteria>

        $bolValidateSchema = $this->validateSchema($dom, self::NETLYNK_XSD_FILE);

        if (true == $bolValidateSchema) {

            if ($this->PlaceTrackRequest($dom->saveXML(), $hardwareBundle)) {

                return array('strStatusTag' => 'dispatched');
            }
        }

        return array();
    }

    /**
     * Sends search order to Netlynk to check hardware order status
     *
     * @param string                $strOrderString the order XML
     * @param NetLynkHardwareBundle $hardwareBundle the hardware bundle
     *
     * @return bool
     */
    public function PlaceTrackRequest($strOrderString, $hardwareBundle)
    {

        $strResponse = $this->sendXmlToNetLynk($strOrderString, self::NETLYNK_TRACK_REQUEST);
        $arrSimpleXmlElement = new SimpleXmlElement($strResponse, LIBXML_NOCDATA);

        $intReference = $hardwareBundle->GetHardwareBundleID();

        foreach ($arrSimpleXmlElement as $simpleXmlElement) {

            if ($intReference == (int) $simpleXmlElement->Reference) {

                $strSerialNumber = (string) $simpleXmlElement->SupplierPartList->SupplierPart->SerialNumber;

                if (!empty($strSerialNumber)) {

                    //OuiNumber not present for NetLynk orders
                    //Using supplier tag as default to preserved current logic
                    $arrStatus = array(
                        'strSerialNumber' => $strSerialNumber,
                        'strOuiNumber'    => self::DEFAULT_OUI
                    );

                    $hardwareBundle->updateTr069Details($arrStatus);

                    return $hardwareBundle->saveHardwareOrderFeedback($strSerialNumber);
                } elseif (!$hardwareBundle->isTr069Compatible()) {

                    // If it is not a Tr09 compatible device
                    // and not having SN, should fall into here.
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Get the status of an RMA
     *
     * @param HardwareRmaProcess $objRma the RMA object
     *
     * @return array
     */
    public function FetchLatestRmaStatus($objRma)
    {
        $bundle = $objRma->GetOriginalBundle();
        $orderDetails = $this->FetchLatestOrderStatus($bundle);
        if (isset($orderDetails['strStatusTag']) && $orderDetails['strStatusTag'] == "dispatched") {

            $orderDetails['rma_complete_date'] = date('Y-m-d');
        }

        return $orderDetails;
    }

    /**
     * Send the given XML to NetLynk and return the response
     *
     * @param string $strOrderString    the XML that is to be sent
     * @param string $netLynkGatewayUrl The URL that the XML will be sent to
     *
     * @return string
     */
    private function sendXmlToNetLynk($strOrderString, $netLynkGatewayUrl)
    {
        $resCurlHandle = curl_init($netLynkGatewayUrl);

        // Set some options
        curl_setopt($resCurlHandle, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($resCurlHandle, CURLOPT_POST, 1);
        curl_setopt($resCurlHandle, CURLOPT_HTTPAUTH, 'CURLAUTH_ANY');
        curl_setopt($resCurlHandle, CURLOPT_HTTPHEADER, array("Content-Type: text/xml"));
        curl_setopt($resCurlHandle, CURLOPT_POSTFIELDS, $strOrderString);
        curl_setopt($resCurlHandle, CURLOPT_SSL_VERIFYPEER, false);

        $strResponse = @curl_exec($resCurlHandle);
        curl_close($resCurlHandle);
        return $strResponse;
    }

    /**
     * Function to place NetLynk hardware order
     *
     * @param string $strOrderString the XML to be sent
     *
     * @return null
     */
    public function PlaceOrder($strOrderString)
    {
        $strResponse = $this->sendXmlToNetLynk($strOrderString, self::NETLYNK_GATEWAY);

        $arrSimpleXmlElement = new SimpleXmlElement($strResponse, LIBXML_NOCDATA);

        foreach ($arrSimpleXmlElement as $simpleXmlElement) {

            $intConfigHardwareBundleID = $simpleXmlElement->Reference;
            $hardwareBundle = new HardwareBundle();
            $hardwareBundle->GetByHardwareID($intConfigHardwareBundleID);

            if (self::SUCCESS_STATUS == $simpleXmlElement->Status) {

                $hardwareBundle->SetWhenOrdered();
                $hardwareBundle->SetStatusTag(-1, 'awaiting_processing');

                // Commit to the database
                if (true == $hardwareBundle->Commit()) {
                    $hardwareBundle->AddContact('Order placed for hardware bundle');
                }

            } elseif (self::FAILURE_STATUS == $simpleXmlElement->Status) {

                $hardwareBundle->SetStatusTag(-1, 'rejected');
                $hardwareBundle->Commit();
                if (self::INVALID_RESCODE == $simpleXmlElement->Information->Message->ResCode) {
                    $problemDescription =
                        'Unable to send XML order to NetLynk, the XML submitted was invalid' . PHP_EOL . PHP_EOL .
                        'You can verify the XML that was sent against the XSD:' .
                        $strSchemaUrl . PHP_EOL . PHP_EOL .
                        'This is the XML that we sent to NetLynk: \n' . PHP_EOL . PHP_EOL.
                        $strOrderString . PHP_EOL . PHP_EOL . PHP_EOL .
                        'This is the error we received that triggered this P1:' . PHP_EOL . PHP_EOL .
                        $strResponse;

                    $this->raiseAutoProblem(
                        'NETLINK_INVALIDXML',
                        'NetLynk Hardware Ordering - Invalid XML',
                        $problemDescription
                    );
                } else {
                    // Raise ticket on the failure of hardware order
                    $intTeamID = PHPLibTeamIdGetByName('Customer Support Centre');
                    $intServiceId = $hardwareBundle->GetServiceID();

                    $strBody = 'Unfortunately your recent order request for hardware has failed.'. PHP_EOL . PHP_EOL;
                    $strBody .= 'A member of our Partner Support Team is now dealing with this. They will contact ';
                    $strBody .= 'you shortly with details about why your hardware ".
                    "order was unsuccessful.'. PHP_EOL . PHP_EOL;
                    $strBody .= 'In the meantime, should you wish to discuss this ".
                    "further please reply to this Ticket, ';
                    $strBody .= 'or raise a new one at the Partner Control Panel ".
                    "- https://partner.plus.net/account/tickets';
                    tickets_ticket_add('Portal', $intServiceId, 0, 0, 'Open', '0', $strBody, '0', $intTeamID);
                }
            } else {
                // Unknown status returned by NetLynk - we should never get here, so raise a problem
                $problemDescription =
                    'Unknown response received from NetLynk.' . PHP_EOL . PHP_EOL .
                    'This is the XML that we sent to NetLynk: \n' . PHP_EOL . PHP_EOL.
                    $strOrderString . PHP_EOL . PHP_EOL . PHP_EOL .
                    'This is the response we received that triggered this P1:' . PHP_EOL . PHP_EOL .
                    $strResponse;

                $this->raiseAutoProblem(
                    'NETLINK_INVALIDRESPONSE',
                    'NetLynk Hardware Ordering - Invalid XML',
                    $problemDescription
                );
            }
        }
    }

    /**
     * Create the final xml and place the order
     *
     * @param array $arrHardWareBundles an array of hardware bundles
     *
     * @return null
     */
    public function SendOrders($arrHardWareBundles)
    {
        $dom = new DOMDocument('1.0', 'UTF-8');

        $orders = $dom->createElement('Orders');
        $dom->appendChild($orders);

        foreach ($arrHardWareBundles as $hardwareBundle) {

            $order = $hardwareBundle->GenerateOrderString($dom);
            $orders->appendChild($order);
        }

        $strSchemaUrl = 'http://www.yaurl.com/generic_edi/xsd/inbound_order.xsd';
        $bolValidateSchema = $this->validateSchema($dom, $strSchemaUrl);

        if (true == $bolValidateSchema) {
            // Strip out any non ISO-8859-1 characters as NetLynk doesn't like them
            $dom->formatOutput = true;
            $orderXml = preg_replace('/[^(\x20-\x7F)]*/', '', $dom->saveXML());

            $this->PlaceOrder($orderXml);
        }
    }

    /**
     * Cache the XSD file on the local machine, to reduce overheads and avoid issues due to timeouts
     *
     * @param str $strSchemaUrl the URL for the XSD file
     * 
     * @return str the local path for the filename, or the original url (if caching failed)
     */
    private function getCachedSchemaUrl($strSchemaUrl)
    {
        // Cache expiry time in seconds
        $cacheExpiryTime = self::XSD_CACHE_LIFETIME_HOURS * (60*60);
        $cacheFileAge = 0;

        $arrReplace = array('/:/', '/\//');
        $filename = preg_replace($arrReplace, '_', $strSchemaUrl);
        $cacheFilename = self::XSD_CACHE_DIR . "/$filename";

        $cachedFilePresent = false;
        $cachedFileValid = false;

        if (file_exists($cacheFilename) && is_readable($cacheFilename)) {
            $cachedFilePresent = true;

            $mtime = filemtime($cacheFilename);

            $cacheFileAge = (time() - $mtime);
            if ($cacheFileAge < $cacheExpiryTime) {
                $cachedFileValid = true;
            }
        }

        // the cached file is still valid: return
        if ($cachedFilePresent && $cachedFileValid) {
            return $cacheFilename;
        }

        // We need to download and cache a copy of the XSD file
        for ($i = 0; $i < self::XSD_CACHE_DOWNLOAD_RETRIES; $i++) {
            if (copy($strSchemaUrl, $cacheFilename) == true) {
                return $cacheFilename;
            }

            // Arbitrary sleep period between retries
            if (($i + 1) < self::XSD_CACHE_DOWNLOAD_RETRIES) {
                sleep(5);
            }
        }

        // We were not able to download a copy of the file. If the cached file isn't too old (arbitrary decision #2:
        // up to double the duration of the cache timeout period), then return it
        if ($cachedFilePresent && $cacheFileAge < ($cacheExpiryTime * 2)) {
            $this->raiseAutoProblem(
                'NETLINK_INVALIDXML',
                'NetLynk Hardware Ordering - Invalid XML',
                "Unable to download XSD file [$strSchemaUrl]. Falling back to expired cache file."
            );

            return $cacheFilename;
        }

        $this->raiseAutoProblem(
            'NETLINK_INVALIDXML',
            'NetLynk Hardware Ordering - Invalid XML',
            "Unable to download XSD file [$strSchemaUrl]. Cache file is either expired or unavailable."
        );

        // As all else has failed, return the original URL
        return $strSchemaUrl;
    }

    /**
     * check whether the xml is valid in accordance with the xsd
     *
     * @param DOMDocument $dom          the DOM object
     * @param str         $strSchemaUrl the URL for the XSD which will be used to validate the DOM object
     *
     * @return bool
     */
    private function validateSchema(DOMDocument $dom, $strSchemaUrl)
    {
        libxml_use_internal_errors(true);

        $strSchemaFile = $this->getCachedSchemaUrl($strSchemaUrl);

        if (!$dom->schemaValidate($strSchemaFile)) {

            $errors = libxml_get_errors();
            $strErrorMessage = '';
            foreach ($errors as $error) {

                $strErrorMessage .= $error->message;
            }

            $problemDescription =
                'Unable to send XML order to NetLynk as the XML has failed validation.' . PHP_EOL . PHP_EOL .
                'The following XSD file was used to validate the XML:' .
                $strSchemaUrl . PHP_EOL . PHP_EOL .
                'This is the XML that failed validation: \n' . PHP_EOL . PHP_EOL.
                $dom->saveXML() . PHP_EOL . PHP_EOL . PHP_EOL .
                'This is the error reported by the XML validator:' . PHP_EOL . PHP_EOL .
                $strErrorMessage;

            $this->raiseAutoProblem(
                'NETLINK_INVALIDXML',
                'NetLynk Hardware Ordering - Invalid XML',
                $problemDescription
            );

            return false;
        }

        return true;
    }

    /**
     * Raise Autoproblem
     *
     * @param string $problemName        the problem name
     * @param string $problemTitle       the problem title
     * @param string $problemDescription the problem description
     *
     * @return null
     */
    private function raiseAutoProblem($problemName, $problemTitle, $problemDescription)
    {
        $objAutoProblem = new PomsClient_AutoProblem(
            $problemName,
            $problemTitle,
            $problemDescription
        );

        $objAutoProblem->raiseProblem();
    }
}
