<?php
/**
 * phpcs DOCBLOCK header
 *
 * @package LegacyCodebase
 * <AUTHOR> <<EMAIL>>
 *
 */

/**
 * Get configuration data for a particular visp.  Can take either the services.isp or visp_config.isp_definition_id
 * As there's a small number of ISPs (24 as of Jan 2014), we cache them all in a single pass rather than
 * re-querying the DB on each request.  Returns boolean false if no data found
 *
 * @param mix $isptag_or_isp_definition_id the isp/idi
 *
 * @return array
 */
function split_product_visp_config_get($isptag_or_isp_definition_id)
{
    static $dataCache = false;

    if (!is_array($dataCache)) {
        $conn      = get_named_connection_with_db('product_reporting');
        $query     = "SELECT * FROM visp_config";
        $resResult = PrimitivesQueryOrExit($query, $conn);                                                     
        $resList   = PrimitivesResultsAsArrayGet($resResult);

        foreach ($resList as $config) {
            $isp = $config['isp'];
            $idi = $config['isp_definition_id'];

            // PHP foreach loops use a reference for $config, so we can't just use "$dataCache[$key] = &$config"
            // Instead, we use a simple (and only slightly hacky) mechanism to save memory: the "idi" key just
            // points to the "isp" key...
            $dataCache[$isp] = $config;
            $dataCache[$idi] = $isp;
        }
    }

    $key = $isptag_or_isp_definition_id;

    if (!array_key_exists($key, $dataCache)) {
        return false;
    }

    $key = (is_array($dataCache[$key]) ? $key : $dataCache[$key]);

    return $dataCache[$key];
}
