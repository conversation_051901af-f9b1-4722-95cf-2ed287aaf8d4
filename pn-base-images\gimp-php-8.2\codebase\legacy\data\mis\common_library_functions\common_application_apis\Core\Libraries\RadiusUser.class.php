<?php
/**
 * Core Radius User
 *
 * @category  Core
 * @package   Core
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @link      Projects/Tr069/PDD
 * @since     File available since 2009-04-23
 */
/**
 * Core Radius User
 *
 * This class holds useful information that is stored in radius
 * for a given user.
 *
 * @category  Core
 * @package   Core
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @link      Projects/Tr069/PDD
 */
class Core_RadiusUser
{
	/**
	 * Radius User (Primary Key in radius2.users)
	 *
	 * @var Int
	 */
	private $radiusId;

	/**
	 * Username
	 *
	 * @var Val_Username
	 */
	private $username;

	/**
	 * Realm
	 *
	 * @var String
	 */
	private $realm;

	/**
	 * Password
	 *
	 * @var String
	 */
	private $password;

	/**
	 * Constructor for Core_RadiusUser
	 *
	 * @param Int          $radiusId The radius id (Primary key)
	 * @param Val_Username $username The username object for the user
	 * @param String       $realm    The realm of the user's connection
	 * @param String       $password The password object for the user
	 *
	 * @return Core_RadiusUser
	 */
	private function __construct(Int $radiusId, Val_Username $username, String $realm, String $password)
	{
		$this->radiusId = $radiusId;
		$this->username = $username;
		$this->realm    = $realm;
		$this->password = $password;
	}

	/**
	 * Getter factory for radius user by service id
	 *
	 * @param Int $serviceId Service Id
	 *
	 * @return Core_RadiusUser
	 */
	public static function getRadiusUserByServiceId(Int $serviceId)
	{
		$adaptor = Db_Manager::getAdaptor('Core', Db_Manager::DEFAULT_TRANSACTION);

		$service = $adaptor->getServiceDao($serviceId);
		$data = $adaptor->getRadiusUserByUsernameIsp($service['username'], $service['isp']);
		$realm = $adaptor->getRadiusUserRealm($serviceId);

		return new Core_RadiusUser(
			new Int($data['intRadiusId']),
			new Val_Username($data['strUsername']),
			new String($realm),
			new String($data['strEncryptedPassword'])
		);
	}

	/**
	 * Getter factory for radius user by radius id
	 *
	 * @param Int $radiusId Radius Id
	 *
	 * @return Core_RadiusUser
	 */
	public static function getRadiusUserByRadiusId(Int $radiusId)
	{
		$adaptor = Db_Manager::getAdaptor('Core', Db_Manager::DEFAULT_TRANSACTION);

		$data = $adaptor->getRadiusUserByRadiusId($radiusId);
		$realm = $adaptor->getRadiusUserRealm($data['intServiceId']);

		return new Core_RadiusUser(
			new Int($data['intRadiusId']),
			new Val_Username($data['strUsername']),
			new String($realm),
			new String($data['strEncryptedPassword'])
		);
	}

	/**
	 * This function was introduced for Tr069 functionality.
	 *
	 * DISCLAIMER::
	 * The problem we were having was, we needed certain information
	 * from Radius but at a time when the radius entry had not been
	 * populated. This function tries to get said Radius information
	 * but if the data does not exist, it pulls the information from
	 * other places. DO NOT use this function unless you are happy
	 * with the way it generates the data. DO NOT change this function
	 * unless you investigate the consequences for Tr069 implementation.
	 * Also NOTE that this will generate a RadiusUser object with a
	 * radiusId of -1
	 *
	 * @param Int $serviceId The service Id
	 *
	 * @return Core_RadiusUser
	 */
	public static function inferRadiusUserByServiceId(Int $serviceId)
	{
		$radiusId = -1;
		$username = null;
		$realm = '';
		$password = null;

		$adaptor = Db_Manager::getAdaptor('Core', Db_Manager::DEFAULT_TRANSACTION);
		$service = $adaptor->getServiceDao($serviceId);
		$data = $adaptor->getRadiusUserByUsernameIsp($service['username'], $service['isp']);
		$realm = $adaptor->getRadiusUserRealm($serviceId);

		// If we can use radius data, then great. If not resort to userdata.services
        if ((isset($data['strUsername']) && !empty($data['strUsername']))
            && (isset($data['strEncryptedPassword']) && !empty($data['strEncryptedPassword']))) {
			    $radiusId = $data['intRadiusId'];
			    $username = $data['strUsername'];
			    $password = $data['strEncryptedPassword'];
		} else {
			    $username = $service['username'];
			    $password = $service['password'];
		}

		return new Core_RadiusUser(
			new Int($radiusId),
			new Val_Username($username),
			new String($realm),
			new String($password)
		);
	}

	/**
	 * Getter for radius Id
	 *
	 * @return Int
	 */
	public function getRadiusId()
	{
		return $this->radiusId;
	}

	/**
	 * Getter for the username
	 *
	 * @return Val_Username
	 */
	public function getUsername()
	{
		return $this->username;
	}

	/**
	 * Getter for the password (Encryped)
	 *
	 * @return String
	 */
	public function getEncryptedPassword()
	{
		return $this->password;
	}

	/**
	 * Getter for the password (Decrypted)
	 *
	 * @return Val_Password
	 */
	public function getDecryptedPassword()
	{
		require_once '/local/data/mis/database/crypt_config.inc';

		$password = Crypt_Crypt::decrypt($this->password, 'users');

		return new Val_Password($password);
	}

	/**
	 * Getter for realm
	 *
	 * @return String
	 */
	public function getRealm()
	{
		return $this->realm;
	}
}
