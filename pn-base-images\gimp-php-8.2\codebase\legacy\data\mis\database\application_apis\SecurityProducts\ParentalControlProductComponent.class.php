<?php

require_once USERDATA_ACCESS_LIBRARY;
require_once PRODUCT_ACCESS_LIBRARY;
require_once TICKETS_ACCESS_LIBRARY;

require_once '/local/data/mis/database/common_define.inc';
require_once '/local/data/mis/database/database_libraries/components/CProductComponent.inc';
require_once '/local/data/mis/database/application_apis/SecurityProducts/NetInterface.class.php';
require_once '/local/data/mis/database/database_libraries/components/CWlrProduct.inc';

class ParentalControlProductComponent extends CProductComponent
{

    const STATE_UNDEFINED = 0;
    const STATE_NEVER_INSTALLED = 1;
    const STATE_ACTIVE = 2;         // Active product component
    const STATE_INACTIVE = 3;       // Product component after subscription expiry date (possible only when failed billing?)
    const STATE_DESTROYED = 4;

    /**
     * Provider key
     *
     * @var string
     */
    const PROVIDER_KEY = 'iaYkJ9QS2GUzOWLXlpRueMpiw';

    /**
     * Provider package code
     *
     * @var integer
     */
    const PROVIDER_PACKAGE_CODE = 'HEL-GRNBEE-OPL';

    /**
     * Licence key
     *
     * @var string
     */
    const LICENCE_TYPE = 'no_trial';        // trial or no_trial

    /**
     * Contract period in days
     * @var integer
     */
    const DAYS = 1825;      // 5 years (5 * 365)

    /**
     * Contains file name used to create account in Netintelligence
     *
     * @var array
     */
    protected $arrFileNames = array(
                        'strCreateAccount' => 'api2_create_account.php',

                        'strRenewal' => 'api2_set_renewal.php',

                        'strSetAccountStatus' => 'api2_set_account_status.php'
                        );

    /**
     * Net interface object
     *
     * @var NetInterface
     */
    protected $objNetInterface;

    /**
     * Parental control config Id
     *
     * @var integer
     */
    protected $intParentalControlConfigId;

    /**
     * Password used to access parental control account
     *
     * @var string
     */
    protected $strPassword;

    /**
     * Licence key used to create parental control account
     *
     * @var integer
     */
    protected $strLicenceKey;

    /**
     * Last updated date and time
     *
     * @var timestamp
     */
    protected $uxtLastUpdate;

    /**
     * Defines how to set the component account start date and end date when renewing.
     *
     * @var string
     */
    protected $strIncr;

    /**
     * Constructor
     *
     * @param integer $intProductComponentInstanceID Product component instance Id
     * @return void
     */
    public function __construct($intProductComponentInstanceID)
    {
        $this->objNetInterface = new NetInterface();
        $this->objNetInterface->setBaseUrl(NETINTELLIGENCE_BASE_URL);

        $this->CProductComponent($intProductComponentInstanceID);

        if (0 == $intProductComponentInstanceID || false == is_numeric($intProductComponentInstanceID)) {

            return false;
        }

        $dbhConnection = get_named_connection_with_db('userdata');
        $intProductComponentInstanceID = mysql_real_escape_string($intProductComponentInstanceID, $dbhConnection['handle']);

        $strQuery =
        "     SELECT  cpc.intParentalControlConfigId, " .
        "             cpc.intProductComponentInstanceID, " .
        "             cpc.vchPassword AS strPassword, " .
        "             pclk.vchLicenceKey AS strLicenceKey, " .
        "             cpc.stmLastUpdate AS uxtLastUpdate " .
                "       FROM userdata.tblProductComponentInstance pci " .
                " INNER JOIN userdata.tblConfigParentalControl cpc ON (cpc.intProductComponentInstanceId = pci.intProductComponentInstanceID) " .
                " INNER JOIN dbProductComponents.tblParentalControlLicenceKeys pclk ON (pclk.intProductComponentInstanceId = pci.intProductComponentInstanceID) " .
                "      WHERE pci.intProductComponentInstanceID = {$intProductComponentInstanceID}";

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);
        $arrResult = PrimitivesResultGet($resResult);

        if(0 == count($arrResult)) {

            return false;
        }

        $this->intParentalControlConfigId = $arrResult['intParentalControlConfigId'];
        $this->strPassword = $arrResult['strPassword'];
        $this->uxtLastUpdate = $arrResult['uxtLastUpdate'];
        $this->strLicenceKey = $arrResult['strLicenceKey'];

    }

    /**
     * Enable Parental Control account
     *
     * @return bool
     */
    public function enable()
    {
        switch ($this->getStatus()) {

            case 'DEACTIVE':
            case 'QUEUED_REACTIVATE':
                return $this->reActivate();
            default:
                return $this->createAccount();
        }
    }

    /**
     * Active parental control account
     *
     * @return bool
     */
    public function reActivate()
    {
        if (!$this->hasDeactiveContract()) {
            if (!$this->prvSetStatus('QUEUED_REACTIVATE')) {
                return false;
            }
            if (!$this->configureNILicenceKey()) {
                return false;
            }
            if(!$this->setNIAccountStatus('on')) {
                return false;
            }
            if (!$this->enableContract()) {
                return false;
            }
            if (!$this->prvSetStatus('ACTIVE')) {
                return false;
            }
            return true;
        }
        return false;
    }

    /**
     * Create parental control account
     *
     * @return bool
     */
    public function createAccount()
    {
        if(!$this->hasActiveContract()) {
            if (!$this->prvSetStatus('QUEUED_ACTIVATE')) {
                return false;
            }
            if (!$this->configureNILicenceKey()) {
                return false;
            }
            if (!$this->createNIAccount()) {
                return false;
            }
            if(!$this->createContract()) {
                return false;
            }
            if(!$this->initConfig()) {
                return false;
            }
            if (!$this->prvSetStatus('ACTIVE')) {
                return false;
            }
            return true;

        }
        return false;

    }

    /**
     * Enable the contract
     *
     * @return bool
     */
    public function enableContract()
    {
        if ($this->m_intProductComponentInstanceID > 0) {
            $dbhConnection = get_named_connection_with_db('userdata');
            $intProductComponentInstanceID = mysql_real_escape_string($this->getProductComponentInstanceID(), $dbhConnection['handle']);

            $strQuery = "UPDATE userdata.tblProductComponentContract SET dtmCancelled = NULL
                        WHERE dteContractStart <= NOW()
                         AND dteContractEnd > NOW()
                         AND dtmCancelled IS NOT NULL
                         AND intProductComponentInstanceID = '{$intProductComponentInstanceID}'";

            $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);
            unset($strQuery, $dbhConnection, $resResult);
            return true;
        }
        return false;

    }

    /**
     * Check whether user have deactive contract.
     *
     * @return bool
     */
    public function hasDeactiveContract()
    {
        $dbhConnection = get_named_connection_with_db('userdata');
        $m_intProductComponentInstanceID = mysql_real_escape_string($this->getProductComponentInstanceID(), $dbhConnection['handle']);

        $strQuery = "SELECT count(*) as intActiveContracts
                       FROM userdata.tblProductComponentContract
                      WHERE dteContractStart <= NOW()
                        AND dteContractEnd > NOW()
                        AND dtmCancelled IS NOT NULL
                        AND intNextProductComponentContractID IS NULL
                        AND intProductComponentInstanceID = '{$m_intProductComponentInstanceID}'";

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection, 'Check if a product component has in deactive state');

        $intDeactiveContracts = PrimitivesResultGet($resResult, 'intDeactiveContracts');

        return (boolean) $intDeactiveContracts;
    }

    /**
    * Disable the Parental control account.
    *
    * @return bool
    */
    public function disable()
    {
        if (!$this->canBeDisabled()) {
            return false;
        }
        if (!$this->prvSetStatus('QUEUED_DEACTIVATE')) {
            return false;
        }
        if (!$this->configureNILicenceKey()) {
            return false;
        }
        if(!$this->setNIAccountStatus('off')) {
            return false;
        }
        if (!$this->cancelContract()) {
            return false;
        }
        if (!$this->prvSetStatus('DEACTIVE')) {
            return false;
        }

        return true;
    }

    /**
     * Destroy product component
     *
     * @param  array Array of optional arguments; required to match
     *               the method's signature in base class.
     *
     * @return bool
     */
    public function destroy($arrArgs = array())
    {
        if (!$this->canBeDestroyed()) {
            return false;
        }

        if (!$this->prvSetStatus('QUEUED_DESTROY')) {
            return false;
        }

        if (!$this->configureNILicenceKey()) {
                return false;
        }

        if(!$this->setNIAccountStatus('off')) {
            return false;
        }

        if (!$this->cancelContract()) {
            return false;
        }

        if (!$this->prvSetStatus('DESTROYED')) {
            return false;
        }

        return true;
    }

    /**
     * checks whether the account can be disable
     *
     * @return bool
     */
    public function canBeDisabled() {

        $strStatusHandle  = $this->getStatusHandleFromID($this->getStatusID());

        if (true === in_array($strStatusHandle, array('ACTIVE', 'QUEUED_DEACTIVATE'))) {

            return true;
        }
        else {

            return false;
        }

    }

    /**
     * checks whether the account can be disable
     *
     * @return bool
     */
    public function canBeDestroyed() {

        $strStatusHandle  = $this->getStatusHandleFromID($this->getStatusID());

        if (true === in_array($strStatusHandle, array('ACTIVE', 'QUEUED_DEACTIVATE', 'QUEUED_DESTROY'))){

            return true;

        }else{

            return false;
        }

    }

    /**
    * Renews the Parental control account.
    *
    *
    * @access public
    * @return bool
    */
    public function renewAccount()
    {
        if (!$this->canBeRenewed()) {
            return false;
        }
        if(!$this->renewNIAccount()) {
            return false;
        }
        if (!$this->renewContract()) {
            return false;
        }
        $this->prvSetStatus('ACTIVE');

        return true;
    }

    /**
     * checks whether the account can be renewed
     *
     * @access
     * @return bool
     */
    public function canBeRenewed()
    {
        $strStatusHandle  = $this->getStatusHandleFromID($this->getStatusID());

        if (false === in_array($strStatusHandle, array('DESTROYED', 'UNCONFIGURED'))) {

            return true;
        }
        else {
            return false;
        }
    }

    /**
     * Check user has licence key, if not get a new licence key and assign to user else use existing licence key.
     *
     * @return bool
     */
    public function configureNILicenceKey()
    {
        $strQuery = "SELECT pclk.vchLicencekey as strLicenceKey
                          FROM dbProductComponents.tblParentalControlLicenceKeys pclk
                          WHERE pclk.intProductComponentInstanceId = '{$this->getProductComponentInstanceID()}'";
        $dbhConnection = get_named_connection_with_db('dbProductComponents');

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);
        $arrResult = PrimitivesResultGet($resResult);

        if (0 == count($arrResult)) {

                $strQuery = "UPDATE dbProductComponents.tblParentalControlLicenceKeys
                             SET intProductComponentInstanceId = '{$this->getProductComponentInstanceID()}'
                                   WHERE intProductComponentInstanceId IS NULL LIMIT 1";
                if (!PrimitivesQueryOrExit($strQuery, $dbhConnection, 'Update the component status')) {
                    return false;
                }
                $strQuery = "SELECT vchLicencekey as strLicenceKey
                                 FROM dbProductComponents.tblParentalControlLicenceKeys
                                 WHERE intProductComponentInstanceId = '{$this->getProductComponentInstanceID()}'";

                $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);
                $arrResult = PrimitivesResultGet($resResult);

                if (empty($arrResult)) {
                    throw new Exception('No Licence key available in tblParentalControlLicenceKeys table');
                }
                $this->strLicenceKey = $arrResult['strLicenceKey'];
        }
        else {
            $this->strLicenceKey = $arrResult['strLicenceKey'];
        }
        return true;
    }

    /**
     * Insert entries into  config table
     *
     * @return bool
     */
    protected function initConfig()
    {
        if (0 <> (int)$this->intParentalControlConfigId) {

            return false;
        }
        if (0 == $this->getProductComponentInstanceID() || false == is_numeric($this->getProductComponentInstanceID())) {

            return false;
        }

        if (false == $this->__construct($this->getProductComponentInstanceID())) {
            $strQuery = " INSERT INTO userdata.tblConfigParentalControl
                          SET intProductComponentInstanceID = '{$this->getProductComponentInstanceID()}',
                              vchPassword = '{$this->getPassword()}',
                              stmLastUpdate =NOW()";

            $dbhConnection = get_named_connection_with_db('userdata');
            $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

            $this->intParentalControlConfigId = PrimitivesInsertIdGet($dbhConnection);

            return true;
        }

        return false;
    }

    /**
     * Return password
     *
     * @return string
     */
    public  function getPassword()
    {
        return $this->strPassword;
    }

    /**
     * Create a account in Net intelligence by calling API
     *
     * @return bool
     */
    protected function createNIAccount()
    {
        $strNTResponse = '';

        $arrService = userdata_service_get($this->getServiceID());
        $arrUser = userdata_user_get($arrService['user_id']);

        $this->objNetInterface->clear();
        $this->objNetInterface->setFileName($this->arrFileNames['strCreateAccount']);

        $this->objNetInterface->addParam('provider_key', self::PROVIDER_KEY);
        $this->objNetInterface->addParam('provider_package_code', self::PROVIDER_PACKAGE_CODE);
        $this->objNetInterface->addParam('licence_key', $this->getLicenceKey());
        $this->objNetInterface->addParam('licence_type', self::LICENCE_TYPE);

        if (false != $this->validateUserName($arrUser['email'])) {
            $this->objNetInterface->addParam('username', $arrUser['email']);
        }

        if (false != $this->validateEmailAddress($arrUser['email'])) {
            $this->objNetInterface->addParam('email', $arrUser['email']);
        }

        $this->objNetInterface->addParam('days', self::DAYS);

        $strNTResponse = $this->objNetInterface->execute();

        if (!$this->isNIErrorOccured($strNTResponse)) {

            $arrNeededList = array( 'account_id', 'username', 'password', 'licence_key', 'portal_url');
            $arrResponseList = $this->getInArrayFormat($arrNeededList, $strNTResponse);

            if ('' == trim($arrResponseList['password']) || ('' == trim($arrResponseList['licence_key']))) {
                throw new Exception('Missing password/licence key from Netintelligence');
            }

            $this->strPassword = $arrResponseList['password'];
            $this->strLicenceKey = $arrResponseList['licence_key'];

            return true;
        }

        return false;
    }

    /**
     * Renew an account in Net intelligence by calling API
     *
     * @return bool
     */
    public function renewNIAccount()
    {
        $strNIResponse = '';

        $arrService = userdata_service_get($this->getServiceID());
        $arrUser = userdata_user_get($arrService['user_id']);

        $this->objNetInterface->clear();
        $this->objNetInterface->setFileName($this->arrFileNames['strCreateAccount']);

        $this->objNetInterface->addParam('provider_key', self::PROVIDER_KEY);
        $this->objNetInterface->addParam('provider_package_code', self::PROVIDER_PACKAGE_CODE);
        $this->objNetInterface->addParam('licence_key', $this->getLicenceKey());
        $this->objNetInterface->addParam('days', self::DAYS);
        $this->objNetInterface->addParam('incr', $this->getIncr());

        $strNIResponse = $this->objNetInterface->execute();

        if ($this->isNIErrorOccured($strNIResponse)) {
            return false;
        }
        else {
            return true;
        }
    }

    /**
     * Set the Netintelligence status to on/off
     *
     * @param string $strStatus Status on/off
     * @return bool
     */
    public function setNIAccountStatus($strStatus)
    {
        $strNTResponse = '';

        $this->objNetInterface->clear();
        $this->objNetInterface->setFileName($this->arrFileNames['strSetAccountStatus']);

        $this->objNetInterface->addParam('provider_key', self::PROVIDER_KEY);
        $this->objNetInterface->addParam('licence_key', $this->getLicenceKey());
        $this->objNetInterface->addParam('status',  $strStatus);

        $strNTResponse = $this->objNetInterface->execute();

        if ($this->isNIErrorOccured($strNTResponse)) {
            return false;
        }
        else {
            return true;
        }
    }

    /**
     * Retrun licence key
     *
     * @return string
     */
    public function getLicenceKey()
    {
        return $this->strLicenceKey;
    }

    /**
     * Retrun incr value.
     *
     * @return string
     */
    public function getIncr()
    {
        return $this->strIncr;
    }

    /**
     * Parse Net intelligence response and check error occured or not
     *
     * @param string $strNTResponse Net intelligence response in XML format
     * @return bool
     */
    protected function isNIErrorOccured($strNTResponse)
    {
        $bolErrorStatus = false;
        $objSimpleXMLElement = null;

        if (isset($strNTResponse)) {
            $objSimpleXMLElement = new SimpleXMLElement($strNTResponse);

            if(isset($objSimpleXMLElement->code)) {
                if (($objSimpleXMLElement->code >= 400) && ($objSimpleXMLElement->code <= 500))

                    throw new Exception($objSimpleXMLElement->desc, (int)$objSimpleXMLElement->code);
            }
        }

        return $bolErrorStatus;
    }

    /**
     * Parse the XML data and store it into array
     *
     * @param array $arrNeededList Array containing data need to be parsed
     * @param array $strNIResponse Net intelligence response in XML format
     * @return array List containing parsed data
     */
    protected function getInArrayFormat($arrNeededList, $strNIResponse)
    {
        $arrResponseList = array();
        $objSimpleXMLElement = new SimpleXMLElement($strNIResponse);

        foreach ($arrNeededList as $strValue) {
            $arrResponseList[$strValue] = $objSimpleXMLElement->$strValue;
        }

        return $arrResponseList;

    }

    /**
     * Get the status of the component
     *
     */
    public function getState()
    {
        if ($this->getServiceComponentID() == COMPONENT_PARENTAL_CONTROL) {

            if (true == in_array($this->getStatus(),$this->getActivationNeededStatuses())) {

                return self::STATE_INACTIVE;
            }
            elseif ($this->getStatus() == 'ACTIVE') {

                return self::STATE_ACTIVE;
            }
            else {
                return self::STATE_NEVER_INSTALLED ;
            }
        }
        return self::STATE_UNDEFINED ;
    }

    /**
     * Return an array of activation needed component statuses
     *
     * @return array
     */
    public function getActivationNeededStatuses()
    {
        $arrStatuses = array(
            'DEACTIVE',
            'QUEUED_DEACTIVATE',
            'QUEUED_REACTIVATE'
            );

        return $arrStatuses;
    }

    /**
     * Validates the given string with regular expression for E-mail.
     *
     * @param string $strEmailAddress Email address
     * @return bool
     */
    public function validateEmailAddress($strEmailAddress)
    {
        $strEmailAddress = trim($strEmailAddress);

        if (preg_match('/^[-a-zA-Z0-9_.]{1,}@[-a-zA-Z0-9_.]{1,}.[-a-zA-Z0-9_.]{2,}$/', $strEmailAddress)) {

            return true;
        }
        else {
            return false;
        }
    }

    /**
     * vaidate the parameter up to 50 characters (including the characters ' @ . - and whitespace)
     *
     * @param string $strUserName User name
     * @return bool
     */
    public function validateUserName($strUserName)
    {
        $strUserName = trim($strUserName);

        if (preg_match('/^[-a-zA-Z0-9_.]{1,}@[-a-zA-Z0-9_.]{1,}.[-a-zA-Z0-9_.]{2,}$/', $strUserName) && ((5 <= strlen($strUserName)) && (128 >= strlen($strUserName)))) {

            return true;
        }
        else {
            return false;
        }

    }

    /**
     * Check where the parameter is included in the array
     *
     * @param string $strIncr Opitional parameter passed to Net intelligence.
     * @return bool
     */
    public function validateIncr($strIncr)
    {
        if (in_array($strIncr, array('now','start','end','contig'))) {

            return true;
        }
    }

}

