<?php
	
	/**
	* Access library for PlusTalk component.  Contains component configurator and component class
	* 
	* 
	*
	* @package    Core
	* @subpackage VoIP 
	* @access     public
	* <AUTHOR> <<EMAIL>>
	* @version    $Id: config-plustalk-access.inc,v 1.9 2007-07-25 06:38:48 rjones Exp $
	* @filesource
	*/

	require_once('/local/data/mis/database/database_libraries/components/component-defines.inc');
	require_once('/local/data/mis/database/database_libraries/sql_primitives.inc');
	require_once('/local/data/mis/database/database_libraries/components/CComponent.inc');


	/* Globally executed code, make configurator entries for all PlusTalk configs */

	$dbhConn = get_named_connection_with_db('unprivileged_reporting');

	$strQuery = "SELECT scp.intServiceComponentID
	               FROM products.tblServiceComponentProduct scp
	         INNER JOIN products.tblServiceComponentProductType scpt
	                 ON scp.intServiceComponentProductTypeID = scpt.intServiceComponentProductTypeID
	              WHERE scpt.vchHandle = 'PLUSTALK'";

	$refResult = PrimitivesQueryOrExit($strQuery, $dbhConn,'Make plustalk configurator entries');
	$arrPlusTalkConfigurators = PrimitivesResultsAsArrayGet($refResult);

	foreach($arrPlusTalkConfigurators as $arrConfigurator)
	{
		$arrConfigurator['intServiceComponentID'] = $arrConfigurator['intServiceComponentID'] * 1;
		$global_component_configurators[$arrConfigurator['intServiceComponentID']] =
		                               "config_plustalk_configurator";
	}

	// Hack to insert the component configurator array into PHP5's global scope
	// if it's not already there
	if(!isset($GLOBALS['global_component_configurators']))
	{
		$GLOBALS['global_component_configurators'] = $global_component_configurators;
	}
	else
	{
		foreach($global_component_configurators as $intIndex => $strConfigurator)
		{
			$GLOBALS['global_component_configurators'][$intIndex] = $strConfigurator;
		}
	}

	unset($arrPlusTalkConfigurators);


	/**
	* Component configurator
	*
	* Follows standard component configurator patter.  Do not modify name or param list.
	*
	* @access public
	* @global
	* <AUTHOR>
	* @param integer component id
	* @param string action
	* @return integer -1 if the component does not exist, otherwise the result of the action performed
	*/
	function config_plustalk_configurator ($intComponentID, $strAction) 
	{
		// Retrieve wifi components

		$objPlusTalkProduct = CComponent::createInstance($intComponentID); 

		if(!$objPlusTalkProduct)
		{
			return (-1);
		}

		switch ($strAction) 
		{
		    case "auto_configure":
				//Do nothing - the product should be added unconfigured.
				break;
			
		    case "auto_enable":
				return $objPlusTalkProduct->enable();
				break;

		    case "auto_disable":
				return $objPlusTalkProduct->disable();
				break;
				
		    case "auto_destroy":
				return $objPlusTalkProduct->destroy();
				break;

		    case "auto_refresh":
		    	return $objPlusTalkProduct->refreshCurrentState();
				break;

		    default:
				//Not a supported action
				return (-1);
				break;
		}
	}


?>
