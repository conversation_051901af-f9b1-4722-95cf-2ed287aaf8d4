<?php
/**
 * FILE HEADER
 *
 * @package LegacyCodebase
 * <AUTHOR> <<EMAIL>>
 */

/**
 * Write or update a problem
 *
 * @param array $array_problem Associative array of problem info.
 *
 * @return integer
 **/
function split_pt_problem_write($array_problem)
{
    $old_problem = array();

    if (isset($array_problem['problem_id']) && $array_problem['problem_id'] > 0) {
        $old_problem = pt_problem_get($array_problem['problem_id']);

        // P 61571. We need to copy over the owner id and fix stage from tblProblemFixHistory when an
        // auto problem has its priority automatically raised
        if (!isset($array_problem['owner_id'])) {
            $array_problem['owner_id'] = $old_problem['owner_id'];
        }
        if (!isset($array_problem['fixstage_id'])) {
            $array_problem['fixstage_id'] = $old_problem['fixstage_id'];
        }
        if ($old_problem['owner_id']!= $array_problem['owner_id']) {
            $array_problem['touched'] = '0000-00-00 00:00:00';
        }
        if (!isset($old_problem['highest_priority_id'])) {
            $old_problem['highest_priority_id'] = 5;
        }
    }

    $intProblemFixStageId = isset($array_problem['fixstage_id']) ? intval($array_problem['fixstage_id']) : 0;
    unset($array_problem['fixstage_id']);

    //check if priority changed and keep the highest ever in the highest_priority_id field
    if (!empty($old_problem)
        && (
            ($array_problem['priority_id'] < $old_problem['highest_priority_id'])
            || $old_problem['highest_priority_id'] == null
        )
    ) {

        $array_problem['highest_priority_id'] = $array_problem['priority_id'];
    }

    // Which fields supplied could be NULL legitimately?
    // This lookup table helps out with determining whether we need to escape some data, or set it to
    // the word "NULL" when we find empty fields
    $nullableFields = array(
        'priority_id',
        'highest_priority_id',
        'auto_problem_id'
    );

    $validation = array(
        'problem_id'            => '^[0-9]+$',
        'problem_name'          => '^.*$',
        'problem_description'   => '^.*',
        'owner_id'              => '^[a-z0-9]{32}|group:[0-9]+$',
        'incident_owner_id'     => '^[a-z0-9]{32}|group:[0-9]+$',
        'status_id'             => '^[0-9]+$',
        'problem_type'          => '^[a-zA-Z]+$',
        'problem_group_id'      => '^[0-9]+$',
        'associate_project_id'  => '^[0-9]+$',
        'intSystemId'           => '^[0-9]+$',
        'priority_id'           => '^[0-9]+$|^$',
        'highest_priority_id'   => '^[0-9]+$|^$',
        'created_by'            => '^[a-z0-9]{32}$',
        'date_eta'              => '^([0-9]{4}-[0-9]{1,2}-[0-9]{1,2}( [0-9]{1,2}(:[0-9]{1,2}(:[0-9]{1,2})?)?)?)?$',
        'date_closed'           => '^([0-9]{4}-[0-9]{1,2}-[0-9]{1,2}( [0-9]{1,2}(:[0-9]{1,2}(:[0-9]{1,2})?)?)?)?$',
        'touched'               => '^([0-9]{4}-[0-9]{1,2}-[0-9]{1,2}( [0-9]{1,2}(:[0-9]{1,2}(:[0-9]{1,2})?)?)?)?$',
        'auto_problem_id'       => '^[0-9]+$'
    );

    // project needs to be set only if intDispositionId is 3 ("Project assigned")
    if (isset($array_problem['intDispositionId']) && ctype_digit($array_problem['intDispositionId'])) {
        $validation['intDispositionId'] = '^[0-9]+$';
        $validation['associate_project_id'] = '^[0-9]+$';

        $arrDisposition = pt_disposition_get($array_problem['intDispositionId']);

        if (trim(strtolower($arrDisposition['strDispositionName'])) != "project assigned") {
            $array_problem['associate_project_id'] = 0;
        }
    } else {
        unset($array_problem['intDispositionId']);
        unset($validation['associate_project_id']);
    }

    $dbhConnection = get_named_connection_with_db('project_tool');

    $problem_id = null;
    if (isset($array_problem['problem_id']) && $array_problem['problem_id'] > 0) {
        $problem_id = $array_problem['problem_id'];
    }

    $reason_id = null;
    if (isset($array_problem['reason_id']) && !empty($array_problem['reason_id'])) {
        $reason_id = $array_problem['reason_id'];
    }
    unset($array_problem['reason_id']);

    if ($problem_id) {
        $array_updates = array();
        $auto_problem_id = '';

        foreach ($array_problem as $key => $value) {
            if (array_key_exists($key, $validation)) {
                // Failure will trigger a halt!
                ppw_validate_field($key, $array_problem, $validation);
            }

            if ($value != '') {
                $array_updates[] = "$key='".addslashes($value)."'";
            } else {
                $array_updates[] = "$key=NULL";
            }
        }

        $strUpdates = join(',', $array_updates);
        $strSQL = "UPDATE pt_problem SET {$strUpdates} WHERE problem_id=$problem_id";

        PrimitivesQueryOrExit($strSQL, $dbhConnection);

        if ($array_problem['problem_description'] != $old_problem['problem_description']) {
            $intProblemId       = (int) $array_problem['problem_id'];
            $strOwnerId         = $old_problem['owner_id'];
            $strProbDescription = addslashes($old_problem['problem_description']);
            $strDateCreated     = $old_problem['date_created'];

            $strSQL = <<<EOQ
INSERT INTO tblPortalProblemDescription (
    intProblemId,
    vchUserId,
    txtDescription,
    dtmCreated,
    stmLastUpdate
) VALUES (
    $intProblemId,
    '$strOwnerId',
    '$strProbDescription',
    '$strDateCreated',
    NOW()
)
EOQ;

            PrimitivesQueryOrExit($strSQL, $dbhConnection);
        }
    } else {
        ppw_validate_field('problem_name', $array_problem, $validation);
        ppw_validate_field('problem_description', $array_problem, $validation);
        ppw_validate_field('owner_id', $array_problem, $validation);
        ppw_validate_field('status_id', $array_problem, $validation);
        ppw_validate_field('priority_id', $array_problem, $validation);
        ppw_validate_field('intSystemId', $array_problem, $validation);
        ppw_validate_field('created_by', $array_problem, $validation);
        ppw_validate_field('date_eta', $array_problem, $validation);
        ppw_validate_field('date_closed', $array_problem, $validation);
        ppw_validate_field('touched', $array_problem, $validation);

        if (isset($array_problem['incident_owner_id'])) {
            ppw_validate_field('incident_owner_id', $array_problem, $validation);
        }

        if (isset($array_problem['problem_type'])) {
            ppw_validate_field('problem_type', $array_problem, $validation);
        }

        if (isset($array_problem['problem_group_id'])) {
            ppw_validate_field('problem_group_id', $array_problem, $validation);
        }

        if (isset($array_problem['intDispositionId'])) {
            ppw_validate_field('intDispositionId', $array_problem, $validation);
            ppw_validate_field('associate_project_id', $array_problem, $validation);

            //  If disposition is not "Project assigned", there shouldnt be a project
            if ($array_problem['intDispositionId'] != 3) {
                $array_problem['associate_project_id'] = 0;
            }
        } else {
            unset($array_problem['intDispositionId']);
            unset($validation['associate_project_id']);
        }

        if (isset($array_problem['auto_problem_id'])) {
            ppw_validate_field('auto_problem_id', $array_problem, $validation);
        } else {
            $array_problem['auto_problem_id'] = '';
        }

        // DJM2013: this is a nasty "convert array elements into variables" hack...
        foreach ($array_problem as $key => $value) {
            $$key = ($value == '' && in_array($key, $nullableFields)) ? 'NULL' : addslashes($value);
        }

        $projectFields = '';
        $projectValues = '';

        if (isset($array_problem['intDispositionId'])) {
            $projectFields = 'intDispositionId, associate_project_id,';
            $projectValues = "{$array_problem['intDispositionId']} , {$array_problem['associate_project_id']},";
        }

        $strSQL = <<<EOQ
INSERT INTO pt_problem (
    problem_name,
    problem_description,
    owner_id,
    incident_owner_id,
    problem_type,
    problem_group_id,
    entity_owner,
    status_id,
    intSystemId,
    $projectFields
    priority_id,
    highest_priority_id,
    created_by,
    date_created,
    date_eta,
    date_closed,
    worked_on,
    touched,
    auto_problem_id
) VALUES (
    '$problem_name',
    '$problem_description',
    '$owner_id',
    '$incident_owner_id',
    '$problem_type',
    '$problem_group_id',
    '$entity_owner',
    $status_id,
    $intSystemId,
    $projectValues
    $priority_id,
    $priority_id,
    '$created_by',
    NOW(),
    '$date_eta',
    '$date_closed',
    'no',
    '0000-00-00 00:00:00',
    $auto_problem_id
)
EOQ;

        PrimitivesQueryOrExit($strSQL, $dbhConnection);
        $problem_id = PrimitivesInsertIdGet($dbhConnection);
    }

    if (!is_null($reason_id)) {
        UpdateProblemReason($problem_id, $reason_id);
    }

    if (empty($old_problem)
        || (
            ($old_problem['fixstage_id'] != $intProblemFixStageId)                   // the fix stage has changed
            || ($old_problem['owner_id'] != $array_problem['owner_id'])              // the ownership has changed
            || (isset($array_problem['priority_id'])
                    && $old_problem['priority_id'] != $array_problem['priority_id']) // the priority has changed
        )
    ) {
        // Odds are that isset() will handle most of our queries, but for unapproved problems,
        // array_key_exists() will catch NULL values.
        if (isset($array_problem['priority_id']) || array_key_exists('priority_id', $array_problem)) {
            $priorityId = $array_problem['priority_id'];
        } else {
            $priorityId = $old_problem['priority_id'];
        }

        $strProblemPriority = ($priorityId == '') ? 'NULL' : mysql_real_escape_string($priorityId);

        $strQuery = "INSERT INTO tblProblemFixHistory "
                  . " (intProblemId, intProblemFixStageId, intPriority, vchOwner, dtmAdded ) "
                  . " VALUES ($problem_id, $intProblemFixStageId, {$strProblemPriority}, "
                  . "'" . mysql_real_escape_string($array_problem['owner_id']) . "' , NOW() )";
        PrimitivesQueryOrExit($strQuery, $dbhConnection);
    }

    // Send an SMS to an internal alerter for P1 issues: this is only needed outside working hours
    if (isset($array_problem['priority_id'])
        && $array_problem['priority_id'] == 1
        && (
            !isset($old_problem['priority_id'])
            || (isset($old_problem['priority_id'])
            && $old_problem['priority_id'] != 1)
        )
    ) {
        $uxtNow = time();

        if (!ProblemTool_Utility::get()->isDuringOfficeHours($uxtNow)
            || I18n_Date::isHoliday($uxtNow)
            || I18n_Date::isWeekend($uxtNow)
        ) {
            // DJM2013/76437: the Framework auto-loader may need to be pulled in
            require_once('/local/codebase2005/modules/Framework/Libraries/bootstrap.inc.php');

            $sms = new PomsClient_Sms();
            $sms->sendP1RaisedAlert($problem_id);
        }
    }

    // Clear the cache used by the Workplace information.html page
    // As the cache file could potentially be owned by a different user, we treat this as a fire+forget action, given
    // that the cache file is refreshed every 60 seconds anyway...
    if (defined('SMARTY_CACHE_DIR')) {
        $cache_file = SMARTY_CACHE_DIR . 'problem_list.cache';
        if (file_exists($cache_file) && is_writable($cache_file)) {
            unlink($cache_file);
        }
    }

    return $problem_id;
}

/**
 * Simple wrapper function to pt_validate() - note that pt_validate() will die() if the validation fails
 * Also note that the arrays are passed by reference to this function to reduce memory overheads
 *
 * @param str   $field_name   the field name/key - should be the same in both the data array and the regex array
 * @param array &$data_array  the array which holds the data to be validated
 * @param array &$regex_array the array which holds the regexs used to validate the data
 *
 * @return bool
 */
function ppw_validate_field($field_name, &$data_array, &$regex_array)
{
    if (!isset($regex_array[$field_name])) {
        error_log(
            "Warning: $field_name validation routine not found:\n" .
            get_minimalist_backtrace() . "\n" .
            print_r($regex_array, true)
        );
        return true;
    }

    return pt_validate(
        'pt_problem_write',
        $data_array[$field_name],
        $regex_array[$field_name],
        $field_name
    );
}
