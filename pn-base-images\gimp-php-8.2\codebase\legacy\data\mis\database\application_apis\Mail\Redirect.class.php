<?php
require_once('/local/data/mis/database/application_apis/Mail/Mailbox.class.php');

/**
 * Redirect type of Mail_Mailbox class
 */
class Mail_Redirect extends Mail_Mailbox
{
    /**
     * @var array relevant fields from config_email for type redirect
     */
    protected $_arrFields = array('email_id', 'component_id','type', 'isp',
                                  'username', 'login', 'redirect_to', 'status');

    /**
     * Changes status of the mailbox to active and adds entries to maildb
     *
     * @throws Mail_Exception
     */
    public function enable()
    {
        if('queued-activate' !== $this->status) {
            throw new Mail_Exception("Trying to enable redirect with status $this->status");
        }

        try {
            $this->_objDb->begin('userdata', 'mailauth', 'maildelivery');

            //create mailbox in mail db and update status
            $this->_objMailApi->createRedirect($this->redirect_to);
            $this->status = 'active';
            $this->save();
			$this->_objDb->commit('userdata', 'mailauth', 'maildelivery');
			$this->emailMailboxChanged('redirect_added', $this->login);
        } catch (Mail_Exception $e) {
            $this->_objDb->rollback('userdata', 'mailauth', 'maildelivery');
            throw new Mail_Exception("Enabling redirect {$this->__toString()} failed");
        }
    }

    /**
     * Destroys component
     *
     * @throws Mail_Exception
     */
    public function destroy($bolDestroyAll = false /* unused - added to satisfy interface */)
    {
        $this->status = 'queued-destroy';
        $this->save();
        try {
            $this->_objDb->begin('userdata', 'mailauth', 'maildelivery');

            $this->_objMailApi->destroyMailbox();
            $this->status = 'destroyed';
            $this->save();
			$this->emailMailboxChanged('redirect_deleted', $this->login);
            $this->_objDb->commit('userdata', 'mailauth', 'maildelivery');
        } catch (Mail_Exception $e) {
            $this->_objDb->rollback('userdata', 'mailauth', 'maildelivery');
            throw new Mail_Exception("Destroying redirect {$this->__toString()} failed");
        }
    }

    /**
     * Changes redirect_to for this redirect in userdata.config_email and Mail Db
     *
     * @param string $strRedirectTo
     */
    public function changeRedirectTo($strRedirectTo)
    {
        try {
            $this->_objDb->begin('userdata', 'mailauth', 'maildelivery');

            $this->_objMailApi->changeRedirectTo($strRedirectTo);
            $strQuery = "UPDATE config_email SET redirect_to = '$strRedirectTo' "
                      . "WHERE email_id = $this->email_id";
            $this->_objDb->query($strQuery, 'userdata');
            $this->redirect_to = $strRedirectTo;

            $this->_objDb->commit('userdata', 'mailauth', 'maildelivery');
        } catch (Mail_Exception $e) {
            $this->_objDb->rollback('userdata', 'mailauth', 'maildelivery');
            throw new Mail_Exception("Changing redirect {$this->__toString()} failed");
        }
    }
}
