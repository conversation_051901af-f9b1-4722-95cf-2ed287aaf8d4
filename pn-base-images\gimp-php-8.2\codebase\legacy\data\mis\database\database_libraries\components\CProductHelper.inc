<?php

/**
* ProductHelper
*
* Utility functions related to the CProduct class
*
* @access     public
* <AUTHOR> <<EMAIL>>
*/
class CProductHelper
{
    private static $selfInstance;

    /**
     * setter used to mock the class for testing purposes
     *
     * @param integer $instance Instance object to use.
     * @return void
     */
    public static function setInstance($instance)
    {
        self::$selfInstance = $instance;
    }

    /**
     * Get<PERSON> used to mock class for testing purposes
     * @return CProductHelper CProductHelper instance
     */
    public static function getInstance()
    {
        if (self::$selfInstance == null) {
            self::$selfInstance = new CProductHelper();
        }
        return self::$selfInstance;
    }

    /**
     * Gets a Product handle based on Service ID
     *
     * <AUTHOR> <PERSON>" <<EMAIL>>
     * @param    integer $intServiceId Service ID for handle to retrieve
     *
     * @return   Object  CProduct component
     */
    public function getProductTypeHandleFromServiceId($intServiceId)
    {
        $dbhRead = get_named_connection_with_db('userdata');

        $strQuery="SELECT vchHandle \n" .
            "  FROM userdata.components com \n" .
            "    INNER JOIN products.service_components sc \n" .
            "      ON com.component_type_id = sc.service_component_id \n" .
            "    INNER JOIN products.tblServiceComponentProduct scp \n" .
            "      ON sc.service_component_id = scp.intServiceComponentID \n" .
            "    INNER JOIN products.tblServiceComponentProductType scpt \n" .
            "      ON scp.intServiceComponentProductTypeID = scpt.intServiceComponentProductTypeID \n" .
            "    WHERE com.service_id = '{$intServiceId}' \n" .
            "    AND com.status NOT IN ('queued-destroy', 'destroyed') \n" .
            "  ORDER BY com.timestamp DESC";

        $refResult = PrimitivesQueryOrExit(
            $strQuery,
            $dbhRead,
            "Fetch default product handle for service id '$intServiceId'",
            false
        );

        $arrHandle = PrimitivesResultsAsListGet($refResult);

        return $arrHandle;
    }

    /**
     * Gets a Component Product based on Service ID and Product Type Handle
     * If there are multiple products, the newest one will be returned
     *
     * <AUTHOR> Jones" <<EMAIL>>
     * @access   public
     *
     * @param integer $intServiceId         Service ID
     * @param string  $strProductTypeHandle Product Type Handle
     * @param string  $strStatus            (Optional) Status of the product component
     *
     * @return CComponent|boolean A CProduct component (or boolean false on fail)
     */
    public function getProductByProductTypeHandle($intServiceId, $strProductTypeHandle, $strStatus = null)
    {
        $objComponent = false; // Prevent nasty return problems

        $dbhRead = get_named_connection_with_db('userdata');
        if (isset($strStatus)) {
            $statusSql = "AND com.status = '".PrimitivesRealEscapeString($strStatus, $dbhRead)."'";
        } else {
            $statusSql = " AND com.status NOT IN ('queued-destroy', 'destroyed')";
        }

        $strGetComponent = "SELECT com.component_id AS intComponentId \n" .
            "  FROM components com \n" .
            "    INNER JOIN products.service_components sc \n" .
            "      ON com.component_type_id = sc.service_component_id \n" .
            "    INNER JOIN products.tblServiceComponentProduct scp \n" .
            "      ON sc.service_component_id = scp.intServiceComponentID \n" .
            "    INNER JOIN products.tblServiceComponentProductType scpt \n" .
            "      ON scp.intServiceComponentProductTypeID = scpt.intServiceComponentProductTypeID \n" .
            "  WHERE com.service_id = '{$intServiceId}' \n" .
            "    AND scpt.vchHandle = '{$strProductTypeHandle}' \n" .
            $statusSql . "\n" .
            "  ORDER BY com.timestamp DESC";
        if (false !== ($resGetComponent = PrimitivesQueryOrExit(
            $strGetComponent,
            $dbhRead,
            'CComponent::getComponentFromService',
            false
        ))) {
            if (PrimitivesNumRowsGet($resGetComponent) > 0) {
                $intComponentId = PrimitivesResultGet($resGetComponent, 'intComponentId');

                $objComponent = CComponent::createInstance($intComponentId);
            }
        }

        return $objComponent;
    }
}
