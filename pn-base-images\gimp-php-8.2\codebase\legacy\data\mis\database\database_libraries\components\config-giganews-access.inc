<?php

/////////////////////////////////////////////////////////////////////
// File:     config-giganews-acces.inc
// Purpose:  Access mini-library for giganews MAAF accounts
/////////////////////////////////////////////////////////////////////

$global_component_configurators[COMPONENT_MAAF_GIGANEWS] = "configure_giganews_configurator";


/////////////////////////////////////////////////////////////////////
// Configurator functions

function config_giganews_auto_configure($intComponentId)
{
	$arrComponent = userdata_component_get($intComponentId);

	switch($arrComponent['status'])
	{
		case 'destroyed':
		case 'unconfigured':
			$objAccount = Brightview_Account::findByServiceId($arrComponent['service_id']);
			if($objAccount) {
				$objGiganewsAccount = Brightview_GiganewsAccount::create($objAccount->getMailId());
				userdata_component_set_status($intComponentId, 'active');
			}
			break;
	}
} // function config_giganews_auto_configure


function config_giganews_auto_destroy($component_id)
{
	$component = userdata_component_get($component_id);

	switch ($component["status"])
	{
		// Free pass to "destroyed"
		case "unconfigured":
			userdata_component_set_status($component_id, "destroyed");
			break;

			// Have or may have configuration
		case "active":
		case "deactive":
		case "queued-destroy":

			userdata_component_set_status($component_id, 'queued-destroy');

			$objAccount = Brightview_Account::findByServiceId($component['service_id']);

			if(($objAccount instanceof Brightview_Account) && $objAccount->hasGiganews()) {

				$objGigaNewsAccount = $objAccount->getGiganewsAccount();

				if ($objGigaNewsAccount instanceof Brightview_GiganewsAccount) {

					$objGigaNewsAccount->destroy();
					userdata_component_set_status($component_id, 'destroyed');
				}
			} else {
				// Problem: 63885 If the service is 'queued-destroy', then we can safely remove the
				// Giganews component as well, since we need to cleanup the
				// components before the service is destroyed.
				$service = userdata_service_get($component['service_id']);
				if ($service['status'] == 'queued-destroy') {
					userdata_component_set_status($component_id, 'destroyed');
				}
			}
			break;

			// Shouldn't be here
		case "queued-reactivate":
		case "queued-deactivate":
		case "queued-activate":
		case "queued-deconfigure":
			break;

			// Already there
		case "destroyed":
			break;
		default:
			// The sky is falling!
			break;
	}

} // function config_giganews_auto_destroy


function config_giganews_auto_refresh($intComponentId)
{

} // function config_giganews_auto_refresh


function config_giganews_auto_disable($component_id, $portalname)
{

} // function config_giganews_auto_disable


function config_giganews_auto_enable($component_id)
{

} // function config_giganews_auto_enable


function configure_giganews_configurator($component_id, $action)
{
	switch ($action)
	{
		case "auto_configure":
			config_giganews_auto_configure($component_id);
			break;

		case "auto_disable":
			config_giganews_auto_disable($component_id);
			break;

		case "auto_enable":
			config_giganews_auto_enable($component_id);
			break;

		case "auto_destroy":
			config_giganews_auto_destroy($component_id);
			break;

		case "auto_refresh":
			config_giganews_auto_refresh($component_id);
			break;

		default:
			break;
	}

} // function configure_giganews_configurator


// Configurator functions
/////////////////////////////////////////////////////////////////////

?>
