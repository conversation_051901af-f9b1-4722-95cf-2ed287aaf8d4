<?php

/**
* BTHardwareBundle class
* used by the hardware classes to Place Or Cancel Orders
*
* @param m_strClassPrefix
* @param m_strSupplierTag
*
* @package    DatabaseAccessCommon
* @subpackage application_apis
* @access     public
* <AUTHOR>
*
*/
class BTHardwareBundle extends HardwareBundle
{
    //
    // PRIVATE Members
    //

    /**
    * The Class Prefix
    *
    * A String that is the prefix of this class
    *
    * @access private
    * @var    String
    */
    var $m_strClassPrefix = 'BT';

    /**
    * The Supplier Tag
    *
    * A String that is this suppliers tag
    *
    * @access private
    * @var    String
    */
    var $m_strSupplierTag = 'BT';

    //
    // Constructor
    //

    /**
    * BTHardwareBundle constructor
    *
    * The constructor method for this class
    *
    * @return void
    */

    function BTHardwareBundle()
    {
        $this->HardwareBundle();
    }

    // Handle order placing.
    /**
     * Handle order placing.
     *
     * @see HardwareBundle::PlaceOrder()
     *
     * @return boolean
     */
    function PlaceOrder()
    {
        // Just queue it.
        return $this->PlacePendingOrder();
    }

    //
    // Protected Methods
    //

    /**
     * DestroyBundleItems
     *
     * @param int $intHardwareBundleItemID HardwareBundleItemID
     *
     * @return void
     */
    function DestroyBundleItems($intHardwareBundleItemID = 0)
    {
        $intBundleID = $this->m_intID;

        $dbhConnection = get_named_connection_with_db('userdata');

        $strSQL = "DELETE FROM hardware_bundle_items WHERE config_hardware_bundle_id = '$intBundleID'";

        if ($intHardwareBundleItemID > 0) {
            $strSQL .= " AND hardware_bundle_item_id = '$intHardwareBundleItemID' ";
        }

        PrimitivesQueryOrExit($strSQL, $dbhConnection);
    }

    /**
     * GetOrderDetails
     *
     * @return void
     */
    function GetOrderDetails()
    {
        $datDate          = date('d/m/Y');
        $rService         = $this->GetService();
        $rUser            = $this->GetUser();
        $rDeliveryAddress = $this->GetDeliveryAddress();
        $rBillingAddress  = $this->GetBillingAddress();
        $intServiceID     = intval($rService['service_id']);
        $intBundleID      = $this->GetHardwareBundleID();
        $intPartnerID     = $this->GetPartnerID();
        $arrItems         = $this->m_arrItems;
        $arrStatus        = $this->m_arrStatuses;

        //if this is an RMA, only order the items that have been requested
        if ($this->m_strOverallStatusTag=='awaiting_rma') {
            $arrRmaBundle = array();
            foreach ($arrItems as $item) {
                if ($item['strStatusTag'] == 'rma_in_progress') {
                    array_push($arrRmaBundle, $item);
                }
            }
            $arrItems = null;
            $arrItems = $arrRmaBundle;
            $this->m_arrItems = $arrRmaBundle;
            $strStatusTag = $this->m_strOverallStatusTag;
        } else {
            $strStatusTag = key($arrStatus);
        }

        // No rogue commas
        $strFullName = str_replace(",", "", $rUser['salutation'] . ' ' . $rUser['forenames'] . ' ' . $rUser['surname']);
        $strAddress1 = str_replace(",", "-", $rDeliveryAddress['house']);
        $strAddress2 = str_replace(",", "-", $rDeliveryAddress['street']);
        $strAddress3 = str_replace(",", "-", $rDeliveryAddress['town']);
        $strAddress4 = str_replace(",", "-", $rDeliveryAddress['county']);
        $strPostcode = str_replace(",", "-", $rDeliveryAddress['postcode']);

        $strOptionalNotes = '';
        $intOrderCount = 1;
        foreach ($arrItems as $rItem) {
            $intProductID = sprintf("%06d", $rItem['strProductCode']);
            $intQuantity  = $rItem['intQuantity'];

            $arrOrder[$intOrderCount]['strFullName']      = $strFullName;
            $arrOrder[$intOrderCount]['strAddress1']      = $strAddress1;
            $arrOrder[$intOrderCount]['strAddress2']      = $strAddress2;
            $arrOrder[$intOrderCount]['strAddress3']      = $strAddress3;
            $arrOrder[$intOrderCount]['strAddress4']      = $strAddress4;
            $arrOrder[$intOrderCount]['strPostcode']      = $strPostcode;
            $arrOrder[$intOrderCount]['intPartnerID']     = $intPartnerID;
            $arrOrder[$intOrderCount]['intOrderID']       = $intBundleID;
            $arrOrder[$intOrderCount]['strOptionalNotes'] = '';
            $arrOrder[$intOrderCount]['datDate']          = $datDate;
            $arrOrder[$intOrderCount]['intProductID']     = $intProductID;
            $arrOrder[$intOrderCount]['intQuantity']      = $intQuantity;
            $arrOrder[$intOrderCount]['strStatusTag']     = $strStatusTag;
            //For rmas, also pass the item id so that only those items are updated
            if ($this->m_strOverallStatusTag=='awaiting_rma') {
                $arrOrder[$intOrderCount]['intID']          =    $rItem['intID'];
            }

            $intOrderCount++;
        }

        return $arrOrder;
    }

    /**
     * AddJiffy
     *
     * @param int    $intAddJiffy AddJiffy
     * @param int    $intRMAID    RMAID
     * @param string $bag_type    bag type
     *
     * @return void
     */
    function AddJiffy($intAddJiffy, $intRMAID, $bag_type)
    {
        $intBundleID = $this->m_intID;

        if ($intAddJiffy == 1) {
            $strHandle = $bag_type[1][0];
        } elseif ($intAddJiffy == 14) {
            $strHandle = $bag_type[0][0];
        } else {
            die("Could not find jiffy bag type");
        }
        if($strHandle !== null) {
            $dbhConnection = get_named_connection_with_db('product');

            $strSQL = "SELECT hardware_supplier_product_id FROM hardware_supplier_products WHERE tag = '$strHandle'";

            $resResult = PrimitivesQueryOrExit($strSQL, $dbhConnection);

            $arrResults = PrimitivesResultsAsArrayGet($resResult);

            $intHWProductID = $arrResults[0]['hardware_supplier_product_id'];
            $dbhConnection = get_named_connection_with_db('userdata');

            $strSQL = 'INSERT INTO hardware_bundle_items ' .
                '(config_hardware_bundle_id, hardware_order_status_id, ' .
                'hardware_supplier_product_id, when_last_updated, quantity) ' .
                "VALUES('$intBundleID', '$intRMAID', '$intHWProductID', NOW(), 1)";

            PrimitivesQueryOrExit($strSQL, $dbhConnection);
        }
    }

    /**
     * AddPowerlineAdapters
     *
     *
     * @return void
     */
    function AddPowerlineAdapters()
    {
        $intBundleID = $this->m_intID;

        $dbhConnection = get_named_connection_with_db('product');

        $strSQL = "SELECT hardware_supplier_product_id FROM hardware_supplier_products WHERE tag = 'POWERLINE_ADAPTER'";

        $resResult = PrimitivesQueryOrExit($strSQL, $dbhConnection);

        $arrResults = PrimitivesResultsAsArrayGet($resResult);

        $intHWProductID = $arrResults[0]['hardware_supplier_product_id'];
        $dbhConnection = get_named_connection_with_db('userdata');

        $strSQL = 'INSERT INTO hardware_bundle_items '.
                  '(config_hardware_bundle_id, hardware_order_status_id, '.
                  'hardware_supplier_product_id, when_last_updated, quantity) '.
                  "VALUES('$intBundleID', 10, '$intHWProductID', NOW(), 1)";

        PrimitivesQueryOrExit($strSQL, $dbhConnection);
    }

    /**
     * PollForOrderUpdate
     *
     * @see HardwareBundle::PollForOrderUpdate()
     *
     * @return void
     */
    function PollForOrderUpdate()
    {
        $strClassName = $this->m_strClassPrefix . 'HardwareBundleSupplier';

        // Find out what the current status of the order is.
        $Supplier = new $strClassName();

        $this->setMessage(date('H:i:s')." Fetching Latest OrderStatus\n");

        $arrNewStatus = $Supplier->FetchLatestOrderStatus($this);

        if (empty($arrNewStatus)) {
            $this->setMessage(date('H:i:s')." Latest Order Status empty.\n");
            return false;
        }

        // Should we be doing this?
        switch ($this->GetStatusTag()) {
            case 'not_ordered':
            case 'rejected':
            case 'cancelled':
                $this->setMessage(date('H:i:s')." No need to update component, already in a final state\n");
                // No need to update component, already in a final state,
                return false;
                break;
        }

        $strNewStatus = $arrNewStatus['strStatus'];

        $this->m_strTrackingOrderNumber = $arrNewStatus['strPostal'];

        $strCurrentStatus = $this->GetStatusTag();
        // Make sure we've been returned a sensible status, as we don't
        // want to mess this component up.
        if ($strNewStatus != '' && $strNewStatus != false) {

            $this->setSerialNumbers($arrNewStatus);
            //Update the TR069 Details
            $this->updateTr069Details($arrNewStatus);

            //P14073 - multiple hardware updates
            //Only update the status if it has been changed
            if ($strNewStatus != $strCurrentStatus) {
                // Update the object
                $this->SetStatusTag(-1, $strNewStatus);

                $this->SetWhenLastUpdated();

                // Set the When_Dispatched flag
                $this->SetWhenDispatched();

                // Trigger hardware dispatch email & SMS for TV orders
                $isTVOrder = false;
                // Trigger hardware dispatch email & SMS for router orders
                $isRouterOrder = false;
                foreach ($this->m_arrItems as $item) {
                    switch ($item['intHardwareProductClassTag']) {
                        case 'tv_box':
                        case 'network_adapter':
                            $isTVOrder = true;
                            break;
                        default:
                            if (stristr($item['intHardwareProductClassTag'], 'router')) {
                                $isRouterOrder = true;
                            }
                    }
                }

                if ($isTVOrder) {
                    $this->SendTVHardwareDispatchEmail();
                }

                if ($isRouterOrder) {
                    $this->SendRouterDispatchEmail();
                }

                // Save the new info
                $binResult = $this->Commit();

                if ($binResult == true) {
                    // If the new status is 'delivered' set the component to 'active'
                    if ($strNewStatus == 'delivered') {
                        userdata_component_set_status($this->m_intComponentID, 'active');
                    }

                    $contactMessage = "Status of hardware order changed from '$strCurrentStatus' to '$strNewStatus'";

                    $this->setMessage(
                        date('H:i:s')." Status of hardware order changed from ".$strCurrentStatus." to ".
                        $strNewStatus."\n"
                    );
                    $this->AddContact($contactMessage);
                } else {
                    $this->setMessage(date('H:i:s')." Failed while saving the changes to database\n");
                }
            }
        } else if ($strNewStatus === false) {
            $this->setMessage(date('H:i:s')." Unable to get a reply from the supplier. Not unexpected\n");
            // We were unable to get a reply from the supplier. Not
            // unexpected, so just return false silently.
            return false;
        } else {
            $this->setMessage(date('H:i:s')." Decoding the response from the supplier resulted in a blank status\n");
            // We shouldn't be here. All replies from the supplier should be recognisable, so we return false. Other
            // code will handle this as though the supplier just didn't reply.
            // DJM: 16/10/2012: error logging removed as it was generating too much output in the error log
            // As per above, this error condition will be handled elsewhere in the code

            return false;
        }
    }

    function setSerialNumbers($update)
    {
        foreach ($update['items'] as $feedback) {
                $serialNumber = $feedback['strSerialNumber'];
                foreach ($this->m_arrItems as $index => $item) {
                    if ($item['strProductCode'] == $feedback['vchItemID']) {
                        $this->m_arrItems[$index]['strSerialNumber'] = $serialNumber;
                    }
                }
                $this->MarkModified();
        }
    }

    /**
     * Actually Cancel the order
     *
     * Cancel the order as per suppliers requirements
     *
     * @access protected
     * @return void
     */
    function DoCancelOrder()
    {
        //@todo - Cancel the order as per suppliers requirements

        //Update the component with the correct status
        userdata_component_set_status($this->m_intComponentID, 'destroyed');
    }

    /**
     * Send tv hardware dispatch email.
     *
     * @return void
     */
    function SendTVHardwareDispatchEmail()
    {
        require_once '/local/www/database-admin/scripts/tv/functions.inc';

        $emailTemplateHandle = 'tv-hardware-dispatch-confirmation.txt';
        $smsTemplateHandle = 'tv-hardware-dispatch-confirmation';
        $templateData = array('trackingLink' => $this->m_strTrackingOrderNumber);

        if ($this->m_binInSync == false) {

            foreach ($this->m_arrItems as $currentItem) {
                $strHarwareType = $currentItem['intHardwareProductClassTag'];

                if ($strHarwareType == 'tv_box') {
                    $templateData["boxDispatched"] = true;
                    
                    if (strrpos($currentItem['strDisplayName'], 'YouView+') !== false) {
                        $templateData["boxType"] = 'YouView+';
                    } else {
                        $templateData["boxType"] = 'YouView';
                    }
                }

                if ($strHarwareType == 'network_adapter') {
                    $templateData["powerlineDispatched"] = true;
                }
            }

            $templateData["dispatchDate"] = time();
            EmailHandler::sendEmail($this->GetServiceID(), $emailTemplateHandle, $templateData);
            sendSms($this->GetServiceID(), $smsTemplateHandle, $templateData);
        }
    }

    /**
     * Send router dispatch email.
     *
     * @return void
     */
    function SendRouterDispatchEmail()
    {
        $emailTemplateHandle = 'router-dispatch-confirmation.txt';

        $serviceId = $this->GetServiceID();

        $objCoreService = new Core_Service($serviceId);
        $objServiceDefinition = new Core_ServiceDefinition($objCoreService->getType());

        if ($objServiceDefinition->getType() == 'business') {
            $emailTemplateHandle = 'router-dispatch-confirmation-business.txt';
        }

        $objOutboundMessageClient = new Sms_OutboundMessageClient();

        $isps = array(
            'plus.net',
            'johnlewis',
            'partner'
        );

        if ($this->m_binInSync == false && in_array($objCoreService->getIsp(), $isps)) {
            $templateData["hasMobile"] = false;
            $templateData["serviceStatus"] = $objCoreService->getStatus();

            if ($objOutboundMessageClient->getCustomerMobileNumber($serviceId)) {
                $templateData["hasMobile"] = true;
            }

            $templateData["routerName"] = '';

            foreach ($this->m_arrItems as $currentItem) {
                if (stristr($currentItem['intHardwareProductClassTag'], 'router')) {
                    $templateData["routerName"] = $currentItem['strDisplayName'];
                }
            }

            $templateData["dispatchDate"] = time();
            EmailHandler::sendEmail($serviceId, $emailTemplateHandle, $templateData);
        }
    }
}
