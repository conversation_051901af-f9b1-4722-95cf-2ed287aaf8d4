<?php
/**
* This class declares the Wifi Component
* 
* Class was extracted from config-wifi-access.inc file on 24/01/2007
*
* @package Core
* @access  public
* @version    $Id: CWifiComponent.inc,v 1.3 2007-08-22 15:42:19 kprzybyszewski Exp $
* 
*/

require_once '/local/data/mis/database/application_apis/CPNI18NCurrency/CPNI18NCurrency.php';

class CWifiComponent
{
	var $m_intComponentID           = 0;
	var $m_intWifiConfigID          = 0;
	var $m_intServiceComponentID    = 0;
	var $m_intServiceID             = 0;
	var $m_strStatus                = 'unconfigured';
	var $m_intActivationInvoiceID   = 0;
	var $m_intCurrentTariffID       = 0;
	var $m_bolInitialActivationPerformed = 0;
	var $m_strTariffDisplayName     = '';
	var $m_strTariffModel           = '';
	var $m_intDialupGroupID         = '';
	var $m_strTariffTypeDisplayName = '';
	var $m_strTariffTypeHandle      = '';
	var $m_strComponentName         = '';
	var $m_uxtNextInvoiceDate       = **********;
	var $m_intBillingDay            = 0;
	var $m_uxtScheduledDestructionDate    = 0;
	var $m_intScheduledDestructionEventID = 0;

	var $m_objError = null;

	////////////////
	// Constructors
	////////////////

	
	/**
	* Contructor for the CWifiComponent class
	* Load a component thingy
	* 
	* @param int The component ID
	*
	*/
	function bolOnBaseAccount()
	{
		$refConnection = get_named_connection_with_db('product');
// Nigel test me with xwfi2b
		$strQuery ="SELECT if(count(*) > 0 ,1,0) as bolOnBaseAccount 
		            FROM userdata.services s 
			         inner join userdata.components c on c.service_id = s.service_id 
				 inner join products.tblComponentWIFIConfig cwc 
				       on cwc.intServiceComponentID = c.component_type_id
				       AND s.type = cwc.intBaseProductServiceDefinitionID
				 WHERE c.component_id = '".$this->GetWorkplaceComponentID()."'";

		$refResult = PrimitivesQueryOrExit($strQuery,$refConnection,'Make dialup configurator entries');
		$bolOnBaseAccount = PrimitivesResultGet($refResult,'bolOnBaseAccount');
	
		return $bolOnBaseAccount;

	} // bolOnBaseAccount

	function CWifiComponent($intComponentID)
	{
		// Check Component exists and is of the correct type
		$refConnection = get_named_connection_with_db('product');
		$strQuery="SELECT service_id as intServiceID,
		                  sc.name as strComponentName,
		                  component_id as intComponentID, 
		                  cw.intWifiConfigID, cw.bolInitialActivationPerformed, 
		                  if(cw.intActivationInvoiceID is null, 0, cw.intActivationInvoiceID) as intActivationInvoiceID, 
		                  cwc.intServiceComponentID,
		                  UNIX_TIMESTAMP(cw.dtmNextInvoiceDate) as uxtNextInvoiceDate,
		                  cw.intBillingDay as intBillingDay,
		                  status as strStatus,
		                  t.intTariffID as intCurrentTariffID,
		                  t.vchDisplayName  as strTariffDisplayName,
		                  tt.vchHandle      as strTariffTypeHandle,
		                  tt.vchDisplayName as strTariffTypeDisplayName, 
		                  cwc.vchTariffModel as strTariffModel,
		                  cwc.intDialupGroupID as intDialupGroupID
		             FROM userdata.components uc
		       INNER JOIN products.service_components sc
		               ON sc.service_component_id = uc.component_type_id 
		       INNER JOIN products.tblComponentWIFIConfig cwc
		               ON sc.service_component_id = cwc.intServiceComponentID
		        LEFT JOIN userdata.tblConfigWifi cw on cw.intWifiConfigID = uc.config_id
		              AND cw.intComponentID = uc.component_id
		        LEFT JOIN products.tblTariff t on cw.intCurrentTariffID = t.intTariffID 
		        LEFT JOIN products.tblTariffType tt on tt.intTariffTypeID = t.intTariffTypeID 
		            WHERE cwc.vchTariffModel !='INVALID'
		              AND uc.component_id = '$intComponentID'";


		$refResult = PrimitivesQueryOrExit($strQuery,$refConnection,'Make dialup configurator entries');
		$arrComponentDetails = PrimitivesResultGet($refResult);
		
		if((!$arrComponentDetails) || count($arrComponentDetails) < 1)
		{
			return(false);	
		}		


		if( (!isset($arrComponentDetails['intComponentID']) ) || $arrComponentDetails['intComponentID'] <1)
		{
			return(false);	
		}

		$arrComponentDetails['intComponentID'] = intval($arrComponentDetails['intComponentID']);
		$this->m_intComponentID = $arrComponentDetails['intComponentID'];
		

		if( (!isset($arrComponentDetails['intServiceID']) ) || $arrComponentDetails['intServiceID'] <1)
		{
			return(false);	
		}

		$this->m_intServiceComponentID = $arrComponentDetails['intServiceComponentID'];
		$this->m_strComponentName      = $arrComponentDetails['strComponentName'];
		
		$arrComponentDetails['intServiceID'] = intval($arrComponentDetails['intServiceID']);
		$this->m_intServiceID = $arrComponentDetails['intServiceID'];

		$this->prvRetrieveActiveCancelationEvent();

		// Create Config entry if needed
		if($arrComponentDetails['intWifiConfigID'] < 1)
		{
			$strQuery = "INSERT INTO userdata.tblConfigWifi set intComponentID = {$this->m_intComponentID} ";

			PrimitivesQueryOrExit($strQuery,$refConnection,'Make dialup configurator entries');
			$this->m_intWifiConfigID = PrimitivesInsertIdGet($refConnection);
			$strQuery = "UPDATE userdata.components set config_id = '$this->m_intWifiConfigID' where component_id = '$this->m_intComponentID' ";

			PrimitivesQueryOrExit($strQuery,$refConnection,'Make dialup configurator entries');
		}
		else
		{
			$this->m_intWifiConfigID        = $arrComponentDetails['intWifiConfigID'];
			$this->m_intActivationInvoiceID = $arrComponentDetails['intActivationInvoiceID'];
		}

		if( (isset($arrComponentDetails['intCurrentTariffID']) ) &&
			$arrComponentDetails['intCurrentTariffID'] > 0)
		{
			$arrComponentDetails['intCurrentTariffID'] = intval($arrComponentDetails['intCurrentTariffID']);
			$this->m_intCurrentTariffID = $arrComponentDetails['intCurrentTariffID'];
		}

		if( (isset($arrComponentDetails['bolInitialActivationPerformed']) ) && $arrComponentDetails['bolInitialActivationPerformed'] > 0)
		{
			$arrComponentDetails['bolInitialActivationPerformed'] = intval($arrComponentDetails['bolInitialActivationPerformed']);
			$this->m_bolInitialActivationPerformed = $arrComponentDetails['bolInitialActivationPerformed'];
		}

		if( (isset($arrComponentDetails['strTariffDisplayName']) ) && $arrComponentDetails['strTariffDisplayName']  !='' )
		{
			$this->m_strTariffDisplayName = $arrComponentDetails['strTariffDisplayName'];
		}

		if( (isset($arrComponentDetails['strTariffModel']) ) && $arrComponentDetails['strTariffModel']  !='' )
		{
			$this->m_strTariffModel = $arrComponentDetails['strTariffModel'];
		}

		if( (isset($arrComponentDetails['strTariffTypeHandle']) ) && $arrComponentDetails['strTariffTypeHandle']  !='' )
		{
			$this->m_strTariffTypeHandle = $arrComponentDetails['strTariffTypeHandle'];
		}
		if( (isset($arrComponentDetails['strTariffTypeDisplayName']) ) && $arrComponentDetails['strTariffTypeDisplayName']  !='' )
		{
			$this->m_strTariffTypeDisplayName = $arrComponentDetails['strTariffTypeDisplayName'];
		}

		$this->m_strStatus = $arrComponentDetails['strStatus'];

		if(isset($arrComponentDetails['uxtNextInvoiceDate']) && $arrComponentDetails['uxtNextInvoiceDate'] > 0)
		{
			$arrComponentDetails['uxtNextInvoiceDate'] = intval($arrComponentDetails['uxtNextInvoiceDate']);
			$this->m_uxtNextInvoiceDate = $arrComponentDetails['uxtNextInvoiceDate'];
		}

		if(isset($arrComponentDetails['intBillingDay']) && $arrComponentDetails['intBillingDay'] > 0)
		{
			$arrComponentDetails['intBillingDay'] = intval($arrComponentDetails['intBillingDay']);
			$this->m_intBillingDay = $arrComponentDetails['intBillingDay'];
		}

		if(isset($arrComponentDetails['intDialupGroupID']) && $arrComponentDetails['intDialupGroupID'] > 0)
		{
			$this->m_intDialupGroupID = $arrComponentDetails['intDialupGroupID'];
		}
	
		return true;
	}

	//////////////
	// Accessors
	//////////////

	/**
	* Returns the component ID
	*
	* @return int component ID
	*/
	function GetWorkplaceComponentID()
	{
		return($this->m_intComponentID);
	} 


	/**
	* Returns the component type (ServiceComponentID)
	*
	* @return int component ID
	*/
	function GetComponentTypeID()
	{
		return $this->m_intServiceComponentID;
	} // func: GetComponentTypeID 

	/**
	* TODO: no idea what this is for...
	*
	* @returns boolean true. Always. 
	*/
	function isValid()
	{
		// TODO add calls to audit functions
		return true;
	}

	/**
	* return the billing day if wifi subscription 
	*
	* @returns integer 
	*/

	function BillingDayGet()
	{
		return $this->m_intBillingDay;

	} // func: BillingDayGet

	/**
	*
	* 
	* @return CError Member object for recording errors
	*/
	function GetError()
	{
		return $this->m_objError;
	}


	function prvIncrementWifiNextInvoiceDate()
	{
		// Don't allow the timestamp to wrap
		if($this->m_uxtNextInvoiceDate >=**********)
		{
			return;
		}

		$refConnection = get_named_connection_with_db('userdata');
		$strYearMonth = date('Y-m',$this->m_uxtNextInvoiceDate)."-".$this->m_intBillingDay;
		
		$strQuery="SELECT UNIX_TIMESTAMP(DATE_ADD('$strYearMonth', INTERVAL 1 MONTH)) as uxtNextInvoiceDate ";

		$refResult = PrimitivesQueryOrExit($strQuery,$refConnection,'Get next billing day');
		$uxtNextInvoiceDate = PrimitivesResultGet($refResult,'uxtNextInvoiceDate');

		$strQuery = "UPDATE userdata.tblConfigWifi 
		             SET dtmNextInvoiceDate = from_unixtime({$uxtNextInvoiceDate}) 
			     WHERE intWifiConfigID = '".$this->GetConfigID()."' 
			     AND intComponentID = '".$this->GetWorkplaceComponentID()."'";

		$refResult = PrimitivesQueryOrExit($strQuery,$refConnection,'Get next billing day');

		// Set services next invoice date

		if($this->bolOnBaseAccount())
		{

			$strQuery = "UPDATE userdata.services set next_invoice = from_unixtime({$uxtNextInvoiceDate}) WHERE service_id = '{$this->m_intServiceID}'";
			$refResult = PrimitivesQueryOrExit($strQuery,$refConnection,'Get next billing day');
		} 
		
		$this->m_uxtNextInvoiceDate = $uxtNextInvoiceDate;

		return $uxtNextInvoiceDate;

	} // func: prvIncrementWifiNextInvoiceDate 

	/**
	* Returns the next invoice date of the Wifi component
	*
	* @return int Next invoice date in unixtime format
	*/
	function GetNextWifiBillingDate($intMonthOffset =0,$uxtTimeNow = 0)
	{
		// The wifi billing day is tied to the main account billing day
		// wifi is allways paid monthly however
	
		$arrService = userdata_service_get($this->m_intServiceID);
		$intBillingDay = $arrService['invoice_day'];

		if($uxtTimeNow < 1)
		{
			$uxtTimeNow  = time();
		}

		$uxtBaseTime = $uxtTimeNow;

		if($this->m_uxtNextInvoiceDate > 0 && $this->m_uxtNextInvoiceDate < ********** && $this->m_uxtNextInvoiceDate > $uxtTimeNow)	
		{
			$uxtBaseTime = $this->m_uxtNextInvoiceDate;;
		}

		$strYearMonth = date('Y-m',$uxtBaseTime);

		$intToday = intval(date('j',$uxtBaseTime));

		if ($intBillingDay < $intToday)
		{
			$intMonthOffset++;
		}

		$refConnection = get_named_connection_with_db('userdata');
		$strQuery = "SELECT UNIX_TIMESTAMP(DATE_ADD(CONCAT('$strYearMonth', '-', '$intBillingDay'), INTERVAL $intMonthOffset MONTH)) as uxtNextBilling";

		$refResult = PrimitivesQueryOrExit($strQuery,$refConnection,'Get next billing day');
		return PrimitivesResultGet($refResult,'uxtNextBilling');

	} // func: GetNextWifiBillingDate 

	function GetNextInvoiceDate()
	{
		return($this->m_uxtNextInvoiceDate);
	} // func: GetNextInvoiceDate

	/**
	* Returns the component status eg active, destroyed
	*
	* @return str Status string
	*/
	function GetStatus()
	{
		return($this->m_strStatus);
	}


	/**
	*
	*
	*
	*
	*/
	function IsScheduledForDestruction()
	{
		return ($this->m_intScheduledDestructionEventID == 0) ? false : true;
	}

	/**
	* Returns the current tariff ID of the component
	*
	* @return int
	*/
	function GetCurrentTariffID()
	{
		return($this->m_intCurrentTariffID);
	}

	/**
	*
	*
	*/
	function prvGetWifiServiceURL()
	{
		return $this->strWifiSOAPURL;
	} 


	/**
	*
	*
	*/
	function GetTariffName()
	{
		return $this->m_strTariffDisplayName;
	} 
	
	/**
	*
	*
	*/
	function GetTariffModel()
	{
		return $this->m_strTariffModel;
	} 

	function GetTariffTypeHandle()
	{
		return $this->m_strTariffTypeHandle;
	}


	function GetTariffTypeDisplayName()
	{
		return $this->m_strTariffTypeDisplayName;
	}

	/////////////////////
	// Static Methods
	////////////////////

	/**
	 * Is this component a wifi component?
	 *
	 * Is this component a wifi component
	 * TODO PHPDoc description for bolWifiComponent
	 *
	 * <code>
	 * $bolShow = CWifiComponent::bolWifiComponent ($intComponentID);
	 *
	 * TODO complete example for method bolWifiComponent
	 * </code>
	 *
	 * @access public
	 * @static
	 * 
	 * @param  int The component ID
	 *
	 * @return bol true or false
	 * @throws none none
	 *
	 */
	
	function bolWifiComponent($intComponentID)
	{
		static $arrWifiBaseAccounts = array();

		if(count($arrWifiBaseAccounts) > 0)
		{
			return $arrWifiBaseAccounts;
		}
		
		$dbConnection = get_named_connection_with_db('userdata');
		
		$intComponentID = addslashes(($intComponentID*1));
		
		$strQuery ="SELECT count(*) as intWifiComponent
		              FROM components c
		        INNER JOIN products.tblComponentWIFIConfig w
		                ON w.intServiceComponentID = c.component_type_id
		             WHERE c.component_id ='$intComponentID'";

		$resResult        = PrimitivesQueryOrExit($strQuery, $dbConnection);
		$intWifiComponent = PrimitivesResultGet($resResult,'intWifiComponent');

		return $intWifiComponent;

	} 



	/**
	 * Get a list of the WIFI Component types
	 *
	 * Get a simple array containing the WIFI Service Component
	 * types.
	 *
	 * <code>
	 * $arrShow = CWifiComponent::WifiComponentTypesGet();
	 *
	 * TODO complete example for method bolWifiComponent
	 * </code>
	 *
	 * @access public
	 * @static
	 * 	 
	 * @return array service component ID's
	 * @throws none none
	 *
	 */
		
	public static function WifiComponentTypesGet()
	{
		static $arrComponentType = array();

		if(count($arrComponentType) > 0)
		{
			return $arrComponentType;
		}
		$dbConnection = get_named_connection_with_db('product');
		
		$strQuery ="SELECT DISTINCT intServiceComponentID FROM tblComponentWIFIConfig";

		$resResult        = PrimitivesQueryOrExit($strQuery, $dbConnection);
		$arrComponentType = PrimitivesResultsAsListGet($resResult);

		return $arrComponentType;

	} 

	/**
	 * Get details of wifi component types and their configuration
	 *
	 * Get details of wifi component types and their configuration
	 * 
	 * <code>
	 * $arrShow = CWifiComponent::WifiComponentTypesGet();
	 *
	 * TODO complete example for method bolWifiComponent
	 * </code>
	 *
	 * @access pubilc
	 * @static
	 *
	 * @return array wifi component types and their settings
	 * @throws none none
	 *
	 */
	
	public static function WifiComponentBaseAccountsGet()
	{
		static $arrWifiBaseAccounts = array();

		if(count($arrWifiBaseAccounts) > 0)
		{
			return $arrWifiBaseAccounts;
		}
		
		$dbConnection = get_named_connection_with_db('product');
		
		$strQuery ="SELECT DISTINCT intServiceComponentID, intBaseProductServiceDefinitionID FROM tblComponentWIFIConfig WHERE intBaseProductServiceDefinitionID is not null";

		$resResult           = PrimitivesQueryOrExit($strQuery, $dbConnection);
		$arrWifiBaseAccounts = PrimitivesResultsAsArrayGet($resResult);

		return $arrWifiBaseAccounts;

	} 


	/**
	* Returns the component ID of the first (hopefully only) Wifi component on an account
	*
	* @param int The ServiceID
	* @static
	* @return integer the componentid
	*         boolean false on error
	*/

	public static function GetWifiComponentByService($intServiceID)
	{
		//Wrap this up till I get time to move it into this class.
		return bolWifiAccountHasActiveWifiComponent($intServiceID);
	}

	/**
	* Returns an array of billable wifi components on this service 
	* N.B.  Billable wifi components are NOT just the active ones. A wifi component may be billable
	* regardless of it's state if it has unpaid usage on an expired credit block. Components who's 
	* accounts are in fialed billing don't get billed
	* @param int The
	* @static
	* @return integer the componentid
	*         boolean false on error
	*/

	function GetBillableWifiComponentsByService($intServiceID,$uxtDateOfRun=0)
	{
		if($uxtDateOfRun < 1)
		{
			$uxtDateOfRun = time();
		}

		// Check not already in failed billing, peeps in failed billng don't get billed 
		if(bolFinancialFailedBilling($intServiceID))
		{
			return array();
		}

		$arrUserDetails = userdata_service_get($intServiceID);
		// Get a list of unused and expired credit blocks, 
		// dont try components which havent had any usage
		$strQuery="SELECT time_id 
		           FROM users u  
		                INNER JOIN time_quota t on t.radius_id = u.radius_id and stop_time < from_unixtime($uxtDateOfRun) and t.time_used < 1 
		           WHERE u.username ='{$arrUserDetails['username']}' and u.isp='{$arrUserDetails['isp']}' ";

		$refConnection   = get_named_connection_with_db('radius_reporting');
		$refResult       = PrimitivesQueryOrExit($strQuery, $refConnection,'Get all used and expired blocks from radius');
		$arrRemoteBlocks = PrimitivesResultsAsArrayGet($refResult);

		$arrRemoteBlocks[]=array('time_id' => -1);

		$strUsedBlocks = primitives_NOT_IN_clause('AND','wtb.intRemoteTimeBlockID',$arrRemoteBlocks,'time_id');
		
	// TODO : these HARD CODED magic numbers should be coming 
	//        from the database and referanced by handle but 
	//        I don't have time

	$strCreditBlockTypeIDs = "6,7"; 

		$strQuery ="SELECT cw.intComponentID 
		            FROM userdata.components c 
		                 INNER JOIN userdata.tblConfigWifi cw on cw.intWifiConfigID = c.config_id 
		                 LEFT JOIN userdata.tblWifiTimeBlock wtb on wtb.intWifiConfigID = cw.intWifiConfigID 
		                           AND (wtb.intWifiScheduledPaymentID is null OR wtb.intWifiScheduledPaymentID =0 )
		                           AND (wtb.intSalesInvoiceID is null OR wtb.intSalesInvoiceID = 0) 
		                           AND wtb.intWifiTimeTypeID in ($strCreditBlockTypeIDs)
		                           $strUsedBlocks 

		            WHERE (c.status = 'active' OR wtb.intWifiTimeBlockID is not null  ) 
		                  AND c.service_id ='$intServiceID'
		            UNION DISTINCT
		            SELECT cw.intComponentID
		            FROM userdata.services s 
		                 INNER JOIN userdata.components c on c.service_id = s.service_id
		                 INNER JOIN tblConfigWifi cw on cw.intComponentID = c.component_id
		                 INNER JOIN products.tblComponentWIFIConfig cwc on cwc.intServiceComponentID = c.component_type_id
		                 INNER JOIN products.tblTariff wt on wt.intTariffID = cw.intCurrentTariffID 
		                            AND wt.intAutomaticMinutesPerCycle > 0
		            WHERE 
		                 cw.dtmNextInvoiceDate is not null 
		                 AND cw.dtmNextInvoiceDate != '0000-00-00' 
		                 AND cw.dtmNextInvoiceDate != '9999-09-09' 
		                 AND cw.dtmNextInvoiceDate != '2030-01-01' 
		                 AND cw.dtmNextInvoiceDate <= DATE_ADD(concat(date_format(from_unixtime($uxtDateOfRun),'%Y-%m-%d'),' 23:59:59'), INTERVAL 1 MONTH)
		                 AND s.next_invoice <= DATE_ADD(concat(date_format(from_unixtime($uxtDateOfRun),'%Y-%m-%d'),' 23:59:59'), INTERVAL 0 MONTH)
		                 AND c.status = 'active' 
		                 AND s.status = 'active' 
		                 AND c.service_id ='$intServiceID'
		";

		$refConnection         = get_named_connection_with_db('userdata');
		$refResult             = PrimitivesQueryOrExit($strQuery, $refConnection,'Get all used and expired blocks from radius');
		$arrBillableComponents = PrimitivesResultsAsListGet($refResult);


		return $arrBillableComponents;
	} // func: GetBillableWifiComponentsByService

	
	/**
	* Retrieves all services which need to have their credit blocks renewed.
	*
	* Credit blocks must be renewed the day before billing, so that they are not being used when billing occurs.
	* This static function returns all services which have wifi which is due to be 
	* billed tomorrow, including interim billings. Does not check wifi tarrif, as this must be checked for 
	* changes/cancellation later anyway.
	* 
	* @static
	* <AUTHOR>
	* @param  The date of the billing run that these services will be billed (usually tomorrow's date)
	* @return array All services
	*/

	function ServicesMissingCreditBlock($dteDate)
	{
		// TODO : these HARD CODED magic numbers should be coming 
		//        from the database and referanced by handle but 
		//        I don't have time

		$strCreditBlockTypeIDs = "6,7"; 
		$strQuery ="SELECT s.service_id,c.component_id
			FROM userdata.services s
			INNER JOIN userdata.components c
				ON s.service_id = c.service_id
			INNER JOIN products.tblComponentWIFIConfig pwc
				on pwc.intServiceComponentID = c.component_type_id
			INNER JOIN userdata.tblConfigWifi wc
				ON wc.intComponentID = c.component_id
		        INNER JOIN products.tblTariff wt on wt.intTariffID = wc.intCurrentTariffID AND wt.intMinutesOfCredit >0 
			LEFT  JOIN userdata.tblWifiTimeBlock wtb 
			        ON wtb.intWifiConfigID = wc.intWifiConfigID 
				   AND wtb.dtmStartDate <= '$dteDate 00:00:00' and wtb.dtmEndDate >= '$dteDate 23:59:59'   
				   AND wtb.intWifiTimeTypeID in ($strCreditBlockTypeIDs)
			WHERE c.status not in ('destroyed','unconfigured') 
			      AND wtb.intWifiConfigID is null 
		";

		$dbhUserdata = get_named_connection_with_db('userdata');

		$refResult = PrimitivesQueryOrExit($strQuery, $dbhUserdata);

		$arrResult = PrimitivesResultsAsArrayGet($refResult);

		return $arrResult; 

	} // func: ServicesMissingCreditBlock  

	function GetServicesNeedingCreditBlockShortening($dteDate)
	{
		$strQuery = "
			SELECT s.service_id,c.component_id,s.invoice_day, wtb.dtmStartDate, wtb.dtmEndDate,
			 DATE_ADD(CONCAT(DATE_FORMAT('$dteDate', '%Y-%m-'), s.invoice_day), INTERVAL 0 Month) as dtmCustomerNextBilling, 
			 '$dteDate' as RunDate,  
			 wtb.intWifiTimeBlockID
			FROM userdata.services s
			INNER JOIN userdata.components c
				ON s.service_id = c.service_id
			INNER JOIN products.tblComponentWIFIConfig pwc
				on pwc.intServiceComponentID = c.component_type_id
			INNER JOIN userdata.tblConfigWifi wc
				ON wc.intComponentID = c.component_id
			INNER JOIN userdata.tblWifiTimeBlock wtb
				ON wtb.intWifiConfigID = wc.intWifiConfigID
			INNER JOIN products.vblWifiTimeType wtt
				ON wtt.intWifiTimeTypeID = wtb.intWifiTimeTypeID
				   AND  wtt.vchHandle in ('CREDIT','PROMOTIONAL_CREDIT')
			WHERE s.next_invoice >= DATE_SUB('2005-06-25', INTERVAL 13 MONTH)
				AND s.status not in ('unconfigured','destroyed')
				AND DATE_ADD(CONCAT(DATE_FORMAT('$dteDate', '%Y-%m-'), s.invoice_day), INTERVAL 0 Month) = '$dteDate'
				AND wtb.dtmStartDate < concat(DATE_ADD(CONCAT(DATE_FORMAT('$dteDate', '%Y-%m-'), s.invoice_day), INTERVAL 0 Month),'23:59:59')
				AND wtb.dtmEndDate > concat(DATE_ADD(CONCAT(DATE_FORMAT('$dteDate', '%Y-%m-'), s.invoice_day), INTERVAL 0 Month),'00:00:00')
                        ";


		$dbhUserdata = get_named_connection_with_db('userdata');

		$refResult = PrimitivesQueryOrExit($strQuery, $dbhUserdata);

		$arrResult = PrimitivesResultsAsArrayGet($refResult);

		return $arrResult; 
	}

 


	function GetServicesForCreditBlockRenewal($dteDate)
	{
		$dbConn = get_named_connection ("userdata_reporting");

		if ($dteDate == '')
		{
			$dteDate = date ('Y-m-d');
		}
		
		$dteDate  = addslashes ($dteDate);

		/* Find all accounts who's latest credit is equal too or beyond the next billing date 
		 * 
		 *
		 */

		$strQuery = "SELECT s.service_id,
		                    s.username,
		                    s.user_id,
		                    s.isp,
		                    s.dtmNextInvoiceDate, 
                                    s.next_invoice as next_invoice
		               FROM services s 
		                     INNER JOIN components c
		                                 ON s.service_id = c.service_id 
		                     INNER JOIN products.tblComponentWIFIConfig pwc 
		                     		 on pwc.intServiceComponentID = c.component_type_id
		                     INNER JOIN tblConfigWifi wc 
		                                 ON wc.intComponentID = c.component_id 
		                     INNER JOIN tblWifiTimeBlock wtb 
		                                 ON wtb.intWifiConfigID = wc.intWifiConfigID 
		                                  AND wc.dtmNextInvoiceDate >= wtb.dtmStartDate
		                                  AND wtb.dtmEndDate >= DATE_SUB(wc.dtmNextInvoiceDate,INTERVAL 2 DAY) 
		                   INNER JOIN products.vblWifiTimeType wtt on 
		                      wtt.intWifiTimeTypeID = wtb.intWifiTimeTypeID 
		                      and wtt.vchHandle in ('CREDIT','PROMOTIONAL_CREDIT')
		                
		              WHERE 
		                
		              
		                AND s.next <= '$dteDate'";

		$resResult = mysql_query ($strQuery, $dbConn)
			or report_error (__FILE__, __LINE__, mysql_error ($dbConn));

		while ($recService = mysql_fetch_array ($resResult, MYSQL_ASSOC))
		{
			$arrServices[] = $recService;
		}

		mysql_free_result ($resResult);

		return $arrServices;
	}

	/**
	* Check if a service is a wifi only account
	* 
	* Check if a service is a wifi only account by comparing the 
	* type with that of the base wifi products.
	*
	* <AUTHOR>
	*
	* @access public
	* @static
	* @param  integer service id to check
	* @param  integer service definition id of the service
	*
	* @return true if wifi only account, false if not.
	* @throws none
	*/
	public static function isWifiOnlyAccount($intServiceID, $intServiceDefinitionID)
	{	
		$arrWifiComponentIDs = CWifiComponent::WifiComponentTypesGet();
		$arrBaseWifiProducts = CWifiComponent::WifiComponentBaseAccountsGet();

		$arrWifiComponents = userdata_component_find(array('service_id' => $intServiceID,
					'type'       => $arrWifiComponentIDs,
					'status'     => array('active',
						'queued-activate',
						'queued-reactivate',
						'deactive',
						'queued-deactivate',
						'queued-deconfigure',
						'queued-destroy')), 0, 0);

		foreach($arrBaseWifiProducts as $k => $arrBaseWifiProduct)
		{
			$arrBaseWifiProducts[$k]['intServiceComponentID'] = intval($arrBaseWifiProduct['intServiceComponentID']);
		}

		if (count($arrWifiComponents) > 0)
		{
			foreach($arrWifiComponents as $arrComponent)
			{
				foreach($arrBaseWifiProducts as $arrBaseWifiProduct)
				{
					if( ($arrBaseWifiProduct['intServiceComponentID'] == $arrComponent['component_type_id'] ) && 
							($intServiceDefinitionID == $arrBaseWifiProduct['intBaseProductServiceDefinitionID']) )
					{
						return true;
					}
				}
			}
		}

		return false;
	}



	////////////////////////
	// Public Methods
	////////////////////////
	


	/**
	* 
	*
	* @return float The monthly charge to be taken, including VAT
	*/

	function prvPerformTariffChange(&$objTariffChange)
	{
		if(isset($objTariffChange['dtmDateExecuted']) && $objTariffChange['dtmDateExecuted'] > 0)
		{
			return false;
		}

		$this->m_intCurrentTariffID = $objTariffChange['intNewTariffID'];

		$refConnection = get_named_connection_with_db('userdata');
		$arrTariffChange = $objTariffChange->GetAll();
		$strQuery = "UPDATE userdata.tblConfigWifi set intCurrentTariffID = '{$arrTariffChange['intNewTariffID']}' WHERE ".
		            "intWifiConfigID = '".$this->GetConfigID()."' AND intComponentID = '".$this->GetWorkplaceComponentID()."'";
		
		$result = PrimitivesQueryOrExit($strQuery,$refConnection,
					   'Update the component for record the initial invoice id');

		$intServiceEventID = $this->LogWifiTariffChange();


		$objTariffChange->SetAll(array('intNewComponentID' => $this->m_intComponentID,
		                               'uxtDateExecuted'           => time(),
		                               'usiExecutedServiceEventID' => $intServiceEventID));
		$objTariffChange->Save();
		// Ticket the account
		$arrAvailableTariffs = $this->GetAvailableTariffs(array($arrTariffChange['intNewTariffID']));

		$strTicketText = "Changed Wifi Tariff to ". $arrAvailableTariffs[0]['strTariffDisplayName'];
		tickets_ticket_add('Script', $this->m_intServiceID, 0, 0, 'Closed', 0, $strTicketText);

		return true;

	} // func: prvPerformTariffChange

	function prvComponentTypeForTariff($intTariffID)
	{
		$refConnection = get_named_connection_with_db('product');
	
		$strQuery ="SELECT t.intServiceComponentID,
		                   sc.available
		              FROM products.tblTariff t,
		                   products.service_components sc
		             WHERE t.intTariffID = '$intTariffID'
		               AND t.intServiceComponentID = sc.service_component_id";
		
		$resResult = PrimitivesQueryOrExit($strQuery, $refConnection, 'Find service component id by tariff id');
		
		$arrComponentType = PrimitivesResultGet($resResult);
		
		if($arrComponentType['available'] == 'No')
		{
			//It is an ex component type. It has ceased to be.
			return false;
		}

		return $arrComponentType['intServiceComponentID'];
		
	} // func: prvComponentTypeForTariff

	function prvPerformComponentChange(&$strErrorMessage, &$objTariffChange, $intNewServiceComponentID)
	{
		global $whoami;
		
		//Get new tariff details
		$arrTariffChange = $objTariffChange->GetAll();

		$arrPrepayTimeBandIDs = array();
		if($arrTariffChange['intNewTimeBandID'] != 0)
		{
			$arrPrepayTimeBandIDs = array($arrTariffChange['intNewTimeBandID']);
		}


		$arrNewTariffs = $this->GetAvailableTariffs(array($arrTariffChange['intNewTariffID']),
		                                     array('PREPAY', 'SUBSCRIPTION', 'PAYG'),
		                                     array($intNewServiceComponentID),
						     0,0,
						     $arrPrepayTimeBandIDs);
						     
		$arrNewTariff = $arrNewTariffs[0];

		// Create new component
		$intComponentID = userdata_component_add($this->m_intServiceID, $intNewServiceComponentID, -1, '', 'unconfigured');

		if(intval($intComponentID) > 0)
		{
			// Set tariff
			$objNewWifiComponent = new CWifiComponent($intComponentID);
		
			$strErrorMessage = $objNewWifiComponent->SetInitialTariff($arrTariffChange['intNewTariffID'], $arrTariffChange['intNewTimeBandID']);
			if($strErrorMessage != '')
			{
				return false;
			}
			// Calculate what payment is needed for new tariff

			$strChargeDesc = "Wireless service changed to '".$objNewWifiComponent->GetTariffName()."'";

			if($objNewWifiComponent->GetTariffModel() == 'PREPAY')
			{
				$floBaseCost     = $arrNewTariff['floCostIncVatInPence'] / 100.0;
				$strChargeDesc   = "Wireless Pre-Pay minutes on change to '".$objNewWifiComponent->GetTariffName()."'";
			}
			else
			{
				$floBaseCost     = $arrNewTariff['intBaseCostIncVatInPence'] / 100.0;
				$strChargeDesc   = "Wireless Subscription on change to '".$objNewWifiComponent->GetTariffName()."'";
			}

			//Create a scheduled payment 
			$objScheduledPayment = CWifiScheduledPayment::Create();
			$arrAccount = userdata_account_get_by_service($this->m_intServiceID);
			
			$arrProperties = array('intAccountID'       => $arrAccount['account_id'],
			                       'intComponentID'     => $objNewWifiComponent->m_intComponentID,
								   'strDescription'     => $strChargeDesc,
								   'strActionerID'      => $whoami,
								   'floAmount'          => $floBaseCost,
								   'floOutstanding'     => $floBaseCost,
								   'uxtDateDue'         => time());

			$objScheduledPayment->SetAll($arrProperties);
			$objScheduledPayment->Save();
			
			
			// Enable component
			$strPromoEvent = "SIGNUP:{$arrTariffChange['intNewTariffID']}";

			if($arrTariffChange['intNewTimeBandID'] > 0)
			{
				$strPromoEvent .= ":{$arrTariffChange['intNewTimeBandID']}";
			}

			$intScheduledPaymentID = $objScheduledPayment->GetID();
			
			$strErrorMessage = $objNewWifiComponent->EnableWifiComponent(0, array($strPromoEvent), true, $intScheduledPaymentID);	
			if($strErrorMessage == '')
			{
				
			}
													
			$intExecutedServiceEventID = $this->LogWifiTariffChange();
		
			//  update the schedule account change row to record thew service event
			$objTariffChange->SetAll(array('intNewComponentID' => $objNewWifiComponent->m_intComponentID, 'uxtDateExecuted' => time(), 'usiExecutedServiceEventID' => $intExecutedServiceEventID));
			$objTariffChange->Save();
			

			// Ticket the account
			$strTicketText = "Changed Wifi Tariff to ". $objNewWifiComponent->GetTariffName();
			tickets_ticket_add('Script', $this->m_intServiceID, 0, 0, 'Closed', 0, $strTicketText);
			
			// Commit suicide  :-)
			$strErrorMessage = $this->DestroyWifiComponent(true);
			if($strErrorMessage == '')
			{
				//bork
			}
					
			return $floBaseCost;
		}
		else
		{
			$strErrorMessage = 'Component not added.';
			return false;
		}

				
	} // func: prvPerformComponentChange

	function prvScheduleSubscriptionPayments($uxtDateOfRun,$arrCurrentTariff,&$floTotalChargePounds)
	{
		global $whoami;
		$uxtLastDay =**********;

		$uxtDateOfRun = strtotime(date('Y-m-d',$uxtDateOfRun));

		if( (!isset($arrCurrentTariff['intAutomaticMinutesPerCycle'])) || (!isset($arrCurrentTariff['intBaseCostIncVatInPence']))) 
		{
			$this->prvSetNextInvoiceDate($uxtLastDay);
			return '';
		}
		if ($arrCurrentTariff['intAutomaticMinutesPerCycle'] < 1  && $arrCurrentTariff['intBaseCostIncVatInPence']  < 1)
		{
			$this->prvSetNextInvoiceDate($uxtLastDay);
			return '';
		}

		// Create the subscription time blocks
		if(!($intWifiTimeTypeID = $this->WifiTimeTypeHandleToID('SUBSCRIPTION')))
		{
			$strErrorMessage = _("The correct time band for subscription time could not be found");
			CurrentUser::ClearTargetUser();
			return $strErrorMessage;
		}

		/** 
		 ** Schedule subscription payments until the next Invoice Date
		 ** for Wifi is greather than or equal to the billing run after the current one
		 **
		 **/
		/* We need to schedule new subscription charges if 
		   It's on or after your billing day  and either your owe for wifi charges or 
		   you will own for wifi charges after the next billing run.
		 */
		   
		$uxtNextBillingDate      = strtotime(date('Y-m-d',$this->GetNextWifiBillingDate(0,$uxtDateOfRun)));
		$uxtFollowingBillingDate = strtotime(date('Y-m-d',$this->GetNextWifiBillingDate(1,$uxtDateOfRun)));
		$uxtNextInvoiceDate      = strtotime(date('Y-m-d',$this->GetNextInvoiceDate()));
		   
		$strDateOfRun            = date('Y-m-d',$uxtDateOfRun);
		$strNextBillingDate      = date('Y-m-d',$uxtNextBillingDate);
		$strFollowingBillingDate = date('Y-m-d',$uxtFollowingBillingDate);
		$strNextInvoiceDate      = date('Y-m-d',$uxtNextInvoiceDate);


		$bolScheduleWifiSubscriptionCharges = false;
		if($uxtDateOfRun >= $uxtNextBillingDate	&& 
		    (($uxtNextInvoiceDate <= $uxtDateOfRun) || 
		     ($uxtNextInvoiceDate > $uxtDateOfRun && $uxtNextInvoiceDate >= $uxtNextBillingDate  && $uxtNextInvoiceDate < $uxtFollowingBillingDate)
		    )
		  )
		{
			$bolScheduleWifiSubscriptionCharges = true;
		}

/*
echo("
		if(uxtDateOfRun >= uxtNextBillingDate	&& 
		    ((uxtNextInvoiceDate < uxtDateOfRun) || 
		     (uxtNextInvoiceDate > uxtDateOfRun && $uxtNextInvoiceDate >= uxtNextBillingDate  && uxtNextInvoiceDate < uxtFollowingBillingDate)
		    )
		  ) 

		if($strDateOfRun >= $strNextBillingDate	&& 
		    (($strNextInvoiceDate < $strDateOfRun) || 
		     ($strNextInvoiceDate > $strDateOfRun && $strNextInvoiceDate >= $strNextBillingDate  && $strNextInvoiceDate < $strFollowingBillingDate)
		    )
		  ) 

		  bolScheduleWifiSubscriptionCharges = $bolScheduleWifiSubscriptionCharges

");
*/
	
		if(!$bolScheduleWifiSubscriptionCharges)
		{
			return '';
		}

		// Schedule Up a minmum on one
		// 

// The next line is for debugging and intentionally comented out on live
/*
echo(" Begining to schedule charges:\n".
      "\tuxtNextInvoiceDate = '$strNextInvoiceDate'\n".
      "\tuxtDateOfRun = '$strDateOfRun'\n".
      "\tWifiBillingDate = '$strNextBillingDate'\n".
      "\tWifiBillingDate + 1 = $strFollowingBillingDate\n"
      );
*/
		while($uxtNextInvoiceDate < $uxtFollowingBillingDate)	
		{
			$strErrorMessage ='';
			$arrSubscriptionDates = $this->prvSubscriptionDateCalculation($strErrorMessage,$uxtNextInvoiceDate);

			if($strErrorMessage !='')
			{
				return $strErrorMessage;
			}
			
			$intSubscriptionTimeBlockID = 0;
			if($arrCurrentTariff['intAutomaticMinutesPerCycle'] >0)
			{
				// Create Subscription Block
				if(($intSubscriptionTimeBlockID = $this->prvInsertTimeBlock($strErrorMessage,$intWifiTimeTypeID,
				                                                            $arrCurrentTariff['intAutomaticMinutesPerCycle'],
				                                                            $arrCurrentTariff['intMinutesBeforeExpiry'],
				                                                            0, 0, 0,$this->GetCurrentTariffID(),0,
				                                                            $arrSubscriptionDates['uxtBeginningOfMonth'],
				                                                            $arrSubscriptionDates['uxtEndOfMonth']))===false)
				{
					CurrentUser::ClearTargetUser();
					return $strErrorMessage;
				}
			} // if: minutes to add

			// Schedule Charge
			$intScheduledPaymentID =0;
			if($arrCurrentTariff['intBaseCostIncVatInPence'] >0 )
			{
				// Add subscription charge
				$objCurrency = CPNI18NCurrency::CreateFromDivisorValue($arrCurrentTariff['strCurrencyCode'], 
										       $arrCurrentTariff['intBaseCostIncVatInPence']);
				$floChargePounds       = $objCurrency->GetValueAsFloat();
				$floTotalChargePounds += $floChargePounds;

				//Create a scheduled payment 
				$objScheduledPayment = CWifiScheduledPayment::Create();	
				$arrAccount = userdata_account_get_by_service($this->m_intServiceID);
				$arrProperties = array('intAccountID'       => $arrAccount['account_id'],
				                       'intComponentID'     => $this->m_intComponentID,
				                       'intWifiTimeBlockID' => $intSubscriptionTimeBlockID,
				                       'strDescription'     => CPNI18NCurrency::I18Nsprintf(_('Wireless Broadband Subscription Charge for the 1 month period between %s and %s '),
				                                                                              CPNI18NCurrency::GetShortDate($arrSubscriptionDates['uxtBeginningOfMonth']),
				                                                                              CPNI18NCurrency::GetShortDate($arrSubscriptionDates['uxtEndOfMonth'])
				                                          ),
				                       'strActionerID'           => $whoami,
				                       'floAmount'               => $floChargePounds,
				                       'floOutstanding'          => $floChargePounds,
				                       'uxtDateDue'              => $uxtNextBillingDate);

				$objScheduledPayment->SetAll($arrProperties);
				$objScheduledPayment->Save();
				$intWifiScheduledPaymentID = $objScheduledPayment->GetID();
				$this->prvRecordScheduledPaymentIDOnTimeBlock($intSubscriptionTimeBlockID,$intWifiScheduledPaymentID);

			} // if: Scheduled Payment

			// Create Referral Token
			if($arrCurrentTariff['intReferralValueIncVatInPence'] > 0 )
			{
				// Create Referral Tokens

				$strCreateTokenError = financial_referral_create_wifi_token($this->GetWorkplaceComponentID(), 
				                                                            $arrSubscriptionDates['uxtBeginningOfMonth'],
				                                                            'wifi_subscription_payment',
				                                                            $intWifiScheduledPaymentID,
				                                                            $arrCurrentTariff['intReferralValueIncVatInPence']);


				if($strCreateTokenError['strErrorMessage'] != '')
				{
					$strProblemComment = wordwrap("In ".__FILE__." on line ".__LINE__.
								      " financial_referral_create_wifi_tokens errored creating referral tokens for ".
								      " wifi payments covering 1 month begining '".date('%d/%M/%Y',$arrSubscriptionDates['uxtBeginningOfMonth'])."'".
								      " component_id #".$this->GetWorkplaceComponentID()." after creating payment '$intScheduledPaymentID'".
								      " The error was: $strCreateTokenError\n. This users account will be ".
								      " inconsitent, and requires manual fixing.");

					$strAutoProblemTag = 'referral_creation_fubar';
					$strProblemTitle = 'Creating Referral Tokens Failed During Wifi Charge Scheduling';
					pt_raise_autoproblem($strAutoProblemTag, $strProblemTitle, $strProblemComment, $strProblemComment);
				}
			} // if: attracts referrals

			// Advance Next Invoice Date
			$uxtNextInvoiceDate = strtotime(date('Y-m-d',$this->prvIncrementWifiNextInvoiceDate()));

		} // while less scheduled than billing date

	return '';

	} // func: prvScheduleSubscriptionPayments


	function prvSetNextInvoiceDate($uxtNextInvoiceDate)
	{
		$strDate = date('Y-m-d',$uxtNextInvoiceDate); 
		$refConnection = get_named_connection_with_db('userdata');
		$strQuery = "UPDATE userdata.tblConfigWifi set  dtmNextInvoiceDate = '$strDate' WHERE dtmNextInvoiceDate < '$strDate' AND intWifiConfigID = '".$this->GetConfigID()."'";
		$refResult = PrimitivesQueryOrExit($strQuery, $refConnection, 'Get time type handle from id');
		$this->m_uxtNextInvoiceDate = $uxtNextInvoiceDate;
		return true;
	} // func: prvSetNextInvoiceDate 

	function CreateMissingCreditBlock($uxtDateOfRun=0)
	{
		if($uxtDateOfRun < 1)
		{
			$uxtDateOfRun = time();
		}

		$uxtEndDate = 0;

		//Check for scheduled cancellation events
		$this->prvRetrieveActiveCancelationEvent();
		
		if($this->m_uxtScheduledDestructionDate != 0 &&
		   $this->m_intScheduledDestructionEventID != 0)
		{
			//End Date for new block is one day before cancellation
			$uxtEndDate = $this->m_uxtScheduledDestructionDate;
		
			if($uxtEndDate < $uxtDateOfRun)
			{
				/* Component should already be destroyed,  don't create a credit block*/
				echo("The component was sheduled to be destroyed on '".date('Y-m-d',$uxtEndDate).
				     "' which is earlier than '".date('Y-m-d',$uxtDateOfRun)."' , skipping credit block creation\n");
				return true;
			}
		}
		//Check for scheduled tariff changes - these override cancellations if the tariff change comes first
		$arrScheduledTariffChanges = CWifiScheduledTariffChange::RetrieveCollection($this->m_intComponentID);	

		if (!empty($arrScheduledTariffChanges) )
		{
			//There can never be more than one
			$arrTariffChange = $arrScheduledTariffChanges[0];

			//If there is both a cancellation and a tariff change scheduled, use the earliest
			$uxtEndDate = ($uxtEndDate == 0) ? $arrScheduledTariffChanges[0]['uxtDateDue'] : min($uxtEndDate, $arrScheduledTariffChanges[0]['uxtDateDue']);
			
		}

		//Create a replacement
		if(! $this->prvCreateTimeBlock('CREDIT', $uxtEndDate,0,$uxtDateOfRun) )
		{
			$this->objError = new CError(__FILE__, __LINE__, $strErrorMessage);
			return false;					
		}

		return true;
	} // func: CreateMissingCreditBlock  

	//////////////////////
	// Private Methods
	/////////////////////

	/*
	* 
	*
	*
	*
	* @return integer Base subscription cost in pence
	*/
	function prvGetBaseSubscriptionCost()
	{
		$arrTariff = $this->GetAvailableTariffs(array($this->GetCurrentTariffID()));

		if(count($arrTariff) < 1)	
		{
			$strErrorMessage = "The correct tariff for subscription time could not be found";
			
			$this->objError = new CError(__FILE__, __LINE__, $strErrorMessage);
			return false;
									
		}

		if(count($arrTariff) > 1)	
		{
			$strErrorMessage = "Multiple active tariffs for subscription time";
			$this->objError = new CError(__FILE__, __LINE__, $strErrorMessage);
			return false;
		}
		
		$intBaseCostIncVatInPence = $arrTariff[0]['intBaseCostIncVatInPence'];

		return $intBaseCostIncVatInPence;
	}


	/**
	* Looks in the service_events table for pending cancellations and populates internal variables
	*
	* m_intScheduledDestructionEventID will contain the service_event ID
	* m_uxtScheduledDestructionDate will contain the date for destruction in unixtime format
	* @return boolean No meaning assigned to the result
	*/
	function prvRetrieveActiveCancelationEvent()
	{
		$this->m_intScheduledDestructionEventID = 0; 
		$this->m_uxtScheduledDestructionDate    = 0; 

		$refConnection = get_named_connection_with_db('userdata');
		$strQuery ="SELECT ce.service_event_id,
		                   UNIX_TIMESTAMP(ce.scheduled_date) as uxtScheduledDestructionDate 
		              FROM service_events ce 
		         LEFT JOIN service_events cce on cce.event_type_id  =  '".WIFI_SCHEDULED_CANCELLATION_REVOKED."' 
		               AND ce.service_id = cce.service_id 
		               AND cce.other_event_id  = ce.other_event_id 
		               AND cce.service_event_id > ce.service_event_id 
		             WHERE ce.other_event_id = '{$this->m_intComponentID}' 
		               AND ce.event_type_id = '".WIFI_SCHEDULED_CANCELLATION."' 
		               AND ce.service_id = '{$this->m_intServiceID}' 
		               AND cce.service_event_id IS NULL 
		          ORDER BY ce.service_event_id ASC
		             LIMIT 1";

		$refResult = PrimitivesQueryOrExit($strQuery,$refConnection,'Get destruction date entries');
		$arrEventDetails = PrimitivesResultGet($refResult);


		if((!$arrEventDetails) || count($arrEventDetails) < 1)
		{
			return(true);
		}

		if( (isset($arrEventDetails['service_event_id']) ) &&  $arrEventDetails['service_event_id']  > 0)
		{
			$this->m_intScheduledDestructionEventID = $arrEventDetails['service_event_id']; 
		}

		if( (isset($arrEventDetails['uxtScheduledDestructionDate']) ) &&  $arrEventDetails['uxtScheduledDestructionDate']  > 0)
		{
			$this->m_uxtScheduledDestructionDate = $arrEventDetails['uxtScheduledDestructionDate']; 
		}

		return(true);
	}

	
	/**
	* Looks in the service_events table for this component's activation event
	*
	*/
	function GetActivationEventID()
	{
		$refConnection = get_named_connection_with_db('userdata');
		$strQuery ="SELECT se.service_event_id
		              FROM service_events se 
		             WHERE se.other_event_id = '{$this->m_intComponentID}' 
		               AND se.event_type_id = '".WIFI_COMPONENT_ACTIVATED."' 
		               AND se.service_id = '{$this->m_intServiceID}' 
		          ORDER BY se.service_event_id ASC
		             LIMIT 1";

		$refResult = PrimitivesQueryOrExit($strQuery,$refConnection,'Get activation service event id');
		$arrEventDetails = PrimitivesResultGet($refResult);

		if( (isset($arrEventDetails['service_event_id']) ) &&  $arrEventDetails['service_event_id']  > 0)
		{
			return $arrEventDetails['service_event_id'];
		}
		
		return false;		

	} 


	/**
	* Works out the pro-rata cost and minutes for creating a subscription Wifi component on an ADSL account
	* 
	* Uses today's date for the calculation
	*
	* @access private
	* <AUTHOR>
	* @param  int     intMonthlyCost The monthly cost in pence
	* @param  int     intMonthlyMinutes The number of inclusive minutes
	* @return array   Result containing elements intCost (in pence), intMinutes and uxtBilling which is the date we next bill on
	*/
	function prvCalculateProRataStartup($intMonthlyCost, $intMonthlyMinutes)
	{
		$intServiceID = $this->m_intServiceID;
		$arrServiceData = userdata_service_get($intServiceID);
		$intServiceBillingDay = $arrServiceData['invoice_day'];
		$strTodayDay = date('d');
		$strTodayYearMonth = date('Y-m');
		$strNextInvoiceDate = $arrServiceData['next_invoice'];

		if ($strNextInvoiceDate == '0000-00-00' || $strNextInvoiceDate == '9999-99-99')
		{
			// Bail out if the service has a bogus next_invoice date
			return FALSE;
		}
		
		if (intval($intServiceBillingDay) > intval($strTodayDay))
		{
			// Next billing date will occur this month
			$intNextBillingOffset       = 0;
			$intPrevBillingOffset       = -1;
			$intNextActualBillingOffset = 1;
		}
		else
		{
			// Next billing date will occur next month
			$intNextBillingOffset       = 1;
			$intPrevBillingOffset       = 0;
			$intNextActualBillingOffset = 2;
		}
		
		// Utilise the database's date handling facilities for consistency
		$strQuery =  "SELECT (TO_DAYS(NOW()) - TO_DAYS(DATE_ADD(CONCAT('$strTodayYearMonth', '-$intServiceBillingDay'), INTERVAL $intPrevBillingOffset MONTH))) as intDaysToPrevBilling,
		                     (TO_DAYS(DATE_ADD(CONCAT('$strTodayYearMonth', '-$intServiceBillingDay'), INTERVAL $intNextBillingOffset MONTH)) - TO_DAYS(NOW())) as intDaysToLastBilling,
		                     UNIX_TIMESTAMP(DATE_ADD(CONCAT('$strTodayYearMonth', '-$intServiceBillingDay'), INTERVAL $intNextActualBillingOffset MONTH)) as uxtNextActualBillingDate";

		$refResult = mysql_query($strQuery);
		$arrResult = mysql_fetch_array($refResult);

		if (!is_array($arrResult))
		{
			return FALSE;
		}

		$intDaysToNextBilling    = $arrResult['intDaysToNextBilling'];
		$intDaysToLastBilling    = $arrResult['intDaysToLastBilling'];

		// How long is the current billing month?
		$intDaysInBillingMonth   = $intDaysToNextBilling + $intDaysToLastBilling;

		// Where are we in the current billing month?
		$floFractionOfMonthLeft  = ($intDaysToNextBilling / $intDaysInBillingMonth);
		
		// Build the return array
		$arrReturn = array();
		$arrReturn['intCost']    = intval($floFractionOfMonthLeft * $intMonthlyCost);
		$arrReturn['intMinutes'] = intval($floFractionOfMonthLeft * $intMonthlyMinutes);
		$arrReturn['uxtBilling'] = $arrResult['uxtNextActualBillingDate'];

		return $arrReturn;
	}

	/**
	* Refreshes the current state of the component
	*
	* Calls EnableWifiComponent and DisableWifiComponent if the component is in a queued state
	*
	* @access public
	* @return boolean False if the component is destroyed, otherwise true
	*/
	function RefreshCurrentState()
	{
		// if we're in a queued state attempt to action it 
		switch($this->m_strStatus)
		{
			case 'active':
			case 'queued-activate':
				$strResult = $this->EnableWifiComponent($this->m_intActivationInvoiceID);
				return($strResult);
				
			case 'queued-deactivate':
				$strResult = $this->DisableWifiComponent();
				return($strResult);
				
			case 'queued-reactivate':
				$strResult = $this->EnableWifiComponent($this->m_intActivationInvoiceID);
				return($strResult);
				
			case 'queued-destroy':
				$strResult = $this->DestroyWifiComponent();
				return($strResult);
			default:
				//do the next bit.
			break;
		}

		return '';

	}


	/**
	* Checks for the existence of other Wifi components on the same service id
	*
	* @return string An empty string if sole component, or an error message
	*/
	function prvCheckSoleWifiComponentOnAccount()
	{
		// Check that no other wifi component is active on this account
		$refConnection = get_named_connection_with_db('product');
		$strQuery="SELECT component_id as intExistingComponentID,
		                  status as strStatus
		             FROM userdata.components uc
		       INNER JOIN products.service_components sc
		               ON sc.service_component_id = uc.component_type_id 
		       INNER JOIN products.tblComponentWIFIConfig cwc
		               ON sc.service_component_id = cwc.intServiceComponentID
		            WHERE uc.service_id = '$this->m_intServiceID'
		              AND (uc.status != 'destroyed' )
		              AND (NOT (uc.status = 'unconfigured'))
		              AND uc.component_id != '$this->m_intComponentID'";

		$refResult = PrimitivesQueryOrExit($strQuery,$refConnection,'Check component uniqueness');
		$arrComponentDetails = PrimitivesResultGet($refResult);

		if(is_array($arrComponentDetails) && count($arrComponentDetails)  > 0)
		{
			return "WIFI Component '{$arrComponentDetails['intExistingComponentID']}' already exists for ".
			       "this user in the state '{$arrComponentDetails['strStatus']}', only one non-destroyed ".
			       "WIFI component is allowed per user.";
		}

		return '';
	}





	/**
	* Creates the remote component given an activation invoice ID
	*
	* @return string Error message or empty string if successful
	*/
	function prvCheckOrCreateRemoteComponent(&$bolEventAdded, $intActivationInvoiceID=0, $bolAddSubscriptionEntry=false, $bolTariffChange=false, $intScheduledPaymentID=0)
	{
		// Check I've been saved
		if($this->GetWorkplaceComponentID() < 1)
		{
			return("The component does not exist or is new and has not yet been saved");
		}
		
		if(!$bolTariffChange)
		{
			// Check I'm the only component on 
			$strError = $this->prvCheckSoleWifiComponentOnAccount();
			if($strError !='')
			{
				return($strError);
			}
		}

		
		$bolEventAdded = false;
		if($this->m_intCurrentTariffID < 1)
		{
			return("The tariff has not been selected for this component");	
		}


		if($intScheduledPaymentID  > 0 && $this->m_intActivationInvoiceID < 1 && $intActivationInvoiceID == 0)
		{
			if(!$this->prvCheckScheduledPaymentBelongsToThisUser($intScheduledPaymentID))
			{
				return("Scheduled Payment ID '$intScheduledPaymentID' does not belong to this user");
			}
			else
			{
				$this->prvRecordInitialPaymentInvoiceID(0,$intScheduledPaymentID);
			}
		}
		elseif($this->m_intActivationInvoiceID < 1 && $intActivationInvoiceID > 0)
		{
			if(!$this->prvCheckInvoiceBelongsToThisUser($intActivationInvoiceID))
			{
				return("Invoice ID '$intActivationInvoiceID' has not been paid or does not belong to this user");
			}
			else
			{
				$this->prvRecordInitialPaymentInvoiceID($intActivationInvoiceID);
			}
		}


		if($this->m_intActivationInvoiceID < 1 && $this->bolInitialPaymentRequired())
		{
			if($intScheduledPaymentID == 0)
			{
				return("The invoice ID on which the initial payment was taken is not recorded, and the payment has not been scheduled");
			}
		}

		

		// Get Service details needed
		if(($strResult = $this->prvCreateRadiusUserAndSubscription($bolAddSubscriptionEntry)) !='')
		{
			return $strResult;
		}

		
		// Log the creation event
		$this->LogActivation($bolAddSubscriptionEntry);
		$bolEventAdded = true;

		return('');
	} // func: prvCheckOrCreateRemoteComponent

	/**
	* Create or reactivate a user and subscription in radius for this component 
	*
	*
	* @access private
	* <AUTHOR>
	* @return string error message, or '' if none
	*/
	function prvCreateRadiusUserAndSubscription($bolAddSubscriptionEntry)
	{
		$arrUserDetails = $this->GetUserDetails(); 
		$objRadiusUser = CRadiusUser::Create();
	
		$bolExists = $objRadiusUser->Retrieve($arrUserDetails['strUserName'], $arrUserDetails['strISP'], $arrUserDetails['intServiceID']);

		if(!$bolExists)
		{	
			//Create user in radius
			$arrNewUserParams = array('strUsername' => $arrUserDetails['strUserName'],
			                          'strISP'      => $arrUserDetails['strISP'],
			                          'strIPAddess' => 'pool',
			                          'strNetmask'  => '***************',
			                          'bolActive'   => true,
			                          'strPassword' => $arrUserDetails['strPassword'],
			                          'intServiceID'=> $arrUserDetails['intServiceID'],
			                          'bolPasswordVisibleToSupport' => true,
			                          'bolAllowCLIOrPassword' => true);
			
			$objRadiusUser->SetAll($arrNewUserParams);
			$objRadiusUser->Save();
		}
		else
		{
			$arrDetails = $objRadiusUser->GetAll();
			if(!$arrDetails['bolActive'])
			{
				$arrParams = array('bolActive' => true);
				$objRadiusUser->SetAll($arrParams);
				$bolCreated = $objRadiusUser->Save();

				if(!$bolCreated)
				{
					return 'Could not create user in radius';	
				}
			}
		}
		//Delete all wifi subscriptions for this user
		$objRadiusSubscription = CRadiusSubscription::Create();
		$arrUser = array('intRadiusID' => $objRadiusUser->GetID());

		$objRadiusSubscription->SetAll();

		$objRadiusSubscription->DeleteSubscription($this->GetAllWifiDialupGroups());
		
		if($bolAddSubscriptionEntry)
		{
			//Create subscription
			$objRadiusSubscription = CRadiusSubscription::Create();
			$bolExists = $objRadiusSubscription->Retrieve($objRadiusUser->GetID(), $this->m_intDialupGroupID);

			if(!$bolExists)
			{
				//Create
				$arrParams = array('intRadiusID' => $objRadiusUser->GetID(),
								           'intGroupID' => $this->m_intDialupGroupID,
								           'strIPAddress' => 'pool',
								           'strNetmask' => '***************');
								   
				$objRadiusSubscription->SetAll($arrParams);
				$bolSaved = $objRadiusSubscription->Save();

				if(!$bolSaved)
				{
					return 'Could not create subscription in radius';
				}
			}
		} // if: bolAddSubscriptionEntry

		return '';
	} // func: prvCreateRadiusUserAndSubscription




	/**
	* Returns the cancellation period of the component
	*
	* FIXME This does not support the PAYG product yet!
	* @param mixed Error string passed by reference
	* @return int Number of days cancellation notice required
	*/
	function GetDaysCancellationNotice(&$strErrorMessage)
	{
		$strErrorMessage = '';
		switch($this->m_strTariffModel)
		{
			case 'SUBSCRIPTION':
				return(30);
			break;
			case 'PAYG':
			case 'PREPAY':
				return(0);
			break;
			default:
				$strErrorMessage = "The Tariff model '{$this->m_strTariffModel}' is not supported by this component";
				return(365*10);
			break;
		}

	} 



	/**
	* Schedules cancellation of the Wifi component
	*
	* Call with no parameter to destroy ASAP respecting the cancellation notice period
	* Call with a unixtime date to schedule destruction for that date, regardless of the notice period FIXME - IS THIS RIGHT??! 
	* @param int Date to destroy in unixtime format
	* @return string Error message or an empty string if the operation was successful
	*/
	function ScheduleForLaterDestruction($uxtDateToDestroy =0)
	{
		if($uxtDateToDestroy < 1)
		{
			$strDaysCancellationError ='';
			$intDaysCancellationNotice = $this->GetDaysCancellationNotice($strDaysCancellationError);

			if($strDaysCancellationError !='')
			{
				return($strDaysCancellationError);
			}

			$uxtDateToDestroy = time() + (24*60*60*$intDaysCancellationNotice);
		}

		if($this->m_strStatus == 'destroyed' || $this->m_strStatus =='queued-destroy')
		{
			return("This component is already destroyed or is currently being destroyed. scheduling it for destruction is pointless");
		}
		if($this->m_intScheduledDestructionEventID > 0)
		{
			return("This component is already scheduled to be destroyed on service event '$this->m_intScheduledDestructionEventID'");
		}

		if($uxtDateToDestroy < (time() - (24*60*60)) )
		{
			return("The suggested scheduled destruction date is in the past");
		}

		$connection = get_named_connection('userdata');

		$service_definition_id = mysql_result(mysql_query("SELECT type FROM services WHERE service_id='$this->m_intServiceID'", $connection), 0, 0);

		$intEventID = userdata_service_event_add($this->m_intServiceID, WIFI_SCHEDULED_CANCELLATION , $service_definition_id, $service_definition_id,
		                                  $this->m_uxtNextInvoiceDate, '', date('Y-m-d 00:00:00',$uxtDateToDestroy), 
						  date('Y-m-d H:i:s'),$this->m_intComponentID);

		$this->prvRetrieveActiveCancelationEvent();

		return('');

	} 





	/**
	* Cancels the scheduled detruction of the component
	*
	* @return string Error message or empty string
	*/
	function CancelScheduledDestruction()
	{
		if($this->m_intScheduledDestructionEventID < 1)
		{
			return("This component is not scheduled for destruction");
		}

		$connection = get_named_connection('userdata');

		$service_definition_id = mysql_result(mysql_query("SELECT type FROM services WHERE service_id='$this->m_intServiceID'", $connection), 0, 0);

		$intEventID = userdata_service_event_add($this->m_intServiceID, WIFI_SCHEDULED_CANCELLATION_REVOKED , $service_definition_id, $service_definition_id,
		                                  $this->m_uxtNextInvoiceDate, '', '', 
						  date('Y-m-d H:i:s'),$this->m_intComponentID);
						  
		$this->prvRetrieveActiveCancelationEvent();

		return('');

	} 





	/**
	* Sets the local component status and updates the local DB
	*
	* This does not have any effect on the remote component
	* @param string New status name eg: active
	*/

	function prvSetStatus($strNewStatus)
	{
		$refConnection = get_named_connection_with_db('userdata');

		$strQuery = "UPDATE components set status  = '$strNewStatus' where component_id = '$this->m_intComponentID' ";

		$result = PrimitivesQueryOrExit($strQuery,$refConnection,
		                                   'Update the component status');

		$this->m_strStatus = $strNewStatus;
		return(true);

	}

function GetTariffDetails($intTariffID)
{
		$refConnection = get_named_connection_with_db('userdata');

		$strQuery = "SELECT sc.name as strComponentName, t.vchDisplayName as strTariffDisplayName, intAutomaticMinutesPerCycle, intMinutesOfCredit 
		             FROM products.tblTariff t 
		             INNER JOIN products.service_components sc ON sc.service_component_id = t.intServiceComponentID 
		             WHERE intTariffID = '{$intTariffID}'"; 

		$refResult = PrimitivesQueryOrExit($strQuery, $refConnection,'Check Tariff belongs to me');
		$arrTariff = PrimitivesResultGet($refResult);

		return $arrTariff;

} // func: GetTariffDetails

	/**
	* Returns whatever tariffs are available for the component.
	*
	* Returns an array of the tariff details. If called with an array of tariff IDs, only the requested
	* tariffs are included in the result.
	* @param array List of tariff IDs to limit the selection to
	* @param array
	* @param array
	* @return mixed The full details of the available tariffs or an error string.
	*/
	function GetAvailableTariffs($arrTariffIDs = array(),
	                             $arrTariffTypeHandles = array(), 
	                             $arrServiceComponentIDs = array(), 
	                             $uxtStartDate = 0 , $uxtEndDate =0,
	                             $arrPrepayTimeBandIDs = array())
	{
		$refConnection = get_named_connection_with_db('product');

		$strTariffClause = '';
		if(count($arrTariffIDs) > 0)
		{
			$strTariffClause = primitives_IN_clause('AND','t.intTariffID',$arrTariffIDs);
		}
		
		if(count($arrTariffTypeHandles) <1)
		{
			$arrTariffTypeHandles[] = $this->m_strTariffModel;
			
		}

		

		$strServiceComponentIDClause = '';

		if(count($arrServiceComponentIDs) > 0)
		{
			$strServiceComponentIDClause = primitives_IN_clause('AND','t.intServiceComponentID',$arrServiceComponentIDs);			
		}
		elseif(isset($this) && strtolower('cwificomponent')== strtolower(get_class($this)))
		{
			$strServiceComponentIDClause =  " AND t.intServiceComponentID = '{$this->m_intServiceComponentID}'";
		}

		$strStartDateValue = 'NOW()';
		$strEndDateValue   = 'NOW()';
		
		if($uxtStartDate >0)
		{
			$strStartDateValue = "from_unixtime('$uxtStartDate')";
		}
		
		if($uxtEndDate >0)
		{
			$strEndDateValue = "from_unixtime('$uxtEndDate')";
		}		
		
		$strPrepayTimeBandIDsClause = '';
		if(count($arrPrepayTimeBandIDs) > 0)
		{
			$strPrepayTimeBandIDsClause = primitives_IN_clause('AND','ptb.intPrepayTariffBandID', $arrPrepayTimeBandIDs);
		}
		
		// Subscription Tariffs
			$strBaseQuery ="SELECT s.intTariffID,
							   'GBP' as strCurrencyCode,
							   s.intTariffBaseCostID,
							   t.vchDisplayName as strTariffDisplayName,
							   t.vchHandle as strTariffHandle,
							   tt.vchHandle as strTariffTypeHandle,
							   intAutomaticMinutesPerCycle,
							   intMinutesBeforeExpiry,
							   intMinutesOfCredit,
							   s.intBaseCostExcVatInPence,
							   s.intBaseCostIncVatInPence,
							   s.decAdditionalMinutesExcVatInPence as floAdditionalMinutesExcVatInPence,
							   s.decAdditionalMinutesIncVatInPence as floAdditionalMinutesIncVatInPence,
							   s.intReferralValueIncVatInPence     as intReferralValueIncVatInPence
						  FROM tblTariff t 
					INNER JOIN tblTariffType tt
							ON t.intTariffTypeID = tt.intTariffTypeID 
					INNER JOIN tblSubscriptionTariffCost s 
							ON t.intTariffID = s.intTariffID
						   AND s.dtmDateActive <=  $strStartDateValue
						   AND (s.dtmDateEnded is null OR s.dtmDateEnded >= $strEndDateValue)";




			$arrAllTariffs =array();
			foreach($arrTariffTypeHandles as $strTariffTypeHandle)
			{
				switch($strTariffTypeHandle)
				{
					case 'PAYG':

						$strQuery = $strBaseQuery . " AND tt.vchHandle = 'PAYG'";

						if($strServiceComponentIDClause != '') {$strQuery .= " $strServiceComponentIDClause";}
						if($strTariffClause != '') {$strQuery .= " $strTariffClause";}

						$refResult = PrimitivesQueryOrExit($strQuery, $refConnection,'Check Tariff belongs to me');
						$arrAvailableTariffs = PrimitivesResultsAsArrayGet($refResult);
						$arrAllTariffs = array_merge($arrAllTariffs, $arrAvailableTariffs);
						continue 2;

					case 'SUBSCRIPTION':
						// Subscription Tariffs

						$strQuery = $strBaseQuery . " AND tt.vchHandle = 'SUBSCRIPTION'";

						if($strServiceComponentIDClause != '') {$strQuery .= " $strServiceComponentIDClause";}
						if($strTariffClause != '') {$strQuery .= " $strTariffClause";}

						$refResult = PrimitivesQueryOrExit($strQuery, $refConnection,'Check Tariff belongs to me');
						$arrAvailableTariffs = PrimitivesResultsAsArrayGet($refResult);
						$arrAllTariffs = array_merge($arrAllTariffs, $arrAvailableTariffs);
						continue 2;

					case 'PREPAY':
						$strQuery="SELECT ptb.intTariffID,
							'GBP' as strCurrencyCode,
							ptb.vchTariffBandDisplayName as strTariffDisplayName,
							t.vchHandle as strTariffHandle,
							tt.vchHandle as strTariffTypeHandle,
							ptb.intPrepayTariffBandID,
							intMinutesInBlock,
							decCostExcVatInPence as floCostExcVatInPence,
							decCostIncVatInPence as floCostIncVatInPence
								FROM tblTariff t
								INNER JOIN tblTariffType tt
								ON t.intTariffTypeID = tt.intTariffTypeID
								INNER JOIN tblPrepayTariffBand ptb
								ON t.intTariffID = ptb.intTariffID
								AND ptb.dtmDateActive <= $strStartDateValue
								AND (ptb.dtmDateEnded is null OR ptb.dtmDateEnded >= $strEndDateValue)";

						if($strServiceComponentIDClause != '') {$strQuery .= " $strServiceComponentIDClause";}
						if($strTariffClause != '') {$strQuery .= " $strTariffClause";}
						if($strPrepayTimeBandIDsClause != '') {$strQuery .= " $strPrepayTimeBandIDsClause";}

						$strQuery .= " ORDER BY intMinutesInBlock ASC";

						$refResult = PrimitivesQueryOrExit($strQuery, $refConnection, 'Check Tariff belongs to me');
						$arrAvailableTariffs = PrimitivesResultsAsArrayGet($refResult);
						$arrAllTariffs = array_merge($arrAllTariffs, $arrAvailableTariffs);
						continue 2;

					default:
						return("The Tariff model '{$this->m_strTariffModel}' is not supported by this component");
				}
			}

			return($arrAllTariffs);

	}





	/**
	 * Return whether or not a given tariff is the one the component is currently on
	 *
	 * @param int Tariff ID
	 * @return boolean The result of the test
	 */
	function prvBolMyTariff($intTariffID)
	{
		$refConnection = get_named_connection_with_db('product');
		// Check it's one of my tariffs 
		$strQuery ="SELECT if(count(*) > 0, '1',0) as bolMyTariff FROM tblTariff WHERE intServiceComponentID  = '$this->m_intServiceComponentID' AND intTariffID = '$intTariffID'";        $refResult = PrimitivesQueryOrExit($strQuery,$refConnection,'Check Tariff belongs to me');
		$bolMyTariff = PrimitivesResultGet($refResult,'bolMyTariff');
		return (bool) $bolMyTariff;
	}


	/**
	 * Retrieve details about a specific pre-pay time band on a specific tariff from the local database
	 *
	 * Passes the result back though the parameters. Will fail and return 'N' if the tariff isn't applicable to the component
	 * @param int The tarrif ID
	 * @param int The prepay time band to retrieve
	 * @return string 'Y' or 'N' on success or failure
	 */
	function prvBolTimeBandTariff($intTariffID,$intPrepayTimeBandID,&$intNumMinutes,&$floCostExcVatInPence,&$floCostIncVatInPence,&$intMinutesBeforeExpiry)
	{
		$refConnection = get_named_connection_with_db('product');
		// Check it's one of my tariffs 
		$strQuery ="SELECT tb.intMinutesInBlock,
			tb.decCostExcVatInPence as floCostExcVatInPence,
			tb.decCostIncVatInPence as floCostIncVatInPence,
			t.intMinutesBeforeExpiry FROM tblTariff t
				INNER JOIN tblPrepayTariffBand tb
				ON tb.intTariffID = t.intTariffID
				AND tb.dtmDateActive < NOW()
				AND (tb.dtmDateEnded is null OR tb.dtmDateEnded >= NOW())
				WHERE intServiceComponentID  = '$this->m_intServiceComponentID'
				AND t.intTariffID = '$intTariffID'
				AND tb.intPrepayTariffBandID = $intPrepayTimeBandID";

		$refResult = PrimitivesQueryOrExit($strQuery,$refConnection,'Check Tariff belongs to me');
		$arrTimeBand = PrimitivesResultGet($refResult);

		if(count($arrTimeBand) < 1)
		{
			return 'N';
		}
		$intNumMinutes          =$arrTimeBand['intMinutesInBlock'];
		$floCostExcVatInPence   =$arrTimeBand['floCostExcVatInPence'];
		$floCostIncVatInPence   =$arrTimeBand['floCostIncVatInPence'];
		$intMinutesBeforeExpiry =$arrTimeBand['intMinutesBeforeExpiry'];

		return 'Y';
	}


	/**
	 * Returns the numeric time type ID for a specific time type eg: PREPAY
	 *
	 * @param string Handle eg: PREPAY, CREDIT
	 * @return int Numeric ID for the time type
	 */
	function WifiTimeTypeHandleToID($strHandle)
	{
		static $arrTimeTypes = array();

		if(count($arrTimeTypes) < 1)
		{
			$refConnection = get_named_connection_with_db('product');
			$strQuery      = "SELECT intWifiTimeTypeID, vchHandle FROM vblWifiTimeType";
			$refResult     = PrimitivesQueryOrExit($strQuery,$refConnection,'Get Wifi Handles');
			$arrTimeTypes  = PrimitivesResultsAsArrayGet($refResult,'vchHandle');
		}

		return $arrTimeTypes[$strHandle]['intWifiTimeTypeID'];
	}


	/**
	 * Returns the numeric free time reason ID for a fre time grant reason  eg: GOODWILL
	 *
	 * @param string Handle eg: GOODWILL
	 * @return int Numeric ID for the free time reason
	 */
	function WifiFreeTimeReasonHandleToID($strHandle)
	{
		static $arrFreeTimeReasons = array();

		if(count($arrFreeTimeReasons) < 1)
		{
			$refConnection       = get_named_connection_with_db('product');
			$strQuery            = "SELECT intFreeTimeReasonID, vchHandle FROM vblWifiFreeTimeReason";
			$refResult           = PrimitivesQueryOrExit($strQuery,$refConnection,'Get Wifi Handles');
			$arrFreeTimeReasons  = PrimitivesResultsAsArrayGet($refResult,'vchHandle');
		}

		return $arrFreeTimeReasons[$strHandle]['intFreeTimeReasonID'];

	}

	/**
	 * Returns an array of reasons for granting WiFi time    
	 *
	 * Returns an array of reasons for granting WiFi time
	 *  
	 * @param string Handle eg: GOODWILL
	 * @return int Numeric ID for the free time reason
	 */
	function WifiGetFreeTimeReasons()
	{
		static $arrFreeTimeReasons = array();

		if(count($arrFreeTimeReasons) < 1)
		{
			$refConnection       = get_named_connection_with_db('product');
			$strQuery            = "SELECT intFreeTimeReasonID, vchHandle as strFreeTimeReasonHandle, " .
				" vchDisplayName as strFreeTimeReasonDisplayName " .
				" FROM vblWifiFreeTimeReason";

			$refResult           = PrimitivesQueryOrExit($strQuery,$refConnection,'Get Wifi Handles');
			$arrFreeTimeReasons  = PrimitivesResultsAsArrayGet($refResult,'strFreeTimeReasonHandle');
		}

		return $arrFreeTimeReasons;

	}



	/**
	 * Sets the initial tariff for the component at the point of creation
	 * @param int Tariff ID
	 * @param int Optional timeband for pre-pay tariffs
	 * @return mixed TRUE or an error string
	 */
	function SetInitialTariff($intCurrentTariffID, $intPrepayInitialTimeBandID = 0)
	{
		if($this->m_intCurrentTariffID > 0)
		{
			return("The '$this->m_strTariffDisplayName' tariff has already been selected for this WiFi component, any further tariff changes must use the ChangeTariff() call");
		}

		if($this->prvBolMyTariff($intCurrentTariffID) != 'Y')
		{
			return("tariff ID '$intCurrentTariffID' is not allowed for this component");
		}

		if($intPrepayInitialTimeBandID > 0)
		{
			$intNumMinutes          = null;
			$floCostExcVatInPence   = null;
			$floCostIncVatInPence   = null;
			$intMinutesBeforeExpiry = null;

			if(($strCheck = $this->prvBolTimeBandTariff($intCurrentTariffID, $intPrepayInitialTimeBandID,
							$intNumMinutes, $floCostExcVatInPence,
							$floCostIncVatInPence, $intMinutesBeforeExpiry)) != 'Y')
			{
				return("Prepay TimeBand ID '$intPrepayInitialTimeBandID' is not allowed on tariff '$intCurrentTariffID' : $strCheck");
			}
		}


		$refConnection = get_named_connection_with_db('userdata');

		// Set the config
		$strPrepayBandSet = '';
		if($intPrepayInitialTimeBandID > 0)
		{
			$strPrepayandSet = ", intPrepayInitialTimeBandID = $intPrepayInitialTimeBandID";
		}
		$strQuery = "UPDATE tblConfigWifi set intCurrentTariffID  = '$intCurrentTariffID' $strPrepayBandSet where intComponentID = '$this->m_intComponentID' AND intWifiConfigID = $this->m_intWifiConfigID AND (intCurrentTariffID is null or intCurrentTariffID =0)";

		$result = PrimitivesQueryOrExit($strQuery, $refConnection, 'Update the component status');

		// Call to refresh my details
		$this->CWifiComponent($this->m_intComponentID);

		return '';

	}




	////////////////////////
	// Public Methods
	////////////////////////
	


	/**
	* 
	*
	* @return float The monthly charge to be taken, including VAT
	*/






	function ScheduleMonthlyChargesAndRenew($uxtDateOfRun='')
	{
		if($uxtDateOfRun == '')
		{
			$uxtDateOfRun = strtotime(date('Y-m-d'));
		}
	
		$strErrorMessage = '';
		
		if($uxtDateOfRun == 0)
		{
			$uxtDateOfRun = strtotime(date('Y-m-d'));
		}

		$uxtDateOfRun = strtotime(date('Y-m-d',$uxtDateOfRun));
		

		CCurrentUser::SetTargetUser($this->m_intServiceID);
		
		//
		// Calculate cost of used credit blocks
		//
		
		$arrChargeableBlocks = $this->GetInvoiceableCreditTime($strErrorMessage,$uxtDateOfRun);	

		if($strErrorMessage != '')
		{
			$this->objError = new CError(__FILE__, __LINE__, $strErrorMessage);
			CCurrentUser::ClearTargetUser();
			return false;
		}
		
		$floTotalChargePounds = 0.0;

		foreach($arrChargeableBlocks as $arrBlock)
		{
			$floChargePence = 0.0;
		
			if($arrBlock['strPricingPerBlockOrPerMinute'] == 'PRICE_PER_MINUTE')
			{
				$floChargePence = $arrBlock['floCostIncVatInPence'] * ceil($arrBlock['intSecondsUsed'] / 60);
			}
			elseif($arrBlock['strPricingPerBlockOrPerMinute'] == 'PRICE_PER_BLOCK')
			{
				//Should never happen - all blocks returned should be ppm credit...
				//One day we may have per-block credit?  Maybe?
				$floChargePence = $arrBlock['floCostIncVatInPence'];
			}

			$floTotalChargePence += $floChargePence;
			$objCurrency = CPNI18NCurrency::CreateFromDivisorValue($arrBlock['strCurrencyCode'], $floChargePence);
			$floChargePounds = $objCurrency->GetValueAsFloat();
			$floTotalChargePounds += $floChargePounds;
		
			if($floTotalChargePounds > 0)
			{
				//Create a scheduled payment 
				$arrAccount = userdata_account_get_by_service($this->GetServiceID());
				$objScheduledPayment = CWifiScheduledPayment::Create();	
				$arrProperties = array('intAccountID'       => $arrAccount['account_id'],
							     'intComponentID'     => $this->GetWorkplaceComponentID(),
							     'intWifiTimeBlockID' => $arrBlock['intWifiTimeBlockID'],
							     'strDescription'     => CPNI18NCurrency::I18Nsprintf(_('%n Wireless Broadband minutes @ %?n%s (inc vat) between %s and %s '),
										      $arrBlock['intMinutesUsed'],
										      $arrBlock['floCostIncVatInPence'],
										      $objCurrency->GetDivisorSymbol('text/html'),
										      CPNI18NCurrency::GetShortDate($arrBlock['uxtActiveFrom']),
										      CPNI18NCurrency::GetShortDate($arrBlock['uxtActiveUntil'])
										     ),
							    'strActionerID'      => $whoami,
							    'floAmount'          => $floChargePounds,
							    'floOutstanding'     => $floChargePounds,
							    'uxtDateDue'         => $uxtDateOfRun);

				$objScheduledPayment->SetAll($arrProperties);
				$objScheduledPayment->Save();
				$intWifiScheduledPaymentID = $objScheduledPayment->GetID();
				$this->prvRecordScheduledPaymentIDOnTimeBlock($arrBlock['intWifiTimeBlockID'],$intWifiScheduledPaymentID);
			}

		} // for: expired credit block not billed for

		// If account is now cancelled, just pay for credit used

		if( ($this->m_intScheduledDestructionEventID != 0) && ($this->m_uxtScheduledDestructionDate < $uxtDateOfRun) )
		{

			// Commit suicide  :-)
			if($this->GetStatus() != 'destroyed')
			{

				// Log real cancellation for reporting
				$this->LogReportingCancellation();

		 		$strErrorMessage = $this->DestroyWifiComponent();

			}
			

			// This component is cancelled, not further subscription charge to pay
			CCurrentUser::ClearTargetUser();
			return  $floTotalChargePounds;			
		} // if scheduled destruction due

		if($this->GetStatus() != 'active')
		{
			return $floTotalChargePounds;
		}

		// Action their tariff change if there is one
		$arrScheduledTariffChanges = CWifiScheduledTariffChange::RetrieveCollection($this->m_intComponentID,false,false, $uxtDateOfRun);

		if(count($arrScheduledTariffChanges) > 0)
		{
			//Only do the first one in the array, as this is the most up to date
			reset($arrScheduledTariffChanges);

			$objTariffChange = CWifiScheduledTariffChange::Create($arrScheduledTariffChanges[0]['intWifiScheduledTariffChangeID']);

			$arrTariffChange = $objTariffChange->GetAll();

			$intRequestedServiceComponentID = $this->prvComponentTypeForTariff($arrTariffChange['intNewTariffID']);

			if($intRequestedServiceComponentID === false)
			{
				//Error - go no further.
				$strErrorMessage = "The component type for the new tariff no longer exists.";
				$this->objError = new CError(__FILE__, __LINE__, $strErrorMessage);
				CCurrentUser::ClearTargetUser();
				return false;
			}


			// Log reporting event

			$arrOldTariff = $this->GetAvailableTariffs(array ($arrTariffChange['intOldTariffID']));	
			$arrNewTariff = $this->GetAvailableTariffs(array ($arrTariffChange['intNewTariffID']), array('SUBSCRIPTION','PAYG','PREPAY'), array($intRequestedServiceComponentID));
			$strOldTariffModel = $arrOldTariff[0]['strTariffTypeHandle'];
			$strNewTariffModel = $arrNewTariff[0]['strTariffTypeHandle'];

			if       (($strOldTariffModel != 'SUBSCRIPTION') && ($strNewTariffModel == 'SUBSCRIPTION')) {
	
				$this->LogReportingUpgrade();
	
			} elseif (($strOldTariffModel == 'SUBSCRIPTION') && ($strNewTariffModel != 'SUBSCRIPTION')) {
	
				$this->LogReportingDowngrade();			
	
			} elseif (($strOldTariffModel == 'SUBSCRIPTION') && ($strNewTariffModel == 'SUBSCRIPTION')) {
	
				if ($arrNewTariff['intAutomaticMinutesPerCycle'] > $arrOldTariff['intAutomaticMinutesPerCycle']) {
					$this->LogReportingUpgrade();
				} elseif ($arrNewTariff['intAutomaticMinutesPerCycle'] < $arrOldTariff['intAutomaticMinutesPerCycle']) {
					$this->LogReportingDowngrade();			
				}
	
			}


			if($intRequestedServiceComponentID != $this->GetComponentTypeID())
			{
				//A component change is required.
				$floBaseCost = $this->prvPerformComponentChange($strErrorMessage, $objTariffChange, $intRequestedServiceComponentID);

				if($floBaseCost === false && $strErrorMessage !='')
				{
					$this->objError = new CError(__FILE__, __LINE__, $strErrorMessage);
					CCurrentUser::ClearTargetUser();
					return false;
				}

				CCurrentUser::ClearTargetUser();
				return  $floBaseCost;
			}
			else
			{
				//No component change required, just a tariff change on the exiting component
				$this->prvPerformTariffChange($objTariffChange);
			}
		

		} // if: Tariff or account change scheduled
	
		// Call to refresh my details
		$this->CWifiComponent($this->m_intComponentID);

		$arrCurrentTariff = $this->GetAvailableTariffs(array($this->GetCurrentTariffID()));
		
		$this->prvScheduleSubscriptionPayments($uxtDateOfRun,current($arrCurrentTariff), $floTotalChargePounds);

		return $floTotalChargePounds;

	} // func: ScheduleMonthlyChargesAndRenew 

	
	/**
	* Returns the component's local config ID
	* @return int Config ID
	*/
	function GetConfigID()
	{
		return($this->m_intWifiConfigID);
	} 

	/**
	* Returns the component's name
	* @return string the component name
	*/
	function GetComponentName()
	{
		return($this->m_strComponentName);
	} 


	/**
	* Returns the component's cancellation date in unixtime or 0 if already destroyed or not scheduled for destruction
	* @return int Zero or the destruction date in uxt
	*/
	function GetScheduledCancelationDate()
	{
		// Can't be scheduled if destroyed or currently destroying
		if($this->m_strStatus == 'queued-destroy' || $this->m_strStatus == 'destroyed')
		{
			return(0);
		}

		if($this->m_uxtScheduledDestructionDate > 0)
		{
			return($this->m_uxtScheduledDestructionDate);
		}

		return(0);
		
	} 



	/**
	*
	*
	*
	*/
	function prvMarkInitialActivationPerformed()
	{
		$refConnection = get_named_connection_with_db('userdata');

		$strQuery = "UPDATE tblConfigWifi set  bolInitialActivationPerformed = 1 where  intWifiConfigID = {$this->m_intWifiConfigID} and intComponentID = '$this->m_intComponentID'";

		$result = PrimitivesQueryOrExit($strQuery,$refConnection,
		                                   'Update the component for record the initial invoice id');

		$this->m_bolInitialActivationPerformed = 1;

			return true;
	} 

function bolInitialPaymentRequired()
{
	switch($this->m_strTariffModel)
	{
		case 'PAYG':
			return false;
		case 'SUBSCRIPTION':
			/* Intentional fallthrough */
		case 'PREPAY':
			/* Intentional fallthrough */
		default:
			return(true);		
		}
} // func: bolInitialPaymentRequired

	/**
	*
	*
	*
	*/
	function prvRecordInitialPaymentInvoiceID($intActivationInvoiceID,$intScheduledPaymentID =0)
	{
		$refConnection = get_named_connection_with_db('userdata');

		$strNextInvoiceClause = '';
		switch($this->m_strTariffModel)
		{
			case 'PAYG':
			case 'SUBSCRIPTION':
				$strNextInvoiceClause = ", dtmNextInvoiceDate = DATE_ADD(NOW(), INTERVAL 1 MONTH), intBillingDay = DATE_FORMAT(NOW(), '%d')";
				break;
				
			case 'PREPAY':
				$strNextInvoiceClause = ", dtmNextInvoiceDate = '2030-01-01'";
				break;

			default:
				return(false);		
		}
		
		$strQuery = "UPDATE tblConfigWifi
		                SET intActivationInvoiceID = '$intActivationInvoiceID'
		                    $strNextInvoiceClause
		              WHERE intWifiConfigID = {$this->m_intWifiConfigID}
		                AND intComponentID = '{$this->m_intComponentID}' AND (intActivationInvoiceID is null or intActivationInvoiceID =0)";

		$result = PrimitivesQueryOrExit($strQuery,$refConnection,
		                                   'Update the component for record the initial invoice id');

		$this->m_intActivationInvoiceID = $intActivationInvoiceID;

		$strQuery = "SELECT unix_timestamp(cw.dtmNextInvoiceDate) as uxtNextInvoiceDate, 
		                    cw.intBillingDay as intBillingDay 
		               FROM userdata.tblConfigWifi cw 
		              WHERE cw.intWifiConfigID = {$this->m_intWifiConfigID} 
		                AND cw.intComponentID = '$this->m_intComponentID'";

		$refResult = PrimitivesQueryOrExit($strQuery,$refConnection,'Make dialup configurator entries');
		$arrNextInvoiceDetails = PrimitivesResultGet($refResult);

		$arrNextInvoiceDetails['uxtNextInvoiceDate'] = intval($arrNextInvoiceDetails['uxtNextInvoiceDate']);
		$this->m_uxtNextInvoiceDate = $arrNextInvoiceDetails['uxtNextInvoiceDate'];

		$arrNextInvoiceDetails['intBillingDay'] = intval($arrNextInvoiceDetails['intBillingDay']);
		$this->m_intBillingDay                  = $arrNextInvoiceDetails['intBillingDay'];

		return(true);

	} 



	
	/**
	*
	*
	*
	*
	*              
	*
	*/

	function GetServiceID()
	{
		if($this->m_intServiceID < 1)
		{
			return(false);
		}
		
		return $this->m_intServiceID;
		
	} 
	
	
	/**
	*
	*
	*
	*
	*              
	*
	*/	   
	function GetUserDetails()
	{
		if($this->m_intServiceID < 1)
		{
			return(false);
		}

		$arrService = userdata_service_get($this->m_intServiceID);
		
		$arrDetails = array('intServiceID'=> intval($this->m_intServiceID),
			         'strUserName' => $arrService['username'], 
			         'strPassword' => (string) $arrService['password'],
			         'strISP'      => $arrService['isp']);
		
		return $arrDetails;

	} 


	/**
	*
	*
	*
	*
	*
	*
	*/
	function prvCheckScheduledPaymentBelongsToThisUser($intScheduledPaymentID)
	{

		// TODO : make sheduled payment ownership queries
		return true;

	} // func : prvCheckScheduledPaymentBelongsToThisUser 




	/**
	*
	*
	*
	*
	*
	*
	*/
	function prvCheckInvoiceBelongsToThisUser($intInvoiceID)
	{
		// Check Invoice Belongs to this user 
		$refConnection = get_named_connection_with_db('userdata');
		$strQuery="SELECT IF(count(*) > 0,'Y','N') as bolInvoiceOK
		             FROM userdata.components uc
		       INNER JOIN userdata.services us
		               ON uc.service_id = us.service_id 
		       INNER JOIN userdata.users uu
		               ON us.user_id = uu.user_id 
		       INNER JOIN userdata.accounts ua
		               ON ua.customer_id = uu.customer_id 
		       INNER JOIN financial.sales_invoices si
		               ON si.account_id = ua.account_id 
		            WHERE uc.component_id = {$this->m_intComponentID} 
		              AND si.invoice_status IN ('fully_paid','fully_refunded')
		              AND si.sales_invoice_id = {$intInvoiceID}";

		$refResult = PrimitivesQueryOrExit($strQuery,$refConnection,'Check Invoice ownership and status');
		$arrInvoiceDetail = PrimitivesResultGet($refResult);

		
		if((!$arrInvoiceDetail) || count($arrInvoiceDetail) < 1)
		{
			return(false);	
		}		

		if( (!isset($arrInvoiceDetail['bolInvoiceOK']) ) || $arrInvoiceDetail['bolInvoiceOK'] !='Y')
		{
			return(false);	
		}

		return(true);

	} // func : func: prvCheckInvoiceBelongsToThisUser 





	function GrantFreePAYGMinutes(&$strErrorMessage,$strIssuingWorkplaceUser,
		                                       $strReasonGrantedHandle,
	                                           $intMinutesToGrant,
	                                           $intMinutesBeforeExpiry)
	{
		$strErrorMessage='';

		// Convert time block type handle to ID
		
		$intWifiTimeTypeID = $this->WifiTimeTypeHandleToID("FREE_TIME");

		if($intWifiTimeTypeID < 1)
		{
			$strErrorMessage='Could not find the time block handle for "FREE_TIME"';
			return(false);
		}

		// Convert free time reason handle to ID
		
		$intWifiFreeTimeReasonID = $this->WifiFreeTimeReasonHandleToID($strReasonGrantedHandle);

		if($intWifiFreeTimeReasonID < 1)
		{
			$strErrorMessage= "Could not correct ID from free time reason '$strReasonGrantedHandle'";
			return(false);
		}

		// Check minutes to grant 

		if($intMinutesToGrant < 0)
		{
			$strErrorMessage='Number of minutes to add is negative';
			return(false);
		}
		if($intMinutesToGrant == 0)
		{
			$strErrorMessage='Please select a number of minutes';
			return(true);
		}
		
		if($intMinutesBeforeExpiry < 1)
		{
			$strErrorMessage='Number of minutes before expiry must be greater than 1';
			return(false);
		}
		
		// Record time creation attempt
		if(($intTimeBlockID = $this->prvInsertTimeBlock($strErrorMessage,$intWifiTimeTypeID,$intMinutesToGrant,$intMinutesBeforeExpiry,0,0,0,0,$intWifiFreeTimeReasonID,0,0,$strIssuingWorkplaceUser))===false)
		{
			return(false);
		}
		
		return($intTimeBlockID);
		
	}


	/**
	*
	*
	*
	*
	*
	*
	*/
	function GrantPromotionalTime(&$strErrorMessage,$arrPromotionalEvents,$intInvoiceID =0 )
	{

		$strErrorMessage ='';
		
		if(count($arrActivationTimeBlocks = $this->prvTimeBlocksForPromotionalActions($arrPromotionalEvents)) < 1)
		{
			return true;
		}

		foreach($arrActivationTimeBlocks as $arrTimeBlock)
		{

			if($this->prvBolInitialPaymentExists($arrTimeBlock['intWifiPromoEventID']))
			{
				continue;	
			}

			switch($arrTimeBlock['strTimeBlockTypeHandle'])	
			{
				case 'PROMOTIONAL_SUBSCRIPTION':
					$this->prvCreateSubscriptionPromoTimeBlock($strErrorMessage,$arrTimeBlock['intWifiPromoEventID'],
							$arrTimeBlock['intWifiTimeTypeID'],$arrTimeBlock['intNumMinutes'],
							$arrTimeBlock['intMinutesBeforeExpiry'],$intInvoiceID);
							
					if($strErrorMessage !='')
					{
						return($strErrorMessage);
					}

					//flag as done in temp table
					$this->prvFlagScheduledPromoEventDone($arrTimeBlock['strEventHandle']);
					
					break;
				case 'PROMOTIONAL_CREDIT':
					$this->prvCreateCreditPromoTimeBlock($strErrorMessage,$arrTimeBlock['intWifiPromoEventID'],
							$arrTimeBlock['intWifiTimeTypeID'],$arrTimeBlock['intNumMinutes'],
							$arrTimeBlock['intMinutesBeforeExpiry']);
					if($strErrorMessage !='')
					{
						return($strErrorMessage);
					}

					//flag as done in temp table
					$this->prvFlagScheduledPromoEventDone($arrTimeBlock['strEventHandle']);


					
					break;
				case 'PROMOTIONAL_PREPAY':
					$this->prvCreatePrepayPromoTimeBlock($strErrorMessage,$arrTimeBlock['intWifiPromoEventID'],
							$arrTimeBlock['intWifiTimeTypeID'],$arrTimeBlock['intNumMinutes'],
							$arrTimeBlock['intMinutesBeforeExpiry'],$intInvoiceID);
					if($strErrorMessage !='')
					{
						return($strErrorMessage);
					}

					// Record in the inital timeband field
					$this->prvRecordInitialTimeBandID($arrTimeBlock['intWifiPromoEventID']);
					//flag as done in temp table
					$this->prvFlagScheduledPromoEventDone($arrTimeBlock['strEventHandle']);
					
					break;
				default: 
					return("The Promotional Time block type '{$arrTimeBlock['strTimeBlockTypeHandle']}' is not supported by this component");
			}
		}

		return true;
	} 



	/**
	* Component configurator worker function
	*
	*
	* 
	* @param The id of the invoice which paid for activation
	* @param 
	*/

	function EnableWifiComponent($intActivationInvoiceID=0, $arrPromotionalEvents = array(), $bolTariffChange=false, $intScheduledPaymentID=0)
	{
		
		if ( in_array($this->GetStatus(), array('destroyed','queued-destroy','invalid')) )
		{
			return 'Component is destroyed and can not be re-enabled';
		}
		
		$strError = '';
		$bolActivationEventAdded = false;

		//Fetch promo events
		$arrPromotionalEvents = $this->prvGetScheduledPromoEventsAndMerge($arrPromotionalEvents);	

		$this->prvSetStatus('queued-activate');

		$strError = $this->prvCheckOrCreateRemoteComponent($bolActivationEventAdded, $intActivationInvoiceID, true, $bolTariffChange, $intScheduledPaymentID); 
		
		if($strError != '')
		{
			return $strError;
		}
		

		// Promo events are only allowed at first activation (signup)
	
		if(count($arrPromotionalEvents) > 0)
		{
			$this->GrantPromotionalTime($strError,$arrPromotionalEvents,$this->m_intActivationInvoiceID);
			if($strError !='')
			{
				return $strError;
			}
		}

		if(!$this->m_bolInitialActivationPerformed )
		{
		
			// Add referrals if needed
			
			// Get current tariff details
			$arrTariff = $this->GetAvailableTariffs(array($this->GetCurrentTariffID()) );

			if(!is_array($arrTariff))
			{
				return $arrTariff;
			}
			if(count($arrTariff) < 1)
			{
				return "The correct tariff could not be found";
			}

			if(isset($arrTariff[0]['intReferralValueIncVatInPence']) && $arrTariff[0]['intReferralValueIncVatInPence'] >0)
			{
				// Get 
				$arrResult = financial_referral_create_wifi_token($this->GetWorkplaceComponentID(),
				                                                  time(),
				                                                  'wifi_signup',
																  $this->m_intActivationInvoiceID,
				                                                  $arrTariff[0]['intReferralValueIncVatInPence']);

				if(isset($arrResult['strErrorMessage']) &&  $arrResult['strErrorMessage'] != '')
				{
					$strProblemComment = wordwrap("In ".__FILE__." on line ".__LINE__.
								      " financial_referral_create_wifi_tokens errored creating referral tokens for ".
								      " a signup wifi payment covering 1 month begining '".date('%d/%M/%Y',time())."'".
								      " component_id #".$this->GetWorkplaceComponentID().
								      " The error was: $strCreateTokenError\n. This users account will be ".
								      " inconsitent, and requires manual fixing.");

					$strAutoProblemTag = 'referral_creation_fubar';
					$strProblemTitle = 'Creating Referral Tokens Failed During Wifi Signup';
					pt_raise_autoproblem($strAutoProblemTag, $strProblemTitle, $strProblemComment, $strProblemComment);
				}
			}

			if(!$this->prvMarkInitialActivationPerformed())
			{
				return("failed to set Initial Activation performed flag");
			}

		}


		$this->prvSetStatus('active');

		return('');

	} // func: EnableWifiComponent



	/**
	* 
	* 
	* 
	* @access private
	* <AUTHOR>
	* @param  array Events
	* @return array merged events and scheduled events.
	*/
	function prvGetScheduledPromoEventsAndMerge($arrPromotionalEvents)
	{
			//Get scheduled events
			$strQuery = "SELECT distinct vchEventHandle as strEventHandle
			               FROM tblWifiScheduledPromoEvent
			              WHERE intComponentID = '{$this->m_intComponentID}'
			                AND dtmDateExecuted IS NULL";

			$refConnection           = get_named_connection_with_db('userdata');
			$refResult               = PrimitivesQueryOrExit($strQuery,$refConnection,'Get scheduled promo events');
			$arrScheduledPromoEvents = PrimitivesResultsAsListGet($refResult);
							
			//de-dupe
			foreach(array_keys($arrPromotionalEvents) as $i)
			{
				foreach(array_keys($arrScheduledPromoEvents) as $j)
				{
					if($arrPromotionalEvents[$i] == $arrScheduledPromoEvents[$j]['strEventHandle'])
					{
						unset($arrPromotionalEvents[$i]);
					}
				}
			}

			//Write any new promo events back to the db.
			foreach(array_keys($arrPromotionalEvents) as $i)
			{
				$strEvent = addslashes($arrPromotionalEvents[$i]);
				
				$strQuery = "INSERT INTO tblWifiScheduledPromoEvent
				                         (intComponentID,
				                          vchEventHandle,
				                          dtmDateCreated)
				                   VALUES ('{$this->m_intComponentID}',
				                           '$strEvent',
				                           NOW())";

				$refResult = PrimitivesQueryOrExit($strQuery,$refConnection,'Add scheduled promo event');
										  
			}
			
			//merge
			$arrPromotionalEvents = array_merge($arrPromotionalEvents, $arrScheduledPromoEvents);
			
			return $arrPromotionalEvents;
	}

	function prvRecordInitialTimeBandID($intWifiPromoEventID)
	{
		if((!$intWifiPromoEventID) || (!isset($this->m_intComponentID)) || $this->m_intComponentID < 1)
		{
			return true;
		}
		$refConnection = get_named_connection_with_db('userdata');

		$strQuery = "UPDATE tblConfigWifi
		                SET intPrepayInitialTimeBandID = '$intWifiPromoEventID' 
		              WHERE intComponentID = '{$this->m_intComponentID}'
		                     AND intPrepayInitialTimeBandID IS NULL";

		$refResult = PrimitivesQueryOrExit($strQuery,$refConnection,'Record the prepay ID');

		return true;
	} // func: prvRecordInitialTimeBandID

	/**
	* 
	* 
	* 
	* @access private
	* <AUTHOR>
	* @param  string the event handle which needs flagged as completed.
	* @return void
	*/
	function prvFlagScheduledPromoEventDone($strEventHandle)
	{
		$refConnection = get_named_connection_with_db('userdata');
		$strEvent = addslashes($strEventHandle);

		$strQuery = "UPDATE tblWifiScheduledPromoEvent
		                SET dtmDateExecuted = NOW()
		              WHERE vchEventHandle = '$strEvent'
		                AND intComponentID = {$this->m_intComponentID}
		                AND dtmDateExecuted IS NULL";

		$refResult = PrimitivesQueryOrExit($strQuery,$refConnection,'mark scheduled promo event as done');
	}



	/**
	*
	*
	*
	*
	*
	*
	*/

	function prvBolInitialPaymentExists($intWifiPromoEventID)
	{
		// Fix Me
		// Check if this Promo Event Currently exists
		if(($arrTimeBlock = $this->prvGetPromotionalTimeBlock($intWifiPromoEventID)) && count($arrTimeBlock) > 0) 
		{
			return $arrTimeBlock['intTimeBlockID'];
		}

		return false;
	} 

	/**
	*
	*
	*
	*
	*
	*
	*/
	function prvTimeBlocksForPromotionalActions($arrPromoEvents = array())
	{
		// Get details of all time blocks turned on at activation
		if(count($arrPromoEvents) < 1 )
		{
			return(array());
		}
		$strPromoList = PrimitivesSafeList($arrPromoEvents);

		$strQuery = "SELECT intWifiPromoEventID,
		                    po.vchDisplayName,
		                    po.vchAdminDescription,
		                    po.vchHandle as strEventHandle,
		                    decCostExcVatInPence as floCostExcVatInPence,
		                    decCostIncVatInPence as floCostIncVatInPence,
		                    intNumMinutes,
		                    intMinutesBeforeExpiry,
		                    tt.vchHandle as strTimeBlockTypeHandle,
		                    tt.intWifiTimeTypeID
		               FROM tblWifiPromoOffer po
		         INNER JOIN vblWifiTimeType tt
		                 ON tt.intWifiTimeTypeID = po.intWifiTimeTypeID
		              WHERE po.dtmDateActive <= NOW()
		                AND (po.dtmDateEnded IS NULL OR po.dtmDateEnded >=NOW())
		                AND po.vchHandle IN ($strPromoList)";

		$refConnection = get_named_connection_with_db('product');
		$refResult = PrimitivesQueryOrExit($strQuery,$refConnection,'Get Activation Results');
		$arrTimeBlocksOnActivation = PrimitivesResultsAsArrayGet($refResult);

		return($arrTimeBlocksOnActivation);
	} 



	/**
	*
	*
	*
	*
	*
	*
	*/
	function prvInsertTimeBlock(&$strErrorMessage, $intWifiTimeTypeID, $intNumMinutes, $intMinutesBeforeExpiry, $intSalesInvoiceID = 0, $intWifiPromoEventID = 0, $intPrepayTariffBandID =0, $intTariffBaseCostID = 0, $intFreeTimeReasonID =0, $uxtStartDate= 0, $uxtEndDate = 0,$strIssuingWorkplaceUser ='')
	{
		$strErrorMessage = '';

		if($intWifiTimeTypeID < 1)
		{
			$strErrorMessage = 'Please specify the time type';
			return(false);
			
		}
		if($intWifiPromoEventID == 0 && $intPrepayTariffBandID == 0 && $intTariffBaseCostID == 0 && $intFreeTimeReasonID ==0)	
		{
			$strErrorMessage = 'Please specify the time block type';
			return(false);
		}
		if($intNumMinutes < 1)
		{
			$strErrorMessage = 'Cannot insert timeblock of less than 1 minute';
			return false;
		}	

		if((!($intMinutesBeforeExpiry > 0)) && (!($uxtStartDate > 0 && $uxtEndDate > 0))) 
		{
			$strErrorMessage = "Please specify the period covered by the block or it's duration type";
			return(false);
		}
		$strSalesInvoiceClause = '';
		if($intSalesInvoiceID > 0)
		{
			$strSalesInvoiceClause ="intSalesInvoiceID = $intSalesInvoiceID,";
		}
		$strWifiPromoEventClause = '';
		if($intWifiPromoEventID > 0)
		{
			$strWifiPromoEventClause ="intWifiPromoEventID = $intWifiPromoEventID,";
		}	
		$strPrepayTariffBandClause = '';
		if($intPrepayTariffBandID > 0)
		{
			$strPrepayTariffBandClause ="intPrepayTariffBandID = $intPrepayTariffBandID,";
		}	

		$strSubscriptionBaseTariffClause = '';
		if($intTariffBaseCostID > 0)
		{
			$strSubscriptionBaseTariffClause ="intTariffBaseCostID = $intTariffBaseCostID,";
		}

		$strFreeTimeReasonClause = '';
		if($intFreeTimeReasonID > 0)
		{
			$strFreeTimeReasonClause ="intFreeTimeReasonID = $intFreeTimeReasonID,";
		}
		
		$refConnection = get_named_connection_with_db('userdata');

		$strMinutesBeforeExpiry = '';

		if($intMinutesBeforeExpiry > 0)
		{
			$strMinutesBeforeExpiry = "intMinutesBeforeExpiry = $intMinutesBeforeExpiry,";
		}
		$strStartAndEndDate = '';

		if($uxtStartDate > 0 && $uxtEndDate > 0)
		{
			$strStartAndEndDate = "dtmStartDate = '".date('Y-m-d H:i:s',$uxtStartDate)."', dtmEndDate = '".date('Y-m-d H:i:s',$uxtEndDate)."',";
		}


		$strIssuingWorkplaceUserClause = ' vchIssuingWorkplaceUser = null ';
		
		if( (!is_null($strIssuingWorkplaceUser)) && $strIssuingWorkplaceUser != '')
		{
			addslashes($strIssuingWorkplaceUser);
			$strIssuingWorkplaceUserClause =" vchIssuingWorkplaceUser = '{$strIssuingWorkplaceUser}' ";
		}
		
		$strQuery="INSERT INTO tblWifiTimeBlock
		                   SET intTariffID = '{$this->m_intCurrentTariffID}',
		                       intWifiConfigID = {$this->m_intWifiConfigID},
		                       intNumMinutes = $intNumMinutes, $strIssuingWorkplaceUserClause, $strMinutesBeforeExpiry $strStartAndEndDate intWifiTimeTypeID = $intWifiTimeTypeID, $strSalesInvoiceClause  $strWifiPromoEventClause $strPrepayTariffBandClause $strSubscriptionBaseTariffClause $strFreeTimeReasonClause intRemoteTimeBlockID = null, dtmDateAdded = now()";

		PrimitivesQueryOrExit($strQuery,$refConnection,'Make dialup configurator entries');
		$intWifiTimeBlockID = PrimitivesInsertIdGet($refConnection);


		//Now create the time_quota entry in radius
		$arrUserDetails = $this->GetUserDetails();	
		
		$objRadiusUser = CRadiusUser::Create();
		$bolExists = $objRadiusUser->Retrieve($arrUserDetails['strUserName'], $arrUserDetails['strISP'], $arrUserDetails['intServiceID']);

		if(!$bolExists)
		{
			if($this->GetStatus() == 'active' || $this->GetStatus() == 'queued-activate' 
			   || $this->GetStatus() == 'queued-reactivate')
			{
				$this->prvCreateRadiusUserAndSubscription(true);
			}
			else
			{
				$this->prvCreateRadiusUserAndSubscription(false);
			}
			$bolExists = $objRadiusUser->Retrieve($arrUserDetails['strUserName'], $arrUserDetails['strISP'], $arrUserDetails['intServiceID']);
			if(!$bolExists)
			{
				error_log("WIFI Component failed to create user '{$arrUserDetails['strUserName']}@{$arrUserDetails['strISP']}' in radius");
			}
		}
		
		$objTimeQuota = CRadiusTimeQuota::Create();
		
		$arrParameters = array('intRadiusID'        => $objRadiusUser->GetID(),
		                       'intGroupID'         => $this->m_intDialupGroupID,
		                       'intPriority'        => $this->prvGetTimeTypePriority($intWifiTimeTypeID),
		                       'intTimeAllowed'     => $intNumMinutes * 60,
		                       'intValidPeriod'     => $intMinutesBeforeExpiry * 60,
		                       'uxtStartTime'       => $uxtStartDate,
		                       'uxtStopTime'        => $uxtEndDate,
		                       'bolActive'          => true,
		                       'uxtDateLastUpdated' => time(),
		                       'intSyncVersion'     => 0,
		                       'intVersion'         => 1); 
		
		$objTimeQuota->SetAll($arrParameters);
		$objTimeQuota->Save();
	
		$intRadiusTimeBlockID = $objTimeQuota->GetID();
		
		$strQuery="UPDATE tblWifiTimeBlock SET intRemoteTimeBlockID = '$intRadiusTimeBlockID'  WHERE intWifiTimeBlockID ='$intWifiTimeBlockID' ";

		PrimitivesQueryOrExit($strQuery, $refConnection, 'update the local timeblock with the remote id');
		return($intWifiTimeBlockID);
	} 



	/*
	* Create a timeblock using the details provided
	*
	* The other function CreateCreditTimeBlock does not allow you to override dates etc.
	* Beware parameter order. Not logical, but most have defaults.
	* 
	* <AUTHOR>
	* @param
	* @param
	* @param
	* @return integer The new timeblock ID or boolean false, call GetError() on this object.
	*/
	function prvCreateTimeBlock($strBlockType, $uxtEndDate=0, $uxtStartDate=0,$uxtMinimumEndDate =0)
	{
		if($strBlockType != 'CREDIT')
		{
			$this->m_objError = new CError(__FILE__, __LINE__, "Only Credit blocks supported for now...");
			return false;
		}
		

		//TODO
		//Validate block type on this account
		switch($this->m_strTariffModel)
		{
			case 'SUBSCRIPTION':
					
				break;
			case 'PAYG':

				break;
			case 'PREPAY':
			
				break;
			default:
				$this->m_objError = new CError(__FILE__, __LINE__, "The tariff model is unknown for this component");
				return false;

				;
		}
	
		$intNumMinutes          = null;
		$floCostExcVatInPence   = null;
		$floCostIncVatInPence   = null;
		$intMinutesBeforeExpiry = null;

		if(!($intWifiTimeTypeID = $this->WifiTimeTypeHandleToID($strBlockType)))
		{
			$this->m_objError = new CError(__FILE__, __LINE__, "The correct id for the block type could not be found");
			return false;
		}

		// Get subscription base cost 
		$arrTariff = $this->GetAvailableTariffs(array($this->GetCurrentTariffID()) );

		if(!is_array($arrTariff))
		{
			$this->m_objError = new CError(__FILE__, __LINE__, $arrTariff);
			return false;
		}
		if(count($arrTariff) < 1)	
		{
			$this->m_objError = new CError(__FILE__, __LINE__, "The correct tariff could not be found");
			return false;
		}

		if(count($arrTariff) > 1)	
		{
			$this->m_objError = new CError(__FILE__, __LINE__, "Multiple active tariffs for subscription time");
			return false;
		}

		$intTariffBaseCostID    = $arrTariff[0]['intTariffBaseCostID'];
		$intMinutesOfCredit     = $arrTariff[0]['intMinutesOfCredit'];
		$intMinutesBeforeExpiry = $arrTariff[0]['intMinutesBeforeExpiry'];

		if($intMinutesOfCredit < 1 && $strBlockType == 'CREDIT')
		{
			$this->m_objError = new CError(__FILE__, __LINE__, "This tariff does not offer a credit block");
			return(false);
		}
		
	
		//If no date is specified, figure it out...
		if($uxtStartDate <1)
		{
			$uxtStartDate = time();
		}

		if($uxtEndDate == 0)
		{
			// Calculate Dates
			$arrTimeBlockDates = array();
		
			switch($strBlockType)
			{
				case 'CREDIT':
					$uxtEndDate = $this->GetCreditBillingDate(0, -1);
					if($uxtMinimumEndDate > 0 && $uxtMinimumEndDate >= $uxtEndDate)
					{
						$uxtEndDate = $this->GetCreditBillingDate(1, -1);
					}
				break;
				
				case 'SUBSCRIPTION':
					$arrTimeBlockDates = $this->prvSubscriptionDateCalculation($strErrorMessage);

					$uxtEndDate = $arrTimeBlockDates['uxtEndOfMonth'];
				break;
				
				default:
				
				break;
			}	
			
			
			if($strErrorMessage != '')
			{
				return(false);
			}

		}


		// Create Local Block
		if(($intTimeBlockID = $this->prvInsertTimeBlock($strErrorMessage,
		                                                $intWifiTimeTypeID,
		                                                $intMinutesOfCredit,
		                                                $intMinutesBeforeExpiry,
		                                                0,
		                                                0,
		                                                0,
		                                                $intTariffBaseCostID,
		                                                0,
		                                                $uxtStartDate,
		                                                $uxtEndDate) ) === false )
		{
			return(false);	
		}

		return($intTimeBlockID);

	} // func: prvCreateTimeBlock



	/**
	*
	*
	*
	*
	*
	*
	*/
	function prvRecordTimeBlockInvoiceID($intTimeBlockID, $intSalesInvoiceID, $strIssuingWorkplaceUser ='')
	{
		$refConnection = get_named_connection_with_db('userdata');

		$strIssuingWorkplaceUserClause ='';
		
		if($strIssuingWorkplaceUser !='')
		{
			$strIssuingWorkplaceUserClause = ", vchIssuingWorkplaceUser ='$strIssuingWorkplaceUser'";
		}		
				
		$strQuery="UPDATE tblWifiTimeBlock set intSalesInvoiceID = '$intSalesInvoiceID' $strIssuingWorkplaceUserClause WHERE intWifiConfigID = {$this->m_intWifiConfigID} AND (tblWifiTimeBlock.intSalesInvoiceID = 0 OR  tblWifiTimeBlock.intSalesInvoiceID is null) AND intWifiTimeBlockID = $intTimeBlockID";
		PrimitivesQueryOrExit($strQuery,$refConnection,'Make dialup configurator entries');
		$intNumEffected = PrimitivesAffectedRowsGet($refConnection);

		if($intNumEffected < 1)
		{
			return("Time Block '$intTimeBlockID' could not be found to update or it's intSalesInvoiceID is already set");
		}
		return('');
	}


	
	/**
	*
	*
	*
	*
	*
	*
	*/
	function prvRecordTimeBlockFreeTimeReasonID($intTimeBlockID, $intFreeTimeReasonID, $strIssuingWorkplaceUser ='')
	{
		$refConnection = get_named_connection_with_db('userdata');

		$strIssuingWorkplaceUserClause ='';
		
		if($strIssuingWorkplaceUser !='')
		{
			$strIssuingWorkplaceUserClause = ", vchIssuingWorkplaceUser ='$strIssuingWorkplaceUser'";
		}		

		
		$strQuery="UPDATE tblWifiTimeBlock set  intFreeTimeReasonID = '$intFreeTimeReasonID'  $strIssuingWorkplaceUserClause WHERE intWifiConfigID = {$this->m_intWifiConfigID} AND (tblWifiTimeBlock.intFreeTimeReasonID = 0 OR  tblWifiTimeBlock.intFreeTimeReasonID is null) AND intWifiTimeBlockID = $intTimeBlockID";
		PrimitivesQueryOrExit($strQuery,$refConnection,'Make dialup configurator entries');
		$intNumEffected = PrimitivesAffectedRowsGet($refConnection);

		if($intNumEffected < 1)
		{
			return "Time Block '$intTimeBlockID' could not be found to update or it's intSalesInvoiceID is already set";
		}
		
		return '';

		
	} 


	/**
	*
	*
	*
	*
	*
	*
	*/
	
	function PurchaseSubscriptionTimeBlock(&$strErrorMessage, $intSalesInvoiceID, $uxtStartTime=0,$uxtStopTime =0)
	{
		$strErrorMessage        = '';
		$intNumMinutes          = null;
		$floCostExcVatInPence   = null;
		$floCostIncVatInPence   = null;
		$intMinutesBeforeExpiry = null;

		if(!($intWifiTimeTypeID = $this->WifiTimeTypeHandleToID('SUBSCRIPTION')))
		{
			$strErrorMessage = "The correct time band for subscription time could not be found";
			return false;
		}

		$arrTariff = $this->GetAvailableTariffs(array($this->GetCurrentTariffID()));

		if(count($arrTariff) < 1)	
		{
			$strErrorMessage = "The correct tariff for subscription time could not be found";
			return false;
		}

		if(count($arrTariff) > 1)	
		{
			$strErrorMessage = "Multiple active tariffs for subscription time";
			return false;
		}
		$intTariffBaseCostID    = $arrTariff[0]['intTariffBaseCostID'];
		$intNumMinutes          = $arrTariff[0]['intAutomaticMinutesPerCycle'];
		$intMinutesBeforeExpiry = $arrTariff[0]['intMinutesBeforeExpiry'];

		if($uxtStartTime > 0 && $uxtStopTime > 0 && $uxtStopTime >= $uxtStartTime)
		{
			$arrTimeBlockDates = array('uxtBeginningOfMonth' => $uxtStartTime,
			                           'uxtEndOfMonth'       => $uxtStopTime);
		}
		else
		{
			// Calculate Dates
			$arrTimeBlockDates = $this->prvSubscriptionDateCalculation($strErrorMessage);
			if($strErrorMessage != '')
			{
				return false;
			}
		}

		// Record time creation attempt
		if(($intTimeBlockID = $this->prvInsertTimeBlock($strErrorMessage,$intWifiTimeTypeID,$intNumMinutes,$intMinutesBeforeExpiry,$intSalesInvoiceID,0, 0,$intTariffBaseCostID,0,$arrTimeBlockDates['uxtBeginningOfMonth'],$arrTimeBlockDates['uxtEndOfMonth']))===false)
		{
			return false;	
		}

		return $intTimeBlockID;
	}






	/**
	*
	*
	*
	*
	*
	*
	*/
	function CreateCreditTimeBlock(&$strErrorMessage)
	{
		$strErrorMessage        = '';
		$intNumMinutes          = null;
		$floCostExcVatInPence   = null;
		$floCostIncVatInPence   = null;
		$intMinutesBeforeExpiry = null;

		if(!($intWifiTimeTypeID = $this->WifiTimeTypeHandleToID('CREDIT')))
		{
			$strErrorMessage = "The correct time band for subscription time could not be found";
			return false;
		}

		// Get subscription base cost 
		$arrTariff = $this->GetAvailableTariffs(array($this->GetCurrentTariffID()));

		if(count($arrTariff) < 1)	
		{
			$strErrorMessage = "The correct tariff for Credit time could not be found";
			return false;
		}

		if(count($arrTariff) > 1)	
		{
			$strErrorMessage = "Multiple active tariffs for subscription time";
			return false;
		}
		
		
		$intTariffBaseCostID    = $arrTariff[0]['intTariffBaseCostID'];
		$intMinutesOfCredit     = $arrTariff[0]['intMinutesOfCredit'];
		$intMinutesBeforeExpiry = $arrTariff[0]['intMinutesBeforeExpiry'];

		if($intMinutesOfCredit < 1)
		{
			$strErrorMessage = "This tariff does not offer a credit block";
			return(false);
		}
		// Calculate Dates
		$uxtEndOfMonth = $this->GetCreditBillingDate(0, -1);

		// Record time creation attempt
		if(($intTimeBlockID = $this->prvInsertTimeBlock($strErrorMessage,$intWifiTimeTypeID,$intMinutesOfCredit,$intMinutesBeforeExpiry,0,0, 0,$intTariffBaseCostID,0,time(), $uxtEndOfMonth))===false)
		{
			return(false);	
		}
	
		return($intTimeBlockID);
	}




	/**
	*
	*
	*
	*
	*
	*
	*/
	function PurchasePrepayTimeBlock(&$strErrorMessage, $intPrepayTimeBandID, 
	                                 $intSalesInvoiceID, $strIssuingWorkplaceUser)
	{
		// Check this block is on my Tariff and get the details
		$strErrorMessage        = '';
		$intNumMinutes          = null;
		$floCostExcVatInPence   = null;
		$floCostIncVatInPence   = null;
		$intMinutesBeforeExpiry = null;

		if(($strCheck = $this->prvBolTimeBandTariff($this->m_intCurrentTariffID,$intPrepayTimeBandID,
		                                            $intNumMinutes,$floCostExcVatInPence,
							    $floCostIncVatInPence,
							    $intMinutesBeforeExpiry)) != 'Y')
		{
			return("Prepay TimeBand ID '$intPrepayTimeBandID' is not allowed on tariff '$$this->m_intCurrentTariffID' : $strCheck");
		}

		if(!($intWifiTimeTypeID = $this->WifiTimeTypeHandleToID('PREPAY')))
		{
			return("The correct time band for Pay As You go time could not be found");
		}

		// Record time creation attempt
		
		if(($intTimeBlockID = $this->prvInsertTimeBlock($strErrorMessage,$intWifiTimeTypeID,
		                                                $intNumMinutes,$intMinutesBeforeExpiry,
		                                                $intSalesInvoiceID,0, 
		                                                $intPrepayTimeBandID,0,0,0,0,
		                                                $strIssuingWorkplaceUser))===false)
		{
			return false;	
		}

		return $intTimeBlockID;
	}



	/**
	*
	*
	*
	*
	*
	*
	*/
	function prvCreatePrepayPromoTimeBlock(&$strErrorMessage, $intPromoID,$intWifiTimeTypeID, $intNumMinutes, $intMinutesBeforeExpiry,  $intSalesInvoiceID=0)
	{
		// Check this block is on my Tariff and get the details
		$strErrorMessage='';

		if($intNumMinutes < 0)
		{
			$strErrorMessage='Number of minutes to add is negative';
			return false;	
		}
		if($intNumMinutes == 0)
		{
			return true;
		}
		// Record time creation attempt
		if(($intTimeBlockID = $this->prvInsertTimeBlock($strErrorMessage,$intWifiTimeTypeID,$intNumMinutes,$intMinutesBeforeExpiry,$intSalesInvoiceID,$intPromoID))===false)
		{
			return false;	
		}
	
		return $intTimeBlockID;

	}



	/**
	*
	*
	*
	*
	*
	*
	*/
	function prvSubscriptionDateCalculation(&$strErrorMessage, $uxtStartDate =0)
	{
		$strErrorMessage = '';

		// Calculate the start and end dates
		$uxtDate = time();
		if($uxtStartDate > 0)
		{
			$uxtDate = $uxtStartDate;
		}
		$arrStartDate = getdate($uxtDate);
		$intMaxDay    = date('t',$uxtDate);

		if($intMaxDay < $this->m_intBillingDay)
		{
			$arrStartDate['mday'] = $intMaxDay;
		}
		$uxtBeginningOfMonth = mktime(0,0,0,$arrStartDate['mon'],$arrStartDate['mday'],$arrStartDate['year']);
		$uxtEndDate          = mktime(0,0,0,$arrStartDate['mon']+1,1,$arrStartDate['year']);
		$arrEndDate          = getdate($uxtEndDate);
		$intMaxDay           = date('t',$uxtEndDate);

		$arrEndDate['mday'] = $this->m_intBillingDay;
		if($intMaxDay < $this->m_intBillingDay)
		{
			$arrEndDate['mday'] = $intMaxDay;
		}

		$uxtEndOfMonth   = mktime(23,59,59, $arrEndDate['mon'],$arrEndDate['mday'],$arrEndDate['year']); 

		return array('uxtBeginningOfMonth' => $uxtBeginningOfMonth, 'uxtEndOfMonth' => $uxtEndOfMonth);

	}	


	/*
	* Calculate a billing date for credit blocks. (service billing date, including interims)
	* 
	* Don't even think about using this.  It will mess with your head.
	* If you think it does what you need, you're wrong.  Go and have a cold shower.
	*
	* <AUTHOR>
	* @param intIndex offsets the month from now. 0 will return the next billing date
	*        1 will get you the one after that, -1 will get you the last one etc
	* @param intOffsetDays offsets the number of days from whatever date you choose.  '-1' is needed
	*        for credit blocks, which must be expired the day before they are next billed
	* @return integer Unix Timestamp of the next credit billing date
	*/
	function GetCreditBillingDate($intIndex=0, $intOffsetDays=0)
	{		
		if($this->m_intServiceID < 1)
		{
			return false;
		}
	
		//Get data to work from - credit blocks run form service billing dates, so get those.
		$arrService = userdata_service_get($this->m_intServiceID);
		$intServiceBillingDay = $arrService['invoice_day'];

		if($intServiceBillingDay < 1 || $intServiceBillingDay > 31)
		{
			return false;
		}

		//Now
		$arrNow = getdate();

		//First day of the month of the billing date reqired

		// the billing day this month is past or offset would move back into previous months roll on to next
		if($arrNow['mday'] >=  $intServiceBillingDay || ( ($intServiceBillingDay + $intOffsetDays) <1 ) )
		{
			$arrNow['mon']++;
		}

		$uxtBillingDate = mktime(0,0,0, $arrNow['mon']+($intIndex), 1, $arrNow['year']);
		$intNumDaysInMonth = date('t',$uxtBillingDate);

		if(($intNumDaysInMonth + $intOffsetDays) < 1)
		{
			$arrNow['mon']++;
			$uxtBillingDate = mktime(0,0,0, $arrNow['mon']+($intIndex), 1, $arrNow['year']);
			$intNumDaysInMonth = date('t',$uxtNextBillingDate);
		}

		//Adjustment for short months
		$arrBillingDate = getdate($uxtBillingDate);
		
		if($intNumDaysInMonth < $intServiceBillingDay)
		{
			$uxtBillingDate = mktime(0,0,0,$arrBillingDate['mon'], $intNumDaysInMonth + ($intOffsetDays), $arrBillingDate['year']);
		}
		else
		{
			$uxtBillingDate = mktime(0,0,0,$arrBillingDate['mon'], $intServiceBillingDay + ($intOffsetDays), $arrBillingDate['year']);
		}

		return $uxtBillingDate;

	} //func: GetCreditBillingDate
	


	/**
	*
	*
	*
	*
	*
	*
	*/
	function prvCreateSubscriptionPromoTimeBlock(&$strErrorMessage, $intPromoID, $intWifiTimeTypeID, $intNumMinutes, $intMinutesBeforeExpiry, $intSalesInvoiceID=0)
	{
		// Check this block is on my Tariff and get the details
		$strErrorMessage='';

		if($intNumMinutes < 0)
		{
			$strErrorMessage='Number of minutes to add is negative';
			return false;	
		}

		$arrTimeBlockDates = $this->prvSubscriptionDateCalculation($strErrorMessage);
		
		if($strErrorMessage != '')
		{
			return false;
		}

		// Record time creation attempt
		if(($intTimeBlockID = $this->prvInsertTimeBlock($strErrorMessage,$intWifiTimeTypeID,$intNumMinutes,$intMinutesBeforeExpiry,$intSalesInvoiceID,$intPromoID,0,0,0,$arrTimeBlockDates['uxtBeginningOfMonth'],$arrTimeBlockDates['uxtEndOfMonth']))===false)
		{
			return false;	
		}


		return $intTimeBlockID;
	}









	/**
	*
	*
	*
	*
	*
	*
	*/
	function prvCreateCreditPromoTimeBlock(&$strErrorMessage, $intPromoID,$intWifiTimeTypeID, $intNumMinutes, $intMinutesBeforeExpiry)
	
	{
		// Check this block is on my Tariff and get the details
		$strErrorMessage='';

		if($intNumMinutes < 0)
		{
			$strErrorMessage='Number of minutes to add is negative';
			return false;	
		}

		$uxtEndDate = $this->GetCreditBillingDate(0, -1);
		if(!$uxtEndDate)
		{
			$strErrorMessage='Failed to get end date';
			return false;
		}

		// Record time creation attempt
		if(($intTimeBlockID = $this->prvInsertTimeBlock($strErrorMessage,$intWifiTimeTypeID,$intNumMinutes,$intMinutesBeforeExpiry,$intSalesInvoiceID,$intPromoID,0,0,0,time(),$uxtEndDate))===false)
		{
			return false;	
		}


		return $intTimeBlockID;
	}	




	/**
	* Look up the priority for a particula time type
	*
	* @access private
	* <AUTHOR>
	* @param  integer the id of the time type from tblWifiTimeType
	* @return integer the priority value from the defines at the top of this file
	*/
	function prvGetTimeTypePriority($intTimeTypeID=0)
	{
		$strQuery = "SELECT vchHandle from vblWifiTimeType WHERE intWifiTimeTypeID = $intTimeTypeID";

		$refConnection = get_named_connection_with_db('product');
		
		$refResult = PrimitivesQueryOrExit($strQuery, $refConnection, 'Get time type handle from id');
		
		$strTimeType = PrimitivesResultGet($refResult, 'vchHandle');
		
		switch ($strTimeType)
		{
			case 'SUBSCRIPTION':
			case 'PROMOTIONAL_SUBSCRIPTION':
				return PRIORITY_WIFI_SUBSCRIPTION;
			break;

			case 'CREDIT':
			case 'PROMOTIONAL_CREDIT':
				return PRIORITY_WIFI_CREDIT;
			break;

			case 'FREE_TIME':
				return PRIORITY_WIFI_GIFT;
			break;

			case 'PREPAY':
			case 'PROMOTIONAL_PREPAY':
				return PRIORITY_WIFI_PREPAY;
			break;
			
			default:
				return PRIORITY_WIFI_DEFAULT;
			break;
		}
		
	}



	/**
	*
	*
	*
	*
	*
	*
	*/
	function DisableWifiComponent()
	{
		
		if ( in_array($this->GetStatus(), array('destroyed','queued-destroy','invalid')) )
		{
			return 'Component is destroyed and can not be deactivated';
		}
		
		$bolActivationEventAdded = false;

		if( ($strError = $this->prvCheckOrCreateRemoteComponent($bolActivationEventAdded,0,false)) != '')
		{
			return $strError;
		}

	
		//Get the radius user
		$arrUserDetails = $this->GetUserDetails();
		$objRadiusUser = CRadiusUser::Create();
		$bolExists = $objRadiusUser->Retrieve($arrUserDetails['strUserName'], $arrUserDetails['strISP'], $arrUserDetails['intServiceID']);
		
		if(!$bolExists)
		{
			//Nothing to delete.
			$this->prvSetStatus('deactive');
			return '';
		}
		

		//Delete the subscription 
		$objRadiusSubscription = CRadiusSubscription::Create();
		$bolExists = $objRadiusSubscription->Retrieve($objRadiusUser->GetID(), $this->m_intDialupGroupID);

		if(!$bolExists)
		{
			//Nothing to delete.
			$this->prvSetStatus('deactive');
			return '';
		}
		
		$objRadiusSubscription->DeleteSubscription($this->GetAllWifiDialupGroups());
		
		//Also flag the user inactive if they have no more active dialup components 
		if(!$this->DialupHasActiveComponent($this->m_intServiceID))
		{
			$objRadiusUser->SetAll(array('bolActive' => false));
			$objRadiusUser->Save();
		}

		$this->prvSetStatus('deactive');
		$this->LogDeactivation();

		return '';

	}



	function GetAllWifiDialupGroups()
	{
		static $arrDialupGroups = array();

		if(count($arrDialupGroups) > 0)
		{
			return $arrDialupGroups;
		}
		$dbConnection = get_named_connection_with_db('product');

		$strQuery ="SELECT DISTINCT intDialupGroupID FROM tblComponentWIFIConfig";

		$resResult        = PrimitivesQueryOrExit($strQuery, $dbConnection);
		$arrControlGroups = PrimitivesResultsAsListGet($resResult);

		return $arrControlGroups;
	}


	/**
	*
	*
	*
	*
	*
	*
	*/
	function prvAllowedToDestroy()
	{
		switch($this->m_strTariffModel)	
		{
			case 'PREPAY':
				return '';
			break;
			
			case 'PAYG':
				/* Intentional Fallthrough */
			case 'SUBSCRIPTION':
				if($this->m_uxtScheduledDestructionDate < 1)
				{
					return('This Wifi component is not scheduled for destruction, please schedule the account for destruction or use the immedate destruction option');
				}

				
				if($this->m_uxtScheduledDestructionDate > time())
				{
					return('This Wifi component is scheduled for on '.
							date('d-m-Y',$this->m_uxtScheduledDestructionDate).
							'. Please use the immediate destruction option or wait until that date');
				}
				return '';
			break;
			
			default: 
				return "The Tariff model '{$this->m_strTariffModel}' is not supported by this component";
			break;
		}
	}


	/**
	*
	*
	*
	*
	*
	*
	*/
	function DestroyWifiComponent($bolImmediateDestruction = false)
	{
		if((!isset($this->m_strTariffModel)) || $this->m_strTariffModel == '')
		{
			$this->prvSetStatus('destroyed');
			if(isset($this->m_intServiceID) && $this->m_intServiceID > 0)
			{
				$this->LogImmediateDestruction();
			}
			return '';
		}

		// Check if scheduled for destruction
		if( (!$bolImmediateDestruction) && (($strErrorMessage = $this->prvAllowedToDestroy()) !='') )
		{
			return($strErrorMessage);	
		}

		$this->prvSetStatus('queued-destroy');
		
		//Expire any left over credit blocks immediately.
		//There shouldn't be any, but just in case, kill 'em.
		$strErrorMessage ='';

		$arrCreditBlocks = $this->GetUnexpiredCreditTime($strErrorMessage);
		if( count($arrCreditBlocks) > 0)
		{
			foreach($arrCreditBlocks as $arrCreditBlock)
			{
				$this->ExpireTimeBlock($arrCreditBlock['intWifiTimeBlockID']);
			}
		}

		
		//Get the radius user
		$arrUserDetails = $this->GetUserDetails();
		$objRadiusUser = CRadiusUser::Create();
		$bolExists = $objRadiusUser->Retrieve($arrUserDetails['strUserName'], $arrUserDetails['strISP'], $arrUserDetails['intServiceID']);
		
		if(!$bolExists)
		{
			//Nothing to delete.
			$this->prvSetStatus('destroyed');
			return '';
		}
		

		//Delete the subscription 
		$objRadiusSubscription = CRadiusSubscription::Create();
		$bolExists = $objRadiusSubscription->Retrieve($objRadiusUser->GetID(), $this->m_intDialupGroupID);

		if(!$bolExists)
		{
			//Nothing to delete.
			$this->prvSetStatus('destroyed');
			return '';
		}
		
		$objRadiusSubscription->DeleteSubscription($this->GetAllWifiDialupGroups());
		
		//Also flag the user inactive if they have no more active dialup components 
		if(!$this->DialupHasActiveComponent($this->m_intServiceID))
		{
			$objRadiusUser->SetAll(array('bolActive' => false));
			$this->prvSetStatus('destroyed');
			$objRadiusUser->Save();
		}

		$this->prvSetStatus('destroyed');

		if($this->m_intScheduledDestructionEventID > 0 && (!$bolImmediateDestruction))
		{
			$this->LogScheduledDestruction();
		}
		else
		{
			$this->LogImmediateDestruction();
		}

		return '';

	} // func: DestroyWifiComponent


	function DialupHasActiveComponent($intServiceID)
	{
		$dbWorkplace = get_named_connection_with_db('userdata');

		$strQuery ="select count(*) as intActiveDialups from userdata.components uc inner join products.component_radius_config pw
		                   on uc.component_type_id = pw.service_component_id where uc.status in ('active','queued-deactivate','queued-deconfigure','queued-destroy') and service_id ='$intServiceID'";
		$resResult   = PrimitivesQueryOrExit($strQuery,$dbWorkplace);
		$intActiveDialups = PrimitivesResultGet($resResult,'intActiveDialups');

		if($intActiveDialups)
		{
			return true;
		}

		return false;
	} // func : DialupHasActiveComponent



	/**
	*
	*
	*
	*
	*
	*
	*/
	function GetPrepayTimeBands($intUseTariffID =0, $bolIncludePendingPriceBands = false, $bolIncludeExpiredPriceBands = false )
	{

		if($intUseTariffID == 0 && isset($this->m_intCurrentTariffID) && $this->m_intCurrentTariffID > 0)
		{
			$intUseTariffID = $this->m_intCurrentTariffID;
		}

		$strAND = 'AND';
		$strOmitExpiredBands ='';
		if(!$bolIncludeExpiredPriceBands)
		{
			$strOmitExpiredBands = "$strAND dtmDateActive <= now()";
		}
		$strOmitPendingBands ='';
		if(!$bolIncludePendingPriceBands)
		{
			$strOmitPendingBands = "$strAND (dtmDateEnded IS NULL OR dtmDateEnded>= now())";
		}
			
		$strQuery="SELECT intPrepayTariffBandID, 'GBP' as strCurrencyCode, intMinutesInBlock, decCostExcVatInPence as floCostExcVatInPence, ".
		                " decCostIncVatInPence as floCostIncVatInPence, dtmDateAdded, dtmDateActive, dtmDateEnded ".
				" FROM tblPrepayTariffBand ".
				" WHERE intTariffID = '$intUseTariffID' $strOmitExpiredBands $strOmitPendingBands";

		$refConnection = get_named_connection_with_db('product');
		$refResult = PrimitivesQueryOrExit($strQuery,$refConnection,'Get Prepay price bands');
		$arrAvailableTimeBlocks = PrimitivesResultsAsArrayGet($refResult);

		return($arrAvailableTimeBlocks);
	} 



	/**
	* Gets the radius time_quota id for this block
	*
	*
	*
	*
	* @return integer The Radius time_quota id for this block
	*/
	function prvLocalToRemoteTimeBlockID($intWifiTimeBlockID, $arrTimeTypes = array('PROMOTIONAL_CREDIT','CREDIT'))
	{
		$refConnection = get_named_connection_with_db('userdata');
		$strTypeJoin = '';
		if(count($arrTimeTypes) > 0)
		{
			$strTypeJoin = ' INNER JOIN products.vblWifiTimeType tt on  tt.intWifiTimeTypeID = tb.intWifiTimeTypeID '.primitives_IN_clause('AND','tt.vchHandle',$arrTimeTypes);

		}

		$strQuery = "SELECT tb.intRemoteTimeBlockID FROM tblWifiTimeBlock tb $strTypeJoin ".
		            "WHERE tb.intWifiTimeBlockID = '$intWifiTimeBlockID' AND tb.intWifiConfigID = '$this->m_intWifiConfigID'";

		$refResult            = PrimitivesQueryOrExit($strQuery,$refConnection,'Translate Remote to Local ID');
		$intRemoteTimeBlockID = PrimitivesResultGet($refResult,'intRemoteTimeBlockID');

		$intRemoteTimeBlockID = intval($intRemoteTimeBlockID);
		return $intRemoteTimeBlockID;

	}



	/**
	* 
	*
	*
	* @access private
	* <AUTHOR>
	* @param  integer 
	* @return array promotional timeblock 
	*/
	function prvGetPromotionalTimeBlock($intWifiPromoEventID)
	{
		$refConnection = get_named_connection_with_db('userdata');

		$strQuery = "SELECT tb.intWifiTimeBlockID,
		                    tb.intRemoteTimeBlockID
		               FROM tblWifiTimeBlock tb
		         INNER JOIN products.tblWifiPromoOffer wpe
		                 ON wpe.intWifiPromoEventID = tb.intWifiPromoEventID
		              WHERE tb.intWifiPromoEventID = '$intWifiPromoEventID'
		                AND tb.intWifiConfigID = '{$this->m_intWifiConfigID}'";

		$refResult              = PrimitivesQueryOrExit($strQuery,$refConnection,'Translate Remote to Local ID');
		$arrPromotionaTimeBlock = PrimitivesResultGet($refResult);

		return $arrPromotionaTimeBlock;
	}



	/**
	* Expires a timeblock by setting the end date to now.
	*
	* Sets first the remote and then the local timeblock enddates to now
	*
	* @access public
	* @param  integer Local Timeblock id
	* @param  integer uxt Expiry time.  Leave default to set to NOW
	* @return boolean true on success, false on failure with objError set
	*/
	function ExpireTimeBlock($intLocalTimeBlockID, $uxtExpiryTime=0)
	{
		$strErrorMessage = '';

		if($uxtExpiryTime == 0)
		{
			$uxtExpiryTime = time();
		}
		
		// Get the remote id	
		$intRemoteTimeBlockID = $this->prvLocalToRemoteTimeBlockID($intLocalTimeBlockID);

		if(! $intRemoteTimeBlockID)
		{
			$this->m_objError = new CError(__FILE__, __LINE__, "Time block '$intLocalTimeBlockID' does not exist");
			return false;
		}
		

		// Expire remote block in radius
		$objTimeQuota = CRadiusTimeQuota::Create($intRemoteTimeBlockID);
		
		if($objTimeQuota === false)
		{
			$this->m_objError = new CError(__FILE__, __LINE__, "Could not find remote time block, id $intRemoteTimeBlockID");
			return false;
		}
	
		$arrRemoteTimeblock = $objTimeQuota->GetAll();

		if($arrRemoteTimeblock['uxtStopTime'] > $uxtExpiryTime || $arrRemoteTimeblock['uxtStopTime'] == '')
		{
			//Expire
			$arrExpiryParams = array('uxtStopTime' => $uxtExpiryTime);

			$objTimeQuota->SetAll($arrExpiryParams);
			$bolSucceeded = $objTimeQuota->Save();
		
			if(! $bolSucceeded)
			{
				$this->m_objError = new CError(__FILE__, __LINE__, "Could not expire remote time block, id $intRemoteTimeBlockID");
				return false;
			}
		}
		else
		{
			// Already expired in radius.  Set local block to match.
			$uxtExpiryTime = $arrRemoteTimeblock['uxtStopTime'];
		}

		
		// Set local expiry time to match radius
		$refConnection = get_named_connection_with_db('userdata');
						
		$strQuery="UPDATE tblWifiTimeBlock
					  SET dtmEndDate = FROM_UNIXTIME($uxtExpiryTime)
					WHERE intWifiTimeBlockID = $intLocalTimeBlockID";
		
		PrimitivesQueryOrExit($strQuery, $refConnection, 'expire the local timeblock');
		
		return true;
		
	}



	/**
	* Sycronise the end of active credit time blocks with the next invoice date
	*
	* Will take care of moving the expiry date to one day before the invoice date you pass it.
	*
	* <AUTHOR>
	* @param  string ref Error message
	* @param  integer Local Timeblock ID
	* @param  integer uxt The SERVICE next invoice date
	* @return boolean true on success, false on failure
	*/
	function SyncExpiryTimeToInvoiceDate(&$strErrorMessage, $intLocalTimeBlockID, $uxtServiceNextInvoiceDate)
	{
		$strErrorMessage = '';
		
		//Expiry time must in this case be set the day before the invoice date
	
		$uxtEndOfMonth = $this->GetCreditBillingDate(0, -1);
	
		$bolResult = $this->ExpireTimeBlock($intLocalTimeBlockID, $uxtEndOfMonth);

		if(!$bolResult)
		{
			$strErrorMessage = "Unable to sync credit block expiry time". $this->objError->GetMessage();
			return false;
		}

		return true;
	}

	/**
	*
	*
	*
	*
	*/
	function MarkCreditBlockInvoiced(&$strErrorMessage,$intWifiTimeBlockID,
	                                  $intSalesInvoiceID = 0, $strFreeTimeReasonHandle ='',
	                                  $strIssuingWorkplaceUser ='')
	{
		$strErrorMessage = '';


		// Translate Time block to remote ID 
		if(!($intRemoteTimeBlockID = $this->prvLocalToRemoteTimeBlockID($intWifiTimeBlockID,$arrTimeTypes)))
		{
			$strErrorMessage ="Time block '$intWifiTimeBlockID' does not exist";
			return false;
		}

		$intFreeTimeReasonID = 0;
		
		if($intSalesInvoiceID > 0)
		{
			// Check the invoice belongs to this customer
			if(!$this->prvCheckInvoiceBelongsToThisUser($intSalesInvoiceID))
			{
				return("Invoice ID '$intSalesInvoiceID' has not been paid or does not belong to this user");
			}

		}
		else
		{
			$intFreeTimeReasonID = $this->WifiFreeTimeReasonHandleToID($strFreeTimeReasonHandle);
			if((!$intFreeTimeReasonID) || is_null($intFreeTimeReasonID) ||$strFreeTimeReasonHandle < 0)
			{
				return("The free time reason '$strFreeTimeReasonHandle' is unknown");
			}
		}


		if($intSalesInvoiceID > 0)
		{
			$strErrorMessage = $this->prvRecordTimeBlockInvoiceID($intWifiTimeBlockID, $intSalesInvoiceID,$strIssuingWorkplaceUser);
		}
		else
		{
			$strErrorMessage = $this->prvRecordTimeBlockFreeTimeReasonID($intWifiTimeBlockID, $intFreeTimeReasonID,$strIssuingWorkplaceUser);
		}

		return $strErrorMessage;	
	}


	/**
	* Fetches an array of all the invoicable timeblocks
	*
	*
	*
	*
	*
	*/
	function GetInvoiceableCreditTime(&$strErrorMessage,$uxtDateOfRun=0)
	{
		$strErrorMessage = '';
		
		if($uxtDateOfRun == 0)
		{
			$uxtDateOfRun = time();
		}

		//Get Radius user id
		$arrUserDetails = $this->GetUserDetails();
		$objRadiusUser = CRadiusUser::Create();
		$objRadiusUser->Retrieve($arrUserDetails['strUserName'], $arrUserDetails['strISP'], $arrUserDetails['intServiceID']);
		
		if(!$intRadiusID = $objRadiusUser->GetID())
		{
			$strErrorMessage = "Cannot find User radius id";
			return false;
		}


		$strQuery = "SELECT time_id as intTimeID,
		                    radius_id as intRadiusID,
		                    group_id as intGroupID,
		                    priority as intPriority,
		                    valid_period as intValidPeriod,
		                    time_allowed as intTimeAllowed,
		                    UNIX_TIMESTAMP(start_time) as uxtStartTime,
		                    UNIX_TIMESTAMP(stop_time) as uxtStopTime,
	 	                    time_used as intTimeUsed,
		                    IF(active = 'Y', 'true', 'false') as bolActive,
		                    UNIX_TIMESTAMP(date_last_updated) as uxtDateLastUpdated,
		                    sync_version as intSyncVersion,
		                    version as intSyncVersion
		               FROM time_quota
		              WHERE radius_id = $intRadiusID
		                AND group_id = '{$this->m_intDialupGroupID}'
		                AND stop_time < from_unixtime($uxtDateOfRun)
		                AND stop_time is not null
				AND time_used > 0 ";

		$refConnection   = get_named_connection_with_db('radius_reporting');
		$refResult       = PrimitivesQueryOrExit($strQuery, $refConnection,'Get Unexpired Credit blocks from radius');
		$arrRemoteBlocks = PrimitivesResultsAsArrayGet($refResult);
	
		if(count($arrRemoteBlocks) == 0)
		{
			return array();
		}

		$arrRemoteBlockIDs = array();

		foreach(array_keys($arrRemoteBlocks) as $i)
		{
			$arrRemoteBlockIDs[] = $arrRemoteBlocks[$i]['intTimeID'];
		}
		
		$arrMergedBlocks = $this->prvGetLocalBlocksAndMerge($strErrorMessage, $arrRemoteBlocks, array(), $arrRemoteBlockIDs);
		
		//Strip out only the credit blocks which are not screwed and have not been paid
		foreach(array_keys($arrMergedBlocks) as $k)
		{
			if($arrMergedBlocks[$k]['strTimeBlockTypeHandle'] != 'CREDIT' && $arrMergedBlocks[$k]['strTimeBlockTypeHandle'] != 'PROMOTIONAL_CREDIT')
			{
				unset($arrMergedBlocks[$k]);
				continue;
			}

			if(isset($arrMergedBlocks[$k]['strBlockErrorMessage']) && $arrMergedBlocks[$k]['strBlockErrorMessage'] != '')
			{
				unset($arrMergedBlocks[$k]);
				continue;
			}
			
			if(isset($arrMergedBlocks[$k]['intWifiScheduledPaymentID']) && $arrMergedBlocks[$k]['intWifiScheduledPaymentID'] > 0)
			{
				unset($arrMergedBlocks[$k]);
				continue;
			}

			if(isset($arrMergedBlocks[$k]['intSalesInvoiceID']) && $arrMergedBlocks[$k]['intSalesInvoiceID'] > 0)
			{
				unset($arrMergedBlocks[$k]);
			}
			
		}

		return $arrMergedBlocks;
	} 

	function prvRecordScheduledPaymentIDOnTimeBlock($intWifiTimeBlockID,$intWifiScheduledPaymentID)
	{
		$strQuery ="UPDATE  userdata.tblWifiTimeBlock SET intWifiScheduledPaymentID = '$intWifiScheduledPaymentID' 
		                    WHERE intWifiConfigID = '".$this->GetConfigID()."' 
		                           AND intWifiTimeBlockID = '{$intWifiTimeBlockID}' 
		                           AND (intWifiScheduledPaymentID is NULL OR intWifiScheduledPaymentID < 1)";
		$refConnection = get_named_connection_with_db('userdata');
		$refResult = PrimitivesQueryOrExit($strQuery,$refConnection,'Record Scheduled Payment');

		return true;

	} // func : prvRecordScheduledPaymentIDOnTimeBlock


	/**
	* Fetches an array of all the time blocks which are unexpired credit blocks
	*
	*
	*
	*
	*
	*/
	function GetUnexpiredCreditTime(&$strErrorMessage)
	{
		$strErrorMessage = '';

		//Get Radius user id
		$arrUserDetails = $this->GetUserDetails();
		$objRadiusUser = CRadiusUser::Create();
		$objRadiusUser->Retrieve($arrUserDetails['strUserName'], $arrUserDetails['strISP'], $arrUserDetails['intServiceID']);
		
		if(!$intRadiusID = $objRadiusUser->GetID())
		{
			$strErrorMessage = "Cannot find User radius id";
			return false;
		}


		$strQuery = "SELECT time_id as intTimeID,
		                    radius_id as intRadiusID,
		                    group_id as intGroupID,
		                    priority as intPriority,
		                    valid_period as intValidPeriod,
		                    time_allowed as intTimeAllowed,
		                    UNIX_TIMESTAMP(start_time) as uxtStartTime,
		                    UNIX_TIMESTAMP(stop_time) as uxtStopTime,
	 	                    time_used as intTimeUsed,
		                    IF(active = 'Y', 'true', 'false') as bolActive,
		                    UNIX_TIMESTAMP(date_last_updated) as uxtDateLastUpdated,
		                    sync_version as intSyncVersion,
		                    version as intSyncVersion
		               FROM time_quota
		              WHERE radius_id = $intRadiusID
		                AND group_id = '{$this->m_intDialupGroupID}'
		                AND stop_time >= NOW()
		                AND stop_time is not null";

		$refConnection   = get_named_connection_with_db('radius_reporting');
		$refResult       = PrimitivesQueryOrExit($strQuery, $refConnection,'Get Unexpired Credit blocks from radius');
		$arrRemoteBlocks = PrimitivesResultsAsArrayGet($refResult);
	
		if(count($arrRemoteBlocks) == 0)
		{
			return array();
		}

		$arrRemoteBlockIDs = array();

		foreach(array_keys($arrRemoteBlocks) as $i)
		{
			$arrRemoteBlockIDs[] = $arrRemoteBlocks[$i]['intTimeID'];
		}
		
		$arrMergedBlocks = $this->prvGetLocalBlocksAndMerge($strErrorMessage, $arrRemoteBlocks, array(), $arrRemoteBlockIDs);

	
		//Strip out only the credit blocks which are not screwed and have not been paid
		foreach(array_keys($arrMergedBlocks) as $k)
		{
			if(($arrMergedBlocks[$k]['strTimeBlockTypeHandle'] != 'CREDIT' && $arrMergedBlocks[$k]['strTimeBlockTypeHandle'] != 'PROMOTIONAL_CREDIT'))
			{
				unset($arrMergedBlocks[$k]);
			}
			if($arrMergedBlocks[$k]['strBlockErrorMessage'] != '' || $arrMergedBlocks[$k]['intSalesInvoiceID'] > 0) 
			{
				unset($arrMergedBlocks[$k]);
			}
			
		}

		return $arrMergedBlocks;

	} 

	/**
	* Get the status of a radius block 
	*
	* @access private
	* @static
	* <AUTHOR>
	* @param  A radius block
	* @return string The status of the block
	*/
	function prvCalculateRemoteStatus($arrRemoteWifiBlock)
	{
		// Calculate Status
		if($arrRemoteWifiBlock['uxtStartTime']   == $arrRemoteWifiBlock['uxtStopTime'] && 
		   $arrRemoteWifiBlock['intTimeUsed']    == 0 && 
		   $arrRemoteWifiBlock['intTimeAllowed'] == 0)
		{
			$strStatus = 'Obsoleted';
			return $strStatus;
		}
		if($arrRemoteWifiBlock['intTimeAllowed'] <= ($arrRemoteWifiBlock['intTimeUsed'] ))
		{
			$strStatus = 'Totally Spent';
			return $strStatus;
		}
		elseif($arrRemoteWifiBlock['uxtStopTime'] != ''  && $arrRemoteWifiBlock['uxtStopTime'] < time())
		{
			$strStatus = 'Expired';
			return $strStatus;
		}
		elseif($arrRemoteWifiBlock['uxtStartTime'] == '')
		{
			$strStatus = 'Awaiting First Use';
			return $strStatus;
		}
		elseif($arrRemoteWifiBlock['uxtStartTime'] > time())
		{
			$strStatus = 'Not Yet Available';
			return $strStatus;
		}

		$strStatus = 'Active';
		return $strStatus;	
	}

	/**
	* Merges local blocks with remote blocks.
	*
	* Merges local blocks with remote blocks, and creates a placeholder block for one which exists on one side but not the other.
	*
	* @param string ref Error message 
	* @param array The remote blocks
	* @param array Local block ids - ignore any local blocks except these (any blocks with errors will be returned too)
	* @param array remote block ids - ignre any remote blocks except these (any blocks with errors will be returned too)
	* @return the big fat merged array of blocks
	*/
	function prvGetLocalBlocksAndMerge(&$strErrorMessage, $arrRemoteWifiBlocks, $arrLocalBlockIDs = array(), $arrRemoteBlockIDs = array())
	{
		
		$strQuery ="SELECT tb.intWifiTimeBlockID,
		                   tb.intSalesInvoiceID,
		                   si.invoice_status as strInvoiceStatus,
		                   IF(si.invoice_status IN ('part_paid', 'fully_paid'),1,0) as bolInvoicePaid,
		                   tb.intWifiScheduledPaymentID,
		                   tb.intWifiTimeTypeID,
		                   tt.vchHandle as strTimeBlockTypeHandle,
		                   tt.vchDisplayName as strTimeBlockTypeDisplayName,
		                   tb.intRemoteTimeBlockID, 
		                   tb.intWifiPromoEventID,
		                   tb.intTariffID,
		                   tb.intNumMinutes,
		                   tb.intMinutesBeforeExpiry,
		                   tb.intPrepayTariffBandID,
		                   tb.dtmDateAdded,
		                   t.vchHandle as strTariffHandle,
		                   t.vchDisplayName as strTariffDisplayName,
		                   case 
		                      when wpo.intNumMinutes is not null     then wpo.intNumMinutes  
		                      when ptb.intMinutesInBlock is not null then ptb.intMinutesInBlock  
		                      when tt.vchHandle in ('SUBSCRIPTION')  then t.intAutomaticMinutesPerCycle  
		                      when tt.vchHandle in ('CREDIT')        then t.intMinutesOfCredit  
		                      else 0 
		                   end as intMinutesInBlock,
		                   case 	
		                      when wpo.decCostExcVatInPence is not null then wpo.decCostExcVatInPence
		                      when ptb.decCostExcVatInPence is not null then ptb.decCostExcVatInPence
		                      when tt.vchHandle in ('SUBSCRIPTION')     then stc.intBaseCostExcVatInPence
		                      when tt.vchHandle in ('CREDIT')           then scc.decAdditionalMinutesExcVatInPence
		                      else 0
		                   end as floCostExcVatInPence,
		                   case  
		                      when wpo.decCostIncVatInPence is not null then wpo.decCostIncVatInPence
		                      when ptb.decCostIncVatInPence is not null then ptb.decCostIncVatInPence
		                      when tt.vchHandle in ('SUBSCRIPTION')     then stc.intBaseCostIncVatInPence
		                      when tt.vchHandle in ('CREDIT')           then scc.decAdditionalMinutesIncVatInPence
		                      else 0
		                   end as floCostIncVatInPence,
		                   case
		                      when tt.vchHandle in ('CREDIT','PROMOTIONAL_CREDIT') then 'PRICE_PER_MINUTE'
		                      else 'PRICE_PER_BLOCK'
		                   end as strPricingPerBlockOrPerMinute,
		                   'GBP' as strCurrencyCode
		              FROM tblWifiTimeBlock tb 
		        INNER JOIN products.vblWifiTimeType tt
		                ON tb.intWifiTimeTypeID = tt.intWifiTimeTypeID
		        INNER JOIN products.tblTariff t
		                ON tb.intTariffID = t.intTariffID
		         LEFT JOIN financial.sales_invoices si
		                ON si.sales_invoice_id = tb.intSalesInvoiceID
		         LEFT JOIN products.tblPrepayTariffBand ptb
		                ON ptb.intPrepayTariffBandID = tb.intPrepayTariffBandID
		         LEFT JOIN products.tblWifiPromoOffer wpo
		                ON wpo.intWifiPromoEventID = tb.intWifiPromoEventID
		         LEFT JOIN products.tblSubscriptionTariffCost stc
		                ON stc.intTariffBaseCostID = tb.intTariffBaseCostID
		               AND tt.vchHandle in ('SUBSCRIPTION')  
		         LEFT JOIN products.tblSubscriptionTariffCost scc
		                ON scc.intTariffBaseCostID = tb.intTariffBaseCostID
		               AND tt.vchHandle in ('CREDIT')  
		             WHERE intWifiConfigID = '{$this->m_intWifiConfigID}'";




		$strFullIDClause = " AND (tb.intWifiTimeBlockID is null OR tb.intRemoteTimeBlockID is null) ";
		
		$strLocalIDsClause = '';
		$strLocalAND ='';
		
		if(count($arrLocalBlockIDs) > 0)
		{
			$strLocalIDsClause = primitives_IN_clause($strLocalAND,'tb.intWifiTimeBlockID',$arrLocalBlockIDs);
			$strLocalAND = 'AND';
		}

		$strRemoteIDsClause = '';
		if(count($arrRemoteBlockIDs) > 0)
		{
			$strRemoteIDsClause = primitives_IN_clause($strLocalAND,'tb.intRemoteTimeBlockID',$arrRemoteBlockIDs);
		}

		if( $strRemoteIDsClause !='' || $strLocalIDsClause !='')
		{
			$strFullIDClause = " AND (($strLocalIDsClause $strRemoteIDsClause ) OR  tb.intWifiTimeBlockID is null OR tb.intRemoteTimeBlockID is null) ";
		}

		$strQuery .= $strFullIDClause;
						   
		$refConnection       = get_named_connection_with_db('userdata');
		$refResult           = PrimitivesQueryOrExit($strQuery,$refConnection,'Get destruction date entries');
		$arrLocalWifiBlocks  = PrimitivesResultsAsArrayGet($refResult);



		$bolBlockErrors = false;
	
		$intFoundLocalWifiBlocks = 0; 

		$arrWifiBlocks = array();


		foreach($arrRemoteWifiBlocks AS $arrRemoteWifiBlock)
		{
			$bolBlockFound = false;
			$strStatus = $this->prvCalculateRemoteStatus($arrRemoteWifiBlock);

			foreach($arrLocalWifiBlocks as $intLocalBlockID => $arrLocalWifiBlock)
			{
				if($arrLocalWifiBlock['intRemoteTimeBlockID'] == $arrRemoteWifiBlock['intTimeID'])
				{
					$bolBlockFound = true;
					$intFoundLocalWifiBlocks++; 

					if($strStatus == 'Expired' || $strStatus == 'Totally Spent')
					{
						if(isset($arrLocalWifiBlock['intSalesInvoiceID']) && $arrLocalWifiBlock['intSalesInvoiceID'] > 0)
						{
							$strStatus = 'Invoiced';
						}
						elseif(isset($arrLocalWifiBlock['intWifiScheduledPaymentID']) && $arrLocalWifiBlock['intWifiScheduledPaymentID'] > 0)
						{
							$strStatus = 'Scheduled For Invoicing';
						}

	
					}

					$arrBlockInfo = array(
							'intWifiTimeBlockID'             => $arrLocalWifiBlock['intWifiTimeBlockID'],
							'intRemoteTimeBlockID'           => $arrLocalWifiBlock['intRemoteTimeBlockID'],
							'intSalesInvoiceID'              => $arrLocalWifiBlock['intSalesInvoiceID'],
							'strInvoiceStatus'               => $arrLocalWifiBlock['strInvoiceStatus'],
							'bolInvoicePaid'                 => $arrLocalWifiBlock['bolInvoicePaid'],
							'intWifiScheduledPaymentID'      => $arrLocalWifiBlock['intWifiScheduledPaymentID'],
							'strBlockErrorMessage'           => '',
							'strBlockTypeHandle'             => $arrLocalWifiBlock['vchHandle'],
							'strBlockTypeDisplayName'        => $arrLocalWifiBlock['vchDisplayName'],
							'strTariffHandle'                => $arrLocalWifiBlock['strTariffHandle'],
							'strTariffDisplayName'           => $arrLocalWifiBlock['strTariffDisplayName'],
							'strTimeBlockTypeHandle'         => $arrLocalWifiBlock['strTimeBlockTypeHandle'],
							'strTimeBlockTypeDisplayName'    => $arrLocalWifiBlock['strTimeBlockTypeDisplayName'],
							'strTimeBlockStatus'             => $strStatus,
							'strCurrencyCode'                => 'GBP',
							'intLocalSizeInMinutes'          => $arrLocalWifiBlock['intNumMinutes'],
							'intSizeInMinutes'               => ceil($arrRemoteWifiBlock['intTimeAllowed']/60),
							'intSecondsUsed'                 => $arrRemoteWifiBlock['intTimeUsed'],
							'intMinutesUsed'                 => ceil($arrRemoteWifiBlock['intTimeUsed']/60),
							'intMinutesAvailable'            => ceil($arrRemoteWifiBlock['intTimeAllowed']/60) - ceil($arrRemoteWifiBlock['intTimeUsed']/60),
							'uxtActiveFrom'                  => $arrRemoteWifiBlock['uxtStartTime'] == '' ? 'N/A' : $arrRemoteWifiBlock['uxtStartTime'], 
							'uxtActiveUntil'                 => $arrRemoteWifiBlock['uxtStopTime']  == '' ? 'N/A' : $arrRemoteWifiBlock['uxtStopTime'], 
							'dtmCreated'                     => $arrLocalWifiBlock['dtmDateAdded'],
							'floCostExcVatInPence'           => $arrLocalWifiBlock['floCostExcVatInPence'],
							'floCostIncVatInPence'           => $arrLocalWifiBlock['floCostIncVatInPence'],
							'intSalesInvoiceID'              => isset($arrLocalWifiBlock['intSalesInvoiceID']) ? $arrLocalWifiBlock['intSalesInvoiceID']     : 0,
							'intFreeTimeReasonID'            => isset($arrLocalWifiBlock['intFreeTimeReasonID']) ? $arrLocalWifiBlock['intFreeTimeReasonID'] : 0,
					        'strFreeTimeReasonHandle'        => isset($arrLocalWifiBlock['intFreeTimeReasonID']) ? $arrLocalWifiBlock['strFreeTimeReasonHandle']: '',
					        'strFreeTimeReasonDisplayName'   => isset($arrLocalWifiBlock['intFreeTimeReasonID']) ? $arrLocalWifiBlock['strFreeTimeReasonDisplayName']: '',
							'strPricingPerBlockOrPerMinute'  => $arrLocalWifiBlock['strPricingPerBlockOrPerMinute'],
							
							);

					$arrWifiBlocks[] = $arrBlockInfo;
					unset($arrLocalWifiBlocks[$intLocalBlockID]);
					break;
				}

			}

			if(!$bolBlockFound)
			{
			
					// Look for the block elsewhere in the local blocks - eg: on a different component
					$arrFoundBlock = $this->prvFindRemoteTimeblockOnLocal($arrRemoteWifiBlock['intTimeID']);
					if ($arrFoundBlock)
					{

						$intFoundLocalWifiBlocks++; 
						if($strStatus == 'Expired' || $strStatus == 'Totally Spent')
						{
							if(isset($arrFoundBlock['intSalesInvoiceID']) && $arrFoundBlock['intSalesInvoiceID'] > 0)
							{
								$strStatus = 'Invoiced';
							}
							elseif(isset($arrFoundBlock['intWifiScheduledPaymentID']) && $arrFoundBlock['intWifiScheduledPaymentID'] > 0)
							{
								$strStatus = 'Scheduled For Invoicing';
							}
		
						}

						$arrBlockInfo = array(
							'intWifiTimeBlockID'             => $arrFoundBlock['intWifiTimeBlockID'],
							'intRemoteTimeBlockID'           => $arrFoundBlock['intRemoteTimeBlockID'],
							'intSalesInvoiceID'              => $arrFoundBlock['intSalesInvoiceID'],
							'strInvoiceStatus'               => $arrFoundBlock['strInvoiceStatus'],
							'bolInvoicePaid'                 => $arrFoundBlock['bolInvoicePaid'],
							'intWifiScheduledPaymentID'      => $arrFoundBlock['intWifiScheduledPaymentID'],
							'strBlockErrorMessage'           => 'Belongs to component ID '.$arrFoundBlock['intComponentID'],
							'strBlockTypeHandle'             => $arrFoundBlock['vchHandle'],
							'strBlockTypeDisplayName'        => $arrFoundBlock['vchDisplayName'],
							'strTariffHandle'                => $arrFoundBlock['strTariffHandle'],
							'strTariffDisplayName'           => $arrFoundBlock['strTariffDisplayName'],
							'strTimeBlockTypeHandle'         => $arrFoundBlock['strTimeBlockTypeHandle'],
							'strTimeBlockTypeDisplayName'    => $arrFoundBlock['strTimeBlockTypeDisplayName'],
							'strTimeBlockStatus'             => $strStatus,
							'strCurrencyCode'                => 'GBP',
							'intLocalSizeInMinutes'          => $arrFoundBlock['intNumMinutes'],
							'intSizeInMinutes'               => ceil($arrRemoteWifiBlock['intTimeAllowed']/60),
							'intSecondsUsed'                 => $arrRemoteWifiBlock['intTimeUsed'],
							'intMinutesUsed'                 => ceil($arrRemoteWifiBlock['intTimeUsed']/60),
							'intMinutesAvailable'            => ceil($arrRemoteWifiBlock['intTimeAllowed']/60) - ceil($arrRemoteWifiBlock['intTimeUsed']/60),
							'uxtActiveFrom'                  => $arrRemoteWifiBlock['uxtStartTime'] == '' ? 'N/A' : $arrRemoteWifiBlock['uxtStartTime'], 
							'uxtActiveUntil'                 => $arrRemoteWifiBlock['uxtStopTime']  == '' ? 'N/A' : $arrRemoteWifiBlock['uxtStopTime'], 
							'dtmCreated'                     => $arrFoundBlock['dtmDateAdded'],
							'floCostExcVatInPence'           => $arrFoundBlock['floCostExcVatInPence'],
							'floCostIncVatInPence'           => $arrFoundBlock['floCostIncVatInPence'],
							'intSalesInvoiceID'              => isset($arrFoundBlock['intSalesInvoiceID']) ? $arrFoundBlock['intSalesInvoiceID']     : 0,
							'intFreeTimeReasonID'            => isset($arrFoundBlock['intFreeTimeReasonID']) ? $arrFoundBlock['intFreeTimeReasonID'] : 0,
					        'strFreeTimeReasonHandle'        => isset($arrFoundBlock['intFreeTimeReasonID']) ? $arrFoundBlock['strFreeTimeReasonHandle']: '',
					        'strFreeTimeReasonDisplayName'   => isset($arrFoundBlock['intFreeTimeReasonID']) ? $arrFoundBlock['strFreeTimeReasonDisplayName']: '',
							'strPricingPerBlockOrPerMinute'  => $arrFoundBlock['strPricingPerBlockOrPerMinute'],
							);
					}
					else
					{
						$strErrorMessage .= " Remote time block '{$arrRemoteWifiBlock['intTimeID']}' is not recorded in workplace \n";
						$arrBlockInfo = array(
						'strBlockErrorMessage'           => "Remote time block '{$arrRemoteWifiBlock['intTimeID']}' is not recorded in workplace and could find no record of it ever have being ",
						'intWifiTimeBlockID'             => 0,
						'intSalesInvoiceID'              => 0,
						'strInvoiceStatus'               => 'Unknown',
						'bolInvoicePaid'                 => 0,
						'intWifiScheduledPaymentID'      => 0,
						'intLocalSizeInMinutes'          => 0,
						'intSizeInMinutes'               => ceil($arrRemoteWifiBlock['intTimeAllowed']/60),
						'intMinutesUsed'                 => ceil($arrRemoteWifiBlock['intTimeUsed']/60),
						'intMinutesAvailable'            => ceil($arrRemoteWifiBlock['intTimeAllowed']/60) - ceil($arrRemoteWifiBlock['intTimeUsed']/60),
						'uxtActiveFrom'                  => $arrRemoteWifiBlock['uxtStartTime'] == '' ? 'N/A' : $arrRemoteWifiBlock['uxtStartTime'], 
						'uxtActiveUntil'                 => $arrRemoteWifiBlock['uxtStopTime']  == '' ? 'N/A' : $arrRemoteWifiBlock['uxtStopTime'], 
						'strTimeBlockStatus'             => $strStatus,
						'floCostExcVatInPence'           => 0.00,
						'dectCostIncVatInPence'          => 0.00,
					    'strCurrencyCode'                => 'GBP',
						'strBlockTypeDisplayName'        => 'Unknown',
						'strTariffHandle'                => 'UNKNOWN',
						'strTariffDisplayName'           => 'Unknown',
						'strTimeBlockTypeHandle'         => 'UNKNOWN',
						'strTimeBlockTypeDisplayName'    => 'Unknown',
						'intSalesInvoiceID'              => 0,
						'intFreeTimeReasonID'            => 0,
						'strFreeTimeReasonHandle'        => '',
						'strFreeTimeReasonDisplayName'   => '',				
						'strPricingPerBlockOrPerMinute'  => 'minute');
					}
					$arrWifiBlocks[] = $arrBlockInfo;
					$bolBlockErrors = true;
			}
			
		}


		if($intFoundLocalWifiBlocks != count($arrRemoteWifiBlocks))
		{
			$strErrorMessage .= "There is a mis-match between the workplace and PMS time blocks. Workplace records $intFoundLocalWifiBlocks timeblocks whilst PMS records ".count($arrRemoteWifiBlocks).".\n";
			$bolBlockErrors = true;
		}

		if(count($arrLocalWifiBlocks) > 0)
		{
			$bolBlockErrors = true;

			foreach($arrLocalWifiBlocks as $arrLocalWifiBlock)
			{
				$arrBlockInfo = array(
					'strBlockErrorMessage'           => "Local time block '{$arrLocalWifiBlock['intWifiTimeBlockID']}' is not recorded in RADIUS ",
					'intWifiTimeBlockID'             => $arrLocalWifiBlock['intWifiTimeBlockID'],
					'intRemoteTimeBlockID'           => 0,
					'intSalesInvoiceID'              => $arrLocalWifiBlock['intSalesInvoiceID'],
					'intWifiScheduledPaymentID'      => $arrLocalWifiBlock['intWifiScheduledPaymentID'],
					'intLocalSizeInMinutes'          => $arrLocalWifiBlock['intNumMinutes'],
					'intSizeInMinutes'               => 0,
					'intMinutesUsed'                 => 0,
					'intMinutesAvailable'            => 0,
					'uxtActiveFrom'                  => 'N/A',
					'uxtActiveUntil'                 => 'N/A',
					'strTimeBlockStatus'             => 'Missing',
					'floCostExcVatInPence'           => 0.00,
					'floCostIncVatInPence'           => 0.00,
					'strCurrencyCode'                => 'GBP',
					'strBlockTypeDisplayName'        => $arrLocalWifiBlock['vchDisplayName'],
					'strTariffHandle'                => $arrLocalWifiBlock['strTariffHandle'],
					'strTariffDisplayName'           => $arrLocalWifiBlock['strTariffDisplayName'],
					'strTimeBlockTypeHandle'         => $arrLocalWifiBlock['strTimeBlockTypeHandle'],
					'strTimeBlockTypeDisplayName'    => $arrLocalWifiBlock['strTimeBlockTypeDisplayName'],
					'intSalesInvoiceID'              => 0,
					'intFreeTimeReasonID'            => 0,
					'strFreeTimeReasonHandle'        => '',
					'strFreeTimeReasonDisplayName'   => '',
					'strPricingPerBlockOrPerMinute'  => 'minute');
			
					$arrWifiBlocks[] = $arrBlockInfo;
					$bolBlockErrors = true;
					
				$strErrorMessage .= "The Workplace time block '{$arrLocalWifiBlock['intWifiTimeBlockID']}' is not recorded in PMS\n";
			}
		}

		usort($arrWifiBlocks,array('CWifiComponent', 'USortCompareBlocksLocalID'));
	
		return($arrWifiBlocks);
	} 

	/**
	 * Find the local timeblock for a given remote timeblock
	 *
	 * @access private
	 * <AUTHOR>
	 * @param  intRemoteID int The ID of the remote timeblock (time_id on radius2.time_quota)
	 * @return array - The workplace timeblock record
	 */
	function prvFindRemoteTimeblockOnLocal($intRemoteID)
	{
		$dbhUserdata = get_named_connection_with_db('userdata');
		$strQuery ="SELECT tb.intWifiTimeBlockID,
		                   tb.intSalesInvoiceID,
		                   si.invoice_status as strInvoiceStatus,
		                   IF(si.invoice_status IN ('part_paid', 'fully_paid'),1,0) as bolInvoicePaid,
		                   tb.intWifiScheduledPaymentID,
		                   tb.intWifiTimeTypeID, 
						   cw.intComponentID,
		                   tt.vchHandle as strTimeBlockTypeHandle,
		                   tt.vchDisplayName as strTimeBlockTypeDisplayName,
		                   tb.intRemoteTimeBlockID, 
		                   tb.intWifiPromoEventID,
		                   tb.intTariffID,
		                   tb.intNumMinutes,
		                   tb.intMinutesBeforeExpiry,
		                   tb.intPrepayTariffBandID,
		                   tb.dtmDateAdded,
		                   t.vchHandle as strTariffHandle,
		                   t.vchDisplayName as strTariffDisplayName, 
		                   case 
		                      when wpo.intNumMinutes is not null     then wpo.intNumMinutes  
		                      when ptb.intMinutesInBlock is not null then ptb.intMinutesInBlock  
		                      when tt.vchHandle in ('SUBSCRIPTION')  then t.intAutomaticMinutesPerCycle  
		                      when tt.vchHandle in ('CREDIT')        then t.intMinutesOfCredit  
		                      else 0 
		                   end as intMinutesInBlock,
		                   case 	
		                      when wpo.decCostExcVatInPence is not null then wpo.decCostExcVatInPence
		                      when ptb.decCostExcVatInPence is not null then ptb.decCostExcVatInPence
		                      when tt.vchHandle in ('SUBSCRIPTION')     then stc.intBaseCostExcVatInPence
		                      when tt.vchHandle in ('CREDIT')           then scc.decAdditionalMinutesExcVatInPence
		                      else 0
		                   end as floCostExcVatInPence,
		                   case  
		                      when wpo.decCostIncVatInPence is not null then wpo.decCostIncVatInPence
		                      when ptb.decCostIncVatInPence is not null then ptb.decCostIncVatInPence
		                      when tt.vchHandle in ('SUBSCRIPTION')     then stc.intBaseCostIncVatInPence
		                      when tt.vchHandle in ('CREDIT')           then scc.decAdditionalMinutesIncVatInPence
		                      else 0
		                   end as floCostIncVatInPence,
		                   case
		                      when tt.vchHandle in ('CREDIT','PROMOTIONAL_CREDIT') then 'PRICE_PER_MINUTE'
		                      else 'PRICE_PER_BLOCK'
		                   end as strPricingPerBlockOrPerMinute,
		                   'GBP' as strCurrencyCode
		              FROM tblWifiTimeBlock tb 
		        INNER JOIN products.vblWifiTimeType tt
		                ON tb.intWifiTimeTypeID = tt.intWifiTimeTypeID
		        INNER JOIN products.tblTariff t
		                ON tb.intTariffID = t.intTariffID
		         LEFT JOIN financial.sales_invoices si
		                ON si.sales_invoice_id = tb.intSalesInvoiceID
			 LEFT JOIN products.tblPrepayTariffBand ptb
		                ON ptb.intPrepayTariffBandID = tb.intPrepayTariffBandID
			 LEFT JOIN userdata.tblConfigWifi cw
				        ON cw.intWifiConfigID = tb.intWifiConfigID
			 LEFT JOIN products.tblWifiPromoOffer wpo
		                ON wpo.intWifiPromoEventID = tb.intWifiPromoEventID
		         LEFT JOIN products.tblSubscriptionTariffCost stc
		                ON stc.intTariffBaseCostID = tb.intTariffBaseCostID
		               AND tt.vchHandle in ('SUBSCRIPTION')  
		         LEFT JOIN products.tblSubscriptionTariffCost scc
		                ON scc.intTariffBaseCostID = tb.intTariffBaseCostID
		               AND tt.vchHandle in ('CREDIT')  
		             WHERE intRemoteTimeBlockID = '$intRemoteID'";

		$refResult = PrimitivesQueryOrExit($strQuery, $dbhUserdata);

		$arrResult = PrimitivesResultsAsArrayGet($refResult);

		if (count($arrResult) == 1)
		{
			return $arrResult[0];
		}
		return false;
	}
	
	function USortCompareBlocksLocalID($arrFirst,$arrSecond)
	{
		if($arrFirst['intWifiTimeBlockID'] == $arrSecond['intWifiTimeBlockID'])
		{
			return 0;
		}
		
		if($arrFirst['intWifiTimeBlockID'] < $arrSecond['intWifiTimeBlockID'])
		{
			return -1;
		}
		
		return 1;
				
	} // func:  USortCompareBlocksLocalID

	/**
	*
	*
	*
	*
	*
	*
	*/
	function GetTimeBlocksForDisplay(&$strErrorMessage, $arrLocalBlockIDs = array())
	{

		$strErrorMessage = '';

		//Get Radius user id
		$arrUserDetails = $this->GetUserDetails();
		$objRadiusUser = CRadiusUser::Create();
		$objRadiusUser->Retrieve($arrUserDetails['strUserName'], $arrUserDetails['strISP'], $arrUserDetails['intServiceID']);
	
		if(!$intRadiusID = $objRadiusUser->GetID())
		{
			$strErrorMessage = "Cannot find User radius id";
			return false;
		}

		$strQuery = "SELECT time_id as intTimeID,
		                    radius_id as intRadiusID,
		                    group_id as intGroupID,
		                    priority as intPriority,
		                    valid_period as intValidPeriod,
		                    time_allowed as intTimeAllowed,
		                    UNIX_TIMESTAMP(start_time) as uxtStartTime,
		                    UNIX_TIMESTAMP(stop_time) as uxtStopTime,
		                    time_used as intTimeUsed,
		                    IF(active = 'Y', 'true', 'false') as bolActive,
		                    UNIX_TIMESTAMP(date_last_updated) as uxtDateLastUpdated,
		                    sync_version as intSyncVersion,
		                    version as intSyncVersion
		               FROM time_quota
		              WHERE radius_id = '$intRadiusID'
		                AND group_id = '{$this->m_intDialupGroupID}'
				 ";
						 
		$refConnection   = get_named_connection_with_db('radius_reporting');

		$refResult       = PrimitivesQueryOrExit($strQuery, $refConnection,'Get Unexpired Credit blocks from radius');
		$arrRemoteBlocks = PrimitivesResultsAsArrayGet($refResult);
	
		if(count($arrRemoteBlocks) == 0)
		{
			return array();
		}

		$arrRemoteBlockIDs = array();

		foreach(array_keys($arrRemoteBlocks) as $i)
		{
			$arrRemoteBlockIDs[] = $arrRemoteBlocks[$i]['intTimeID'];
		}
		
		$arrMergedBlocks = $this->prvGetLocalBlocksAndMerge($strErrorMessage, $arrRemoteBlocks, array(), $arrRemoteBlockIDs);
		
		return $arrMergedBlocks;

	} 


	/**
	* Get the monthly Wifi subscription fee for a give service
	*
	* @param int $intServiceID The service ID to check
	* @access public
	* @static
	* @return mixed FALSE if not a subscription Wifi account, the amount in pence if a fee is required
	*/
	function GetWifiMonthlyFee($intServiceID)
	{
		$intComponentID = CWifiComponent::GetWifiComponentByService($intServiceID);

		if($intComponentID > 0)
		{
	    	$objTempWifi = new CWifiComponent($intComponentID);

			$intMyTariff = $objTempWifi->GetCurrentTariffID();
			$arrCurrentTariff = $objTempWifi->GetAvailableTariffs(array($intMyTariff));
			$intMonthlyFee = $arrCurrentTariff['intBaseCostIncVatInPence'];

			if ($intMonthlyFee > 0)
			{
				return $intMontlyFee;
			}
		}

		return FALSE;
	}



	/*
	* 
	* Logging Functions
	*
	*/

	/**
	* Adds a service event logging the creation of the wifi component
	*
	* Depends of the existence of the WIFI_COMPONENT_CREATED define
	* @return int Service event ID
	*/
	function LogCreation()
	{
		global $whoami;



		$connection = get_named_connection('userdata');

		$service_definition_id = mysql_result(mysql_query("SELECT type FROM services WHERE service_id='$this->m_intServiceID'", $connection), 0, 0);


		return userdata_service_event_add($this->m_intServiceID, WIFI_COMPONENT_CREATED, $service_definition_id, $service_definition_id,
		                                  $this->m_uxtNextInvoiceDate, $this->m_intActivationInvoiceID, '', 
		                                  date('Y-m-d H:i:s'), 0, $this->m_intComponentID);
	}
	
	/**
	* Adds a service event logging the activation of the wifi component
	*
	* Depends of the existence of the WIFI_COMPONENT_ACTIVED define
	* @return int Service event ID
	*/
	function LogActivation($bolAddSubscriptionEntry)
	{
		if(!$bolAddSubscriptionEntry)
		{
			return false;
		}

		$connection = get_named_connection('userdata');

		$service_definition_id = mysql_result(mysql_query("SELECT type FROM services WHERE service_id='$this->m_intServiceID'", $connection), 0, 0);

		return userdata_service_event_add($this->m_intServiceID, WIFI_COMPONENT_ACTIVATED, $service_definition_id, $service_definition_id,
		                                  $this->m_uxtNextInvoiceDate, $this->m_intActivationInvoiceID, '', 
		                                  date('Y-m-d H:i:s'), 0, $this->m_intComponentID);

	}


	/**
	* Adds a service event logging the deactivation of the wifi component
	*
	* Depends of the existence of the WIFI_COMPONENT_DISABLED define
	* @return int Service event ID
	*/
	function LogDeactivation()
	{
		$connection = get_named_connection('userdata');

		$service_definition_id = mysql_result(mysql_query("SELECT type FROM services WHERE service_id='$this->m_intServiceID'", $connection), 0, 0);

		return userdata_service_event_add($this->m_intServiceID, WIFI_COMPONENT_DISABLED, $service_definition_id, $service_definition_id,
		                                  $this->m_uxtNextInvoiceDate, '', '', 
		                                  date('Y-m-d H:i:s'), 0, $this->m_intComponentID);
	}


	/**
	* Logs a service event upon scheduled destruction of the component
	*
	* @return int The service event ID
	*/
	function LogScheduledDestruction($uxtScheduledDate='')
	{
		$connection = get_named_connection('userdata');

		$service_definition_id = mysql_result(mysql_query("SELECT type FROM services WHERE service_id='$this->m_intServiceID'", $connection), 0, 0);

		return userdata_service_event_add($this->m_intServiceID, WIFI_SCHEDULED_COMPONENT_CANCELLATION, $service_definition_id, $service_definition_id,
		                                  $this->m_uxtNextInvoiceDate, '', ($uxtScheduledDate == '') ? date('Y-m-d H:i:s', $uxtScheduledDate) : '', 
		                                  date('Y-m-d H:i:s'), 0, $this->m_intComponentID);
	}


	/**
	* Logs a service event upon immediate destruction of the component
	*
	* @return int The service event ID
	*/
	function LogImmediateDestruction()
	{
		$connection = get_named_connection('userdata');

		$service_definition_id = mysql_result(mysql_query("SELECT type FROM services WHERE service_id='$this->m_intServiceID'", $connection), 0, 0);

		return userdata_service_event_add($this->m_intServiceID, WIFI_IMMEDIATE_COMPONENT_CANCELLATION, $service_definition_id, $service_definition_id,
		                                  $this->m_uxtNextInvoiceDate, '', '', 
		                                  date('Y-m-d H:i:s'), 0, $this->m_intComponentID);
	
	}

	/**
	* Logs a service event upon wifi tariff change 
	*
	* @return int The service event ID
	*/
	function LogWifiTariffChange()
	{
		$connection = get_named_connection('userdata');

		$service_definition_id = mysql_result(mysql_query("SELECT type FROM services WHERE service_id='$this->m_intServiceID'", $connection), 0, 0);

		return userdata_service_event_add($this->m_intServiceID, WIFI_TARIFF_CHANGE, $service_definition_id, $service_definition_id,
		                                  $this->m_uxtNextInvoiceDate, '', '', 
		                                  date('Y-m-d H:i:s'), 0, $this->m_intComponentID);
	} // func: LogWifiTariffChange


	/**
	* Logs a service event for the reporting definition of a WiFi Signup
	*
	* @return int The service event ID
	*/
	function LogReportingSignup()
	{
		$connection = get_named_connection('userdata');

		$service_definition_id = mysql_result(mysql_query("SELECT type FROM services WHERE service_id='$this->m_intServiceID'", $connection), 0, 0);

		return userdata_service_event_add($this->m_intServiceID, WIFI_COMPONENT_REPORTING_SIGNUP, $service_definition_id, $service_definition_id,
		                                  $this->m_uxtNextInvoiceDate, '', '', 
		                                  date('Y-m-d H:i:s'), 0, $this->m_intComponentID);
	
	}


	/**
	* Logs a service event for the reporting definition of a WiFi Upgrade
	*
	* @return int The service event ID
	*/
	function LogReportingUpgrade()
	{
		$connection = get_named_connection('userdata');

		$service_definition_id = mysql_result(mysql_query("SELECT type FROM services WHERE service_id='$this->m_intServiceID'", $connection), 0, 0);

		return userdata_service_event_add($this->m_intServiceID, WIFI_COMPONENT_REPORTING_UPGRADE, $service_definition_id, $service_definition_id,
		                                  $this->m_uxtNextInvoiceDate, '', '', 
		                                  date('Y-m-d H:i:s'), 0, $this->m_intComponentID);
	
	}


	/**
	* Logs a service event for the reporting definition of a WiFi Downgrade
	*
	* @return int The service event ID
	*/
	function LogReportingDowngrade()
	{
		$connection = get_named_connection('userdata');

		$service_definition_id = mysql_result(mysql_query("SELECT type FROM services WHERE service_id='$this->m_intServiceID'", $connection), 0, 0);

		return userdata_service_event_add($this->m_intServiceID, WIFI_COMPONENT_REPORTING_DOWNGRADE, $service_definition_id, $service_definition_id,
		                                  $this->m_uxtNextInvoiceDate, '', '', 
		                                  date('Y-m-d H:i:s'), 0, $this->m_intComponentID);
	
	}


	/**
	* Logs a service event for the reporting definition of a WiFi Cancellation
	*
	* @return int The service event ID
	*/
	function LogReportingCancellation()
	{
		$connection = get_named_connection('userdata');

		$service_definition_id = mysql_result(mysql_query("SELECT type FROM services WHERE service_id='$this->m_intServiceID'", $connection), 0, 0);

		return userdata_service_event_add($this->m_intServiceID, WIFI_COMPONENT_REPORTING_CANCELLATION, $service_definition_id, $service_definition_id,
		                                  $this->m_uxtNextInvoiceDate, '', '', 
		                                  date('Y-m-d H:i:s'), 0, $this->m_intComponentID);
	
	}


	// Get the user details for all users with wifi accounts in the past 

	function GetWifiSchedulableServices($uxtDateOfRun=0,$arrServiceIDs = array())
	{
		if($uxtDateOfRun < 1)
		{
			$uxtDateOfRun = time();
		}
	
		$strDate = date('Y-m-d', $uxtDateOfRun);

		/* This query finds:
			A) Users with active wifi subscription components which will be billed in the next billing month
		*/

		$refConnection                       = get_named_connection_with_db('userdata');
		
		$strQuery = "SELECT distinct s.service_id,username, user_id ,'monthly' as invoice_period
			           FROM userdata.services s 
			           INNER JOIN userdata.components c on c.service_id = s.service_id
			           INNER JOIN tblConfigWifi cw on cw.intComponentID = c.component_id
			           INNER JOIN products.tblComponentWIFIConfig cwc on cwc.intServiceComponentID = c.component_type_id
			           INNER JOIN products.tblTariff wt on wt.intTariffID = cw.intCurrentTariffID AND wt.intAutomaticMinutesPerCycle > 0
			           WHERE 
			                 cw.dtmNextInvoiceDate is not null 
			                 AND cw.dtmNextInvoiceDate != '0000-00-00' 
			                 AND cw.dtmNextInvoiceDate != '9999-09-09' 
			                 AND cw.dtmNextInvoiceDate != '2030-01-01' 
			                 AND cw.dtmNextInvoiceDate <= 
			                      DATE_ADD(concat(date_format(from_unixtime($uxtDateOfRun),'%Y-%m-%d'),' 23:59:59'), INTERVAL 1 MONTH)
			                 AND s.next_invoice <= 
			                      DATE_ADD(concat(date_format(from_unixtime($uxtDateOfRun),'%Y-%m-%d'),' 23:59:59'), INTERVAL 0 MONTH)
			                 AND c.status = 'active' 
			                 AND s.status = 'active' 
			";

		$resResult                           = PrimitivesQueryOrExit($strQuery,$refConnection,'Get next billing day');
		$arrComponentsToBeBilledWithinAMonth = PrimitivesResultsAsArrayGet($resResult,'service_id');


		/* This query finds:
			B) Users with Wifi uncharged credit blocks which expired before the billing day and are on active wifi components
			C) Users with Wifi uncharged credit blocks which expired before the billing day and are on destroyed wifi components
		*/
		$arrPaymentsToSchedule = array();

		$strQuery = "SELECT distinct s.service_id,username, user_id ,'monthly' as invoice_period
			FROM userdata.services s 
			INNER JOIN userdata.components c on c.service_id = s.service_id
			INNER JOIN tblConfigWifi cw on cw.intComponentID = c.component_id
			INNER JOIN products.tblComponentWIFIConfig cwc on cwc.intServiceComponentID = c.component_type_id
			LEFT JOIN userdata.tblWifiTimeBlock wtb on wtb.intWifiConfigID = cw.intWifiConfigID
			AND (wtb.intWifiScheduledPaymentID is null OR wtb.intWifiScheduledPaymentID = 0)
			AND (wtb.intSalesInvoiceID is null OR wtb.intSalesInvoiceID = 0)
			AND wtb.dtmEndDate <= concat(date_format(from_unixtime($uxtDateOfRun),'%Y-%m-%d'),' 23:59:59')

			LEFT JOIN products.vblWifiTimeType wtt on wtb.intWifiTimeTypeID = wtt.intWifiTimeTypeID
			AND wtt.vchHandle in ('PROMOTIONAL_CREDIT','CREDIT')

			WHERE (DATE_ADD(CONCAT(DATE_FORMAT('$strDate', '%Y-%m-'), s.invoice_day), INTERVAL 0 Month) <= '$strDate' 
			      AND (c.status = 'active' OR wtt.vchHandle is not null))
			      OR (c.status ='destroyed' and wtt.vchHandle is not null)
			";

		$resResult             = PrimitivesQueryOrExit($strQuery,$refConnection,'Get next billing day');
		$arrPaymentsToSchedule = PrimitivesResultsAsArrayGet($resResult,'service_id');

		foreach($arrComponentsToBeBilledWithinAMonth as $intServiceID => $arrComponentToBeBilledWithinAMonth)
		{
			if(!isset($arrPaymentsToSchedule[$intServiceID]))
			{
				$arrPaymentsToSchedule[$intServiceID] = $arrComponentToBeBilledWithinAMonth;
			}
		}

		// knockout anyone in failed billing
		foreach($arrPaymentsToSchedule as $intIndex => $arrPaymentToSchedule)
		{
			if(bolFinancialFailedBilling($arrPaymentToSchedule['service_id']))
			{
				unset($arrPaymentsToSchedule[$intIndex]);
			}
		}

		return $arrPaymentsToSchedule;

	} // func: GetWifiSchedulableServices


	// Get the user details for all users with unsubmitted scheduled payments due on or before this day

	function GetInterimBillableServices($uxtDateOfRun=0,$arrServiceIDs = array())
	{
		$strServiceIDClause  = '';

		if($uxtDateOfRun < 1)
		{
			$uxtDateOfRun = time();
		}


		if(count($arrServiceIDs) > 0)
		{
			$strServiceIDClause = primitives_IN_clause('AND','s.service_id',$arrServiceIDs);
		}

		$strQuery = "SELECT DISTINCT s.service_id as intServiceID, s.username as strUsername, a.account_id as intAccountID,
		                    s.service_id as intOriginalServiceID, s.username as strOriginalUsername,
		                    c.component_id as intWifiComponentID, 
				    if(wsd.service_definition_id is not null AND wsd.service_definition_id = s.type,1,0) as bolWifiOnlyAccount,
		                    if(wsd.service_definition_id is not null,
		                       wsd.service_definition_id,
		                       sd.service_definition_id) as intAccountType, 
		                    if(wsd.type is not null,wsd.type,sd.type) as strAccountClass, 
		                    if(wsd.name is not null,wsd.name,sd.name) as strWifiAccountName
		FROM financial.tblWifiScheduledPayment wsp 
		     INNER JOIN userdata.components c on c.component_id = wsp.intComponentID 
		     INNER JOIN userdata.services s on s.service_id = c.service_id 
                     INNER JOIN userdata.users u on u.user_id = s.user_id
                     INNER JOIN userdata.accounts a on a.customer_id = u.customer_id
		     INNER JOIN products.tblComponentWIFIConfig cwc on cwc.intServiceComponentID = c.component_type_id 
		     INNER JOIN products.service_definitions sd on s.type = sd.service_definition_id 
		     LEFT JOIN products.service_definitions wsd on wsd.service_definition_id = cwc.intBaseProductServiceDefinitionID 
		     WHERE wsp.dteDateDue <= from_unixtime($uxtDateOfRun) 
		           AND (wsp.intSalesInvoiceID is null OR wsp.intSalesInvoiceID < 0)
		           AND (wsp.intBillingReportByCustomerID is null OR wsp.intBillingReportByCustomerID < 0)
		           $strServiceIDClause
		     ";

		$refConnection                  = get_named_connection_with_db('userdata');
		$resResult                      = PrimitivesQueryOrExit($strQuery,$refConnection,'Get next billing day');
		$arrScheduledPaymentsToDespatch = PrimitivesResultsAsArrayGet($resResult);

		// knockout anyone in failed billing
		foreach($arrScheduledPaymentsToDespatch as $intIndex => $arrScheduledPaymentToDespatch)
		{
			if(bolFinancialFailedBilling($arrScheduledPaymentToDespatch['intServiceID']))
			{
				unset($arrScheduledPaymentsToDespatch[$intIndex]);
			}
		}

		return $arrScheduledPaymentsToDespatch;

	} // func: GetInterimBillableServices


}
?>
