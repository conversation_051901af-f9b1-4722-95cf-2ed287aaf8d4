<?php
require_once USERDATA_ACCESS_LIBRARY;
require_once PRODUCT_ACCESS_LIBRARY;
require_once SECURE_TRANSACTION_ACCESS_LIBRARY;
require_once FINANCIAL_ACCESS_LIBRARY;
require_once TICKETS_ACCESS_LIBRARY;
require_once CONFIG_DIALUP_ACCESS_LIBRARY;

require_once('/local/data/mis/database/database_libraries/components/CProductComponent.inc');

class CBullGuardComponent extends CProductComponent
{
    const STATE_UNDEFINED = 0;
    const STATE_PURCHASED_NOT_ACTIVATED = 1;
    const STATE_NEVER_INSTALLED = 2;
    const STATE_ACTIVE_TRIAL = 3;        // Active trial
    const STATE_INACTIVE_TRIAL = 4;        // Trial after subscription expiry date
    const STATE_ACTIVE = 5;            // Active product component
    const STATE_INACTIVE = 6;            // Product component after subscription expiry date (possible only when failed billing?)

    protected $intBullGuardConfigID = 0;
    protected $strInstallation =  '';
    protected $strActivation = '';
    protected $strExpiry = '';

    /**
     * Constructor
     *
     * @param unknown_type $intProductComponentInstanceID
     * @return unknown
     */
    public function __construct($intProductComponentInstanceID)
    {
        $this->CProductComponent($intProductComponentInstanceID);

        if (0 == $intProductComponentInstanceID || false == is_numeric($intProductComponentInstanceID)) {

            return false;
        }

        // Get configuration from database
        $strQuery =
        "     SELECT  cbg.intBullGuardConfigID, " .
        "             cbg.intProductComponentInstanceID, " .
        "             cbg.dtmCreate AS strActivation, " .
        "             IFNULL(cbg.dtmInstallation, '') AS strInstallation, " .
        "             IFNULL(cbg.dtmExpiry, '1970-01-01 00:00:00') AS strExpiry, " .
        "             cbg.dtmDestroy" .
                "       FROM userdata.tblProductComponentInstance pci " .
                " INNER JOIN userdata.tblConfigBullGuard cbg ON (cbg.intProductComponentInstanceID = pci.intProductComponentInstanceID) " .
                "      WHERE pci.intProductComponentInstanceID = {$intProductComponentInstanceID}";

        $dbhConnection = get_named_connection_with_db('userdata');
        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);
        $arrResult = PrimitivesResultGet($resResult);

        if(0 == count($arrResult)) {

            return false;
        }

        $this->intBullGuardConfigID = $arrResult['intBullGuardConfigID'];

        $this->strActivation = $arrResult['strActivation'];
        $this->strInstallation = $arrResult['strInstallation'];
        $this->strExpiry = $arrResult['strExpiry'];

        return true;
    }

    /**
     * Get status of component
     *
     * @return int
     */
    public function getState()
    {
        // Never activated and logged into subscription server
        if (false == $this->getActivationDate() && false == $this->getInstalationDate()) {

            return self::STATE_NEVER_INSTALLED;
        }

        $utxExpiryDate = $this->getExpiryDate(true);

        $utxCurrentDate = mktime(
            0,
            0,
            0,
            date('m'),
            date('d'),
            date('Y')
            );

        if ($this->getServiceComponentID() == COMPONENT_BULL_GUARD_TRIAL) {

            if ($utxExpiryDate >= $utxCurrentDate &&  'ACTIVE' == $this->getStatus()) {

                return self::STATE_ACTIVE_TRIAL;
            }
            else {

                return self::STATE_INACTIVE_TRIAL;
            }
        }
        elseif ($this->getServiceComponentID() == COMPONENT_BULL_GUARD) {

            // Purchased product component, it's active but never loged to subscription server
            if (true == in_array($this->getStatus(), $this->getActiveStatuses())  && false != $this->getActivationDate() && false == $this->getInstalationDate()) {

                return self::STATE_PURCHASED_NOT_ACTIVATED;
            }

            if (true == in_array($this->getStatus(), $this->getActiveStatuses()) && $utxExpiryDate >= $utxCurrentDate) {

                return self::STATE_ACTIVE;
            }
            else {

                return self::STATE_INACTIVE;
            }
        }

        return self::STATE_UNDEFINED;
    }

    /**
     * Return an array of active product statuses
     *
     * @return array
     */
    public function getActiveStatuses()
    {
        // Allow to queued destroyed component. It's used for monthly subscription, that will expiry at the end of the month
        $arrStatuses = array(
            'ACTIVE',
            'QUEUED_ACTIVATE',
            'QUEUED_DESTROY'
            );

        return $arrStatuses;
    }

    /**
     * Config database handling functions
     *
     */

    /**
     * Return an bullguard instalation date unix timestamp
     * (first login into subscription server)
     *
     * @return int
     */
    public function getInstalationDate()
    {
        return empty($this->strInstallation) ? false : strtotime($this->strInstallation);
    }

    public function getActivationDate()
    {
        return empty($this->strActivation) ? false : strtotime($this->strActivation);
    }

    /**
     * Return subscription expiry date
     *
     * @param bool $bolStrict
     * @return timestamp
     */
    public function getExpiryDate($bolStripTime = false)
    {
        $utxExpiry = empty($this->strExpiry) ? false : strtotime($this->strExpiry);

        if (true == $bolStripTime)
        {
            $utxExpiry = mktime(
                0,
                0,
                0,
                date('m', $utxExpiry),
                date('d', $utxExpiry),
                date('Y', $utxExpiry)
                );
        }

        return $utxExpiry;
    }

    /**
     * Set instalation date (first login at subscription server)
     *
     * @param integer $utxDate
     * @return unknown
     */
    public function setInstalationDate($utxDate = 0)
    {
        $this->strInstallation =  empty($utxDate) ? date('Y-m-d H:i:s') : date('Y-m-d H:i:s', $utxDate);
        return $this->updateConfigDate('dtmInstallation', $utxDate);
    }

    /**
     * Set subscription expiry date
     *
     * @param int $utxDate
     * @return bool
     */
    public function setExpiryDate($utxDate = 0)
    {
        $this->strExpiry = empty($utxDate) ? date('Y-m-d H:i:s') : date('Y-m-d H:i:s', $utxDate);
        return $this->updateConfigDate('dtmExpiry', $utxDate);
    }

    /**
     * Set date of component destroy
     *
     * @param int $utxDate
     * @return bool
     */
    public function setDestroyDate($utxDate = 0)
    {
        return $this->updateConfigDate('dtmDestroy', $utxDate);
    }

    /**
     * Some protected, internal methods
     *
     */

    protected function initConfig()
    {
        // Config record already exist
        if (0 <> (int)$this->intBullGuardConfigID) {

            return false;
        }

        if (0 == $this->getProductComponentInstanceID() || false == is_numeric($this->getProductComponentInstanceID())) {

            return false;
        }

        // Be sure that record don't exist in table
        if (false == $this->__construct($this->getProductComponentInstanceID())) {

            $strQuery = "INSERT INTO userdata.tblConfigBullGuard (intProductComponentInstanceID,dtmCreate) VALUES (" . $this->getProductComponentInstanceID() . ",NOW())";

            $dbhConnection = get_named_connection_with_db('userdata');
            $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

            $this->intBullGuardConfigID = PrimitivesInsertIdGet($dbhConnection);

            return true;
        }

        return false;
    }

    /**
     * Enter description here...
     *
     * @param unknown_type $strFieldName
     * @param unknown_type $utxDate
     * @return unknown
     */
    protected function updateConfigDate($strFieldName, $utxDate = 0)
    {
        if (0 == $this->intBullGuardConfigID || false == is_numeric($this->intBullGuardConfigID)) {

            return false;
        }

        if (0 == $utxDate) {

            $utxDate = time();
        }

        $strDate = date('Y-m-d H:i:s', $utxDate);

        $strQuery = "UPDATE userdata.tblConfigBullGuard SET {$strFieldName} = '{$strDate}' WHERE intBullGuardConfigID = {$this->intBullGuardConfigID} LIMIT 1";
        $dbhConnection = get_named_connection_with_db('userdata');
        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

        unset($strQuery, $dbhConnection, $resResult);
        return true;
    }

    protected function setComponentDesctiption($strDescription)
    {
        $dbhConnection = get_named_connection_with_db('userdata');

        $strDescription = PrimitivesRealEscapeString($strDescription, $dbhConnection);

        if (0 != $this->getComponentID() && true == is_numeric($this->getComponentID())) {

            $strQuery = "UPDATE userdata.components SET description = '{$strDescription}' WHERE component_id = " . $this->getComponentID() . " LIMIT 1";

            $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

            return true;
        }

        return false;
    }

    protected function logStatusChange($strStatus)
    {
        $objEventLogger = $this->prvGetEventLogger();
        $strEventHdl = '';

        if (true == ($objEventLogger instanceof CProductComponentEvent)) {

            switch (strtoupper($strStatus))
            {
                case 'QUEUED_ACTIVATE':
                $strEventHdl = 'BullGuardConfiguration';
                break;

                case 'ACTIVE':
                $strEventHdl = 'BullGuardActivation';
                break;

                case 'DEACTIVE':
                $strEventHdl = 'BullGuardDeactivation';
                break;

                case 'DESTROYED':
                $strEventHdl = 'BullGuardDestruction';
                break;
            }

            if ('' != trim($strEventHdl)) {

                return $objEventLogger->logStatusChange($strEventHdl);
            }
        }

        return false;
    }

    /**
     * Override for interface methods
     *
     */

    /**
     * Configurators
     *
     */

    /**
     * Refreshes the current state of the component
     * If the component is in a queued state
     *
     * @access public
     * @return boolean False if the component is destroyed, otherwise true
     */
    public function refreshCurrentState()
    {
        return 0;
    }

    /**
     * Component configurator function. reEnables the component.
     *
     * @return bool
     */
    public function reEnable()
    {

        $strStatusHandle  = $this->getStatusHandleFromID($this->getStatusID());
        switch($strStatusHandle)
        {
            case 'ACTIVE':
            case 'DESTROYED':
            case 'QUEUED_DESTROY':
            case 'UNCONFIGURED':
            case 'QUEUED_ACTIVATE':
                return FALSE;
                break;

            case 'QUEUED_REACTIVATE':
            case 'QUEUED_DEACTIVATE':
            case 'DEACTIVE':
                break;
        }

        $this->prvSetStatus('QUEUED_REACTIVATE');
        $this->prvSetStatus('ACTIVE');

        $this->logStatusChange('ACTIVE');

        return true;
    }

    /**
     * Component configuration function. Configure the component
     * Set to QA state
     *
     */
    public function configure()
    {
        $this->prvSetStatus('QUEUED_ACTIVATE');
        $this->logStatusChange('QUEUED_ACTIVATE');

        $this->refreshInstance($this->getProductComponentInstanceID());

        if (true == $this->initConfig()) {

            return true;
        }

        return false;
    }

    /**
     * Component configurator function. Enables the component.
     * Set to A state
     *
     * @return bool
     */
    public function enable()
    {
        $strStatusHandle  = $this->getStatusHandleFromID($this->getStatusID());

        switch($strStatusHandle)
        {
            case  'QUEUED_REACTIVATE':
            case  'QUEUED_DEACTIVATE':
            case  'DEACTIVE':
                return $this->reEnable();
                break;

            case  'ACTIVE':
                //Nothing to do here
                    return TRUE;
                break;

            case  'DESTROYED':
            case  'QUEUED_DESTROY':
                   //nothing to do here
                    return FALSE;
                    break;

            case  'UNCONFIGURED':
            case  'QUEUED_ACTIVATE':
                //Continue to enable
                break;
        }

        $this->prvSetStatus('ACTIVE');
        $this->refreshInstance($this->getProductComponentInstanceID());

        $this->logStatusChange('ACTIVE');

        return true;
    }

    /**
     * Component configurator function. Disables the component.
     *
     * @return bool
     */
    public function disable()
    {
        $this->prvSetStatus('DEACTIVE');
        $this->refreshInstance($this->getProductComponentInstanceID());

        $this->logStatusChange('DEACTIVE');

        return true;
    }

    /**
     * Component configurator function. Destroys the component.
     *
     * @return bool
     */
    public function destroy($arrArgs = array())
    {
        $this->setDestroyDate();

        $strDate = date('Y/m/d');
        $this->setComponentDesctiption("Inactive since {$strDate}");

        $this->prvSetStatus('DESTROYED');
        $this->refreshInstance($this->getProductComponentInstanceID());

        $this->logStatusChange('DESTROYED');

        return true;
    }
}
