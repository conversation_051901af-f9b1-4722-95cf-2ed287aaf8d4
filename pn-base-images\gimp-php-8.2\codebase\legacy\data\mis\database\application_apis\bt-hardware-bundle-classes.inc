<?php
	///////////////////////////////////////////////////////////////////////////
	// hardware-bundle-classes.inc
	// <PERSON>, October 2002
	// Revised Neil <PERSON>, June 2004
	// $Id: bt-hardware-bundle-classes.inc,v 1.14 2009-06-10 20:17:36 bselby Exp $
	//
	// This file contains all classes used by the hardware bundle system for BT orders.
	//
	// IMPORTANT:
	// The database has been designed to allow each item of the order to be
	// handled by a different supplier, thereby having its own status. However,
	// these classes have been written with the assumption that the supplier
	// and status for each item in an order are the same.
	//
	// Functions marked *** STATIC *** don't require an instansiated object to be used.
	// i.e. You can just call classname::functionname()
	//
	// Hungarian notation used:
	//
	// str : String
	// int : Integer
	// arr : Array of items (not a row from a database)
	// r   : Record (associative array, eg a row from the database)
	// bin : Boolean
	// h   : Handle (eg database connection or result, or a file handle)
	// m_  : Class member variable (shouldn't be touched outside the class)
	//

	include '/local/data/mis/database/application_apis/HardwareBundle/BTHardwareBundle.class.php';
	include '/local/data/mis/database/application_apis/HardwareBundle/BTHardwareBundleType.class.php';
	include '/local/data/mis/database/application_apis/HardwareBundle/BTHardwareBundleSupplier.class.php';
	include '/local/data/mis/database/application_apis/HardwareBundle/BTHardwareRmaProcess.class.php';