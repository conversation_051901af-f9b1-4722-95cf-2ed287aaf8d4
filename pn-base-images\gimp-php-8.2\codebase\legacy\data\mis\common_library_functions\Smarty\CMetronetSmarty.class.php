<?php

// Simple Smarty wrapper for Metronet. Done so that the Metronet-Smarty specific
// config file will be loaded. (used to set template variables such as secure_portal_url)

class CMetronetSmarty extends Smarty
{
	function CMetronetSmarty()
	{
		// we need to set this to /something/ in order for the config_load() function to work.
		$this->compile_dir = SMARTY_COMPILE_DIR;

		$this->config_load('/local/data/mis/portal_modules/phplib/smarty_metronet_local.conf', 'urls');
	}
}

?>
