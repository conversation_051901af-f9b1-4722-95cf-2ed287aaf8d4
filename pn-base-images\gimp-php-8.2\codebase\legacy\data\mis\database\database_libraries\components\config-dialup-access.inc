<?php
/**
 * Purpose:  Access mini-library for config_dialups
 *
 * @package LegacyCodebase
 * <AUTHOR> <<EMAIL>>
 */
use Plusnet\Feature\FeatureToggleManager;
use Plusnet\FailedPayment\Exceptions\NoFailedPaymentDetailsException;

include_once('/local/data/mis/database/database_libraries/radius-access.inc');
include_once('/local/data/mis/database/database_libraries/product-access.inc');
include_once('/local/data/mis/database/database_libraries/components/config-surf-access.inc');
include_once('/local/data/mis/database/database_libraries/sql_primitives.inc');
include_once('/local/data/mis/database/database_libraries/programme-tool-access.inc');
include_once('/local/data/mis/database/application_apis/adsl_provision.inc');
include_once('/local/data/mis/common_library_functions/common_application_apis/BusinessTier.php');

// Require class for updating brightview soulstone records
require_once '/local/data/mis/database/class_libraries/Brightview/Library.inc';
require_once '/local/data/mis/database/database_libraries/components/CInternetConnectionProduct.inc';

//require maintenance script for cbc if we need to (re)set b/w threshold
require_once '/local/data/mis/database/application_apis/Cbc/PaygBwMaintenance.class.php';
include_once '/local/data/mis/database/database_libraries/adsl-access.inc';

define('REQUIRE_PASSWORD', '16');

// Used to cache configurator lookups; for now, we cache on a per-server basis and set a 1-hour timeout
define('ADSL_INFORMATION_CACHE_DIR', '/tmp');
define('ADSL_INFORMATION_CACHE_FILENAME', 'adslConfiguratorCache.cache');
define('ADSL_INFORMATION_CACHE_TIMEOUT', 3600);

global $staticip_activation_reasons;
$staticip_activation_reasons = array(
    'not_active' => '',
    'cli'        => 'Activated for multiple CLIs (Not requested)',
    'request'    => "Activated on user's request"
);

$adslConfigurators = getAdslConfigurators();

// DJM2015: the joy of global variables which may or may not already exist...
global $global_component_configurators;
if (!isset($global_component_configurators) || !is_array($global_component_configurators)) {
    $global_component_configurators = array();
}

// Hack to insert the component configurator array into PHP5's global scope if it's not already there
if (!isset($GLOBALS['global_component_configurators'])) {
    $GLOBALS['global_component_configurators'] = $global_component_configurators;
} else {
    foreach ($global_component_configurators as $intIndex => $strConfigurator) {
        $GLOBALS['global_component_configurators'][$intIndex] = $strConfigurator;
    }
}

// NOTE; we can't use array_merge() as it renumbers the keys (aka the service-component ids!) if there's a clash
// Instead, we use the PHP5.3 function array_replace()...
$global_component_configurators = array_replace($global_component_configurators, $adslConfigurators);

/**
 * Adds ADSL-related configurators to the global component configurators
 * As this include file is used on portal and workplace, we cache the configurator data to file to minimise
 * database overheads
 *
 * @return undefined
 */
function getAdslConfigurators()
{
    $strCacheName = ADSL_INFORMATION_CACHE_DIR . '/' . ADSL_INFORMATION_CACHE_FILENAME;
    $adslConfigurators = csdi_getDataFromCache($strCacheName, ADSL_INFORMATION_CACHE_TIMEOUT);

    if (!$adslConfigurators) {
        $adslConfigurators = array();

        $dbConn = get_named_connection_with_db('unprivileged_reporting');

        // First, we need to define the INTERNET_CONNECTION configurators.
        // The UNION query will be removed after we finish the cleanup tasks for RPR. At the moment those internet
        // connection components are NOT the radius component so do not get picked up in the original query.
        $strQuery = <<<EOQ
SELECT
    rc.service_component_id,
    IFNULL(scpt.vchHandle, 'LEGACY_DIALUP_COMPONENT') AS vchHandle
FROM
    products.component_radius_config rc
    LEFT JOIN products.tblServiceComponentProduct cpt
        ON (cpt.intServiceComponentID = rc.service_component_id)
    LEFT JOIN products.tblServiceComponentProductType scpt
        ON (scpt.intServiceComponentProductTypeID = cpt.intServiceComponentProductTypeID)
UNION
    SELECT DISTINCT
        scc.service_component_id,
        scpt.vchHandle
    FROM
        products.service_definitions sd
        INNER JOIN products.tblProductVariant pv USING (intProductVariantId)
        INNER JOIN products.tblProductFamily pf USING (intProductFamilyId)
        INNER JOIN products.service_component_config scc ON sd.service_definition_id = scc.service_definition_id
        INNER JOIN products.tblServiceComponentProduct scp ON scc.service_component_id = scp.intServiceComponentId
        INNER JOIN products.tblServiceComponentProductType scpt USING (intServiceComponentProductTypeID)
    WHERE
        pf.vchHandle IN ("VALUE", "VALUE_SOLUS", "VALUE_DUALPLAY")
        AND scpt.vchHandle = "INTERNET_CONNECTION"
EOQ;

        $result = PrimitivesQueryOrExit($strQuery, $dbConn);
        $data = PrimitivesResultsAsArrayGet($result);

        foreach ($data AS $configurator) {
            $strName = $configurator['vchHandle'];
            $intID = $configurator['service_component_id'];

            switch ($strName) {
                case 'INTERNET_CONNECTION':
                    // Configuration for CInternetConnectionProduct based components must be handled separately
                    $adslConfigurators[$intID] = 'config_internet_connection_configurator';
                    break;
                case 'LEGACY_DIALUP_COMPONENT':
                    // It's old dialup component without ProductComponent system handling
                    $adslConfigurators[$intID] = 'config_dialup_configurator';
                    break;
                default:
                    // ProductComponent based component, we use config_dialup_configurator for backwards compatibility
                    error_log(
                        __FILE__ . ': ' . __LINE__ . ': ' .
                        "WARNING: ADSL ProductComponent component $intID/$strName will be managed via the default" .
                        " config_dialup_configurator(). This can cause various issues!"
                    );

                    $adslConfigurators[$intID] = 'config_dialup_configurator';
                    break;
            }
        }

        // Then we need to define the static-ip configurators
        $strQuery = 'SELECT service_component_id FROM products.component_ipzone_config';
        $result = PrimitivesQueryOrExit($strQuery, $dbConn);
        $data = PrimitivesResultsAsArrayGet($result);

        foreach ($data AS $configurator) {
            $intID = $configurator['service_component_id'];
            $adslConfigurators[$intID] = 'config_staticip_configurator';
        }

        csdi_putDataToCache($strCacheName, $adslConfigurators);
    }

    return $adslConfigurators;
}

/////////////////////////////////////////////////////////////
// Function:  config_dialup_component_convert
// Purpose:   Convert one dialup component to another
//            keeping and extra hours attached to that component
//            but reseting and standard hours.
//
// Arguments: $component_id
//            $replacement_dialup_component_type_id
// Returns:   $radius_id, success
//            -1,         failure contacting radius DB
//            -2,         No active dialup components
//                        deactivated base RADIUS entry
//            -3,         The target component cannot be
//                        deactivated and was set active.
//            -4,         The component and/or the new type
//                        are not dialup components
//            -5,         The component is alraedy of this type
/////////////////////////////////////////////////////////////
function config_dialup_component_convert($component_id, $replacement_dialup_component_type_id)
{
    $replacement_dialup_component_type_id = addslashes($replacement_dialup_component_type_id);

    $component = userdata_component_get($component_id);

    $connection = get_named_connection_with_db('userdata');

    $query = "UPDATE components SET component_type_id ='$replacement_dialup_component_type_id' " .
        "WHERE component_id ='$component_id'";

    PrimitivesQueryOrExit($query, $connection, 'Convert dialup component ');

    // ValueFamily products have it's own radius component - do not run config_dialup_set_all_radius_data() for them
    return config_dialup_set_all_radius_data($component['service_id'], $component_id, true);
}


function resellerHasFailedBillingPage(array $service)
{
    $dbConnection = get_named_connection_with_db('ResellerFailedBillingConfig');

    // First check I'm a reseller
    if ('partner' != $service['isp']) {
        return false;
    }

    //Get my actorId
    $intServiceId = (integer)$service['service_id'];
    $strQuery = "select actorID as intActorId FROM PlusnetSession.tblBusinessActor ba inner join PlusnetSession.tblAuthenticationRealm ar on ar.authenticationRealmID = ba.authenticationRealmID where ar.realm in('partner.plus.net') and ba.externalUserID='{$intServiceId}'";

    $dbConnection = get_named_connection_with_db('userdata');
    $refResult = PrimitivesQueryOrExit($strQuery, $dbConnection, 'Check if curently restricted');
    $intActorId = PrimitivesResultGet($refResult, 'intActorId');

    // Switch to the partner database and get the failed billing URL if they have one

    $dbResellerConnection = get_named_connection_with_db('ResellerFailedBillingConfig');

    $strQuery = "SELECT if(count(*) > 0,1,0)  as bolHasFailedBillingUrl FROM Reseller.tblFailedBillingUrl fbu
INNER JOIN Reseller.tblEndUser eu on eu.intPartnerActorId = fbu.intPartnerActorId
WHERE eu.intEndUserActorId='{$intActorId}'";

    $refResult = PrimitivesQueryOrExit($strQuery, $dbResellerConnection, 'Check if curently restricted');

    $bolHasFailedBillingUrl = PrimitivesResultGet($refResult, 'bolHasFailedBillingUrl');

    return $bolHasFailedBillingUrl;
}

/////////////////////////////////////////////////////////////
// Function:  LatestOpenFailedBillingEvent
// Purpose:   Return the service event ID of the lastest
//            open failed_billing event.
//
//            components and userdata
//
// Arguments: $service_id
//
// Returns:   boolean. >0,  The event ID of the lastest open failed_billing event
//                     <= 0, No open fialed billing event
//
/////////////////////////////////////////////////////////////


function LatestOpenFailedBillingEvent($service_id)
{
    $arrEnteredFailedBillingBeforeDeactiviationID = array(
        SERVICE_EVENT_FAILED_BILLING_STATE_ENTERED,
        SERVICE_EVENT_FAILED_BILLING_WILL_ENTER_AFTER_GRACE_PERIOD,
        SERVICE_EVENT_FAILED_BILLING_WILL_ENTER_AFTER_GRACE_PERIOD_WITH_EMAIL
    );

    $arrClosesFailedBillingBeforeDeactiviation = array(
        SERVICE_EVENT_FAILED_BILLING_REACTIVATED_BY_PAYMENT,
        SERVICE_EVENT_FAILED_BILLING_RESTRICTIONS_REMOVED_BY_CSC,
        SERVICE_EVENT_FAILED_BILLING_RESTRICTIONS_REMOVED_BY_PAYMENT,
        SERVICE_EVENT_FAILED_BILLING_REACTIVATED_BY_CSC,
        SERVICE_EVENT_FAILED_BILLING_GRACE_PERIOD_ENDED,
    );

    $strQuery = '
SELECT opening.service_event_id
  FROM service_events opening
 LEFT JOIN service_events closing ON closing.service_event_id > opening.service_event_id
   AND closing.event_date >= opening.event_date
   AND closing.event_type_id IN (' . implode(',', $arrClosesFailedBillingBeforeDeactiviation) . ')
   AND closing.service_id = opening.service_id
 WHERE opening.event_type_id IN (' . implode(',', $arrEnteredFailedBillingBeforeDeactiviationID) . ')
   AND opening.service_id = ' . $service_id . '
   AND closing.service_event_id IS NULL
  ORDER BY opening.service_event_id DESC
 LIMIT 1';

    $dbConnection = get_named_connection_with_db('userdata');

    $refResult = PrimitivesQueryOrExit($strQuery, $dbConnection, 'Check if curently restricted');

    $intLatestServiceEventID = PrimitivesResultGet($refResult, 'service_event_id');

    if (FeatureToggleManager::isOnFiltered('RBM_MIGRATION_COMPLETE', null, null, null, $service_id)) {
        $fbVersionTwoJourney = Plusnet\FailedPayment\FailedPaymentHandler::isFbVersionTwoJourney($service_id);
    } else {
        $fbVersionTwoJourney = BusTier_BusTier::getClient('failed_billing')->isFbVersionTwoJourney(
            new PositiveInt($service_id)
        );
    }

    if (empty($intLatestServiceEventID) && $fbVersionTwoJourney) {
        return true;
    }

    return $intLatestServiceEventID;

} // func : LatestOpenFailedBillingEvent

/////////////////////////////////////////////////////////////
// Function:  bolDialupsRestrictedByFailedBilling
// Purpose:   To check if the producti's dialups are
//            currently restricted to the the failed
//            billing proxy by having failed_billing
//            but yet deactivated or reactiviated after
//            payment recieved.
//
//            In short it pairs up begin failed_billing
//            events with the first event which close failed_billing or
//            represent deactivation after failed_billing.
//            them. If it finds any unclosed pairs it
//            means that dialups are currently resticted
//            but not yet deactivated
//
//            components and userdata
//
// Arguments: $service_id
//
// Returns:   boolean. true,  dialups are currently restricted
//                     false, dialups use the unrestricited groups
//
/////////////////////////////////////////////////////////////

function bolFinancialFailedBilling($service_id)
{
    $bolDebug = false;
    $intPid = posix_getpid();

    if (defined('MULTI_PROCESS_DB_CACHING') && MULTI_PROCESS_DB_CACHING == true) {
        $bolDebug = true;
    }

    /*
        failed_billing related events -- At the point the code was last modified
        36 | failed_billing - payment reactivation post deactivation
        35 | failed_billing - deactivation
        34 | failed_billing - CSC reactivation
        32 | failed_billing
        33 | failed_billing - payment reactivation
        37 | failed_billing - CSC reactivation post deactivation
     */
    $intEnteredFailedBillingBeforeDeactiviationID = 32;
    $strClosesFailedBillingBeforeDeactiviation = "36,35,34,33,37";

    // Check if service_id is set
    if (!$service_id) {
        return false;
    }

    if (is_array($service_id)) {
        if (sizeof($service_id) == 0) {
            return false;
        }

        $strServiceIDList = implode(", ", $service_id);
    } else {
        $strServiceIDList = $service_id;
    }

    $strQuery = " SELECT count(*) as bolDialupRestrictedByFailedBilling " .
        " FROM  service_events opening " .
        " LEFT JOIN  service_events closing on closing.service_event_id > opening.service_event_id " .
        " AND closing.event_date >= opening.event_date " .
        " AND closing.event_type_id in ($strClosesFailedBillingBeforeDeactiviation) " .
        " AND closing.service_id = opening.service_id " .
        " WHERE opening.event_type_id = $intEnteredFailedBillingBeforeDeactiviationID AND opening.service_id IN ($strServiceIDList) " .
        "  AND closing.service_event_id is null ";

    $dbConnection = get_named_connection_with_db('userdata');

    $refResult = PrimitivesQueryOrExit($strQuery, $dbConnection, 'Check if curently restricted');
    $bolInFailedBilling = PrimitivesResultGet($refResult, 'bolDialupRestrictedByFailedBilling') > 0 ? true : false;
    if ($bolDebug) {
        print ("INFO [pid $intPid]: FB check 1 returned " . ($bolInFailedBilling ? 'TRUE' : 'FALSE') . "\n");
    }

    $manager = BusTier_BusTier::getClient('failed_billing');
    if ($bolDebug) {
        print ("INFO [pid $intPid]: got BusTier FB client\n");
    }

    $service = userdata_service_get($service_id);
    $serviceDefinitionDetails = product_get_service($service['type']);

    if ($bolDebug) {
        print ("INFO [pid $intPid]: got service/product details for $service_id/{$service['type']}\n");
    }

    if (FeatureToggleManager::isOnFiltered('RBM_MIGRATION_COMPLETE', null, null, null, $service_id)) {
        $isIspReportSectorConfigured = Plusnet\FailedPayment\FailedPaymentHandler::isIspReportSectorConfigured(
            $serviceDefinitionDetails['isp'],
            $service['strReportSector']
        );
    } else {
        $isIspReportSectorConfigured = $manager->isIspReportSectorConfigured(
            new String($serviceDefinitionDetails['isp']),
            new String($service['strReportSector'])
        );
    }

    if (empty($bolInFailedBilling) && $isIspReportSectorConfigured) {
        // if the Migration complete feature toggle is on, use the Failed Payment methods to get failed billing data
        if (FeatureToggleManager::isOnFiltered('RBM_MIGRATION_COMPLETE', null, null, null, $service_id)) {
            if ($bolDebug) {
                print ("INFO [pid $intPid]: fetching FailedBilling_Instance using "
                    . "Plusnet\\FailedPayment\\Entity\\FailedPaymentInstance::fetchByServiceId\n");
            }
            try {
                $failedPaymentInstance =
                    Plusnet\FailedPayment\Entity\FailedPaymentInstance::fetchByServiceId($service_id);

                $endDate = $failedPaymentInstance->getEndDate();

                $endDate = !empty($endDate)
                    ? \I18n_Date::fromDateTime($endDate) : null;

                $instance = new FailedBilling_Instance(
                    $failedPaymentInstance->getBusinessActor(),
                    new PositiveInt($failedPaymentInstance->getInstanceId()),
                    new Int($failedPaymentInstance->getInvoiceId()),
                    new PositiveInt($failedPaymentInstance->getIspReportSectorId()),
                    new PositiveInt($failedPaymentInstance->getFailedBillingDay()),
                    \I18n_Date::fromDateTime($failedPaymentInstance->getStartDate()),
                    $endDate
                );
            } catch (NoFailedPaymentDetailsException $e) {
                if ($bolDebug) {
                    print ("INFO [pid $intPid]: exception caught: " . $e->getMessage() . ": returning false\n");
                }

                return false;
            }
        } else {
            // If the migration complete toggle is not enabled revert to using the old failed billing method
            if ($bolDebug) {
                print ("INFO [pid $intPid]: calling FailedBilling_Instance::fetchInstanceByServiceId()\n");
            }
            try {
                $instance = FailedBilling_Instance::fetchInstanceByServiceId(new PositiveInt($service_id));
            } catch (FailedBilling_NoDataException $e) {
                if ($bolDebug) {
                    print ("INFO [pid $intPid]: exception caught: " . $e->getMessage() . ": returning false\n");
                }

                return false;
            }
        }
    }

    if ($instance instanceof FailedBilling_Instance) {
        if ($bolDebug) {
            print ("INFO [pid $intPid]: FB instance found: returning true\n");
        }

        return true;
    }

    return $bolInFailedBilling;
} // function bolFinancialFailedBilling($service_id)

function bolResellerRestrictedBroadband($service_id)
{

    /*
        Reseller's end user restriction
        128 | broadband service restricted
        129 | restriction lifted
     */
    $intEnteredResellerFailedBillingRestrictions = 128;
    $intClosedResellerFailedBillingRestrictions = "129";

    // Check if service_id is set
    if (!$service_id) {
        return false;
    }

    if (is_array($service_id)) {
        if (sizeof($service_id) == 0) {
            return false;
        }

        $strServiceIDList = implode(", ", $service_id);
    } else {
        $strServiceIDList = $service_id;
    }

    $strQuery = " SELECT count(*) as intDialupRestrictedByReseller " .
        " FROM  service_events opening " .
        " LEFT JOIN  service_events closing on closing.service_event_id > opening.service_event_id " .
        " AND closing.event_date >= opening.event_date " .
        " AND closing.event_type_id in ($intClosedResellerFailedBillingRestrictions) " .
        " AND closing.service_id = opening.service_id " .
        " WHERE opening.event_type_id = $intEnteredResellerFailedBillingRestrictions AND opening.service_id IN ($strServiceIDList) " .
        "  AND closing.service_event_id is null ";

    $dbConnection = get_named_connection_with_db('userdata');

    $refResult = PrimitivesQueryOrExit($strQuery, $dbConnection, 'Check if curently restricted');
    $bolInFailedBilling = PrimitivesResultGet($refResult, 'intDialupRestrictedByReseller') > 0 ? true : false;

    return $bolInFailedBilling;

} // function bolResellerRestrictedBroadband($service_id)


////////////////////////////////////////////////////////////////////////////////

/*
    ----------------------------------------
    bolFinancialFailedBillingGracePeriodEx()
    ----------------------------------------

    Input: mixed; either a single service ID or an array of service IDs to
        determine if it/they are in failed billing grace period.

    Output: false = error.
            array containing zero or more service IDs that have open failed billing
            grace period service events.
 */

function bolFinancialFailedBillingGracePeriodEx($service_id)
{
    // if it's not set, or if it's set to nothing, then bail out
    if (!isset($service_id) || $service_id == '') {
        return false;
    }

    if (is_array($service_id)) {
        // an empty array makes no sense
        if (sizeof($service_id) == 0) {
            return false;
        }

        $strServiceIDs = implode("', '", $service_id);      // string-ize N service IDs
    } else {
        $strServiceIDs = $service_id;       // not an array, but no problem as the query is easy going :)
    }

    // probably not as efficient runtime-wise, but a lot easier to maintain by using defines instead of their literal values
    $strClosesFailedBillingBeforeDeactiviation = implode("', '", array(
        SERVICE_EVENT_FAILED_BILLING_STATE_ENTERED,
        SERVICE_EVENT_FAILED_BILLING_RESTRICTIONS_REMOVED_BY_PAYMENT,
        SERVICE_EVENT_FAILED_BILLING_RESTRICTIONS_REMOVED_BY_CSC,
        SERVICE_EVENT_FAILED_BILLING_DEACTIVATION,
        SERVICE_EVENT_FAILED_BILLING_REACTIVATED_BY_PAYMENT,
        SERVICE_EVENT_FAILED_BILLING_REACTIVATED_BY_CSC,
        SERVICE_EVENT_FAILED_BILLING_GRACE_PERIOD_ENDED
    ));

    // Problem 30914 - we need to detect both types of grace period; pre-and-post email notification.
    $strOpeningGraceEvents = implode("', '", array(
        SERVICE_EVENT_FAILED_BILLING_WILL_ENTER_AFTER_GRACE_PERIOD,
        SERVICE_EVENT_FAILED_BILLING_WILL_ENTER_AFTER_GRACE_PERIOD_WITH_EMAIL
    ));

    $strQuery = "SELECT opening.service_id as intServiceID
                   FROM service_events opening
              LEFT JOIN service_events closing on closing.service_event_id > opening.service_event_id
                    AND closing.event_date >= opening.event_date
                    AND closing.event_type_id in ('{$strClosesFailedBillingBeforeDeactiviation}')
                    AND closing.service_id = opening.service_id
                  WHERE opening.event_type_id IN ('{$strOpeningGraceEvents}')
                    AND opening.service_id IN ('{$strServiceIDs}')
                    AND closing.service_event_id is null
            GROUP BY opening.service_id";

//          echo "{$strQuery}<br>\n";

    $dbConnection = get_named_connection_with_db('userdata');

    $hResult = PrimitivesQueryOrExit($strQuery, $dbConnection, 'Check if in the grace period before restriction');

    $arrResults = PrimitivesResultsAsListGet($hResult);

//          echo "<pre>Grace period results are:"; print_r($arrResults); echo "</pre>";

    return $arrResults;
}

////////////////////////////////////////////////////////////////////////////////

/*
    --------------------------------------
    bolFinancialFailedBillingGracePeriod()
    --------------------------------------

    Determines whether a single service ID (or at least one in a collection of
    service IDs) is in the Failed Billing Grace Period (Metronet)

    Returns: true = the specified service ID (or at least 1 of them, if an
                    array is passed) is marked as being in the grace period.

            false = the specified service ID (or none of them specified) is
                    not in the grace period.

    Note: I have put this function in here because it is similar to
    bolFinancialFailedBilling() although I thought it would probably
    be better suited to live in financial-access.inc ?
 */

function bolFinancialFailedBillingGracePeriod($service_id)
{
    $arrServiceIDs = bolFinancialFailedBillingGracePeriodEx($service_id);

    return ($arrServiceIDs !== false && sizeof($arrServiceIDs) > 0) ? true : false;
}

////////////////////////////////////////////////////////////////////////////////

function bolDialupRestrictedDueToUsageCap($intServiceID)
{
// Usage cap related events -- At the point the code was last modified
// 47 | Usage cap - restriction added
// 48 | Usage cap - restriction removed

    if (!preg_match('/^[0-9]+$/', $intServiceID)) {
        return false;
    }

// DJM 2013: query optimised by the DBAs to help address P76583
    $strQuery = <<<EOQ
SELECT 
count(*) AS bolDialupRestrictedByReachingUsageCap
FROM  
service_events opening
WHERE 
opening.event_type_id = 47
AND opening.service_id = $intServiceID 
AND NOT EXISTS (
    SELECT 
        *
    FROM 
        service_events closing
    WHERE 
        closing.service_id = opening.service_id
        AND closing.service_event_id > opening.service_event_id
        AND closing.event_date >= opening.event_date
        AND closing.event_type_id = 48
)
EOQ;

    $dbhConnection = get_named_connection_with_db('userdata');

    $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection, 'Check if curently restricted by reaching usage cap');

    if (PrimitivesResultGet($resResult, 'bolDialupRestrictedByReachingUsageCap') > 0) {
        return true;
    }

    return false;

} // function bolDialupRestrictedDueToUsageCap($intServiceID)

////////////////////////////////////////////////////////////////////////////////

/*
    ------------------------------
    GetFailedBillingEventSummary()
    ------------------------------
 */

function GetFailedBillingEventSummary($service_id)
{
    if (!isset($service_id) || $service_id == '') {
        return false;
    }

    $bolRestrictedByFailedBilling = bolFinancialFailedBilling($service_id);
    $bolRestrictedByFailedBillingGracePeriod = bolFinancialFailedBillingGracePeriod($service_id);
    $bolDeactivatedByFailedBilling = LatestOpenFailedBillingEvent($service_id);

    $strFailedBillingText = $strFailedBillingTextShort = '';

    if ($bolDeactivatedByFailedBilling && !$bolRestrictedByFailedBillingGracePeriod) {
        $strFailedBillingText = 'Deactivated after failed payment';
        $strFailedBillingTextShort = 'failed billing';
    } elseif ($bolRestrictedByFailedBillingGracePeriod) {
        $strFailedBillingText = 'Currently in failed payment grace period';
        $strFailedBillingTextShort = 'grace period';
    } elseif ($bolRestrictedByFailedBilling) {
        $strFailedBillingText = 'Restricted after failed payment';
        $strFailedBillingTextShort = 'restricted';
    } elseif (bolDialupRestrictedDueToUsageCap($service_id)) {
        $strFailedBillingText = 'Restricted due to data transfer cap';
        $strFailedBillingTextShort = 'restricted';
    }

    $arrFailedBillingInfo = array(
        'strLongDescription'              => $strFailedBillingText,
        'strShortDescription'             => $strFailedBillingTextShort,
        'bolRestricted'                   => $bolRestrictedByFailedBilling,
        'bolGracePeriod'                  => $bolRestrictedByFailedBillingGracePeriod,
        'intLatestOpenFailedBillingEvent' => $bolDeactivatedByFailedBilling
    );

    return $arrFailedBillingInfo;
}

////////////////////////////////////////////////////////////////////////////////

/////////////////////////////////////////////////////////////
// Function:  config_dialup_set_all_radius_data
// Purpose:   Set up RADIUS to be completely in sync with all
//            components and userdata
//
// Arguments: $service_id
//            $component_id, component being sent signal (optional)
//            $only_referesh_this_component, update only the target
//                                          component reseting standard dialup
//                                          hours (defaults to false)
//
// Returns:   $radius_id, success
//            -1,         failure contacting radius DB
//            -2,         No active dialup components
//                        deactivated base RADIUS entry
//            -3,         The target component cannot be
//                        deactivated and was set active.
//            -4,         One or more of this users IP/Dialup
//                        configurations is misconfigured
//            -5,         Couldn't request disconnection of
//                        any currently active connections
//                        via radius
/////////////////////////////////////////////////////////////

function config_dialup_set_all_radius_data(
    $service_id,
    $target_component_id = '',
    $only_refresh_this_component = false,
    $activateComponent = true
) {
    $bolFriaco = false;
    $strFriacoCLI = '';

    $return_value = 0;

    //TRUE - get new data (not from cache)
    $service = userdata_service_get($service_id, true);
    $service_id = intval($service['service_id']);
    $username = $service['username'];
    $isp = $service['isp'];
    $password = $service['password'];
    $cli = $service['cli_number'];
    $type = $service['type'];

    //This has been set to false to allow the user still browse the web during failed billing - DO NOT CHANGE IT without talking to a BA.
    $bolFailedBilling = false;

    $bolResellerDisabledBroadband = false;
    $bolResellerDisabledBroadband = bolResellerRestrictedBroadband($service_id);

    $password_visible_to_support = product_password_visible_to_support($service['type']);

    // only refresh this component doesn't make sence if no target component given
    if ($target_component_id == '') {
        $only_refresh_this_component = false;
    }

    // Get StaticIP component types
    $array_staticip_types = product_staticip_component_types_get();

    // See if this account has any active StaticIP components attached
    $array_type_ids = array();

    while (list(, $type) = each($array_staticip_types)) {
        $array_type_ids[] = $type['service_component_id'];
    }

    $criteria = array(
        'type'       => $array_type_ids,
        'status'     => array('active'),
        'service_id' => $service_id
    );

    $array_staticip_components = userdata_component_find($criteria, 0, 0);

    // Find out the static IP to use
    if (count($array_staticip_components) == 0) {
        $ip_address = 'pool';
        $ip_netmask = '***************';
        $additional_flags = 0;
        settype($additional_flags, 'integer');
    } elseif (count($array_staticip_components) == 1) {
        $intStaticIpId = $array_staticip_components[0]['config_id'];
        if (is_numeric($intStaticIpId) && $intStaticIpId > 0) {
            $arrStaticIp = config_staticip_get($intStaticIpId);
            $ip_address = $arrStaticIp['ip_address'];
            $ip_netmask = $arrStaticIp['ip_netmask'];
            $additional_flags = 0;
            settype($additional_flags, 'integer');
        }
    }

    // Check if account is chargeable

    $is_chargeable = false;
    $product_def = product_get_service($service['type']);

    if ($product_def['type'] == 'colease' ||
        $product_def['minimum_charge'] > 0
    ) {
        $is_chargeable = true;
    }

    // Check if (any) associated account is chargeable, this allows
    // accounts such as netstart moblie accounts to ommit CLI when bundled
    // with subscription accounts.

    $associated_account_is_chargeable = false;

    $parent_service_id = userdata_service_is_associated($service_id);

    if ($parent_service_id > 0) {
        // is parent account active
        $parent_service = userdata_service_get($parent_service_id);

        if ($parent_service['status'] == 'active' ||
            $parent_service['status'] == 'deactive'
        ) {
            // is parent account one of the weird colease bunch
            $parent_product = product_get_service($parent_service['type']);

            if ($parent_product['type'] == 'colease' ||
                $parent_product['minimum_charge'] > 0
            ) {
                $associated_account_is_chargeable = true;
            }
        }
    }

    // Decide if account is allowed on 0845 without a CLI
    $allow_cli_or_password = 'N';

    if ($is_chargeable || $associated_account_is_chargeable ||
        $password_visible_to_support == 'N'
    ) {
        $allow_cli_or_password = 'Y';
    }

    // Check if account is destroyed, destroyed accounts cannot have active components
    $account_is_destroyed = $service['status'] == 'destroyed' ? true : false;


    $array_dialup_components = radiusGetServiceDialupComponents($service_id);


    $max_quota_id = 0;

    $array_subscriptions_to_add = array();
    $array_components_to_deactivate = array();
    $array_components_to_destroy = array();
    $array_exclude_subscriptions_from_delete = array();

    // states in which the quota should be recognised
    $array_active_quota_states = array(
        'active', 'queued-activate',
        'queued-reactivate', 'queued-deactivate',
        'deactive'
    );
    //decide final state for each dialup component


    foreach ($array_dialup_components AS $component) {
        $type = $component['component_type_id'];

        $bolAdslComponent = ($component['vchCommunicationMethod'] == 'ADSL/RDSL');
        $bolFriaco = ('FRIACO' == $component['vchCommunicationMethod']) ? true : false;

        $radius_config =& $component;

        // running total of the largest quota
        if (($radius_config['quota_id'] > $max_quota_id) &&
            in_array($component['status'], $array_active_quota_states)
        ) {
            $max_quota_id = $radius_config['quota_id'];
        }

        // basic rules
        $component_requires_external_activation = ($radius_config['requires_active_account'] == 'Y' &&
            ($service['status'] == 'queued-activate' || $service['status'] == '' || $service['status'] == 'unconfigured' || $service['status'] == 'presignup'));
        if ($bolAdslComponent && isAdslDateActiveNull($service_id)) {
            $component_requires_external_activation = true;
        }
        $marked_for_destruction = ($account_is_destroyed ||
            $component['status'] == 'queued-destroy' ||
            $component['status'] == 'destroyed');

        $marked_for_deactivation = ($component['status'] == 'queued-deactivate' || $component['status'] == 'deactive' ||
            (in_array($service['status'], array('deactive', 'queued-deactive')) && $radius_config['requires_active_account'] == 'Y' &&
                $radius_config['intProxyRestrictedGroupID'] == 0));

        if ($marked_for_destruction) {
            $array_components_to_destroy[] = array(
                'component_id'  => $component['component_id'],
                'new_status'    => 'destroyed',
                'intCompTypeID' => $component['component_type_id']
            );

            continue;
        }

        // set return value if the target component cannot be deactived as requested
        if ($target_component_id == $component['component_id'] &&
            $marked_for_deactivation && $radius_config['can_be_disabled'] == 'N'
        ) {
            $return_value = -3;
        }

        if ($marked_for_deactivation && $radius_config['can_be_disabled'] == 'Y') {
            if ((!$only_refresh_this_component) || ($component['component_id'] == $target_component_id)) {
                $array_components_to_deactivate[] = array(
                    'component_id'            => $component['component_id'],
                    'new_status'              => 'deactive',
                    'dialup_group_id'         => $radius_config['dialup_group_id'],
                    'subscription_required'   => $radius_config['subscription_required'],
                    'inital_standard_seconds' => $radius_config['standard_seconds_per_month'],
                    'intCompTypeID'           => $component['component_type_id']
                );

            }

            // set deactive components to 'awaiting refresh'
            if ($component['status'] == 'deactive') {
                userdata_component_set_status($component['component_id'], 'queued-deactivate');
            }
            continue;
        }


        // set active components to 'awaiting refresh'
        if ($component['status'] == 'active' && !$bolResellerDisabledBroadband) {
            userdata_component_set_status($component['component_id'], 'queued-activate');
        }

        //  component should be active ?
        if (!$component_requires_external_activation || !$activateComponent) {
            if ((!$only_refresh_this_component) || ($component['component_id'] == $target_component_id)) {
                $arrRadiusDialupComponents = radiusGetServiceDialupComponents($service_id);
                $arrRadiusDialupComponent = (isset($arrRadiusDialupComponents[$component['component_id']])) ?
                    $arrRadiusDialupComponents[$component['component_id']] : null;
                $intDialupGroupId = (is_array($arrRadiusDialupComponent) && isset($arrRadiusDialupComponent['dialup_group_id'])) ?
                    $arrRadiusDialupComponent['dialup_group_id'] : null;
                $arrSubscriptionToAdd = array(
                    'dialup_group_id'          => $intDialupGroupId,
                    'ip_check_dialup_group_id' => $radius_config['dialup_group_id'],
                    'subscription_required'    => $radius_config['subscription_required'],
                    'component_id'             => $component['component_id'],
                    'staticip_allowed'         => $radius_config['staticip_allowed'],
                    'service_component_id'     => $radius_config['service_component_id'],
                    'name'                     => $radius_config['name'],
                    'inital_standard_seconds'  => $radius_config['standard_seconds_per_month'],
                );
                if (isset($radius_config['additional_flags']) &&
                    settype($radius_config['additional_flags'], 'integer') &&
                    $radius_config['additional_flags'] > 0
                ) {
                    $additional_flags = ($additional_flags |
                        $radius_config['additional_flags']);
                }


                $arrSubscriptionToAdd['bolRestricted'] = false;

                if ($bolAdslComponent && ($bolFailedBilling || $bolResellerDisabledBroadband) &&
                    (isset($radius_config['intProxyRestrictedGroupID']) &&
                        $radius_config['intProxyRestrictedGroupID'] > 0)
                ) {
                    $arrSubscriptionToAdd['bolRestricted'] = true;
                    $arrSubscriptionToAdd['dialup_group_id'] = $radius_config['intProxyRestrictedGroupID'];
                    // Now we allow dynamic IP on ADSL force the assigning of a static IP if your in failed_billing
                    $arrSubscriptionToAdd['staticip_allowed'] = 'always required';

                    if ($bolResellerDisabledBroadband && false == resellerHasFailedBillingPage($service)) {
                        // The reseller has no failed billing page to send them to
                        // So we simply turn their access off.
                        continue;
                    }
                }

                $array_subscriptions_to_add[] = $arrSubscriptionToAdd;

            } // if : component to be refreshed

            if (!$bolFailedBilling && !$bolResellerDisabledBroadband) {
                $array_exclude_subscriptions_from_delete[$radius_config['dialup_group_id']] = $radius_config['dialup_group_id'];
            } else {
                if (isset($radius_config['intProxyRestrictedGroupID']) &&
                    $radius_config['intProxyRestrictedGroupID'] > 0
                ) {
                    $array_exclude_subscriptions_from_delete[$radius_config['intProxyRestrictedGroupID']] = $radius_config['intProxyRestrictedGroupID'];
                }
            }
        }

    } // foreach : dialup component

    // Get the service's CLIs from userdata.
    $array_clis = config_cli_get_all($service_id, 0);

    // Validate each dialup to make sure it's static IP settings are correct
    DialupCheckDialupGroupIPCompatibility($array_subscriptions_to_add, $array_components_to_deactivate, $service, count($array_clis), $return_value, $array_exclude_subscriptions_from_delete, $service['status']);

    reset($array_subscriptions_to_add);

    /*
    //The next 8 lines are used in testing & are intentionally commented out in production
        error_log("====================== DEACTIVE ==========================\n");
        error_log(print_r($array_components_to_deactivate,1));
        error_log("====================== DONT DELETE =======================\n");
        error_log(print_r($array_exclude_subscriptions_from_delete,1));
        error_log("====================== ACTIVATE ==========================\n");
        error_log(print_r($array_subscriptions_to_add,1));
        error_log("==========================================================\n");
        error_log("======================== DESTROY ========================\n");
        error_log(print_r($array_components_to_destroy,1));
        error_log("==========================================================\n");
     */
    // Look for existing radius user
    $radius_id = radius_user_exists($service['username'], $service['isp']);

    if ($password == '') {
        $password = null;
    }

    if ($radius_id != 0) {
        /*
        * Due to the impact of readius component rereshes blatting non-synced
        * (w/ userdata.services) passwords the ability for a refresh to sync passwords
        * has been removed entirely. This means that anywhere that changes a password
        * that should be propagated to radius automatically will need to manually call
        * radius_user_update_password(username, isp, password)
        */
        $password = null;

        // Update the existing user with the new data
        radius_user_update($radius_id, $username, $isp, $cli, $password,
            $ip_address, $ip_netmask, $service_id,
            $max_quota_id, $password_visible_to_support, $additional_flags,
            0, $allow_cli_or_password);
    } else {
        // Create a new RADIUS user
        $radius_id = radius_user_add($username, $isp, $cli, $password,
            $ip_address, $ip_netmask, $service_id,
            $max_quota_id, $password_visible_to_support, $additional_flags,
            0, $allow_cli_or_password);

        // Safety check to ensure any orphan subscriptions are removed
        $array_exclude_subscriptions_from_delete = array();
    }

    // If we have an IP block we need to let Ellacoya know
    $strUserRealm = adslGetUserRealm($service_id);
    if ($strUserRealm != '') {
        if ($intIPMask = substr($ip_netmask, strrpos($ip_netmask, '.') + 1)) {
            $intBlockSize = 256 - $intIPMask;

            // Not a single IP. Need to tell Ellacoya
            if ($intBlockSize > 1) {
                $bolEvent = 0;
                $objEllacoyaManager = new EllacoyaManager('PLUSNET');
                if ($objEllacoyaManager->eventCheck()) {
                    $bolEvent = 1;
                } else {
                    if (!$objEllacoyaManager->addIpBlock($strUserRealm, $ip_address, $intBlockSize)) {
                        $bolEvent = 1;
                    }
                }

                if ($bolEvent == 1) {
                    $objEllacoyaManager->eventRaise(array(
                        'Operation'       => 'Configure IP Block',
                        'strUserRealm'    => $strUserRealm,
                        'strIPAddress'    => $ip_address,
                        'intBlockSize'    => $intBlockSize,
                        'strProblemGroup' => 'EllacoyaSubscriberError',
                        'strFile'         => __FILE__,
                        'intLine'         => __LINE__
                    ));
                } // if ($bolEvent == 1)
            } // if ($intBlockSize > 1)
        } // if ($intIPMask = substr($ip_netmask, strrpos($ip_netmask, '.')+1))
    } // if ($strUserRealm != '')

    // Only carry on doing things to RADIUS if we have a valid radius_id
    if ($radius_id == 0) {
        return (-1);
    }

    // Delete any existing subscriptions
    radius_user_delete_subscriptions($radius_id, $array_exclude_subscriptions_from_delete);
    //Remove all the speed settings from the user
    RadiusRemoveAllProvidedSpeedAndContentionConfiguration($radius_id);

    if ($bolFailedBilling || $bolResellerDisabledBroadband) {
        radius_mark_for_disconnection($radius_id);
    }

    // mark those components deactivated/destroyed correctly
    foreach ($array_components_to_deactivate AS $update_me) {
        userdata_component_set_status($update_me['component_id'], $update_me['new_status']);

    }

    foreach ($array_components_to_destroy AS $update_me) {
        userdata_component_set_status($update_me['component_id'], $update_me['new_status']);

    }

    reset($array_subscriptions_to_add);

    if ((!WifiHasActiveComponent($service_id)) && count($array_subscriptions_to_add) == 0) {
        // no dailup components active, deactivate the radius user entry
        radius_user_set_status($radius_id, 'N');

        // PDSL changes to remove Proxy configuration
        deleteStationIdUser($service_id, $strUserRealm, $radius_id);

        // Set error type if not the product of an IP/dialup
        // configuration which has already been detected
        if ($return_value != -4) {
            $return_value = -2;
        }

    } else {
        //has active dialup components set base entry active
        $bolActiveRadius = true;

        radius_user_set_status($radius_id, 'Y');

        // return radius_id unless non-deactivable component
        if ($return_value == 0) {
            $return_value = $radius_id;
        }
    }

    // See if Account has an active surf component
    $bolSurfActive = CheckIsSurfActive($service_id);
    // Initialise Array
    $arrExcludeDialGroupIDs = array();


    // Does the account have an active surf component
    if ($bolSurfActive) {
        foreach ($array_subscriptions_to_add AS $radius_config) {
            // Get ID of restricted dial up group id
            $intRestrictedSurfGroupID = GetRestrictedSurfGroupID($radius_config['service_component_id']);
            if ($intRestrictedSurfGroupID > 0) {
                // Add Surf Dial to array so not removed at the end
                $arrExcludeDialGroupIDs[] = $intRestrictedSurfGroupID;

                radius_subscription_refresh($service_id, $radius_id, $intRestrictedSurfGroupID,
                    'Y', $radius_config['ip_configuration']);

                userdata_component_set_status($radius_config['component_id'], 'active');

            } else {
                // Add exisiting group dial id to array so not removed at the end
                $arrExcludeDialGroupIDs[] = $radius_config['dialup_group_id'];

                radius_subscription_refresh($service_id, $radius_id, $radius_config['dialup_group_id'],
                    'Y', $radius_config['ip_configuration']);
                userdata_component_set_status($radius_config['component_id'], 'active');


            }
        }
    } else {


        // Add correct subscriptions to the radius user
        foreach ($array_subscriptions_to_add AS $radius_config) {
            $arrExcludeDialGroupIDs[] = $radius_config['dialup_group_id'];
            radius_subscription_refresh($service_id, $radius_id, $radius_config['dialup_group_id'],
                'Y', $radius_config['ip_configuration']);
            if ($activateComponent) {
                userdata_component_set_status($radius_config['component_id'], 'active');
            }
        }
    }

    // Delete all subscriptions that are not in array arrExcludeDialGroupIDs
    radius_user_delete_subscriptions($radius_id, $arrExcludeDialGroupIDs);

    // Insert additional CLI numbers for user (don't include the primary)
    //

    // Delete existing multiple CLIs
    radius_user_delete_additional_clis($radius_id);

    // Add any CLIS that are set up in userdata
    foreach ($array_clis AS $cli) {
        radius_user_additional_cli_add($radius_id, $cli['cli_number']);
    }

    // Get all clis including primary
    if (true === $bolFriaco) {
        $arrAllCLIs = config_cli_get_all($service_id);

        if (!empty($arrAllCLIs)) {
            foreach ($arrAllCLIs as $arrCLI) {
                if (true == $arrCLI['is_primary_cli']) {
                    $strFriacoCLI = $arrCLI['cli_number'];
                }

            } // end foreach

        } // end if

    } // end if

    // Fix for problem 23892. Only configures prov data for adsl accounts.
    // And making it much faster
    $arrProduct = product_get_adsl_service($service['type']);

    $bolADSL = false;

    if ($arrProduct) {
        configDialupResetProvidedSpeedAndContentionInRadius($service_id, $radius_id);

        $bolADSL = true;
    }

    // Update the brightview soulstone if relevant
    Brightview_Account::updateDialupComponentSettings($service_id, $ip_address, $password, $bolADSL, $strFriacoCLI);
    Cbc_PaygBwMaintenance::autoSetCbcBandwidthThreshold($service_id);

    return ($return_value);

} // func : config_dialup_set_all_radius_data

/**
 * Check whether the ADSL date is null
 *
 * @param intServiceId
 *
 * @return boolean
 */

function isAdslDateActiveNull($intServiceId)
{
    $db = get_named_connection_with_db('adsl');

    $strQuery = "SELECT
                    service_id  AS intServiceId,
                    date_active AS strDateActive
                FROM
                    install_diary
                WHERE
                    service_id = " . $intServiceId . "
                AND
                    date_active IS NOT NULL ";
    $resResult = PrimitivesQueryOrExit($strQuery, $db);
    $arrResult = PrimitivesResultsAsArrayGet($resResult);

    if (count($arrResult) == 0) {
        return true;
    }

    return false;

}

function radiusIsMaafUser($strIsp)
{
    $strQuery = "select if(count(*) > 0, 1, 0) as bolIsMaff from products.visp_config where isp ='$strIsp' and short_brand ='ma'";
    $dbWorkplace = get_named_connection_with_db('product');
    $resResult = PrimitivesQueryOrExit($strQuery, $dbWorkplace);
    $bolIsMaff = PrimitivesResultGet($resResult, 'bolIsMaff');

    return $bolIsMaff;
}

/**
 * Resets the user's speed and contention in radius
 *
 * <AUTHOR>
 *
 * @param intServiceID
 * @param intRadiusID
 * @param intDialupGroupID
 *
 * @return None
 */

function configDialupResetProvidedSpeedAndContentionInRadius($intServiceID, $intRadiusID)
{
    //Check if this is a temp upgrade service_id
    $arrUpgradeDetails = userdata_is_service_adsl_upgrade($intServiceID);
    if ($arrUpgradeDetails) {
        return;
    }


    /**
     * Idle Time outs project
     *
     * <AUTHOR> SWestcott
     * @since  4/11/2005
     */
    require_once '/local/data/mis/database/class_libraries/Customers/C_Core_Service.php';
    require_once '/local/data/mis/database/class_libraries/Customers/C_Core_ADSLService.php';
    require_once '/local/data/mis/database/class_libraries/Products/CConnectionProfile.php';
    require_once '/local/data/mis/database/class_libraries/Products/CConnectionProfileGroup.php';
    require_once '/local/data/mis/database/class_libraries/Products/CProvidedService.php';

    $objAdslService = new C_Core_ADSLService(null, null, $intServiceID);
    $objProvidedService = $objAdslService->getProvidedService();

    if (!empty($objProvidedService)) {
        $objConnectionProfile = $objProvidedService->getConnectionProfile();
        /**
         * END Idle Time outs project
         */

        //Remove all the speed and contention settings of the user in radius
        RadiusRemoveAllProvidedSpeedAndContentionConfiguration($intRadiusID);

        //Get the group_id's the user is subscribed and set the privided speeds setting for each adsl group_id id in subscription table.
        $arrUserAdslDialupGroups = RadiusGetUsersAdslDialupGroups($intRadiusID);

        foreach ($arrUserAdslDialupGroups as $intDialupGroupID) {
            RadiusAddProvidedSpeedAndContentionSettings($intRadiusID,
                $objConnectionProfile->getRgrpId(),
                $intDialupGroupID);
        }
    }
}


function WifiHasActiveComponent($intServiceID)
{
    $dbWorkplace = get_named_connection_with_db('userdata');

    $strQuery = "select count(*) as intActiveDialups from userdata.components uc inner join products.tblComponentWIFIConfig pw on uc.component_type_id = pw.intServiceComponentID where uc.status in ('active','queued-deactivate','queued-deconfigure','queued-destroy') and service_id ='$intServiceID'";

    $resResult = PrimitivesQueryOrExit($strQuery, $dbWorkplace);
    $intActiveDialups = PrimitivesResultGet($resResult, 'intActiveDialups');

    if ($intActiveDialups) {
        return true;
    }

    return false;
} // func : WifiHasActiveComponent


/////////////////////////////////////////////////////////////
// Function: DialupCheckDialupGroupIPCompatibility
// Purpose:  Internal function which checks the StaticIP
//           to be used on each active dialup component
// Arguments:
// Returns:  none
/////////////////////////////////////////////////////////////


function DialupCheckDialupGroupIPCompatibility(&$arrSubscriptionsToAdd, &$arrComponentsToDeactivate,
                                               &$arrService, $unNumCLIs, &$ReturnValue, &$arrExcludeSubscriptionsFromDelete, $strServiceStatus)
{
    $arrErrorsByAccountStates = array(
        'invalid'           => array('new_status' => 'queued-activate', 'report_error' => true),
        'unconfigured'      => array('new_status' => 'queued-activate', 'report_error' => true),
        'presignup'         => array('new_status' => 'queued-activate', 'report_error' => false),
        'queued-activate'   => array('new_status' => 'queued-activate', 'report_error' => false),
        'active'            => array('new_status' => 'queued-activate', 'report_error' => true),
        'queued-reactivate' => array('new_status' => 'queued-activate', 'report_error' => true),
        'queued-deactivate' => array('new_status' => 'queued-activate', 'report_error' => false),
        'deactive'          => array('new_status' => 'queued-activate', 'report_error' => false),
        'queued-destroy'    => array('new_status' => 'destroyed', 'report_error' => false),
        'destroyed'         => array('new_status' => 'destroyed', 'report_error' => false),
    );
    // Check IP configuration on each dialup group is OK
    foreach ($arrSubscriptionsToAdd AS $intIndex => $arrRadiusConfig) {
        $strError = "";
        $strMessage = "Warning: Dialup/IP mis-configuration";

        $arrIPConfiguration = radius_dialup_group_specific_ip_address_get($arrService['service_id'], $arrRadiusConfig, $unNumCLIs);

        if ($arrIPConfiguration === false || count($arrIPConfiguration) < 1) {

            // Valid IP settings could not be found for this dialup,

            // Get how to deal with invalid dialup settings for accounts in this state from lookup table
            if (!isset($arrErrorsByAccountStates[$strServiceStatus])) {
                $strError = "ERROR in " . __FILE__ . "@" . __LINE__ . " Service ID '{$arrService['service_id']}'  has account status '$strServiceStatus'. The dialup configurator does not recognised this account status and cannot handle the refresh of invalid component {$arrRadiusConfig['component_id']} on this account defaulting to 'invalid' status\n";

                $strMessage = "Error: Dialup/IP mis-configuration";
                $arrAction = $arrErrorsByAccountStates['invalid'];
            } else {
                $arrAction = $arrErrorsByAccountStates[$strServiceStatus];
            }

            // Mark dialup group so that access is removed
            unset($arrSubscriptionsToAdd[$intIndex]);
            if (isset($arrExcludeSubscriptionsFromDelete[$arrRadiusConfig['dialup_group_id']])) {
                unset($arrExcludeSubscriptionsFromDelete[$arrRadiusConfig['dialup_group_id']]);
            }

            $arrComponentsToDeactivate[] = array(
                'component_id'            => $arrRadiusConfig['component_id'],
                'new_status'              => $arrAction['new_status'],
                'dialup_group_id'         => $arrRadiusConfig['dialup_group_id'],
                'subscription_required'   => $arrRadiusConfig['subscription_required'],
                'inital_standard_seconds' => $arrRadiusConfig['standard_seconds_per_month'],
            );

            // Signal IP misconfiguration
            if (!$arrAction['report_error']) {
                continue;
            }

            $ReturnValue = -4;

            $arrRadiusConfig['staticip_allowed'] = str_replace('_', ' ', $arrRadiusConfig['staticip_allowed']);

            $strCompatable = '';
            if ($arrRadiusConfig['staticip_allowed'] != 'forbidden') {
                $strCompatable = '(from a compatible pool) ';
            }

            // Attempt to ticket the effected acount (if tickets library included)
            // and mail the problem co-ordinator. Really this code should auto
            // raise a problem but because it's in a configurator I can't be i
            // sure program-tool-access will be included.
            $strMail = " ERROR: in Dialup configurator, Service_id {$arrService['service_id']} has bad Dialup/IP configuration for component '{$arrRadiusConfig['name']}' ( component_id '{$arrRadiusConfig['component_id']}')  using dialup_group_id '{$arrRadiusConfig['dialup_group_id']}'. Static IP's are '{$arrRadiusConfig['staticip_allowed']}' with this component. Because the correct IP setting cannot be determined this component will be deactivated.";

            $strTicket = "The dialup configurator has been unable to activate this customers '{$arrRadiusConfig['name']}' component because the IP address settings on this account are incompatible with this kind of dialup. Static IP's $strCompatable are '{$arrRadiusConfig['staticip_allowed']}' with this component.\n\n Once the IP address configuration has been corrected, activation of this component can be completed by refreshing it, until then this dialup has been disabled. ";
            $intTicketID = 0;

            if (function_exists('tickets_ticket_add')) {
                $intTicketID = tickets_ticket_add('Script', $arrService['service_id'], 0, 0, 'Open', 0, $strTicket);
                $strMail .= " A Question ($intTicketID) has been added to this account.";
            }

            if ($intTicketID < 1) {
                $strMail .= " No Question could  been added to this account, Please raise a Question to CSC for them to correct this problem.";
                $strMessage = "Error: Dialup/IP mis-configuration";
            }

            continue;

        } // if: This dialup is incompatible with it's current IP Settings

        $arrRadiusConfig['ip_configuration'] = $arrIPConfiguration;
        $arrSubscriptionsToAdd[$intIndex] = $arrRadiusConfig;
    } // for: each dialup configurator to pass

} // func : DialupCheckDialupGroupIPCompatibility

/////////////////////////////////////////////////////////////
// Function:  config_staticip_get
// Purpose:   Get a static ip config record
// Arguments: $staticip_id
// Returns:   Static ip configuration record
/////////////////////////////////////////////////////////////

function config_staticip_get($staticip_id)
{

    $connection = get_named_connection('userdata');

    $query = "SELECT si.staticip_id,
                     si.component_id,
                     iz.vchZone AS zone,
                     INET_NTOA(si.ip_address) AS ip_address,
                     INET_NTOA(si.ip_netmask) AS ip_netmask,
                     si.available,
                     si.note,
                     si.reason_activated,
                     si.reverse_dns,
                     si.timestamp,
                     cic.radius_base_flags,
                     cic.bolDuringRestrictionsOnly
              FROM userdata.config_staticip si
              INNER JOIN userdata.components c
                 ON c.config_id = si.staticip_id
              INNER JOIN products.component_ipzone_config cic
                 ON cic.service_component_id = c.component_type_id
              INNER JOIN products.tblIpZone iz
                 ON iz.intZoneId = cic.intZoneId
              WHERE si.staticip_id = %d
                AND c.status = 'active'";

    $query = sprintf($query, mysql_real_escape_string($staticip_id));
    $result = mysql_query($query, $connection)
    or report_error(__FILE__, __LINE__, mysql_error());

    $row = mysql_fetch_array($result, MYSQL_ASSOC);

    mysql_free_result($result);

    return $row;

} // function config_staticip_get


/////////////////////////////////////////////////////////////
//
// CLI Numbers (public)


/////////////////////////////////////////////////////////////
// Function:  config_cli_set_primary
// Purpose:   Set a service's primary CLI number
// Arguments: $service_id (The service ID)
//            $cli_number (CLI Number that needs to be the Primary
/////////////////////////////////////////////////////////////

function config_cli_set_primary($service_id, $cli_number)
{
    $connection = get_named_connection('userdata');

    $service_id = addslashes($service_id);
    $cli_number = addslashes($cli_number);

    $existing_cli = config_cli_number_exists($service_id, $cli_number);

    if ($existing_cli != 0) {
        // It is, so make sure it's the primary
        $service_cli_number_id = $existing_cli['service_cli_number_id'];
        $is_primary = $existing_cli['is_primary_cli'];

        if ($is_primary != 'Y') {
            // Set all as not primary first
            config_cli_clear_primary($service_id);

            config_cli_update_id_primary_cli($service_cli_number_id, 'Y');
        } else {
            // CLI Is already the primary
            // Fall through to the end to set it in the services table
        }
    } else {
        // Set all as not primary first
        config_cli_clear_primary($service_id);

        // Add the primary to the cli table
        config_cli_insert($service_id, $cli_number, 'Y');
    }

    // Whatever happened before, we need to set the primary in the services table
    userdata_service_set_cli($service_id, $cli_number);

    // Update RADIUS
    config_dialup_set_all_radius_data($service_id);

} // function config_cli_set_primary


/////////////////////////////////////////////////////////////
// Function:  config_cli_add
// Purpose:   Set a service's primary CLI number
// Arguments: $service_id (The service ID)
//            $cli_number (CLI Number to add)
//            $is_primary (Whether the new number should be the primary)
// Returns  : The ID of the new CLI number, or its existing ID
//            if it already exists, or 0 if a problem occurred
/////////////////////////////////////////////////////////////

function config_cli_add($service_id, $cli_number, $is_primary_cli)
{
    $connection = get_named_connection('userdata');

    $service_id = addslashes($service_id);
    $cli_number = addslashes($cli_number);
    $is_primary_cli = addslashes($is_primary_cli);

    $service = userdata_service_get($service_id);

    if (!preg_match('/^[NY]$/', $is_primary_cli)) {
        report_error("Invalid parameter", __FILE__, __LINE__);
    }

    $cli_count = config_cli_count_clis($service_id);

    if ($cli_count == 0) {
        // This is the user's first CLI number. (He didn't have one before)
        // Add it . No need to muck with Static IPs. Ignore the $is_primary parameter.
        // It's the only one, so it has to be primary anyway.

        $new_cli_id = config_cli_insert($service_id, $cli_number, 'Y');

        // Set the CLI in the services table
        userdata_service_set_cli($service_id, $cli_number);

        // All done!
        return $new_cli_id;
    } else {
        // Need to add the new cli to the system.
        // See if it's already in the system
        $cli_exists = config_cli_number_exists($service_id, $cli_number);

        if ($cli_exists == 0) {
            // It's not already in the system. Add it

            // Clear the primary if the new one is going to be Primary
            if ($is_primary_cli == 'Y') {
                config_cli_clear_primary($service_id);
                // update services (it's the new primary)
                userdata_service_set_cli($service_id, $cli_number);

            }

            // Add the new one
            $return_id = config_cli_insert($service_id, $cli_number, $is_primary_cli);
        } else {
            // It is, so just set it as primary if we need to
            if ($cli_exists['is_primary_cli'] == 'N') {
                $return_id = $cli_exists['service_cli_number_id'];

                // Clear the current primary
                config_cli_clear_primary($service_id);

                // Set the CLI as Primary
                config_cli_update_id_primary_cli($return_id, $is_primary_cli);

                // Set the Primary CLI in the services table
                userdata_service_set_cli($service_id, $cli_number);
            }
        }
    }

    // OK, CLIs are up to date. Do we need to add a static IP?
    // We may do if the customer has more than 1 CLI and he's using a FRIACO account type
    if (config_cli_count_clis($service_id) > 1
        && product_service_requires_has($service['type'], 'friaco')
    ) {
        // We need to add an invisible static ip to this account, if one isn't already there
        $ip_status = config_staticip_activation_status($service_id);

        if ($ip_status == -1) {
            // FIXME: Catch special case: the user has more than one static ip component.
            error_log("config_cli_add: Service $service_id has more than one static IP component");
        } elseif ($ip_status == 0) {
            // The account doesn't actually have a staticip component.
            // Remove the extra CLI numbers: the user CANNOT have them unless a staticip is attached to the account
            config_cli_number_delete_all($service_id, false);

            $return_id = 0;
        } else {
            $activate_staticip = false;

            // The account has one staticip component. Deal with it.
            switch ($ip_status['reason_activated']) {
                case 'not_active':
                    // The component needs to be activated
                    $activate_staticip = true;
                    break;

                case 'cli':
                case 'request':
                    // Check that the component really is active
                    if ($ip_status['status'] == 'unconfigured') {
                        // The component needs to be activated
                        $activate_staticip = true;
                    } else {
                        // Account already has a valid staticip component. Nothing more to do here!
                    }
                    break;

                default:
                    // Ummmmm..........
                    $return_id = 0;
            }

            if ($activate_staticip) {
                // We do need to activate the staticIP component, so activate it with 'cli' as the reason.
                if (!config_staticip_manual_configure($ip_status['component_id'], 'cli')) {
                    // The configuration of the staticip component failed for some reason
                    // Remove the extra CLI numbers: the use CANNOT have them unless a staticip is attached to the account
                    config_cli_number_delete_all($service_id, false);

                    $return_id = 0;
                }
            }
        }
    }

    // Update RADIUS
    config_dialup_set_all_radius_data($service_id);

    return $return_id;

} // function config_cli_add

/////////////////////////////////////////////////////////////
// Function:   get_cli_config_expiry_days
// Purpose:    Get the number of days that a cli number will
//             expire after if it is unused from kirin_config
//             database.
// Arguments:  None
// Returns:    number of days inactive until expiry
/////////////////////////////////////////////////////////////
function get_cli_config_expiry_days()
{

    $connection = get_named_connection('kirin_config');
    $q = "SELECT config_value FROM kirin_config WHERE config_name = 'cli_expiry_days'";
    $result = mysql_query($q, $connection);
    if (mysql_num_rows($result) > 0) {
        while ($rows = mysql_fetch_array($result)) {
            $days = $rows['config_value'];
        }
    }

    return ($days);
}

////////////////////////////////////////////////////////////
// Function: change_cli_config_expiry_days
// Purpose:  Updates the database to have the passed value
//           in the kirin_config database;
// Arguments: days - new value for days until expire
// returns:  true / false
////////////////////////////////////////////////////////////

function change_cli_config_expiry_days($days)
{
    $days = addslashes($days);
    $connection = get_named_connection('kirin_config');
    $result = mysql_query("UPDATE kirin_config
                        SET config_value = '$days'
                        WHERE config_name = 'cli_expiry_days'"); // or die(mysql_error());

    return ($result);
}

/////////////////////////////////////////////////////////////
// Function:  config_cli_remove
// Purpose:   Remove a CLI number from a service
//            Fails if CLI is primary, or is the last one
// Arguments: $service_id (The Service the CLI is attached to)
//            $service_cli_number_id (The CLI ID)
// Returns  : 1 for success, 0 for failure
/////////////////////////////////////////////////////////////

function config_cli_remove($service_id, $service_cli_number_id, $tidy = false)
{
    $connection = get_named_connection('userdata');

    $service_cli_number_id = addslashes($service_cli_number_id);

    // Is this the last CLI number?
    $cli_count = config_cli_count_clis($service_id);

    switch ($cli_count) {
        case 0:
            // Can't delete if he ain't got any!
            return 0;

        case 1:
            // Can't delete the only CLI number!
            return 0;
    }

    // Check if the CLI number is the primary
    $cli = config_cli_get($service_cli_number_id);

    if ($cli['is_primary_cli'] == 'Y') {
        // Can't delete the primary
        return 0;
    }

    // All checks passed: zap the cli!
    if ($tidy) {
        //this function will not delete any created before $lastmod
        $days2delete = get_cli_config_expiry_days(); //gets amount of days to expiry of a cli (if unused)

        //create the timestamp for this date
        $lastmod = date("YmdHis", mktime(date("H"), date("i"), date("s"), date("m"), date("d") - $days2delete, date("Y")));

        //remove any entries that are outside the expiry time that have not
        //meen used and where created before this expiry time beguins
        $remove = config_cli_number_delete_tidy($service_cli_number_id, $lastmod);
        if (!$remove) {
            return (0);
        }
    } else {
        //proceed as normal
        config_cli_number_delete($service_cli_number_id);
    }

    // Everyting's done. Can we remove the dude's static ip?
    if (config_cli_count_clis($service_id) <= 1) {
        // We may be able to revoke this static ip component.
        // Let's have a look...
        $ip_status = config_staticip_activation_status($service_id);

        if ($ip_status['reason_activated'] == 'cli') {
            // We can revoke the staticip
            config_staticip_manual_deconfigure($ip_status['component_id']);
        } else {
            // The user has actually requested a staticip, so we can't revoke it.
            // Do nothing
        }
    }
    // Update RADIUS
    config_dialup_set_all_radius_data($service_id);

    return 1;

} // function config_cli_remove


/////////////////////////////////////////////////////////////
// Function:  config_cli_count_clis
// Purpose:   Count the number of CLIs attached to a service
// Arguments: $service_id (The service ID)
// Returns  : The number of CLI numbers attached to the service
/////////////////////////////////////////////////////////////

function config_cli_count_clis($service_id)
{
    $connection = get_named_connection('userdata');

    $service_id = addslashes($service_id);

    $count_query = "SELECT COUNT(*)
                    FROM service_cli_numbers
                    WHERE service_id='$service_id'";

    $count_result = mysql_query($count_query, $connection);

    return mysql_result($count_result, 0, 0);

} // function config_cli_count_clis


/////////////////////////////////////////////////////////////
// Function:  config_cli_get_all
// Purpose:   Get all cli numbers attached to the service
// Arguments: $service_id (The service ID)
//          : $include_primary (True if the primary CLI should be included in the array)
// Returns  : An array of CLI numbers
/////////////////////////////////////////////////////////////

function config_cli_get_all($service_id, $include_primary = 1)
{
    $service_id = addslashes($service_id);

    $connection = get_named_connection('userdata');

    $query = "SELECT service_cli_number_id, cli_number, is_primary_cli
            FROM service_cli_numbers
            WHERE cli_number is not null AND cli_number !=''
            AND service_id='$service_id'";

    if (!$include_primary) {
        $query .= " AND is_primary_cli='N'";
    }

    $result = mysql_query($query, $connection);

    $array_clis = array();

    while ($cli = mysql_fetch_array($result, MYSQL_ASSOC)) {
        $array_clis[] = $cli;
    }

    return $array_clis;

} // function config_cli_get_all


/////////////////////////////////////////////////////////////
// Function:  config_cli_get
// Purpose:   Get a cli number record
// Arguments: $service_cli_number_id (The CLI ID)
// Returns  : CLI number record
/////////////////////////////////////////////////////////////

function config_cli_get($service_cli_number_id)
{
    $service_cli_number_id = addslashes($service_cli_number_id);

    $connection = get_named_connection('userdata');

    $query = "SELECT service_id, cli_number, is_primary_cli
                FROM service_cli_numbers
            WHERE service_cli_number_id='$service_cli_number_id'";

    $result = mysql_query($query, $connection);

    $cli = mysql_fetch_array($result, MYSQL_ASSOC);

    return $cli;

} // function config_cli_get


// CLI Numbers (public)
//
/////////////////////////////////////////////////////////////
//
// CLI Numbers (private)
// Don't use these functions. Internal to the public functions above.


/////////////////////////////////////////////////////////////
// Function:  config_cli_insert
// Purpose:   Insert a row into the services_cli_numbers table
// Arguments: $service_id (The service ID)
//            $cli_number (CLI Number to add)
//            $is_primary (Whether the new number should be the primary)
// Returns  : The ID of the new CLI number
/////////////////////////////////////////////////////////////

function config_cli_insert($service_id, $cli_number, $is_primary)
{
    $connection = get_named_connection('userdata');

    $service_id = addslashes($service_id);
    $cli_number = addslashes($cli_number);
    $is_primary = addslashes($is_primary);

    // Strip spaces out of the CLI number
    $cli_number = str_replace(' ', '', $cli_number);

    $insert_query = "INSERT INTO service_cli_numbers (
                                service_id, cli_number, is_primary_cli )
                        VALUES (
                                '$service_id', '$cli_number', '$is_primary' )";

    mysql_query($insert_query, $connection);

    return mysql_insert_id($connection);

} // function config_cli_insert


/////////////////////////////////////////////////////////////
// Function:  config_cli_number_exists
// Purpose:   Find out if a CLI number exists on a service
// Arguments: $service_id (The service ID)
//            $cli_number (CLI Number to add)
// Returns  : 0 if the number doesn't exist, record of the cli if it does.
/////////////////////////////////////////////////////////////

function config_cli_number_exists($service_id, $cli_number)
{
    $connection = get_named_connection('userdata');

    $service_id = addslashes($service_id);
    $cli_number = addslashes($cli_number);

    $query = "SELECT service_cli_number_id, is_primary_cli
                FROM service_cli_numbers
            WHERE service_id='$service_id'
                AND cli_number='$cli_number'";

    $result = mysql_query($query, $connection);

    if (mysql_num_rows($result) == 0) {
        return 0;
    } else {
        $cli = mysql_fetch_array($result, MYSQL_ASSOC);

        return $cli;
    }

} // function config_cli_number_exists


/////////////////////////////////////////////////////////////
// Function:  config_cli_clear_primary
// Purpose:   Set is_primary_cli to 'N' for all clis belonging to a service
// Arguments: $service_id (The service ID)
/////////////////////////////////////////////////////////////

function config_cli_clear_primary($service_id)
{
    $connection = get_named_connection('userdata');

    $service_id = addslashes($service_id);

    $query = "UPDATE service_cli_numbers
                SET is_primary_cli='N'
            WHERE service_id='$service_id'";

    mysql_query($query, $connection);

} // function config_cli_clear_primary


function config_cli_number_delete_tidy($service_cli_number_id, $lastmod)
{
    $connection = get_named_connection('userdata');

    $service_cli_number_id = addslashes($service_cli_number_id);
    $lastmod = addslashes($lastmod);

    $query = "DELETE FROM service_cli_numbers
                    WHERE service_cli_number_id='$service_cli_number_id'
                    AND timestamp < '$lastmod'";

    mysql_query($query, $connection);

    $affect = mysql_affected_rows();
    if ($affect > 0) {
        return (1);
    } else {
        return (0);
    }
} // function config_cli_number_delete


/////////////////////////////////////////////////////////////
// Function:  config_cli_update_id_primary_cli
// Purpose:   Set is_primary_cli for a specific CLI number
// Arguments: $service_cli_number_id (The CLI ID)
//            $is_primary_cli        ('Y' or 'N')
/////////////////////////////////////////////////////////////

function config_cli_update_id_primary_cli($service_cli_number_id, $is_primary_cli)
{
    $connection = get_named_connection('userdata');

    $service_cli_number_id = addslashes($service_cli_number_id);
    $is_primary_cli = addslashes($is_primary_cli);

    $query = "UPDATE service_cli_numbers
                SET is_primary_cli='$is_primary_cli'
            WHERE service_cli_number_id='$service_cli_number_id'";

    mysql_query($query, $connection);

} // function config_cli_update_id_primary_cli


/////////////////////////////////////////////////////////////
// Function:  config_cli_number_delete
// Purpose:   Deletes a CLI number from the database
// Arguments: $service_cli_number_id (The CLI ID)
/////////////////////////////////////////////////////////////

function config_cli_number_delete($service_cli_number_id)
{
    $connection = get_named_connection('userdata');

    $service_cli_number_id = addslashes($service_cli_number_id);

    $query = "DELETE FROM service_cli_numbers
                    WHERE service_cli_number_id='$service_cli_number_id'";

    mysql_query($query, $connection);


} // function config_cli_number_delete


/////////////////////////////////////////////////////////////
// Function:  config_cli_number_delete
// Purpose:   Deletes a CLI number from the database
// Arguments: $service_cli_number_id (The CLI ID)
/////////////////////////////////////////////////////////////

function config_cli_number_delete_all($service_id, $include_primary = true)
{
    $connection = get_named_connection('userdata');

    $service_id = addslashes($service_id);

    $query = "DELETE FROM service_cli_numbers
                    WHERE service_id='$service_id'";

    if (!$include_primary) {
        $query .= ' AND is_primary_cli="N"';
    }
    mysql_query($query, $connection);

} // function config_cli_number_delete


// CLI Numbers (private)
//
/////////////////////////////////////////////////////////////


/////////////////////////////////////////////////////////////
// Function : config_staticip_activation_status
// Purpose  : Returns the activation status of a staticip component
//            attached to the account.
// Arguments: $service_id
// Returns  : Activation status
//            0 if no staticIP component was found
//            -1 if more than one static IP component was found.
/////////////////////////////////////////////////////////////

function config_staticip_activation_status($service_id, $dialup_group_id)
{
    $connection = get_named_connection('userdata');

    $service_id = addslashes($service_id);

    // First, look for an active staticip component
    $component_query = "SELECT component_id, status, config_id
                        FROM components, " . primitives_named_db_prefix('userdata', 'product') . "component_ipzone_config ip
                        WHERE service_id='$service_id'
                AND ip.service_component_id = components.component_type_id
                        AND status IN ('active', 'queued-activate', 'unconfigured', 'queued-reactivate')";

    $component_result = mysql_query($component_query, $connection);

    if (mysql_num_rows($component_result) > 1) {
        // Something ain't right! No-one should have more than one staticip component!
        return -1;
    }

    if (mysql_num_rows($component_result) == 0) {
        // No static IP component on the account!
        return 0;
    }

    $component = mysql_fetch_array($component_result, MYSQL_ASSOC);
    $component_id = $component['component_id'];

    if ($component['status'] == 'unconfigured') {
        // Component is not active. Knock up a return array
        $row = array(
            'component_id'     => $component_id,
            'staticip_id'      => -1,
            'reason_activated' => 'not_active'
        );
    } else {
        // There is a component, find out its activation
        $config_query = "SELECT component_id, staticip_id, reason_activated
                        FROM config_staticip
                        WHERE component_id='$component_id'";

        $config_result = mysql_query($config_query, $connection);

        $row = mysql_fetch_array($config_result, MYSQL_ASSOC);
    }

    return $row;
}


/////////////////////////////////////////////////////////////
// Function:  config_staticip_set_activation_reason
// Purpose:
// Arguments: $component_id
/////////////////////////////////////////////////////////////


function config_staticip_set_activation_reason($component_id, $new_reason)
{
    global $staticip_activation_reasons;

    // Make sure the new reason is valid
    switch ($new_reason) {
        case 'cli':
        case 'request':
        case 'not_active':
            break;

        default:
            return false;
    }

    $component = userdata_component_get($component_id);
    $staticip_id = $component['config_id'];

    $connection = get_named_connection('userdata');

    $description = addslashes($staticip_activation_reasons[$new_reason]);

    // Update the reason
    $update = "UPDATE config_staticip
                SET reason_activated='$new_reason'
                WHERE staticip_id='$staticip_id'";

    mysql_query($update, $connection);

    // Update the description
    $update = "UPDATE components
                SET description='$description'
                WHERE component_id='$component_id'";

    mysql_query($update, $connection);

    return true;
}


/**
 * config_staticip_manual_configure
 *
 * @param int    $component_id
 * @param string $reason_activated
 * @param bool   $bolContactRadius
 *
 * @return int
 */
function config_staticip_manual_configure($component_id, $reason_activated = 'request', $bolContactRadius = true)
{
    global $staticip_activation_reasons;

    if (empty($component_id) OR
        (!is_int($component_id) AND !ctype_digit($component_id))
    ) {

        return 0;
    }

    $component = userdata_component_get($component_id);

    if (empty($component)) {
        return 0;
    }

    $service_id = $component['service_id'];
    $component_type_id = $component["component_type_id"];
    $parameters = config_staticip_get_ipzone($component_type_id);

    $do_configure = 0;

    switch ($component["status"]) {
        case 'unconfigured':
            // Mark component
            userdata_component_set_status($component_id, "queued-activate");
            $do_configure = 1;
            break;

        case 'active':
            return 1;
            break;

        default:
            $do_configure = 0;
            break;
    }

    if (!$do_configure) {
        return 0;
    }

    // Check for configuration attempts on dynamic IP's which aren't in failed_billing
    if (isset($parameters['bolDuringRestrictionsOnly']) && $parameters['bolDuringRestrictionsOnly']) {

        if (!bolFinancialFailedBilling($component['service_id'])) {
            userdata_component_set_status($component_id, "unconfigured");

            return -15;
        }

        // force activation reason
        $reason_activated = 'failed_billing';
    }

    // Allocate the static IP {block}
    $staticip_id = config_staticip_allocate($parameters["zone"], $parameters["netmask"], $component_id, $reason_activated);

    if (!$staticip_id || $staticip_id == -1) {
        userdata_component_set_status($component_id, 'unconfigured');

        return 0;
    }

    // Create a proper description
    $description = $staticip_activation_reasons[$reason_activated];

    // Attach allocation to the static ip component
    userdata_component_set_configuration($component_id, $staticip_id, $description);

    // Get configuration info about this user
    $arrVispDataForUser = product_users_visp_configuration_get($service_id);

    // get default reverse DNS
    $strReverseDns = strstr($arrVispDataForUser['default_customer_email_address'], '@');

    // remove the initial @ sign
    $strReverseDns = substr($strReverseDns, 1);

    // MAAF: customers don't follow PN email scheme. (missing username in domain part in email's eg.: <EMAIL> or external mail)
    $strBaseVispDomainName = str_replace('www.', '', $arrVispDataForUser['url_domain']);

    if ($strReverseDns == $strBaseVispDomainName || false === strpos($strReverseDns, $strBaseVispDomainName)) {

        $arrService = userdata_service_get($service_id);
        $strReverseDns = $arrService['username'] . '.' . $strBaseVispDomainName;
    }

    // update reverse dns
    ConfigStaticIPSetReverseDns($staticip_id, $strReverseDns);

    // Check for other active ip components and destroy them (can only have one active static ip component)
    $arrStaticIPComponentTypes = product_staticip_component_types_get();

    $arrStaticIPComponentTypeIDs = array();

    if (isset($arrStaticIPComponentTypes) && sizeof($arrStaticIPComponentTypes) > 0) {
        foreach ($arrStaticIPComponentTypes as $arrStaticIPComponentType) {
            $arrStaticIPComponentTypeIDs[] = $arrStaticIPComponentType['service_component_id'];
        }

        unset($arrStaticIPComponentTypes);

        $arrCriteria = array(
            'type'       => $arrStaticIPComponentTypeIDs,
            'status'     => array('active'),
            'service_id' => $service_id
        );

        $arrStaticIPComponents = userdata_component_find($arrCriteria, 0, 0);

        unset($arrStaticIPComponentTypeIDs);

        if (isset($arrStaticIPComponents) && sizeof($arrStaticIPComponents) > 0) {
            foreach ($arrStaticIPComponents as $arrStaticIPComponent) {
                // Destroy static ip component without refreshing dialup component
                config_staticip_auto_destroy($arrStaticIPComponent['component_id'], false);
            }

            unset($arrStaticIPComponents);

        } // end if

    } // end if

    // Set static ip component active
    userdata_component_set_status($component_id, "active");

    // Update RADIUS
    if ($bolContactRadius) {
        $radius_id = config_dialup_set_all_radius_data($service_id);
        if ($radius_id == -1) {
            // Failure contacting the radius database, ip_config
            // should be queued
            userdata_component_set_status($component_id, "queued-activate");
        }
    }

    return $staticip_id;

} // function config_staticip_manual_configure


/////////////////////////////////////////////////////////////
// Function:  config_staticip_manual_deconfigure
// Purpose:
// Arguments: $component_id
/////////////////////////////////////////////////////////////


function config_staticip_manual_deconfigure($component_id, $bolContactRadius = true)
{
    // Get component
    $component = userdata_component_get($component_id);
    $service_id = $component['service_id'];

    // First see if the account has multiple CLIs
    // problem 33782 fix - additional condition added
    if ((hasAnyMultiCLIsComponent($service_id)) && (config_cli_count_clis($service_id) > 1)) {
        // Go no further!
        return;
    }

    switch ($component["status"]) {
        case "active":
        case "deactive":
        case "queued-reactivate":
        case "queued-deactivate":
            // Mark component
            userdata_component_set_status($component_id, "queued-deconfigure");
            $do_deconfigure = 1;
            break;

        default:
            $do_deconfigure = 0;
            break;
    }

    if (!$do_deconfigure) {
        return;
    }

    // reset reverse dns
    ConfigStaticIPSetReverseDns($component['config_id'], 'unassigned.plus.net.uk');

    // Release allocation
    config_staticip_release($component["config_id"], $component["component_id"]);
    // Update RADIUS
    if ($bolContactRadius) {
        $radius_id = config_dialup_set_all_radius_data($service_id);

        if (($radius_id < 0) && ($radius_id != -4)) {
            // Failure contacting the radius database, ip_config
            // should be queued
            return;
        }
    }

    // Unconfigure the component
    userdata_component_set_status($component_id, "unconfigured");

} // function config_staticip_manual_deconfigure

/////////////////////////////////////////////////////////////
// Function: hasAnyMultiCLIsComponent
// Purpose:  check if components which required multi CLIs are "active" on the account
// Arguments: $intServiceID
/////////////////////////////////////////////////////////////

function hasAnyMultiCLIsComponent($intServiceID)
{
    if (empty($intServiceID)) {
        return false;
    }

    $dbhConn = get_named_connection_with_db('userdata_reporting');

    $strQuery = 'SELECT count(*) AS intCount ' .
        'FROM userdata.components c ' .
        'INNER JOIN products.component_radius_config crc ' .
        'ON crc.service_component_id = c.component_type_id ' .
        'WHERE crc.staticip_allowed = "required with multiple_clis" ' .
        'AND c.status IN ("queued-activate","queued-reactivate","active") ' .
        'AND c.service_id = "' . $intServiceID . '" ';

    $resResult = PrimitivesQueryOrExit($strQuery, $dbhConn);
    $intCount = PrimitivesResultGet($resResult, 'intCount');

    if (!empty($intCount)) {
        return true;
    }

    return false;
}

function config_staticip_release($staticip_id, $component_id_attached_to)
{
    $connection = get_named_connection("userdata");

    // Check against the component_id it's currently attached to in case
    // actually released previously but left 'queued-X' because the
    // dialup components could not be updated

    $staticip_id = addslashes($staticip_id);
    $component_id_attached_to = addslashes($component_id_attached_to);

    // Remove any service_specific IP configs
    $query = "UPDATE config_staticip
                SET available = 'yes',
                    component_id = NULL,
                    reason_activated='not_active'
            WHERE staticip_id = '$staticip_id'
                AND component_id='$component_id_attached_to'";

    mysql_query($query, $connection);

    if (mysql_affected_rows($connection) > 0) {
        // An IP was actually relaesed, remove and related dialup specific IP  configurations
        $query = "DELETE FROM staticip_dialup_configuration WHERE staticip_id='$staticip_id'";
        @mysql_query($query, $connection);

        if (mysql_errno($connection)) {
            error_log("Radius access error @ " . __LINE__ . ": " . mysql_error(), 0);

            return false;
        }
    }

    // Update the components table to clear the data out
    $update = "UPDATE components
                SET config_id=-1, description=''
                WHERE component_id='$component_id_attached_to'";

    mysql_query($update, $connection);

} // function config_staticip_release


/**
 * This is cleanup function that should be use by config_staticip_allocate only
 * Its purpose is to make sure, that all static IPs will be release if allocation process fails.
 *
 * @access private
 *
 * @param integer $intComponentID
 * @param integer $intStaticIPID
 */
function releaseNotNeededStaticIPs($intComponentID, $intStaticIPID = null)
{

    $strQuery = "UPDATE config_staticip SET
                        component_id = NULL,
                        available = 'yes'
                        WHERE component_id = $intComponentID";
    if (isset($intStaticIPID)) {
        $strQuery .= " AND staticip_id != $intStaticIPID";
    }

    $dbhConnection = get_named_connection_with_db('userdata');
    if (!PrimitivesQueryOrExit($strQuery, $dbhConnection, 'Allocate static IP', false)) {
        $strProblemText = "Query failed - see error log for info.  Query was:\n\n$strQuery\n";
        pt_raise_autoproblem('config_staticip_allocate', 'Problem allocating static IP: releaseNotNeededStaticIPs', $strProblemText, $strProblemText);
    }

} // end of function unallocateStaticIPsFromComponent()

/**
 * Allocates an IP to a component
 *
 * Finds the next available IP and marks it as being used by the specified component ID
 *
 * @access public
 *
 * @param  string  $strZone             The zone to get the IP from - see table products.component_ipzone_config
 * @param  string  $strNetmask          The netmask of the IP to get
 * @param  integer $intComponentID      The component to assign the IP to
 * @param  string  $strActivationReason Reason why the IP was activated: currently 'cli' or 'request'
 */
function config_staticip_allocate($strZone, $strNetmask, $intComponentID, $strActivationReason)
{
    // check for blank parameters
    if (($strZone == '')
        || is_null($strZone)
        || ($strNetmask == '')
        || is_null($strNetmask)
        || !preg_match('/^[0-9]+$/', $intComponentID)
        || ($intComponentID == 0)
    ) {
        return false;
    }


    $dbhConnection = get_named_connection_with_db('userdata');

    $strZone = addslashes($strZone);
    $strNetmask = addslashes($strNetmask);
    $intComponentID = addslashes($intComponentID);
    $strActivationReason = addslashes($strActivationReason);
    $intStaticIPID = 0;

    while (!$intStaticIPID) {
        // Step.1. lock the table for one IP
        $intTimeout = 10; //in seconds
        $bolIsLocked = primitives_get_lock('config_staticip_allocate', $dbhConnection, $intTimeout);


        // error handling
        if (!$bolIsLocked) {
            $strProblemText = "Locking table in userdata connection failed. With string: [config_staticip_allocate] ";
            $strReason = "Table lock failed.";
            config_staticip_allocate_failure($strProblemText, $intComponentID, $bolIsLocked, $dbhConnection, $strReason);

            return false;
        }

        // Step.2. SELECT the staticip_id of one of the available static ips for the zone
        $strSelectQuery = "SELECT MIN(si.staticip_id) AS staticip_id
                           FROM userdata.config_staticip si
                           INNER JOIN products.tblIpZone iz
                              ON iz.intZoneId = si.intZoneId
                           WHERE iz.vchZone = '$strZone'
                           AND si.ip_netmask = INET_ATON('$strNetmask')
                           AND si.available = 'yes'
                           AND si.bolDoNotAssign = 0";
        $resFreeIP = PrimitivesQueryOrExit($strSelectQuery, $dbhConnection, 'Allocate static IP', false);

        $arrIP = PrimitivesResultGet($resFreeIP);

        $intConfigStaticIP = (int)$arrIP['staticip_id'];

        // error handling
        if (!$resFreeIP || ($resFreeIP && 0 == $intConfigStaticIP)) {
            $strProblemText = "Zone $strZone has no IPs or select query failed: see error log for info.  Query was:\n\n$strSelectQuery\n";
            $strReason = "$strZone has no IPs.";
            config_staticip_allocate_failure($strProblemText, $intComponentID, $bolIsLocked, $dbhConnection, $strReason);

            return false;
        }


        // Step.3. update that row
        $strUpdateQuery = "UPDATE config_staticip
                SET
                    available = 'no',
                    component_id = $intComponentID,
                    reason_activated = '$strActivationReason'
                    WHERE staticip_id = '$intConfigStaticIP'
                    ";

        $resUpdate = PrimitivesQueryOrExit($strUpdateQuery, $dbhConnection, 'Allocate static IP', false);

        // error handling
        if (!$resUpdate || ($resUpdate && 1 <> PrimitivesAffectedRowsGet($dbhConnection))) {
            $strProblemText = "Update query failed - see error log for info.  Query was:\n\n$strUpdateQuery\n";
            $strReason = "Update failed.";
            config_staticip_allocate_failure($strProblemText, $intComponentID, $bolIsLocked, $dbhConnection, $strReason);

            return false;
        }

        // Step.4. release the lock
        $bolIsUnlock = primitives_release_lock('config_staticip_allocate', $dbhConnection);

        // error handling
        if (!$bolIsUnlock) {
            $strProblemText = "Unlocking table in userdata connection failed. With string: [config_staticip_allocate] ";
            $strReason = "Table unlock failed.";
            config_staticip_allocate_failure($strProblemText, $intComponentID, $bolIsLocked, $dbhConnection, $strReason);

            return false;
        } else {
            $bolIsLocked = false;
        }

        $strQuery = "SELECT INET_NTOA(ip_address) AS ip_address
                    FROM config_staticip
                    WHERE staticip_id = $intConfigStaticIP
                    ";

        $res = PrimitivesQueryOrExit($strQuery, $dbhConnection, 'Allocate static IP', false);

        if (!$res) {
            $strProblemText = "Query failed - see error log for info.  Query was:\n\n$strQuery\n";
            $strReason = "Select failed.";
            config_staticip_allocate_failure($strProblemText, $intComponentID, $bolIsLocked, $dbhConnection, $strReason);

            return false;
        }

        $arrIP = PrimitivesResultGet($res);

        if (config_staticip_confirm_available_in_radius($arrIP['ip_address'], true)) {
            $intStaticIPID = $intConfigStaticIP;
        }
    }

    // Me must release all static IPs allocated for this component except for
    // one
    releaseNotNeededStaticIPs($intComponentID, $intStaticIPID);

    return $intStaticIPID;

} // function config_staticip_allocate


/**
 * Rises autoproblem for Allocating IP to a component
 *
 * @access public
 *
 * @param  string  $strProblemText Message to be shown
 * @param  integer $intComponentID The component to assign the IP to
 * @param  bolean  $bolIsLocked    Is the table currently locked?
 * @param  handle  $dbhConnection  Current connection to unlock
 * @param  string  $strReason      Reason of failure inside static IP allocation function
 */
function config_staticip_allocate_failure($strProblemText, $intComponentID, $bolIsLocked, $dbhConnection, $strReason)
{
    if (true === $bolIsLocked) {
        // it's currently locked and there was a problem => we need to unlock it.
        $bolUnlock = primitives_release_lock('config_staticip_allocate', $dbhConnection);
    }

    pt_raise_autoproblem('config_staticip_allocate', 'Problem allocating static IP: ' . $strReason,
        $strProblemText, $strProblemText);

    // Me must release all static IPs allocated for this component
    releaseNotNeededStaticIPs($intComponentID);

} // function config_staticip_allocate_failure

/**
 * Check with RADIUS to see if an IP is already in use
 *
 * Does a lookup in the RADIUS session tables to see if the specified IP is in use. If it is, then optionally queue up
 * the account with that IP for disconnection
 *
 * @access public
 *
 * @param  string  $strIPAddress    IP address to check
 * @param  boolean $bolDoDisconnect Queue up the account with the specified IP for a forced disconnection
 *
 * @return boolean true = IP available, false = IP not available
 * <AUTHOR> Hau <<EMAIL>>
 */
function config_staticip_confirm_available_in_radius($strIPAddress, $bolDoDisconnect = false)
{
    $strIPAddress = addslashes($strIPAddress);

    $dbhConnection = get_named_connection_with_db('radius_reporting', true, true);

    $arrRadiusSessionTables = radius_session_tables_spaning_this_period_get_ContSessionSummary();

    foreach ($arrRadiusSessionTables as $strTableName) {
        $strQuery = 'SELECT radius_id ' .
            "FROM $strTableName " .
            'WHERE ip_address = "' . $strIPAddress . '" ' .
            'AND event = "active" ' .
            'AND last_seen >= DATE_SUB(NOW(), INTERVAL 12 HOUR)';

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

        if ($intRadiusID = PrimitivesResultGet($resResult, 'radius_id')) {
            if ($bolDoDisconnect === true) {
                radius_mark_for_disconnection($intRadiusID);
            }

            return false;
        }

    } // foreach($arrRadiusSessionTables as $strTableName)

    return true;

} // function config_staticip_confirm_available_in_radius()

/////////////////////////////////////////////////////////////
// Function:  config_staticip_get_component_type_parameters
// Purpose:
// Arguments: $component_type_id
// Returns:
/////////////////////////////////////////////////////////////

function config_staticip_get_ipzone($component_type_id)
{
    $connection = get_named_connection('product');

    $query = "SELECT cic.service_component_id, iz.vchZone AS zone, cic.decInitialCostIncVat,
                     iz.intNumberAllocated AS number_allocated,
                     INET_NTOA(4294967296 - iz.intNumberAllocated) AS netmask,
                     cic.radius_base_flags,
                     cic.bolDuringRestrictionsOnly
              FROM component_ipzone_config cic
              INNER JOIN tblIpZone iz ON iz.intZoneId = cic.intZoneId
              WHERE cic.service_component_id = %d";

    $query = sprintf($query, mysql_real_escape_string($component_type_id));
    $result = mysql_query($query, $connection)
    or report_error(__FILE__, __LINE__, mysql_error());

    $ipzone = mysql_fetch_array($result, MYSQL_ASSOC);

    mysql_free_result($result);

    return $ipzone;

} // function config_staticip_get_ipzone


/////////////////////////////////////////////////////////////
// Function    : ConfigStaticIPSetReverseDns
// Description : Sets the field reverse_dns on the config_staticip table
// Arguments   : $intStaticIpId
//               $strReverseDns
// Returns     : $bolSuccess
// Author      : BHau
// Date        : 11/11/2002
/////////////////////////////////////////////////////////////

function ConfigStaticIPSetReverseDns($intStaticIpId, $strReverseDns)
{
    $bolSuccess = false;

    // validate component id
    if (!preg_match('/^[0-9]+$/', $intStaticIpId)) {
        return false;
    }

    // validate reverse dns entry
    if (!preg_match('/^[0-9a-zA-Z.]{1,255}$/', $strReverseDns)) {
        return false;
    }

    // connect to db
    $dbConnection = get_named_connection('userdata');

    // update db
    $strQuery = 'UPDATE config_staticip
                    SET reverse_dns = "' . addslashes($strReverseDns) . '"
                WHERE staticip_id = "' . $intStaticIpId . '"';

    $resResult = mysql_query($strQuery, $dbConnection)
    or report_error(__FILE__, __LINE__, mysql_error($dbConnection));

    // check to see if row successfully updated
    if (mysql_affected_rows($dbConnection) == 1) {
        $bolSuccess = true;
    }

    // return success or not
    return $bolSuccess;

} // function ConfigStaticIPSetReverseDns()

/////////////////////////////////////////////////////////////
// Function:  config_staticip_auto_destroy
// Purpose:
// Arguments: $component_id
/////////////////////////////////////////////////////////////

function config_staticip_auto_destroy($component_id, $bolRefreshDialupComponent = true)
{
    // Get component
    $component = userdata_component_get($component_id);
    $service_id = $component['service_id'];

    switch ($component["status"]) {
        case "active":
        case "deactive":
        case "queued-reactivate":
        case "queued-deactivate":
        case "queued-destroy":
            // Mark component
            userdata_component_set_status($component_id, "queued-destroy");
            $do_destroy = 1;
            break;

        case "unconfigured":
            userdata_component_set_status($component_id, "destroyed");
            $do_destroy = 0;
            break;

        default:
            $do_destroy = 0;
            break;
    }

    if (!$do_destroy) {
        return;
    }

    // Release allocation
    config_staticip_release($component["config_id"], $component_id);


    // Update RADIUS
    if ($bolRefreshDialupComponent) {
        $radius_id = config_dialup_set_all_radius_data($service_id);

        if ($radius_id == -1) {
            // Failure contacting the radius database, ip_config
            // should be queued
            return;
        }
    }

    try {

        // Destroy the Product Components for the component

        $staticIpComponent = new CProduct($component_id);

        if ($staticIpComponent !== false) {

            $arrProductComponentInstances = $staticIpComponent->getProductComponents();

            if (!empty($arrProductComponentInstances)) {

                foreach ($arrProductComponentInstances as $productComponentInstance) {

                    $objPaymentScheduler = new CProductComponentPaymentScheduler(
                        $productComponentInstance->getProductComponentInstanceID(), ''
                    );
                    if ($objPaymentScheduler !== false) {
                        $objPaymentScheduler->cancelAllOutstandingPayments();
                    }

                }
            }
        }

        $productComponent = new ProductComponent_SubComponent();
        $productComponent->destroy($component_id);

    } catch (Exception $exception) {

        // Print the error in error log. Shouldnt be much of a trouble.
        error_log(
            " ERROR in " . __FILE__ . "@" . __LINE__ .
            " Exception caught from ProductComponent_SubComponent::destroy " .
            " message : " . $exception->getMessage() . "| component ID : {$component_id} | Service ID : '{$service_id}'\n"
        );

    }

    // Mortify the component
    userdata_component_set_status($component_id, "destroyed");

} // function config_staticip_auto_destroy

/////////////////////////////////////////////////////////////
// Function:  config_dialup_auto_destroy
// Purpose:
// Arguments:
/////////////////////////////////////////////////////////////


function config_dialup_auto_destroy($component_id)
{
    $component = userdata_component_get($component_id);

    // We allow destruction in any state
    $service_id = $component["service_id"];

    // Set to queued-destroy
    userdata_component_set_status($component_id, 'queued-destroy');

    // If there is no radius component associated with a component
    // is no need to change radius data
    if (product_radius_config_get($component['component_type_id'])) {
        // Update all data
        $radius_id = config_dialup_set_all_radius_data($service_id);

        if (($radius_id < 0) && ($radius_id != -4)) {
            // failure contacting the radius database
            return (false);
        }
    }

    // Set the component status to destroyed.
    userdata_component_set_status($component_id, 'destroyed');
} // function config_dialup_auto_destroy


/////////////////////////////////////////////////////////////
// Function:  config_dialup_auto_enable
// Purpose:
// Arguments:
/////////////////////////////////////////////////////////////

function config_dialup_auto_enable($component_id)
{
    $component = userdata_component_get($component_id);

    // If there is no radius component assosiated with a component
    // Do not autoconfigure dialup component
    if (!product_radius_config_get($component['component_type_id'])) {

        userdata_component_set_status($component_id, 'active');

        return false;
    }

    switch ($component["status"]) {
        case "queued-activate":
        case "queued-reactivate":
        case "queued-deactivate":
        case "deactive":
        case "active":
            // Activate
            $service_id = $component["service_id"];

            // Set to queued-reactivate so that config_dialup_set_all_radius_data
            // knows to include this component
            userdata_component_set_status($component_id, 'queued-reactivate');
            // Update all data
            $radius_id = config_dialup_set_all_radius_data($service_id);
            if ($radius_id < 0) {
                // Failure contacting the radius database
                // or no activatable dialup components
                break;
            }

            // Set the component status to active.

            break;

        case "queued-deconfigure":
        case "queued-destroy":
        case "unconfigured":
        case "destroyed":
            // Leave it
            break;

        default:
            // The sky is falling!
            break;
    }

} // function config_dialup_auto_enable

/////////////////////////////////////////////////////////////
// Function:  config_dialup_auto_enable_all
//
// Purpose:   To activate any (& all) queued-activate
//            dialup components on this account. It is used
//            by ADSL, SurfTime and other services where
//            the account must be active before the dialup
//            can be activated.
//
//            Setting the account to active is a seperate
//            task and must be completed first if required.
//
// Arguments: $service_id
/////////////////////////////////////////////////////////////

function config_dialup_auto_enable_all($service_id)
{

    // get all queued-activate dialup components for this user

    $array_all_radius_configs = product_radius_component_types_get();

    $array_type_ids = array();

    while (list(, $type) = each($array_all_radius_configs)) {
        $array_type_ids[] = $type['service_component_id'];
    }

    $criteria = array(
        'type'       => $array_type_ids,
        'status'     => array('queued-activate', 'queued-reactivate'),
        'service_id' => $service_id
    );

    $array_dialup_components = userdata_component_find($criteria, 0, 0);

    foreach ($array_dialup_components AS $dialup_component) {
        config_dialup_auto_enable($dialup_component['component_id']);
    }

} // func : config_dialup_auto_enable_all


/////////////////////////////////////////////////////////////
// Function:  config_dialup_auto_disable
// Purpose:
// Arguments:
/////////////////////////////////////////////////////////////

function config_dialup_auto_disable($component_id)
{
    $component = userdata_component_get($component_id);

    switch ($component["status"]) {
        case "active":
        case "queued-activate":
        case "queued-reactivate":
        case "queued-deactivate":
        case "deactive":
        case "queued-deconfigure":
            // Deactivate
            $service_id = $component["service_id"];
            $failedBillingManager = BusTier_BusTier::getClient('failed_billing');

            $fbVersionTwoJourney = false;

            if (FeatureToggleManager::isOnFiltered('RBM_MIGRATION_COMPLETE', null, null, null, $service_id)) {
                $fbVersionTwoJourney = Plusnet\FailedPayment\FailedPaymentHandler::isFbVersionTwoJourney($service_id);
            } else {
                $fbVersionTwoJourney = $failedBillingManager->isFbVersionTwoJourney(new PositiveInt($service_id));
            }

            // Set to queued-deactivate so that config_dialup_set_all_radius_data
            // knows not include this component
            userdata_component_set_status($component_id, 'queued-deactivate');
            // Update all data
            if (!$fbVersionTwoJourney) {
                $radius_id = config_dialup_set_all_radius_data($service_id, $component_id);

                if ($radius_id == -1) {
                    // failure contacting the RADIUS database
                    break;
                }

                if ($radius_id == -3) {
                    // This component can't be deativated, set the component status to active.
                    userdata_component_set_status($component_id, 'active');
                } else {
                    // Set the component status to deactive.
                    userdata_component_set_status($component_id, 'deactive');
                }
            }
            break;

        case "queued-destroy":
        case "unconfigured":
        case "destroyed":
            // Leave it
            break;

        default:
            // The sky is falling!
            break;
    }

} // function config_dialup_auto_disable

/////////////////////////////////////////////////////////////
// Function:  config_dialup_auto_refresh
// Purpose:
// Arguments:
/////////////////////////////////////////////////////////////

function config_dialup_auto_refresh($component_id)
{
    $component = userdata_component_get($component_id);

    switch ($component["status"]) {
        // Needs update
        case "active":
        case "queued-reactivate":
        case "queued-activate":
        case "queued-deactivate":
        case "deactive":
            break;

        // Leave alone
        case "queued-deconfigure":
        case "queued-destroy":
        case "unconfigured":
        case "destroyed":
            return;
            break;

        // The sky is falling!
        default:
            return;
            break;
    }

    $service_id = $component["service_id"];

    // Set to queued-reactivate so that config_dialup_set_all_radius_data
    // knows to include this component
    userdata_component_set_status($component_id, 'queued-reactivate');
    // Update all data
    $radius_id = config_dialup_set_all_radius_data($service_id);

} // function config_dialup_auto_refresh

function config_dialup_auto_configure($component_id)
{
    $component = userdata_component_get($component_id);
    $service_id = $component['service_id'];

    // Behaviour based on current component status
    switch ($component["status"]) {
        case "unconfigured":
        case "deactive":
        case "queued-activate":
        case "queued-reactivate":
        case "active":
            // OK
            break;

        case "queued-deactivate":
        case "queued-deconfigure":
        case "queued-destroy":
        case "destroyed":
            // These aren't the droids you're looking for
            return;

        default:
            // The sky is falling!
            break;
    }

    // Set to queued-activate so that config_dialup_set_all_radius_data
    // knows to include this component
    userdata_component_set_status($component_id, 'queued-activate');
    // Update all data

    $radius_id = config_dialup_set_all_radius_data($service_id, true);

    if ($radius_id < 0) {
        // Error contacting RADIUS database or
        // no activatable components
        return;
    }
    // add CLI into services_cli_numbers!!
    $service = userdata_service_get($service_id);
    config_cli_set_primary($service_id, $service['cli_number']);

} // function config_dialup_auto_configure


/////////////////////////////////////////////////////////////
// Function:
// Purpose:
// Arguments:
/////////////////////////////////////////////////////////////

function config_dialup_configurator($component_id, $action)
{

    switch ($action) {
        case "auto_configure":
            config_dialup_auto_configure($component_id);
            break;

        case "auto_disable":
            config_dialup_auto_disable($component_id);
            break;

        case "auto_enable":
            config_dialup_auto_enable($component_id);
            break;

        case "auto_refresh":
            config_dialup_auto_refresh($component_id);
            break;

        case "auto_destroy":
            config_dialup_auto_destroy($component_id);
            break;

        default:
            break;

    }

}

/**
 * Handle legacy component signaling for Internet connection product
 *
 * @param integer $intComponentId Id of component
 * @param string  $strSignal      legacy component signals e.g 'auto_configure' etc
 * @param         array           config-dialup-access.inc additional argument to config internet component
 */
function config_internet_connection_configurator($intComponentId, $strSignal, $additionalArguments = array())
{
    try {

        return CInternetConnectionProduct::handleLegacyComponentSignaling(
            $intComponentId,
            $strSignal,
            $additionalArguments
        );
    } catch (Exception $objException) {

        error_log(__FUNCTION__ . ": catched exception durring sending {$strSignal} to component {$intComponentId}. Catched exception was: {$objException->getMessage()}, traceback was: {$objException->getTraceAsString()}");

        return false;
    }
}

/////////////////////////////////////////////////////////////
// Function:
// Purpose:
// Arguments:
/////////////////////////////////////////////////////////////

function config_staticip_configurator($component_id, $action, $bolRefreshDialupComponent = true)
{

    switch ($action) {

        case "auto_configure":
            // Static IPs don't
            break;

        case "auto_disable":
        case "auto_enable":
        case "auto_refresh":
            // Nothing to do here
            break;

        case "auto_destroy":
            config_staticip_auto_destroy($component_id, $bolRefreshDialupComponent);
            break;

        default:
            break;

    }

} // function config_staticip_configurator

function config_staticip_ip_assigned_for_dialup_options($service_id, &$error_message)
{
    $connection = get_named_connection_with_db('userdata');
    // Has static IP ?

    $query = "SELECT si.staticip_id,
                     iz.intNumberAllocated AS block_size
              FROM userdata.components c
              INNER JOIN userdata.config_staticip si
                 ON si.component_id = c.component_id AND si.staticip_id = c.config_id
              INNER JOIN products.tblIpZone iz ON iz.intZoneId = si.intZoneId
              WHERE c.service_id = '$service_id'
                AND c.status = 'active'
              ORDER BY c.component_id LIMIT 1";
    $result = PrimitivesQueryOrExit($query, $connection, 'List multiple IPs');

    $array_staticip = PrimitivesResultGet($result);
    if (count($array_staticip) == 0) {
        $array_staticip['block_size'] = 0;
    }
    // Has multiple CLI ?
    $query = "SELECT if(count(*) > 1,1,0) as has_multiple_cli " .
        "FROM service_cli_numbers " .
        "WHERE service_id ='$service_id'";

    $result = PrimitivesQueryOrExit($query, $UserdataConnection, 'count multiple clis');
    $has_multiple_cli = PrimitivesResultGet($result, 'has_multiple_cli');

    // Has smtp ?
    $has_smtp = 0;

    if ($array_staticip['block_size'] > 0) {
        $query = "SELECT if(count(*) > 1,1,0) as has_smtp " .
            "FROM " . primitives_named_db_prefix('userdata', 'product') . "component_mailbox_config c,  userdata.components uc, userdata.config_email ue" .
            "WHERE uc.status='active' and " .
            "      uc.component_type_id=c.service_component_id and " .
            "      uc.config_id = ue.email_id  and " .
            "      ue.component_id = uc.component_id and " .
            "      ue.delivery_method='smtp' and " .
            "      uc.service_id = '$service_id' ";

        $result = PrimitivesQueryOrExit($query, $UserdataConnection, 'check for smtp');
        $has_smtp = PrimitivesResultGet($result, 'has_smtp');
    }


    // Get available options

} // func: config_staticip_ip_assigned_for_dialup_options

function config_staticip_set_dialup_option($service_id, $component_id, $ip_option_id, &$error_message)
{
    $error_message = '';

    $connection = get_named_connection_wtih_db('userdata');

    //  Get dialup service allowed settings
    $query = "SELECT dialup_group_id, dynamic_ip_allowed, single_static_ip_allowed, multiple_static_ip_allowed " .
        "FROM " . primitives_named_db_prefix('userdata', 'product') . "component_radius_config,  components " .
        "WHERE component.status IN('active','queued-activate') AND " .
        "      components.component_type_id=component_radius_config.service_component_id";

    $result = PrimitivesQueryOrExit($query, $connection, 'Get Dialup Settings');

    $array_dialup_settings = PrimitivesResultGet($result);

    if (count($array_dialup_settings) == 0) {
        $error_message = "The component is not a dialup component or is not active";

        return (false);
    }

    //  Get IP service toploogy
    //  Count CLIS's
    //  Get customers static IP
    //  Get customers smtp mail server address
    //
    switch ($ip_option_id) {

        case 1:
            // Dynamic
            // Check dialup settings
            if ($array_dialup_settings['dynamic_ip_allowed'] == 'no') {
                $error_message = "Dynamic IP is not allowed on this service";
            }
            if (($array_dialup_settings['dynamic_ip_allowed'] == 'single_cli_only') &&
                ($num_clis > 1)
            ) {
                $error_message = "you cannot use Dynamic IP while you have more " .
                    "than one CLI configured";

                return (false);
            }
            // Do remove
            $query = "REPLACE INTO staticip_dialup_configuration (staticip_id,dialup_group_id,date_added,ip_address,ip_netmask,ip_assigned_option_id) values(-1,{$array_dialup_settings['dialup_group_id']},now(),'pool','***************',1)" .
                PrimitivesQueryOrExit($query, $connection, 'Get Dialup Settings');

            break;
        case 2:
            //  Static IP
            if (count($array_staticip) == 0) {
                $error_message = "You don't have a static IP assigned";

                return (false);
            }

            if ($array_dialup_settings['single_static_ip_allowed'] != 'yes') {
                $error_message = "Single static IP's are not allowed on this dialup service";

                return (false);
            }

            if ($array_staticip['ip_netmask'] != '***************') {
                $error_message = "You have a block of IP's not a single static, " .
                    "please select another option";
            }
            $query = "REPLACE INTO staticip_dialup_configuration (staticip_id,dialup_group_id,date_added,ip_address,ip_netmask,ip_assigned_option_id) values(-1,{$array_dialup_settings['dialup_group_id']},now(),'{$array_staticip['ip_address']}','***************',1)";

            break;
        case 3:
            //  Routed block
            break;
        case 4:
            //  First available host
            break;
        case 5:
            // SMTP Server IP
            break;

        default:
            error_log(__FILE__ . '@' . __LINE__ . "Unknown static ip otion '$ip_option_id', 'service_id = $service_id', dialup_group_id = '$dialup_group_id' ");

            return (false);

    }

    if (config_dialup_set_all_radius_data($service_id) == -1) {
        // Failed contating RADIUS
        return (false);
    }

    // Reconfiguration complete
    return (true);
}

/**
 * Gets the cost of any IP component type
 *
 * @access public
 *
 * @param integer Component type ID from products.service_components
 *
 * @returns decimal
 */
function getCostOfIPType($intComponentTypeID)
{
    if (!preg_match('/^[0-9]+$/', $intComponentTypeID)) {
        return 0;
    }

    $strQuery = 'SELECT decInitialCostIncVat ' .
        'FROM component_ipzone_config ' .
        'WHERE service_component_id = "' . addslashes($intComponentTypeID) . '"';

    $dbhConnection = get_named_connection_with_db('product_reporting');

    $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

    $decCost = PrimitivesResultGet($resResult, 'decInitialCostIncVat');

    if ($decCost === false) {
        $decCost = 0;
    }

    return $decCost;
}
