<?php
    require_once('/local/data/mis/database/application_apis/Mail/Mailbox.class.php');

    /**
     * Mailing list type of Mail_Mailbox class
     * Any operations on the list or performed on the userdata only
     * no changes are made in maildb
     */
    class Mail_List extends Mail_Mailbox
    {
        /**
         * @var array valid options that can be inserted into userdata.config_email.mlist_options
         */
        protected static $_arrValidOptions = array('reply2list', 'useronly', 'anyaddress');

        /**
         * @var array relevant fields from config_email for type mailing list
         */
        protected $_arrFields = array('email_id', 'component_id', 'type', 'isp',
                                      'username', 'login', 'mlist_options', 'status');

        /**
         * Changes mailing list option (edit capability from portal tool)
         *
         * @param array $arrOptions
         * @throws Mail_Exception
         */
        public function setOptions($arrOptions)
        {
            $arrDiff = array_diff($arrOptions, self::$_arrValidOptions);
            if(!empty($arrDiff)){
                throw new Mail_Exception('Invalid mailing list options: ' . print_r($arrOptions, true));
            }
            $this->mlist_options = implode(',', $arrOptions);
            $this->save();
        }

        /**
         * Changes status of the mailbox to active
         */
        public function enable()
        {
            //do nothing, it should stay in queued state and component_add should pick it up
        }

        /**
         * Destroys the list
         *
         * @throws Mail_Exception
         */
        public function destroy($bolDestroyAll = false /* unused - added to satisfy interface */)
        {
            $this->status = 'queued-destroy';
            $this->save();
        }
    }
