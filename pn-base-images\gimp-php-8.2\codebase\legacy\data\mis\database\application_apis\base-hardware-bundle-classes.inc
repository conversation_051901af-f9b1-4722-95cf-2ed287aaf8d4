<?php
	///////////////////////////////////////////////////////////////////////////
	// hardware-bundle-classes.inc
	// <PERSON>, October 2002
	// Revised <PERSON>, June 2004
	// $Id: base-hardware-bundle-classes.inc,v 1.5 2007-06-08 13:15:53 kprzybyszewski Exp $
	//
	// This file contains the base class used by the hardware bundle system.
	//
	// IMPORTANT:
	// The database has been designed to allow each item of the order to be
	// handled by a different supplier, thereby having its own status. However,
	// these classes have been written with the assumption that the supplier
	// and status for each item in an order are the same.
	//
	// Functions marked *** STATIC *** don't require an instansiated object to be used.
	// i.e. You can just call classname::functionname()
	//
	// Hungarian notation used:
	//
	// str : String
	// int : Integer
	// arr : Array of items (not a row from a database)
	// r   : Record (associative array, eg a row from the database)
	// bin : Boolean
	// h   : Handle (eg database connection or result, or a file handle)
	// m_  : Class member variable (shouldn't be touched outside the class)
	//

	require_once '/local/data/mis/database/database_libraries/tickets-access.inc';
	require_once '/local/data/mis/database/database_libraries/customer-numbers-access.inc';
	require_once '/local/data/mis/database/database_libraries/adsl-access.inc';
	require_once '/local/data/mis/database/database_libraries/product-access.inc';
	require_once '/local/data/mis/database/database_libraries/direct-debit-access.inc';
	require_once '/local/data/mis/database/database_libraries/financial-access.inc';
	require_once '/local/data/mis/database/database_libraries/secure-transaction-access.inc';
	require_once '/local/data/mis/database/database_libraries/sql_primitives.inc';
	require_once '/local/data/mis/database/database_libraries/components/config-hardware-bundle-access.inc';
	require_once '/local/data/mis/database/application_apis/HardwareBundle/HardwareBundleBase.class.php';
	
?>