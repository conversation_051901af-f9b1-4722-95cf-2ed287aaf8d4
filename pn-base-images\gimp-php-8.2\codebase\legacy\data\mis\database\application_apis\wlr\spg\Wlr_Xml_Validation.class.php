<?php
    define ('WXV_BT_ACCNO_PORTAL',          0);
    define ('WXV_BT_ACCNO_PROVISIONING',    1);

    class Wlr_Xml_Validation
    {

////////////////////////////////////////////////////////////////////////////////
//
// Constructor
//

        function Wlr_Xml_Validation()
        {
        }

////////////////////////////////////////////////////////////////////////////////
//
// public static methods
//

        function IsBTAccountNumberValid($strBTAccountNumber, $intSource)
        {
            switch ($intSource)
            {
                case WXV_BT_ACCNO_PORTAL:
                    $strPattern = "/^([a-zA-Z]{2})?([0-9 \s]{8,10})$/";
                    break;

                case WXV_BT_ACCNO_PROVISIONING:
                    $strPattern = "/^[0-9 \s]{8,10}$/";
                    break;

                default:
                    return false;        // unrecognised source caller, thus fail it anyway
            }

            return (preg_match($strPattern, $strBTAccountNumber) != FALSE) ? true : false;
        }

////////////////////////////////////////////////////////////////////////////////

        /*
            -----------------------------------------------
            MakeBTAccountNumberCompatibleWithProvisioning()
            -----------------------------------------------

            Assumes that the supplied BT account number is already either:

             a) valid in terms of what the portal expects
             b) valid in terms of what provisioning system expects

            The outcome is undefined otherwise!
        */

        function MakeBTAccountNumberCompatibleWithProvisioning($strBTAccountNumber)
        {
            if (Wlr_Xml_Validation::IsBTAccountNumberValid($strBTAccountNumber, WXV_BT_ACCNO_PROVISIONING))
            {
                return $strBTAccountNumber;
            }

            return preg_replace("/^([a-zA-Z]{2})/", "", $strBTAccountNumber);
        }

////////////////////////////////////////////////////////////////////////////////

        function IsPostcodeValid($strPostcode)
        {
            $intLength = strlen($strPostcode);

            return ($intLength >= 5 && $intLength <= 8 && (preg_match("/^[a-z  A-Z 0-9]*$/", $strPostcode) != FALSE)) ? true : false;
        }

////////////////////////////////////////////////////////////////////////////////

        function IsCLIValid($strCli)
        {
            $intLength = strlen($strCli);

            return ($intLength >= 10 && $intLength <= 14 && (preg_match("/^0[0-9 \s]*$/", $strCli) != FALSE)) ? true : false;
        }

////////////////////////////////////////////////////////////////////////////////

        /**
         * Check whether supplied data is fit for an EngineeringNotes field in XML data.
         *
         * @static
         * @access public
         * @param string $strNotes free-form text to validate
         * @return boolean
         **/

        public static function isEngineeringNotesValid($strNotes)
        {
            if ($strNotes == '' || (strlen($strNotes) <= 320 && preg_match('%^[a-zA-Z0-9\-\s\,\/\&\(\)\\\'][a-zA-Z0-9\-\s\,\/\.\&\(\)\\\']*$%', $strNotes))) {

                return TRUE;
            }


            return FALSE;
        }



        /**
         * Check whether supplied dates are a) dates and b) unique.
         * Dates supplied to this method are expected to be in YYYY-MM-DD format.
         *
         * @static
         * @access public
         * @param string $dtePref preferred appointment date
         * @param string $dteAlt1 first alternative appointment date
         * @param string $dteAlt2 second alternative appointment date
         * @return boolean
         **/

        public static function validateNewProvideAppointmentDates($dtePref, $dteAlt1, $dteAlt2)
        {
            // Do they all look like dates?

            $arrData = array($dtePref, $dteAlt1, $dteAlt2);

            foreach ($arrData as $unkQuickValidation) {

                if ($unkQuickValidation !== date('Y-m-d', strtotime($unkQuickValidation))) {

                    return FALSE;
                }
            }


            // Are they all unique dates?

            if ($dtePref == $dteAlt1 || $dtePref == $dteAlt2 || $dteAlt1 == $dteAlt2) {

                return FALSE;
            }


            return TRUE;
        }
    }

