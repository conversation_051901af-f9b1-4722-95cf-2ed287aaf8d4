<?php
/**
 * This file contains a class HomePhone which in turn used to display the home phone details.
 * <AUTHOR> <l<PERSON><EMAIL>>
 *
 */
/**
 * Needed requirements
 */
require_once '/local/data/mis/database/database_libraries/components/CWlrProduct.inc';
require_once '/local/data/mis/database/application_apis/wlr/spg/SpgOrder.class.php';
require_once '/local/www/database-admin/include/misc.inc';
require_once '/local/data/mis/database/database_libraries/components/HomePhoneSubscriptionPriceHelper.class.php';

/**
 * HomePhone class is used to hold the details about a home phone product like its name, component id, features, subscription cost etc.
 *
 */
class HomePhone
{
    /**
     * variable that holds product name
     *
     * @var string
     */
    protected $strHomePhoneProduct;

    /**
     * variable to hold component Id.
     *
     * @var integer
     */
    protected $intWlrComponentId;

    /**
     * Variable to hold home phone features
     *
     * @var array
     */
    protected $arrHomePhoneFeatures;

    /**
     * Holds the value for move away date
     *
     * @var string
     */
    protected $strHomePhoneMoveAwayDate;

    /**
     * Holds the value for move away verb
     *
     * @var string
     */
    protected $strHomePhoneMoveAwayVerb;

    /**
     * Holds a boolean value for cancelAllowed
     *
     * @var boolean
     */
    protected $bolHomePhoneCancelAllowed;

    /**
     * Holds subsription cost
     *
     * @var float
     */
    protected $floHomePhoneSubscriptionCost;

    /**
     * Holds call plan cost
     *
     * @var float
     */
    protected $floCallPlanCost;

    /**
     * Holds the call plan name
     *
     * @var string
     */
    protected $strCallPlanName;

    /**
     * Holds the phone number
     *
     * @var string
     */
    protected $strHomePhoneNumber;

    /**
     * Holds Contract Start Date
     *
     * @var string
     */
    protected $uxtHomePhoneSubscriptionStart;

    /**
     * Holds Contract End Date
     *
     * @var string
     */
    protected $uxtHomePhoneSubscriptionEnd;

    /**
     * Holds Contract Length
     *
     * @var string
     */
    protected $strHomePhoneSubscriptionContractLength;

    /**
     * Holds the Contract Handle
     *
     * @var string
     */
    protected $strHomePhoneSubscriptionContractHandle;

    /**
     * Service Component Id of the Home Phone component
     *
     * @var int
     */
    protected $intServiceComponentId;

    /**
     * variable to hold tariff Id.
     *
     * @var integer
     */
    protected $intTariffId;

    /**
     * determines wether its just subscription, or linerent+callplan
     * set in CWlrLineRent
     */
    private $bolIsSplitCharge = false;

    /**
     * @var string
     */
    protected $strHomePhoneStatus = null;

    /**
     * Default call plan data
     * @var array
     */
    private $arrCallPlan = array(
                'intCallPlanId'               => 0,
                'intProductComponentConfigId' => 0,
                'vchDisplayName' => '',
                'intChargingIntervalSeconds'  => 0,
                'intCustomCreditLimitPence'   => 0,
                'intCostIncVatPence'          => 0,
                'intTariffID'                 => 0,
                'intServiceComponentID'       => 0);

    /**
     * gets call plan name
     *
     * @return string
     * @access public
     */
    public function getCallPlanName()
    {
        return $this->arrCallPlan['vchDisplayName'];
    }

    /**
     * gets call plan tariff id
     *
     * @return string
     * @access public
     */
    public function getCallPlanTariffID()
    {
        return $this->arrCallPlan['intTariffID'];
    }

    /**
     * gets call plan service component id
     *
     * @return string
     * @access public
     */
    public function getCallPlanServiceComponentID()
    {
        return $this->arrCallPlan['intServiceComponentID'];
    }

    /**
     * gets call plan details
     *
     * @return array
     * @access public
     */
    public function getCallPlanDetails()
    {
        return $this->arrCallPlan;
    }

    /**
     * gets call plan cost pense
     *
     * @return int
     * @access public
     */
    public function getCallPlanCostPence()
    {
        return $this->arrCallPlan['intCostIncVatPence'];
    }

    /**
     * returns flag whether price should be shows as split
     *
     * @return bool
     * @access public
     */
    public function isSplitCharge()
    {
        return $this->bolIsSplitCharge;
    }

    /**
     * Setter method for Home phone product name.
     *
     * @param string $strHomePhoneProduct
     */
    public function setHomePhoneProduct ($strHomePhoneProduct)
    {
        $this->strHomePhoneProduct = $strHomePhoneProduct;
    }

    /**
     * Setter method for WLR component Id.
     *
     * @param int $intWlrComponentId
     */
    public function setWlrComponentId ($intWlrComponentId)
    {
        $this->intWlrComponentId = $intWlrComponentId;
    }

    /**
     * Setter method for tariff id
     *
     * @param int $intTariffId
     */
    public function setTariffId($intTariffId)
    {
        $this->intTariffId = $intTariffId;
    }

    /**
     * Setter method for home phone features
     *
     * @param array $arrHomePhoneFeatures
     */
    public function setHomePhoneFeatures ($arrHomePhoneFeatures)
    {
        $this->arrHomePhoneFeatures = $arrHomePhoneFeatures;
    }

    /**
     * Setter method for Home phone move away date
     *
     * @param string $strHomePhoneMoveAwayDate
     */
    public function setHomePhoneMoveAwayDate ($strHomePhoneMoveAwayDate)
    {
        $this->strHomePhoneMoveAwayDate = $strHomePhoneMoveAwayDate;
    }

    /**
     * Setter method for home phone move away verb
     *
     * @param string $strHomePhoneMoveAwayVerb
     */
    public function setHomePhoneMoveAwayVerb ($strHomePhoneMoveAwayVerb)
    {
        $this->strHomePhoneMoveAwayVerb = $strHomePhoneMoveAwayVerb;
    }

    /**
     * Setter method for home phone cancelAllowed
     *
     * @param boolean $bolHomePhoneCancelAllowed
     */
    public function setHomePhoneCancelAllowed ($bolHomePhoneCancelAllowed)
    {
        $this->bolHomePhoneCancelAllowed = $bolHomePhoneCancelAllowed;
    }

    /**
     * Setter method for home phone subscription cost
     *
     * @param float $floHomePhoneSubscriptionCost
     */
    public function setHomePhoneSubscriptionCost($floHomePhoneSubscriptionCost)
    {
        $this->floHomePhoneSubscriptionCost = $floHomePhoneSubscriptionCost;
    }

    /**
     * Setter method for home phone number
     *
     * @param string $strHomePhoneNumber
     */
    public function setHomePhoneNumber ($strHomePhoneNumber)
    {
        $this->strHomePhoneNumber = $strHomePhoneNumber;
    }

    /**
     * Setter method for home contract start
     *
     * @param str $strHomePhoneSubscriptionStart
     */
    public function setHomePhoneSubscriptionContractStart($strHomePhoneSubscriptionStart)
    {
        $this->uxtHomePhoneSubscriptionStart = $strHomePhoneSubscriptionStart;
    }

    /**
     * Setter method for home contract end
     *
     * @param str $strHomePhoneSubscriptionEnd
     */
    public function setHomePhoneSubscriptionContractEnd($strHomePhoneSubscriptionEnd)
    {
        $this->uxtHomePhoneSubscriptionEnd = $strHomePhoneSubscriptionEnd;
    }

    /**
     * Setter method for home contract length
     *
     * @param str $strHomePhoneSubscriptionContractLength
     */
    public function setHomePhoneSubscriptionContractLength($strHomePhoneSubscriptionContractLength)
    {
        $this->strHomePhoneSubscriptionContractLength = $strHomePhoneSubscriptionContractLength;
    }

    /**
     * Setter method for home contract handle
     *
     * @param unknown_type $strHomePhoneSubscriptionContractHandle
     */
    public function setHomePhoneSubscriptionContractHandle($strHomePhoneSubscriptionContractHandle)
    {
        $this->strHomePhoneSubscriptionContractHandle = $strHomePhoneSubscriptionContractHandle;
    }

    /**
     * sets the home phone status
     *
     * @param string $strStatus
     * @return void
     */
    public function setHomePhoneStatus($strStatus)
    {
        $this->strHomePhoneStatus = $strStatus;
    }

    /**
     * Set the product component instance id.
     *
     * @param int $intWlrSubscriptionID
     * @return void
     */
    public function setProductComponentInstanceID($intProductComponentInstanceID)
    {
        $this->intProductComponentInstanceID = $intProductComponentInstanceID;
    }

    /**
     * Getter for HomePhoneProduct
     *
     * @return string
     */
    public function getHomePhoneProduct()
    {
        return $this->strHomePhoneProduct;
    }

    /**
     * Getter method for WLRComponentId
     *
     * @return integer
     */
    public function getWlrComponentId()
    {
        return $this->intWlrComponentId;
    }

    /**
     * Getter method for tariff id
     *
     * @return integer
     */
    public function getTariffId()
    {
        return $this->intTariffId;
    }

    /**
     * Getter method for home phone features
     *
     * @return string
     */
    public function getHomePhoneFeatures()
    {
        return $this->arrHomePhoneFeatures;
    }

    /**
     * Getter method for move away date
     *
     * @return string
     */
    public function getHomePhoneMoveAwayDate()
    {
        return $this->strHomePhoneMoveAwayDate;
    }

    /**
     * Getter method for move away verb
     *
     * @return string
     */
    public function getHomePhoneMoveAwayVerb()
    {
        return $this->strHomePhoneMoveAwayVerb;
    }

    /**
     * Getter method for cancelAllowed
     *
     * @return boolean
     */
    public function getHomePhoneCancelAllowed()
    {
        return $this->bolHomePhoneCancelAllowed;
    }

    /**
     * Getter method for subscription cost
     *
     * @return float
     */
    public function getHomePhoneSubscriptionCost()
    {
        return $this->floHomePhoneSubscriptionCost;
    }

    /**
     * Getter method for home phone number
     *
     * @return string
     */
    public function getHomePhoneNumber()
    {
        return $this->strHomePhoneNumber;
    }

    /**
     * Getter method for subscription contract start date
     *
     * @return str strHomePhoneSubscriptionStart
     */
    public function getHomePhoneSubscriptionContractStart()
    {
        return $this->uxtHomePhoneSubscriptionStart;
    }

    /**
     * Getter method for subscription contract start end
     *
     * @return str strHomePhoneSubscriptionEnd
     */
    public function getHomePhoneSubscriptionContractEnd()
    {
        return $this->uxtHomePhoneSubscriptionEnd;
    }

    /**
     * Getter method for subscription contract length
     *
     * @return str strHomePhoneSubscriptionContractLength
     */
    public function getHomePhoneSubscriptionContractLength()
    {
        return $this->strHomePhoneSubscriptionContractLength;
    }

    /**
     * Getter method for subscription contract handle
     *
     * @return string
     */
    public function getHomePhoneSubscriptionContractHandle()
    {
        return $this->strHomePhoneSubscriptionContractHandle;
    }

    /**
     * Getter for the service component id of the home phone component
     *
     * @return int
     */
    public function getServiceComponentId()
    {
        return $this->intServiceComponentId;
    }

    /**
     * getter for home phone status
     *
     * @return string
     */
    public function getHomePhoneStatus()
    {
        return $this->strHomePhoneStatus;
    }

    /**
     * Design comment - having so many basic accessors is ugly. Why not just use
     * the __get and __set methods and check for property availability?
     */




    /**
     * Finds the component ID of the WLR product that the Workplace customer
     * details page effectively uses on the Domains / Services and Home Phone tabs.
     *
     * Refactored to at least attempt a consistent way of Workplace acquiring
     * a WLR product. (Domains / Services could also be refactored to use
     * this class/method, but that's beyond the scope of why this particular
     * change has been made)
     *
     * @access public
     * @static
     * @param integer - service ID
     * @return integer - component ID on success, 0 if error/no component
     * @throws none
     **/

    public static function findCurrentComponentId($intServiceId)
    {
        // 1. we need to know about unconfigured components for the Order Resubmission URL within Workplace Account Control section.
        $intWlrComponentIdUnconfigured = CProduct::getComponentIDByServiceID($intServiceId, 'WLR', array('unconfigured'));
        // 2. we also need to know about non-unconfigured components for all other purposes.
        $arrWLRStates = array('queued-activate','queued-reactivate','active','queued-deactivate','queued-deconfigure','deactive');
        $intWlrComponentIdNotUnconfigured = CProduct::getComponentIDByServiceID($intServiceId, 'WLR', $arrWLRStates);

        // 3. Which one are we interested in?
        if ($intWlrComponentIdNotUnconfigured) {

            return $intWlrComponentIdNotUnconfigured;

        } elseif ($intWlrComponentIdUnconfigured) {

            return $intWlrComponentIdUnconfigured;
        }


        return 0;
    }


    /**
     * Constructor for Home phone. This returns a home phone object
     *
     * @param Integer $intServiceId
     * @return HomePhone
     * @throws Exception
     */
    public function __construct($intServiceId)
    {
        $intWlrComponentId = self::findCurrentComponentId($intServiceId);

        if ($intWlrComponentId) {

            $this->setWlrComponentId($intWlrComponentId);

            $objWlrProduct = CWlrProduct::createInstance($intWlrComponentId);

            $strWlrProductName = $objWlrProduct->getFullName();

            $this->setHomePhoneProduct($strWlrProductName);

            $this->setHomePhoneStatus($objWlrProduct->getStatus());

            // problem 33472 - need to show Resubmit tool URL if customer has a currently Rejected
            // order on their account (as well as being rejected by our provisioning system)

            $arrRejectedWlrOrders = Wlr_SpgOrder::getRejectedOrdersByComponentId($intWlrComponentId);

            // bit of ewww-ness coming next; look away if screamish... now!!
            $arrWlrAdditionalFeatureNames = array();

            $arrWlrAdditionalFeatures = CWlrCallFeature::enumAllCallFeatures($objWlrProduct);

            if ($arrWlrAdditionalFeatures !== false && is_array($arrWlrAdditionalFeatures) && !empty($arrWlrAdditionalFeatures)) {

                // TODO: refactor these 3 loops
                foreach($arrWlrAdditionalFeatures['added'] as $thisAdditionalFeature) {
                    $arrWlrAdditionalFeatureNames[] = $thisAdditionalFeature['strFeatureName'] . " (awaiting activation)";
                }

                foreach($arrWlrAdditionalFeatures['removed'] as $thisAdditionalFeature) {
                    $arrWlrAdditionalFeatureNames[] = $thisAdditionalFeature['strFeatureName'];
                }

                foreach($arrWlrAdditionalFeatures['active'] as $thisAdditionalFeature) {
                    $arrWlrAdditionalFeatureNames[] = $thisAdditionalFeature['strFeatureName'];
                }

            }

            $this->setHomePhoneFeatures($arrWlrAdditionalFeatureNames);

            // is customer moving away from us, and if so, when?
            $objWlrLineRental = CProductComponent::createInstanceFromComponentID($intWlrComponentId, 'WLRLineRent', array('ACTIVE', 'QUEUED-ACTIVATE', 'UNCONFIGURED'));

            $strHomePhoneNumber='N/A';

            if (is_object($objWlrLineRental)) {

                $arrWlrLRConfig=$objWlrLineRental->getUserConfig();
                $strHomePhoneNumber=$arrWlrLRConfig['vchCli'];

                $arrTemp = $objWlrLineRental->getCallPlan();
                if(is_array($arrTemp))
                {
                    $this->arrCallPlan = $arrTemp;
                    $this->arrCallPlan['intTariffID'] = $objWlrLineRental->getTariffID();
                    $this->arrCallPlan['intServiceComponentID'] =  $objWlrLineRental->getServiceComponentID();
                    $this->bolIsSplitCharge = $objWlrLineRental->displaySplitCharge();
                }
                $this->setProductComponentInstanceID($objWlrLineRental->getProductComponentInstanceID());
            }

            $this->setHomePhoneNumber($strHomePhoneNumber);

            if ($objWlrLineRental !== false && ($arrWlrCancelOrderInfo = Wlr_OrderType_CancelOther::getLatestCancelOtherEntry($objWlrLineRental->getProductComponentInstanceID())) != FALSE && is_array($arrWlrCancelOrderInfo) && !empty($arrWlrCancelOrderInfo)){

                $strHomePhoneMoveAwayDate = date("d/m/Y", strtotime($arrWlrCancelOrderInfo['dteExpectedCancellationDate']));

                if($arrWlrCancelOrderInfo['bolCustomerChoseToStay'] == 0)
                {
                    if($objWlrLineRental->hasTransferredOut()) {

                        $strHomePhoneMoveAwayVerb = 'completed on';
                        $bolHomePhoneCancelAllowed = false;
                    }
                    else {

                        // customer hasn't said that they want to stay with us, thus give CSC
                        // the ability to set this, should the customer contact us and request it.
                        $strHomePhoneMoveAwayVerb = 'scheduled for';
                        $bolHomePhoneCancelAllowed = true;
                    }

                }
                else {

                    // customer has said they want to stay with us, thus there's nothing to cancel
                    $strHomePhoneMoveAwayVerb = 'request cancelled for';
                    $bolHomePhoneCancelAllowed = false;
                }
            }
            else {

                // do this if an error occurred, or if customer has no Cancel Other entry
                $strHomePhoneMoveAwayDate = 'N/A';
                $strHomePhoneMoveAwayVerb = '';
                $bolHomePhoneCancelAllowed = false;
            }

            $this->setHomePhoneMoveAwayDate($strHomePhoneMoveAwayDate);
            $this->setHomePhoneMoveAwayVerb($strHomePhoneMoveAwayVerb);
            $this->setHomePhoneCancelAllowed($bolHomePhoneCancelAllowed);

            //get subscription cost
            $floSubscriptionCost='N/A';

            $intWlrSubscriptionID = CProductComponent::getProductComponentInstance($intServiceId, 'SUBSCRIPTION', array('ACTIVE', 'QUEUED-ACTIVATE', 'UNCONFIGURED'),'WLR');

            $objWlrSubscription = CProductComponent::createInstance($intWlrSubscriptionID);

            if(is_object($objWlrSubscription)){

                $arrTariffDetails = $objWlrSubscription->getTariffDetails($objWlrSubscription->getTariffID());
                $this->setTariffId($objWlrSubscription->getTariffID());
                $this->setHomePhoneSubscriptionContractStart($objWlrSubscription->getContractStart());
                $this->setHomePhoneSubscriptionContractEnd($objWlrSubscription->getContractEnd());
                $this->setHomePhoneSubscriptionContractLength($arrTariffDetails['strContractLengthDisplayName']);
                $this->setHomePhoneSubscriptionContractHandle($arrTariffDetails['strContractLengthHandle']);
                $this->intServiceComponentId = $objWlrSubscription->getServiceComponentID();
                $floSubscriptionCost = format_currency(
                    $objWlrSubscription->getPaymentFrequencyCost($objWlrSubscription->getTariffID())/100);
            }

            $this->setHomePhoneSubscriptionCost($floSubscriptionCost);
        }
        else {

            throw new Exception("Home Phone not available for service ID $intServiceId");
        }
    }

    /**
     * Function to retrieve the provisioned supplier platform.
     *
     * @return string $strProvisionedPlatform
     */
    public function getProvisionedPlatform()
    {
        $strProvisionedPlatform = CWlrProduct::getSupplierPlatform($this->intProductComponentInstanceID);
        if(empty($strProvisionedPlatform)) {
            $strProvisionedPlatform = 'WLR3';
        }
        return $strProvisionedPlatform;
    }

    /**
     * Gets the current subscription prices, from subscriptions retrieved from billing
     *
     * @param int $intServiceId the customer's service Id
     *
     * @return array the subscription prices
     */
    public function getCurrentSubscriptionPrices($intServiceId)
    {
        $helper = new HomePhoneSubscriptionPriceHelper();
        return $helper->getCurrentSubscriptionPrices($intServiceId);
    }
}
