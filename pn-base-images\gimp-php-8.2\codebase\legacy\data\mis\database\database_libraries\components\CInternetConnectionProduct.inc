<?php
/**
 * Access library for internet connection component.
 *
 * @package    Products
 * @subpackage InternetConnection
 * @access     public
 * <AUTHOR> <<EMAIL>>
 * @version    $Id: CInternetConnectionProduct.inc,v 1.12.2.3 2009/07/01 14:27:54 mstarbuck Exp $
 * @filesource
 */

require_once '/local/data/mis/database/database_libraries/components/CProduct.inc';
require_once '/local/data/mis/database/database_libraries/components/CInternetConnectionProductHelper.php';

if (!defined('CUSTOMER_DETAILS')) {
    require_once SECURE_TRANSACTION_ACCESS_LIBRARY;
    require_once FINANCIAL_ACCESS_LIBRARY;
    require_once TICKETS_ACCESS_LIBRARY;
    require_once DIRECT_DEBIT_ACCESS_LIBRARY;
    require_once '/local/data/mis/database/database_libraries/mailer_functions.inc';
}

require_once CONFIG_DIALUP_ACCESS_LIBRARY;
require_once '/local/data/mis/portal_modules/payment/cc_result.inc';
require_once '/local/data/mis/database/database_libraries/CoreObjects/Financial/CProductComponentScheduledPayment.inc';
require_once '/local/data/mis/common_library_functions/class_libraries/CAutoProblem.class.php';
require_once PHPLIB_ACCESS_LIBRARY;
require_once '/local/data/mis/database/database_libraries/components/CWlrProduct.inc';
use \Plusnet\Feature\FeatureToggleManager;

/**
 * Internet Connection Product
 *
 * Internet Connection product
 *
 * @package    Products
 * @subpackage InternetConnection
 * <AUTHOR> Black <<EMAIL>>
 */
class CInternetConnectionProduct extends CProduct
{
    /**
     * Constructor
     *
     * @param integer $intComponentID
     */
    function __construct($intComponentID)
    {
        $bolResult = $this->refreshInstance($intComponentID);

        if (false == $bolResult) {

            $this->setError(
                __FILE__, __LINE__, "Failed to construct CInternetConnectionProduct for {$intComponentID} component id"
            );
        }
    }

    /**
     * Retrieves the Internet Connection Product by Service ID
     *
     * @param integer $intServiceId
     * @param string  $strStatus    (Optional) Status of the component
     *
     * @return CInternetConnectionProduct
     */
    public static function getInternetConnectionProductFromServiceId($intServiceId, $strStatus=null)
    {
        $objInternetConnectionProduct = CProduct::getProductByProductTypeHandle($intServiceId, 'INTERNET_CONNECTION', $strStatus);
        return $objInternetConnectionProduct;
    }

    /**
     * Get a list of the InternetConnection Component types
     *
     * Get a simple array containing the InternetConnection Service Component
     * types.
     *
     * <code>
     * $arrList = CProduct::getProductComponentTypes();
     * </code>
     *
     * @access public
     * @static
     * <AUTHOR> Black" <<EMAIL>>
     * @return array service component ID's
     */
    public static function getInternetConnectionComponentTypes()
    {
        return self::getProductComponentTypes('INTERNET_CONNECTION');
    }

    /**
     * isSignupOrUpgrade
     *
     * @param  integer $intServiceID
     * @param  string  $strProductHandle
     * @return string  [SIGNUP|UPGRADE]
     */
    public function isSignupOrUpgrade($intServiceID, $strProductHandle = 'INTERNET_CONNECTION')
    {
        return CProduct::isSignupOrUpgrade($intServiceID, $strProductHandle);
    }

    /**
     * Actually create a new component product
     *
     * This method creates the ICP component on the account, and adds the Product
     * Components to the new Component. It does NOT do any validation
     *
     * @static
     * @param integer $intServiceID
     * @param integer $intServiceComponentID
     * @param integer $intTariffID
     * @param integer $uxtNextInvoiceDate
     * @param array $arrProductComponentOptions
     * @param integer $uxtStartTime
     * @return boolean
     */
    public static function create($intServiceID, $intServiceComponentID,
        $intTariffID = 0, $uxtNextInvoiceDate = false,
        $arrProductComponentOptions='', $uxtStartTime = '')
    {
        $intComponentID = userdata_component_add($intServiceID, $intServiceComponentID, '-1', '', 'unconfigured');
        if (false == $intComponentID) {
            return false;
        }

        $arrProductComponentIDs = CProduct::getDefaultProductComponentIDs($intServiceComponentID);

        if (!(isset($arrProductComponentIDs)
            && is_array($arrProductComponentIDs) && count($arrProductComponentIDs) > 0)) {
            return false;
        }

        $strProductHandle = 'INTERNET_CONNECTION';

        foreach ($arrProductComponentIDs as $intProductComponentID) {
            switch ($intProductComponentID) {
                case PRODUCT_COMPONENT_SUBSCRIPTION:
                    $objProductComponent = CProductComponent::create(
                        $intServiceID, $intComponentID, $intProductComponentID,
                        $intTariffID, $uxtNextInvoiceDate, $strProductHandle,
                        $uxtStartTime
                    );
                    break;
                default:
                    $objProductComponent = CProductComponent::create(
                        $intServiceID, $intComponentID, $intProductComponentID,
                        0, false, $strProductHandle, $uxtStartTime
                    );
                    break;
            }
        }

        $objProduct = CInternetConnectionProduct::createInstance($intComponentID);

        if (isset($objProduct) && is_object($objProduct)) {
            $objEventLogger = $objProduct->prvGetEventLogger();
            $objEventLogger->logCreation('InternetConnectionCreation');
            unset($objEventLogger);
            return $objProduct;
        }

        return false;
    }

    /**
     * Creates a product instance to add to the account
     *
     * @param integer $intServiceID Service ID
     * @param integer $intServiceComponentID Service Component ID (the component type to create)
     * @param array $arrProductComponentOptions Product Component options to add
     * @param array $arrOptParams Optional Parameters, provided with keys:
     *                            strContractHandle   (MONTHLY|QUARTERLY|ANNUAL|18MONTH|24MONTH|NONE)
     *                            strPaymentFrequency (MONTHLY|QUARTERLY|ANNUAL|NONE)
     * @return CInternetConnectionProduct
     */
    public static function doSignup($intServiceID, $intServiceComponentID,
        $arrProductComponentOptions = array(), $arrOptParams = array())
    {
         $arrArgs = array();
         if (isset($arrProductComponentOptions['objPaymentStart'])) {
            $arrArgs['objPaymentStart'] = $arrProductComponentOptions['objPaymentStart'];
         }
        // Create empty object in case errors are raised
        $objInternetConnectionProduct = new CInternetConnectionProduct(0);

        // Don't allow for more than one Internet Connection Product
        $objCheckInternetConnection = CInternetConnectionProduct::getInternetConnectionProductFromServiceId(
            $intServiceID
        );

        if (true == is_object($objCheckInternetConnection)) {

            $objInternetConnectionProduct->setError(
                __FILE__, __LINE__, "Service {$intServiceID} already have internet connection product"
            );
            return $objInternetConnectionProduct;
        }

        // Get a Tariff for the subscription
        $strContractLengthHandle =
            (isset($arrOptParams['strContractLengthHandle'])) ? $arrOptParams['strContractLengthHandle'] : 'MONTHLY';
        $strPaymentFrequencyHandle =
            (isset($arrOptParams['strPaymentFrequencyHandle'])) ? $arrOptParams['strPaymentFrequencyHandle']
                : 'MONTHLY';

        // Get the cost level handle (for BoS products)
        $strCostLevelHandle =
            (isset($arrOptParams['strCostLevelHandle'])) ? $arrOptParams['strCostLevelHandle'] : '';

        $bolValidCostLevel = false;
        if (!empty($strCostLevelHandle)){
            $bolValidCostLevel = CInternetConnectionProduct::isValidPricePlan($strCostLevelHandle);
        }

        $intTariffID = self::getSignupSubscriptionTariffID(
            $intServiceComponentID,
            $strContractLengthHandle,
            $strPaymentFrequencyHandle,
            ($bolValidCostLevel ? $strCostLevelHandle : '')
        );

        if (false === $intTariffID) {
            $objInternetConnectionProduct->setError(
                __FILE__, __LINE__,
                "A tariff could not be found for Internet Connection Product Type [$intServiceComponentID] " .
                "for [$strContractLengthHandle] contract length and [$strPaymentFrequencyHandle] payment frequency"
            );
            return $objInternetConnectionProduct;
        }

        // Create the product
        $arrService = userdata_service_get($intServiceID);
        $uxtNextInvoice = strtotime($arrService['next_invoice']);
        $objInternetConnectionProduct = CInternetConnectionProduct::create(
            $intServiceID, $intServiceComponentID, $intTariffID,
            $uxtNextInvoice, $arrProductComponentOptions
        );
        $objInternetConnectionProduct->setStatus('queued-activate');
        $intComponentID = $objInternetConnectionProduct->getComponentID();

        // Create the product components
        $arrInternetConnectionProductComponents =
            $objInternetConnectionProduct->listProductComponentInstanceIdByHandle();

        if (empty($arrInternetConnectionProductComponents)) {
            $objInternetConnectionProduct->setError(
                __FILE__, __LINE__, "Could not create Product Components for Component [{$intComponentId}]"
            );
            return $objInternetConnectionProduct;
        }

        if (isset($arrProductComponentOptions['bolDialup']) && true === $arrProductComponentOptions['bolDialup']) {
            $objInternetConnectionProduct->enable($arrArgs);
        }

        // Log event
        $objEventLogger = new CComponentEvent($intComponentID);
        $objEventLogger->logSignup('INTERNET_CONNECTION');
        unset($objEventLogger);

        return $objInternetConnectionProduct;
    }

    /**
     * Return the tariff id for this component
     * that is for the signup costs on the subscription - first tariff covering the initial lower price
     *
     * @param integer $intServiceComponentID
     * @param string $strContractLengthHandle
     * @param string $strPaymentFrequencyHandle
     * @return integer
     */
    public static function getSignupSubscriptionTariffID($intServiceComponentID,
        $strContractLengthHandle, $strPaymentFrequencyHandle, $strCostLevelHandle = null)
    {
        $dbhConnection = get_named_connection_with_db('product_reporting');

        // Firstly check whether one distinct tariff id is found
        $strQuery =
            'SELECT DISTINCT t.intTariffID AS intTariffID ' .
            '           FROM dbProductComponents.tblTariff t ' .
            '     INNER JOIN dbProductComponents.tblContractLength cl ON cl.intContractLengthID = t.intContractLengthID ' .
            "            AND cl.vchHandle = '$strContractLengthHandle' " .
            '     INNER JOIN dbProductComponents.tblPaymentFrequency pf ON pf.intPaymentFrequencyID = t.intPaymentFrequencyID ' .
            "            AND pf.vchHandle = '$strPaymentFrequencyHandle' " .
            '     INNER JOIN dbProductComponents.tblProductComponentConfig pcc ON pcc.intProductComponentConfigID = t.intProductComponentConfigID ' .
            '     INNER JOIN products.tblServiceComponentProduct scp ON scp.intServiceComponentProductID = pcc.intServiceComponentProductID ' .
            "            AND scp.intServiceComponentID = '$intServiceComponentID' " .
            '     INNER JOIN dbProductComponents.tblProductComponent pc ON pc.intProductComponentID = pcc.intProductComponentID ' .
            '            AND pc.vchHandle = "SUBSCRIPTION" ';

        if ($strCostLevelHandle) {

            $strQuery .= "
                INNER JOIN
                    dbProductComponents.tblTariffPricePlan tpp ON tpp.intTariffId = t.intTariffID
                INNER JOIN
                    dbProductComponents.tblPricePlan pp ON pp.intPricePlanId = tpp.intPricePlanId
                AND
                    pp.vchHandle = '$strCostLevelHandle'
            ";
        }

        $strQueryAll = $strQuery . ' WHERE (t.dtmEnd IS NULL OR t.dtmEnd > NOW())';

        if (false ===
            ($refResult =
                PrimitivesQueryOrExit(
                    $strQueryAll, $dbhConnection,
                    "getSignupSubscriptionTariffID '$intServiceComponentID'"
                )
            )) {
            return false;
        }

        // Return Tarriff ID
        if (PrimitivesNumRowsGet($refResult) > 0) {
            return PrimitivesResultGet($refResult, 'intTariffID');
        }

        return false;
    }

    /**
     * Returns rolling tariffs details for given service id.
     *
     * In theory it should give you number of months with a lower price
     * for given customer.
     *
     * @param int    $serviceId
     * @param string $strContractLengthHandle
     * @param string $strPaymentFrequencyHandle
     *
     * @return array
     */
    public static function getRollingTariffsDetailsForServiceId($serviceId,
        $strContractLengthHandle, $strPaymentFrequencyHandle)
    {
        $dbhConnection = get_named_connection_with_db('product_reporting');

        $strQuery = "
            SELECT
                DISTINCT t.intTariffID, t.intNextTariffID, t.intCostIncVatPence
            FROM dbProductComponents.tblTariff t
            INNER JOIN dbProductComponents.tblContractLength cl
                ON cl.intContractLengthID = t.intContractLengthID
                AND cl.vchHandle = '$strContractLengthHandle'
            INNER JOIN dbProductComponents.tblPaymentFrequency pf
                ON pf.intPaymentFrequencyID = t.intPaymentFrequencyID
                AND pf.vchHandle = '$strPaymentFrequencyHandle'
            INNER JOIN dbProductComponents.tblProductComponentConfig pcc
                ON pcc.intProductComponentConfigID = t.intProductComponentConfigID
            INNER JOIN products.tblServiceComponentProduct scp
                ON scp.intServiceComponentProductID = pcc.intServiceComponentProductID
            INNER JOIN products.service_components sc
                ON sc.service_component_id = scp.intServiceComponentID
            INNER JOIN userdata.components c
                ON c.component_type_id = sc.service_component_id
            INNER JOIN dbProductComponents.tblProductComponent pc
                ON pc.intProductComponentID = pcc.intProductComponentID
                AND pc.vchHandle = 'SUBSCRIPTION'
            WHERE (t.dtmEnd IS NULL OR t.dtmEnd > NOW())
                AND c.service_id = '$serviceId'
                AND t.intTariffID != t.intNextTariffID
                AND pcc.dtmEnd IS NULL
        ";

        if (false ===
            ($refResult =
                PrimitivesQueryOrExit(
                    $strQuery, $dbhConnection,
                    "getRollingTariffsNumberForServiceId '$serviceId'"
                )
            )) {
            return false;
        }

        return PrimitivesResultsAsArrayGet($refResult);
    }

    /**
     * Return the tariff id for this component that is for the upgrade costs on the subscription
     *
     * @param integer $intServiceComponentID
     * @param string $strContractLengthHandle
     * @param string $strPaymentFrequencyHandle
     * @param array $arrOptions
     * @return integer
     */
    public static function getUpgradeSubscriptionTariffID($intServiceComponentID,
        $strContractLengthHandle, $strPaymentFrequencyHandle, array $arrOptions = array())
    {

        $bolValueFamily = (isset($arrOptions['bolValueFamily']) && true === $arrOptions['bolValueFamily']);
        $bolBpr09Family = (isset($arrOptions['bolBpr09Family']) && true === $arrOptions['bolBpr09Family']);

        $dbhConnection = get_named_connection_with_db('product');
        $strQuery =
            '    SELECT t.intTariffID AS intTariffID ' .
            '      FROM dbProductComponents.tblTariff t ' .
            'INNER JOIN dbProductComponents.tblContractLength cl ON cl.intContractLengthID = t.intContractLengthID ' .
            "       AND cl.vchHandle = '$strContractLengthHandle' " .
            'INNER JOIN dbProductComponents.tblPaymentFrequency pf ON pf.intPaymentFrequencyID = t.intPaymentFrequencyID ' .
            "       AND pf.vchHandle = '$strPaymentFrequencyHandle' " .
            'INNER JOIN dbProductComponents.tblProductComponentConfig pcc ON pcc.intProductComponentConfigID = t.intProductComponentConfigID ' .
            'INNER JOIN products.tblServiceComponentProduct scp ON scp.intServiceComponentProductID = pcc.intServiceComponentProductID ' .
            "       AND scp.intServiceComponentID = '$intServiceComponentID' " .
            'INNER JOIN dbProductComponents.tblProductComponent pc ON pc.intProductComponentID = pcc.intProductComponentID ' .
            '       AND pc.vchHandle = "SUBSCRIPTION" ' .
            '     WHERE (t.dtmEnd IS NULL OR t.dtmEnd > NOW())';

        // This is to exclude lead prices from MAAF / JLP products
        if (!$bolBpr09Family && !$bolValueFamily
            && $strContractLengthHandle != '18MONTH' && $strContractLengthHandle != 'ANNUAL') {

            $strQuery .= 'AND t.intTariffID = t.intNextTariffID';
        }

        if (false ===
            ($refResult = PrimitivesQueryOrExit(
                $strQuery, $dbhConnection,
                "getUpgradeSubscriptionTariffID '$intServiceComponentID'"
            ))) {
            return false;
        }

        if (PrimitivesNumRowsGet($refResult) > 0) {
            $intTariffID = PrimitivesResultGet($refResult, 'intTariffID');
        } else {
            return false;
        }

        return $intTariffID;
    }

    /**
     * Return string of component (component name)
     *
     * <AUTHOR> Black <<EMAIL>>
     * @return string
     */
    public function __toString()
    {
        return $this->getFullName();
    }

    /**
     * Returns the full name of this Product, including any extra strings like number of minutes included
     *
     * <AUTHOR>
     * @return string full product name
     */
    public function getFullName()
    {
        return $this->m_strComponentName;
    }

    /**
     * Get the cost of signing up to this product.
     *
     * @param intger $intServiceID
     * @param integer $intServiceComponentID
     * @param integer $intTariffID
     * @param integer $uxtProRataCalculationDate
     * @return array
     */
    public function getSignupCost($intServiceID, $intServiceComponentID, $intTariffID, $uxtProRataCalculationDate = 0)
    {
        $arrTariffDetails = CProduct::getTariffDetails($intTariffID);
        $arrProRataCostDetails = CProductComponent::generateProRataPaymentDetails(
            $intServiceID, $intTariffID, 'INNERNET_CONNECTION',
            $uxtProRataCalculationDate
        );

        $arrSignupCostDetails = array();
        $arrSignupCostDetails['arrProRataCostDetails'] = $arrProRataCostDetails;
        $arrSignupCostDetails['arrTariffDetails']      = $arrTariffDetails;

        return $arrSignupCostDetails;
    }

    /**
     * Get the cost of upgrading to this product.
     *
     * @param integer $intServiceID
     * @param integer $intServiceComponentID
     * @param integer $intTariffID
     * @param integer $uxtProRataCalculationDate
     * @return array
     */
    public static function getUpgradeCost($intServiceID, $intServiceComponentID,
        $intTariffID, $uxtProRataCalculationDate = 0)
    {
        $intExistingTariffID   = CProductComponent::getExistingTariffID($intServiceID, 'INTERNET_CONNECTION');
        $arrOldTariffDetails   = CProduct::getTariffDetails($intExistingTariffID);
        $arrTariffDetails      = CProduct::getTariffDetails($intTariffID);
        $arrProRataCostDetails = CProductComponent::generateProRataPaymentDetails(
            $intServiceID, $intTariffID, 'INTERNET_CONNECTION',
            $uxtProRataCalculationDate
        );

        if (false !== $intExistingTariffID) {
            $arrProRataCostDetails['intProRataCost'] =
                $arrTariffDetails['intCostIncVatInPence'] - $arrOldTariffDetails['intCostIncVatInPence'];
            $arrProRataCostDetails['floProRataCostDisplay'] = $arrProRataCostDetails['intProRataCost'] / 100;
        }

        $arrCostDetails = array();
        $arrCostDetails['arrProRataCostDetails'] = $arrProRataCostDetails;
        $arrCostDetails['arrTariffDetails']      = $arrTariffDetails;

        return $arrCostDetails;
    }

    /**
     * Do upgrade
     *
     * @param integer $intNewServiceComponentID
     * @param array $arrProductComponentOptions
     * @param array $arrOptParams
     * @return boolean
     */
    public function doUpgrade($intNewServiceComponentID, $arrProductComponentOptions, $arrOptParams = array())
    {

        // get new tariff id
        $strContractLengthHandle   = (isset($arrOptParams['strContractLengthHandle']))
                ? $arrOptParams['strContractLengthHandle']
                : 'MONTHLY';
        $strPaymentFrequencyHandle = (isset($arrOptParams['strPaymentFrequencyHandle']))
                ? $arrOptParams['strPaymentFrequencyHandle']
                : 'MONTHLY';

        if (!is_null($arrOptParams['intSelectedTariffID'])) {

            $intNewTariffID = $arrOptParams['intSelectedTariffID'];
        } else {

            $intNewTariffID = self::getUpgradeSubscriptionTariffID(
                $intNewServiceComponentID, $strContractLengthHandle, $strPaymentFrequencyHandle, $arrOptParams
            );
        }

        if (empty($intNewTariffID)) {

            // Egads!! It didn't return a Tariff ID! This is NOT good! We need to try a safer default
            $intNewTariffID = self::getUpgradeSubscriptionTariffID(
                $intNewServiceComponentID, 'MONTHLY', $strPaymentFrequencyHandle, $arrOptParams
            );

            if (empty($intNewTariffID)) {

                // Mah Gahd! Still no Tariff ID! We cannot continue here!!!
                require_once '/local/data/mis/database/database_libraries/programme-tool-access.inc';

                $strDescription =
                    "The Internet Connectivity Product Regrade method attempted to retrieve a tariff ID " .
                    "for the target product the user is regrading to. However, no valid tariff was found for the " .
                    "target product. This could be because either the user is regrading to an unsupported product, " .
                    "or because the tariffs have not been configured properly for the target product.";
                $strVerbose = "Service ID: {$this->getServiceID()}\n" .
                        "Target Product ID: {$intNewServiceComponentID}\n" .
                        "Contract Length: {$strContractLengthHandle}\n";
                pt_raise_autoproblem(
                    'AccountRegradeFailure',
                    'Internet Connection Regrade Failed: No Valid Tariff for target product',
                    $strDescription,
                    $strVerbose
                );
                return false;
            }

            // We successfully got a tariff ID for the new product, but it might be the wrong one. Raise a ticket on
            // the account to highlight the issue.
            $strTicketBody = "[Internal]\n" .
                    "The Account Regrade process encountered an issue when trying to change the customer's account. " .
                    "The system attempted to select a tariff matching the contract period of the old product " .
                    "({$strContractLengthHandle}) but this is invalid for the new product. The system has " .
                    "automatically selected a period of 'MONTHLY'. Please check the customer's account type and " .
                    "scheduled payments, and ensure that the contract period is correct.\n";

            $intTeamId = PHPLibTeamIdGetByName('Customer Support Centre');
            tickets_ticket_add('Script', $this->getServiceID(), '', '', 'Open', 0, $strTicketBody, 0, $intTeamId);
        }

        // Create a new instance for the new product
        $newComponent = self::create(
            $this->getServiceID(),
            $intNewServiceComponentID,
            $intNewTariffID,
            false,
            $arrProductComponentOptions
        );

        // Wipe out the current product
        $this->destroy();

        // Enable the new product
        $newComponent->enable(
            array(
                'objPaymentStart' => isset($arrOptParams['objPaymentStart']) ? $arrOptParams['objPaymentStart'] : null
            )
        );

        return $newComponent;
    }

    /**
     * Change account type
     *
     * @param integer $intOldComponentId
     * @param integer $intNewComponentId
     * @param boolean $bolSheduledChange
     * @param integer $uxtDateRefPoint
     * @return boolean
     */
    function changeAccountType($intOldComponentId, $intNewComponentId, $bolSheduledChange = FALSE, $uxtDateRefPoint)
    {
        return true;
    }

    /**
     * Get cost for service definition ID
     *
     * @param integer $intServiceDefinitionID
     * @param string $strType
     * @return float
     */
    public static function getCostForServiceDefinitionID($intServiceDefinitionID, $strType = 'SIGNUP')
    {
        $intServiceComponentID = CInternetConnectionProduct::getDefaultServiceComponentIDForServiceDefinitionID(
            $intServiceDefinitionID
        );
        $intServiceComponentID = (isset($intServiceComponentID) && $intServiceComponentID > 0) ?
            $intServiceComponentID : false;

        if (false !== $intServiceComponentID) {
            if ('SIGNUP' == $strType) {
                $intTariffID = self::getSignupSubscriptionTariffID($intServiceComponentID, 'MONTHLY', 'MONTHLY');
            }

            if ('UPGRADE' == $strType) {
                $intTariffID = self::getUpgradeSubscriptionTariffID($intServiceComponentID, 'MONTHLY', 'MONTHLY');
            }

            $intTariffID = (isset($intTariffID) && $intTariffID > 0) ? $intTariffID : false;

            if (false !== $intTariffID) {
                $arrTariffDetails = CProduct::getTariffDetails($intTariffID);

                if (isset($arrTariffDetails['intCostIncVatInPence']) && $arrTariffDetails['intCostIncVatInPence'] > 0) {
                    $floCost = $arrTariffDetails['intCostIncVatInPence'] / 100;
                    return $floCost;
                }
            }
        }
    }

    /**
     * Method to generate prorata payment details during component activation
     *
     * @param integer $uxtProRataCalculationDate
     * @return array
     */
    private function getActivationCostDetails($uxtProRataCalculationDate)
    {
        $intServiceID = $this->getServiceId();
        $arrService   = userdata_service_get($intServiceID);

        /*
         * If the component is activated on the same day as the next invoice of
         * BB, then we need to set the activation date as todays date as it
         * messes up the generateProRataPaymentDetails() as this gives a period
         * of more than a month to calculate the prorata cost.
         * ($uxtNextInvoice > $uxtMonthInterval) -> this is for any reason if
         * the edr hasnt run on any day and some accounts that needed to be
         * activated has their next_invoice moved
        */
        $uxtMonthInterval = strtotime(date('Y-m-d', $uxtProRataCalculationDate) . ' +1 month');
        $uxtNextInvoice   = strtotime($arrService['next_invoice']);

        if (($uxtNextInvoice > $uxtMonthInterval) || $uxtNextInvoice == strtotime(date('Y-m-d', time()))) {
            $uxtProRataCalculationDate = time();
        }

        // Currently we only charge for the subscription, you'll need to make
        // this more general if other chargeable components are added.
        $intSubscriptionId=CProductComponent::getProductComponentInstance(
            $this->getServiceId(), 'SUBSCRIPTION', array('ACTIVE'), 'INTERNET_CONNECTION'
        );
        $objSubscription = CProductComponent::createInstance($intSubscriptionId);

        $intTariffID      = $objSubscription->getTariffID();
        $arrTariffDetails = CProduct::getTariffDetails($intTariffID);

        $arrProRataCostDetails = CProductComponent::generateProRataPaymentDetails(
            $intServiceID,
            $arrTariffDetails['intNextTariffID'],
            'INTERNET_CONNECTION',
            $uxtProRataCalculationDate
        );

        return array(
            'arrProRataCostDetails'         => $arrProRataCostDetails,
            'arrTariffDetails'              => $arrTariffDetails,
            'intProductComponentInstanceId' => $objSubscription->getProductComponentInstanceID(),
        );
    }

    private function chargeProrataActivationCost()
    {
        $uxtProRataCalculationDate = time();
        $arrInvoicePeriod   = UserdataGetMonthlyInvoicePeriod($this->getServiceID());
        $uxtNextInvoiceDate = UserdataGetNextMonthlyInvoiceDate($this->getServiceID());

        $arrProrataCostDetails = $this->getActivationCostDetails($uxtProRataCalculationDate);
        $arrInvoiceItems = array();

        $intTotalCost = $arrProrataCostDetails['arrProRataCostDetails']['intProRataCost'];

        $helper = Reseller_DiscountHelper::getByServiceId(new Int($this->getServiceID()));

        if ($helper instanceof Reseller_DiscountHelper) {

            // get discount helper for specific product type
            $arrService = userdata_service_get($this->getServiceID());
            $discount = $helper->getDiscount(new Int($arrService['type']));

            // get the discounted price in pence
            $objDiscountedPrice = $discount->applyDiscountPence(new Int($intTotalCost));
            $intTotalCost = $objDiscountedPrice->getValue();
        }

        if ($intTotalCost <= 0) {
            return true;
        }

        $arrInvoiceItems[] = array(
            'amount'      => $intTotalCost / 100,
            'description' => $arrProrataCostDetails['arrProRataCostDetails']['strProRataDescription'],
            'gross'       => TRUE
        );

        $floTotalDisplayCost = number_format(($intTotalCost / 100), 2);

        // When does the period end?
        $uxtPeriodEndDate = $arrInvoicePeriod['uxtPeriodEnd'];

        //create scheduled payments for all required product components
        $objPaymentScheduler = new CProductComponentPaymentScheduler(
            $arrProrataCostDetails['intProductComponentInstanceId'], ''
        );
        $objPaymentScheduler->addIncVatAmount(
            $intTotalCost,
            $uxtNextInvoiceDate,
            $arrProrataCostDetails['arrProRataCostDetails']['strProRataDescription'],
            $uxtProRataCalculationDate,
            $uxtPeriodEndDate
        );
        $intSchedulePaymentID = $objPaymentScheduler->m_intScheduledPaymentID;

        if (!isset($intSchedulePaymentID) && $intSchedulePaymentID != '') {
            $strTicketBody =
                "A scheduled payment could not be raised for ".
                "&pound;{$arrProrataCostDetails['arrProRataCostDetails']['floProRataCostDisplay']}" .
                "for the {$arrDetails['arrProRataCostDetails']['strProRataDescription']}.".
                "Please take this payment manually.";

            tickets_ticket_add('Script', $this->getServiceID(), '', '', 'Open', 0, $strTicketBody);
        }
    }

    /**
     * Refreshes the current state of the component
     *
     * @param array $additionalArguments additional argument to refresh current status og internet connection component
     * 
     * @return boolean
     */
    public function refreshCurrentState($additionalArguments = array())
    {
        $objService = new Core_Service($this->getServiceID());

        switch ($this->getStatus())
        {
            // Don't use enable() because it's charging for subscription, just re-enable components and RADIUS
            case 'queued-reactivate':
                if (true == $this->enableProductComponents()) {

                    config_dialup_configurator($this->getComponentID(), 'auto_enable');
                    return true;
                }

                return false;
                break;

            // It's specific for signup component state, signal can be processed only if account is already active.
            // Used mainly when adding component by hand or component stucked in qa state
            case 'queued-activate':
                if ('active' == $objService->getStatus()) {
                    
                    $arrArgs = array(
                        'uxtEnableDate' => mktime(0, 0, 0, date('m') + 1, $objService->getInvoiceDay(), date('Y'))
                    );
                 
                    if (is_array($additionalArguments) && isset($additionalArguments['bolTakeProRataPayment'])) {
                    	
                        $arrArgs['bolTakeProRataPayment'] = $additionalArguments['bolTakeProRataPayment'];
                    }
                    return $this->enable($arrArgs);
                }

                return false;
                break;

            // Just disable component
            case 'queued-deactivate':
                return $this->disable();
                break;

            // Scheduled to destroy, need to finish a job
            case 'queued-destroy':
                return $this->destroy();
                break;

            // Re-enable/disable/destroy component instances if out of sync
            case 'active':
                return $this->enableProductComponents();
                break;

            case 'deactive':
                return $this->disableProductComponents();
                break;

            case 'destroyed':
                return $this->destroy();
                break;

            default:
                break;
        }

        return false;
    }

    /**
     * Component configurator function. Enables the component.
     *
     * @param array $arrArgs
     *
     * @return boolean
     */
    public function enable($arrArgs = array())
    {
        $intServiceIdToUse = userdata_service_is_resold($this->getServiceID());
        if (!$intServiceIdToUse) {

            $intServiceIdToUse = $this->getServiceID();
        }

        $arrService = userdata_service_get($intServiceIdToUse);

        $bolTakeProRataPayment = (isset($arrArgs['bolTakeProRataPayment'])) ?
            $arrArgs['bolTakeProRataPayment'] : false;
        $bolTakeFirstSubscription = isset($arrArgs['bolTakeFirstSubscription']) ?
            $arrArgs['bolTakeFirstSubscription'] : true;

        if ($arrService['isp'] == 'partner') {

            $bolTakeFirstSubscription  = (!$bolTakeProRataPayment);
            $intEnableDateAdvanceMonths = ($bolTakeProRataPayment == true)? 1 : 0;
            $arrArgs = array(
                'uxtEnableDate' => mktime(0, 0, 0, date('m')+$intEnableDateAdvanceMonths , $arrService['invoice_day'], date('Y')),
                'bolPartner'    => true
             );
        }

        $this->setStatus('queued-activate');
        $this->logDebug('status set to queued-activate');

        if (false === $this->enableProductComponents($arrArgs)) {

            $this->logDebug('enableProductComponents failed');
            return false;
        }

        $this->logDebug('enableProductComponents successful');

        $arrProductComponents = $this->listProductComponentInstanceIdByHandle();

        if (empty($arrProductComponents)) {
            $this->logDebug('listProductComponentInstanceIdByHandle failed');
            return FALSE;
        }

        $intSubscriptionProductComponentInstanceID = $arrProductComponents['SUBSCRIPTION'];

        if (false === CProductComponentSubscription::configureSubscriptions(
                                                        $intSubscriptionProductComponentInstanceID, $arrArgs)) {
            $this->logDebug('configureSubscriptions failed');
            return false;
        }
        $this->logDebug('configureSubscriptions successful');

        // charge prorata activation cost
        // If the rbm migratio  is complete then there is no need to charge it
        if ($bolTakeProRataPayment === true &&
            !FeatureToggleManager::isOnFiltered('RBM_MIGRATION_COMPLETE', null, null, null, $this->getServiceID())) {

            $this->chargeProrataActivationCost();
        }

/*
    PROBLEM 60534 - Duplication of Scheduled Payments on ADSL Activation
    YES, THIS IS IN CAPITALS AS IT'S IMPORTANT! :-D

    The following block of code has been commented out as it's believed that
    enableProductComponents() and friends actually create contracts and scheduled
    payments, etc.

    The $bolTakeFirstSubscription has already been configured to "FALSE" inside
    adslSignupMakeActive() but due to enable() being called from other parts of
    the codebase without any explicit mention of 'bolTakeFirstSubscription' the
    system will always create a scheduled payment that duplicate the ones created
    via enableProductComponents().

        if($bolTakeFirstSubscription) {

            $arrInternetConnectionProductComponents = $this->listProductComponentInstanceIdByHandle();

            if (empty($arrInternetConnectionProductComponents)) {

                $this->logDebug('listProductComponentInstanceIdByHandle failed');
                return FALSE;
            }

            $intSubscriptionProductComponentInstanceID = $arrInternetConnectionProductComponents['SUBSCRIPTION'];
            $objSubscriptionProductComponent = CProductComponentSubscription::createInstance($intSubscriptionProductComponentInstanceID);
            $intTariffId = $objSubscriptionProductComponent->getTariffID();
            $uxtNextInvoice = $objSubscriptionProductComponent->getNextInvoiceDate();

            $this->logDebug("about to generate scheduled payments, tariff ID = {$intTariffId}, next invoice = {$uxtNextInvoice}");
            $objStartDate = I18n_Date::fromTimestamp($uxtNextInvoice);
            $arrScheduledPaymentDetails = $objSubscriptionProductComponent->generateScheduledPaymentsDetailsForContract(
                                                                              $intTariffId, $objStartDate, $intPrepaidCount);
            $objSubscriptionProductComponent->createScheduledPayments($arrScheduledPaymentDetails);
        }
*/
        $this->logDebug('about to call config_dialup_configurator');
        config_dialup_configurator($this->getComponentID(), 'auto_enable');
        $this->logDebug('config_dialup_configurator completed');

        $this->deleteTransientInitialContractPaymentFlag();

        $this->refreshInstance($this->getComponentID());
        $this->logStatusChange('ACTIVE');
        $this->logDebug('activation event logged, process completed');

        return true;
    }



    /**
     * Component configurator function. Disables the component.
     *
     * @return boolean
     */
    public function disable()
    {
        $this->setStatus('queued-deactivate');

        if (false === $this->disableProductComponents()) {

            return false;
        }

        config_dialup_configurator($this->getComponentID(), 'auto_disable');

        $this->refreshInstance($this->getComponentID());
        $this->logStatusChange('DEACTIVE');

        return true;
    }

    /**
     * Component configurator function. Destroys the component.
     *
     * @param array $arrArgs
     * @return boolean
     */
    public function destroy($arrArgs = array())
    {
        $objComponentInstance = CProductComponent::createInstanceFromComponentID(
            $this->getComponentID(), 'SUBSCRIPTION'
        );

        // There can be no active subscription,
        // ie. for temporary upgrade accounts - allow to destroy such components as well
        if (true == ($objComponentInstance instanceof CProductComponent)) {

            // Contracts are issued up-front, to proper cancelation end of contract date need to be passed
            if (!isset($arrArgs['uxtCancelled']) || !is_numeric($arrArgs['uxtCancelled'])) {

                // If we've not specified the cancellation time then we need to figure it out for ourselves.
                // Previously, this used the contract end date, but this is incorrect since it doesn't properly
                // represent when the contract was actually ended, particularly if the contract is already
                // partway through its period
                $service = userdata_service_get($this->getServiceID());
                $billingDate = Core_BillingDate_Facade::getBillingDateFromDateAndPeriod(
                    $this->getServiceID(), $service['next_invoice'],
                    $service['invoice_period'], $service['invoice_day']
                );
                // 1. We're not passing in any other time to use, so we'll assume that this is occurring right now
                // 2. This next bit will retrieve the pro-rata period from the beginning of the current period
                //    upto the time we pass in, but the way this works is it rounds it to the nearest day, so this will
                //    actually be yesterday at 23:59:59
                $cInternetConnectionProductHelper = new CInternetConnectionProductHelper();
                $arrArgs = $cInternetConnectionProductHelper->setCancelledDate($arrArgs, $billingDate);
            }

            // Cancel issued scheduled payments related to destroyed component
            $objPaymentScheduler = new CProductComponentPaymentScheduler(
                $objComponentInstance->getProductComponentInstanceID(), ''
            );

            if (!$objPaymentScheduler->cancelAllOutstandingPayments()) { 
                error_log('ERROR: Failed to cancel all outstanding payments for serviceId ' .
                            $this->getServiceId() . ' componentId: ' . $this->getComponentID() .
                            "\nP1 has been raised for manual cancellation");
            }
        }

        $this->setStatus('queued-destroy');

        if (false === $this->destroyProductComponents($arrArgs)) {

            return false;
        }

        config_dialup_configurator($this->getComponentID(), 'auto_destroy');

        $this->refreshInstance($this->getComponentID());
        $this->logStatusChange('DESTROYED');

        return true;
    }

    /**
     * Component status change logger.
     *
     * @access private
     * @param string $strStatus
     */
    function logStatusChange($strStatus)
    {
        $objEventLogger = $this->prvGetEventLogger();

        switch ($strStatus) {
            case 'ACTIVE':
                $objEventLogger->logStatusChange('InternetConnectionActivation');
                break;
            case 'DEACTIVE':
                $objEventLogger->logStatusChange('InternetConnectionDeactivation');
                break;
            case 'DESTROYED':
                $objEventLogger->logStatusChange('InternetConnectionDestruction');
                break;
            default:
                break;
        }
    }

    /**
     * Is this component a Internent Connection component
     *
     * @param integer $intComponentID
     * @return boolean
     */
    public static function isInternetConnectionProduct($intComponentID = false)
    {
        if (false === $intComponentID) {
            return false;
        }

        $dbhConnection = get_named_connection_with_db('userdata');
        $intComponentID = addslashes($intComponentID * 1);

        $strQuery = "
            SELECT count(*) as intNumComponents
            FROM components c
            INNER JOIN products.tblServiceComponentProduct scp
              ON c.component_type_id = scp.intServiceComponentID
            INNER JOIN products.tblServiceComponentProductType scpt
              ON scp.intServiceComponentProductTypeID = scpt.intServiceComponentProductTypeID
              AND scpt.vchHandle = 'INTERNET_CONNECTION'
            WHERE c.component_id = '$intComponentID'";

        $refResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);
        $intNumComponents = PrimitivesResultGet($refResult, 'intNumComponents');

        if (0 < $intNumComponents) {
            return true;
        }

        return false;
    }

    /**
     * Returns all products that are available to be signed up to
     * in order of hierachy (ie lowest can upgrade to all others etc)
     *
     * @param string $strISP
     * @return array
     */
    public function getAvailableProductsForISP($strISP)
    {
        $dbhConnection = get_named_connection_with_db('product_reporting');
        $strQuery =
            'SELECT sd.service_definition_id as intServiceDefinitionID, ' .
            '       sc.service_component_id as intServiceComponentID, ' .
            '       sd.name as strProduct, ';

        if ($strISP == 'madasafish') {
            $strQuery.= 't.intCostIncVatPence as floCostInPence ';
        } else {
            $strQuery.= '((sd.minimum_charge * 100) + t.intCostIncVatPence) as floCostInPence ';
        }

        $strQuery.=
            '      FROM products.service_definitions sd ' .
            'INNER JOIN products.service_component_config scc ON sd.service_definition_id = scc.service_definition_id ' .
            'INNER JOIN products.service_components sc ON scc.service_component_id = sc.service_component_id ' .
            'INNER JOIN products.tblServiceComponentProduct scp ON scc.service_component_id = scp.intServiceComponentID ' .
            'INNER JOIN products.tblServiceComponentProductType scpt ON scp.intServiceComponentProductTypeID = scpt.intServiceComponentProductTypeID ' .
            '       AND scpt.vchHandle = "INTERNET_CONNECTION" ' .
            'INNER JOIN dbProductComponents.tblProductComponentConfig pcc ON scp.intServiceComponentProductID = pcc.intServiceComponentProductID ' .
            'INNER JOIN dbProductComponents.tblProductComponent pc ON pcc.intProductComponentID = pc.intProductComponentID ' .
            '       AND pc.vchHandle = "SUBSCRIPTION" ' .
            'INNER JOIN dbProductComponents.tblTariff t ON pcc.intProductComponentConfigID = t.intProductComponentConfigID ' .
            '       AND (t.dtmEnd IS NULL OR t.dtmEnd > NOW())' .
            'INNER JOIN dbProductComponents.tblPaymentFrequency pf ON t.intPaymentFrequencyID = pf.intPaymentFrequencyID ' .
            '       AND pf.vchHandle = "MONTHLY" ' .
            'INNER JOIN dbProductComponents.tblContractLength cl ON t.intContractLengthID = cl.intContractLengthID ' .
            '       AND cl.vchHandle = "MONTHLY" ' .
            "     WHERE sd.isp = '$strISP' " .
            '       AND sd.signup_via_portal = "Y" ' .
            '  GROUP BY sd.service_definition_id, sc.service_component_id ' .
            '  ORDER BY floCostInPence';

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);
        $arrProducts = PrimitivesResultsAsArrayGet($resResult, 'intServiceDefinitionID');
        return $arrProducts;
    }

    /**
     * For up-sell, retrieve the lowest starting price for broadband offered by the specified ISP
     *
     * @param string $strISP
     * @return float
     */
    public static function getStartPriceForISP($strISP)
    {
        // retrieve products available for the ISP, keyed by SDI
        $arrAvailableProducts = self::getAvailableProductsForISP($strISP);
        $arrCosts = array();

        // retrieve signup cost for each product
        foreach (array_keys($arrAvailableProducts) as $intSrvDefId) {
            $arrCosts[] = self::getCostForServiceDefinitionID($intSrvDefId);
        }

        // sort and return the lowest
        sort($arrCosts, SORT_NUMERIC);
        return reset($arrCosts);
    }

    /**
     * Gets the service component id for a service definition id that handles the internet connection product
     *
     * @param integer $intServiceDefinitionID
     * @return integer
     */
    public static function getDefaultServiceComponentIDForServiceDefinitionID($intServiceDefinitionID)
    {
        $dbhConnection = get_named_connection_with_db('product_reporting');
        $strQuery =
            '    SELECT sc.service_component_id ' .
            '      FROM service_components sc ' .
            'INNER JOIN service_component_config scc ON sc.service_component_id = scc.service_component_id ' .
            "       AND scc.service_definition_id = '$intServiceDefinitionID' " .
            'INNER JOIN tblServiceComponentProduct scp ON sc.service_component_id = scp.intServiceComponentID ' .
            'INNER JOIN products.tblServiceComponentProductType scpt ON scp.intServiceComponentProductTypeID = scpt.intServiceComponentProductTypeID ' .
            '       AND scpt.vchHandle = "INTERNET_CONNECTION" ' .
            '     WHERE sc.available = "Yes"';

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);
        return PrimitivesResultGet($resResult, 'service_component_id');
    }

    /**
     * Wrapper static method to handle logic for changing / moving details as part of an account change
     *
     * @param integer $intServiceID
     * @param integer $intNewServiceDefinitionID
     * @param string $strPaymentFrequencyHandle
     * @param bool $bolKeepCurrentContractLength
     * @param string $strContractLengthHandle
     * @param int $intMarketId
     * @param int $intSelectedTariffID
     * @param I18n_Date $objPaymentStart
     */

    public static function accountTypeChangeHandler($intServiceID,
        $intNewServiceDefinitionID, $strPaymentFrequencyHandle = 'MONTHLY',
        $bolKeepCurrentContractLength = false, $strContractLengthHandle ='',
        $intMarketId = null, $intSelectedTariffID = null, $objPaymentStart = null)
    {

        // Set contract length
        $objValueFamilyProduct = null;
        $objBpr09FamilyProduct = null;

        // We need to work out if the new service definition is a bpr09 or value family product...
        try {

            $objValueFamilyProduct = new ProductFamily_Value($intNewServiceDefinitionID);
            $bolIsValueFamily = true;

        } catch (ProductFamily_NotFamilyMember $objException) {

            $bolIsValueFamily = false;
        }

        try {

            $objBprFamilyProduct = new ProductFamily_Bpr09($intNewServiceDefinitionID);
            $bolIsBpr09Family= true;

        // For all the others MONTLY
        } catch (ProductFamily_NotFamilyMember $objException) {

            $bolIsBpr09Family = false;
        }





        // BPR09 - allow different contract lengths to be passed in,
        // but default to the old way of looking for a Value product
        //         if none is passed.

        if ('' == $strContractLengthHandle
            || !in_array($strContractLengthHandle, array('MONTHLY', 'ANNUAL', '24MONTH', 'QUARTERLY')) ) {

            if ($bolIsValueFamily) {

                $strContractLengthHandle = '18MONTH';
            } else {

                $strContractLengthHandle = 'MONTHLY';
            }

        }

        // Work out if we need to handle Internet Connection products and how we need to handle them
        $arrStatus = array(
            'unconfigured',
            'queued-activate',
            'queued-reactivate',
            'active',
            'queued-deactivate',
            'deactive'
        );

        $intInternetConnectionComponentID = CProduct::getComponentIDByServiceID(
            $intServiceID, 'INTERNET_CONNECTION',
            CInternetConnectionProduct::getActiveStatuses()
        );

        if (isset($intInternetConnectionComponentID) && $intInternetConnectionComponentID > 0) {
            $objInternetConnection = CProduct::createInstance($intInternetConnectionComponentID);
        }

        $objInternetConnection =
            (isset($objInternetConnection) && is_object($objInternetConnection)) ? $objInternetConnection : false;




        // If we have the selected tariff, it means we have already have decide for the service component
        // the account should have. We need not use the market id to figure out the service component again.
        // So we will use use the tariff id to find the service component.
        if ($intSelectedTariffID != null) {

            $arrTariffDetails = CProduct::getTariffDetails($intSelectedTariffID);

            if (isset($arrTariffDetails['intServiceComponentID'])) {

                $intNewServiceComponentID = $arrTariffDetails['intServiceComponentID'];

            }

        }

        if ($intMarketId != null &&  !(isset($intNewServiceComponentID) && ctype_digit($intNewServiceComponentID))) {
            try {
                $intNewServiceComponentID =
                    ProductFamily_InternetConnectionHelper::getId($intNewServiceDefinitionID, $intMarketId);
            } catch (RuntimeException $exception) {
                // This function throwing an exception if there is no internet component
                // Any we need to proceed even there is no market component
            }
        }
        if ($intMarketId == null || !(isset($intNewServiceComponentID) && $intNewServiceComponentID > 0)) {
            $intNewServiceComponentID =
                CInternetConnectionProduct::getDefaultServiceComponentIDForServiceDefinitionID($intNewServiceDefinitionID);
        }



        $intNewServiceComponentID =
            (isset($intNewServiceComponentID) && $intNewServiceComponentID > 0) ? $intNewServiceComponentID : false;
        $arrProductComponentOptions = array();

        $arrOptParams = array(
            'strContractLengthHandle'   => $strContractLengthHandle,
            'strPaymentFrequencyHandle' => $strPaymentFrequencyHandle,
            'bolValueFamily'            => $bolIsValueFamily,
            'bolBpr09Family'            => $bolIsBpr09Family,
            'intSelectedTariffID'       => $intSelectedTariffID,
            'objPaymentStart'           => $objPaymentStart,
        );

        if (false !== $objInternetConnection && false !== $intNewServiceComponentID) {

            $arrOptParams['bolKeepCurrentContractLength'] = $bolKeepCurrentContractLength;

            $objInternetConnection->doUpgrade($intNewServiceComponentID, $arrProductComponentOptions, $arrOptParams);
        } elseif (false === $objInternetConnection && false !== $intNewServiceComponentID) {

            CInternetConnectionProduct::doSignup(
                $intServiceID, $intNewServiceComponentID, $arrProductComponentOptions, $arrOptParams
            );
        } elseif (false !== $objInternetConnection && false === $intNewServiceComponentID) {

            // Need to end current
            $objInternetConnection->destroy();
        }
    }

    /**
     *
     * @return CProductComponent
     */
    public function getConnectivityProductComponentObject()
    {
        $bolFalse = FALSE;

        // hmm, I wonder if we need more/less product component statuses here?
        $objConnectivityProductComponentInstance =
            CProductComponent::createInstanceFromComponentID(
                $this->getComponentID(), 'CONNECTIVITY',
                self::getActiveStatusesForProductComponent('CONNECTIVITY')
            );

        if (!is_object($objConnectivityProductComponentInstance)) {
            return $bolFalse;
        }

        return $objConnectivityProductComponentInstance;
    }

    /**
     * Returns back the basic component statuses for what represent an 'active'
     * (personally I prefer the word 'available') Internet Connection product
     *
     * @return array
     */
    public static function getActiveStatuses()
    {
        return array(
            'queued-activate',
            'queued-reactivate',
            'active',
            'deactive'
        );
    }

    /**
     * Similar to getActiveStatuses(), except this one works on a product component
     *
     * @param string $strProductComponentHandle
     * @return array
     */
    public static function getActiveStatusesForProductComponent($strProductComponentHandle)
    {
        // OK, these statuses are the same at the moment, but you can't assume that
        // to be the case until development/testing is completed, I 'spose.
        $arrStatuses = array(
            'SUBSCRIPTION' => array(
                'ACTIVE',
                'QUEUED_ACTIVATE',
                'QUEUED_REACTIVATE'
            ),
            'CONNECTIVITY' => array(
                'ACTIVE',
                'QUEUED_ACTIVATE',
                'QUEUED_REACTIVATE'
            )
        );

        if (!isset($arrStatuses[$strProductComponentHandle]) || !is_array($arrStatuses[$strProductComponentHandle])) {
            return array();
        }

        return $arrStatuses[$strProductComponentHandle];
    }

    /**
     * Return back simplified summary of info about this product
     *
     * @return mixed FALSE = error
     *               empty array = no additional data worth mentioning (unlikely!)
     *               populated array = additional info, structured in key=>array(value) pairs,
     *               or key=>FALSE if 'key' data was unaccessible at the time
     */
    public function getExtendedSummaryInfo()
    {
        $objConnectivity = $this->getConnectivityProductComponentObject();
        // no object would be an error

        if (!is_object($objConnectivity)) {
            return FALSE;
        }

        $arrAddress = $objConnectivity->getInstallationAddress();
        // returning FALSE instead of an array is also an error

        if (FALSE === $arrAddress) {
            return FALSE;
        }

        // less likely, but an empty array isn't an error (although tbh is a bit weird!)
        if (is_array($arrAddress) && empty($arrAddress)) {
            return array();
        }

        // build a simplified string of the address components that are populated,
        // seperating each one with a comma
        $arrAddressKeys = array('house', 'street', 'town', 'county', 'postcode', 'country');

        // doing it this way as to avoid stuff like "24, Fake street,, F41 2KE",
        // i.e. the two commas next to each other
        $arrAddressComponents = array();

        foreach ($arrAddressKeys as $strAddressKey) {
            $strAddressComponent = (isset($arrAddress[$strAddressKey])) ? trim($arrAddress[$strAddressKey]) : '';

            if ('' !== $strAddressComponent) {
                $arrAddressComponents[] = $strAddressComponent;
            }
        }

        $strAddress = implode(', ', $arrAddressComponents);

        // return everything neat 'n' tidy
        return array(
            'arrRawAddress' => array($arrAddress),
            'strAddress' => array($strAddress)
        );
    }

    /**
     * Added just for problem 52117 - not to be used for anything else as it *will* be removed
     * once that problem is out the way.
     *
     * @param string $strMessage
     */
    public static function appendSharedErrorLog($strMessage)
    {
        shared_error_log("::SS::CICP:" . $strMessage, TRUE);
    }

    /**
     * Added just for problem 52117 - not to be used for anything else as it *will* be removed
     * once that problem is out the way.
     *
     * @todo laugh cynically at the optimism of the description above
     * @param string $strMessage
     */
    private function logDebug($strMessage)
    {
        $intServiceId = $this->getServiceID();
        $intComponentId = $this->getComponentID();

        $strMessage = "SID {$intServiceId}, CID {$intComponentId} : {$strMessage}";

        self::appendSharedErrorLog($strMessage);
    }

    /**
     * Handle legacy component signaling,
     * $strSignal handle signals: auto_configure, auto_refresh, auto_disable, auto_enable, auto_destroy
     *
     * @param integer $intComponentId      Id of component
     * @param string  $strSignal           legacy component signals e.g 'auto_configure' etc
     * @param array   $additionalArguments additional argument to config internet component 
     *
     * @return void
     * @throws Exception
     * 
     * <AUTHOR> Kurylowicz
     * @since 23-09-2008
     */
    public static function handleLegacyComponentSignaling($intComponentId, $strSignal, $additionalArguments = array())
    {
        $objInternetConnection = new CInternetConnectionProduct($intComponentId);

        if ($objInternetConnection->getError() instanceof CError) {

            throw new Exception($objInternetConnection->getError()->getErrorMessage());
        }

        $strCurrentStatus = $objInternetConnection->getStatus();

        switch ($strSignal)
        {
            case "auto_configure":
            case "auto_refresh":
                if (true == $objInternetConnection->refreshCurrentState($additionalArguments)) {

                    $objInternetConnection->refreshInstance($intComponentId);

                    if ('active' == $objInternetConnection->getStatus()) {

                        config_dialup_configurator($objInternetConnection->getComponentID(), $strSignal);
                    }

                    return;
                }

                throw new Exception("Unable to refresh state of component id: {$intComponentId}");
                break;

            case "auto_disable":
                // Don't process components in non-active state
                if ('active' <> $strCurrentStatus) {

                    return;
                }

                if (true == $objInternetConnection->disable()) {

                    return;
                }

                throw new Exception("Unable to disable component id: {$intComponentId}");
                break;

            case "auto_enable":
                // Don't reactivate component in non-deactive status
                // P64018 - if account is re-enable before the component is fully deactivated the deactivation
                //   occurs later and leaves account in broken state.
                if (!in_array($strCurrentStatus, array('deactive', 'queued-deactivate'))) {

                    return;
                }

                // Don't use enable() because it's charging for subscription
                if (true == $objInternetConnection->enableProductComponents()) {

                    config_dialup_configurator($objInternetConnection->getComponentID(), 'auto_enable');
                    return;
                }

                throw new Exception("Unable to enable component id: {$intComponentId}");
                break;

            case "auto_destroy":
                if (true == $objInternetConnection->destroy()) {

                    return;
                }

                throw new Exception("Unable to destroy component id {$intComponentId}");
                break;

            default:
                throw new Exception(
                    "Durring processing component id: {$intComponentId} catched unsupported signal: {$strSignal}"
                );
                break;
        }
    }

    /**
     * Validates the supplied cost level handle
     *
     * @param string $strCostLevelHandle
     *
     * @return bool
     */
    public static function isValidPricePlan($strCostLevelHandle)
    {
        $dbhConnection = get_named_connection_with_db('dbProductComponents_reporting');
        $strQuery = "
            SELECT
                intPricePlanId
            FROM
                tblPricePlan
            WHERE
                vchHandle = '%s'
        ";

        $strQuery = sprintf(
            $strQuery,
            mysql_real_escape_string($strCostLevelHandle)
        );
        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);
        $intPricePlanId = PrimitivesResultGet($resResult, 'intPricePlanId');

        return (true == $intPricePlanId);
    }

    /**
     * Returns product details.
     *
     * This query will returns the cost level and
     * contract length of the product
     *
     * @param int    $serviceId
     *
     * @return string $arrDetails
     */
    public static function getServiceProductDetails($intServiceId)
    {
        $dbhConnection = get_named_connection_with_db('product_reporting');

        $strQuery = "SELECT pp.vchDescription AS costLevel,
                        cl.vchDisplayName AS contractLength
                   FROM userdata.components c
             INNER JOIN products.service_components sc
                     ON c.component_type_id = sc.service_component_id
             INNER JOIN products.tblServiceComponentProduct scp
                     ON sc.service_component_id = scp.intServiceComponentID
             INNER JOIN products.tblServiceComponentProductType scpt
                     ON scp.intServiceComponentProductTypeID = scpt.intServiceComponentProductTypeID
             INNER JOIN userdata.tblProductComponentInstance pci
                     ON pci.intComponentID = c.component_id
             INNER JOIN dbProductComponents.tblTariff t
                     ON t.intTariffID = pci.intTariffID
             INNER JOIN dbProductComponents.tblTariffPricePlan tpp
                     ON tpp.intTariffId = t.intTariffID
             INNER JOIN dbProductComponents.tblPricePlan pp
                     ON pp.intPricePlanId = tpp.intPricePlanId
             INNER JOIN dbProductComponents.tblContractLength cl
                     ON t.intContractLengthID = cl.intContractLengthID
                  WHERE scpt.vchHandle = 'INTERNET_CONNECTION'
                    AND c.status NOT IN ('queued-destroy', 'destroyed')
                    AND c.service_id = '%d' ";

        $strQuery = sprintf(
            $strQuery,
            mysql_real_escape_string($intServiceId)
        );

        $result = PrimitivesQueryOrExit($strQuery, $dbhConnection);
        $arrDetails = PrimitivesResultGet($result);

        return $arrDetails;
    }

}
