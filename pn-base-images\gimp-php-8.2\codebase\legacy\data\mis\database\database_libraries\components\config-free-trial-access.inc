<?php

    /////////////////////////////////////////////////////////////////////
    // File:     config-free-trial-access.inc
    // Purpose:  Access mini-library for Free Trial Component
	//
	// Requires: free_trial-access.inc
	//
	// Auth: DWales
	//
	// Don't call the manual_configure function from anywhere unless
	// you fully understand the principles behind the free trial system
	// alterations could have an effect on abuse preventatives built in
	// to this system. Changes to this code will result in death by 
	// large strick inserted into painful area.
    /////////////////////////////////////////////////////////////////////

	require_once("/local/data/mis/database/database_libraries/product-access.inc");

    /////////////////////////////////////////////////////////////////////
    // Functions
    //
	// Misc
	// ----
	//
	// calculate_upgrade_account_cost
	//
    // Write functions
    // ---------------
    //
    // config_free_trial_manual_configure
    //
    // Read functions
    // --------------
    //
    // config_free_trial_get
    //
    // Delete functions
    // ----------------
    //
    // config_free_trial_auto_destroy
    //
    /////////////////////////////////////////////////////////////////////


	/////////////////////////////////////////////////////////////////////
	// Data

   	$global_component_configurators["170"]  = "config_free_trial_configurator";
    
    	// Hack to insert the component configurator array into PHP5's global scope
    	// if it's not already there
	if(!isset($GLOBALS['global_component_configurators']))
	{
		$GLOBALS['global_component_configurators'] = $global_component_configurators;
	}
	else
	{
		foreach($global_component_configurators as $intIndex => $strConfigurator)
		{
			$GLOBALS['global_component_configurators'][$intIndex] = $strConfigurator;
		}
	}


	//////////////////////////////////////////////////////////////////////
	// Function calculate_upgrade_account_cost($new_type_id,$user_id)
	//
	// Desc: Calculates the charge to be made to a users account if
	// they upgrade to $new_type_id account in 10 days time.
	// this must be called prior to altering any invoice dates.
	//
	// Returns: array(
	//					"result" 
	//					"val"
	//				)
	// if "result" == "error" then "val" will contain the error message
	// else if "result" == "success" then "val" will contain a price value.
	//////////////////////////////////////////////////////////////////////
	function calculate_upgrade_account_cost($new_type_id,$user_id)
	{
		$avg_days_per_month = 30.4375;

		$chan = get_named_connection("userdata");

		// Get the current account details.

		$sqlstr = "SELECT type,invoice_period,unix_timestamp(next_invoice) as next_invoice FROM services WHERE user_id = '$user_id'";

		$result_id = mysql_query($sqlstr,$chan)  
			or report_error (__FILE__, __LINE__, mysql_error ($chan));

		$user_info = mysql_fetch_array($result_id,MYSQL_ASSOC);

		mysql_free_result($result_id);

		//Check to see if payment period is never.
		if($user_info["invoice_period"] == "never")
		{
			return array("result"=>"success",
					 	 "val"=>"0.00");
		}

		if($user_info["next_invoice"] == "0")
		{
			return array("result"=>"error",
						 "val"=>"next_invoice date is not set correctly on this account.");
		}

		// Get current daily account costs

		$current_daily_cost = round((product_get_service_price($user_info["type"], 0) / $avg_days_per_month),2);
		$new_daily_cost     = round((product_get_service_price($new_type_id, 0) / $avg_days_per_month),2);

		// How many days are left until invoice date
			
		$today_timestamp = strtotime(date("d F Y",time()));
		$days_paid_for = ceil( ($user_info["next_invoice"] - $today_timestamp) / 86400 );

		// Invoice date can't be set in the past while assigning this component.

		if($days_paid_for < 0)
		{
			return array("result"=>"error",
						 "val"=>"next_invoice date is in the past.");
						 
		}

		// User can't pay for more than a year in advance while assigning this component.

		if($days_paid_for > 366)
		{
			return array("result"=>"error",
						 "val"=>"next_invoice date is more than a year from now.");
		}

		$extra_daily_cost = round(($new_daily_cost - $old_daily_cost),2);

		if($extra_daily_cost < 0)
		{
			return array("result"=>"error",
						 "val"=>"extra daily cost calculated to a minus figure.");

		}

		$total_upgrade_charge = round(($extra_daily_cost * $days_paid_for),2);

		if($total_upgrade_charge < 0)
		{
               return array("result"=>"error",
                            "val"=>"upgrade charge calculated to a minus figure.");
		}

		return array("result"=>"success",
					 "val"=>"$total_upgrade_charge");
	}

	/////////////////////////////////////////////////////
	// Function: config_free_trial_standard_configure
	//
	// To be called prior to changing a users account.
	// Returns: False if failed else free_trial_id.
	/////////////////////////////////////////////////////

	function config_free_trial_standard_configure($component_id, $trial_account_type, $trial_cli_number,
												  $service_id)
	{

		$trial_period_length_days = 10;

		$trial_cli_number = str_replace(" ","",$trial_cli_number);
		$trial_cli_number = str_replace("-","",$trial_cli_number);

		// Get the extra info needed.

		$sqlstr = "SELECT next_invoice, user_id, type FROM services where service_id = '$service_id'";

		$chan = get_named_connection("userdata");

		$result_id = mysql_query($sqlstr,$chan);

		$row = mysql_fetch_array($result_id,MYSQL_ASSOC);

		$trial_original_account_type = $row["type"];

		// If these match then the account is a new signup
		if($trial_original_account_type == $trial_account_type)
		{
			$trial_original_account_type = "0";
		}

		$trial_original_account_invoice_date = $row["next_invoice"];
		$user_id = $row["user_id"];

		mysql_free_result($result_id);

		$trial_takeup_charge_array = calculate_upgrade_account_cost($trial_account_type,$user_id);

		if($trial_takeup_charge_array["result"] != "success")
		{
			return $trial_takeup_charge_array;
		}

		$trial_takeup_charge = $trial_takeup_charge_array["val"];

		// Configure component now.

		$sqlstr =	"INSERT INTO config_free_trial (free_trial_id,component_id,trial_period_length_days,".
					"trial_period_start_date,trial_period_end_date,trial_account_type,trial_original_account_type,".
					"trial_original_account_invoice_date,trial_takeup_charge,trial_cli_number,trial_user_id,".
					"trial_component_status) VALUES (".
					"NULL,'$component_id','$trial_period_length_days',NOW(),".
					"DATE_ADD(NOW(), INTERVAL $trial_period_length_days DAY),'$trial_account_type','$trial_original_account_type',".
					"'$trial_original_account_invoice_date','$trial_takeup_charge','$trial_cli_number',".
					"'$user_id','active')";

		$chan = get_named_connection("userdata");

		if(!mysql_query($sqlstr,$chan))
		{
			return FALSE; 
		}

		$return_id = mysql_insert_id($chan);

		// Now we can move the users invoice date forward so many days to stop any billing while on trial.
		$sqlstr = "UPDATE services set next_invoice = DATE_ADD(next_invoice, INTERVAL $trial_period_length_days DAY) ".
				  "WHERE service_id = $service_id";

		$chan = get_named_connection("userdata");

		if(!mysql_query($sqlstr,$chan))
		{
			return FALSE; 
		}

		return array("result"=>"success",
					 "val"=>"$return_id");
	}

	/////////////////////////////////////////////////////
	// Function: config_free_trial_get
	//
	/////////////////////////////////////////////////////

	function config_free_trial_get($component_id)
	{

		$sqlstr = "SELECT * FROM config_free_trial component_id = '$component_id'";

		$chan = get_named_connection("userdata");

		$result_id = mysql_query($sqlstr,$chan);
		$row = mysql_fetch_array($result_id,MYSQL_ASSOC);

		if(empty($result_id))
		{
			return FALSE;
		}

		return $row;
	}

	/////////////////////////////////////////////////////
	// Function: config_free_trial_auto_destroy
	//
	/////////////////////////////////////////////////////

	function config_free_trial_auto_destroy($component_id)
	{
		$sqlstr = "UPDATE config_free_trial SET trial_component_status = 'deactive' WHERE component_id = '$component_id'";

		$chan = get_named_connection("userdata");

		if(!mysql_query($sqlstr,$chan))
		{
			return FALSE;
		}

		return TRUE;
	}


	/////////////////////////////////////////////////////
	// Function: config_free_trial_configurator
	//
	/////////////////////////////////////////////////////

	function config_free_trial_configurator ($component_id, $action)
	{

		switch ($action)
		{
			case 'auto_configure':
				break;

			case 'auto_refresh':
				break;

			case 'auto_disable':
				break;

			case 'auto_enable':
				break;

			case 'auto_destroy':
				config_free_trial_auto_destroy ($component_id);
				break;

			default:
				break;
		}
	}

?>
