<?php
/**
* BTHardwareBundleSupplier class
* Encapsulates a hardware bundle Supplier
*
* @param m_strClassPrefix
* @param m_strSupplierTag
* @todo - add missing param's (as needed)
*
* @package    DatabaseAccessCommon
* @subpackage application_apis
* @access     public
* <AUTHOR>
*
*/
class BTHardwareBundleSupplier extends HardwareBundleSupplier
{
    //
    // PRIVATE Members
    //

    /**
     * The Class Prefix
     *
     * A String that is the prefix of this class
     *
     * @access private
     * @var    String
     */
    var $m_strClassPrefix         = 'BT';


    /**
     * The Supplier Tag
     *
     * A String that is this suppliers tag
     *
     * @access private
     * @var    String
     */

    var $m_strSupplierTag         = 'BT';



    //
    // Constructor
    //

    /**
     * BTHardwareBundleType constructor
     *
     * The constructor method for this class
     *
     * @return void
     */
    function BTHardwareBundleSupplier()
    {
        // Populate the server connection variables
        $this->HardwareBundleSupplier();
    }

    //
    // Protected Methods
    //


    /**
     * SendOrders
     *
     * @param array $arrHardwareBundles HardwareBundles
     *
     * @see HardwareBundleSupplier::SendOrders()
     *
     * @return void
     */
    function SendOrders($arrHardwareBundles)
    {
        $arrBundleDetails = array();

        foreach ($arrHardwareBundles as $HardwareBundle) {
            // get all hardware details and save to the table
            $arrBundleDetails[] = $HardwareBundle->GetOrderDetails();
        }
        // Save the order to the order database
        if ($this->SaveOrderToDatabase($arrBundleDetails) == true) {
            // Update the bundles
            foreach ($arrHardwareBundles as $HardwareBundle) {
                //For RMA's, also pass the item IDs that require updating.
                if ($HardwareBundle->m_strOverallStatusTag == 'awaiting_rma') {
                    $arrBundleItemIds = array();
                    foreach ($HardwareBundle->m_arrItems as $item) {
                        if ($item['strStatusTag'] == 'rma_in_progress') {
                            array_push($arrBundleItemIds, $item['intID']);
                        }
                    }
                } else {
                    $arrBundleItemIds = false;
                }

                $HardwareBundle->SetWhenOrdered();
                //only update items that are in relevant status

                $HardwareBundle->SetStatusTag(-1, 'awaiting_processing', $arrBundleItemIds);

                // Commit to the database
                if ($HardwareBundle->Commit() == true) {
                    $HardwareBundle->sendOrderedSms();
                    $HardwareBundle->AddContact('Order placed for hardware bundle');
                } else {
                    error_log(
                        "BTHardwareBundleSupplier::SendOrders - ".
                        "The following Hardware Bundle object did not Commit() for some reason:\n " .
                        dump_structure_to_string($HardwareBundle)
                    );
                }
            }

            return true;
        } else {
            return false;
        }
    }

    /**
     * SaveOrderToDatabase
     *
     * @param array $arrBundleDetails BundleDetails
     *
     * @return boolean
     */
    function SaveOrderToDatabase($arrBundleDetails)
    {
        $dbhConnection = get_named_connection_with_db('common_adsl');

        foreach ($arrBundleDetails as $arrBundle) {
            $intCount = 1;

            foreach ($arrBundle as $arrItem) {
                extract($arrItem, EXTR_OVERWRITE);

                $strQuery = 'SELECT intHWCommOrderTypeID FROM vblHWCommOrderType '.
                'WHERE vchHardwareOrderStatusTag = "' . $strStatusTag .'"';

                $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

                $arrResult = PrimitivesResultsAsArrayGet($resResult);

                $intOrderType = $arrResult[0]['intHWCommOrderTypeID'];

                $arrDate = explode("/", $datDate);

                $datDate = $arrDate[2] . '-' . $arrDate[1] . '-' . $arrDate[0];

                $strInsert = 'INSERT INTO tblHWOrderRecord '
                        .   ' SET intHWCommOrderTypeID         = %d, '
                        .       ' vchName                      = "%s", '
                        .       ' vchAddress1                  = "%s", '
                        .       ' vchAddress2                  = "%s", '
                        .       ' vchAddress3                  = "%s", '
                        .       ' vchAddress4                  = "%s", '
                        .       ' vchPostcode                  = "%s", '
                        .       ' intPartnerID                 =  %d, '
                        .       ' intConfigHardwareBundleID    =  %d, '
                        .       ' intLine                      =  %d, '
                        .       ' vchOrderNotes                = "%s", '
                        .       ' dteRequiredDate              = "%s", '
                        .       ' vchItemID                    = "%s", '
                        .       ' intQuantity                  =  %d ';

                $strInsert = sprintf(
                    $strInsert,
                    mysql_real_escape_string($intOrderType),
                    mysql_real_escape_string($strFullName),
                    mysql_real_escape_string($strAddress1),
                    mysql_real_escape_string($strAddress2),
                    mysql_real_escape_string($strAddress3),
                    mysql_real_escape_string($strAddress4),
                    mysql_real_escape_string($strPostcode),
                    mysql_real_escape_string($intPartnerID),
                    mysql_real_escape_string($intOrderID),
                    mysql_real_escape_string($intCount),
                    mysql_real_escape_string($strOptionalNotes),
                    mysql_real_escape_string($datDate),
                    mysql_real_escape_string($intProductID),
                    mysql_real_escape_string($intQuantity)
                );

                PrimitivesQueryOrExit($strInsert, $dbhConnection);

                $intCount++;
            }
        }

        return true;
    }

    /**
     * Poll for Orcer Acknowledments()
     *
     * Poll the supplier for order acknowledgements and update the accounts as neccessary
     *
     * @access protected
     * @return Array - array of errors
     */
    function PollForOrderAcknowledgements()
    {
        //An array to hold an errors we may want to return
        $arrErrors = array();

        return $arrErrors;
    }

    /**
     * PollForOrderUpdates
     *
     * @param string $strNotOlderThan           Ordered date
     * @param string $intConfigHardwareBundleId ConfigHardwareBundleId
     *
     * @see HardwareBundleSupplier::PollForOrderUpdates()
     *
     * @return void
     */
    function PollForOrderUpdates($strNotOlderThan = null, $intConfigHardwareBundleId = null)
    {
        //Query the XFB tables for any updates
        //Task 335593 - moving this from coredb master to slave.
        $dbhConnection = get_named_connection_with_db('userdata_reporting');

        // Get a list of components that are needing updating
        $strQuery = <<<EOQ
SELECT DISTINCT
    chb.component_id AS intComponentID
FROM
    dbCommonADSL.tblHWFeedbackRecord            AS hwfr
    INNER JOIN userdata.config_hardware_bundles AS chb ON hwfr.intConfigHardwareBundleID = chb.config_hardware_bundle_id
    LEFT JOIN userdata.tblHardwareRmaProcess    AS rma ON chb.config_hardware_bundle_id = rma.usiConfigHardwareBundleID
WHERE
    hwfr.intPartnerID = 0
    AND hwfr.dteIncidentDate IS NULL
    AND rma.usiHardwareRmaProcessID IS NULL
EOQ;

        if (!empty($intConfigHardwareBundleId)) {
            $strQuery .= sprintf(' AND chb.config_hardware_bundle_id = %d ', (int) $intConfigHardwareBundleId);
        }

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);
        if ($resResult) {
            $arrResults = PrimitivesResultsAsArrayGet($resResult);
            if (empty($arrResults)) {
                $this->setMessage(date('H:i:s')." No Orders to update\n");
            }

            foreach ($arrResults as $arrRow) {
                $intComponentID = $arrRow['intComponentID'];

                // Create the bundle component
                $HardwareBundle = new BTHardwareBundle();

                // Initialise the component
                $HardwareBundle->GetByComponentID($intComponentID);

                $this->setMessage(date('H:i:s')." Updating Component Id: ".$intComponentID."\n");
                // Ask the component to poll for an update
                $HardwareBundle->PollForOrderUpdate();

                $this->setMessage($HardwareBundle->getMessage());
            }
        }
    }


    /**
     * Look in the H/W feedback table for RMA updates
     *
     * @access public
     * @see application_apis/HardwareBundle/HardwareBundleSupplier#PollForRmaUpdates()
     *
     * @return void
     */
    function PollForRmaUpdates()
    {
        //Query the XFB tables for any updates

        $dbhConnection = get_named_connection_with_db('userdata_reporting');

        // Get a list of components that are needing updating
        $strQuery = <<<EOQ
SELECT DISTINCT
    rma.usiHardwareRmaProcessID
FROM
    dbCommonADSL.tblHWFeedbackRecord            AS hwfr
    INNER JOIN userdata.config_hardware_bundles AS chb ON hwfr.intConfigHardwareBundleID = chb.config_hardware_bundle_id
    INNER JOIN userdata.tblHardwareRmaProcess   AS rma ON chb.config_hardware_bundle_id = rma.usiConfigHardwareBundleID
WHERE
    hwfr.intPartnerID = 0
    AND hwfr.dteIncidentDate IS NULL
EOQ;

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

        $strClassName = $this->m_strClassPrefix . 'HardwareRmaProcess';

        if ($resResult) {
            $arrResults = PrimitivesResultsAsArrayGet($resResult);

            foreach ($arrResults as $arrRow) {
                $intRmaID = $arrRow['usiHardwareRmaProcessID'];

                // Create the bundle component
                $objRma = new $strClassName();

                // Initialise the component
                $objRma->GetByID($intRmaID);

                // Ask the component to poll for an update
                $objRma->PollForUpdate();
            }
        }
    }


    /**
     *  Get the latest Order Status
     *
     * Fetch the latest status for a hardware bundle - and the tracking order number from the supplier
     *
     * @param Object $HardwareBundle - The Hardware Bundle
     *
     * @access protected
     *
     * @return Array - The Order Status and tracking number
     */
    function FetchLatestOrderStatus($HardwareBundle)
    {
        // Get the order number
        // Now all we need to do is query the database and not mess about
        // with any files.
        $intOrderNumber = $HardwareBundle->m_intID;

        $intPartnerID = $HardwareBundle->m_intPartnerID;

        $strSQL = 'SELECT CFT.vchHandle, '
                .       ' FR.vchCarrierRef, '
                .       ' FR.vchSerialNumber, '
                .       ' FR.vchOuiNumber, '
                .       ' FR.vchItemID, '
                .       ' HOR.intLine '
                .  ' FROM tblHWFeedbackRecord FR, '
                .       ' vblHWCommFeedbackType CFT, '
                .       ' tblHWOrderRecord AS HOR '
                . ' WHERE FR.intHWCommFeedbackTypeID = CFT.intHWCommFeedbackTypeID '
                . ' AND FR.vchItemID = HOR.vchItemID '
                . ' AND FR.intConfigHardwareBundleID = HOR.intConfigHardwareBundleID '
                .   " AND FR.intConfigHardwareBundleID = '$intOrderNumber' "
                .   " AND FR.intPartnerID = '$intPartnerID' "
                .   ' AND FR.dteIncidentDate IS NULL '
                .   ' ORDER BY intHWFeedbackRecordID DESC';

        $dbhConnectionReporting = get_named_connection_with_db('common_adsl_reporting');
        $dbhConnection = get_named_connection_with_db('common_adsl');

        $resResult = PrimitivesQueryOrExit($strSQL, $dbhConnectionReporting);

        $arrResults = PrimitivesResultsAsArrayGet($resResult);

        $arrStatusTag = array();

        $bolUpdateField = false;

        if (count($arrResults) > 0) {
            $strReturnType = $arrResults[0]['vchHandle'];

            switch ($strReturnType) {
                case 'SHP':
                    // Item has been shipped but not delivered.
                    // A suitable order status: dispatched
                    $strStatusTag = 'dispatched';
                    $bolUpdateField = true;
                    break;

                case 'POD':
                    // Item has been confirmed as delivered
                    // New status for item: delivered
                    $strStatusTag = 'delivered';
                    $bolUpdateField = true;
                    break;

                case 'RTN':
                    // Item has been returned successfully
                    // New status for item: rma_complete
                    $strStatusTag = 'rma_complete';
                    $bolUpdateField = true;
                    break;

                default:
                    $this->RaiseProblem(__FILE__, __LINE__, 'Incorrect return type from BT feedback file', true);
                    break;
            }

            $arrStatusTag['strStatus'] = $strStatusTag;
            $arrStatusTag['strPostal'] = '';
            $arrStatusTag['strOuiNumber'] = '';
            $items = array();

            foreach ($arrResults as $arrResult) {
                $item = array();
                if (!empty($arrResult['vchCarrierRef'])) {
                    $arrStatusTag['strPostal'] = $arrResult['vchCarrierRef'];
                }


                if (!empty($arrResult['vchSerialNumber'])) {
                    $arrStatusTag['strSerialNumber'] = $arrResult['vchSerialNumber'];
                    $item['strSerialNumber'] = $arrResult['vchSerialNumber'];
                }

                if (!empty($arrResult['vchOuiNumber'])) {
                    $arrStatusTag['strOuiNumber'] = $arrResult['vchOuiNumber'];
                }

                if (!empty($arrResult['vchItemID'])) {
                    $item['vchItemID'] = $arrResult['vchItemID'];
                }

                if (!empty($arrResult['intLine'])) {
                    $item['intLine'] = $arrResult['intLine'];
                }
                $items[] = $item;
            }
            $arrStatusTag['items'] = $items;

            if ($bolUpdateField === true) {
                $strSQL = "UPDATE tblHWFeedbackRecord SET dteIncidentDate = NOW() ".
                    "WHERE intConfigHardwareBundleID = '$intOrderNumber' ".
                    "AND intPartnerID = '$intPartnerID'";

                PrimitivesQueryOrExit($strSQL, $dbhConnection);
            }
        }

        return $arrStatusTag;

    }

    /**
     * Get the status of an RMA
     *
     * Get the latest status of an RMA
     *
     * @param Object $objRma - The RMA
     *
     * @access protected
     *
     * @return Array - The RMA Status Array
     */
    function FetchLatestRmaStatus($objRma)
    {
        // Get the order number
        $strRmaCode = $objRma->GetRmaCode();

        $strRetVal = '';

        //Get the latest RMA status
        $objBundle = $objRma->GetOriginalBundle();
        $arrStatusTag = $this->FetchLatestOrderStatus($objBundle);

        //If the status tag is returned - action as such
        if (isset($arrStatusTag['strStatus'])) {
            //If the RMA is complete then return the date is was returned
            if (in_array($arrStatusTag['strStatus'], array('rma_complete','dispatched','delivered'))) {
                //The RMA class will now update the hardware with the correct statuses
                $arrStatusTag['rma_complete_date'] = date('Y-m-d H:i:s');
            } else {
                //The RMA is not yet complete but the process has moved on - we need to update the status

                //Update the status of the overall hardware component
                $objBundle->SetOverallStatusTag($arrStatusTag['strStatus'], true);

                $objBundle->Commit();

            }
        }

        return $arrStatusTag;
    }
}
