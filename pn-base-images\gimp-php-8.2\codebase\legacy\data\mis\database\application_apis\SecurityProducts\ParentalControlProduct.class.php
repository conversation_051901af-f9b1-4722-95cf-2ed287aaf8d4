<?php

require_once '/local/data/mis/database/database_libraries/components/CProduct.inc';

/**
 * Class Parental Control Product
 * Extends CProduct
 *
 * @access public
 *
 */

class ParentalControlProduct extends CProduct
{
	/**
	 * Constructor
	 *
	 * @param int $intComponentId Component Id
	 */
 	public function __construct($intComponentId)
	{
		$this->CProduct($intComponentId);
	}

	/**
	 * Create Parental control account
	 *
	 * @return bool
	 */
	public function enable()
	{
		if (!$this->canBeEnabled()) {
			return false;
		}
		switch ($this->getStatus()) {
			case 'deactive':
				if (!$this->prvSetStatus('queued-reactivate')) {
					return false;
				}
				break;
			default:
				if (!$this->prvSetStatus('queued-activate')) {
					return false;
				}
				break;
		}

		if(!$this->enableProductComponents()) {
			return false;
		}
		if (!$this->prvSetStatus('active')) {
			return false;
		}
		$this->logStatusChange('ACTIVE');

		return true;

	}

	/**
	* Cancel the product.
	*
	*
	* @access public
	* @return bool
	*/
	public function disable()
	{
		if (!$this->canBeDisabled()) {
			return false;
		}

		if (!$this->prvSetStatus('queued-deactivate')) {
			return false;
		}

		if (!$this->disableProductComponents()) {
			return false;
		}

		if (!$this->prvSetStatus('deactive')) {
			return false;
		}

		$this->logStatusChange('DEACTIVE');

		return true;
	}

	/**
	* Destroy the product.
	*
	*
	* @access public
	* @return bool
	*/
	public function destroy()
	{
		if (!$this->canBeDestroyed()) {
			return false;
		}

		if (!$this->prvSetStatus('queued-destroy')) {
			return false;
		}

		if (!$this->destroyProductComponents()) {
			return false;
		}

		if (!$this->prvSetStatus('destroyed')) {
			return false;
		}

		$this->logStatusChange('DESTROYED');

		return true;
	}

	/**
	* Component configurator function. Checks if the compoent may be enabled.
	*
	*
	* @access public
	* @return boolean False if the component is destroyed, otherwise true
	*/
	public function canBeDestroyed()
	{
		if (true === in_array($this->GetStatus(), array('active', 'queued-deactivate', 'queued-destroy'))){
			return true;
		}
		else{
			return false;
		}

	}

	/**
	* Parental control Component status change logger.
	*
	*
	* @access public
	* @return void
	*/
	public function logStatusChange($strStatus)
	{
		$objEventLogger = $this->prvGetEventLogger();

		switch ($strStatus) {
			case 'ACTIVE':
				$objEventLogger->logStatusChange('ParentalControlActivation');
				break;
			case 'DEACTIVE':
				$objEventLogger->logStatusChange('ParentalControlDeactivation');
				break;
			case 'DESTROYED':
				$objEventLogger->logStatusChange('ParentalControlDestruction');
			default;
				break;
		}
	}

	/**
	* Calls renew on each of this object's product components
	*
	* @access public
	* @return bool
	*/
	public function renew()
	{
		$bolError = false;

		$arrProductComponents = $this->getProductComponents();

		foreach($arrProductComponents as $objProductComponent) {

			if (!$objProductComponent->renewAccount()) {
				$bolError = true;
			}
			else {
				if (!$this->prvSetStatus('active')) {
					$bolError = true;
				}
			}
		}
		if($bolError) {
			return false;
		}

		return true;

	}

}

