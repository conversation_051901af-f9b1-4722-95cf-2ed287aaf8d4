<?php

function config_framed_route_configurator($intComponentId, $strSignal)
{
    $product = new CProduct($intComponentId);
    $component = FramedRouteComponent::get($intComponentId);

    switch($strSignal) {
    case 'auto_configure':
        if(!$product->getProductComponents()) {
            ProductComponent_SubComponent::createDefaultsForComponent($intComponentId);
        }

        // intentional fallthrough

    case 'auto_refresh':
        if(!$component) return;  // no radius entry for this service (yet)

        if($component->configure()) {
            userdata_component_set_status($intComponentId, 'active');
        }
        break;

    case 'auto_destroy':
        if($component) $component->destroy();
        $product->disableProductComponents();

        userdata_component_set_status($intComponentId, 'destroyed');
        break;
    }
}


class FramedRouteComponent
{
    private $intComponentId;
    private $intServiceId;
    private $intRadiusId;
    private $strZone;
    private $intNumberAllocated;

    public static function get($intComponentId, $intServiceId = NULL, $intRadiusId = NULL)
    {
        $coredb = get_named_connection_with_db('userdata');

        settype($intComponentId, 'integer');

        if(!$intServiceId || !$intRadiusId) {
            $query = "SELECT s.service_id, s.username, s.isp FROM userdata.services s
                      INNER JOIN userdata.components c USING(service_id)
                      WHERE c.component_id = $intComponentId";
            $service = PrimitivesResultGet(PrimitivesQueryOrExit($query, $coredb));
            if(!$service) throw new InvalidArgumentException("Invalid component ID: $intComponentId");
            $intServiceId = $service['service_id'];

            if(!$intRadiusId) {
                $radius = get_named_connection_with_db('radius');
                $query = "SELECT radius_id FROM users
                      WHERE username = '" .
                         PrimitivesRealEscapeString($service['username'], $coredb) . "'
                      AND isp = '" .
                         PrimitivesRealEscapeString($service['isp'], $coredb) . "'";

                $intRadiusId = PrimitivesResultGet(
                    PrimitivesQueryOrExit($query, $radius),
                    'radius_id'
                );

                if(!$intRadiusId) return FALSE;
            }
        }

        $query = "SELECT frc.vchZone, frc.intNumberAllocated
                  FROM userdata.components c
                  INNER JOIN products.tblComponentFramedRouteConfig frc
                    ON frc.intServiceComponentId = c.component_type_id
                  WHERE c.component_id = $intComponentId";
        $data = PrimitivesResultGet(PrimitivesQueryOrExit($query, $coredb));

        if(!$data) throw new InvalidArgumentException("Not a framed route component");

        return new self($intComponentId, $intServiceId, $intRadiusId,
                        $data['vchZone'], $data['intNumberAllocated']);
    }

    public static function configureAll($intServiceId, $intRadiusId)
    {
        $coredb = get_named_connection_with_db('userdata');

        settype($intServiceId, 'integer');

        $query = "SELECT c.component_id FROM userdata.services s
                  INNER JOIN userdata.components c USING(service_id)
                  INNER JOIN products.tblComponentFramedRouteConfig frc
                     ON frc.intServiceComponentId = c.component_type_id
                  WHERE s.service_id = $intServiceId
                    AND c.status NOT IN('queued-destroy', 'destroyed')";

        $ids = PrimitivesResultsAsListGet(PrimitivesQueryOrExit($query, $coredb));

        foreach($ids as $id) {
            $component = self::get($id, $intServiceId, $intRadiusId);
            $component->configure();
        }
    }

    public function __construct($intComponentId, $intServiceId, $intRadiusId,
                                $strZone, $intNumberAllocated)
    {
        settype($intComponentId, 'integer');
        if($intComponentId < 1) throw new InvalidArgumentException("Invalid component ID");

        settype($intServiceId, 'integer');
        if($intServiceId < 1) throw new InvalidArgumentException("Invalid service ID");

        settype($intRadiusId, 'integer');
        if($intRadiusId < 1) throw new InvalidArgumentException("Invalid radius ID");

        settype($strZone, 'string');

        settype($intNumberAllocated, 'integer');
        if($intNumberAllocated < 1) throw new InvalidArgumentException("Invalid number of allocated addresses");

        $this->intComponentId = $intComponentId;
        $this->intServiceId = $intServiceId;
        $this->intRadiusId = $intRadiusId;
        $this->strZone = $strZone;
        $this->intNumberAllocated = $intNumberAllocated;
    }

    public function getNetmask()
    {
        return long2ip(4294967296 - $this->intNumberAllocated);
    }

    public function getAllocatedIp()
    {
        $db = get_named_connection_with_db("userdata_reporting");
        $query = "SELECT INET_NTOA(ip_address) AS ip_address FROM userdata.config_staticip
                  WHERE component_id = {$this->intComponentId}";
        return PrimitivesResultGet(PrimitivesQueryOrExit($query, $db), 'ip_address');
    }

    public function configure()
    {
        $ip = $this->getAllocatedIp();
        if(!$ip) {
            $ip_id = config_staticip_allocate($this->strZone, $this->getNetmask(),
                                              $this->intComponentId, 'request');
            if(!$ip_id) return FALSE;

            $coredb = get_named_connection_with_db("userdata");
            $query = "SELECT INET_NTOA(ip_address) AS ip_address
                      FROM userdata.config_staticip WHERE staticip_id = $ip_id";
            $ip = PrimitivesResultGet(PrimitivesQueryOrExit($query, $coredb), 'ip_address');
            if(!$ip) return FALSE;

            $last_ip = long2ip(sprintf("%u", ip2long($ip)) +
                               $this->intNumberAllocated - 1);

            userdata_component_set_configuration($this->intComponentId, $ip_id,
                                                 "$ip - $last_ip");
        }

        $radius = get_named_connection_with_db("radius");
        $query = "REPLACE INTO tblRadiusFramedRoutes SET
                  intRadiusId = {$this->intRadiusId},
                  intNetworkAddress = INET_ATON('" . PrimitivesRealEscapeString($ip, $radius) . "'),
                  intSubNet = " . (4294967296 - $this->intNumberAllocated);
        PrimitivesQueryOrExit($query, $radius);

        $hellacoya = new EllacoyaManager('PLUSNET');
        $username = adslGetUserRealm($this->intServiceId);

        if(!$hellacoya->addIpBlock($username, $ip,  $this->intNumberAllocated, TRUE)) {
            $hellacoya->eventRaise(array('Operation'       => 'Configure framed route',
                                         'strUserRealm'    => $username,
                                         'strIPAddress'    => $ip,
                                         'intBlockSize'    => $this->intNumberAllocated,
                                         'strProblemGroup' => 'EllacoyaSubscriberError',
                                         'strFile'         => __FILE__,
                                         'intLine'         => __LINE__));

        }

        return TRUE;
    }

    public function destroy()
    {
        $ip = $this->getAllocatedIp();

        if($ip) {
            $radius = get_named_connection_with_db("radius");
            $query = "DELETE FROM tblRadiusFramedRoutes
                      WHERE intRadiusId = {$this->intRadiusId}
                        AND intNetworkAddress = INET_ATON('" . PrimitivesRealEscapeString($ip, $radius) . "')";
            PrimitivesQueryOrExit($query, $radius);

            $smellacoya = new EllacoyaManager('PLUSNET');
            $username = adslGetUserRealm($this->intServiceId);

            if(!$smellacoya->removeIpBlock($username, $ip, $this->intNumberAllocated)) {
                $smellacoya->eventRaise(
                    array(
                        'Operation'       => 'Destroy framed route',
                        'strUserRealm'    => $username,
                        'strIPAddress'    => $ip,
                        'intBlockSize'    => $this->intNumberAllocated,
                        'strProblemGroup' => 'EllacoyaSubscriberError',
                        'strFile'         => __FILE__,
                        'intLine'         => __LINE__
                    )
                );
            }

            $coredb = get_named_connection_with_db("userdata");
            $query = "UPDATE userdata.config_staticip
                      SET component_id = NULL, available = 'yes'
                      WHERE component_id = {$this->intComponentId}
                        AND ip_address = '" . PrimitivesRealEscapeString($ip, $coredb) . "'";
            PrimitivesQueryOrExit($query, $coredb);
        }
    }
}
