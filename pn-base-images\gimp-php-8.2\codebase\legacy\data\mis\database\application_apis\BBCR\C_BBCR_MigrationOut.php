<?php

/**
 * Class for processing Migration records
 *
 * @package    LegacyCodebase
 * @subpackage BBCR
 * <AUTHOR> <<EMAIL>>
 */

require_once (CLASS_ACCESS_LIBRARY);
require_once (CBC_ACCESS_LIBRARY);
require_once (USERDATA_ACCESS_LIBRARY);
require_once (PRODUCT_ACCESS_LIBRARY);
require_once (ADSL_ACCESS_LIBRARY);
require_once (SQL_PRIMITIVES_LIBRARY);
require_once (CONFIG_WIFI_ACCESS_LIBRARY);
require_once (BBCR_LOCAL_LIBRARY_ROOT . '/C_Process_Migration.php');
require_once (BBCR_LOCAL_LIBRARY_ROOT . '/C_Process_Migration_Payment.php');
require_once (BBCR_LOCAL_LIBRARY_ROOT . '/C_BBCR_DomainComponent.php');
require_once COMMON_LIBRARY_ROOT . 'class_libraries/Retention/DiscountManager.class.php';
require_once '/local/data/mis/database/class_libraries/CancellationCeaseType.class.php';
require_once '/local/data/mis/database/class_libraries/CancellationHelper.class.php';
require_once '/local/data/mis/database/database_libraries/components/TV.class.php';
require_once '/local/data/mis/audit_logging/AuditLogger.php';

use Plusnet\HouseMoves\Services\ServiceManager;
use Plusnet\InventoryEventClient\Context\KCI3CeaseContext;
use Plusnet\InventoryEventClient\Context\AccountClosureContext;
use Plusnet\InventoryEventClient\Service\ServiceManager as IECServiceManager;
use Plusnet\InventoryEventClient\Model\Snapshot\Component\Internet\InternetComponent;
use Plusnet\AccountClosure\Application\AccountClosure;

global $global_component_configurators;

/**
 * Class for processing Migration records
 *
 * @package    LegacyCodebase
 * @subpackage BBCR
 * <AUTHOR> Szulc <<EMAIL>>
 */
class C_BBCR_MigrationOut extends C_BBCR_RecordProcessor
{
    /**
     * New Dedicated Network name.
     *
     * @var string
     */
    const PSN = 'WBMC PSN WBC';
    /**
     * Ignored count.
     *
     * @var integer
     */
    public $intIgnored = 0;

    /**
     * Failed count.
     *
     * @var integer
     */
    public $intFailed = 0;

    /**
     * Records passed count.
     *
     * @var integer
     */
    public $intRecordsPassed = 0;

    /**
     * Return array.
     *
     * @var array
     */
    private $returnValue = array();

    /**
     * Database connection handle.
     *
     * @var resource
     */
    private $dbhConnection = null;

    /**
     * Service id that class processes.
     *
     * @var integer
     */
    private $intReportServiceId;

    /**
     * Existing Termination Data
     *
     * @var terminationData object
     */
    private $terminationData = null;

    /**
     * BBCR migration out processor.
     *
     * @return void
     */
    public function C_BBCR_MigrationOut()
    {
    }

    /**
     * Is already processed customer?
     *
     * @return boolean
     */
    private function isAlreadyProcessedCustomer()
    {
        $arrService = userdata_service_get($this->intServiceId);

        if (in_array($arrService['status'], array('destroyed'))) {
            return true;
        } else {
            $arrProductDetails = product_get_adsl_service($arrService['type']);

            if ($arrProductDetails === false) {
                return true;
            }
        }

        return false;

    }

    /**
     * Method check if record has been processed by script
     *
     * @param array &$arrRecord Array of BBCR record data
     *
     * @return boolean
     */
    private function hasRecordBeenProcessed(&$arrRecord)
    {
        if ($this->isAlreadyProcessedCustomer()) {
            return true;
        }

        if (!$this->dbhConnection) {
            $this->dbhConnection = get_named_connection_with_db('userdata');
        }

        $strQuery = 'SELECT intServiceID FROM tblBBCRProcessedRecords WHERE '.
            'intServiceID='.intval($this->intServiceId).' AND ' .
            'vchTelephoneNumber="'.htmlspecialchars($arrRecord['TELEPHONENUMBER']).'" AND ' .
            'vchNetworkID="'.htmlspecialchars($arrRecord['NETWORKID']).'" AND ' .
            'vchOrderType="MIGRATION_OUT"';

        if (!$resResult = PrimitivesQueryOrExit($strQuery, $this->dbhConnection, 'query failed on', false, true)) {
            $this->reportSQLError($strQuery);

            return;
        }

        if (PrimitivesResultGet($resResult, 'intServiceID')) {
            return true;
        }

        return false;
    }

    /**
     * Notify record precessing.
     *
     * @param array &$arrRecord Array of BBCR record data
     *
     * @return boolean
     */
    private function notifyRecordProcessing(&$arrRecord)
    {
        if (!$this->dbhConnection) {
            $this->dbhConnection = get_named_connection_with_db('userdata');
        }

        $strQuery = 'INSERT INTO userdata.tblBBCRProcessedRecords (
                intServiceId, vchTelephoneNumber, vchNetworkId, dtmRecordEntryTime,
                vchCustomerReference, vchOrderStatus, vchOrderType
            )
            VALUES (' . intval($this->intServiceId) . ', ' .
            '"' . htmlspecialchars($arrRecord['TELEPHONENUMBER']) . '", ' .
            '"' . htmlspecialchars($arrRecord['NETWORKID']) . '", ' .
            'NOW(), '.
            '"", ' .
            '"",' .
            '"MIGRATION_OUT")';

        if (!PrimitivesQueryOrExit($strQuery, $this->dbhConnection, 'query failed on', false, true)) {
            $this->reportSQLError($strQuery);

            return;
        }

        return true;
    }

    /**
     * Method that processes the passed BBCR record.
     *
     * @param array   &$arrRecord           Array of BBCR record data
     * @param boolean $bolMigrationOutCease Migration out cease
     *
     * @return array
     */
    public function processRecord(&$arrRecord, $bolMigrationOutCease = false)
    {
        AuditLogger::functionEntry(__METHOD__);

        $this->returnValue['intErrorCode'] = 0;
        $this->returnValue['strErrorMessage'] = '';
        $this->returnValue['arrResultParameters'] = array();

        $arrService = $this->findMatchingServiceId($arrRecord);

        if ($this->bolSkip) {
            AuditLogger::functionExit(__METHOD__);
            return $this->setIgnoreReturn();
        } elseif (empty($this->intServiceId)) {
            $this->returnValue['intErrorCode'] = 5;
            $this->returnValue['strErrorMessage'] = $this->strErrorMessage;
            unset($this->strErrorMessage);
            $this->intFailed++;

            AuditLogger::functionExit(__METHOD__);
            return $this->returnValue;
        }
        if ($this->isNetworkTransferOrder($this->intServiceId, $arrRecord)) {
            if (self::PSN != $this->getProvisionedNetwork($this->intServiceId)) {
                AuditLogger::functionExit(__METHOD__);
                return $this->setIgnoreReturn();
            }
        }

        if (isset($arrRecord['CANCELLEDDATE']) 
            && preg_match('|[0-9]{2}/[0-9]{2}/[0-9]{4}|i', $arrRecord['CANCELLEDDATE'])
        ) {
            $this->returnValue['strErrorMessage'] = get_class($this) . " not interested in this record - as " .
                "migration order got cancelled. Skipping.\n";
        } elseif (isset($arrRecord['SERVICEID']) 
            && isset($arrRecord['NETWORKID']) 
            && isset($arrRecord['TELEPHONENUMBER']) 
            && isset($arrRecord['COMPLETIONDATE']) 
            && preg_match('|[0-9]{2}/[0-9]{2}/[0-9]{4}|i', $arrRecord['COMPLETIONDATE'])
        ) {
            if ($this->hasRecordBeenProcessed($arrRecord)) {
                $this->intRecordsPassed++;
                $this->returnValue['strErrorMessage'] = "Record has been already processed. Skipping.\n";

                AuditLogger::functionExit(__METHOD__);
                return $this->returnValue;
            }

            $cancellationHelper = new CancellationHelper($this->intServiceId);

            if ($cancellationHelper->isAccountScheduledForCancellation() || $cancellationHelper->isDestroyedAccount()) {
                $this->intRecordsPassed++;
                $this->returnValue['strErrorMessage'] = "Service ID: {$this->intServiceId}. Record is for an account " .
                    "that is destroyed, or has been scheduled for cancellation. Skipping.\n";

                AuditLogger::functionExit(__METHOD__);
                return $this->returnValue;
            }

            $arrProvisionData = adslGetProvisionedService($this->intServiceId);

            if ($arrProvisionData['vchSupplierHandle'] != 'BT') {
                $this->returnValue['strErrorMessage'] = get_class($this) . ' didn\'t think service ID ' .
                    $this->intServiceId . ' was provisioned with BT'."\n";
                $this->returnValue['intErrorCode'] = '5';

                AuditLogger::functionExit(__METHOD__);
                return $this->returnValue;
            }

            $strCeaseTypeHandle = CancellationCeaseType::MIGRATION_OUT;

            if (!$this->isNewBillingEngineFeatureToggleOn($this->intServiceId) && true == $bolMigrationOutCease) {
                $strCeaseTypeHandle = CancellationCeaseType::MIGRATION_OUT_WITHOUT_MAC;
            }

            $ceaseType = new CancellationCeaseType();
            $ceaseType->populateByHandle($strCeaseTypeHandle);

            if (preg_match('/^(\d{2})\/(\d{2})\/(\d{4})(.*)$/', $arrRecord['COMPLETIONDATE'], $arrMatch)) {
                $uxtCompletionDate = mktime(0, 0, 0, $arrMatch[2], $arrMatch[1], $arrMatch[3]);
            }

            $this->houseMoveService = $this->getHouseMoveService();
            $this->houseMoveData = $this->houseMoveService->getHouseMoveData();
            $arrReturn = array();

            if ($this->isNewBillingEngineFeatureToggleOn($this->intServiceId) && empty($this->houseMoveData)) {

                // Avoid Account Closure Process if Account is already queued-destroy or destroyed
                if ($this->isAccountDestroyed($this->intServiceId)) {
                    $currentDateTime = date('Y-m-d H:i:s');
                    Log_AuditLog::write(
                        'This account is already queued-destroy but KCI-3 for PH/BB
                        arrived at this time :' . $currentDateTime . ' . hence ignoring this
                        cease request.'
                    );

                    AuditLogger::functionExit(__METHOD__);
                    return;
                }

                $eventService = IECServiceManager::getService('EventService');

                $context = new AccountClosureContext();

                // to letting IEC know request comes from BBCR cease so that it will add Legacy BB removal in Price Change
                $context->setIsBBCRCease(true);

                // Find if remove channel or remove tv
                // has happened on this account
                // For remove channel only flow,
                // we should not send destroyed payload to RBM
                $context->setCeasingPacksOnly($this->isChannelPackRemovedOnActiveTv());

                $eventService->takePreChangeSnapshot($this->intServiceId, $context);

                AccountClosure::destroyComponents($this->intServiceId, 'BBCR_MIGRATION');

                $domainComponent = new C_BBCR_DomainComponent();
                $domainComponent->destroyDomainComponents($this->intServiceId);

                $this->terminationData = AccountClosure::userdataGetTerminationDataByServiceId(
                    $this->intServiceId,
                    $arrRecord['COMPLETIONDATE']
                );

                AccountClosure::sendNotificationToPartner($this->intServiceId, 'cease-completed');

                $context->setTerminationData($this->terminationData);
                $eventService->takePostChangeSnapshot($this->intServiceId);

                AccountClosure::terminateAccount(
                    $this->intServiceId,
                    $this->terminationData,
                    $arrRecord['COMPLETIONDATE']
                );
            } elseif (!empty($this->houseMoveData)) {
                $this->processCease($arrRecord['COMPLETIONDATE'], $this->houseMoveData);
            } else {
                $objMigrationProcessing = new C_Process_Migration_Payment(
                    $this->intServiceId,
                    $ceaseType,
                    $uxtCompletionDate
                );
                // 21CN customerref field is [0-9]{9}-[0-9]{3}, so we need to treat it differently
                $arrReturn = $objMigrationProcessing->processCancellation(true);
            }

            if ($arrReturn['intErrorCode'] != 0) {
                $this->intFailed++;
            } else {
                $this->intRecordsPassed++;
            }

            $this->returnValue['intErrorCode'] = $arrReturn['intErrorCode'];
            $this->returnValue['strErrorMessage'] = $arrReturn['strErrorMessage'];
            $this->notifyRecordProcessing($arrRecord);

        } else {
            $this->returnValue['strErrorMessage'] = get_class($this) . " not interested in this record - not " .
                "completed or not a cease. Skipping.\n";
        }

        AuditLogger::functionExit(__METHOD__);
        return $this->returnValue;
    }


    /**
     * If house move, processCease
     *
     * @param string $completionDate KCI3 completion date
     * @param object $houseMoveData  housemove data
     *
     * @return void
     */
    private function processCease($completionDate, $houseMoveData)
    {
        AuditLogger::functionEntry(__METHOD__);

        // Avoid BB Cease Process if Account is already queued-destroy or destroyed
        if ($this->isAccountDestroyed($this->intServiceId)) {
            $currentDateTime = date('Y-m-d H:i:s');
            Log_AuditLog::write(
                'This account is already queued-destroy but KCI-3 for PH/BB
                        arrived at this time :' . $currentDateTime . ' . hence ignoring this
                        cease request.'
            );

            AuditLogger::functionExit(__METHOD__);
            return;
        }

        $eventService = IECServiceManager::getService('EventService');

        $context = new KCI3CeaseContext();

        // to letting IEC know request comes from BBCR cease so that it will add Legacy BB removal in Price Change
        $context->setIsBBCRCease(true);

        // Find if remove channel or remove tv
        // has happened on this account
        // For remove channel only flow,
        // we should not send destroyed payload to RBM
        $context->setCeasingPacksOnly($this->isChannelPackRemovedOnActiveTv());

        $eventService->takePreChangeSnapshot($this->intServiceId, $context);
        $componentService = $this->getHouseMoveComponentsService();

        if ($houseMoveData->getIsManualHouseMoveProcess()
            || $houseMoveData->getAreAllProductsDeactivated()
        ) {
            $this->returnValue['intErrorCode'] = 0;
            $this->returnValue['strErrorMessage']
                = "Manual HouseMove/ All products deactivated, Not interested in this record. Service Id: " .
                $this->intServiceId . " - Skipping.\n";

            AuditLogger::functionExit(__METHOD__);
            return;
        } else {
            $this->houseMoveDeactivateComponents();
        }
        //commiting the transaction before taking snapshot
        try {
            Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);
        } catch (Exception $e) {
            echo date('H:i:s') . ": ERROR: failed to commit default transaction.  Resetting transaction\n" .
                "\nException message: " . $e->getMessage() .
                "\nException stacktrace: " . $e->getTraceAsString();

            // All this does is empty the "connection" array held by the transaction object, so there's no risk
            // of a further exception being thrown.  In addition, any statements etc which have been cached by
            // the transaction will be dropped and rebuilt when next called
            Db_Manager::closeTransactionConnections(Db_Manager::DEFAULT_TRANSACTION);
        }

        $eventService->takePostChangeSnapshot($this->intServiceId);

        $customerAgreedTerminationDate = $houseMoveData->getCeaseDate();

        $existingComponentId = $houseMoveData->getExistingBroadbandComponentId();

        $existingBroadbandComponentStatus = $componentService->getExistingComponentStatus($existingComponentId);

        if ($existingBroadbandComponentStatus == 'destroyed') {
            $actualCancellationDate = \DateTime::createFromFormat('d/m/Y H:i:s', $completionDate);

            if (new \DateTime($customerAgreedTerminationDate) > $actualCancellationDate) {
                $priceChangeService = IECServiceManager::getService('PriceChangeService');


                $context->setEventDate($actualCancellationDate);

                $componentDetails = array(
                    'componentId'        => $existingComponentId,
                    'usageIdentifier'    => null,
                    'cli'                => null,
                    'nextInvoiceDate'    => null,
                    'tariffId'           => null,
                    'dtmStart'           => null,
                    'componentStatus'    => $existingBroadbandComponentStatus,
                    'serviceComponentId' => null
                );

                $internetComponent
                    = InternetComponent::createFromRow($componentDetails);

                if (array_key_exists('type', $this->serviceDetails)) {
                    $priceChangeService->buildPriceChangeForCompletionDateChange(
                        $context,
                        $this->intServiceId,
                        $this->serviceDetails['type'],
                        $internetComponent
                    );
                }
            }
        }

        $componentService->updateProductsDeactivated($this->houseMoveService);
        $this->returnValue['strErrorMessage'] = "'HouseMove Cease' Service Id: " .
            $this->intServiceId . " - Processed.\n";
        $this->intRecordsPassed++;

        AuditLogger::functionExit(__METHOD__);
        return;
    }

    /**
     * Method to find if TV is active but the channel pack is queued-destroy
     *
     * @return boolean
     */
    public function isChannelPackRemovedOnActiveTv()
    {
        $tvComponentId = $this->findTVComponentId();
        if (is_numeric($tvComponentId)) {
            $tvComponent = $this->getProduct($tvComponentId);
            $arrTvProductComponents = $tvComponent->getProductComponents();
            foreach ($arrTvProductComponents as $productComponent) {
                if ($productComponent->getStatus() == 'QUEUED_DESTROY') {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Method to get the product for the given component Id
     *
     * @param string $componentId Component Id
     *
     * @return CProduct
     */
    protected function getProduct($componentId)
    {
        return new CProduct($componentId);
    }

    /**
     * Get a ComponentsService
     *
     * @return \Plusnet\HouseMoves\Services\ComponentsService
     */
    public function getHouseMoveComponentsService()
    {
        return ServiceManager::getService('ComponentsService');
    }

    /**
     * Deactivate the components for house move
     *
     * @return void
     */
    private function houseMoveDeactivateComponents()
    {
        $existingBroadbandComponentId = $this->houseMoveData->getExistingBroadbandComponentId();

        $componentService = $this->getHouseMoveComponentsService();

        $existingBroadbandComponentStatus = $componentService->getExistingComponentStatus(
            $existingBroadbandComponentId
        );

        if ($existingBroadbandComponentStatus != 'destroyed') {
            $componentService->deactivateBBComponent($existingBroadbandComponentId);

            $componentService->deactivateComponentInstances($existingBroadbandComponentId);

            $componentService->updateBroadbandDependentComponentStatus($this->intServiceId, 'active', 'deactive');
        }
        $this->findAndDeactivateTv($componentService);
    }

    /**
     * Find Tv component id and deactivate
     *
     * @param ComponentService $componentService component service
     *
     * @return void
     */
    private function findAndDeactivateTv($componentService)
    {
        $tvComponentId = $this->findTVComponentId();
        if (is_numeric($tvComponentId)) {
            userdata_component_set_status($tvComponentId, 'deactive');
            $componentService->deactivateComponentInstances($tvComponentId);
        }
    }

    /**
     * Find if the customer has Tv
     *
     * @return Integer
     */
    public function findTVComponentId()
    {
        return TV::findActiveComponentId($this->intServiceId);
    }

    /**
     * Get the Provisioned Network For given service id
     * @param $service_id
     * @return string
     */
    private function getProvisionedNetwork($service_id)
    {

        $provisionedNetworkManager = new \Plusnet\ProvisionedNetwork\Manager();
        return $provisionedNetworkManager->getProvisionedNetworkFromServiceId($service_id);;
    }

    /**
     * Returns a string summarising processing results.
     *
     * @return string
     */
    public function getSummary()
    {
        $returnValue = (string) '';

        $returnValue .= "Number of records failed to action: " . $this->intFailed        . "\n"
                      . "Number of total records uploaded: "   . $this->intRecordsPassed . "\n"
                      . "Number of ignored records:"           . $this->intIgnored     . "\n";

        return $returnValue;
    }

    /**
     * Cancellation destroy groupware components.
     *
     * @param integer $intServiceID Service id
     *
     * @return void
     */
    public function CancellationDestroyGroupwareComponents($intServiceID = null)
    {
        if ((null == $intServiceID) && ($this instanceof C_BBCR_MigrationOut)) {
            $intServiceID = $this->intServiceId;
        }

        // Problem 8711 fix
        $arrGroupWareDetails = groupware_group_id_get_by_service($intServiceID);

        if (is_array($arrGroupWareDetails)) {
            if ($arrGroupWareDetails['is_owner'] == '1') {
                $arrBundles = groupware_group_bundles_get($arrGroupWareDetails['group_id']);
                groupware_group_delete($arrGroupWareDetails['group_id']);

                foreach ($arrBundles as $key => $arrBundle) {
                    groupware_group_bundle_remove($arrGroupWareDetails['group_id'], $arrBundle['bundle_type_id']);
                }
            }
        }
    }

    /**
     * Cancellation destroy associated ADSL upgrade account.
     *
     * @param integer $intBaseServiceDefinitionID Base service definition id
     * @param integer &$intTypeToCheckForHardware Hardware type
     * @param integer $intServiceID               Service id
     *
     * @return boolean
     */
    public function CancellationDestroyAssociatedADSLUpgradeAccount(
        $intBaseServiceDefinitionID,
        &$intTypeToCheckForHardware,
        $intServiceID = null
    ) {
        global $my_id;

        if ((null == $intServiceID) && ($this instanceof C_BBCR_MigrationOut)) {
            $intServiceID = $this->intServiceId;
        }

        if ($adslUpgrade = userdata_is_service_adsl_upgrade($intServiceID)) {
            userdata_adsl_upgrade_set_status($adslUpgrade['adsl_upgrade_id'], 'failed');
            $serviceType = $intBaseServiceDefinitionID;
            $realService = userdata_service_get($adslUpgrade['service_id']);
            $realServiceType = $realService['type'];
            userdata_service_log_type_change(
                $realService['service_id'],
                $serviceType,
                $realServiceType,
                $realService['next_invoice']
            );
        }

        if ($arrUpgradeRecords = userdata_get_adsl_upgrade_services($intServiceID)) {
            $activeUpgradeRecord = 0;

            foreach ($arrUpgradeRecords as $upgradeRecord) {
                if ($upgradeRecord['upgrade_status'] == 'in_progress') {
                    $activeUpgradeRecord = $upgradeRecord;
                }
            }

            $arrUpgradeAccount = userdata_service_get($activeUpgradeRecord['holding_adsl_service_id']);
            $intTypeToCheckForHardware = $arrUpgradeAccount['type'];

            if ($activeUpgradeRecord) {
                userdata_adsl_upgrade_set_status($activeUpgradeRecord['adsl_upgrade_id'], 'failed');
                alter_adsl_status($activeUpgradeRecord['holding_adsl_service_id'], 'cancelled');
                userdata_service_set_status($activeUpgradeRecord['holding_adsl_service_id'], 'queued-destroy');
                userdata_service_set_enddate(
                    $activeUpgradeRecord['holding_adsl_service_id'],
                    date("Y-m-d H:i:s", mktime(0, 0, 0, date("m"), date("d"), date("Y")))
                );
                destroy_all_components($activeUpgradeRecord['holding_adsl_service_id']);

                if ($this->bolRaiseTicket) {
                    tickets_ticket_add(
                        'Internal',
                        $activeUpgradeRecord['holding_adsl_service_id'],
                        0,
                        0,
                        'Closed',
                        $my_id,
                        'Temporary upgrade account cancelled, due to cancellation of users original account'
                    );
                }
            }

        }

        return true;

    }

    /**
     * Cancellation return ADSL stock.
     *
     * @param integer $intTypeToCheckForHardware Hardware type
     *
     * @return boolean
     */
    public function CancellationReturnADSLStock($intTypeToCheckForHardware)
    {
        if (isset($intTypeToCheckForHardware) 
            && $intTypeToCheckForHardware != '' 
            && $intTypeToCheckForHardware != '0'
        ) {
            $arrAccountHardware = adslGetAccountHardware($intTypeToCheckForHardware);

            if (is_array($arrAccountHardware) && isset($arrAccountHardware['service_component_id'])) {
                adslHardwareStockLevelAlter($arrAccountHardware['service_component_id'], '+');
            }
        }

        return true;
    }

    /**
     * Cancellation record cancellation events.
     *
     * @param integer $intAccountFrom   Account from
     * @param integer $intAccountTo     Account to
     * @param integer $intCBCFlexID     CBC flex id
     * @param integer $intWifiComponent Wifi component
     * @param integer $intServiceID     Service id
     *
     * @return void
     */
    public function CancellationRecordCancellationEvents(
        $intAccountFrom,
        $intAccountTo,
        $intCBCFlexID,
        $intWifiComponent,
        $intServiceID = null
    ) {
        if ((null == $intServiceID) && ($this instanceof C_BBCR_MigrationOut)) {
            $intServiceID = $this->intServiceId;
        }

        $intServiceEventID = userdata_service_event_add(
            $intServiceID,
            SERVICE_EVENT_MIGRATION_OUT,
            $intAccountFrom,
            $intAccountTo,
            '',
            '',
            '',
            '',
            '',
            $intWifiComponent
        );

        if ($intCBCFlexID !== false && $intCBCFlexID > 0) {
            InsertCBCFlexServiceLog($intServiceID, $intCBCFlexID, 0, $intServiceEventID);
        }
    }

    /**
     * Method sets error code and message if DBError occurs.
     *
     * @param string $strQuery SQL Query that raised error
     *
     * @return void
     */
    private function reportSQLError($strQuery)
    {
        $this->returnValue['intErrorCode'] = 1;
        $this->returnValue['strErrorMessage'] = 'DBError occurred in statement ' . $strQuery .
            '. See critical error log for details.';
    }

    /**
     * @return array
     */
    private function setIgnoreReturn()
    {
        $this->returnValue['intErrorCode'] = 0;
        $this->intIgnored++;
        return $this->returnValue;
    }

    /**
     * Returns termination data.
     *
     * @return terminationData
     */
    public function getTerminationData()
    {
        return $this->terminationData;
    }
}
