<?php

require_once APPLICATION_API_ROOT . 'SecurityProducts/ParentalControlManager.class.php';

$global_component_configurators[COMPONENT_PARENTAL_CONTROL] = 'config_parentalcontrol_configurator';

function config_parentalcontrol_configurator($intComponentId, $strAction)
{
	$objParentalControlManager = new ParentalControlManager();
	$objParentalControlManager->setComponentId($intComponentId);

	switch($strAction) {

		case 'auto_enable':
			try {
				return $objParentalControlManager->createAccount();
			}
			catch (Exception $objException) {
				return false;
			}

		case 'auto_configure':
			return false;

		case 'auto_disable':
			try {
				return $objParentalControlManager->cancelAccount();
			}
			catch (Exception $objException) {
				return false;
			}

		case 'auto_destroy':
			try {
				return $objParentalControlManager->cancelAccount();
			}
			catch (Exception $objException) {
				return false;
			}

		case 'auto_refresh':
			try {
				return $objParentalControlManager->createAccount();
			}
			catch (Exception $objException) {
				return false;
			}

		default:
			return false;
	}
}

