<?php
require_once SQL_PRIMITIVES_LIBRARY;
require_once PRODUCT_ACCESS_LIBRARY;
require_once USERDATA_ACCESS_LIBRARY;
require_once COMPONENT_DEFINES_LIBRARY;
require_once SECURE_TRANSACTION_ACCESS_LIBRARY;
require_once SMARTY_LIBRARY;

require_once (dirname(__FILE__) . '/BullGuardManagerException.class.php');
require_once (dirname(__FILE__) . '/BullGuardUserException.class.php');
require_once (dirname(__FILE__) . '/SecurityProduct.class.php');
require_once (dirname(__FILE__) . '/BullGuardProductComponent.class.php');

require_once '/local/data/mis/database/application_apis/EmailHandler/EmailHandler.class.php';
require_once '/local/data/mis/database/database_libraries/programme-tool-access.inc';
require_once '/local/data/mis/common_library_functions/common_application_apis/Core/Libraries/Service.class.php';

class BullGuardManager
{
    const CSC_REFUND_TICKET_POOL = 'CSC_BILLING';

    /**
     * Accessible variables
     *
     */

    protected $intComponentId = 0;
    protected $arrComponent = array();

    protected $intServiceId = 0;
    protected $arrService = array();

    /**
     * Internal variables
     *
     */

    private $arrPaymentOptionsCache = array();
    private $objTemplate;

    private $arrProductObjects = array();
    private $arrComponentObjects = array();
    private $arrSubscriptionObjects = array();

    private $strTemplateBaseDir = '/local/data/mis/portal_modules/BullGuard/templates/';
    private $strEmailTemplateDir = '/local/data/mis/portal_modules/mailtemplates/bullguard/';

    private $strTransaction = 'TRANSACTION_BULLGUARD_MANAGER';

    /**
     * Populate data across the object using component id, find exact one component.
     *
     * @param int $intComponentId
     * @return bool
     * @throws BullGuardManagerException
     */
    public function populateDataByComponent($intComponentId)
    {
        if (true == is_numeric($intComponentId) && 0 != $intComponentId) {

            $this->arrComponent = userdata_component_get($intComponentId);

            // If component exist
            if (false == empty($this->arrComponent)) {

                $this->intComponentId = $this->arrComponent['component_id'];

                $this->intServiceId = (int)$this->arrComponent['service_id'];
                $this->arrService = userdata_service_get($this->arrComponent['service_id']);

                return true;
            }
        }

        throw new BullGuardManagerException('Incorrect component id passed', BullGuardManagerException::ERR_MISSING_DATA);
    }

    protected function findComponentsInServiceComponents($arrCriteria)
    {
        $arrComponents = array();
        $arrComponentsAll = userdata_component_find($arrCriteria);

        // Validate is BG component valid CProductComponent, if it's not - kick it from list, there was many invalid components in database
        foreach ($arrComponentsAll as $arrComponent)
        {
            $objProduct = $this->getProduct($arrComponent['component_id']);

            if (true == ($objProduct instanceof CProduct))
            {
                $arrProductComponents = $objProduct->getProductComponents();

                if (true == is_array($arrProductComponents) && false == empty($arrProductComponents))
                {
                    $arrComponents[] = $arrComponent;
                }
            }
        }

        unset($arrComponentsAll);

        return $arrComponents;
    }

    /**
     * Populate data across the object using service id. Try to find any active (product or trial) component. If active component
     * not found, try to detect last destroyed component
     *
     * @param int $intServiceId
     * @return bool
     * @throws BullGuardManagerException
     */
    public function populateDataByService($intServiceId)
    {
        if (true == is_numeric($intServiceId) && 0 != $intServiceId) {

            $this->arrService = userdata_service_get($intServiceId);

            if (false == empty($this->arrService)) {

                $this->intServiceId = (int)$this->arrService['service_id'];

                // Find component
                $arrCriteria = array(
                    'service_id'	=> $this->intServiceId,
                    'type'  		=> array(COMPONENT_BULL_GUARD, COMPONENT_BULL_GUARD_TRIAL),
                    'status'		=> array (
                            'unconfigured',
                            'queued-activate',
                            'queued-reactivate',
                            'active',
                            'queued-deactivate',
                            'queued-deconfigure',
                            'deactive',
                            'queued-destroy',
                            'destroyed',
                            'invalid'
                            )
                    );

                $arrComponents = $this->findComponentsInServiceComponents($arrCriteria);
                $arrActiveStatuses = array('queued-activate', 'active');

                // Try find active product component first
                foreach ($arrComponents as $arrComponent)
                {
                    if (COMPONENT_BULL_GUARD == $arrComponent['component_type_id'] && true == in_array($arrComponent['status'], $arrActiveStatuses)) {

                        $this->intComponentId = $arrComponent['component_id'];
                        $this->arrComponent = $arrComponent;
                        return true;
                    }
                }

                // Next find the newest product component in any state
                foreach ($arrComponents as $arrComponent)
                {
                    if (COMPONENT_BULL_GUARD == $arrComponent['component_type_id'] && (int)$this->arrComponent['uxtCreationDate'] < $arrComponent['uxtCreationDate']) {

                        $this->intComponentId = $arrComponent['component_id'];
                        $this->arrComponent = $arrComponent;
                    }
                }

                if (false == empty($this->arrComponent)) {

                    return true;
                }

                // Finally try to find trial component in any state
                foreach ($arrComponents as $arrComponent)
                {
                    if (COMPONENT_BULL_GUARD_TRIAL == $arrComponent['component_type_id']) {

                        $this->intComponentId = $arrComponent['component_id'];
                        $this->arrComponent = $arrComponent;
                        return true;
                    }
                }

                return false;
            }
        }

        throw new BullGuardManagerException('Incorrect service id passed.', BullGuardManagerException::ERR_MISSING_DATA);
    }

    /**
     * Get smarty template object
     *
     * @return CPage
     */
    protected function &getTemplate()
    {
        if (true == is_null($this->objTemplate)) {

            $this->objTemplate = new Smarty();

            $this->objTemplate->compile_dir = SMARTY_COMPILE_DIR;
            $this->objTemplate->compile_id = get_class($this);

            $this->objTemplate->template_dir = $this->strTemplateBaseDir;
        }

        return $this->objTemplate;
    }

    /**
     * Return customer security product
     *
     * @param int $intComponentId
     *
     * @return CSecurityProduct
     * @throws BullGuardManagerException
     */
    public function &getProduct($intComponentId = 0)
    {
        if ($intComponentId == 0) {

            $intComponentId = $this->intComponentId;
        }

        if (false == is_numeric($intComponentId) || 0 == $intComponentId) {

            throw new BullGuardManagerException('Can\'t determine component id', BullGuardManagerException::ERR_MISSING_DATA);
        }

        if (true == is_null($this->arrProductObjects[$intComponentId])) {

            $this->arrProductObjects[$intComponentId] = CComponent::createInstance($intComponentId);

            if (false == ($this->arrProductObjects[$intComponentId] instanceof CSecurityProduct)) {

                throw new BullGuardManagerException('Can\'t create CSecurityProduct instance', BullGuardManagerException::ERR_PRODUCT);
            }
        }

        return $this->arrProductObjects[$intComponentId];
    }

    /**
     * Return customer Bul Guard component
     *
     * @param int $intComponentId
     *
     * @return CBullGuardComponent
     * @throws BullGuardManagerException
     */
    public function &getComponent($intComponentId = 0)
    {
        if (0 == $intComponentId) {

            $intComponentId = $this->intComponentId;
        }

        if (true == is_null($this->arrComponentObjects[$intComponentId])) {

            foreach ($this->getProduct($intComponentId)->getProductComponents() as $objComponent)
            {
                if (true == ($objComponent instanceof CBullGuardComponent)) {

                    $this->arrComponentObjects[$intComponentId] = $objComponent;
                    break;
                }
            }
        }

        return isset($this->arrComponentObjects[$intComponentId]) ?
               $this->arrComponentObjects[$intComponentId] :
               false;
    }

    /**
     * Return a subscription component of security product
     *
     * @param integer $intComponentId
     * @return CProductComponentSubscription
     */
    public function &getSubscription($intComponentId = 0)
    {
        if (0 == $intComponentId) {

            $intComponentId = $this->intComponentId;
        }

        if (true == is_null($this->arrSubscriptionObjects[$intComponentId])) {

            foreach ($this->getProduct($intComponentId)->getProductComponents() as $objComponent)
            {
                if (true == ($objComponent instanceof CProductComponentSubscription)) {

                    $this->arrSubscriptionObjects[$intComponentId] = $objComponent;
                }
            }
        }

        return isset($this->arrSubscriptionObjects[$intComponentId]) ?
               $this->arrSubscriptionObjects[$intComponentId] :
               false;
    }

    /**
     * Return a component array (from userdata.components)
     *
     * @return array
     */
    public function getComponentArray()
    {
        return $this->arrComponent;
    }

    /**
     * Send an email with information about product activation
     *
     * @return bool
     */
    protected function emailCustomer($strHandle, $arrExtraParams = array())
    {
        if (0 == $this->intServiceId || false == is_numeric($this->intServiceId)) {

            throw new BullGuardManagerException('Service id not set', BullGuardManagerException::ERR_MISSING_DATA);
        }

        if (false == is_array($arrExtraParams)) {

            $arrExtraParams = array();
        }

        if (Core_Service::isJlpVisp($this->arrService['isp'])) {

        switch (strtoupper($strHandle))
            {
                case 'ACTIVATE':
                    $strMailTemplate = 'bullguard_signup';
                    break;

                case 'SIGNUP':
                    $strMailTemplate = 'bullguard';
                    break;

                default:
                throw new BullGuardManagerException('Invalid email handler', BullGuardManagerException::ERR_MISSING_DATA);
            }

            try
            {
                $arrExtraParams['intServiceId'] = $this->intServiceId;
                $strResult = EmailHandler::sendEmail(
                            $this->intServiceId,
                            $strMailTemplate,
                            $arrExtraParams,
                            IspAutomatedEmail_IspAutomatedEmail::SN_FULL,
                            $this->strTransaction
                            );

                // Problem fix for P 55216 which is related to encyrpting the error log
                // Due to this method having a private transaction and then trying to get
                // the same information on SIGNUP_EMAIL transaction in create_adsl_account
                // an error is raised which then encrypts the parameters to create_adsl_account
                // which is the signup_array which is passed in by reference, so instead of
                // if being an array it is then an encrypted string
                Db_Manager::commit($this->strTransaction);
            }
            catch (EmailHandler_Exception $excEmailHandler)
            {
                pt_raise_autoproblem('EmailHandler generated an error',
                                     'EmailHandler generated an error',
                                     $excEmailHandler->getMessage(),
                                     __FILE__);
            }
        } else {

            switch (strtoupper($strHandle)) {

                    case 'ACTIVATE':
                        $strMailTemplate = $this->strEmailTemplateDir . 'bullguard_order_upgrade_eb016.txt';
                        break;

                    case 'SIGNUP':
                        $strMailTemplate = $this->strEmailTemplateDir . 'bullguard_order_signup_eb017.txt';
                        break;

                    default:
                        throw new BullGuardManagerException('Invalid email handler', BullGuardManagerException::ERR_MISSING_DATA);
                }

                $strResult = mailer_send_customer_mail($this->intServiceId, $strMailTemplate, $arrExtraParams, true, true);
            }

        return $strResult;
    }

    /**
     * Raise a ticket
     *
     * @param string $strHandle
     * @param array $arrData
     * @return integer
     */
    public function raiseTicket($strHandle, $arrData = array())
    {
        $strFileName = '';

        switch ($strHandle) {

            case 'DEACTIVATE_REFUND':
                $strFileName = 'TicketDeactivatedNeedRefund.tpl';
                $intTeamId = phplibGetTeamIdByHandle(self::CSC_REFUND_TICKET_POOL);
                $strStatus = 'Open';
                break;

            case 'DEACTIVATE':
                $strFileName = 'TicketDeactivated.tpl';
                $intTeamId = 0;
                $strStatus = 'Closed';
                break;

            case 'ACTIVATE':
                $strFileName = 'TicketActivated.tpl';
                $intTeamId = 0;
                $strStatus = 'Closed';
                break;

            case 'ACTIVATE_TRIAL':
                $strFileName = 'TicketActivatedTrial.tpl';
                $intTeamId = 0;
                $strStatus = 'Closed';
                break;

            case 'DESTROY_TRIAL':
                $strFileName = 'TicketDestroyedTrial.tpl';
                $intTeamId = 0;
                $strStatus = 'Closed';
                break;

            case 'DESTROY':
                $strFileName = 'TicketDestroyed.tpl';
                $intTeamId = 0;
                $strStatus = 'Closed';
                break;

            default:
                return false;
                break;
        }

        if (false == empty($arrData)) {

            $this->getTemplate()->assign($arrData);
        }

        return tickets_ticket_add(
                'Script',
                $this->intServiceId,
                0,
                0,
                $strStatus,
                SCRIPT_USER,
                $this->getTemplate()->fetch($strFileName),
                0,
                $intTeamId
                );
    }

    /**
     * Signup for Bull Guard product
     *
     * @throws BullGuardManagerException
     * @return int
     */
    protected function signupProduct()
    {
        if (0 == $this->intServiceId) {

            throw new BullGuardManagerException('Service id not set', BullGuardManagerException::ERR_MISSING_DATA);
        }

        // Find a correct tariff, signups are always with monthly subscription
        $arrPaymentOptions = $this->getActivationPayments();
        $arrPaymentOption = array();

        // Validate given tariff
        foreach ($arrPaymentOptions as $arrOption)
        {
            if ('MONTHLY' == $arrOption['strPaymentFrequencyHandle']) {

                $arrPaymentOption = $arrOption;
                break;
            }
        }

        if (true == empty($arrPaymentOption)) {

            throw new BullGuardManagerException('Can\'t determine component tariff', BullGuardManagerException::ERR_MISSING_DATA);
        }

        $objNewBullGuardProduct = CProduct::create($this->intServiceId, COMPONENT_BULL_GUARD, $arrPaymentOption['intTariffID'], time());

        if (false == $objNewBullGuardProduct) {

            throw new BullGuardManagerException('Can\'t create product', BullGuardManagerException::ERR_PRODUCT);
        }

        if (true !== $objNewBullGuardProduct->configure()) {

            throw new BullGuardManagerException('Can\'t configure product', BullGuardManagerException::ERR_PRODUCT);
        }

        $intComponentId = 0;

        foreach ($arrProductComponents as $objProductComponent)
        {
            if (true == ($objProductComponent instanceof CBullGuardProductComponent)) {

                $intComponentId = $objProductComponent->getComponentID();
            }
        }

        $arrAdditionalVars = array('service_id' => $this->intServiceId);
        $this->emailCustomer('SIGNUP', $arrAdditionalVars);

        return $intComponentId;
    }

    /**
     * Activate component in trial mode, return created component id
     *
     * @throws BullGuardManagerException
     * @return integer
     */
    protected function activateTrial()
    {
        if (0 == $this->intServiceId) {

            throw new BullGuardManagerException('Service id not set', BullGuardManagerException::ERR_MISSING_DATA);
        }

        // Don't allow create more than one trial component
        $arrCriteria = array(
            'service_id'=> $this->intServiceId,
            'type'  	   => array (COMPONENT_BULL_GUARD_TRIAL),
            'status'	   => array (
                    'unconfigured',
                    'queued-activate',
                    'queued-reactivate',
                    'active',
                    'queued-deactivate',
                    'queued-deconfigure',
                    'deactive',
                    'queued-destroy',
                    'destroyed',
                    'invalid'
                    )
            );

        $arrComponents = $this->findComponentsInServiceComponents($arrCriteria);

        if (false == empty($arrComponents)) {

            throw new BullGuardManagerException('User already have trial component.', BullGuardManagerException::ERR_TRIAL_COMPONENT_EXIST);
        }

        // Create product without any tariff
        $objNewBullGuardProduct = CProduct::create($this->intServiceId, COMPONENT_BULL_GUARD_TRIAL);

        if (false == $objNewBullGuardProduct) {

            throw new BullGuardManagerException('Can\'t create product', BullGuardManagerException::ERR_PRODUCT);
        }

        // Initialise config, for trial component it will set correct subscription expiry date
        if (true !== $objNewBullGuardProduct->configure()) {

            throw new BullGuardManagerException('Can\'t configure product', BullGuardManagerException::ERR_PRODUCT);
        }

        // Activate components: CSecurityProduct::enable()
        if (true !== $objNewBullGuardProduct->enable()) {

            throw new BullGuardManagerException('Problem with product activation occured', BullGuardManagerException::ERR_PRODUCT);
        }

        // Get expiry date and add service notice
        $utxExpiryDate = false;
        $intComponentId = 0;

        $arrProductComponents = $objNewBullGuardProduct->getProductComponents();

        foreach ($arrProductComponents as $objProductComponent)
        {
            if (true == ($objProductComponent instanceof CBullGuardTrialComponent)) {

                $utxExpiryDate = $objProductComponent->getExpiryDate();
                $intComponentId = $objProductComponent->getComponentID();
            }
        }

        $arrEmailParams = array('service_id' => $this->intServiceId);

        $this->emailCustomer('ACTIVATE', $arrEmailParams);
        $this->raiseTicket('ACTIVATE_TRIAL', array('utxExpiry' => $utxExpiryDate));

        return $intComponentId;
    }

    /**
     * Creates the BullGuardProduct object
     *
     * @param int $intTariffId
     * @return BullGuradProduct
     */
    public function createBullGuardProduct($intTariffId, $uxtNextInvoice)
    {
        if (false == empty($this->arrComponent) && 'unconfigured' == $this->arrComponent['status'] && $intTariffId == $this->getSubscription()->getTariffID()) {

            $objNewBullGuardProduct = $this->getProduct();

        }
        else {

            $objNewBullGuardProduct = CProduct::create($this->intServiceId, COMPONENT_BULL_GUARD, $intTariffId, $uxtNextInvoice);
        }

        if (false == ($objNewBullGuardProduct instanceof CSecurityProduct)) {

            throw new BullGuardManagerException('Can\'t create product', BullGuardManagerException::ERR_PRODUCT);
        }

        return $objNewBullGuardProduct;
    }

    /**
     * Destroys the trial components
     *
     * @param int $intServiceId
     * @return int
     */
    public function destroyTrialComponents($intServiceId)
    {
        // Check for any trial component that can be destroyed and
        // get installation date if trial component found
        $arrCriteria = array(
            'service_id' => $intServiceId,
            'type' => array (COMPONENT_BULL_GUARD_TRIAL),
            'status' => array (
                'unconfigured',
                'queued-activate',
                'queued-reactivate',
                'active',
                'queued-deactivate',
                'queued-deconfigure',
                'deactive',
                'queued-destroy',
                'invalid'
            )
        );

        $utxInstallDate = false;
        $arrComponents = $this->findComponentsInServiceComponents($arrCriteria);

        if (false == empty($arrComponents)) {

            foreach ($arrComponents as $arrTrialComponent) {

                $utxInstallDate = $this->getManager()->getComponent($arrTrialComponent['component_id'])->getInstalationDate();

                $this->getManager()->getProduct($arrTrialComponent['component_id'])->destroy();
            }
        }

        return $utxInstallDate;
    }

    /**
     * Gives the payment option details
     *
     * @param string $strOption
     *
     * @return array
     */
    public function getPaymentOption($strOption)
    {
        $floAmountToPay = 0;
        $intTariffId = 0;

        foreach ($arrPaymentOptions = $this->getActivationPayments() as $arrPaymentOption) {

            if ($strOption == $arrPaymentOption['strPaymentFrequencyHandle']) {

                $floAmountToPay = (($arrPaymentOption ['intCostIncVatInPence'] + $arrPaymentOption['arrProRata']['intProRataCost']) / 100);
                $intTariffId = $arrPaymentOption['intTariffID'];
            }
        }

        // Check is there any active component
        if (false == $this->canActivateBullGuard()) {

            throw new BullGuardManagerException('Component already exist', BullGuardManagerException::ERR_PRODUCT);
        }

        if (false == is_numeric($intTariffId) || 0 == $intTariffId || true == empty($intTariffId)) {

            throw new BullGuardUserException('Can\'t determine component tariff', BullGuardUserException::ERR_MISSING_TARIFF);
        }

        // Validate given tariff
        $arrPaymentOptions = $this->getActivationPayments();
        $arrPaymentOption = array();

        foreach ($arrPaymentOptions as $arrOption) {

            if ($intTariffId == $arrOption['intTariffID']) {

                $arrPaymentOption = $arrOption;
                break;
            }
        }

        if (true == empty($arrPaymentOption)) {

            throw new BullGuardManagerException('Can\'t determine component tariff', BullGuardManagerException::ERR_MISSING_DATA);
        }

        return $arrPaymentOption;
    }

    /**
     * Activate component
     *
     * @param array $paymentOption
     * @param BullGuardProduct $objNewBullGuardProduct
     * @param int $uxtProRataCalculationDate
     * @param int $utxInstallDate
     * @param int $uxtNextInvoice
     * @param int $transactionId
     * @param int $floActivationCost
     * @throws BullGuardManagerException
     * @return int
     */
    public function activateProduct(
        $paymentOption,
        $objNewBullGuardProduct,
        $uxtProRataCalculationDate,
        $utxInstallDate,
        $uxtNextInvoice,
        $transactionId,
        $floActivationCost
    )
    {
        // Check service id
        if (0 == $this->intServiceId) {

            throw new BullGuardManagerException('Service id not set', BullGuardManagerException::ERR_MISSING_DATA);
        }

        // Payment was successful: Configure
        if (true !== $objNewBullGuardProduct->configure()) {

            throw new BullGuardManagerException('Can\'t configure product', BullGuardManagerException::ERR_PRODUCT);
        }

        // Set invoice date
        $arrProductComponents = $objNewBullGuardProduct->getProductComponents();

        foreach ($arrProductComponents as $objProductComponent) {

            $objProductComponent->setNextInvoiceDate($uxtProRataCalculationDate);
        }

        // If it's PAYG account and has no biling date set it to BG billing date
        if (('' == $this->arrService['next_invoice'] || '9999-09-09' == $this->arrService['next_invoice']) && false == userdataIsAccountAdsl($this->intServiceId)) {

            userdata_service_set_next_invoice($this->intServiceId, date('Y-m-d', $uxtProRataCalculationDate));
        }

        // Activate components: CSecurityProduct::enable()
        if (true !== $objNewBullGuardProduct->enable()) {

            throw new BullGuardManagerException('Problem with product activation occured', BullGuardManagerException::ERR_PRODUCT);
        }

        // Finally set subscription expiry date and email customer
        foreach ($arrProductComponents as $objProductComponent)
        {
            if (true == ($objProductComponent instanceof CBullGuardProductComponent)) {

                // If customer has trial component that was destroyed, get installation date
                if (false != $utxInstallDate) {

                    $objProductComponent->setInstalationDate($utxInstallDate);
                }

                $objProductComponent->setExpiryDate($uxtNextInvoice);
            }
        }

        //Generate Invoices
        $invoiceItems = array(
            array(
                'description' => $paymentOption['arrProRata']['strProRataDescription'],
                'amount' => ($paymentOption['arrProRata']['intProRataCost']/100),
                'gross' => true,
                'component_id' => $objNewBullGuardProduct->getComponentID()
            ),
            array(
                'description' => CProductComponent::getSchedulePaymentDescription(
                    $paymentOption['intTariffID'],
                    $uxtProRataCalculationDate,
                    $uxtNextInvoice
                ),
                'amount' => ($paymentOption['intCostIncVatInPence']/100),
                'gross' => true,
                'component_id' => $objNewBullGuardProduct->getComponentID()
            )
        );

        // Invoice description
        $invoiceDesc = CProductComponent::getInvoiceProductComponentDisplayName(
            $paymentOption['intTariffID']
        );

        $intInvoiceId = financial_generate_invoice(
            $this->intServiceId,
            $invoiceDesc,
            $invoiceItems,
            $transactionId,
            date('Y-m-d')
        );

        if (false == is_numeric($intInvoiceId) || 0 == $intInvoiceId) {

            throw new BullGuardManagerException('Failed to create invoice.', BullGuardUserException::ERR_PAYMENT);
        }

        $arrTplParams = array(
            'arrOption'	=> $paymentOption,
            'utxExpity'	=> $uxtNextInvoice,
            'floCost'	=> $floActivationCost,
            'intInvoiceId'	=> $intInvoiceId
        );

        $arrEmailParams = array('service_id' => $this->intServiceId);

        $this->emailCustomer('ACTIVATE', $arrEmailParams);
        $this->raiseTicket('ACTIVATE', $arrTplParams);

        return $objNewBullGuardProduct->getComponentID();
    }

    /**
     * Deactivate product and raising refund ticket.
     *
     * @return int
     */
    public function deactivateProduct()
    {
        // First of all check is component valid - it not just destroy it
        if (!$this->getComponent() || !$this->getSubscription()) {

            $this->getProduct()->destroy();
            return true;
        }

        // If it's a trial, just destroy it
        if (true == ($this->getComponent() instanceof CBullGuardTrialComponent)) {

            $this->getProduct()->destroy();
            return true;
        }

        // Find subscription component and next invoice date
        $intTicketId = 0;

        $arrCurrentTariff = CProductComponent::getTariffDetails($this->getSubscription()->getTariffID());
        $utxNextInvoice = $this->getSubscription()->getNextInvoiceDate();
        $utxExpiry = $this->getComponent()->getExpiryDate();

        if (true == empty($arrCurrentTariff) || false === $utxNextInvoice) {

            throw new BullGuardManagerException('Unable to find correct subscription tariff for product.', BullGuardManagerException::ERR_MISSING_DATA);
        }

        switch ($arrCurrentTariff['strPaymentFrequencyHandle'])
        {
            case 'MONTHLY':
                // No refund, don't reset expiry date in config table, customer's subscription will be valid
                // until end of biling month
                $this->getProduct()->queueDestroy();

                $intTicketId = $this->raiseTicket('DEACTIVATE', array('utxExpiry'  => $utxExpiry));
                break;

            case 'ANNUAL':
                // Calculate refund
                $intNumOfDays = round(($utxNextInvoice - time()) / 86400);
                $intCostPerDay = round(($arrCurrentTariff['intCostIncVatPence']/365), 2);

                $intRefund = round($intNumOfDays * $intCostPerDay);

                $intTicketId = $this->raiseTicket(
                        'DEACTIVATE_REFUND',
                        array(
                        'intNumOfDaysToRefund'	=> $intNumOfDays,
                        'intRefundInPence'	=> $intRefund
                        ));

                foreach ($this->getProduct()->getProductComponents() as $objComponent)
                {
                    if (true == ($objComponent instanceof CBullGuardProductComponent)) {

                        $objComponent->setExpiryDate();
                    }
                }

                // Destroy immediatly
                $this->getProduct()->destroy();
                break;
        }

        return $intTicketId;
    }

    /**
     * Get activation payment informations: availible tarrifs, pro-rata charges
     *
     * @return array
     */
    public function &getActivationPayments()
    {
        if (0 == $this->intServiceId) {

            throw new BullGuardManagerException('Service id not set', BullGuardManagerException::ERR_MISSING_DATA);
        }

        if (true == empty($this->arrPaymentOptionsCache)) {

            $this->arrPaymentOptionsCache = CProduct::getProductPaymentFrequencyOptions(COMPONENT_BULL_GUARD);

            foreach ($this->arrPaymentOptionsCache as $intIndex => $arrOption)
            {
                $this->arrPaymentOptionsCache[$intIndex]['arrProRata'] = CProductComponent::generateProRataPaymentDetails($this->intServiceId, $arrOption['intTariffID']);
            }
        }

        return $this->arrPaymentOptionsCache;
    }

    /**
     * Check can Bull Guard product be activated
     *
     * @return bool
     */
    public function canActivateBullGuard()
    {
        if ('active' <> $this->arrService['status']) {

            return false;
        }

        if (true == empty($this->arrComponent) ||
           ('active' != $this->arrComponent['status'] && 'queued-activate' != $this->arrComponent['status']) ||
            $this->arrComponent['component_type_id'] == COMPONENT_BULL_GUARD_TRIAL ||
           (false === $this->getSubscription() && false === $this->getComponent())) {

                return true;
        }

        return false;
    }

    public function isBullGuardQueuedActivate()
    {
        return ('queued-activate' == $this->arrComponent['status']);
    }

    /**
     * Process signup pf product
     *
     * @return unknown
     */
    public function processSignup()
    {
        if (true == empty($this->arrComponent)) {

            $this->signupProduct();
        }

        return false;
    }

    public function getPostSignupActivationInvoiceItem()
    {
        if (false == empty($this->arrComponent) && 'queued-activate' == $this->arrComponent['status']) {

            $arrInvoiceItem = array(
                'amount'       => ($this->getSubscription()->getCurrentCost() / 100),
                'description'  => $this->getComponent()->getInvoiceProductComponentDisplayName($this->getSubscription()->getTariffID()),
                'gross'        => true,
                'type'         => 'security',
                'component_id' => $this->getComponent()->getComponentID(),
            );

            return $arrInvoiceItem;
        }

        return array();
    }

    /**
     * Process activation of BG product
     *
     */
    public function processPostSignupActivation()
    {
        if (false == empty($this->arrComponent) && 'queued-activate' == $this->arrComponent['status']) {

            if (true !== $this->getProduct()->configure()) {

                throw new BullGuardManagerException('Can\'t configure product', BullGuardManagerException::ERR_PRODUCT);
            }

            //Due to problem fix 63169 - we need to prevent duplication of initial payment,
            $arrArgs = array();
            $arrArgs['intPrepaidCount'] = 1;

            // Activate components: CSecurityProduct::enable()
            if (true !== $this->getProduct()->enable($arrArgs)) {

                throw new BullGuardManagerException('Problem with product activation occured', BullGuardManagerException::ERR_PRODUCT);
            }

            // Set proper invoice date for subscription component
            $this->getSubscription()->refreshInstance($this->getSubscription()->getProductComponentInstanceID());

            $intTariffId = $this->getSubscription()->getTariffID();
            $uxtNextInvoiceDate = CProductComponent::calculatePaymentPeriodEnd(time(), $intTariffId, $this->intServiceId);

            if(false == $this->getSubscription()->setNextInvoiceDate($uxtNextInvoiceDate)) {

                throw new BullGuardManagerException('Unable to set next invoice date for subscription component', BullGuardManagerException::ERR_PRODUCT);
            }

            // Finally set subscription expiry date
            $utxContractEnd =  $this->getSubscription()->getContractEnd();
            $utxExpiryDate = mktime(
                0,
                0,
                0,
                date('m', $utxContractEnd),
                date('d', $utxContractEnd) + 1,
                date('Y', $utxContractEnd)
                );

            $this->getComponent()->setExpiryDate($utxExpiryDate);
        }
    }

    /**
     * Checks is BullGuard availible for given ISP
     *
     * @static
     * @throws BullGuardManagerException
     *
     * @param $strIsp
     * @return bool
     */
    public static function isAvalibleForIsp($strIsp)
    {
        if ('' == trim($strIsp)) {

            throw new BullGuardManagerException("No isp passed to BullGuardManager::isAvalibleForIsp() method");
        }

        $arrIspDetails = product_visp_config_get($strIsp);

        if (true == empty($arrIspDetails)) {

            throw new BullGuardManagerException("Invalid isp passed to BullGuardManager::isAvalibleForIsp() method");
        }

        $arrBullGuardVisps = array('ma', 'wr', 'gb');

        return in_array($arrIspDetails['short_brand'], $arrBullGuardVisps);
    }
}
