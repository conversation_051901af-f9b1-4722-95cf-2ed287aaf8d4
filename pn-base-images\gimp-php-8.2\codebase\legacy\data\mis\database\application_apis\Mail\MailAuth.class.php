<?php

/**
 * Low-level interface to the mailauth database
 */
class Mail_MailAuth
{
	private $prim;


	/**
	 * Factory method
	 */
	public static function get()
	{
		return new self(Lib_SqlPrimitives::singleton());
	}

	public function __construct(Lib_SqlPrimitives $prim)
	{
		$this->prim = $prim;
	}

	/**
	 * getAuthIsp
	 * massage isp names into auth db table naming convention
	 *
	 * @param Mail_Service $service
	 * @return String converted is name
	 */
	protected static function getAuthIsp(Mail_Service $service)
	{
		// Adding this kludge due to not knowing if this class is to be later
		// used on Plusnet -- currently it looks to be MAAF-only, but who knows
		// what'll happen in t'future :D

		$strIsp = $service->getIsp();

		switch ($strIsp) {

			case 'plus.net':

				return 'plusnet';
		}


		return $strIsp;
	}

	/**
	 * getUserId
	 * get auth id by looking at login name in appropriate visp auth table
	 *
	 * @param Mail_Service $service
	 * @param String $username
	 * @param String $alias
	 * @return Integer
	 */
	public function getUserId(Mail_Service $service, String $username, String $alias)
	{
		if('bv.master' != $alias) {
			$username = $username . '+' . $alias;
		}
		$db = get_named_connection_with_db('mailauth');
		$query = "SELECT id FROM mailusers_" . self::getAuthIsp($service) . "
		          WHERE username = '" . PrimitivesRealEscapeString($username, $db) . "'";
		$id = $this->prim->PrimitivesResultGet(PrimitivesQueryOrExit($query, $db), 'id');

		return $id ? new Int($id) : FALSE;
	}

	/**
	 * addUser
	 * create new auth user for a mailbox config
	 *
	 * @param Mail_Service $service
	 * @param String $username
	 * @param String $cryptPassword
	 * @param String $soulstoneUsername - optional string that is their username
	 *                                    in Soulstone. This is used to influence
	 *                                    how the mail directory path is generated.
	 * @return Integer auth id
	 */
	public function addUser(Mail_Service $service, String $username, String $cryptPassword, String $soulstoneUsername = NULL)
	{
		$db = get_named_connection_with_db('mailauth');

		$intPaidFor = ($service->isPaidForService()->getValue()) ? 1 : 0;

		$query = "REPLACE INTO auth.mailusers_" . self::getAuthIsp($service) . " SET
		          username = '" . PrimitivesRealEscapeString($username, $db) . "',
		          passwd = '" . PrimitivesRealEscapeString($cryptPassword, $db) . "',
		          sysuser = 'mail',
		          homedir = '" . PrimitivesRealEscapeString($service->getHomeDir($soulstoneUsername), $db) . "',
		          intPaidFlag = '{$intPaidFor}'";
		PrimitivesQueryOrExit($query, $db);
		$id = $this->prim->PrimitivesInsertIdGet($db);

		$query = "REPLACE INTO auth.mailmgt_" . self::getAuthIsp($service) . " SET id = $id, intServiceID = " .
		         $service->getServiceId() . ", destroyed = 0, created = 0 ";
		PrimitivesQueryOrExit($query, $db);

		return new Int($id);
	}

	/**
	 * setPassword
	 * updates password for given user
	 *
	 * @param Mail_Service $service
	 * @param String $username
	 * @param String $cryptPassword
	 * @return void
	 */
	public function setPassword(Mail_Service $service, String $alias, String $cryptPassword)
	{

		if($alias == 'bv.master') {
			$login = $service->getUsername();
		} else {
			$login = $service->getUsername() . '+' . $alias;
		}

		$db = get_named_connection_with_db('mailauth');
		$query = "UPDATE mailusers_" . self::getAuthIsp($service) . "
		          SET passwd = '" . PrimitivesRealEscapeString($cryptPassword, $db) . "'
		          WHERE username = '" . PrimitivesRealEscapeString($login, $db) . "'";
		PrimitivesQueryOrExit($query, $db);
	}

	/**
	 * destroyUser
	 * does not delete user, just flags them up to be destroyed by netops component destroy script
	 *
	 * @param Mail_Service $service
	 * @param Integer $userId
	 * @return void
	 */
	public function destroyUser(Mail_Service $service, Int $userId)
	{
		$db = get_named_connection_with_db('mailauth');
		$query = "UPDATE auth.mailmgt_" . self::getAuthIsp($service) . " SET destroyed = 1 WHERE id = $userId";
		PrimitivesQueryOrExit($query, $db);
	}

	/**
	 * destroyUsersForService
	 * does not delete users, just flags them up to be destroyed by netops component destroy script
	 *
	 * @param Mail_Service $service
	 * @return void
	 */
	public function destroyUsersForService(Mail_Service $service)
	{
		$db = get_named_connection_with_db('mailauth');
		$query = "UPDATE auth.mailmgt_" . self::getAuthIsp($service) . " SET destroyed = 1 WHERE intServiceID = " .
		         $service->getServiceId();
		PrimitivesQueryOrExit($query, $db);
	}

	/**
	 * setAuthField
	 * updates the appropriate field in mailusers_$isp (intPaidFlag, bolExtPop, bolExtWebmail)
	 *
	 * @param Mail_Service $service
	 * @param Int $authId
	 * @param Bool $state
	 * @param String $strFieldName
	 * @return void
	 */
	private function setAuthField(Mail_Service $service, Int $authId, Bool $state, String $strFieldName)
	{
		$intFieldState = ($state->getValue()) ? 1 : 0;

		$strQuery = "UPDATE auth.mailusers_" . self::getAuthIsp($service) .
		            " SET {$strFieldName} = '{$intFieldState}' WHERE id = '{$authId}'";

		$dbhConn = get_named_connection_with_db('mailauth');

		PrimitivesQueryOrExit($strQuery, $dbhConn);
	}

	/**
	 * setPaidFlagState
	 * updates intPaidFlag in the auth db
	 *
	 * @param Mail_Service $service
	 * @param Int $authId
	 * @param Bool $state
	 * @return void
	 */
	public function setPaidFlagState(Mail_Service $service, Int $authId, Bool $state)
	{
		$this->setAuthField($service, $authId, $state, new String('intPaidFlag'));
	}

	/**
	 * setPaidFlagState
	 * updates bolExtPop3 in the auth db
	 *
	 * @param Mail_Service $service
	 * @param Int $authId
	 * @param Bool $state
	 * @return void
	 */
	public function setPop3State(Mail_Service $service, Int $authId, Bool $state)
	{
		$this->setAuthField($service, $authId, $state, new String('bolExtPop3'));
	}

	/**
	 * setWebmailState
	 * updates bolExtWebmail in the auth db
	 *
	 * @param Mail_Service $service
	 * @param Int $authId
	 * @param Bool $state
	 * @return void
	 */
	public function setWebmailState(Mail_Service $service, Int $authId, Bool $state)
	{
		$this->setAuthField($service, $authId, $state, new String('bolExtWebmail'));
	}

	/**
	 * setImapState
	 * updates bolExtImap in the auth db
	 *
	 * @param Mail_Service $service
	 * @param Int $authId
	 * @param Bool $state
	 * @return void
	 */
	public function setImapState(Mail_Service $service, Int $authId, Bool $state)
	{
		$this->setAuthField($service, $authId, $state, new String('bolExtImap'));
	}
}
