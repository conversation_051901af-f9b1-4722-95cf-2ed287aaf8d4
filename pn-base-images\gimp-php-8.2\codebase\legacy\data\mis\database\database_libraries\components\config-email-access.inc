<?php
/**
 * config-email-access.inc
 *
 * Access Library for Config Email
 *
 * @package    LegacyCodebase
 * @subPackage Database Libraries
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2012 Plusnet
 */

require_once('/local/data/mis/database/application_apis/Mail/Manager.class.php');
require_once(dirname(__FILE__) . '/config-email-access-php5.inc');

$global_component_configurators[COMPONENT_PLUS_EMAIL]  = "config_email_configurator";	// plus.net
$global_component_configurators[COMPONENT_PLUSNET_MAILBOX]  = "config_email_configurator";	// plus.net
$global_component_configurators[COMPONENT_F9_EMAIL] = "config_email_configurator";	// f9
$global_component_configurators[COMPONENT_FOL_EMAIL] = "config_email_configurator";	// freeonline
$global_component_configurators[COMPONENT_DABSOL_EMAIL] = "config_email_configurator";	// dabsol
$global_component_configurators[COMPONENT_SEARCHPRO_EMAIL] = "config_email_configurator";	// searchpro
$global_component_configurators[COMPONENT_PARTNER_EMAIL] = "config_email_configurator";	// partner
$global_component_configurators[COMPONENT_JOHNLEWIS_EMAIL] = "config_email_configurator";	// partner

// Hack to insert the component configurator array into PHP5's global scope
// if it's not already there
if(!isset($GLOBALS['global_component_configurators']))
{
    $GLOBALS['global_component_configurators'] = $global_component_configurators;
}
else
{
    foreach($global_component_configurators as $intIndex => $strConfigurator)
    {
        $GLOBALS['global_component_configurators'][$intIndex] = $strConfigurator;
    }
}