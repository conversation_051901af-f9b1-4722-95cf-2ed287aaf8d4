<?php

require_once('/local/data/mis/database/database_libraries/sql_primitives.inc');
require_once('/local/data/mis/database/database_libraries/CoreObjects/CObject/CObject.inc');
require_once('/local/data/mis/database/database_libraries/CoreObjects/Utility/CValidator.inc');
require_once('/local/data/mis/database/database_libraries/CoreObjects/Financial/CFinancialHelper.inc');
require_once('/local/data/mis/database/database_libraries/CoreObjects/Financial/CScheduledPayment.inc');
require_once('/local/data/mis/database/database_libraries/CoreObjects/Financial/CPaymentScheduler.inc');

require_once DATABASE_LIBRARY_ROOT . '/userdata-access.inc';

/**
 * One off payment scheduler
 */
class COneOffPaymentScheduler extends CPaymentScheduler
{
	/**
	 * Service Id (userdata.services)
	 */
	protected $intServiceId = null;

	/**
	 * Constructor
	 *
	 * @param integer $intServiceId
	 * @param string $strActionerId
	 */
	public function __construct($intServiceId, $strActionerId)
	{
		$arrService = userdata_service_get($intServiceId);

		if(!$arrService) {
			throw new Exception('Invalid service id!');
		}

		$this->intServiceId = $intServiceId;
		$this->m_intScheduledPaymentTypeID = CScheduledPayment::getTypeIDByHandle("ONE_OFF");

		parent::CPaymentScheduler($strActionerId);
	}

	/**
	 * Get all scheduled payments
	 *
	 * @return array Scheduled payments
	 */
	public function getScheduledPayments()
	{
		$dbhFinancial = get_named_connection_with_db('financial');

		$strQuery = "
			SELECT
			  sp.intScheduledPaymentID,
			  sp.intScheduledPaymentTypeID,
			  sp.intAccountID,
			  sp.intSalesInvoiceID,
			  sp.intLineItemID,
			  sp.vchDescription as strDescription,
			  sp.vchActionerID as strActionerID,
			  sp.intAmountExVatPence,
			  sp.intVatPence,
			  UNIX_TIMESTAMP(sp.dteDue) as uxtDateDue,
			  sp.bolOnBilling,
			  UNIX_TIMESTAMP(sp.dteStartPeriodCovered) as uxtStartPeriodCovered,
			  UNIX_TIMESTAMP(sp.dteEndPeriodCovered) as uxtEndPeriodCovered,
			  UNIX_TIMESTAMP(sp.dtmInvoiced) as uxtInvoiced,
			  UNIX_TIMESTAMP(sp.dtmDateExpected) as uxtDateExpected,
			  UNIX_TIMESTAMP(sp.dtmCancelled) as uxtCancelled
			FROM financial.tblScheduledPayment sp
			INNER JOIN financial.tblScheduledPaymentType spt
			  ON sp.intScheduledPaymentTypeID = spt.intScheduledPaymentTypeID
			INNER JOIN userdata.accounts a
			  ON sp.intAccountID = a.account_id
			INNER JOIN userdata.users u
			  ON a.customer_id = u.customer_id
			INNER JOIN userdata.services s
			  ON u.user_id = s.user_id
			WHERE s.service_id = {$this->intServiceId}
			  AND spt.vchHandle = 'ONE_OFF'
		";

		if(!$bolIncludeNotDue) {
			$strQuery .= ' AND sp.dteDue <= NOW()';
		}

		if(!$bolIncludeCancelled) {
			$strQuery .= ' AND sp.dtmCancelled IS NULL';
		}

		if(!$bolIncludeInvoiced) {
			$strQuery .= ' AND sp.dtmInvoiced IS NULL AND sp.intSalesInvoiceID IS NULL';
		}

		$resResult = PrimitivesQueryOrExit($strQuery, $dbhFinancial);
		return PrimitivesResultsAsArrayGet($resResult);
	}

	/**
	 * Find the Account ID for this payment
	 * *should* be protected / private but needs to be public because of parent definition
	 *
	 * @return integer Account ID
	 */
	public function getAccountID()
	{
		$dbhUserdata = get_named_connection_with_db('userdata');

		$strQuery = "
			SELECT
			  a.account_id AS intAccountId
			FROM userdata.services s
			INNER JOIN userdata.users u
			  ON s.user_id = u.user_id
			INNER JOIN userdata.accounts a
			  ON u.customer_id = a.customer_id
			WHERE s.service_id = {$this->intServiceId}
		";

		$resResults = PrimitivesQueryOrExit($strQuery, $dbhUserdata);

		if(!$resResults) {
			return false;
		}

		$arrResult = PrimitivesResultGet($resResults);

		if(count($arrResult) == 0) {
			return false;
		}

		$intAccountId = $arrResult['intAccountId'];

		return $intAccountId;
	}

	/**
	 * Interface compliance
	 * *should* be protected / private but needs to be public because of parent definition
	 *
	 * @return boolean
	 */
	public function createPaymentConfig()
	{
		// Not required
		return true;
	}
}
