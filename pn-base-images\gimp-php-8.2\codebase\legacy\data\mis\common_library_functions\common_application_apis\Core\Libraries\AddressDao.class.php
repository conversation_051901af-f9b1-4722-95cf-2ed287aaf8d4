<?php
/**
 * Address DAO class
 * 
 * @package    Core
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <akuryl<PERSON><EMAIL>>
 * 
 * @copyright  2008 PlusNet
 * @version    CVS: $Id: AddressDao.class.php,v 1.2 2008-04-08 04:43:13 akurylowicz Exp $
 * @since      File available since 18/02/2007
 */

/**
 * Address DAO class
 * 
 * <code>
 * <i>userdata.addresses</i> table
 * +-------------+---------------------------+------+-----+-------------------+----------------+
 * | Field       | Type                      | Null | Key | Default           | Extra          |
 * +-------------+---------------------------+------+-----+-------------------+----------------+
 * | address_id  | int(8) unsigned zerofill  |      | PRI | NULL              | auto_increment | 
 * | customer_id | int(8) unsigned           |      | MUL | 0                 |                | 
 * | house       | varchar(60)               |      |     |                   |                | 
 * | street      | varchar(60)               |      |     |                   |                | 
 * | town        | varchar(60)               |      |     |                   |                | 
 * | county      | varchar(60)               |      |     |                   |                | 
 * | postcode    | varchar(10)               |      | MUL |                   |                | 
 * | country     | char(3)                   |      |     |                   |                | 
 * | db_src      | varchar(4)                |      |     |                   |                | 
 * | timestamp   | timestamp                 | YES  |     | CURRENT_TIMESTAMP |                | 
 * | status      | enum('active','inactive') | YES  |     | active            |                | 
 * +-------------+---------------------------+------+-----+-------------------+----------------+
 * 
 * <i>userdata.tblAddressType</i> table
 * +------------------+---------------------+------+-----+-------------------+-------+
 * | Field            | Type                | Null | Key | Default           | Extra |
 * +------------------+---------------------+------+-----+-------------------+-------+
 * | intAddressId     | int(8)              |      | PRI | 0                 |       |
 * | intAddressTypeId | tinyint(3) unsigned |      | PRI | 0                 |       |
 * | stmTimestamp     | timestamp           | YES  |     | CURRENT_TIMESTAMP |       |
 * +------------------+---------------------+------+-----+-------------------+-------+
 * 
 * <i>userdata.tblAddressType</i> table
 * +------------------+---------------------+------+-----+-------------------+----------------+
 * | Field            | Type                | Null | Key | Default           | Extra          |
 * +------------------+---------------------+------+-----+-------------------+----------------+
 * | intAddressTypeId | tinyint(3) unsigned |      | PRI | NULL              | auto_increment |
 * | vchHandle        | varchar(32)         | YES  |     | NULL              |                |
 * | vchDisplayName   | varchar(50)         | YES  |     | NULL              |                |
 * | stmTimestamp     | timestamp           | YES  |     | CURRENT_TIMESTAMP |                |
 * +------------------+---------------------+------+-----+-------------------+----------------+
 * </code>
 * 
 * @package    Core
 * <AUTHOR> Marek <<EMAIL>>
 * <AUTHOR> Kurylowicz <<EMAIL>>
 * @copyright  2008 PlusNet
 */
class Core_AddressDao extends Db_Object implements Core_IAddressDao
{
	/**
	 * getAddressDao has "rows: multiple" and uses "IN" clause
	 * @access protected
	 * @var bool
	 */
	protected $bolMultiSelectHack = TRUE;

	/**
	 * @access protected
	 * @var string Primary key name
	 */
	protected $strPrimaryKey = 'address_id';

	protected $address_id; 

	protected $customer_id;

	protected $house = '';

	protected $street = '';

	protected $town = '';

	protected $county = '';

	protected $postcode = '';

	protected $country = '';

	protected $status = '';

	protected $strTypeHandle = null;

	protected $strTypeName = null;

	protected $arrSetterFunctions = array(
		'address_id'	=> 'AddressId', 
		'customer_id'	=> 'CustomerId', 
		'house'			=> 'House',
		'street'		=> 'Street',
		'town'			=> 'Town',
		'county'		=> 'County',
		'postcode'		=> 'Postcode',
		'country'		=> 'Country',
		'status'		=> 'Status',
		'strTypeHandle'	=> 'TypeHandle',
		'strTypeName'	=> 'TypeName'
		);
	
	public function init() {}

	/**
	 * Fetch a single row from <i>userdata.addresses</i> table
	 * 
	 * @access public
	 * @static
	 *  
	 * @uses Db_Object::getObject()
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @param int    $intAddressId
	 * @param string $strTransaction
	 * 
	 * @return Core_AddressDao
	 */
	public static function get($intAddressId, $strTransaction = Db_Manager::DEFAULT_TRANSACTION)
	{
		$strDebugSource = __CLASS__ . ' ' . __METHOD__;
		Dbg_Dbg::write("$strDebugSource: Fetching address for Id $intAddressId", 'Core');
		
		return parent::getObject(get_class(), $intAddressId, $strTransaction);
	}

	/**
	 * Fetches multiple rows from <i>userdata.addresses</i> table
	 * for given <i>$intUserId</i>
	 * 
	 * @access public
	 * @static
	 *  
	 * @uses Db_Object::findObjects()
	 * @uses getAddressIdsForUserId
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @param int    $intUserId
	 * @param string $strTransaction
	 * 
	 * @return array Array of Core_AddressDao objects
	 */
	public static function getAddresess($intUserId, $strTransaction = Db_Manager::DEFAULT_TRANSACTION)
	{
		$strDebugSource = __CLASS__ . ' ' . __METHOD__;
		Dbg_Dbg::write("$strDebugSource: Fetching addresses for User Id $intUserId", 'Core');
		
		$arrAddressess = self::findObjects(__CLASS__, 'getAddressIdsForUserId', array('intUserId' => $intUserId), $strTransaction);

		return $arrAddressess;
	}

	/**
	 * Sets address ID
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @param int $intAddressId
	 */
	public function setAddressId($intAddressId)
	{
		 $this->address_id = $intAddressId;
	}

	/**
	 * Sets customer ID
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @param int $intCustomerId
	 */
	public function setCustomerId($intCustomerId)
	{
		 $this->customer_id = $intCustomerId;
	}

	/**
	 * Sets house name/number
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @param string $strHouse
	 */
	public function setHouse($strHouse)
	{
		 $this->house = $strHouse;
	}

	/**
	 * Sets street name
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @param string $strStreet
	 */
	public function setStreet($strStreet)
	{
		 $this->street = $strStreet;
	}

	/**
	 * Sets town/city name
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @param string $strTown
	 */
	public function setTown($strTown)
	{
		 $this->town = $strTown;
	}

	/**
	 * Sets country name
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @param string $strCounty
	 */
	public function setCounty($strCounty)
	{
		 $this->county = $strCounty;
	}

	/**
	 * Sets postcode
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @param string $strPostcode
	 */
	public function setPostcode($strPostcode)
	{
		 $this->postcode = $strPostcode;
	}

	/**
	 * Sets country name
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @param string $strCountry
	 */
	public function setCountry($strCountry)
	{
		 $this->country = $strCountry;
	}

	/**
	 * Sets address status
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @param string $strStatus
	 */
	public function setStatus($strStatus)
	{
		 $this->status = $strStatus;
	}

	/**
	 * Sets address type handle
	 *
	 * @access public
	 * 
	 * @see userdata.tblAddressType
	 * @see userdata.tblAddressHasType
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @param string $strHandle
	 */
	public function setTypeHandle($strHandle)
	{
		 $this->strTypeHandle = $strHandle;
	}

	/**
	 * Sets address type display name
	 *
	 * @access public
	 *
	 * @see userdata.tblAddressType
	 * @see userdata.tblAddressHasType
	 *  
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @param string $strName
	 */
	public function setTypeName($strName)
	{
		 $this->strTypeName = $strName;
	}

	/**
	 * Returns address ID
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @return int
	 */
	public function getAddressId()
	{
		 return $this->address_id;
	}

	/**
	 * Returns customer ID
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @return int
	 */
	public function getCustomerId()
	{
		 return $this->customer_id;
	}

	/**
	 * Returns house name/number
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @return string
	 */
	public function getHouse()
	{
		 return $this->house;
	}

	/**
	 * Returns street name
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @return string
	 */
	public function getStreet()
	{
		 return $this->street;
	}

	/**
	 * Returns town/city name
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @return string
	 */
	public function getTown()
	{
		 return $this->town;
	}

	/**
	 * Returns county name
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @return string
	 */
	public function getCounty()
	{
		 return $this->county;
	}

	/**
	 * Returns postcode
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @return string
	 */
	public function getPostcode()
	{
		 return $this->postcode;
	}

	/**
	 * Returns country name
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @return string
	 */
	public function getCountry()
	{
		 return $this->country;
	}

	/**
	 * Returns address status
	 *
	 * @access public
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @return string
	 */
	public function getStatus()
	{
		 return $this->status;
	}

	/**
	 * Returns address type handle
	 *
	 * @access public
	 * 
	 * @see userdata.tblAddressType
	 * @see userdata.tblAddressHasType
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @return string
	 */
	public function getTypeHandle()
	{
		 return $this->strTypeHandle;
	}

	/**
	 * Returns address type display name
	 *
	 * @access public
	 * 
	 * @see userdata.tblAddressType
	 * @see userdata.tblAddressHasType
	 * 
	 * <AUTHOR> Marek" <<EMAIL>>
	 * 
	 * @return string
	 */
	public function getTypeName()
	{
		 return $this->strTypeName;
	}
	
}
