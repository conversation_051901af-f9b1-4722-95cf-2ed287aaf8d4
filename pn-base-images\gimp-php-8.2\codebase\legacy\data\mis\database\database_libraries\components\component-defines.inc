<?php
/*
 * Defines specific to components
 *
 *
 *
 */

//
// This takes care of redefining
//
require_once '/local/data/mis/common_library_functions/configDefineIfMissing.inc';

configDefineIfMissing('COMPONENT_GENERIC_WEBSTATS_STANDARD',  '23');
configDefineIfMissing('COMPONENT_GENERIC_WEBSTATS_ADVANCED',  '24');
configDefineIfMissing('COMPONENT_GENERIC_NIGHT_TIME_DIAL_UP', '200');
configDefineIfMissing('COMPONENT_GENERIC_FIREWALL',           '423');
configDefineIfMissing('COMPONENT_GENERIC_CONTENT_FILTER',     '494');

// Plus components

configDefineIfMissing('COMPONENT_PLUS_DIALUP_0845_RESIDENTIAL',          '17');
configDefineIfMissing('COMPONENT_PLUS_DIALUP_0845_BIZ',                  '19');
configDefineIfMissing('COMPONENT_PLUS_DIALUP_SURFTIME_LITE',             '22');
configDefineIfMissing('COMPONENT_PLUS_DIALUP_SURFTIME_OFFPEAK',          '2');
configDefineIfMissing('COMPONENT_PLUS_DIALUP_SURFTIME_24',               '1');
configDefineIfMissing('COMPONENT_PLUS_DIALUP_SURFTIME_BIZ',              '20');
configDefineIfMissing('COMPONENT_PLUS_DIALUP_ADSL_500',                  '51');
configDefineIfMissing('COMPONENT_PLUS_STATIC_IP_ADSL_RESIDENTIAL',       '171');
configDefineIfMissing('COMPONENT_PLUS_DIALUP_ADSL_P500',                 '69');
configDefineIfMissing('COMPONENT_PLUS_DIALUP_ADSL_S500',                 '53');
configDefineIfMissing('COMPONENT_PLUS_DIALUP_ADSL_S1000',                '54');
configDefineIfMissing('COMPONENT_PLUS_DIALUP_ADSL_S2000',                '55');
configDefineIfMissing('COMPONENT_PLUS_STATIC_IP_SURFTIME',               '21');
configDefineIfMissing('COMPONENT_PLUS_STATIC_IP_ADSL_4',                 '60');
configDefineIfMissing('COMPONENT_PLUS_STATIC_IP_ADSL_1',                 '73');
configDefineIfMissing('COMPONENT_PLUS_STATIC_IP_ADSL_8',                 '169');
configDefineIfMissing('COMPONENT_PLUS_STATIC_IP_ADSL_16',                '176');
configDefineIfMissing('COMPONENT_PLUS_STATIC_IP_BIZSURF',                '201');
configDefineIfMissing('COMPONENT_PLUS_STATIC_IP_ADSL_64',                '229');
configDefineIfMissing('COMPONENT_PLUS_ECOMMERCE_BASIC',                  '13');
configDefineIfMissing('COMPONENT_PLUS_ECOMMERCE_ADVANCED',               '14');
configDefineIfMissing('COMPONENT_PLUS_ECOMMERCE_SUPER',                  '15');
configDefineIfMissing('COMPONENT_PLUS_PORTAL_LOGIN',                     '16');
configDefineIfMissing('COMPONENT_PLUS_WEBSPACE',                         '4');
configDefineIfMissing('COMPONENT_PLUS_CGI',                              '6');
configDefineIfMissing('COMPONENT_PLUS_EMAIL',                            '5');
configDefineIfMissing("COMPONENT_PLUSNET_MAILBOX",                       '796');  // CUSTOMER-5101: Unused plusnet mailbox component
configDefineIfMissing('COMPONENT_PLUS_FRONTPAGE',                        '9');
configDefineIfMissing('COMPONENT_PLUS_DIALUP_0845_FREEPLUS_RESIDENTIAL', '70');
configDefineIfMissing('COMPONENT_PLUS_DIALUP_0845_CHARGED_RESIDENTIAL',  '71');
configDefineIfMissing('COMPONENT_PLUS_DIALUP_0845_CHARGED_BIZ',          '72');
configDefineIfMissing('COMPONENT_PLUS_ADSL_PORT_BLOCKED_STATIC_IP',      '180');
configDefineIfMissing('COMPONENT_PLUS_BBYW_DIALUP',                      '519');
configDefineIfMissing('COMPONENT_PLUSNET_BUSINESS_HOSTING',              '1515');


// Added for BRP-1301: Incorrect static IP component being added to new business products when customer adds this via the MC
configDefineIfMissing('COMPONENT_PLUS_ADSL_BUSINESS_SINGLE_STATIC_IP', '73');

// {{ FIXME: legacy badly named defines. root out and destroy!
configDefineIfMissing('COMPONENT_WEBSPACE',                     '4');   // FIXME: name
configDefineIfMissing('COMPONENT_CGI',                          '6');   // FIXME: name
configDefineIfMissing('COMPONENT_EMAIL',                        '5');   // FIXME: name
configDefineIfMissing('COMPONENT_FRONTPAGE',                    '9');   // FIXME: name
// }}


// F9 components

configDefineIfMissing('COMPONENT_F9_DIALUP_0845_RESIDENTIAL', '30');
configDefineIfMissing('COMPONENT_F9_DIALUP_0845_BUSINESS',    '31');
configDefineIfMissing('COMPONENT_F9_DIALUP_0820_BUSINESS',    '32');
configDefineIfMissing('COMPONENT_F9_DIALUP_0800_RESIDENTIAL', '33');
configDefineIfMissing('COMPONENT_F9_DIALUP_0800_BUSINESS',    '34');
configDefineIfMissing('COMPONENT_F9_STATIC_IP_DIALUP',        '61');
configDefineIfMissing('COMPONENT_F9_CGI',                     '28');
configDefineIfMissing('COMPONENT_F9_FRONTPAGE',               '27');
configDefineIfMissing('COMPONENT_F9_EMAIL',                   '25');
configDefineIfMissing('COMPONENT_F9_PORTAL_LOGIN',            '29');
configDefineIfMissing('COMPONENT_F9_WEBSPACE',                '26');
configDefineIfMissing('COMPONENT_F9_DIALUP_ADSL_P500',        '135');
configDefineIfMissing('COMPONENT_F9_DIALUP_ADSL_S500',        '132');
configDefineIfMissing('COMPONENT_F9_DIALUP_ADSL_S1000',       '133');
configDefineIfMissing('COMPONENT_F9_DIALUP_ADSL_S2000',       '134');
configDefineIfMissing('COMPONENT_F9_BBYW_DIALUP',             '520');

// FreeOnline components

configDefineIfMissing('COMPONENT_FOL_DIALUP_0845',       '36');
configDefineIfMissing('COMPONENT_FOL_PORTAL_LOGIN',      '35');
configDefineIfMissing('COMPONENT_FOL_EMAIL',             '39');
configDefineIfMissing('COMPONENT_FOL_WEBSPACE',          '40');
configDefineIfMissing('COMPONENT_FOL_MYSQL',             '85');
configDefineIfMissing('COMPONENT_FOL_DIALUP_ADSL_P500',  '139');
configDefineIfMissing('COMPONENT_FOL_DIALUP_ADSL_S500',  '136');
configDefineIfMissing('COMPONENT_FOL_DIALUP_ADSL_S1000', '137');
configDefineIfMissing('COMPONENT_FOL_DIALUP_ADSL_S2000', '138');
configDefineIfMissing('COMPONENT_FOL_BBYW_DIALUP',       '521');

// PlusnetUK components

configDefineIfMissing('COMPONENT_PNUK_PORTAL_LOGIN',          '37');
configDefineIfMissing('COMPONENT_PNUK_RESELLER_PORTAL_LOGIN', '66');
configDefineIfMissing('COMPONENT_PNUK_LLTRAFFIC_ANALYSIS',    '99');
configDefineIfMissing('COMPONENT_PNUK_DIALUP_ADSL_P500',      '131');
configDefineIfMissing('COMPONENT_PNUK_DIALUP_ADSL_S500',      '128');
configDefineIfMissing('COMPONENT_PNUK_DIALUP_ADSL_S1000',     '129');
configDefineIfMissing('COMPONENT_PNUK_DIALUP_ADSL_S2000',     '130');


// Dabsol components

configDefineIfMissing('COMPONENT_DABSOL_DIALUP_0845',         '41');
configDefineIfMissing('COMPONENT_DABSOL_PORTAL_LOGIN',    '63');
configDefineIfMissing('COMPONENT_DABSOL_EMAIL',       '42');
configDefineIfMissing('COMPONENT_DABSOL_WEBSPACE',        '43');


// Searchpro components

configDefineIfMissing('COMPONENT_SEARCHPRO_DIALUP_0845',      '44');
configDefineIfMissing('COMPONENT_SEARCHPRO_PORTAL_LOGIN',     '45');
configDefineIfMissing('COMPONENT_SEARCHPRO_EMAIL',        '46');
configDefineIfMissing('COMPONENT_SEARCHPRO_WEBSPACE',         '47');
configDefineIfMissing('COMPONENT_SEARCHPRO_CGI',          '48');
configDefineIfMissing('COMPONENT_SEARCHPRO_FRONTPAGE',    '49');
configDefineIfMissing('COMPONENT_SEARCHPRO_RESELLER_PORTAL_LOGIN', '68');

// Metronet components
 configDefineIfMissing('COMPONENT_METRONET_PORTAL_LOGIN', 495);

// Domain components

configDefineIfMissing('COMPONENT_FREE_UK_DOMAIN',               '12');
configDefineIfMissing('COMPONENT_UK_DOMAIN',                    '38');
configDefineIfMissing('COMPONENT_NON_UK_DOMAIN',                '62');
configDefineIfMissing('COMPONENT_NETSTART_FIRST_UK_DOMAIN',     '64');
configDefineIfMissing('COMPONENT_NETSTART_FIRST_NON_UK_DOMAIN', '65');
configDefineIfMissing('COMPONENT_POUND_CO_UK_DOMAIN',           '175');
configDefineIfMissing('COMPONENT_GROUPWARE_DOMAIN',             '182');

// Generic components

configDefineIfMissing('COMPONENT_FAX2EMAIL',              '3');
configDefineIfMissing('COMPONENT_MYSQL',              '10');
configDefineIfMissing('COMPONENT_NETANNOUNCE',        '18');
configDefineIfMissing('COMPONENT_MYCIRCULAR',         '84');
configDefineIfMissing('COMPONENT_UNIFIED_MESSAGING',          '8');
configDefineIfMissing('COMPONENT_FREE_TRIAL',         '170');
configDefineIfMissing('COMPONENT_GROUPWARE',         181);

// SURF COMPONENT (P2P & BINARY NEWSFEED RESTRICTION)
configDefineIfMissing('COMPONENT_SURF', 312);

// CAPACITY BASED CHARGING
configDefineIfMissing('COMPONENT_CBC_FLEX',            310);
configDefineIfMissing('COMPONENT_CBC_EXTRA_BANDWIDTH', 311);
configDefineIfMissing('COMPONENT_CBC_PAYG_BANDWIDTH',  322);
configDefineIfMissing('COMPONENT_CBC_PAYG_CALENDAR_MONTH',  556);
configDefineIfMissing('COMPONENT_CBC_PAYG_END_OF_MONTH', 615);

// Spam & Anti-Virus
configDefineIfMissing('COMPONENT_INCLUDED_SPAM_VIRUS_BUNDLE', 235);

// Hardware Bundles
configDefineIfMissing('COMPONENT_WIRELESS_ROUTER_BUNDLE', 417);
configDefineIfMissing('COMPONENT_WIRELESS_LAPTOP_BUNDLE', 418);
configDefineIfMissing('COMPONENT_WIRELESS_DESKTOP_BUNDLE', 419);

// Thomson Wireless hardware (see task:231783)
configDefineIfMissing('COMPONENT_WIRELESS_THOMSON_ROUTER_BUNDLE', 598);
configDefineIfMissing('COMPONENT_WIRELESS_THOMSON_1PORT_ROUTER', 600);
configDefineIfMissing('COMPONENT_WIRELESS_THOMSON_USB_ADAPTER', 599);

configDefineIfMissing('COMPONENT_WIRELESS_LAPTOP_DESKTOP_BUNDLE', 399);
configDefineIfMissing('COMPONENT_WIRELESS_DESKTOP_ONLY_BUNDLE', 400);

configDefineIfMissing('COMPONENT_VOIP_ROUTER_BUNDLE', 420);
configDefineIfMissing('COMPONENT_VOIP_ADAPTOR_BUNDLE', 421);

// MAAF HW wireless kit
configDefineIfMissing('COMPONENT_DLINK_WIRELESS_KIT', 535);

// Vista Harware
configDefineIfMissing('COMPONENT_BT_1800_HG_WIRELESS_ROUTER', 518);

// Configs for new CGI platform
configDefineIfMissing('COMPONENT_CCGI_PLUS', 393);
configDefineIfMissing('COMPONENT_CCGI_F9', 394);
configDefineIfMissing('COMPONENT_CCGI_FOL', 395);

//PlusTalk
configDefineIfMissing('PLUSTALK', 395);

//Wifi Java Services
configDefineIfMissing('WIFI_SERVICES_HOST', '**************');
configDefineIfMissing('WIFI_SERVICES_PORT', '80');
configDefineIfMissing('WIFI_SERVICES_PATH', 'jboss-net/services/WiFiComponent');
configDefineIfMissing('WIFI_SERVICES_URL', '************:8080/jboss-net/services/WiFiComponent');

// Mailbox and webspace product bundle products
configDefineIfMissing('ADD_ON_PRODUCT_BUNDLE_COMPONENT_ID', 511);

// Basic (limited functionality, can receive mail only from PN) mailboxes
configDefineIfMissing('COMPONENT_PLUS_BASIC_MAILBOX', 512);
configDefineIfMissing('COMPONENT_F9_BASIC_MAILBOX', 513);
configDefineIfMissing('COMPONENT_FOL_BASIC_MAILBOX', 514);

// Free dialup (0808)
configDefineIfMissing('COMPONENT_PLUS_HEAVY_FRIACO_DIALUP', 211);
configDefineIfMissing('COMPONENT_F9_HEAVY_FRIACO_DIALUP',   215);
configDefineIfMissing('COMPONENT_FOL_HEAVY_FRIACO_DIALUP',  220);
configDefineIfMissing('COMPONENT_MAAF_FRIACO_DIALUP',  532);
configDefineIfMissing('COMPONENT_WAITROSE_FRIACO_DIALUP',   581);
configDefineIfMissing('COMPONENT_GREENBEE_FRIACO_DIALUP',   582);

// RIN dialups
configDefineIfMissing('COMPONENT_PLUS_RIN_DIALUP', 517);
configDefineIfMissing('COMPONENT_FOL_RIN_DIALUP', 518);

// Community Site
configDefineIfMissing('COMPONENT_COMMUNITY_SITE', 523);

// ADSL Service Care Level
configDefineIfMissing('COMPONENT_ENHANCED_CARE', 559);
configDefineIfMissing('COMPONENT_ENHANCED_CARE_INCLUSIVE', 627);

// Security components
configDefineIfMissing('COMPONENT_BULL_GUARD',       546);
configDefineIfMissing('COMPONENT_BULL_GUARD_TRIAL', 547);

// Parental control component
configDefineIfMissing('COMPONENT_PARENTAL_CONTROL', 560);


//BV Legacy static ip component
configDefineIfMissing('COMPONENT_BV_LEGACY_STATIC_IP_ADSL_1', 550);

// Paper and PDF billing
configDefineIfMissing('COMPONENT_PAPERBILLING',      551);
configDefineIfMissing('COMPONENT_PAPERBILLING_FREE', 552);

// whether to add surcharge for credit card payments (Replaced with COMPONENT_NON_DD_SURCHARGE)
configDefineIfMissing('COMPONENT_CREDIT_CARD_SURCHARGE', 553);

// whether to add surcharge for non DD payments
configDefineIfMissing('COMPONENT_NON_DD_SURCHARGE', 918);

// MAAF Webmail component
configDefineIfMissing('COMPONENT_MAAF_WEBMAIL', 534);

// MAAF FreeDomain component
configDefineIfMissing('COMPONENT_MAAF_FREEDOMAIN', 533);
configDefineIfMissing('COMPONENT_MAAF_WEBSPACE', 557);

// MAAF portal login component
 configDefineIfMissing('COMPONENT_MAAF_PORTAL_LOGIN', 525);

// PARTNER components
configDefineIfMissing('COMPONENT_PARTNER_PORTAL_LOGIN', 586);
configDefineIfMissing('COMPONENT_PARTNER_ADMIN_LOGIN',  809);
configDefineIfMissing('COMPONENT_PARTNER_ENDUSER',      594);
configDefineIfMissing('COMPONENT_PARTNER_EMAIL',        587);
configDefineIfMissing('COMPONENT_PARTNER_ADMIN',        790);

// MAAF giganews component
configDefineIfMissing('COMPONENT_MAAF_GIGANEWS', 526);

// Max Premium
configDefineIfMissing('COMPONENT_MAX_PREMIUM', 612);
configDefineIfMissing('COMPONENT_MAX_PREMIUM_BUSINESS', 628);

/**
 * On live the last inset ID on products.service_components is
 * 936, which is "Refer 3 offer code". Therefore, when the SQL
 * is run for the release of Annex M on ADSL2+, the
 * service_component_id will be '938'.
 *
 * <AUTHOR> R. <<EMAIL>>
 * @since   2011-01-19
 */
configDefineIfMissing('COMPONENT_ANNEX_M',      938);

// uSwitch and MoneySavingSupermarket promotions
configDefineIfMissing('COMPONENT_199HP_OFFER', 621);
configDefineIfMissing('COMPONENT_FREEHP_OFFER', 622);


// Original PlusNet WLR products

configDefineIfMissing('COMPONENT_WLR_ANYTIME',         509);
configDefineIfMissing('COMPONENT_WLR_ANYTIME_PLUS',    510);
configDefineIfMissing('COMPONENT_WLR_EVENING_WEEKEND', 515);


// MAAF WLR products

configDefineIfMissing('COMPONENT_WLR_TALK',         554);
configDefineIfMissing('COMPONENT_WLR_TALK_ANYTIME', 555);


// Greenbee WLR products

configDefineIfMissing('COMPONENT_WLR_GREENBEE_PHONE',        582);
configDefineIfMissing('COMPONENT_WLR_GREENBEE_PHONE_BUNDLE', 583);


// PlusNet Home Phone Essential

configDefineIfMissing('COMPONENT_WLR_ESSENTIAL', 596);


// Business phone components
configDefineIfMissing('COMPONENT_WLR_BUS_ANY_NO', 1331);
configDefineIfMissing('COMPONENT_WLR_BUS_500_NO', 1332);
configDefineIfMissing('COMPONENT_WLR_BUS_1000_NO', 1333);
configDefineIfMissing('COMPONENT_WLR_BUS_PAYG_NO', 1334 );

configDefineIfMissing('COMPONENT_WLR_BUS_ANY_12', 1335);
configDefineIfMissing('COMPONENT_WLR_BUS_500_12', 1336);
configDefineIfMissing('COMPONENT_WLR_BUS_1000_12', 1337 );
configDefineIfMissing('COMPONENT_WLR_BUS_PAYG_12', 1338);

configDefineIfMissing('COMPONENT_WLR_BUS_ANY_24', 1339 );
configDefineIfMissing('COMPONENT_WLR_BUS_500_24', 1340);
configDefineIfMissing('COMPONENT_WLR_BUS_1000_24', 1341 );
configDefineIfMissing('COMPONENT_WLR_BUS_PAYG_24', 1342);

// new PlusNet WLR products (RPR 2009)

configDefineIfMissing('COMPONENT_WLR_PN_TALK_ANYTIME',         669); // now known as "Talk Anytime Intl 300"; grandfathered.
configDefineIfMissing('COMPONENT_WLR_PN_TALK_EVENING_WEEKEND', 670);


// Business Product Refresh 2009 additions

configDefineIfMissing('COMPONENT_WLR_PN_BUSINESS_PHONE_PAYG',    735);
configDefineIfMissing('COMPONENT_WLR_PN_BUSINESS_PHONE_DAYTIME', 736);


// New "new" Plusnet Talk Anytime product, to supercede 669.
// Created as part of VAT2010/Home2Mobile projects.

configDefineIfMissing('COMPONENT_WLR_PN_RES_TALK_ANYTIME_2010',     919);


// Inclusive UK mobile calls trial products

configDefineIfMissing('COMPONENT_WLR_PN_RES_MOBILE_ANYTIME_TRIAL1', 920);
configDefineIfMissing('COMPONENT_WLR_PN_RES_MOBILE_ANYTIME_TRIAL2', 921);


// John Lewis WLR products, introduced Aug-Oct 2011
configDefineIfMissing('COMPONENT_WLR_JOHNLEWIS_RES_EW',           995);
configDefineIfMissing('COMPONENT_WLR_JOHNLEWIS_RES_ANYTIME_INTL', 996);
configDefineIfMissing('COMPONENT_WLR_JOHNLEWIS_RES_ANYTIME',      997);
// Adding the 2 new Mobile call Plans
configDefineIfMissing('COMPONENT_WLR_JOHNLEWIS_RES_ANYTIME_INTL_W_MOBILE', 1979);
configDefineIfMissing('COMPONENT_WLR_JOHNLEWIS_RES_ANYTIME_W_MOBILE',      1980);

// New WLR products added as part of Project Penguin (Wlr pricing update 2013)
configDefineIfMissing('COMPONENT_WLR_PN_RES_ANYTIME_INTERNATIONAL_2013', 1345);
configDefineIfMissing('COMPONENT_WLR_PN_RES_ANYTIME_INTERNATIONAL_WITH_MOBILE_2013', 1346);
configDefineIfMissing('COMPONENT_WLR_PN_RES_ANYTIME_2013', 1347);
configDefineIfMissing('COMPONENT_WLR_PN_RES_ANYTIME_WITH_MOBILE_2013', 1348);
configDefineIfMissing('COMPONENT_WLR_PN_RES_EVENINGS_AND_WEEKENDS_FREE_2013', 1349);
configDefineIfMissing('COMPONENT_WLR_PN_RES_EVENINGS_AND_WEEKENDS_FREE_WITH_MOBILE_2013', 1350);
configDefineIfMissing('COMPONENT_WLR_PN_RES_EVENINGS_AND_WEEKENDS_2013', 1351);
configDefineIfMissing('COMPONENT_WLR_PN_RES_EVENINGS_AND_WEEKENDS_WITH_MOBILE_2013', 1352);
configDefineIfMissing('COMPONENT_WLR_PN_RES_TALK_MOBILE_2013', 1353);
configDefineIfMissing('COMPONENT_WLR_PN_RES_NO_CALLS_2013', 1354);
configDefineIfMissing('COMPONENT_WLR_PN_RES_NO_CALLS_WITH_MOBILE_2013', 1355);
configDefineIfMissing('COMPONENT_WLR_PN_RES_WEEKENDS_2013', 1356);
configDefineIfMissing('COMPONENT_WLR_PN_RES_WEEKENDS_WITH_MOBILE_2013', 1357);


// 2014 Pricing Refresh
configDefineIfMissing('COMPONENT_WLR_PN_RES_ANYTIME_INTERNATIONAL_2014', 1638);
configDefineIfMissing('COMPONENT_WLR_PN_RES_ANYTIME_INTERNATIONAL_WITH_MOBILE_2014', 1639);
configDefineIfMissing('COMPONENT_WLR_PN_RES_ANYTIME_2014', 1640);
configDefineIfMissing('COMPONENT_WLR_PN_RES_ANYTIME_WITH_MOBILE_2014', 1641);
configDefineIfMissing('COMPONENT_WLR_PN_RES_EVENINGS_AND_WEEKENDS_FREE_2014', 1642);
configDefineIfMissing('COMPONENT_WLR_PN_RES_EVENINGS_AND_WEEKENDS_FREE_WITH_MOBILE_2014', 1643);
configDefineIfMissing('COMPONENT_WLR_PN_RES_EVENINGS_AND_WEEKENDS_2014', 1644);
configDefineIfMissing('COMPONENT_WLR_PN_RES_EVENINGS_AND_WEEKENDS_WITH_MOBILE_2014', 1645);
configDefineIfMissing('COMPONENT_WLR_PN_RES_TALK_MOBILE_2014', 1646);
configDefineIfMissing('COMPONENT_WLR_PN_RES_WEEKENDS_2014', 1647);
configDefineIfMissing('COMPONENT_WLR_PN_RES_WEEKENDS_WITH_MOBILE_2014', 1648);
configDefineIfMissing('COMPONENT_WLR_PN_RES_LINE_ONLY_2014', 1651);
configDefineIfMissing('COMPONENT_WLR_PN_RES_LINE_ONLY_WITH_MOBILE_2014', 1652);
configDefineIfMissing('COMPONENT_WLR_PN_RES_EVENING_AND_WEEKEND_UK_AND_MOBILE_CALLS', 2057);
configDefineIfMissing('COMPONENT_WLR_PN_RES_UNLIMITED_UK_AND_MOBILE_CALLS', 2058);
configDefineIfMissing('COMPONENT_WLR_PN_BUS_UNLIMITED_UK_CALLS', 2405);
configDefineIfMissing('COMPONENT_WLR_PN_BUS_UNLIMITED_UK_AND_INTERENATIONAL_500_CALLS', 2406);
configDefineIfMissing('COMPONENT_WLR_PN_BUS_PAYG', 2418);

// 10Gb included usage, HP, 5.50 offers
configDefineIfMissing('COMPONENT_BBSOLUS_OFFER', 642);
configDefineIfMissing('COMPONENT_BBHP_OFFER', 643);
configDefineIfMissing('COMPONENT_10GBBB_OFFER', 644);
configDefineIfMissing('COMPONENT_10GBBBHP_OFFER', 645);

$component_dialup_types = array (
    COMPONENT_PLUS_DIALUP_0845_RESIDENTIAL => '1',
    COMPONENT_PLUS_DIALUP_0845_BIZ         => '1',
    COMPONENT_PLUS_DIALUP_SURFTIME_LITE    => '1',
    COMPONENT_PLUS_DIALUP_SURFTIME_OFFPEAK => '1',
    COMPONENT_PLUS_DIALUP_SURFTIME_24      => '1',
    COMPONENT_PLUS_DIALUP_SURFTIME_BIZ     => '1',
    COMPONENT_PLUS_DIALUP_ADSL_500         => '1',
    COMPONENT_PLUS_DIALUP_ADSL_S500        => '1',
    COMPONENT_PLUS_DIALUP_ADSL_S1000       => '1',
    COMPONENT_PLUS_DIALUP_ADSL_S2000       => '1',
    COMPONENT_F9_DIALUP_0845_RESIDENTIAL   => '1',
    COMPONENT_F9_DIALUP_0845_BUSINESS      => '1',
    COMPONENT_F9_DIALUP_0820_BUSINESS      => '1',
    COMPONENT_F9_DIALUP_0800_RESIDENTIAL   => '1',
    COMPONENT_F9_DIALUP_0800_BUSINESS      => '1',
    COMPONENT_FOL_DIALUP_0845              => '1',
    COMPONENT_DABSOL_DIALUP_0845           => '1',
    COMPONENT_SEARCHPRO_DIALUP_0845        => '1',
);

$component_simple_dialup_types = array (
    COMPONENT_PLUS_DIALUP_0845_RESIDENTIAL,
    COMPONENT_PLUS_DIALUP_0845_BIZ,
    COMPONENT_F9_DIALUP_0845_RESIDENTIAL,
    COMPONENT_F9_DIALUP_0845_BUSINESS,
    COMPONENT_FOL_DIALUP_0845,
    COMPONENT_DABSOL_DIALUP_0845
);

$component_ecommerce_types = array (
    COMPONENT_PLUS_ECOMMERCE_BASIC    => '1',
    COMPONENT_PLUS_ECOMMERCE_ADVANCED => '1',
    COMPONENT_PLUS_ECOMMERCE_SUPER    => '1'
);

configDefineIfMissing('PRODUCT_COMPONENT_UNCONFIGURED', 1);
configDefineIfMissing('PRODUCT_COMPONENT_QUEUED_ACTIVATE', 2);
configDefineIfMissing('PRODUCT_COMPONENT_QUEUED_REACTIVATE', 3);
configDefineIfMissing('PRODUCT_COMPONENT_ACTIVE', 4);
configDefineIfMissing('PRODUCT_COMPONENT_QUEUED_DEACTIVATE', 5);
configDefineIfMissing('PRODUCT_COMPONENT_QUEUED_DECONFIGURE', 6);
configDefineIfMissing('PRODUCT_COMPONENT_DEACTIVE', 7);
configDefineIfMissing('PRODUCT_COMPONENT_QUEUED_DESTROY', 8);
configDefineIfMissing('PRODUCT_COMPONENT_DESTROYED', 9);

// Waitrose
configDefineIfMissing('COMPONENT_WAITROSE_0845', 561);
configDefineIfMissing('COMPONENT_WAITROSE_PORTAL_LOGIN', 570);
configDefineIfMissing('COMPONENT_WAITROSE_WEBMAIL', 574);

// Greenbee
configDefineIfMissing('COMPONENT_GREENBEE_PORTAL_LOGIN', 571);
configDefineIfMissing('COMPONENT_GREENBEE_WEBMAIL', 575);

// John Lewis
configDefineIfMissing('COMPONENT_JOHNLEWIS_EMAIL', 992);
configDefineIfMissing('COMPONENT_JOHNLEWIS_PORTAL_LOGIN', 993);
configDefineIfMissing('COMPONENT_JOHNLEWIS_DYNAMIC_IP', 994);
configDefineIfMissing('COMPONENT_JOHNLEWIS_STATIC_IP', 1012); // This is the legacy component - Static IP add-on is 1017
configDefineIfMissing('COMPONENT_JOHNLEWIS_STATIC_IP_ADDON', 1017);

// Broadband scope
configDefineIfMissing('COMPONENT_BBS_EU_SUPPORT', 711);

// Enhanced Care
configDefineIfMissing('COMPONENT_ENHANCEDCARE', 559);

// BroadbandScope 8 static ip
configDefineIfMissing('COMPONENT_BROADBANDSCOPE_8_STATIC_IP', 690);
configDefineIfMissing('COMPONENT_MAX_PREMIUM_BUSINESS_INCLUSIVE', 960);

// Vodafone components
configDefineIfMissing('COMPONENT_VODAFONE_PORTAL_LOGIN', 1158);
