<?php

	// Spam Protection Access library
	//
	// This library requires:
	//      groupware-access.inc
	//      userdata-access.inc
	//      component-defines.inc
	//      config-email-access.inc



	///////////////////////////////////////////////
	// Function   spam_protection_validate_setup
	// Purpose    Ensures that there are sufficient config_sets for this bundle
	///////////////////////////////////////////////
	function spam_protection_validate_setup($group_member_bundle_id)
	{
		$config_sets     = groupware_config_sets_get($group_member_bundle_id, 'spam');
		$member_bundle   = groupware_group_member_bundle_get($group_member_bundle_id);
		$group_member    = groupware_group_member_get($member_bundle['group_member_id']);

		$member_domains  = spam_protection_get_domains($group_member['service_id']);

		$spam_element    = groupware_group_member_bundle_elements_get('spam');

		$intConfigSetsNo    = count($config_sets);
		$intMemberDomainsNo = count($member_domains);
		
		// If this condition isn't met, we need to create the missing config_sets
		if($intConfigSetsNo < $intMemberDomainsNo)
		{
			// Set a flag for all created entries
			foreach($config_sets as $config)
			{
				$config_domain[$config['spam_domain']] = $config;
			}

			foreach($member_domains as $row)
			{
				if(!isset($config_domain[$row['domainname']]))
				{
					$settings = array('spam_domain'   => $row['domainname'],
					                  'spam_security' => '0',
					                  'spam_status'   => '1');

					groupware_config_sets_create($group_member_bundle_id, $spam_element['bundle_type_element_id'], $settings);
				}
			}
		}
		elseif(($intConfigSetsNo > $intMemberDomainsNo))
		{
			//we need to remove non existing domain spam components
			
			foreach($member_domains as $strDomainDetails)
			{
				$arrExistingDomains[$strDomainDetails['domainname']] = 1;
			}

			$dbhConnection = get_named_connection_with_db('groupware');
		
			foreach($config_sets as $group_member_config_set_id => $arrSpamComponentDetails)
			{
				if(!$arrExistingDomains[$arrSpamComponentDetails['spam_domain']])
				{
					$strQuery = "UPDATE group_member_config_set_values SET when_removed=now() WHERE group_member_config_set_id=".$group_member_config_set_id."";
					PrimitivesQueryOrExit($strQuery, $dbhConnection);
				}
				else
				{
					//domain shouldn't have more then one spam component
					$arrExistingDomains[$arrSpamComponentDetails['spam_domain']] = 0;
				}
			}
		}
	}



	/////////////////////////////////////////////////////////////
	// Function:  spam_protection_update_level
	// Purpose:   Set the security level for a specific config set
	// Arguments: $config_set_id
	//            $group_member_id
	//            $spam_level
	// Returns :  nothing
	/////////////////////////////////////////////////////////////
	function spam_protection_update_level($config_set_id, $group_member_bundle_id, $spam_level)
	{
		$field = groupware_bundle_type_get_element_field_by_tag('spam_security');
		groupware_config_sets_set_value($config_set_id, $field['bundle_type_element_field_id'], $spam_level);

		$arrBundleDetails = groupware_group_member_bundle_get($group_member_bundle_id);
		$arrGroupMemberDetails = groupware_group_member_get($arrBundleDetails['group_member_id']);

		// refresh all active email components
		// - don't need to do queued-activate or queued-reactivate because any changes will be picked up when they are done anyway
		if(isset($arrGroupMemberDetails['service_id']))
		{
			$arrComponents = userdata_component_find(array('service_id' => $arrGroupMemberDetails['service_id'],
			                                               'status'     => array('active'),
			                                               'type'       => array(COMPONENT_PLUS_EMAIL, COMPONENT_F9_EMAIL, COMPONENT_FOL_EMAIL)));

			foreach(array_keys($arrComponents) as $intKey)
			{
				config_email_configurator($arrComponents[$intKey]['component_id'], 'auto_refresh');
			}
		}
	}



	/////////////////////////////////////////////////////////////
	// Function:  spam_protection_update_status
	// Purpose:   Set the spam status for a specific config set
	// Arguments: $config_set_id
	//            $group_member_id
	//            $spam_status
	// Returns :  nothing
	/////////////////////////////////////////////////////////////
	function spam_protection_update_status($config_set_id, $group_member_bundle_id, $spam_status)
	{
		$field = groupware_bundle_type_get_element_field_by_tag('spam_status');
		groupware_config_sets_set_value($config_set_id, $field['bundle_type_element_field_id'], $spam_status);
	}



	/////////////////////////////////////////////////////////////
	// Function:  spam_protection_get_domains
	// Purpose:   Get domains associated with this service_id that have pop3 handled by PlusNet
	// Arguments: $service_id
	// Returns :  array of domains for this service_id, that have pop3 handled by PlusNet
	/////////////////////////////////////////////////////////////
	function spam_protection_get_domains($service_id)
	{
		$array_return = array();

		$connection = get_named_connection('domains');

		$query = "SELECT domainname ".
		         "  FROM newdomains ".
		         " WHERE service_id  = '$service_id' ".
		         "   AND mailservice = 'pop3'";

		$result = mysql_query($query, $connection)
			  or report_error(__FILE__, __LINE__, mysql_error($connection));

		$service        = userdata_service_get($service_id);
		$isp            = product_visp_config_get($service['isp']);
		$master_domain  = $service['username'] . '.' . substr ($isp['default_customer_homepages_domain'],13);

		$array_return[] = array('domainname' => $master_domain);

		while ($row = mysql_fetch_assoc($result))
		{
			$array_return[] = $row;

		}
		return $array_return;
	}



	function spam_protection_array_levels()
	{
		$connection = get_named_connection('groupware');

		$query = "SELECT filter_level, filter_name FROM spam_filter_levels";
		$result = mysql_query($query, $connection)
			  or report_error(__FILE__, __LINE__, mysql_error($connection));

		while ($row = mysql_fetch_assoc($result))
		{
			$array_return[$row['filter_level']] = $row['filter_name'];

		}
		return $array_return;
	}



	function spam_protection_array_statuses()
	{
		$connection = get_named_connection('groupware');

		$query = "SELECT filter_status, status_name FROM spam_filter_statuses";
		$result = mysql_query($query, $connection)
			  or report_error(__FILE__, __LINE__, mysql_error($connection));

		while ($row = mysql_fetch_assoc($result))
		{
			$array_return[$row['filter_status']] = $row['status_name'];

		}
		return $array_return;
	}



	/////////////////////////////////////////
	// Function  spam_protection_get_need_updating
	// purpose   returns associative arrays for all spam_protection that need updating
	
	function spam_protection_get_all_settings()
	{
		$connection = get_named_connection('groupware_reporting');

		// Firstly, get a list of the
		$query = "SELECT btef.field_tag, btef.field_name, gmcsv.config_value, gmcs.group_member_config_set_id ".
		         "  FROM bundle_type_element_fields AS btef, ".
		         "       group_member_config_set_values AS gmcsv, ".
		         "       group_member_config_sets AS gmcs ".
		         " WHERE gmcsv.bundle_type_element_field_id = btef.bundle_type_element_field_id ".
		         "   AND gmcsv.group_member_config_set_id = gmcs.group_member_config_set_id ";

		$result = mysql_query($query, $connection)
			  or report_error(__FILE__, __LINE__, mysql_error($connection));

		$array_config_sets = array();

		while($row = mysql_fetch_assoc($result))
		{
			$array_config_sets[$row['group_member_config_set_id']][$row['field_tag']] = $row['config_value'];
		}

		return $array_config_sets;
	}

	function spam_protection_get_removed_settings()
	{
		$connection = get_named_connection('groupware_reporting');

		$query = "SELECT DISTINCT(group_member_config_set_id) ".
		         "  FROM group_member_config_set_values ".
		         " WHERE when_removed IS NOT NULL";

		$result = mysql_query($query, $connection)
			  or report_error(__FILE__, __LINE__, mysql_error($connection));

		$array_config_sets = array();

		while($row = mysql_fetch_assoc($result))
		{
			$array_config_sets[$row['group_member_config_set_id']] = $row['group_member_config_set_id'];
		}

		return $array_config_sets;
	}

	/////////////////////////////////////////////////////////////
	// Function:  SpamProtectionVerifyStatus
	// Purpose:   Check and verify spam settings before the setting file is created
	// Arguments: none
	// Returns :  nothing
	/////////////////////////////////////////////////////////////
	
	function SpamProtectionVerifyStatus()
	{
		$dbhConnection = get_named_connection_with_db('groupware_reporting');

		// Get all settings which are marked as active, but should be deactive

		$arrResults = array();

		$strSQL = 'SELECT gmcsv.group_member_config_set_value_id AS GroupMemberConfigSetValueID '.
		            'FROM group_member_config_set_values AS gmcsv, '.
		                ' group_member_config_sets AS gmcs, '.
		                ' group_member_bundles AS gmb, '.
		                ' group_bundles AS gb, '.
		                ' groups AS g, '.
		                ' userdata.components AS uc '.
		           'WHERE gmcs.group_member_config_set_id = gmcsv.group_member_config_set_id '.
		             'AND gmcs.group_member_bundle_id = gmb.group_member_bundle_id '.
		             'AND gmb.group_bundle_id = gb.group_bundle_id '.
		             'AND gb.group_id = g.group_id '.
		             'AND uc.component_id = g.component_id '.
		             "AND (gb.licenses = 0 OR uc.status != 'active') ".
		             'AND gmcsv.when_removed IS NULL';

		$resResult = PrimitivesQueryOrExit($strSQL, $dbhConnection);

		$arrResults = PrimitivesResultsAsArrayGet($resResult);

		$dbhConnection = get_named_connection_with_db('groupware');

		foreach ($arrResults as $arrGroupConfigID)
		{
			$intGroupConfigID = $arrGroupConfigID['GroupMemberConfigSetValueID'];

			$strSQL = 'UPDATE group_member_config_set_values '.
			             'SET when_removed = NOW() '.
			           "WHERE group_member_config_set_value_id = '$intGroupConfigID'";

			PrimitivesQueryOrExit($strSQL, $dbhConnection);
		}

		$arrResults = array();

		// Check for rogue settings which do not have the correct configuration

		$strSQL = 'SELECT gmcsv.group_member_config_set_value_id AS GroupMemberConfigSetValueID '.
		            'FROM group_member_config_set_values AS gmcsv, '.
		                ' group_member_config_sets AS gmcs, '.
		                ' group_member_bundles AS gmb, '.
		                ' group_bundles AS gb, '.
		                ' groups AS g, '.
		                ' userdata.components AS uc '.
		           'WHERE gmcs.group_member_config_set_id = gmcsv.group_member_config_set_id '.
		             'AND gmcs.group_member_bundle_id = gmb.group_member_bundle_id '.
		             'AND gmb.group_bundle_id = gb.group_bundle_id '.
		             'AND gb.group_id = g.group_id '.
		             'AND uc.component_id = g.component_id '.
		             'AND gmcsv.when_removed IS NULL';

		$resResult = PrimitivesQueryOrExit($strSQL, $dbhConnection);

		$arrResults = PrimitivesResultsAsArrayGet($resResult);

		$intCorrect = count($arrResults);

		$arrCorrect = $arrResults;

		$arrResults = array();

		// Get all records which are active
		$strSQL = 'SELECT gmcsv.group_member_config_set_value_id AS GroupMemberConfigSetValueID '.
		            'FROM group_member_config_set_values AS gmcsv '.
		           'WHERE gmcsv.when_removed IS NULL';

		$resResult = PrimitivesQueryOrExit($strSQL, $dbhConnection);

		$arrResults = PrimitivesResultsAsArrayGet($resResult);

		$intTotal = count($arrResults);

		echo "Total of $intTotal active spam settings. Some of these may be wrong\n";

		if($intTotal != $intCorrect)
		{
			// Data in the table is not correct somewhere.
			// This needs fixing.

			echo "Found " . ($intTotal - $intCorrect) . " records which are incorrect. Fixing\n";

			foreach($arrCorrect as $arrItem)
			{
				$intNumber = $arrItem['GroupMemberConfigSetValueID'];
				$arrCorrectItems[$intNumber] = $arrItem['GroupMemberConfigSetValueID'];
			}

			echo "Created array of correct items\n";

			unset($arrCorrect);

			$intCount = 0;

			foreach($arrResults as $arrItem)
			{
				$intItemID = $arrItem['GroupMemberConfigSetValueID'];

				if(in_array($intItemID, $arrCorrectItems) === false)
				{
					echo "$intItemID is orphan data. Closing\n";

					$strSQL = 'UPDATE group_member_config_set_values '.
					             'SET when_removed = NOW() '.
					           "WHERE group_member_config_set_value_id = '$intItemID'";

					PrimitivesQueryOrExit($strSQL, $dbhConnection);

					unset($arrCorrectItems[$intItemID]);
				}
				else
				{
					unset($arrCorrectItems[$intItemID]);
				}

				$intCount++;
				
				if($intCount % 1000 == 0)
				{
					echo "Processed $intCount accounts\n";
				}
			}
		}

		return true;
	}
?>
