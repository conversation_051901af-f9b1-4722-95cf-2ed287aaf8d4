<?php
/**
 * TV
 *
 * Class used to store all the relevant information on a customer's TV service.
 *
 * @package TV
 * <AUTHOR> <<EMAIL>>
 *
 */

/**
 * TV
 *
 * Class used to store all the relevant information on a customer's TV service.
 *
 * @package TV
 * <AUTHOR> <<EMAIL>>
 *
 */
class TV
{
    /**
     * TV component ID (userdata.components.component_id)
     * @var integer
     */
    protected $intTVComponentId;

    /**
     * User service ID
     * @var integer
     */
    protected $serviceId;

    /**
     * TV product name and cost
     * @var array
     */
    protected $arrTVProduct = array();

    /**
     * TV set top box name
     * @var string
     */
    protected $strTVHardware;

    /**
     * Info on the channel packs the customer is currently subscribed to
     * @var array
     */
    protected $arrChannelPacks = array();

    /**
     * Whether or not the customer has taken powerline adapters
     * @var boolean
     */
    protected $bolHasPowerline;

    /**
     * Whether or not the customer's activation fee was waived
     * @var boolean
     */
    protected $bolActivationWaived;

    /**
     * Profit foregone contract length in months
     * @var integer
     */
    protected $intContractedServiceLength;

    /**
     * Profit foregone contract start date
     * @var string
     */
    protected $strContractedServiceStart;

    /**
     * Profit foregone contract start date
     * @var string
     */
    protected $strContractedServiceEnd;

    /**
     * Whether or not the customer has a profit foregone contract for TV
     * @var boolean
     */
    protected $bolInContract;

    /**
     * Info on the hardware orders a customer has had (tracking number, date).
     * @var array
     */
    protected $arrHardwareOrders;

    /**
     * Get TV Component Id
     *
     * @return integer
     */
    public function getTVComponentId()
    {
        return $this->intTVComponentId;
    }

    /**
     * Get TV Product
     *
     * @return array
     */
    public function getTVProduct()
    {
        return $this->arrTVProduct;
    }

    /**
     * Get TV Hardware
     *
     * @return string
     */
    public function getTVHardware()
    {
        return $this->strTVHardware;
    }

    /**
     * Get Channel packs
     *
     * @return array
     */
    public function getChannelPacks()
    {
        return $this->arrChannelPacks;
    }

    /**
     * Get Hardware Orders
     *
     * @return array
     */
    public function getHardwareOrders()
    {
        return $this->arrHardwareOrders;
    }

    /**
     * Has Power Line Adapter
     *
     * @return boolean
     */
    public function hasPowerline()
    {
        return $this->bolHasPowerline;
    }

    /**
     * Has waived activation
     *
     * @return boolean
     */
    public function hasWaivedActivation()
    {
        return $this->bolActivationWaived;
    }

    /**
     * Has Contract?
     *
     * @return boolean
     */
    public function hasContract()
    {
        return $this->bolInContract;
    }

    /**
     * Get Contracted Service Length
     *
     * @return integer
     */
    public function getContractedServiceLength()
    {
        return $this->intContractedServiceLength;
    }

    /**
     * Get Contracted Service Start Date
     *
     * @return string
     */
    public function getContractedServiceStart()
    {
        return $this->strContractedServiceStart;
    }

    /**
     * Contracted Service End Date
     *
     * @return string
     */
    public function getContractedServiceEnd()
    {
        return $this->strContractedServiceEnd;
    }

    /**
     * Returns the component ID of an TV Component ID which isn't un-configured
     *
     * @param integer $intServiceId customer service ID
     *
     * @return integer component ID
     */
    public static function findCurrentComponentId($intServiceId)
    {
        $arrTVStates = array(
            'queued-activate',
            'queued-reactivate',
            'active',
            'queued-deactivate',
            'queued-deconfigure',
            'deactive'
        );
        $intTVComponentId = CProduct::getComponentIDByServiceID($intServiceId, 'YOUVIEW_TV', $arrTVStates);
        return $intTVComponentId;
    }

    /**
     * Retrieves the TV Connection Product by Service ID
     *
     * @param integer $intServiceId
     * @param string  $strStatus    (Optional) Status of the component
     *
     * @return CTvComponent
     */
    public static function getTVProductFromServiceId($intServiceId, $strStatus=null)
    {
        $objTVProduct = CProduct::getProductByProductTypeHandle($intServiceId, 'YOUVIEW_TV', $strStatus);
        return $objTVProduct;
    }

    /**
     * Returns the component ID of an active TV component
     *
     * @param integer $intServiceId         customer service ID
     * @param boolean $bolAllowQueuedActive include queued-active component
     *
     * @return integer
     */
    public static function findActiveComponentId($intServiceId, $bolAllowQueuedActive = false)
    {
        $arrTVStates = array('queued-reactivate', 'active');

        if ($bolAllowQueuedActive) {
            $arrTVStates[] = 'queued-activate';
        }

        $intTVComponentIdActive = CProduct::getComponentIDByServiceID($intServiceId, 'YOUVIEW_TV', $arrTVStates);
        return $intTVComponentIdActive;
    }

    /**
     * Returns true if the customer has added TV within the last 14 days
     *
     * @param integer $intTVComponentId TV component ID
     *
     * @return boolean
     */
    public static function isCustomerWithin14Days($intTVComponentId)
    {
        $db = get_named_connection_with_db('userdata');

        $strQuery = "SELECT creationdate from userdata.components
                     WHERE component_id = $intTVComponentId";

        $result = PrimitivesQueryOrExit($strQuery, $db);
        $tvAddDate = strtotime(PrimitivesResultGet($result, 'creationdate'));

        if (time() <= strtotime("+ 15 days", $tvAddDate)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Returns true if the customer has ordered Powerline Adapters within the last 14 days
     *
     * @param integer $intTVComponentId TV component ID
     *
     * @return boolean
     */
    public function powerlinesWithin14Days($intTVComponentId)
    {
        if ($this->hasPowerline()) {
            foreach ($this->getHardwareOrders() as $order) {

                if (strrpos($order['display_name'], 'Powerline Adapter')) {

                    if (strtotime($order['dtmStart']) >= strtotime("- 14 days")) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * Returns true if the customer has an active TV service with no scheduled cease
     *
     * @param integer $intServiceId customer service ID
     *
     * @return boolean
     */
    public function canRemoveTV()
    {
        $componentId = self::findCurrentComponentId($this->serviceId);
        if ($componentId) {
            $pciId = getPciId($componentId, 1);
            if ($pciId) {
                $subscription = CProductComponent::createInstance($pciId);
                if ($subscription->getStatus() == 'QUEUED_DESTROY') {
                    return false;
                } else {
                    return true;
                }
            }
        }
        return false;
    }


    /**
     * Return the current status of the TV subscription component
     * or false if there's no TV component
     *
     * @param int $serviceId Service id
     *
     * @return string
     **/
    public function getSubscriptionStatus()
    {
        $componentId = self::findCurrentComponentId($this->serviceId);
        if ($componentId) {
            $pciId = getPciId($componentId, 1);
            if ($pciId) {
                $subscription = CProductComponent::createInstance($pciId);
                return $subscription->getStatus();
            }
        }
        return false;
    }


    /**
     * Returns true if the users account is not queued-activate
     * group.
     *
     * @param int $serviceId customer service ID
     *
     * @return bool
     **/
    public function canAddTV()
    {
        $service = userdata_service_get($this->serviceId);
        if ($service['status'] != 'queued-activate' ) {
            return true;
        }

        return false;
    }

    /**
     * Returns the name if the customer has hardware of a specified type
     *
     * @param integer $intServiceId customer service ID
     * @param string  $type         should be 'tv_box' or 'network_adapter'
     *
     * @return string
     */
    public static function findHardware($intServiceId, $type)
    {
        $db = get_named_connection_with_db('userdata');

        $strQuery = "SELECT  " .
            "    hsp.display_name " .
            "FROM  " .
            "    products.hardware_supplier_products hsp " .
            "LEFT JOIN products.hardware_product_classes hpc " .
            "    ON hpc.hardware_product_class_id = hsp.hardware_product_class_id " .
            "INNER JOIN products.hardware_bundle_config_items hbci " .
            "    ON hbci.hardware_supplier_product_id = hsp.hardware_supplier_product_id " .
            "INNER JOIN products.component_hardware_bundle_config chbc " .
            "    ON chbc.component_hardware_bundle_config_id = hbci.component_hardware_bundle_config_id " .
            "INNER JOIN products.service_components sc  " .
            "    ON sc.service_component_id = chbc.service_component_id " .
            "INNER JOIN userdata.components c  " .
            "    ON c.component_type_id = sc.service_component_id " .
            "WHERE " .
            "    c.service_id = $intServiceId " .
            "    AND hpc.tag = '$type' " .
            "    AND c.status = 'queued-activate' " .
            "ORDER BY c.creationdate DESC LIMIT 1; ";

        $result = PrimitivesQueryOrExit($strQuery, $db);
        $hardware = PrimitivesResultGet($result, 'display_name');

        if ($hardware == false) { // Check if hardware has been added as part of a bundle
            $strQuery = "SELECT  " .
                "    hsp.display_name " .
                "FROM  " .
                "    products.hardware_supplier_products hsp " .
                "LEFT JOIN products.hardware_product_classes hpc " .
                "    ON hpc.hardware_product_class_id = hsp.hardware_product_class_id " .
                "INNER JOIN userdata.hardware_bundle_items hbi " .
                "    ON hbi.hardware_supplier_product_id = hsp.hardware_supplier_product_id " .
                "INNER JOIN userdata.config_hardware_bundles chb " .
                "    ON chb.config_hardware_bundle_id = hbi.config_hardware_bundle_id " .
                "INNER JOIN userdata.components c " .
                "    ON c.component_id = chb.component_id " .
                "WHERE " .
                "    c.service_id = $intServiceId " .
                "    AND hpc.tag = '$type' " .
                "    AND c.status = 'queued-activate' " .
                "ORDER BY c.creationdate DESC LIMIT 1; ";

            $result = PrimitivesQueryOrExit($strQuery, $db);
            return PrimitivesResultGet($result, 'display_name');

        } else {

            return $hardware;
        }
    }

    /**
     * Returns info on the customer's TV hardware orders
     *
     * @param integer $intServiceId customer service ID
     *
     * @return array hardware order details
     */
    protected function findHardwareOrders($intServiceId)
    {
        $db = get_named_connection_with_db('userdata');

        $strQuery = "SELECT " .
            " DISTINCT(hbi.hardware_bundle_item_id), " .
            "    hsp.display_name, hwf.vchCarrierRef, ohbs.dtmStart " .
            "FROM  " .
            "    products.hardware_supplier_products hsp " .
            "INNER JOIN userdata.hardware_bundle_items hbi " .
            "    ON hbi.hardware_supplier_product_id = hsp.hardware_supplier_product_id " .
            "INNER JOIN userdata.config_hardware_bundles chb " .
            "    ON chb.config_hardware_bundle_id = hbi.config_hardware_bundle_id " .
            "LEFT JOIN dbCommonADSL.tblHWFeedbackRecord hwf " .
            "   ON hwf.intConfigHardwareBundleID = hbi.config_hardware_bundle_id " .
            "   AND hwf.vchItemID != '044642' " .
            "LEFT JOIN userdata.tblOverallHardwBundleStatus ohbs " .
            "   ON ohbs.usiConfigHardwareBundleID = hbi.config_hardware_bundle_id " .
            "   AND ohbs.usiHardwareOrderStatusID = '10' " .
            "INNER JOIN userdata.components c " .
            "    ON c.component_id = chb.component_id " .
            "WHERE " .
            "    c.service_id = $intServiceId " .
            "    AND hsp.tag IN ('TV_BOX_G4', 'TV_BOX_Z4', 'POWERLINE_ADAPTER', 'YOUVIEW_RETURNS_BAG') " .
            "    AND c.status = 'queued-activate'; ";

        $result = PrimitivesQueryOrExit($strQuery, $db);
        $orders = PrimitivesResultsAsArrayGet($result);
        foreach ($orders as $key => $order) {
            if (!in_array($order['vchCarrierRef'], array('', 'S1ST CLASS', '1ST CLASS'))) {
                $order['tracking_number'] = $order['vchCarrierRef'];
                $orders[$key] = $order;
            }
        }
        return $orders;
    }

    /**
     * Returns true if the customer is on YouView+ their activation fee was waived during signup
     *
     * @param integer $intServiceId customer service ID
     *
     * @return boolean
     */
    protected function wasActivationWaived($intServiceId)
    {
        if ($this->arrTVProduct['component_type_id'] != 1835) {
            return false;
        }

        $db = get_named_connection_with_db('userdata');

        $strQuery = "SELECT " .
            "    sp.intScheduledPaymentID " .
            "FROM " .
            "    financial.tblScheduledPayment sp " .
            "INNER JOIN userdata.accounts ac " .
            "    ON ac.account_id = sp.intAccountID " .
            "INNER JOIN userdata.users u " .
            "    ON u.address_id = ac.address_id " .
            "INNER JOIN userdata.services s " .
            "    ON s.user_id = u.user_id " .
            "WHERE s.service_id = $intServiceId " .
            "AND sp.vchDescription = 'YouView+ Activation Fee';";

        $result = PrimitivesQueryOrExit($strQuery, $db);
        $intScheduledPaymentID = PrimitivesResultGet($result, 'intScheduledPaymentID');
        return ($intScheduledPaymentID == false) ? true : false;


    }


    /**
     * retrieves information on the customer's TV service and channel packs
     *
     * @param integer $intServiceId customer service ID
     *
     * @return void
     */
    protected function findProductInformation($intServiceId)
    {
        $arrChargeableComponents = $this->getChargeableComponents($intServiceId);
        foreach ($arrChargeableComponents as $productComponent) {

            if ($productComponent['vchComponentTypeHandle'] == 'YOUVIEW_TV') {
                if ($productComponent['vchHandle'] == 'SUBSCRIPTION') {

                    $this->arrTVProduct = $productComponent;
                    $this->arrTVProduct['nextInvoice'] = $this->getNextInvoiceDate($intServiceId);
                    $this->intTVComponentId = $productComponent['component_id'];

                    $pciId = $this->getPCI($productComponent, 1);
                    if ($pciId) {
                        $subscription = $this->createProductComponentInstance($pciId);
                        $this->arrTVProduct['status'] = $subscription->getStatus();
                    }

                    break;
                }
            }
        }

        $f2cClient = $this->getF2CClient();
        try {
            $channelPacks = $f2cClient->getChannelPacks(array('serviceId' => $intServiceId));
            $channelPacks = $channelPacks['channel_packs'];
        } catch (Exception $e) {
            var_dump($e->getMessage());
        }

        $allowedStatus = array('ACTIVE', 'QUEUED_DESTROY');
        foreach ($channelPacks as $pack) {
            if (in_array($pack['status'], $allowedStatus)) {
                $this->arrChannelPacks[] = $pack;
            }
        }

        if (isset($this->arrTVProduct['status']) && ($this->arrTVProduct['status'] == 'QUEUED_DESTROY')) {

            $this->arrTVProduct['scheduledCancellation'] = true;
            $this->arrTVProduct['cancellationDate'] = $this->getCancellationDate();

        } elseif ($this->arrTVProduct == array()) {

            $this->arrTVProduct['status'] = 'DESTROYED';
            $this->arrTVProduct['isDestroyed'] = true;
            $this->intTVComponentId = self::findCurrentComponentId($intServiceId);
            $this->arrTVProduct['cancellationDate'] = $this->getCancellationDate();
        }
    }

    /**
     * Get the customer's scheduled cease date
     *
     * @return string MySQL datetime
     */
    protected function getCancellationDate()
    {
        if (!is_numeric($this->intTVComponentId)) {
            return false;
        }


        $db = get_named_connection_with_db('systemEvents');

        $strQuery = "SELECT tse.dteDue FROM dbSystemEvents.tblScheduledEvent tse
                     INNER JOIN dbSystemEvents.tblCancellation tc
                     ON tc.intScheduledEventID = tse.intScheduledEventID
                     WHERE tc.intComponentID = {$this->intTVComponentId}";

        $result = PrimitivesQueryOrExit($strQuery, $db);

        return PrimitivesResultGet($result, 'dteDue');
    }

    /**
     * retrieves information on the customer's contract
     *
     * @param integer $intServiceId customer service ID
     *
     * @return void
     */
    protected function findProfitForegoneContract($intServiceId)
    {

        $account = userdata_account_get_by_service($intServiceId);
        $account_id = $account['account_id'];

        $contractClient = BusTier_BusTier::getClient('contracts')
            ->setAccount($account_id)
            ->setRaiseServiceNotice(false);

        $contracts = $contractClient->getContracts(
            array('serviceId' => $intServiceId)
        );

        $this->bolInContract = false;

        foreach ($contracts as $contract) {
            if ($contract->getCancellationAlgorithm() == 'PROFIT_FOREGONE') {

                $contractedServices = $contract->getContractedServices();
                foreach ($contractedServices as $contractedService) {

                    $contractedServiceDefinition = $contractedService->getDefinition();
                    if (strpos($contractedServiceDefinition->getSubjectHandle(), 'YouView') !== false) {

                        $duration = $contract->getDefinition()->getDuration();
                        $this->intContractedServiceLength = $duration['value'];
                        $this->strContractedServiceStart = $contract->getStartDate();
                        $this->strContractedServiceEnd = $contract->getEndDate();

                        if ($contractedService->getStatus() == 'ACTIVE') {
                            $this->bolInContract = true;
                        }
                    }
                }
            }
        }
    }

    /**
     * Constructor
     *
     * @param integer $intServiceId service id
     */
    public function __construct($intServiceId)
    {
        $this->requireLegacyFiles();
        $this->serviceId = $intServiceId;
        $this->findProductInformation($intServiceId);
        try {
            $this->findProfitForegoneContract($intServiceId);
        } catch (Exception $exception) {
            error_log('Error finding contract: ' . $exception->getMessage());
        }
        $this->strTVHardware = $this->findHardware($intServiceId, 'tv_box');
        $this->bolActivationWaived = $this->wasActivationWaived($intServiceId);
        $this->arrHardwareOrders = $this->findHardwareOrders($intServiceId);

        if ($this->findHardware($intServiceId, 'network_adapter') !== false) {
            $this->bolHasPowerline = true;
        } else {
            $this->bolHasPowerline = false;
        }
    }

    /**
     * The function to get the cancellation reason from DB for new TV
     * @return array
     */
    public static function getCancellationReasons()
    {
        $db = get_named_connection_with_db('systemEvents');

        $strQuery = "SELECT intCancellationReasonID, vchDescription, bolHTT
                     FROM dbSystemEvents.tblCancellationReason
                     WHERE intCancellationReasonTypeID =
                     (select intCancellationReasonTypeID
                        from dbSystemEvents.tblCancellationReasonType
                        where vchHandle = 'TV_NEW')";

        $result = PrimitivesQueryOrExit($strQuery, $db);
        $reasons = PrimitivesResultsAsArrayGet($result);

        return $reasons;
    }

    /**
     * Function to get the tv product component id and component id for the
     * user's service id
     * @param serviceId $serviceId int
     * @return arrContracts $arrContracts array
     */
    public static function getTVProductsByServiceId($serviceId)
    {
        $arrContracts = array();
        $strQuery = sprintf("SELECT
                intProductComponentID,
                intProductComponentInstanceId,
                c.component_type_id,
                c.component_id,
                scpt.vchHandle
                FROM
                  userdata.components c
                INNER JOIN userdata.tblProductComponentInstance PC
                  ON c.component_id=PC.intComponentID
                INNER JOIN products.service_components sc
                  ON sc.service_component_id = c.component_type_id
                INNER JOIN products.tblServiceComponentProduct scp
                  ON scp.intServiceComponentId = sc.service_component_id
                INNER JOIN products.tblServiceComponentProductType scpt
                  ON scpt.intServiceComponentProductTypeID = scp.intServiceComponentProductTypeID
                AND c.service_id=%u
                AND c.status='active'
                AND scpt.vchHandle='YOUVIEW_TV'
                ORDER BY intProductComponentInstanceId", $serviceId);

        $dbhConnection = get_named_connection_with_db('userdata');

        $resResult = PrimitivesQueryOrExit(
            $strQuery,
            $dbhConnection,
            'Get Products By Service'
        );
        if (false !== $resResult) {
            $arrContracts = PrimitivesResultsAsArrayGet($resResult);
        }
        return $arrContracts;
    }

    /**
     * @param $intServiceId
     * @return array
     */
    protected function getChargeableComponents($intServiceId)
    {
        $arrChargeableComponents = CProductComponent::getChargeableComponentsFromServiceId($intServiceId);
        return $arrChargeableComponents;
    }

    /**
     * @return \Plusnet\F2CApiClient\ClientAdaptor
     */
    protected function getF2CClient()
    {
        $f2cClient = Plusnet\F2CApiClient\Client::factory();
        return $f2cClient;
    }

    protected function getNextInvoiceDate($serviceId)
    {
        return getNextInvoiceDate($serviceId);
    }

    /**
     * @param $pciId
     * @return bool|null|object
     */
    protected function createProductComponentInstance($pciId)
    {
        $subscription = CProductComponent::createInstance($pciId);
        return $subscription;
    }

    protected function requireLegacyFiles()
    {
        require_once '/local/www/database-admin/include/misc.inc';
        require_once '/local/data/mis/database/database_libraries/components/CProductComponent.inc';
        require_once '/local/www/database-admin/scripts/tv/functions.inc';

        // Use shared database connections (see standard_include.inc)
        if (!defined('DEFAULT_PRIVATE_DB_CONNECTIONS')) {
            define('DEFAULT_PRIVATE_DB_CONNECTIONS', false);
        }

    }

    protected function getPCI($productComponent, $intProductComponentId)
    {
        return getPciId($productComponent['component_id'], $intProductComponentId);
    }
}
