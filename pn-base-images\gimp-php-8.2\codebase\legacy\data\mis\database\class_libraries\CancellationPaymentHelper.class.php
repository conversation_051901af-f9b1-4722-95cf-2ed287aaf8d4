<?php
/**
 * Helper class for CancellationPayment
 *
 * @package LegacyCodebase
 * <AUTHOR> <n<PERSON><PERSON>@plus.net>
 * @link    link
 */

/**
 * Provides an interface to untestable legacy code.
 *
 * @package LegacyCodebase
 * <AUTHOR> <n<PERSON><PERSON>@plus.net>
 * @link    link
 */
class CancellationPaymentHelper
{
    /**
     * Load all the legacy files
     *
     * @return void
     */
    public function loadLegacyFiles()
    {
        require_once '/local/data/mis/database/standard_include.inc';
        require_once '/local/www/database-admin/config/config.inc';
        require_once DATABASE_LIBRARY_ROOT . 'financial-access.inc';
        require_once DATABASE_LIBRARY_ROOT . 'phplib-access.inc';
        require_once DATABASE_LIBRARY_ROOT . 'userdata-access.inc';
        require_once DATABASE_LIBRARY_ROOT . 'secure-transaction-access.inc';
        require_once DATABASE_LIBRARY_ROOT . 'tickets-access.inc';
        require_once DATABASE_LIBRARY_ROOT . 'direct-debit-access.inc';
        require_once '/local/data/mis/database/application_apis/EmailHandler/EmailHandler.class.php';
        require_once '/local/data/mis/database/database_libraries/components/CProduct.inc';
    }

    /**
     * Wrapper for financial_sales_invoice_get
     *
     * @param int $invoiceId id of the invoice you want details for
     *
     * @return array
     */
    public function getInvoice($invoiceId)
    {
        return financial_sales_invoice_get($invoiceId);
    }

    /**
     * Wrapper for userdata_account_get_by_service
     *
     * @param int $serviceId id of the service you want details for
     *
     * @return array
     */
    public function getUserdata($serviceId)
    {
        return userdata_account_get_by_service($serviceId);
    }

    /**
     * Wrapper for userdata_service_get
     *
     * @param int $serviceId id of the service you want details for
     *
     * @return array
     */
    public function getAccountDetails($serviceId)
    {
        return userdata_service_get($serviceId);
    }

    /**
     * Wrapper for Financial_DirectDebitDetails::getActiveInstructionByService
     *
     * @param int $serviceId the service id
     *
     * @return Financial_DirectDebitDetails
     */
    public function getDirectDebitDetails($serviceId)
    {
        return Financial_DirectDebitDetails::getActiveInstructionByService($serviceId);
    }


    /**
     * Wrapper for financial_valid_creditcard_get
     *
     * Returns the credit_details_id of a valid (not expired) credit card for a given account,
     * or false if there isn't one.
     *
     * @param int $accountId The account id.
     *
     * @return mixed credit_details_id of the valid card, or false if we haven't got one
     */
    public function getValidCardDetails($accountId)
    {
        return financial_valid_creditcard_get($accountId);
    }

    /**
     * Wrapper for  EmailHandler::sendEmail
     *
     * @param int    $serviceId the service id
     * @param string $template  the email template to use
     * @param array  $emailData any additional data
     *
     * @return void
     */
    public function sendEmail($serviceId, $template, $emailData)
    {
        EmailHandler::sendEmail($serviceId, $template, $emailData);
    }

    /**
     * Wrapper for CProduct::getServiceCustomerSectorHandle
     *
     * @param int $serviceId the service id
     *
     * @return string
     */
    public function getCustomerSector($serviceId)
    {
        return CProduct::getServiceCustomerSectorHandle($serviceId);
    }

    /**
     * Wrapper for phplibGetTeamIdByHandle
     *
     * @param string $handle the handle for the ticket pool
     *
     * @return int
     */
    public function getTicketPoolId($handle)
    {
        return phplibGetTeamIdByHandle($handle);
    }

    /**
     * Wrapper for tickets_ticket_add
     *
     * @param int    $serviceId the service id
     * @param string $body      the ticket body
     * @param int    $teamId    the ticket pool id
     * @param string $state     open or closed
     *
     * @return void
     */
    public function addTicket($serviceId, $body, $teamId, $state)
    {
        tickets_ticket_add('Script', $serviceId, 0, 0, $state, '', $body, 0, $teamId);
    }

    /**
     * Wrapper for  DirectDebitGetServiceNotificationDays
     *
     * @param int $serviceId the service id
     *
     * @return int
     */
    public function getServiceNotificationDays($serviceId)
    {
        return DirectDebitGetServiceNotificationDays($serviceId);
    }

    /**
     * Wrapper for direct_debit_calculate_working_days
     *
     * @param int $notificationDays direct debit notification days
     *
     * @return int
     */
    public function calculateWorkingDays($notificationDays)
    {
        return direct_debit_calculate_working_days($notificationDays);
    }

    /**
     * Perform a card payment and return the transaction Id.
     *
     * @param int   $serviceId the service id
     * @param float $total     total charge
     * @param int   $invoiceId the invoice Id
     *
     * @return int
     */
    public function payWithCard($serviceId, $total, $invoiceId)
    {
        $strResult = '';
        $strError = ''; // these have to be variables as they are passed by reference
        return financial_take_payment_with_status(
            $serviceId, $total, $strResult, $strError,
            IspPayments_PaywareManager::TERMINAL_TYPE_MOTO,
            $invoiceId
        );
    }


    /**
     * Wrapper for direct_debit_transaction_add
     *
     * @param int    $instructionId           the DD instruction Id
     * @param float  $totalOutstandingCharges total outstanding charges
     * @param int    $invoiceId               the invoice id
     * @param int    $timestamp               timestamp
     * @param string $codeHandle              dd transaction code handle
     *
     * @return array
     */
    public function addTransaction($instructionId, $totalOutstandingCharges, $invoiceId, $timestamp, $codeHandle)
    {
        return direct_debit_transaction_add(
            $instructionId,
            0,
            $totalOutstandingCharges,
            'Cancellation Payment',
            'Cancellation Payment',
            $totalOutstandingCharges,
            $invoiceId,
            array(),
            null,
            $timestamp,
            $codeHandle
        );
    }
}
