<?php
/**
 * Webmail product component
 * 
 * @package components.MAAF
 * 
 * <AUTHOR> <<EMAIL>>
 * 
 * @version $Id: BundledMailboxes.class.php,v 1.2 2007-12-03 07:52:13 swestcott Exp $
 */

/**
 * Needed library
 */
require_once '/local/data/mis/database/database_libraries/components/CProductComponent.inc';
require_once '/local/data/mis/database/database_libraries/userdata-access.inc';
require_once '/local/data/mis/database/database_libraries/components/MAAF/Mailboxes.CProductComponent.class.php';

/**
 * Class for product component that deals with bundled mailboxes
 * 
 * <AUTHOR> <<EMAIL>>
 * 
 */
class MAAF_CProductComponent_BundledMailboxes extends MAAF_CProductComponent_Mailboxes 
{	
	/**
	 * Number of bundeled mailboxes
	 */
	const MAILBOXES_NUMBER = 2;
	
	/**
	 * Status event raiser
	 * 
	 * @var string
	 */
	protected $strStatusEventRaiser = 'Bund<PERSON>Mailboxes';
	
	/**
	 * Method returns number of mailboxes
	 * 
	 * <AUTHOR> <<EMAIL>>
	 * 
	 * @return integer
	 */
	protected function getNumberOfAllowedMailboxes()
	{
		return self::MAILBOXES_NUMBER;
	} // end of method getNumberOfMailboxes()	

} // end of class MAAF_CProductComponent_BundledMailboxes