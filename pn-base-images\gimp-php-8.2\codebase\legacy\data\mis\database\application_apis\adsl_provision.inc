<?php
    /////////////////////////////////////////////////////////////////////////////////////////////
    // Name        : adsl_provision
    //
    // Description : Functions for ADSL provisioning - cease and provide
    //
    // Author      : <PERSON><PERSON>
    //
    /////////////////////////////////////////////////////////////////////////////////////////////

    require_once('/local/data/mis/database/standard_include.inc');
    require_once('/local/data/mis/database/database_libraries/userdata-access.inc');
    require_once('/local/data/mis/database/database_libraries/product-access.inc');
    require_once('/local/data/mis/database/database_libraries/financial-access.inc');
    require_once('/local/data/mis/database/database_libraries/secure-transaction-access.inc');
    require_once('/local/data/mis/database/database_libraries/tickets-access.inc');
    require_once('/local/data/mis/database/database_libraries/sql_primitives.inc');

    if(!usingPHP5Soap())
    {
        require_once('/local/data/mis/database/application_apis/soap_classes.inc');
    }
    require_once(COMMON_ADSL_API);
    require_once DATABASE_LIBRARY_ROOT.'CoreObjects/Financial/CFinancialHelper.inc';

    $arrPricing = CFinancialHelper::calcVat(adslGetBtActivationCost(), true);
    $floAdslActivationCost = number_format($arrPricing['incvat'], 2);

    define('ADSL_SERVICE_URL',                 ADSLGetConfigurationValues('strADSLServiceURL', true));
    define('ADSL_REPROVISION_COST',            $floAdslActivationCost);
    define('ADSL_REPROVISION_COST_PNUK',       $floAdslActivationCost + 11.75);
    define('ADSL_REPROVISION_COST_METRONET',   $floAdslActivationCost);
    define('ADSL_REPROVISION_COST_BT_INSTALL', ADSLGetConfigurationValues('decADSLProvisionCostBTInstall', true));
    define('WLR_HOUSE_MOVE_SURCHARGE',        '3.00');
    define('WLR_NUMBER_RETENTION_FEE',        '35.85');

    // Funnction to check if called by PHP5
    function usingPHP5Soap()
    {
        $strClass = 'soapclient';
        $arrExistingClasses = get_declared_classes();

        foreach($arrExistingClasses as $strExistingClass)
        {
            if(strtolower($strExistingClass) === $strClass)
            {
                return true;
            }
        }

        return false;
    }

    /////////////////////////////////////////////////////////////////////////////////////////////
    // Name        : getADSLAccountDetails
    //
    // Description : Get all ADSL information for this customer
    //
    // Parameters  : intServiceID - Service ID of customer
    //
    // Return      : arrAccountDetails - array of required information
    /////////////////////////////////////////////////////////////////////////////////////////////

    function getADSLAccountDetails($intServiceID) {
        if ($intServiceID=='')
        {
            return false;
        }
        $dbhConn = get_named_connection('userdata');

        $strQuery = "SELECT s.username, ".
                    "s.cli_number, ".
                    "s.type, ".
                    "s.status, ".
                    "u.salutation, ".
                    "u.forenames, ".
                    "u.surname, ".
                    "u.telephone, ".
                    "u.email, ".
                    "u.fax, ".
                    "u.mobile, ".
                    "a.house, ".
                    "a.street, ".
                    "a.town, ".
                    "a.county, ".
                    "a.postcode, ".
                    "ac.account_id ".
                    "FROM services s ".
                    "INNER JOIN users u ON s.user_id = u.user_id ".
                    "INNER JOIN addresses a ON u.address_id = a.address_id ".
                    "INNER JOIN accounts ac ON ac.customer_id = u.customer_id ".
                    "WHERE s.service_id = ".$intServiceID;

        $resResult = mysql_query($strQuery,$dbhConn);

        while($adbRow = mysql_fetch_array($resResult,MYSQL_ASSOC)) {

            $arrAccountDetails = $adbRow;

            // Fix for missing salutations
            $arrAccountDetails['salutation'] = empty($arrAccountDetails['salutation']) ? 'Mr' : $arrAccountDetails['salutation'];
        }

        $strQuery = "SELECT c.company_name ".
                    "FROM customers c ".
                    "INNER JOIN users u ON u.customer_id = c.customer_id ".
                    "INNER JOIN services s ON s.user_id = u.user_id ".
                    "WHERE s.service_id = $intServiceID";

        $resResult = mysql_query($strQuery,$dbhConn);

        if(mysql_num_rows($resResult) == 1) {

            $arrAccountDetails['company_name'] = mysql_result($resResult,0,0);
        }

        $dbhConn = get_named_connection('product');

        $strQuery = "SELECT product_name,
                     isp,
                     nat,
                     speed,
                     install_type,
                     bt_product_code,
                     hardware_type
                     FROM adsl_product
                     WHERE service_definition_id = ".$arrAccountDetails['type'];

        $resResult = mysql_query($strQuery,$dbhConn);

        while($adbRow = mysql_fetch_array($resResult,MYSQL_ASSOC)) {

            foreach ($adbRow as $strKey => $strValue) {

                $arrAccountDetails[$strKey] = $strValue;
            }
        }

        // Usual test for the plus.net.uk isp.
        if (strpos(strtolower($arrAccountDetails['product_name']), 'plus.net.uk') !== false) {

            $arrAccountDetails['isp'] = 'plus.net.uk';
        }

        $dbhConn = get_named_connection('adsl');

        $strQuery = "SELECT bt_reference_number, ".
                    "bt_circuit_number, ".
                    "location_floor, ".
                    "location_room, ".
                    "location_position ".
                    "FROM install_diary ".
                    "WHERE service_id = $intServiceID";

        $resResult = mysql_query($strQuery,$dbhConn);

        if(isset($resResult) && mysql_num_rows($resResult) == 1) {

            while($adbRow = mysql_fetch_array ($resResult,MYSQL_ASSOC)) {

                foreach ($adbRow as $strKey => $strValue) {

                    $arrAccountDetails[$strKey] = $strValue;
                }
            }

        } else {

            $arrAccountDetails['bt_reference']       = '';
            $arrAccountDetails['bt_circuit']         = '';
            $arrAccountDetails['location_floor']     = '';
            $arrAccountDetails['location_room']      = '';
            $arrAccountDetails['location_position']  = '';
        }

        $strRealm = '';

        //  problem:13893
        //  altered function call so we dont have multiple similar functions
        $arrRealms = radius_get_dialup_number_details(array(), $intServiceID);

        foreach ($arrRealms as $arrRealm) {

            if($arrRealm['com_method'] == 'ADSL/RDSL') {

                $arrAccountDetails['realm'] = $arrRealm['realm'];
                break;
            }
        }

        mysql_free_result($resResult);

        if(!isset($arrAccountDetails['realm']))
        {
            $arrAccountDetails['realm'] = '';
            settype($arrAccountDetails['realm'], 'string');
        }

        //$arrAccountDetails['service_id'] = $intServiceID;
        $arrAccountDetails['service_id'] = intval($intServiceID,10);

        $arrAccountDetails['template'] = getCRFTemplate($arrAccountDetails['install_type'],
                                                        $arrAccountDetails['nat'],
                                                        $arrAccountDetails['bt_product_code']);

        $dbhConn = get_named_connection('userdata');

        $strQuery = "SELECT INET_NTOA(ip_address) AS ip_address,
                            INET_NTOA(ip_netmask) AS ip_netmask ".
                    "FROM components c, config_staticip cs ".
                    "WHERE c.component_id = cs.component_id ".
                    "AND c.service_id = $intServiceID";

        $resResult = mysql_query($strQuery,$dbhConn);

        if(isset($resResult) && mysql_num_rows($resResult) == 1) {

            while($adbRow = mysql_fetch_array ($resResult,MYSQL_ASSOC)) {

                foreach ($adbRow as $strKey => $strValue) {

                    $arrAccountDetails[$strKey] = $strValue;
                }
            }

        } else {

            $arrAccountDetails['ip_address'] = '';
            $arrAccountDetails['ip_netmask']   = '';
        }

        // Need to increment last element of IP address because
        // of way these addresses are processed.
        // Like the peace of God, it passeth all understanding.
        if($arrAccountDetails['ip_address'] != '' && $arrAccountDetails['install_type'] == 'bt' && $arrAccountDetails['nat'] == 'no') {

            $arrIPElements = array();
            $arrIPElements = explode(".",$arrAccountDetails['ip_address']);
            $arrIPElements[3]++;
            $arrAccountDetails['ip_address'] = implode(".",$arrIPElements);
        }
        // Check to make sure that none of the array elements are NULL
        // If they are, it causes SOAP serialization/de-serialization to fail
        // because NuSOAP gives it a non-existent xsi type.
        foreach ( $arrAccountDetails as $strKey => $strValue ) {

            if(is_null($strValue)) {

                $arrAccountDetails[$strKey] = '';
            }
        }

        return $arrAccountDetails;
    }

    /////////////////////////////////////////////////////////////////////////////////////////////
    // Name        : getCRFTemplate
    //
    // Description : Get CRF template to use for this account
    //
    // Parameters  : strInstallType - two character install type code
    //               strNAT         - 'yes' for NAT, 'no' for No NAT
    //
    // Return      : strTemplate - name of template
    /////////////////////////////////////////////////////////////////////////////////////////////

    function getCRFTemplate ($strInstallType, $strNAT, $strBTProduct ) {

        if($strInstallType == 'si') {

            $strTemplate = 'Self_Install';

        } elseif ($strInstallType == 'bt') {

            if(strstr($strBTProduct,'USB')) {

                $strTemplate = 'BT_Install_USB';

            } elseif($strNAT == 'yes') {

                $strTemplate = 'BT_Install_NAT';

            } elseif ($strNAT == 'no') {

                $strTemplate = 'BT_Install_NoNAT';

            }
        }

        return $strTemplate;
    }

    /////////////////////////////////////////////////////////////////////////////////////////////
    // Name        : takeReprovidePayment
    //
    // Description : Take payment for re-provision
    //
    // Parameters  : arrAccountDetails - array of ADSL account information
    //
    // Return      : strStatus - success/fail
    /////////////////////////////////////////////////////////////////////////////////////////////

    function takeReprovidePayment ($arrAccountDetails, $strHouseMove = '') {

        // Set up visp details
        $arrDBVispDetails = product_visp_tags_get_all();
        $visp_details = array();

        foreach ($arrDBVispDetails as $arrVispConfig)
        {
            $strVispIsp = $arrVispConfig['isp'];

            $strUrlDomain = ('' == trim($arrVispConfig['url_domain'])) ? $arrVispConfig['portal_main_page_url'] : $arrVispConfig['url_domain'];
            $strUrlDomain = (false === strpos($strUrlDomain, 'www.')) ? "http://www.{$strUrlDomain}" : "http://{$strUrlDomain}";

            $arrVispConfigRecord = array(
                'visp'          => $arrVispConfig['full_name'],
                'visp_url'      => $strUrlDomain,
                'visp_strapline'=> $arrVispConfig['tag_line'],
                'support_number'=> $arrVispConfig['support_phone_number'],
                'support_email' => $arrVispConfig['support_email_address'],
                'portal_url'    => 'http://' . $arrVispConfig['portal_main_page_url']
                );

            $visp_details[$strVispIsp] = $arrVispConfigRecord;
        }

        // Additional entry for some legacy VISP not in visp_config SQL table
        $visp_details['plus.net.uk'] = $visp_details['plus.net'];

        $intServiceID = $arrAccountDetails['service_id'];
        $isp = $arrAccountDetails['isp'];

        //Metronet: Get the master account details.
        if ($isp == 'metronet') {
            //Used for billing to master account.
            $tmp = userdata_service_is_resold($intServiceID);
            if ($tmp) {
                $intServiceID = $tmp;
            }
            $strBillLineExtra = " for service {$arrAccountDetails['username']}";
        }
        else {
            $strBillLineExtra = '';
        }

        // charge customer the initial fee
        // set up email header
        $support_email = $visp_details[$isp]['support_email'];
        $email_header = "From: $support_email\nReply-To: $support_email\nReturn-Path: $support_email";

        // set up template object
        $template = new Template('/local/www/database-admin/scripts/financial/templates');

        if ($strHouseMove != 'Deferred')
        {
            //  Added for Problem:13151
            if ($arrAccountDetails['install_type'] == 'si')
            {
                // HACK-A-DOODLE-DOO

                if($isp == 'plus.net.uk') $amount = ADSL_REPROVISION_COST_PNUK;
                else $amount = ADSL_REPROVISION_COST;
            }
            else
            {
                $amount = ADSL_REPROVISION_COST_BT_INSTALL;
            }
        }
        else
        {
            $amount = 0.00;

        } // !(if ($arrAccountDetails['strHouseMove'] == 'NonDeferred'))

        $arrPricing = CFinancialHelper::splitVat($amount, true);

        // set up invoice items
        $amount_exc_vat    = number_format($arrPricing['exvat'], 2, '.', '');
        $vat_amount        = number_format($arrPricing['vat'], 2, '.', '');

        if ($strHouseMove != 'Deferred')
        {
            $invoice_item_desc = 'ADSL Re-Provisioning Charge' . $strBillLineExtra;

            // set up invoice
            $invoice_desc      = 'ADSL Re-Provisioning Charge' . $strBillLineExtra;

        } // if ($arrAccountDetails['strHouseMove'] == 'NonDeferred')
        else
        {
            $invoice_item_desc = 'ADSL Re-Provisioning Admin Charge' . $strBillLineExtra;

            // set up invoice
            $invoice_desc      = 'ADSL Re-Provisioning Admin Charge' . $strBillLineExtra;

        } // !(if ($arrAccountDetails['strHouseMove'] == 'NonDeferred'))

        $invoice_item      = array();
        $invoice_item[]    = array('amount'=>$amount_exc_vat, 'description'=>$invoice_item_desc, 'gross' => false);

        // attempt to take payment (master for metronet)
        $transaction_id = financial_take_payment_with_status($intServiceID, $amount, $result, $error, $invoice_id=0);

        if($transaction_id)
        {
            //generate the invoice (master for metronet)
            $invoice_id = financial_generate_invoice($intServiceID, $invoice_desc, $invoice_item, $transaction_id);

            // set up templates

            $template = new Template ('/local/www/database-admin/scripts/financial/templates');

            if ($strHouseMove != 'Deferred')
            {
                if (true == userdataIsMaafUser($intServiceID))
                {
                    $template->set_file('success_email_template', 'adsl_reprovision_successful_email_eb238.txt');
                    $template->set_file('success_ticket_template', 'adsl_reprovision_successful_ticket_eb239.txt');
                }
                else
                {
                    $template->set_file('success_email_template', 'adsl_reprovision_successful_email.txt');
                    $template->set_file('success_ticket_template', 'adsl_reprovision_successful_ticket.txt');
                }
            }
            else
            {
                if (true == userdataIsMaafUser($intServiceID))
                {
                    $template->set_file('success_email_template', 'move_broadband_deferred_email_eb450.txt');
                    $template->set_file('success_ticket_template', 'move_broadband_deferred_ticket_text_eb450.txt');
                }
                else
                {
                    $template->set_file('success_email_template', 'move_broadband_deferred_email.txt');
                    $template->set_file('success_ticket_template', 'move_broadband_deferred_ticket_text.txt');
                }
            }

            // set variables into template
            $template->set_var('username', $arrAccountDetails['username']);
            $template->set_var('full_name', ucwords(strtolower($arrAccountDetails['forenames'].' '.$arrAccountDetails['surname'])));
            $template->set_var('amount', $amount);
            $template->set_var('portal_url', $visp_details[$isp]['portal_url']);
            $template->set_var('visp_url', $visp_details[$isp]['visp_url']);
            $template->set_var('visp', $visp_details[$isp]['visp']);
            $template->set_var('visp_myreferrals_strapline', $visp_details[$isp]['visp_strapline']);

            // parse templates
            $template->parse('success_email', 'success_email_template');
            $template->parse('success_ticket', 'success_ticket_template');

            // create wordwrapped variables from templates
            $email_text  = wordwrap($template->get_var('success_email'), 72);
            $ticket_text = wordwrap($template->get_var('success_ticket'), 72);

            // create a contact on the user's account (child account for metronet)
            tickets_ticket_add('Internal', $arrAccountDetails['service_id'], '', '', 'Closed', $my_id, $ticket_text);

            // email customer
            mail($arrAccountDetails['email'], 'Payment for moving your ADSL', $email_text, $email_header);

            return("Success");

        }
        else
        {
            if (true == userdataIsMaafUser($intServiceID))
            {
                $template->set_file('failure_email_template', 'adsl_reprovision_failed_email_eb236.txt');
                $template->set_file('failure_ticket_template', 'adsl_reprovision_failed_ticket_eb237.txt');
            }
            else
            {
                $template->set_file('failure_email_template', 'adsl_reprovision_failed_email.txt');
                $template->set_file('failure_ticket_template', 'adsl_reprovision_failed_ticket.txt');
            }

            // set variables into template
            $template->set_var('username', $arrAccountDetails['username']);
            $template->set_var('amount', $amount);
            $template->set_var('account_type', $arrAccountDetails['product_name']);
            $template->set_var('full_name', ucwords(strtolower($arrAccountDetails['forenames'].' '.$arrAccountDetails['surname'])));
            $template->set_var('support_number', $visp_details[$isp]['support_number']);
            $template->set_var('portal_url', $visp_details[$isp]['portal_url']);
            $template->set_var('visp_url', $visp_details[$isp]['visp_url']);
            $template->set_var('visp', $visp_details[$isp]['visp']);
            $template->set_var('visp_myreferrals_strapline', $visp_details[$isp]['visp_strapline']);


            // parse templates
            $template->parse('failure_email', 'failure_email_template');
            $template->parse('failure_ticket', 'failure_ticket_template');


            // create wordwrapped variables from templates
            $email_text  = wordwrap($template->get_var('failure_email'), 72);
            $ticket_text = wordwrap($template->get_var('failure_ticket'), 72);


            // create a ticket on the user's account to failed billing team (child account for metronet)
            tickets_ticket_add('Internal', $arrAccountDetails['service_id'], '', '', 'Open', $my_id, $ticket_text, 0, 13, 0);

            // email customer
            mail($email_to, 'Please update your payment details', $email_text, $email_header);

            return("Failure");
        }
    }


    /////////////////////////////////////////////////////////////////////////////////////////////
    // Name        : ceaseADSL
    //
    // Description : Process cancellation of ADSL account. Take and confirm details, and generate
    //               and send appropriate document to BT.
    //
    // Parameters  : service_id - Service ID of customer to be cancelled.
    //               strADSLCeaseTypeTag - the type of the cease
    //
    // Return      : String indicating success or failure
    /////////////////////////////////////////////////////////////////////////////////////////////

    function ceaseADSL ($intServiceID,$strADSLCeaseTypeTag = '') {

        // Get ADSL account details
        $arrAccountDetails = getADSLAccountDetails($intServiceID);

        // Since this is a cease, use the default date and time.
        // Date = now + 7 days.
        // Time = 10.30 - 13.00 slot
        $arrAccountDetails['date'] = date("d/m/Y",(time() + 604800));
        $arrAccountDetails['time'] = 2;
        // Just to be safe
        settype($arrAccountDetails['time'], 'integer');

        // Not an ISDN conversion.
        $arrAccountDetails['ISDN'] = '';

        $arrSubStatus = ADSLProvisionSubStatusGetByTag('cease_issued');
        ADSLProvisionActionAdd($intServiceID, $arrSubStatus['usiADSLProcessSubStatusId'], $strAdditionalNote='An automated cease was issued for this account');

        //If the cease type is Cancellation Request or Failed Billing then don't send the CEASE to BT, these are performed manually
        if($strADSLCeaseTypeTag == 'cancellation_request' || $strADSLCeaseTypeTag == 'failed_billing')
        {
            return 'Success';
        }

        // Initialise SOAP service
        $objSoapClient = new soapclient(ADSL_SERVICE_URL);
        $strRetval = $objSoapClient->call('cease',$arrAccountDetails);

        if($strRetval == 'Success') {

            return "Success";
        } elseif($strRetval == 'Failure') {

            return "Failure\n";
        } else {

            return 'Unexpected Return Value : '.dump_structure_to_string($strRetval)."\n";
        }
    }


    /////////////////////////////////////////////////////////////////////////////////////////////
    // Name        : provideADSL
    //
    // Description : Process provision of ADSL account. Take and confirm details, and generate
    //               and send appropriate document to BT.
    //
    // Parameters  : intServiceID - Service ID of customer to be cancelled.
    //               strISDN      - is this an ISDN conversion
    //               strDate      - date of provision, defaults to seven days in the future
    //               intTime      - number indicating the standard timeslot for the installation,
    //                              defaults to 2 ( 10.30 - 13.00 )
    //
    // Return      : String indicating success or failure
    /////////////////////////////////////////////////////////////////////////////////////////////

    function provideADSL ($intServiceID, $strISDN='', $strDate='', $intTime='') {

        // Get ADSL account details
        $arrAccountDetails = getADSLAccountDetails($intServiceID);

        if($strDate == '') {

            // Since this is a cease, use the default date and time.
            // Date = now + 7 days.
            // Time = 10.30 - 13.00 slot
            $arrAccountDetails['date'] = date("d/m/Y",(time() + 604800));
            $arrAccountDetails['time'] = 2;
        } else {

            $arrAccountDetails['date'] = $strDate;
            $arrAccountDetails['time'] = $intTime;
        }
        // Just to be safe
        settype($arrAccountDetails['time'], 'integer');

        // Check if ISDN conversion.
        if($strISDN == 'Y')
        {
            $arrAccountDetails['ISDN'] = 'Y';
        }
        else
        {
            $arrAccountDetails['ISDN'] = '';
        }

        // Initialise SOAP service

        $objSoapClient = new soapclient(ADSL_SERVICE_URL);

        $arrSubStatus = ADSLProvisionSubStatusGetByTag('automated_provide');
        ADSLProvisionHistoryAdd($intServiceID, $arrSubStatus['usiADSLProcessSubStatusId'], 'An automated provide was issued for this account');

        $strRetval = $objSoapClient->call('provide',$arrAccountDetails);

        if($strRetval == 'Success') {

            return "Success";
        } elseif($strRetval == 'Failure') {

            return "Failure\n";
        } else {
            return "Unexpected Return Value : $strRetval";
        }
    }


    /////////////////////////////////////////////////////////////////////
    // Function    : adsl_get_dialup_number_details
    // Description : returns an array of dialup_group_id, phone_number, realm, communication_method
    //               for each dialup id in affinity database.
    //               Cribbed from original radius function and hacked about a bit,
    //               since it's only being used to get the ADSL realm.
    // Arguments   : $service_id   - Service ID
    //               $intProductID - Product ID
    // Returns     : array (0..N =>array(phone_number, The phone number to be dialed
    //                              realm,    The authentication realm, for display to the
    //                                        customer use 'username' instead
    //                              username, the actual login details with realm appended (if any),
    //                                        if a username is given it is substitiued for '<username>'
    //                              communication_method, The type of connection (for info only)
    //                              authentication_method, The kind of PPP authentication needed on this service
    //                             )
    /////////////////////////////////////////////////////////////////////

    //  There is a very similar function in radius-access.inc radius_get_product_dialup_details
    //  if you change any db queries here you may have tyo change them there as well
    function adsl_get_dialup_number_details($service_id = 0,$intProductID)
    {
        $connection = get_named_connection_with_db("product");

        $service['type'] = $intProductID;
        $all = array();
        // Find user
        $service = array();

        //get the dialup_group_ids
        $sql = " SELECT distinct component_radius_config.service_component_id, dialup_group_id, name, default_quantity ".
               " FROM service_definitions, service_component_config, component_radius_config ".
               " WHERE service_definitions.service_definition_id = service_component_config.service_definition_id".
               " AND service_component_config.service_component_id = component_radius_config.service_component_id ";

        // Limit dialup groups to only those their supposed to have
        $sql .= "AND service_component_config.service_definition_id = ".$intProductID." ";

        $result = PrimitivesQueryOrExit($sql, $connection,'get dialup_group_ids');
        while($rows = mysql_fetch_array($result, MYSQL_ASSOC)){
            $i['dialup_group_id']  = $rows['dialup_group_id'];
            $i['name']  = $rows['name'];
            $i['service_component_id']  = $rows['service_component_id'];

            if ($service_id > 0)
            {
                // Include active dialups on this account and default components
                $criteria = array (
                            'service_id' => $service_id,
                            'type'       => array($i['service_component_id']),
                            'status'     => array('active'));
                $array_components = userdata_component_find ($criteria);

                if (count ($array_components) > 0)
                {
                    $i['reason_included'] = 'ActiveComponentOnAccount';
                    $ids[] = $i;
                }
                else
                {
                    if ($rows ['default_quantity'] > 0)
                    {
                        $i['reason_included'] = 'DefaultComponent';
                        $ids[] = $i;
                    }
                }
            }
            else
            {
                // Only include those which will be added to all new accounts of this type
                if ($rows ['default_quantity'] > 0)
                {
                    $i['reason_included'] = 'DefaultComponent';
                    $ids[] = $i;
                }
            }
        }
        mysql_free_result($result);

        //change database
        $connection = get_named_connection_with_db("radius_reporting");

        foreach($ids AS $id)
        {
            $sql = "SELECT cg.group_id, c.phone_number,c.realm, ".
                         " st.communication_method, ".
                         " st.authentication_method ".
                   "FROM control c ".
                   "INNER JOIN control_group cg ON c.group_id = cg.group_id ".
                   "INNER JOIN service_topology st ON cg.service_topology_id = st.service_topology_id ".
                   "WHERE ";

            $sql .= " c.group_id ='".$id['dialup_group_id']."'";

            $sql .= " ORDER BY c.search_priority asc ";
            $sql .= " LIMIT 1";

            $result = PrimitivesQueryOrExit($sql, $connection,'Get matching dialup group');
            while($rows = mysql_fetch_array($result, MYSQL_ASSOC))
            {
                $this['dialup_group_id'] = $rows['group_id'];
                $this['reason_included'] = $id['reason_included'];
                $this['name'] = $id['name'];
                $this['phone_number'] = $rows['phone_number'];
                $this['username'] = 'username';
                if(isset($service['username']))
                {
                    $this['username'] = $service['username'];
                }
                $this['realm'] = $rows['realm'] ;

                if($this['realm']!='')
                {
                    $seperator = '@';
                    if(strstr($this['realm'],$seperator)!=FALSE)
                    {
                        $seperator = '-';
                    }

                    $this['username'] .= $seperator.$rows['realm'];
                }
                $this['com_method'] = $rows['communication_method'];
                $this['auth_method'] = $rows['authentication_method'];
                $all[$id['service_component_id']] = $this;
            }
        }
        mysql_free_result($result);
        return($all);
    }
?>
