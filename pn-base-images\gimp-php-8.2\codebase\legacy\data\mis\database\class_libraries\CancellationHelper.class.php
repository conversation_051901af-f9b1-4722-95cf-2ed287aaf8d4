<?php
/**
 * Class CancellationHelper
 *
 * @package    Legacycodebase
 * @subpackage Legacycodebase
 * <AUTHOR> <gay<PERSON><PERSON>@qburst.com>
 * @link       link
 */
/**
 * Can<PERSON>ation<PERSON>elper to schedule the account
 * for cancellation, to deactivate account,
 * to close account, to get the latest subscription
 * invoice details for the service, check whether need to
 * refund the customer, calculate the outstanding
 * charges based on the cease type and to get the
 * notice period based on Visp and product type.
 *
 * @package    Legacycodebase
 * @subpackage Legacycodebase
 * <AUTHOR> <gay<PERSON><PERSON>@qburst.com>
 * @copyright  2009 Plusnet
 * @link       link
 */
class CancellationHelper
{
    /**
     * Notice period for business type accounts
     */
    const BUSINESS_TYPE_NOTICE_PERIOD = 30;

    /**
     * Notice period for residential type accounts
     */
    const RESIDENTIAL_TYPE_NOTICE_PERIOD = 14;

    /**
     * Notice period not applicable accounts
     */
    const NOTICE_PERIOD_NOT_APPLICABLE = 0;

    /**
     * Object of Core_Service
     *
     * @var Core_Service
     */
    private $_objCoreService;

    /**
     * Service id
     *
     * @var $intServiceId
     */
    private $_intServiceId;

    /**
     * Constructor
     *
     * @param int          $intServiceId   Service id
     * @param Core_Service $objCoreService Core service object
     */
    public function __construct($intServiceId = null, Core_Service $objCoreService = null)
    {
        $this->includeLegacyFiles();

        $this->setServiceId($intServiceId);

        if (!empty($objCoreService)) {
            $this->_objCoreService = $objCoreService;
        } else {
            $this->_objCoreService = $this->getCoreService();
        }
    }

    /**
     * store the service id in the instance.
     * @param Int $intServiceId service ID to store.
     * @return void
     */
    public function setServiceId($intServiceId)
    {
        $this->_intServiceId = $intServiceId;
    }

    /**
     * Retrieve object of core service
     *
     * @return Core_Service $objCoreService
     */
    public function getCoreService()
    {
        if (!($this->_objCoreService instanceof Core_Service)) {
            $this->_objCoreService = $this->createCoreService();
        }

        return $this->_objCoreService;
    }

    /**
     * Check whether account is BBScope
     *
     * @return bool True or false
     */
    public function isBBScope()
    {
        $arrProduct = $this->getProductAttributes();

        if ('BBSCOPE' == $arrProduct['strFamilyHandle']) {
            return true;
        }

        return false;
    }

    /**
     * Check whether the account is scheduled for cancellation
     *
     * @return bool True or false
     */
    public function isAccountScheduledForCancellation()
    {
        $objEndDate = $this->_objCoreService->getEndDate();

        if (! ($objEndDate instanceof I18n_Date)) {
            return false;
        }

        $objNow = I18n_Date::now();
        $objNow->resetDay();
        $objEndDate->resetDay();

        if ($objEndDate->getTimestamp() >= $objNow->getTimestamp()) {
             return true;
        }

        return false;
    }

    /**
     * Call out to userdata-access.inc userdata_account_get_by_user()
     *
     * @param integer $userId ['user_id'] from service array
     *
     * @return bool True or false
     */
    public function getUserData($userId)
    {
        return userdata_account_get_by_user($userId);
    }

    /**
     * Call out to Core_Service to check whether an account is a partner visp
     *
     * @param string $isp isp name
     *
     * @return bool True or false
     */
    public function isPartnerVisp($isp)
    {
        return Core_Service::isPartnerVisp($isp);
    }

    /**
     * Check whether the account is scheduled for cancellation
     *
     * @param integer $serviceId Service id
     *
     * @return bool True or false
     */
    public function isRbmOnFiltered($serviceId)
    {
        return \Plusnet\Feature\FeatureToggleManager::isOnFiltered(
            'RBM_MIGRATION_COMPLETE',
            null,
            null,
            null,
            $serviceId
        );
    }

    /**
     * Check whether the account balance is valid for cancellation. 'Valid' means any of these:
     *
     * 1. RBM is enabled
     * 2. RBM is not enabled and account balance is £0.00
     * 3. Account is a partner visp
     *
     * @param array $service Service details, including coredb account balance
     *
     * @return bool Indicates whether account balance is valid for cancellation
     */
    public function isAccountBalanceValidForCancellation($service)
    {
        // If RBM is enabled then cancellation is allowed and remaining balance is handled by RBM
        if ($this->isRbmOnFiltered($this->_intServiceId)) {
            return true;
        }

        // Get the balance from coredb
        $account = $this->getUserData($service['user_id']);

        // Cancellation is allowed for zero balance or partner visp
        if ($account['balance'] == 0.00 || $this->isPartnerVisp($service['isp'])) {
            return true;
        }

        return false;
    }

    /**
     * Checks that account is destroyed
     *
     * Account is considered as destroyed when
     * service status is 'destroyed' or 'queued-destroy'
     *
     * @return boolean
     */
    public function isDestroyedAccount()
    {
        $arrStatuses = array(
            'destroyed',
            'queued-destroy'
        );

        $strServiceStatus = $this->_objCoreService->getStatus();

        return in_array($strServiceStatus, $arrStatuses);
    }

    /**
     * Check whether the account has CBCFlexComponent
     * and if present log the service event against flex id
     *
     * @param integer $intServiceEventId Service event id
     *
     * @return void
     */
    protected function checkForCBCFlexComponent($intServiceEventId)
    {
        $intCBCFlexComponentId = $this->getCBCFlexComponentId();

        if ($intCBCFlexComponentId > 0) {
            $intCBCFlexId = $this->getConfigFlexForCBCFlexId($intCBCFlexComponentId);

            if ($intCBCFlexId > 0) {
                // Log the event against Flex ID
                //As default set value of $intToCBCFlexID to zero.
                $this->insertCBCFlexServiceLog($intCBCFlexId, 0, $intServiceEventId);
            }
        }
    }

    /**
     * Check for adsl upgrade service for the account
     * If adsl upgrade service being temporary or in progress,
     * set upgrade status to failed and adsl status to cancelled.
     * Signal all components to self destruction and log the
     * cancellation to a ticket.
     *
     * @return void
     */
    protected function cancelAdslUpgrade()
    {
        // if service is temporary adsl upgrade service, set upgrade status to 'failed'
        $arrAdslUpgrade = $this->temporaryAdslUpgradeService();
        if ($arrAdslUpgrade) {
            $this->setAdslUpgradeStatus($arrAdslUpgrade['adsl_upgrade_id'], 'failed');
        }

        $arrActiveUpgradeRecord = array();
        $arrUpgradeRecords = $this->getAdslUpgradeStatus();
        // if service has in_progress adsl upgrade - cancel it also
        if (!empty($arrUpgradeRecords)) {
            foreach ($arrUpgradeRecords as $arrUpgradeRecord) {
                if ($arrUpgradeRecord['upgrade_status'] == 'in_progress') {
                    $arrActiveUpgradeRecord = $arrUpgradeRecord;
                }
            }



            // there is an 'in_progress' adsl upgrade
            if (!empty($arrActiveUpgradeRecord)) {
                // change the upgrade status to 'failed'
                $this->setAdslUpgradeStatus($arrActiveUpgradeRecord['adsl_upgrade_id'], 'failed');
                // set the adsl status to 'cancelled'
                $this->updateAdslStatus($arrActiveUpgradeRecord['holding_adsl_service_id'], 'cancelled');
                // change the upgrade service's status
                $this->updateServiceStatus($arrActiveUpgradeRecord['holding_adsl_service_id'], 'queued-destroy');
                // set upgrade service's enddate to today
                $strServiceEndDate = I18n_Date::now()->toI18nStrHere('LONG_DB_DATE_FORMAT');
                $this->setServiceEndDate($arrActiveUpgradeRecord['holding_adsl_service_id'], $strServiceEndDate);
                // Signal all components to self-destruction
                $this->destroyComponents($arrActiveUpgradeRecord['holding_adsl_service_id']);
                //Log cancellation to a ticket
                $recipientActor = $this->getRecipientActor($arrActiveUpgradeRecord['holding_adsl_service_id']);
                $strTemplateHandle = 'temp_upgrade_account_cancelled';
                $this->addServiceNotice($strTemplateHandle, array(), $recipientActor);
            }
        }
    }

    /**
     * To cancel/destroy account
     *
     * @return void
     */
    public function closeAccount($ignoreLoginComponent = false)
    {
        //signal all components to self-destruction
        $this->destroyComponents($this->_intServiceId, $ignoreLoginComponent);

        //change the service status to 'queued-destroy'
        $this->updateServiceStatus($this->_intServiceId, 'queued-destroy');

        //check if they have a CBC Flex component as this will need logging for report purposes.
        $intServiceEventId = $this->logActionedForScheduledCancellation();
        $this->checkForCBCFlexComponent($intServiceEventId);

        $this->updateInstallDiaryStatus($this->_intServiceId);

        $recipientActor = $this->getRecipientActor($this->_intServiceId);
        $strTemplateHandle = 'account_cancellation_automatic';
        $this->addServiceNotice($strTemplateHandle, array(), $recipientActor);

        //check for adsl upgrade for the account
        $this->cancelAdslUpgrade();
    }

    /**
     * Updates the install diary status to cancelled if set to not submitted
     *
     * @param string $serviceId serviceId
     */
    protected function updateInstallDiaryStatus($serviceId)
    {
        $arrInstallDiary = $this->getInstallDiaryStatus($serviceId);
        if($arrInstallDiary['status'] == 'not_submitted')
        {
            $this->updateAdslStatus($serviceId, 'cancelled');
        }
    }

    /**
     * Gets the install diary status
     *
     * @param string $serviceId service id
     * @return array
     */
    protected function getInstallDiaryStatus($serviceId)
    {
        return AdslGetInstallDiary($serviceId);
    }

    /**
     * Get chargeable components for the service
     *
     * @param Boolean $includeBBComponent Boolean to include BB component
     *
     * @return array Array of chargeable components
     */
    public function getAccountChargeableComponents($includeBBComponent = false)
    {
        $arrChargeableComponents = array();

        if ($includeBBComponent) {
            $arrRawChargeableComponents = $this->getAllChargeableComponents();
        } else {
            $arrRawChargeableComponents = $this->getChargeableComponents();
        }

        // Format chargeable components
        //   WLR - component name, no other features or options.
        //   Domians - Domian name
        //   Bullguard - component name
        //   BB phone - component name
        foreach ($arrRawChargeableComponents as $arrComponent) {
            // We should only display each component once (this means we'll only see WLR once)
            $arrChargeableComponents[$arrComponent['component_id']] = $arrComponent;

            $arrPattern = array('.co.uk','.me.uk');

            // If the domain is active then display the domain name, if not show the component name
            if ($arrComponent['component_type_id'] == COMPONENT_POUND_CO_UK_DOMAIN
                && !empty($arrComponent['description'])
            ) {
                //For domain components, if description is domain name (ie if it contains .me.uk or .co.uk)
                //its shown along with the component name
                //Domain component without a domain name in the description needs to be shown.
                if (strpos($arrComponent['description'], $arrPattern[0])
                    || strpos($arrComponent['description'], $arrPattern[1])
                ) {
                    $arrComponent['strChargeableComponentName'] .= ' ('.$arrComponent['description'].')';

                     $arrChargeableComponents[$arrComponent['component_id']] = $arrComponent;
                }
            }

            $arrComponent['decChargeableComponentPrice']
                = sprintf('%.2f', $arrComponent['decChargeableComponentPrice']);
        }

        // The following components cannot exist on their own:
        //  + COMPONENT_CREDIT_CARD_SURCHARGE
        //  + COMPONENT_NON_DD_SURCHARGE
        //  + COMPONENT_PAPERBILLING
        // If these are the only components returned, then remove them from the array..
        if (count($arrChargeableComponents < 3)) {
            $arrChargeableComponentsWithoutExtras = $arrChargeableComponents;

            foreach ($arrChargeableComponentsWithoutExtras as $intKey => $arrComponent) {

                if ($arrComponent['component_type_id'] == COMPONENT_CREDIT_CARD_SURCHARGE
                    || $arrComponent['component_type_id'] == COMPONENT_NON_DD_SURCHARGE
                    || $arrComponent['component_type_id'] == COMPONENT_PAPERBILLING
                ) {
                    unset($arrChargeableComponentsWithoutExtras[$intKey]);
                }

            }

            // if empty, then we only have components 'paper billing' and 'credit card'
            // so there's no charges and we should return an empty array
            if (empty($arrChargeableComponentsWithoutExtras)) {

                $arrChargeableComponents = array();
            }
        }

        return $arrChargeableComponents;
    }

    /**
     * Get the correct notice period for the account
     *
     * @return int
     */
    public function getNoticePeriod()
    {
        $objServiceDefinition = $this->createCoreDefinition();

        $intNoticePeriod = self::RESIDENTIAL_TYPE_NOTICE_PERIOD;

        if ($objServiceDefinition->isBusiness()) {

            if ($this->isBBScope()) {

                $intNoticePeriod = self::NOTICE_PERIOD_NOT_APPLICABLE;
            } else {

                $intNoticePeriod = self::BUSINESS_TYPE_NOTICE_PERIOD;
            }
        }

        return $intNoticePeriod;
    }

    /**
     * Get the latest subscription invoice details
     *
     * @param float $floAmount Amount
     *
     * @return array
     */
    public function getLatestInvoiceToRefund($floAmount)
    {
        $intAccountId = $this->getAccountByUserId();

        return $this->sqlToGetLatestInvoiceToRefund($floAmount, $intAccountId);
    }

    /**
     * Query to get the latest invoice
     *
     * @param float   $floAmount    Amount
     * @param integer $intAccountId Account id
     *
     * @return array
     */
    protected function sqlToGetLatestInvoiceToRefund($floAmount, $intAccountId)
    {
        $dbhConnection = get_named_connection_with_db('financial_reporting')
                         or report_error(__FILE__, __LINE__, 'Failed to connect to database');

        $floAmount = PrimitivesRealEscapeString($floAmount, $dbhConnection);
        $intAccountId = PrimitivesRealEscapeString($intAccountId, $dbhConnection);

        $strQuery = "SELECT
                        MAX(sales_invoice_id) AS intSalesInvoiceId
                    FROM
                        financial.sales_invoices
                    WHERE
                        invoice_status = 'fully_paid'
                    AND
                        invoice_type = 1
                    AND
                        gross_value >= $floAmount
                    AND
                        account_id = $intAccountId";

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);
        $intSalesInvoiceId = PrimitivesResultGet($resResult, 'intSalesInvoiceId');

        if (empty($intSalesInvoiceId)) {
            $this->raiseFailedRefundTicket($floAmount);

            return false;
        }

        return $intSalesInvoiceId;
    }

    /**
     * Get the outstanding charges with respect to the
     * cease type and cancellation date, which is being
     * calculated from the notice period
     *
     * @param I18n_Date $objCancellationDate         Cancellation date
     * @param I18n_Date $objReqestedCancellationDate Requested cancellation date
     * @param array     $chargeTypes                 Array of types of fees to process
     * @param bool      $bolExcludeCessationFee      Flag to exclude cassation charge from calculations (MigrationsOut)
     *
     * @return array
     */
    public function getOutstandingCharges(
        I18n_Date $objCancellationDate,
        I18n_Date $objReqestedCancellationDate,
        $chargeTypes = null,
        $bolExcludeCessationFee = false
    ) {
        $arrOptions = array();

        $uxtCancellationDate = $objCancellationDate->getTimestamp();
        $uxtRequestedCancellationDate = $objReqestedCancellationDate->getTimestamp();

        if ($bolExcludeCessationFee) {
            $arrOptions = array(
                'bolExcludeCessationContractCharge' => true
            );
        }

        return $this->getOutstandingFees(
            $uxtCancellationDate,
            $uxtRequestedCancellationDate,
            $chargeTypes,
            $arrOptions
        );
    }

    /**
     * Refund customer
     *
     * @param float $floAmount    Amount to be refunded
     * @param int   $intInvoiceId Invoice id to be refunded against
     *
     * @return bool true/false based on refund process completion
     */
    public function refundCustomer($floAmount, $intInvoiceId)
    {
        $objRefundManager = $this->createRefundManager($intInvoiceId);

        return $this->processRefundForAccount($floAmount, $objRefundManager);
    }

    /**
     * Raise a failed refund ticket to the customer account
     *
     * @param float $floAmount Amount to be refunded
     *
     * @return void
     */
    protected function raiseFailedRefundTicket($floAmount)
    {
        $intTeamId = $this->getTeamIdByHandle('CSC_BILLING');
        $recipientActor = $this->getRecipientActor($this->_intServiceId);
        $strTemplateHandle = 'refund_failed';
        $arrTemplateData = array('floRefundAmount' => $floAmount);
        $this->addTicket($strTemplateHandle, $arrTemplateData, $recipientActor, $intTeamId);
    }

    /**
     * Get team id by handle
     *
     * @param string $strHandle Handle
     *
     * @return int
     */
    protected function getTeamIdByHandle($strHandle)
    {
        return phplibGetTeamIdByHandle($strHandle);
    }

    /**
     * Create object C_Refunds_Manager
     *
     * @param int $intInvoiceId Invoice id to be refunded against
     *
     * @return C_Refunds_Manager
     */
    protected function createRefundManager($intInvoiceId)
    {
        return new C_Refunds_Manager($intInvoiceId, SCRIPT_USER, $this->_intServiceId);
    }

    /**
     * Process refunding on the account
     *
     * @param float             $floAmount        Amount to be refunded
     * @param C_Refunds_Manager $objRefundManager Invoice id to be refunded against
     *
     * @return bool
     */
    protected function processRefundForAccount($floAmount, $objRefundManager)
    {
        $strRefundReason = "other";
        $strRefundReasonOther = "Refund for overpaid subscription charges : $floAmount";

        $bolRefundProcess = $objRefundManager->processRefund($floAmount, $strRefundReason, $strRefundReasonOther);

        if ($bolRefundProcess) {
            return true;
        }
        $this->raiseFailedRefundTicket($floAmount);
        return false;
    }

    /**
     * Deactivate the account
     *  - Store the account's current status
     *  - Change the service's status to queued-deactivate
     *  - Signal all components to auto-disable
     *  - Log the disabling in a service event
     *  - Log the cancellation in a ticket
     *  - Change the service's status to deactive
     *
     * @return void
     */
    public function deactivateAccount()
    {
        $this->setKeyedServiceNote('PRE_DISABLE_STATUS');
        $this->updateServiceStatus($this->_intServiceId, 'queued-deactivate');
        $this->disablecomponents();
        $this->logDisableToServiceEvent();

        $recipientActor = $this->getRecipientActor($this->_intServiceId);
        $strTemplateHandle = 'account_deactivated';
        $this->addServiceNotice($strTemplateHandle, array(), $recipientActor);

        $this->updateServiceStatus($this->_intServiceId, 'deactive');
    }

    /**
     * Schedule the account for cancellation.
     *  - set end date for service
     *  - schedule cancellation using system events
     *  - raise a ticket to log the scheduled cancellation
     *
     * @return void
     */
    public function scheduleAccountCancellation()
    {
        $uxtTimeStamp = time();
        $objDate = $this->createI18nDateFromTimestamp($uxtTimeStamp);
        $objModifiedDate = $objDate->getModified(1, I18n_Date::MONTHS);
        $strServiceEndDate = $objModifiedDate->toMySql();
        $strCancellationDate = $objModifiedDate->toI18nStrHere('SHORT_DATE_FORMAT');

        $this->setServiceEndDate($this->_intServiceId, $strServiceEndDate);
        $this->logScheduledCancellation($strServiceEndDate);

        $recipientActor = $this->getRecipientActor($this->_intServiceId);
        $strTemplateHandle = 'account_cancellation_scheduled';
        $arrTemplateData = array('strCancellationDate' => $strCancellationDate);
        $this->addServiceNotice($strTemplateHandle, $arrTemplateData, $recipientActor);
    }

    /**
     * Get the MAC key generation date
     *
     * @return I18n_Date $objMacKeyGenerationDate MAC key generation date
     *
     */
    public function getMacKeyGenerationDate()
    {
        $uxtMacKeyGenerationDate = $this->getMACkeyScheduledGenerationDate();
        if (empty($uxtMacKeyGenerationDate)) {

            return null;
        }

        return $this->createI18nDateFromTimestamp($uxtMacKeyGenerationDate);
    }

    /**
     * Query to get the mac key generation date
     *
     * @return timestamp $uxtMacKeyGenerationDate Timestamp value
     */
    protected function getMACkeyScheduledGenerationDate()
    {
        $dbhConnection = get_named_connection_with_db('userdata_reporting')
                            or report_error(__FILE__, __LINE__, 'Failed to connect to database');
        $intServiceId = PrimitivesRealEscapeString($this->_intServiceId, $dbhConnection);

        $strQuery = 'SELECT
                        UNIX_TIMESTAMP(dtmMACScheduledGeneration) AS uxtMacKeyGenerationDate
                    FROM
                        userdata.tblServiceMACDetails
                    WHERE
                        dtmMACScheduledGeneration >= DATE_SUB(NOW(), INTERVAL 37 DAY)
                    AND
                        intServiceID = '.$intServiceId.'
                    ORDER BY
                        dtmMACScheduledGeneration DESC
                    LIMIT 1';

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);
        $uxtMacKeyGenerationDate = PrimitivesResultGet($resResult, 'uxtMacKeyGenerationDate');

        return $uxtMacKeyGenerationDate;
    }

    /**
     * Downgrade to generic services
     *
     * @return void
     */
    public function downgradeToGenericServices()
    {
        global $global_component_configurators;
        $arrService = $this->userdataServiceGet();
        $intTypeToCheckForHardware = $this->getTypeToCheckForHardware($arrService);
        $processMigration = $this->createProcessMigration(null, $global_component_configurators);
        $intCBCFlexId = $processMigration->configCBCComponent($this->_intServiceId);

        try {
                $processMigration->selectDestinationAccountType($arrService);
                $processMigration->changeDestinationAccountType($arrService, $intTypeToCheckForHardware, $intCBCFlexId);

        } catch ( Exception $objException ) {

                // We were unable to change the account type for this service - report an error, but don't die as
                // this method is called from critical systems such as BBCR
                error_log(
                    "Unable to downgrade to accout with service id {$this->_intServiceId}"
                    . " to generic services in CancellationHelper::downgradeToGenericServices."
                    . " Failed with the message:\n\n"
                    . $objException->getMessage()
                );
        }
    }

    /**
     * Get type id to check for hardware
     *
     * @param array $arrService Service array
     *
     * @return integer Type id
     */
    protected function getTypeToCheckForHardware($arrService)
    {
        $arrADSLStatus = adsl_get_install_status_by_service($arrService, '');

        foreach ($arrADSLStatus[0] as $arrADSLStatusDetail) {

            if ($arrADSLStatusDetail['status'] == 'active' || $arrADSLStatusDetail['status'] == 'cancelled') {
                $intTypeToCheckForHardware = $arrService['type'];
            }
        }

        return $intTypeToCheckForHardware;
    }

    /**
     * Sort the invoices passed it into charges / refunds and bill as appropriate.
     *
     * @param array $arrItemsToInvoice An array of outstanding cancellation invoice items
     *
     * @return integer invoice id
     */
    public function raisePendingCancellationInvoice(array $arrItemsToInvoice)
    {
        // Go through and add items, split into 3 sections (cancellation charges, deferred contracts and refunds)
        $floTotalOfDeferredContracts = 0;
        $floCancellationCharges = 0;
        $arrInvoiceItems = array();

        // The last elemet of the array is always a total to invoice, so we don't want to include this as a line item..
        // .. so take what we need, and remove it from the array.
        $floTotalOutstandingCharges  = $arrItemsToInvoice['floTotalOutstandingCharges'];
        array_pop($arrItemsToInvoice);

        foreach ($arrItemsToInvoice as $arrOutstandingCharge) {

            switch ($arrOutstandingCharge['strInvoiceLine'])
            {
                case 'CANCELLATION_CHARGES' :

                    // If part of cancellation charges (ie normal cancellation charges that apply) then add to one item
                    $floCancellationCharges += $arrOutstandingCharge['floCost'];

                    break;

                case 'REFUND_CHARGES' :
                case 'CBC_CHARGES':
                case 'DEFERRED_CONTRACTS' :
                case 'DEFERRED':
                case 'PROFIT_FOREGONE':
                default:

                    $componentId
                        = (!empty($arrOutstandingCharge['componentId'])) ? $arrOutstandingCharge['componentId'] : null;
                    // Add these as their own line items
                    $arrInvoiceItems[] = array(
                        'description'   => $arrOutstandingCharge['strName'],
                        'amount'        => $arrOutstandingCharge['floCost'],
                        'gross'         => true,
                        'component_id'  => $componentId,
                    );

                    $floTotalOfDeferredContracts += $arrOutstandingCharge['floCost'];

                    break;

            } // switch

        } // foreach

        if (isset($floCancellationCharges) && $floCancellationCharges > 0) {

            $arrInvoiceItems[] = array('description' => 'Subscription Billing',
                                       'amount'      => $floCancellationCharges,
                                       'gross'       => true);

            $arrInvoiceItems = array_reverse($arrInvoiceItems);

        } // end if

        // Now create an invoice, which we can take payment for later..
        $intInvoiceId = $this->financialGenerateInvoice('Cancellation Charge', $arrInvoiceItems);

        $arrAccount = $this->getAccountIdByServiceId();

        return $intInvoiceId;
    }


    /**
     * Get details for a service
     *
     * @return array Service detail
     */
    protected function userdataServiceGet()
    {
        return userdata_service_get($this->_intServiceId);
    }

    /**
     * Create Process migration object
     *
     * @param date  $dteCompletionDate               Completion date
     * @param array $arrGlobalComponentConfigurators Global component configurators
     *
     * @return C_Process_Migration Object of process migration
     */
    protected function createProcessMigration($dteCompletionDate, $arrGlobalComponentConfigurators)
    {
        $processMigration = new C_Process_Migration(
            $this->_intServiceId,
            $dteCompletionDate,
            $arrGlobalComponentConfigurators
        );

        return $processMigration;
    }

    /**
     * Function to create the object I18nDate from Timestamp
     *
     * @param timestamp $uxtTimeStamp Timestamp
     *
     * @return I18n_Date
     */
    protected function createI18nDateFromTimestamp($uxtTimeStamp)
    {
        return I18n_Date::fromTimestamp($uxtTimeStamp);
    }

    /**
     * Log that a service has been scheduled for cancellation.
     *
     * @param string $strServiceEndDate Service end date
     *
     * @return int
     */
    protected function logScheduledCancellation($strServiceEndDate)
    {
        return userdata_service_log_scheduled_cancellation($this->_intServiceId, $strServiceEndDate);
    }

    /**
     * Get the outstanding charge with respect to the
     * cancellation date, which is being calculated from the
     * notice period and cease type
     *
     * @param timestamp $uxtCancellationDate          Cancellation date
     * @param timestamp $uxtRequestedCancellationDate Requested Cancellation date
     * @param array     $chargeTypes                  Array of types of fees to process
     * @param array     $arrOptions                   Array with options exclude/include cessation charge
     *
     * @return array Array of outstanding fees on account
     */
    protected function getOutstandingFees(
        $uxtCancellationDate,
        $uxtRequestedCancellationDate,
        $chargeTypes = null,
        $arrOptions = array()
    ) {
        return financialGetOutstandingFees(
            $this->_intServiceId,
            $uxtCancellationDate,
            $uxtRequestedCancellationDate,
            $chargeTypes,
            $arrOptions
        );
    }

    /**
     * Disable all components attached to the service
     *
     * @return array
     */
    protected function disablecomponents()
    {
        return disable_all_components($this->_intServiceId);
    }

    /**
     * Log that the service has been disabled.
     *
     * @return int
     */
    protected function logDisableToServiceEvent()
    {
        return userdata_service_log_disable($this->_intServiceId);
    }

    /**
     * Update the service note table with the status
     *
     * @param string $strNoteStatus Service note status
     *
     * @return int
     */
    protected function setKeyedServiceNote($strNoteStatus)
    {
        return userdata_keyed_service_note_set(
            $this->_intServiceId,
            $strNoteStatus,
            $this->_objCoreService->getStatus()
        );
    }

    /**
     * Create core definition object
     *
     * @return Core_ServiceDefinition
     */
    protected function createCoreDefinition()
    {
        return new Core_ServiceDefinition($this->_objCoreService->getType());
    }

    /**
     * Get chargeable components
     *
     * @return array
     */
    protected function getChargeableComponents()
    {
        return CProductComponent::getChargeableComponentsFromServiceId(
            $this->_intServiceId,
            array('INTERNET_CONNECTION')
        );
    }

    /**
     * Get all chargeable components including BB component
     *
     * @return array
     */
    protected function getAllChargeableComponents()
    {
        return CProductComponent::getChargeableComponentsFromServiceId(
            $this->_intServiceId
        );
    }

    /**
     * Create core service object
     *
     * @return Core_Service
     */
    protected function createCoreService()
    {
        return new Core_Service($this->_intServiceId);
    }

    /**
     * Get product attributes for an account
     *
     * @return array
     */
    protected function getProductAttributes()
    {
        return product_get_account_attributes($this->_objCoreService->getType());
    }

    /**
     * Generates an invoice and returns the invoice id for an account.
     *
     * @param String $strInvoiceDescription Description of invoice
     * @param Array  $arrInvoiceItems       An array of invoice items
     *
     * @return integer
     */
    protected function financialGenerateInvoice($strInvoiceDescription , $arrInvoiceItems)
    {
        return financial_generate_invoice($this->_intServiceId, $strInvoiceDescription, $arrInvoiceItems);
    }

    /**
     * Returns an array with account information
     *
     * @return        Array
     */
    protected function getAccountIdByServiceId()
    {
        return userdata_account_get_by_service($this->_intServiceId);
    }

    /**
     * Add Ticket
     *
     * @param string             $strTemplateHandle Template handle
     * @param array              $arrTemplateData   Array template data
     * @param Auth_BusinessActor $recipientActor    Business actor object
     * @param int                $intTeamId         Team Id
     *
     * @return void
     */
    protected function addTicket(
        $strTemplateHandle, array $arrTemplateData, Auth_BusinessActor $recipientActor, $intTeamId
    ) {
        $ticketClient = BusTier_BusTier::getClient('tickets');
        $ticket = $ticketClient->createTicket($recipientActor);
        $ticket->commentFromTemplate($strTemplateHandle, $arrTemplateData, $recipientActor, $intTeamId);
        Db_Manager::commit();
    }


    /**
     * Add Service Notice
     *
     * @param string             $strTemplateHandle Template handle
     * @param array              $arrTemplateData   Array template data
     * @param Auth_BusinessActor $recipientActor    Business actor object
     *
     * @return void
     */
    protected function addServiceNotice(
        $strTemplateHandle, array $arrTemplateData, Auth_BusinessActor $recipientActor
    ) {
        $actionerActor = $this->getRecipientActorForScriptUser();

        $serviceNoticeClient =  BusTier_BusTier::getClient('serviceNotices');
        $serviceNoticeType   = $serviceNoticeClient->getNoticeTypeByHandle('Script');
        $intServiceTypeId    = $serviceNoticeType->getServiceNoticeTypeId();
        $serviceNotice       = $serviceNoticeClient->createServiceNotice($recipientActor, 'service_notice_transaction');

        $result = $serviceNotice->commentFromTemplate(
            $strTemplateHandle, $arrTemplateData,
            $actionerActor, $intServiceTypeId
        );

        Db_Manager::commit('service_notice_transaction');
    }


    /**
     * Get business actor fir the service id
     *
     * @param integer $intServiceId Service id
     *
     * @return Auth_BusinessActor
     */
    public function getRecipientActor($intServiceId)
    {
        return Auth_BusinessActor::getActorByExternalUserId((int)$intServiceId);
    }


    /**
     * Get business actor for the script user
     *
     * @return Auth_BusinessActor
     */
    public function getRecipientActorForScriptUser()
    {
        return Auth_BusinessActor::getActorByExternalUserId(SCRIPT_USER);
    }



    /**
     * Destroy all components attached to a service
     *
     * @param int $intAdslServiceId Adsl service id
     *
     * @return array
     */
    protected function destroyComponents($intAdslServiceId, $ignoreLoginComponent=false)
    {
        return destroy_all_components($intAdslServiceId, $ignoreLoginComponent);
    }

    /**
     * Log that a scheduled cancellation has been actioned
     *
     * @return int
     */
    protected function logActionedForScheduledCancellation()
    {
        return userdata_service_log_actioned_scheduled_cancellation($this->_intServiceId);
    }

    /**
     * Get the CBCFlex active component id for the service.
     *
     * @return int
     */
    protected function getCBCFlexComponentId()
    {
        return GetCBCFlexActiveComponentID($this->_intServiceId);
    }

    /**
     * Get CBC flex id for the component id.
     *
     * @param int $intCBCFlexComponentId CBCFlex component id
     *
     * @return int
     */
    protected function getConfigFlexForCBCFlexId($intCBCFlexComponentId)
    {
        return ConfigFlexGetCBCFlexID($intCBCFlexComponentID);
    }

    /**
     * Add log to the CBC Flex logging
     *
     * @param int $intCBCFlexID      CBCFlex id
     * @param int $intToCBCFlexId    To CBCFlex id
     * @param int $intServiceEventID Service event id
     *
     * @return int
     */
    protected function insertCBCFlexServiceLog($intCBCFlexID, $intToCBCFlexId, $intServiceEventID)
    {
        return InsertCBCFlexServiceLog($this->_intServiceId, $intCBCFlexID, $intToCBCFlexId, $intServiceEventID);
    }

    /**
     * Service an ADSL upgrade temporary service or not
     *
     * @return array
     */
    protected function temporaryAdslUpgradeService()
    {
        return userdata_is_service_adsl_upgrade($this->_intServiceId);
    }

    /**
     * Sets the status of a particular adsl upgrade record
     *
     * @param int    $intAdslUpgradeId Adsl upgrade id
     * @param string $strStatus        Status
     *
     * @return bool
     */
    protected function setAdslUpgradeStatus($intAdslUpgradeId, $strStatus)
    {
        return userdata_adsl_upgrade_set_status($intAdslUpgradeId, $strStatus);
    }

    /**
     * Get all adsl upgrade service associated with specified service
     *
     * @return array
     */
    protected function getAdslUpgradeStatus()
    {
        return userdata_get_adsl_upgrade_services($this->_intServiceId);
    }

    /**
     * Update the adsl status.
     *
     * @param int    $intAdslServiceId Adsl Service id
     * @param string $strStatus        Status
     *
     * @return bool
     */
    protected function updateAdslStatus($intAdslServiceId, $strStatus)
    {
        return alter_adsl_status($intAdslServiceId, $strStatus);
    }

    /**
     * Update the service status
     *
     * @param int    $intAdslServiceId Adsl Service id
     * @param string $strStatus        Status
     *
     * @return int
     */
    protected function updateServiceStatus($intAdslServiceId, $strStatus)
    {
        return userdata_service_set_status($intAdslServiceId, $strStatus);
    }

    /**
     * Set the service end date
     *
     * @param int    $intAdslServiceId  Adsl Service id
     * @param string $strServiceEndDate Service end date
     *
     * @return int
     */
    protected function setServiceEndDate($intAdslServiceId, $strServiceEndDate)
    {
        return userdata_service_set_enddate($intAdslServiceId, $strServiceEndDate);
    }

    /**
     * Method to get account id by user id
     *
     * @return int
     */
    protected function getAccountByUserId()
    {
        return Core_Account::getAccountIdByUserId(
            $this->_objCoreService->getUserId(), Db_Manager::DEFAULT_TRANSACTION
        );
    }

    /**
     * Getter for all mac key data we need for email
     *
     * This is only used with a customer migrates out, and therefore
     * when we call generateCeaseType() we expect a CancellationCeaseType
     * of type CancellationCeaseType::MIGRATION_OUT
     *
     * @return array
     */
    public function getMacKeyEmailData()
    {

        $data = array();

        $objCancellationDate = I18n_Date::now();
        //This should be date when customer reqested MAC key
        $objReqestedCancellationDate = I18n_Date::now();

        $data['arrOutstandingFees'] = $this->getOutstandingCharges(
            $objCancellationDate,
            $objReqestedCancellationDate,
            null,
            true
        );

        // Extract the total from the main charges array,
        // so we can loop through it in the template.
        $data['floTotalOutstandingCharges']
            = sprintf('%.2f', $data['arrOutstandingFees']['floTotalOutstandingCharges']);

        unset( $data['arrOutstandingFees']['floTotalOutstandingCharges'] );

        // Format the values in arrOutstandingFees
        foreach ($data['arrOutstandingFees'] as $intKey => $arrFee) {
            if (isset($arrFee['floCost'])) {
                $data['arrOutstandingFees'][$intKey]['floCost'] = sprintf('%.2f', $arrFee['floCost']);
            }
        }

        $data['arrChargeableComponents'] = $this->getAccountChargeableComponents();
        $data['intNoticePeriod'] = $this->getNoticePeriod();

        return $data;
    }

    /**
     * Inclusion of legacy files
     *
     * @return void
     */
    protected function includeLegacyFiles()
    {
        require_once '/local/data/mis/database/database_libraries/product-access.inc';
        require_once '/local/data/mis/database/database_libraries/tickets-access.inc';
        require_once '/local/data/mis/database/database_libraries/components/CProductComponent.inc' ;
        require_once '/local/www/database-admin/include/misc.inc';
        require_once '/local/data/mis/database/database_libraries/userdata-access.inc';
        require_once '/local/data/mis/database/database_libraries/financial-access.inc';
        require_once '/local/data/mis/database/class_libraries/C_Refunds_Manager.php';
        require_once '/local/data/mis/database/database_libraries/components/config-cbc-flex-access.inc';
        require_once '/local/data/mis/database/database_libraries/components/config-cbc-payg-access.inc';
        require_once '/local/data/mis/common_library_functions/class_libraries/C_Core_Calc_Math.inc';
        require_once '/local/data/mis/common_library_functions/class_libraries/C_Core_Actions_ActionTemplate.inc';
        require_once '/local/data/mis/common_library_functions/class_libraries/C_Core_Actions_ValidateAction.inc';
        require_once '/local/data/mis/database/database_libraries/adsl-access.inc';
        require_once '/local/data/mis/common_library_functions/common_application_apis/BBCR/C_BBCR_RecordProcessor.php';
        require_once '/local/data/mis/database/application_apis/BBCR/C_BBCR_MigrationOut.php';
        require_once '/local/data/mis/database/application_apis/BBCR/C_Process_Migration.php';
        require_once '/local/data/mis/database/database_libraries/libs/arrays.inc';
        require_once '/local/data/mis/database/database_libraries/CoreObjects/Financial/CFinancialHelper.inc';
        require_once '/local/data/mis/database/database_libraries/phplib-access.inc';
        require_once '/local/data/mis/database/class_libraries/CancellationCeaseType.class.php';
    }
}
