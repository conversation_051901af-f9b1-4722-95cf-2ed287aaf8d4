<?php


	////////////////////////////////////////////
	// Function:  split_TicketsUpdateMetrics
	// Arguments  intTicketId
	//            strStatus
	//            intTeamid
	//            dtmStatusTime
	// Purpose    Fetch TicketMetrics for a given day
	////////////////////////////////////////////
	function split_TicketsUpdateMetrics($intTicketId, $intPartnerId, $strStatus, $intTeamId, $dtmStatusTime, $intActionerId = null)
	{
		$dbConnection = get_named_connection('common_tickets');
		$strStatus    = strtolower($strStatus);

		// Initialise everything, to make it all nice and clean
		$arrMetric['intTicketMetricId'] = 0;
		$arrMetric['dtmDateRaised']     = '';
		$arrMetric['dtmDateClosed']     = '';
		$arrMetric['dtmDateResponded']  = '';
		$arrMetric['idxTeamId']         = $intTeamId;
		$arrMetric['idxPartnerId']      = PARTNER_ID;
		$arrMetric['idxTicketId']       = $intTicketId;
		$arrMetric['intScriptContacts'] = 0;
		$arrMetric['intWorkplaceContacts'] = 0;
		$arrMetric['intUserContacts'] = 0;

		// Fetch the current metric stored for this ticket, if we can
		$strQuery  = 'SELECT intTicketMetricId, dtmDateRaised, dtmDateClosed, dtmDateResponded, idxPartnerId, intScriptContacts, intWorkplaceContacts, intUserContacts '.
		               'FROM tblTicketMetrics '.
		              'WHERE idxTicketId  = "'.$intTicketId.'" '.
		                'AND idxPartnerId = "'.$intPartnerId.'"';
		$resResult = mysql_query($strQuery, $dbConnection) or report_error(__FILE__, __LINE__, mysql_error($dbConnection));
		$arrResult = mysql_fetch_array($resResult);
		if(!is_array($arrResult)) {//if this is the initial Question add then the results will be empty and we should have an array to pass to array merge
			$arrResult = array();
		}
		$arrMetric = array_merge($arrMetric, $arrResult);

		// Update the dates/times depending on the status we are being told to update

		switch($strStatus)
		{
			case 'closed':
				// If the Question has never been repsonded to before now, then this is the
				// first response
				if($arrMetric['dtmDateResponded'] == '0000-00-00 00:00:00' || $arrMetric['dtmDateResponded'] == '')
				{
					$arrMetric['dtmDateResponded'] = $dtmStatusTime;
				}
				$arrMetric['dtmDateClosed'] = $dtmStatusTime;
			break;

			default:
				if($arrMetric['dtmDateResponded'] == '0000-00-00 00:00:00' && $arrMetric['dtmDateRaised'] != '0000-00-00 00:00:00' && $arrMetric['dtmDateRaised'] != '' && $arrMetric['dtmDateResponded'] =='')
				{
					$arrMetric['dtmDateResponded'] = $dtmStatusTime;
				}
			break;
		}

		// Set the date/time that the Question was raised, if it is not set.
		if($arrMetric['dtmDateRaised'] == '0000-00-00 00:00:00' || $arrMetric['dtmDateRaised'] == '')
		{
			$arrMetric['dtmDateRaised'] = $dtmStatusTime;
		}

		switch( $intActionerId )
		{
			case '0': $arrMetric[ 'intUserContacts' ]++; break;

			case 'zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz':
			case '3ab53331cedca4a1a791c46fde8a1bbc': $arrMetric[ 'intScriptContacts' ]++; break;

			default: $arrMetric[ 'intWorkplaceContacts' ]++; break;
		}
		// Problem 9701
		// Only populate Metrics table if this isn't a contact - i.e. a Question raised as 'Closed'
		if($arrMetric['dtmDateRaised'] != $arrMetric['dtmDateClosed']) {

			// Now 'save' the information back to the table
			if($arrMetric['intTicketMetricId'] > 0)
			{
				$strQuery  = 'UPDATE tblTicketMetrics '.
				                'SET dtmDateRaised    = "'.$arrMetric['dtmDateRaised'].'", '.
			                    'dtmDateClosed        = "'.$arrMetric['dtmDateClosed'].'", '.
			                    'dtmDateResponded     = "'.$arrMetric['dtmDateResponded'].'", '.
			                    'idxTeamId            = "'.$arrMetric['idxTeamId'].'", '.
			                    'idxTicketId          = "'.$arrMetric['idxTicketId'].'", '.
			                    'idxPartnerId         = "'.$arrMetric['idxPartnerId'].'", '.
			                    'intScriptContacts    = '. $arrMetric[ 'intScriptContacts' ] .', ' .
			                    'intWorkplaceContacts = '. $arrMetric[ 'intWorkplaceContacts' ] .',  ' .
			                    'intUserContacts      = '. $arrMetric[ 'intUserContacts' ] .', ' .
			                    'vchDbSrc             = "$Id: TicketsUpdateMetrics.method.php,v 1.1 2006-12-06 08:42:01 bhau Exp $" '.
			              'WHERE intTicketMetricId = "'.$arrMetric['intTicketMetricId'].'"';
			}
			else
			{
				$strQuery  = 'INSERT INTO tblTicketMetrics '.
				             '(dtmDateRaised, dtmDateClosed, dtmDateResponded, idxTeamId, idxTicketId, idxPartnerId, intScriptContacts, intWorkplaceContacts, intUserContacts, vchDbSrc) '.
				             'VALUES '.
				             '("'.$arrMetric['dtmDateRaised'].'", "'.$arrMetric['dtmDateClosed'].'", "'.$arrMetric['dtmDateResponded'].'", "'.$arrMetric['idxTeamId'].'", "'.
				             	  $arrMetric['idxTicketId'].'", "'.
				             	  $arrMetric['idxPartnerId'].'", ' .
				             	  $arrMetric[ 'intScriptContacts' ] . ', ' .
				             	  $arrMetric[ 'intWorkplaceContacts' ] . ', ' .
				             	  $arrMetric[ 'intUserContacts' ] . ', ' .
				             	  '"$Id: TicketsUpdateMetrics.method.php,v 1.1 2006-12-06 08:42:01 bhau Exp $" )';
			}
			$resResult = mysql_query($strQuery, $dbConnection)
		             or report_error(__FILE__, __LINE__, mysql_error($dbConnection));
		}
	}

?>