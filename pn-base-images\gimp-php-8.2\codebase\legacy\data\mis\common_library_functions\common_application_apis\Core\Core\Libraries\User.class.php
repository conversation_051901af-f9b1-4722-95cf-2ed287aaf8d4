<?php
/**
 * User base class
 *
 * @package    Core
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <aku<PERSON><PERSON><PERSON>@plus.net>
 *
 * @copyright  2008 PlusNet
 * @version    CVS: $Id: User.class.php,v 1.5 2008-07-21 11:32:43 ablagnys Exp $
 * @since      File available since 18/02/2007
 */

/**
 * User base class
 *
 * @package    Core
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <akuryl<PERSON>@plus.net>
 * @copyright  2008 PlusNet
 */
class Core_User
{
	/**
	 * @access private
	 * @var Core_UserDao
	 */
	private $objDao = null;

	/**
	 * @access private
	 * @var array Array of Core_Address objects
	 */
	private $arrAddresses = null;

	/**
	 * Default constructor
	 *
	 * When <i>$intUserId</i> provided it initializes
	 * {@link Core_User::$objDao} DAO
	 *
	 * @access public
	 * <AUTHOR> <smare<PERSON>@plus.net>
	 * <AUTHOR> <<EMAIL>>
	 *
	 * @uses Core_UserDao::get()
	 *
	 * @param int $intUserId User ID
	 * @param string $strTransactionName
	 */
	public function __construct($intUserId = null, $strTransactionName = Db_Manager::DEFAULT_TRANSACTION)
	{
		if (isset($intUserId))
		{
			$this->setDao(Core_UserDao::get($intUserId, $strTransactionName));
		}
	}

	/**
	 * Takes care of all calls to setters and getters of DAO object
	 *
	 * @access public
	 *
	 * @uses Core_User::$objDao
	 *
	 * <AUTHOR> Marek" <<EMAIL>>
	 * <AUTHOR> Borodaenko" <<EMAIL>>
	 *
	 * @param string    $strMethod Method name
	 * @param array     $arrParams Params passed to method called
	 *
	 * @throws Exception
	 */
	public function __call($strMethod, $arrParams)
	{
		if(preg_match('/^(get|set)/', $strMethod)) {

			return call_user_func_array(array($this->objDao, $strMethod), $arrParams);
		}

		throw new Exception("Method does not exist: ".get_class($this).'::'.$strMethod);
	}

	/**
	 * Calls write method of DAO object
	 *
	 * @access public
	 *
	 * @uses Core_User::$objDao
	 *
	 * <AUTHOR> Marek" <<EMAIL>>
	 */
	public function write()
	{
		return $this->objDao->write();
	}

	/**
	 * Sets {@link Core_User::$objDao} DAO
	 *
	 * @access public
	 *
	 * <AUTHOR> Marek" <<EMAIL>>
	 *
	 * @param Core_IUserDao $objUserDao
	 */
	public function setDao(Core_IUserDao $objUserDao)
	{
		$this->objDao = $objUserDao;
	}

	/**
	 * Retrieves data about addresses associated with a user
	 *
	 * @access public
	 * <AUTHOR> Marek" <<EMAIL>>
	 *
	 * @uses Core_User::$arrAddresses
	 * @uses Core_User::getAddressesForUserId()
	 */
	public function fetchAddresses()
	{

		$this->arrAddresses = self::getAddressesForUserId($this->getUserId(), $this->objDao->getTransaction());
	}

	public function addAddress(array $arrAddressDetails, $strTransactionName = null) {

		$strTransactionName = (empty($strTransactionName)) ? $this->getTransaction() : $strTransactionName;

		$objAdaptor = Db_Manager::getAdaptor('Core', $strTransactionName, true);

		$objAddressDao = new Core_AddressDao($strTransactionName, true);
		$objAddressDao->setVars($arrAddressDetails);
		$objAddressDao->write();

		$objAdaptor->deleteUserAddressLinks(Core_Address::TYPE_DELIVERY, $this->getUserId());

		$objAdaptor->assignTypeToAddress($objAddressDao->getAddressId(), Core_Address::TYPE_DELIVERY);

		$objAdaptor->assignAddressToUser($this->getUserId(), $objAddressDao->getAddressId());

		return $objAddressDao->getAddressId();
	}

	/**
	 * Returns {@link Core_Address} object of given address type
	 *
	 * @access public
	 * <AUTHOR> Marek" <<EMAIL>>
	 *
	 * @uses Core_User::$arrAddresses
	 * @uses Core_UserDao::getTypeHandle()
	 *
	 * @param string $strType Address type
	 *
	 * @return Core_Address if address of given type was found
	 *         FALSE if address of given type was not found
	 */
	public function getAddressByType($strType)
	{
		foreach ($this->arrAddresses as $objAddress) {

			if ($objAddress->getTypeHandle() == $strType) {

				return $objAddress;
			}
		}

		return false;
	}

	/**
	 * Retrives data about associated addresses for a given user ID
	 *
	 * @access public
	 * <AUTHOR> Marek" <<EMAIL>>
	 * @static
	 *
	 * @uses Core_AddressDao::getAddresess()
	 * @see Core_Address
	 *
	 * @param int $intUserId User ID
	 * @param string $strTransactionName
	 *
	 * @return array Array of Core_Address objects
	 */
	public static function getAddressesForUserId($intUserId, $strTransactionName = Db_Manager::DEFAULT_TRANSACTION) {

		$arrAddresses = array();
		$arrAddressDaos = Core_AddressDao::getAddresess($intUserId, $strTransactionName);

		foreach ($arrAddressDaos as $objAddressDao) {

			$objAddress = new Core_Address(null, $strTransactionName, true);
			$objAddress->setDao($objAddressDao);

			$arrAddresses[] = $objAddress;
		}

		return $arrAddresses;
	}

	/**
	 * Retrives user details for given service ID
	 *
	 * @access public
	 * <AUTHOR> Marek" <<EMAIL>>
	 * @static
	 *
	 * @uses Core_AddressDao::getAddresess()
	 * @see getUserDetailsByServiceId
	 * @see Core_UserDao
	 *
	 * @param int $intServiceId Service ID
	 * @param string $strTransactionName
	 *
	 * @return Core_User
	 */
	public static function getUserByServiceId($intServiceId, $strTransactionName = Db_Manager::DEFAULT_TRANSACTION) {

		$objAdaptor = Db_Manager::getAdaptor('Core', $strTransactionName);

		$arrDetails = $objAdaptor->getUserDetailsByServiceId($intServiceId);

		$objUserDao = new Core_UserDao($strTransactionName, false);

		if (is_array($arrDetails) && !empty($arrDetails)) {

			$objUserDao->setVars($arrDetails);
		}

		$objUser = new Core_User();
		$objUser->setDao($objUserDao);

		return $objUser;
	}
}
