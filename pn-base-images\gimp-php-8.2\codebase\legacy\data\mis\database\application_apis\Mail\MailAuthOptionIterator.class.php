<?php

require_once '/local/data/mis/database/application_apis/Mail/MailAuthOptionIteratorException.class.php';

class Mail_MailAuthOptionIterator
{
    private $service;
    private $mailAuth;
    private $mailDelivery;
    private $optionConfigInterface;

    public function __construct(Mail_Service $service, Mail_MailAuth $mailAuth, Mail_MailDelivery $mailDelivery, Mail_IMailAuthOptionConfig $optionConfigInterface)
    {
        $this->service = $service;
        $this->mailAuth = $mailAuth;
        $this->mailDelivery = $mailDelivery;

        $this->optionConfigInterface = $optionConfigInterface;
    }

    /**
     * @throws Mail_MailAuthOptionIteratorException
     * @return void
     */
    public function configure()
    {
        $arrMailInfo = $this->mailDelivery->getAdditionalMailConfigsForService($this->service);
        $arrMailInfo = array_merge($arrMailInfo, $this->mailDelivery->getMasterMailConfigsForService($this->service));

        $arrFailures = array();

        foreach ($arrMailInfo as $mailConfig) {
            $alias = $mailConfig['LocalPart'];

            $authId = $this->mailAuth->getUserId($this->service, $this->service->getUsername(), new String($alias));

            if (!$authId) {
                // failed to find alias, thus log it in the exception
                $arrFailures[] = $alias;
            } else {
                $this->optionConfigInterface->configure($this->service, $this->mailAuth, $this->mailDelivery, $authId);
            }
        }


        if (!empty($arrFailures)) {
            throw new Mail_MailAuthOptionIteratorException("Failed to find mail user for alias(es) '" . implode(', ', $arrFailures) . "', service ID '{$this->service->getServiceId()}'");
        }
    }
}
