<?php

	/////////////////////////////////////////////////////////////////////
	// File:     config-cgi-access.inc
	// Purpose:  Access mini-library for CGI
	/////////////////////////////////////////////////////////////////////

	/////////////////////////////////////////////////////////////////////
	// Functions
	//
	// Write functions
	// ---------------
	//
	// config_cgi_add
	//
	// Read functions
	// --------------
	//
	// config_cgi_get
	//
	// Delete functions
	// ----------------
	//
	// config_cgi_delete
	//
	/////////////////////////////////////////////////////////////////////


	/////////////////////////////////////////////////////////////////////
	// Data

	$global_component_configurators["6"]  = "config_cgi_configurator"; // plus.net
	$global_component_configurators["28"] = "config_cgi_configurator"; // force9
	$global_component_configurators["48"] = "config_cgi_configurator"; // searchpro
	$global_component_configurators["83"] = "config_cgi_configurator"; // freeonline
	$global_component_configurators["393"] = "config_cgi_configurator"; // plus.net 
	$global_component_configurators["394"] = "config_cgi_configurator"; // force9
	$global_component_configurators["395"] = "config_cgi_configurator"; // freeonline

	// Data
	/////////////////////////////////////////////////////////////////////
	
	// Hack to insert the component configurator array into PHP5's global scope
	// if it's not already there
	if(!isset($GLOBALS['global_component_configurators']))
	{
		$GLOBALS['global_component_configurators'] = $global_component_configurators;
	}
	else
	{
		foreach($global_component_configurators as $intIndex => $strConfigurator)
		{
			$GLOBALS['global_component_configurators'][$intIndex] = $strConfigurator;
		}
	}

	/////////////////////////////////////////////////////////////////////
	// Library functions

	/////////////////////////////////////////////////////////////
	// Function:  config_cgi_add
	// Purpose:   Create a cgi configuration for a
	//            component
	// Arguments: $component_id (id of corresponding component)
	// Returns:
	/////////////////////////////////////////////////////////////

	function config_cgi_add ($component_id) {

		global $global_db_src;
                $connection = get_named_connection ("userdata");

		// FIXME

	}


	/////////////////////////////////////////////////////////////
	// Function:  config_cgi_get
	// Purpose:   Get a component and portal configuration
	//            record
	// Arguments: $component_id (id of component)
	// Returns:
	/////////////////////////////////////////////////////////////

	function config_cgi_get ($component_id) {

		global $global_db_src;
                $connection = get_named_connection ("userdata");

		$component_id = addslashes ($component_id);

		// FIXME

	}

	/////////////////////////////////////////////////////////////
	// Function:  config_cgi_delete
	// Purpose:   Delete a webstats configuration
	// Arguments:
	/////////////////////////////////////////////////////////////

	function config_cgi_delete () {

                $connection = get_named_connection ("userdata");

		// FIXME

	}


	/////////////////////////////////////////////////////////////
	// Function:  config_cgi_auto_configure
	// Purpose:   'unconfigured' -> 'queued-activate' state
	//            transition handler for auto-configuration
	// Arguments: $component_id
	/////////////////////////////////////////////////////////////

	function config_cgi_auto_configure ($component_id) {

		// Do nothing - user must ask for a cgi to be activated

	}


	/////////////////////////////////////////////////////////////
	// Function:  config_cgi_auto_refresh
	// Purpose:
	// Arguments:
	/////////////////////////////////////////////////////////////

	function config_cgi_auto_refresh ($component_id) {

		$component = userdata_component_get ($component_id);

		switch ($component["status"]) {
		    case "active":
			// Queue it for backend refresh
			userdata_component_set_status ($component_id, "queued-reactivate");
			break;

		    default:
			// None of our beeswax
			break;
		}

	}

	/////////////////////////////////////////////////////////////
	// Function:  config_cgi_auto_disable
	// Purpose:
	// Arguments:
	/////////////////////////////////////////////////////////////

	function config_cgi_auto_disable ($component_id) {

		$component = userdata_component_get ($component_id);

		switch ($component["status"]) {
		    case "queued-activate":
		    case "queued-reactivate":
		    case "active":
			// Queue it for backend disable
			userdata_component_set_status ($component_id, "queued-deactivate");
			break;

		    default:
			// None of our beeswax
			break;
		}

	}


	/////////////////////////////////////////////////////////////
	// Function:  config_cgi_auto_enable
	// Purpose:
	// Arguments:
	/////////////////////////////////////////////////////////////

	function config_cgi_auto_enable ($component_id) {

		$component = userdata_component_get ($component_id);

		switch ($component["status"]) {
		    case "queued-deactivate":
		    case "deactive":
			// Queue it for reenable
			userdata_component_set_status ($component_id, "queued-reactivate");
			break;

		    default:
			// None of our beeswax
			break;
		}

	}


	/////////////////////////////////////////////////////////////
	// Function:  config_cgi_auto_destroy
	// Purpose:   * -> 'queued-destroy' state
	//            transition handler for auto-destruction
	// Arguments: $component_id
	/////////////////////////////////////////////////////////////

	function config_cgi_auto_destroy ($component_id)
	{
		userdata_component_set_status ($component_id, "destroyed");
	}


	function config_cgi_configurator ($component_id, $action) {

		switch ($action) {

		    case "auto_configure":
			config_cgi_auto_configure ($component_id);
			break;

		    case "auto_refresh":
			config_cgi_auto_refresh ($component_id);
			break;

		    case "auto_disable":
			config_cgi_auto_disable ($component_id);
			break;

		    case "auto_enable":
			config_cgi_auto_enable ($component_id);
			break;

		    case "auto_destroy":
			config_cgi_auto_destroy ($component_id);
			break;

		    default:
			break;

		}

	}

	// Library functions
	/////////////////////////////////////////////////////////////////////


?>
