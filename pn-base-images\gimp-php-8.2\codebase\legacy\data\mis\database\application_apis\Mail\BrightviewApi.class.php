<?php

require_once 'Service.class.php';
require_once 'MailAuth.class.php';
require_once 'MailDelivery.class.php';
require_once 'MailAuthOptionIterator.class.php';
require_once 'MailAuthOptionAutoConfigurePaidForFlag.class.php';
require_once 'MailAuthOptionSetFlags.class.php';

/**
 * Brightview Mail API
 *
 * Because Mail_MailApi is currently too Plusnet specific.
 */
class Mail_BrightviewApi
{
	private $service;
	private $mailAuth;
	private $mailDelivery;

	/**
	 * Factory to create it from a service array, eg from userdata.services
	 */
	public static function fromServiceArray(array $service)
	{
		return new self(Mail_Service::fromServiceArray($service),
		                Mail_MailAuth::get(),
		                Mail_MailDelivery::get());
	}

	public function __construct(Mail_Service $service,
	                            Mail_MailAuth $mailAuth,
	                            Mail_MailDelivery $mailDelivery)
	{
		$this->service = $service;
		$this->mailAuth = $mailAuth;
		$this->mailDelivery = $mailDelivery;
	}
	
	public function getAdditionalMailConfigsForService()
	{
		return $this->mailDelivery->getAdditionalMailConfigsForService($this->service);
	}
	
	public function getMasterMailConfigsForService()
	{
		return $this->mailDelivery->getMasterMailConfigsForService($this->service);
	}

	public function createMasterMailbox(String $realm, String $cryptPassword, Int $quotaBytes, String $soulstoneUsername)
	{

		if(!$this->mailAuth->getUserId($this->service, $this->service->getUsername(), new String('bv.master'))) {
			$this->mailAuth->addUser($this->service, $this->service->getUsername(), $cryptPassword);
		}

		$virtualDomainId = $this->createVirtualDomain(new String($this->service->getUsername() . '.' . $realm));
		if(!$this->mailDelivery->getMailboxId($virtualDomainId, new String('bv.master'))) {
			$this->mailDelivery->addMailbox($this->service, new String('catchall'), $virtualDomainId,
			                                new String('bv.master'), new Bool(TRUE), $quotaBytes, $soulstoneUsername);
		}
	}

	public function createAdditionalMailBox(String $alias, String $domain, String $cryptPassword, Int $quotaBytes, String $soulstoneUsername)
	{
		// Bodge to check whether the vISP is actually recognised.. we don't care at this
		// stage whether it's fully migrated or not - we're just interested in if any
		// "we don't recognise this vISP" exception is thrown.

		$this->mailDelivery->isVispFullyMigrated($this->service);

		$username = new String($this->service->getUsername() . "+$alias");
		$this->mailAuth->addUser($this->service, $username, $cryptPassword, $soulstoneUsername);

		$virtualDomainId = $this->createVirtualDomain($domain);
		$this->mailDelivery->addMailbox($this->service, new String('mailbox'),
		                                $virtualDomainId, $alias,
		                                new Bool(FALSE), $quotaBytes, $soulstoneUsername);
	}

	public function createForward(String $alias, String $domain, String $destination)
	{
		$mailboxId = $this->getMailboxId($alias, $domain);
		$this->mailDelivery->addRedirect($mailboxId, $destination);
	}
	
	public function remapForward(String $alias, String $domain, String $destination)
	{
		$mailboxId = $this->getMailboxId($alias, $domain);
		$this->mailDelivery->deleteRedirect($mailboxId);
		$this->mailDelivery->addRedirect($mailboxId, $destination);
	}

	public function setMailboxPassword(String $alias, String $cryptPassword, array $domains)
	{
		$this->mailAuth->setPassword($this->service, $alias, $cryptPassword);
	}

	public function setMasterAsCatchAll(String $domain)
	{
		$virtualDomainId = $this->getVirtualDomainId($domain);
		
		// is master already catchall?
		// yes - do nothing
		$mailboxid = $this->mailDelivery->getMailboxId($virtualDomainId, new String('bv.master'));

		if((is_object($mailboxid) && $mailboxid->getValue() < 1) || $mailboxid === FALSE) {

			// no - make it catchall		
			$this->mailDelivery->setCatchallToMailboxForDomain($this->service, $virtualDomainId);

			$this->mailDelivery->addMailbox($this->service, new String('catchall'),
				                        $virtualDomainId, new String('bv.master'), new Bool(TRUE), new Int(100), new String($this->service->getUsername()));
		} 
	}

	public function setCatchAll(String $domain, String $alias)
	{
		$virtualDomainId = $this->getVirtualDomainId($domain);

		// remove any other catchalls for this domain if its a bv.master
		$this->mailDelivery->deleteMasterCatchAllForDomain($this->service, $virtualDomainId);

		$this->mailDelivery->remapCatchall($this->service, $virtualDomainId, $alias);
	}

	public function disableCatchAll(String $domain)
	{
		$virtualDomainId = $this->getVirtualDomainId($domain);
		$this->mailDelivery->deleteMasterCatchAllForDomain($this->service, $virtualDomainId);
		$this->mailDelivery->setCatchallToMailboxForDomain($this->service, $virtualDomainId);
		
	}

	public function removeForward(String $alias, String $domain)
	{
		$mailboxId = $this->getMailboxId($alias, $domain);
		$this->mailDelivery->deleteRedirect($mailboxId);
	}

	public function deleteMailbox(String $alias, String $domain)
	{
		$mailboxId = $this->getMailboxId($alias, $domain);
		$data = $this->mailDelivery->getMailboxDetails($mailboxId);

		$address = new String("{$alias}@{$domain}");
		$authId = $this->mailAuth->getUserId($this->service, $this->service->getUsername(), $alias);
		if(!$authId) {
			throw new Exception("Failed to find mail user '$address' in database");
		}

		$this->mailDelivery->deleteMailbox($mailboxId);
		$this->mailAuth->destroyUser($this->service, $authId);

		if($data['MailboxTypeID'] == 1) {
			$this->setMasterAsCatchAll($domain);
		}
	}
	
	public function deleteVirtualDomainAndMailboxes(String $domain) 
	{
		// if you try and destroy a domain that doesn't exist in tblVirtualDomains this will throw an exception so catch it and stop 
		try {
			$virtualDomainId = $this->getVirtualDomainId($domain);
			$additionalMailboxes = $this->getAdditionalMailConfigsForService();
			$masterMailboxes = $this->getMasterMailConfigsForService();

			foreach($additionalMailboxes as $additionalMailbox) {

				if($additionalMailbox['Domain'] == $domain->getValue()) {

					//remove the auth entry and the mailbox entry
					$this->deleteMailbox(new String($additionalMailbox['LocalPart']), $domain);
				}
			}
			foreach($masterMailboxes as $masterMailbox) {

				if($masterMailbox['Domain'] == $domain->getValue()) {

					// leave the auth entry for the visp domain entry
					$this->mailDelivery->deleteMailbox(new Int($masterMailbox['MailboxID']));
				}
			}
			$this->mailDelivery->deleteVirtualDomain($virtualDomainId);
		} catch(Exception $e) {
			error_log($e->getMessage());
		}
	}

	public function destroy()
	{
		$this->mailDelivery->deleteMailboxesForService($this->service);
		$this->mailAuth->destroyUsersForService($this->service);
	}
	
	public function validateVirtualDomain(String $domain)
	{
		$this->getVirtualDomainId($domain);
	}

	private function createVirtualDomain(String $domain)
	{
		$virtualDomainId = $this->mailDelivery->getVirtualDomainId($domain);
		if(!$virtualDomainId) {
			$virtualDomainId = $this->mailDelivery->addVirtualDomain($this->service, $domain);
		}
		return $virtualDomainId;
	}

	private function getVirtualDomainId(String $domain)
	{
		$virtualDomainId = $this->mailDelivery->getVirtualDomainId($domain);
		if(!$virtualDomainId) {

			$maafFreeDomainName = MAAF_Component_FreeDomainName::getComponentByServiceId($this->service->getServiceId());
			if($maafFreeDomainName instanceof MAAF_Component_FreeDomainName) {

				$virtualDomainId = $this->createVirtualDomain(new String($maafFreeDomainName->getDomainName()));
				if($virtualDomainId) {

					return $virtualDomainId;
				}
			}
			throw new Exception("Failed to find virtual domain '$domain' in database");
		}

		return $virtualDomainId;
	}

	private function getMailboxId(String $alias, String $domain)
	{
		$virtualDomainId = $this->getVirtualDomainId($domain);

		$mailboxId = $this->mailDelivery->getMailboxId($virtualDomainId, $alias);
		if(!$mailboxId) {
			throw new Exception("Failed to find mailbox '{$alias}@{$domain}' in database");
		}

		return $mailboxId;
	}



	/**
	 * Configure the intPaidFor flag in mail database.
	 * It will set it to either an enabled/disabled state based on whether
	 * the main account is paid for, or not.
	 *
	 * @access public
	 **/

	public function autoConfigurePaidForFlag(CComponent $componentDetails)
	{
		$mailOptionConfigIterator = new Mail_MailAuthOptionIterator(
			$this->service,
			$this->mailAuth,
			$this->mailDelivery,
			new Mail_MailAuthOptionAutoConfigurePaidForFlag($this->service, $componentDetails)
		);

		$mailOptionConfigIterator->configure();
	}



	public function setOptionFlags($intOptionBits)
	{
		$mailOptionConfigIterator = new Mail_MailAuthOptionIterator(
			$this->service,
			$this->mailAuth,
			$this->mailDelivery,
			new Mail_MailAuthOptionSetFlags($intOptionBits)
		);

		$mailOptionConfigIterator->configure();
	}

}
