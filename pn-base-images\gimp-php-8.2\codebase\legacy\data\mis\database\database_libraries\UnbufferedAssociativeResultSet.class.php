<?php
/**
 * phpcs DOCBLOCK header
 *
 * @package LegacyCodebase
 * <AUTHOR> <<EMAIL>>
 */

/**
 * UnbufferedAssociativeResultSet
 *
 * Allows for unified access to a database result set
 * Example:
 *     $resHugeOfData = PrimitivesQueryOrExit($strQuery, $dbConn);
 *     foreach (UnbufferedAssociativeResultSet::factory($resHugeOfData) as $arrRecord) {
 *             print_r($arrRecord);
 *     }
 *
 * @package LegacyCodebase
 * <AUTHOR> <kp<PERSON><PERSON>bysz<PERSON><EMAIL>>
 */
class UnbufferedAssociativeResultSet implements ArrayAccess, Iterator, Countable
{
    // Wrapping a Mysql resultset in an Iterator class is slightly tricky, as the mysql_fetch() methods
    // automatically move their internal row-pointer forward after each request.  I.e. we need to ensure
    // that our internal "pointer" is consistent with the resultset pointer
    // There's two ways to manage this:
    //    1) Reseek before every request - however, this adds significant overhead due to the extra DB comms
    //    2) Manually track the differences between the two pointers
    //
    // In the interests of performance, we therefore go for option 2)
    //
    // (NOTE: the previous version of this code went for option 1), but didn't handle multiple calls to current()!)

    // The resultset we're iterating over
    private $resResult;

    // The number of items in the resultset
    private $intCount;

    // Where the pointer within the resultset is
    private $intCurrentMysql;

    // Where the pointer for this class is
    private $intCurrentInternal;

    /**
     * Factory method used to create a new instance of the class
     *
     * @param res $resResult a result handle
     *
     * @return UnbufferedAssociativeResultSet
     */
    public static function factory($resResult)
    {
        return new self($resResult);
    }

    /**
     * Internal function to handle seeking within the resultset
     *
     * @param int $intOffset the offset to seek to
     *
     * @return bool
     */
    protected function seekInResultset($intOffset)
    {
        if (!$this->offsetExists($intOffset)) {
            return false;
        }

        if ($intOffset == $this->intCurrentMysql) {
            return true;
        }

        if (!mysql_data_seek($this->resResult, $intOffset)) {
            return false;
        }

        $this->intCurrentMysql = $intOffset;
        return true;
    }

    /**
     * Constructor
     *
     * @param res $resResult a result handle
     *
     * @return undefined
     */
    function __construct($resResult)
    {
        $this->resResult          = $resResult;
        $this->intCurrentInternal = 0;
        $this->intCurrentMysql    = 0;

        $this->setCount();

        // To be safe, we manually seek to the first record, in case something else has already
        // moved the resultset's pointer...
        $this->seekInResultset(0);
    }

    /**
     * Used to see if a given offset is valid for the current resultset
     *
     * @param int $intOffset the requested offset
     *
     * @return bool
     */
    function offsetExists($intOffset)
    {
        if ($this->intCount == 0) {
            // If the resultset is empty, all offsets (including 0!) are invalid
            return false;
        }

        if ($intOffset < 0) {
            return false;
        }

        return ($intOffset < $this->intCount);
    }

    /**
     * Gets the record for the given offset
     *
     * @param int $intOffset the requested position
     *
     * @return mix
     */
    function offsetGet($intOffset)
    {
        // NOTE: the ArrayAccess interface doesn't define what should happen if the offset
        // is invalid (presumably, the expectation is that the caller will always call offsetExists() first)
        //
        // I.e. returning boolean false doesn't really mean anything...
        if (!$this->offsetExists($intOffset)) {
            return false;
        }

        if (!$this->seekInResultset($intOffset)) {
            return false;
        }

        // NOTE: mysql_fetch_assoc() automatically moves the internal resultset pointer to the next record
        $result = mysql_fetch_assoc($this->resResult);
        if ($result === false) {
            return false;
        }

        $this->intCurrentMysql++;
        return $result;
    }

    /**
     * Returns the record at the current offset
     *
     * @return array
     */
    function current()
    {
        return $this->offsetGet($this->intCurrentInternal);
    }

    /**
     * Returns the next offset; also increments the internal counter
     *
     * @return undefined
     */
    function next()
    {
        $this->intCurrentInternal++;
    }

    /**
     * Resets to the initial offset (i.e. 0)
     *
     * @return undefined
     */
    function rewind()
    {
        $this->intCurrentInternal = 0;
    }

    /**
     * Returns a count of how many items are in the resultset
     *
     * @return int
     */
    function count()
    {
        return $this->intCount;
    }

    /**
     * Sets the count of how many items are in the resultset
     *
     * @return undefined
     */
    private function setCount()
    {
        if (null !== $this->intCount) {
            return;
        }

        $this->intCount = mysql_num_rows($this->resResult);
    }

    /**
     * Returns the current offset
     *
     * @return int
     */
    function key()
    {
        return $this->intCurrentInternal;
    }

    /**
      * Indicates if the current offset is valid
     *
     * @return bool
     */
    function valid()
    {
        return $this->offsetExists($this->intCurrentInternal);
    }

    /**
     * Returns a pointer to the currently instantiated class
     *
     * @return UnbufferedAssociativeResultSet
     */
    function getIterator()
    {
        return $this;
    }

    /**
     * Interface method; throws an exception if used
     *
     * @param int $offset the offset to overwrite
     * @param mix $value  the value to set
     *
     * @return undefined
     */
    function offsetSet($offset, $value)
    {
        throw new Exception("This collection is read only.");
    }

    /**
     * Interface method; throws an exception if used
     *
     * @param int $offset the offset to overwrite
     *
     * @return undefined
     */
    function offsetUnset($offset)
    {
        throw new Exception("This collection is read only.");
    }

    /**
     * Interface method; throws an exception if used
     *
     * @param mix $value the value to append
     *
     * @return undefined
     */
    function append($value)
    {
        throw new Exception("This collection is read only");
    }
}
