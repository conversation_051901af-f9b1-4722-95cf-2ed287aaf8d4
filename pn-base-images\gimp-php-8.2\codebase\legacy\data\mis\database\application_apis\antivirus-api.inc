<?php

	// Anti Virus Access library
	//
	// This library requires:
	//      groupware-access.inc
	//      userdata-access.inc
	//      component-defines.inc
	//      config-email-access.inc



	function get_antivirus_status($group_member_id)
	{
		$status = 'inactive';

		$connection = get_named_connection('groupware');

		$query = 'SELECT status
		            FROM antivirus_status
		           WHERE group_member_id = "'.addslashes($group_member_id).'"';

		$result = mysql_query($query, $connection)
		          or report_error(__FILE__, __LINE__, mysql_error($connection));

		if(mysql_num_rows($result))
		{
			$data = mysql_fetch_array($result, MYSQL_ASSOC);
			$status = $data['status'];
		}
 
		return $status;
	}


	function update_antivirus_status($service_id, $group_member_id, $new_status, $strAccountType='', $strSource='Script')
	{
		global $brand, $my_id;

		//Sanity check the source
		switch($strSource)
		{
			case 'Script':
				$strUserId = SCRIPT_USER;				
				break;
			case 'Internal':
				$strUserId = $my_id;
				break;
			case 'Portal':
			default:
				$strUserId = '0';
				break;
		}

		$service_data = userdata_service_get($service_id);
		$username     = $service_data['username'];

		$return_text  = 'An error has occurred whilst trying to process your request. Please try again in a few moments.';
 
		$connection   = get_named_connection('groupware');

		$query = 'SELECT antivirus_status_id
		            FROM antivirus_status
		           WHERE group_member_id = "'.addslashes($group_member_id).'"';

		$result = mysql_query($query, $connection)
		          or report_error(__FILE__, __LINE__, mysql_error($connection));
 
		if(mysql_num_rows($result) > 0)
		{
			$query = 'UPDATE antivirus_status
			             SET status = "'.addslashes($new_status).'"
			           WHERE group_member_id = "'.addslashes($group_member_id).'"';
		}
		else
		{
			$query = 'INSERT INTO antivirus_status
			                      (status, group_member_id, db_src)
			               VALUES ("'.addslashes($new_status).'", "'.addslashes($group_member_id).'", "$Id: antivirus-api.inc,v 1.9 2007-01-16 16:09:58 msimons Exp $")';
		}

		$result = mysql_query($query, $connection)
		          or report_error(__FILE__, __LINE__, mysql_error($connection));

		switch($new_status)
		{
			case 'active':
				if(mysql_affected_rows($connection))
				{
					// Added check for easystart, add different ticket if true
					if ($strAccountType != 'easystart' && $strAccountType != 'broadbandhomelite')
					{
						$return_text = 'Your anti-virus protection will be enabled within the next 24 hours';
						$ticket_text = 'Services activation - Antivirus

Username: '.$username.'

Online Antivirus protection has been activated.';
					}
					elseif($strAccountType == 'easystart')
					{
						$return_text = 'Your anti-virus protection will be enabled within the next 24 hours';
						$ticket_text = 'Customer has been sent information about Services activation - Spam & Antivirus

Username: '.$username.'

As well as getting your hardware for free, two additional services have been provided at no extra charge. Virus Protection and Spam filtering will have been enabled on your '.$brand.' mail account. As a result, you may receive emails warning you when an infected file have been detected and blocked. You will also receive emails that have had the word [-SPAM-] added to their subject line. This is because the filter checks for patterns that are common amongst unsolicited emails, when an email contains these patterns then the Spam label is added. The tool is activated on the middle setting. You can use the portal tool to alter how strictly the filter works, making it more or less likely to catch unsolicited emails. It is important to understand that the higher the filter setting, the more likely that legitimate emails may be mistakenly identified as Spam.

If you have extra mailboxes or domains hosted on your '.$brand.' account, they will automatically benefit from these services as well.';
					}
					elseif($strAccountType == 'broadbandhomelite')
					{
						$return_text = 'Your anti-virus protection will be enabled within the next 24 hours';
                                                $ticket_text = 'Services activation - Spam & Antivirus

Username: '.$username.'

Your broadband package includes two additional services at no extra charge. Virus Protection and Spam filtering will have been enabled on your '.$brand.' mail account. As a result, you may receive emails warning you when an infected file have been detected and blocked. You will also receive emails that have had the word [-SPAM-] added to their subject line. This is because the filter checks for patterns that are common amongst unsolicited emails, when an email contains these patterns then the Spam label is added. The tool is activated on the middle setting. You can use the portal tool to alter how strictly the filter works, making it more or less likely to catch unsolicited emails. It is important to understand that the higher the filter setting, the more likely that legitimate emails may be mistakenly identified as Spam.

If you have extra mailboxes or domains hosted on your '.$brand.' account, they will automatically benefit from these services as well.';
					}				

				}
	
			break;

			case 'inactive':
				if(mysql_affected_rows($connection))
				{
					$return_text = 'Your anti-virus protection will be disabled within the next 24 hours';
					$ticket_text = 'Services deactivation - Antivirus

Username: '.$username.'

Online Antivirus protection has been deactivated.';
				}
			break;
		}

		if(isset($ticket_text) && trim($ticket_text) != '')
		{
				$ticket_id = tickets_ticket_add($strSource, $service_id, 0, 0, 'Closed', $strUserId, $ticket_text, 0, 1);
		}

		// refresh all active email components
		// - don't need to do queued-activate or queued-reactivate because any changes will be picked up when they are done anyway
		$arrComponents = userdata_component_find(array('service_id' => $service_id,
		                                               'status'     => array('active'),
		                                               'type'       => array(COMPONENT_PLUS_EMAIL, COMPONENT_F9_EMAIL, COMPONENT_FOL_EMAIL)));

		foreach(array_keys($arrComponents) as $intKey)
		{
			config_email_configurator($arrComponents[$intKey]['component_id'], 'auto_refresh');
		}

		return $return_text;
	}
?>
