<?php

require_once '/local/data/mis/database/application_apis/Mail/IMailAuthOptionConfig.interface.php';
require_once '/local/data/mis/database/class_libraries/Brightview/BrightViewPasswd.class.php';


class Mail_MailAuthOptionSetFlags implements Mail_IMailAuthOptionConfig
{
	private $intFlags;


	public function __construct($intOptionBits)
	{
		$this->intFlags = $intOptionBits;
	}



	public function configure(Mail_Service $service, Mail_MailAuth $mailAuth, Mail_MailDelivery $mailDelivery, Int $authId)
	{
		$mailAuth->setPop3State($service, $authId, new Bool($this->intFlags & BrightViewPasswd::OPT_POP3));
		$mailAuth->setWebmailState($service, $authId, new Bool($this->intFlags & BrightViewPasswd::OPT_WEBMAIL));
		$mailAuth->setImapState($service, $authId, new Bool($this->intFlags & BrightViewPasswd::OPT_WEBMAIL));
	}
}

