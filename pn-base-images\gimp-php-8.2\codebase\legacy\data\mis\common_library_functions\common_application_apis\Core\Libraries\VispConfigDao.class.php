<?php
	/**
	 * 
	 */
	class Core_VispConfigDao extends Db_Object
	{
		/**
		 * @access protected
		 * @var string Primary key name
		 */
		protected $strPrimaryKey = 'isp_definition_id';

		protected $intIspDefinitionId = null;

		protected $strIsp = null;

		protected $intOwningCompanyId = null;

		protected $intOwnerContactId = null;

		protected $strFullName = null;

		protected $strShortName = null;

		protected $strShortBrand = null;

		/**
		 * Enter description here...
		 *
		 * @var unknown_type
		 */
		protected $arrSetterFunctions = array('intIspDefinitionId' => 'IspDefinitionId',
		                                      'strIsp'             => 'Isp',
		                                      'intOwningCompanyId' => 'OwningCompanyId', 
		                                      'intOwnerContactId'  => 'OwnerContactId',
		                                      'strFullName'        => 'FullName',
		                                      'strShortName'       => 'ShortName',
		                                      'strShortBrand'      => 'ShortBrand');

		/**
		 * Enter description here...
		 *
		 */
		public function init() {}

		/**
		 * 
		 */
		public static function get($strIsp, $strTransaction = Db_Manager::DEFAULT_TRANSACTION)
		{
			$strDebugSource = __CLASS__ . ' ' . __METHOD__;
			Dbg_Dbg::write("$strDebugSource: Fetching visp config details for ISP $strIsp", 'Core');

			return parent::getObject(get_class(), $strIsp, $strTransaction);
		}

		/**
		 * 
		 */
		public function getIspDefinitionId()
		{
			return $this->intIspDefinitionId;
		}

		/**
		 * 
		 */
		public function getIsp()
		{
			return $this->strIsp;
		}

		/**
		 * 
		 */
		public function getOwningCompanyId()
		{
			return $this->intOwningCompanyId;
		}

		/**
		 * 
		 */
		public function getOwnerContactId()
		{
			return $this->intOwnerContactId;
		}

		/**
		 * 
		 */
		public function getFullName()
		{
			return $this->strFullName;
		}

		/**
		 * 
		 */
		public function getShortName()
		{
			return $this->strShortName;
		}

		/**
		 * 
		 */
		public function getShortBrand()
		{
			return $this->strShortBrand;
		}

		/**
		 * 
		 */
		public function setIspDefinitionId($intIspDefinitionId)
		{
			$this->intIspDefinitionId = $intIspDefinitionId;
		}

		/**
		 * 
		 */
		public function setIsp($strIsp)
		{
			$this->strIsp = $strIsp;
		}

		/**
		 * 
		 */
		public function setOwningCompanyId($intOwningCompanyId)
		{
			$this->intOwningCompanyId = $intOwningCompanyId;
		}

		/**
		 * 
		 */
		public function setOwnerContactId($intOwnerContactId)
		{
			$this->intOwnerContactId = $intOwnerContactId;
		}

		/**
		 * 
		 */
		public function setFullName($strFullName)
		{
			$this->strFullName = $strFullName;
		}

		/**
		 * 
		 */
		public function setShortName($strShortName)
		{
			$this->strShortName = $strShortName;
		}

		/**
		 * 
		 */
		public function setShortBrand($strShortBrand)
		{
			$this->strShortBrand = $strShortBrand;
		}
	}
