<?php
/**
 * File docblock
 *
 * @package Core
 * <AUTHOR> <<EMAIL>>
 */

/**
 * The base class for all error handling.
 *
 * @package Core
 * <AUTHOR> <<EMAIL>>
 */
class CError
{
    /**
     * The source file raising the error
     *
     * @param string
     */
    private $strSourceFile;

    /**
     * The line number of the error
     *
     * @param integer
     */
    private $intLineNumber;

    /**
     * Records the error message
     *
     * @param string
     */
    private $strErrorMessage;

    /**
     * An exception object - used to generate stack traces, etc
     *
     * @param Exception
     */
    private $objException;

    /**
     * Constructor
     *
     * @param str       $strSourceFile   the file where the error occurred
     * @param int       $intLineNumber   the line in the file where the error occurred
     * @param str       $strErrorMessage the reason for the failure
     * @param Exception $objException    the exception associated with the failure
     *
     * @return undefined
     */
    public function CError($strSourceFile, $intLineNumber, $strErrorMessage, Exception $objException = null)
    {
        $this->setError($strSourceFile, $intLineNumber, $strErrorMessage, $objException);
    }

    /**
     * Bulk set method
     *
     * @param str       $strSourceFile   the file where the error occurred
     * @param int       $intLineNumber   the line in the file where the error occurred
     * @param str       $strErrorMessage the reason for the failure
     * @param Exception $objException    the exception associated with the failure
     *
     * @return undefined
     */
    public function setError($strSourceFile, $intLineNumber, $strErrorMessage, Exception $objException = null)
    {
        $this->strSourceFile   = $strSourceFile;
        $this->intLineNumber   = $intLineNumber;
        $this->strErrorMessage = $strErrorMessage;

        $this->objException    = (!empty($objException) ? $objException : new Exception($strErrorMessage));
    }

    /**
     * Getter method
     *
     * @return str
     */
    public function getSourceFile()
    {
        return $this->strSourceFile;
    }

    /**
     * Getter method
     *
     * @return int
     */
    public function getLineNumber()
    {
        return $this->intLineNumber;
    }

    /**
     * Getter method
     *
     * @return str
     */
    public function getErrorMessage()
    {
        return $this->strErrorMessage;
    }

    /**
     * Getter method
     *
     * @return Exception
     */
    public function getException()
    {
        return $this->objException;
    }
        
    /**
     * Essentially an alias for is_a('CError'): slightly slower but makes much neater code
     *
     * @param mix $objError The variable to test
     *
     * @return bool
     */
    public static function isError($objError)
    {
        return (strtolower(get_class($objError)) == 'cerror' ? true : false);
    }
}
