<?php

require_once '/local/data/mis/database/application_apis/Mail/MailDeliveryException.class.php';

/**
 * Low-level interface to the mail delivery database
 */
class Mail_MailDelivery
{
    private $prim;

    /**
     * Factory method
     *
     * @return Mail_MailDelivery
     */
    public static function get()
    {
        return new self(Lib_SqlPrimitives::singleton());
    }

    /**
     * @param Lib_SqlPrimitives $prim SQL primitives
     */
    public function __construct(Lib_SqlPrimitives $prim)
    {
        $this->prim = $prim;
    }

    /**
     * Determines if a vISP has fully migrated or not.
     * See http://trac.plus.net/BVMailMigration/ticket/6 for details
     *
     * @access public
     * @param $service - Mail_Service object used to determine whether
     *                   its ISP is fully migrated.
     * @throws Mail_MailDeliveryException
     * @return boolean
     **/
    public function isVispFullyMigrated(Mail_Service $service)
    {
        $dbhConn = get_named_connection_with_db('maildelivery');

        $strQuery = "SELECT DISTINCT
                        t.bolComplete
                     FROM tblBrightviewIspMigrationTracker AS t
                     INNER JOIN tblVirtualDomains AS vt
                        ON vt.ISP = t.vchIsp
                     WHERE
                        vt.ServiceID = {$service->getServiceId()}";

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConn, __METHOD__, FALSE);

        if (FALSE == $resResult || $this->prim->PrimitivesNumRowsGet($resResult) != 1) {
            throw new Mail_MailDeliveryException("failed to determine if vISP for service ID {$service->getServiceId()} is fully migrated");
        }


        $intMigrated = $this->prim->PrimitivesResultGet($resResult, 'bolComplete');

        return ($intMigrated == 1);
    }

    public function getVirtualDomainId(String $domain)
    {
        $db = get_named_connection_with_db('maildelivery');
        $query = "SELECT VirtualDomainID FROM dbMailDelivery.tblVirtualDomains
                  WHERE Domain = '" . PrimitivesRealEscapeString($domain, $db) . "'";
        $id =  $this->prim->PrimitivesResultGet(PrimitivesQueryOrExit($query, $db), 'VirtualDomainID');

        return $id ? new Int($id) : FALSE;
    }

    public function getDomain(Int $virtualDomainId)
    {

        $db = get_named_connection_with_db('maildelivery');
        $query = "SELECT Domain FROM dbMailDelivery.tblVirtualDomains
                  WHERE VirtualDomainID = '" . PrimitivesRealEscapeString($virtualDomainId, $db) . "'";
        $domain =  $this->prim->PrimitivesResultGet(PrimitivesQueryOrExit($query, $db), 'Domain');

        return $domain == '' ? FALSE : new String($domain);
    }

    public function getMailboxId(Int $virtualDomainId, String $localPart)
    {
        $db = get_named_connection_with_db('maildelivery');
        $query = "SELECT MailboxID FROM tblMailboxes
                  WHERE VirtualDomainID = $virtualDomainId
                    AND LocalPart = '" . PrimitivesRealEscapeString($localPart, $db) . "'";
        $id =  $this->prim->PrimitivesResultGet(PrimitivesQueryOrExit($query, $db), 'MailboxID');

        return $id ? new Int($id) : FALSE;
    }

    public function getMailboxDetails(Int $mailboxId)
    {
        $db = get_named_connection_with_db('maildelivery');
        $query = "SELECT vd.ServiceID,mb.LocalPart,vd.Domain,vd.Username,vd.ISP,vd.VirtualDomainID,mb.MailboxTypeID
                  FROM tblMailboxes mb
                  INNER JOIN tblVirtualDomains vd ON mb.VirtualDomainID = vd.VirtualDomainID
                  WHERE MailboxID = $mailboxId";
        $data =  $this->prim->PrimitivesResultGet(PrimitivesQueryOrExit($query, $db));

        return is_array($data) ? $data : array();
    }

    public function getAdditionalMailConfigsForService(Mail_Service $service)
    {
        $db = get_named_connection_with_db('maildelivery');
        $query = "SELECT mb.MailboxID, mb.LocalPart,mb.MailboxTypeID,r.Destination,mb.VirtualDomainID, vd.Domain, mb.Username
                  FROM tblMailboxes mb
                  INNER JOIN tblVirtualDomains vd ON mb.VirtualDomainID = vd.VirtualDomainID
                  LEFT JOIN tblRedirects r ON mb.MailboxID = r.MailboxID
                  WHERE vd.ServiceID = {$service->getServiceId()} AND mb.LocalPart != 'bv.master' ";
        $data =  $this->prim->PrimitivesResultsAsArrayGet(PrimitivesQueryOrExit($query, $db));

        return is_array($data) ? $data : array();
    }

    public function getMasterMailConfigsForService(Mail_Service $service)
    {
        $db = get_named_connection_with_db('maildelivery');
        $query = "SELECT mb.MailboxID, mb.LocalPart,mb.MailboxTypeID,r.Destination,mb.VirtualDomainID,vd.Domain, mb.Username
                  FROM tblMailboxes mb
                  INNER JOIN tblVirtualDomains vd ON mb.VirtualDomainID = vd.VirtualDomainID
                  LEFT JOIN tblRedirects r ON mb.MailboxID = r.MailboxID
                  WHERE vd.ServiceID = {$service->getServiceId()} AND mb.LocalPart = 'bv.master' ";
        $data =  $this->prim->PrimitivesResultsAsArrayGet(PrimitivesQueryOrExit($query, $db));

        return is_array($data) ? $data : array();
    }

    public function addVirtualDomain(Mail_Service $service, String $domain)
    {
        $db = get_named_connection_with_db('maildelivery');

        $query = "INSERT INTO dbMailDelivery.tblVirtualDomains SET
                  ServiceID = " . $service->getServiceId() . ",
                  Domain = '" . PrimitivesRealEscapeString($domain, $db) . "',
                  Username = '" . PrimitivesRealEscapeString($service->getUsername(), $db) . "',
                  ISP = '" . PrimitivesRealEscapeString($service->getIsp(), $db)  . "',
                  TLK = '" . PrimitivesRealEscapeString($service->getTlk(), $db) . "',
                  RelayHost = 0";
        PrimitivesQueryOrExit($query, $db);
        $id = $this->prim->PrimitivesInsertIdGet($db);


        // Ideally, isVispFullyMigrated() would have been called earlier on in the
        // Mail_BrightviewApi class to determine if it's going to explode with
        // an exception.

        $intMailFilterTypeId = $this->isVispFullyMigrated($service) ? 2 : 3;

        $query = "INSERT INTO dbMailDelivery.tblMailFilterSettings SET
                  intVirtualdomainId = $id,
                  bolBsb = 0, bolSpam = 1, intAgressiveness = 1, bolDnsProcessed = 3,
                  bolMarkSpam = 0, bolAntiVirus = 1, intMailFilterTypeId = {$intMailFilterTypeId}, bolMoveToInbox = 1, bolPostiniOnlyEmail = 1 ";
        PrimitivesQueryOrExit($query, $db);

        return new Int($id);
    }

    public function addMailbox(Mail_Service $service, String $type, Int $virtualDomainId, String $localPart,
                               Bool $primary, Int $quotaBytes, String $soulstoneUsername)
    {
        $db = get_named_connection_with_db('maildelivery');

        switch($type) {
        case 'catchall':
            $typeId = 1;
            break;
        case 'mailbox':
            $typeId = 2;
            break;
        case 'redirect':
            $typeId = 5;
            break;
        }

        if($localPart != 'bv.master') {

            $maildir = $service->getHomeDir($soulstoneUsername);

        } else {

            $maildir = $service->getHomeDir();
        }

        $query = "INSERT INTO dbMailDelivery.tblMailboxes SET
                  ServiceID = " . $service->getServiceId() . ",
                  MailboxTypeID = $typeId,
                  VirtualDomainID = $virtualDomainId,
                  LocalPart = '" . PrimitivesRealEscapeString($localPart, $db) . "',
                  Username = '" . PrimitivesRealEscapeString($service->getUsername(), $db) . "',
                  ISP = '" . PrimitivesRealEscapeString($service->getIsp(), $db) . "',
                  UID = 'mail',
                  GID = 'mail',
                  Maildir = '" . PrimitivesRealEscapeString($maildir, $db) . "',
                  intMaildirQuota = " . ($quotaBytes->getValue()*1024*1024) ;
        PrimitivesQueryOrExit($query, $db);

        return new Int($this->prim->PrimitivesInsertIdGet($db));
    }

    public function remapCatchall(Mail_Service $service, $virtualDomainId, $alias)
    {
        $db = get_named_connection_with_db('maildelivery');
        $query = "UPDATE tblMailboxes SET MailboxTypeID = 2 WHERE MailboxTypeID = 1 AND VirtualDomainID = $virtualDomainId ";
        PrimitivesQueryOrExit($query, $db);
        $query = "UPDATE tblMailboxes SET MailboxTypeID = 1 WHERE MailboxTypeID = 2 AND VirtualDomainID = $virtualDomainId AND LocalPart = '$alias' ";
        PrimitivesQueryOrExit($query, $db);
    }

    public function addRedirect(Int $mailboxId, String $destination)
    {
        $db = get_named_connection_with_db('maildelivery');
        $query = "INSERT INTO dbMailDelivery.tblRedirects SET
                       MailboxID = $mailboxId,
                   Destination = '" . PrimitivesRealEscapeString($destination, $db) . "'";
        PrimitivesQueryOrExit($query, $db);
        $query = "UPDATE tblMailboxes SET MailboxTypeID = 5 WHERE MailboxTypeID = 2 AND MailboxID = $mailboxId ";
        PrimitivesQueryOrExit($query, $db);

        return new Int($this->prim->PrimitivesInsertIdGet($db));
    }

    public function deleteMasterCatchAllForDomain(Mail_Service $service, $virtualDomainId)
    {
        $db = get_named_connection_with_db('maildelivery');
        $query = "DELETE FROM dbMailDelivery.tblMailboxes
                  WHERE ServiceID = " . $service->getServiceid() . "
                    AND MailboxTypeID = 1
                    AND VirtualDomainID = $virtualDomainId
                    AND LocalPart = 'bv.master' ";
        PrimitivesQueryOrExit($query, $db);

    }

    public function setCatchallToMailboxForDomain(Mail_Service $service, Int $virtualDomainId)
    {
        $db = get_named_connection_with_db('maildelivery');
        $query = "UPDATE tblMailboxes SET MailboxTypeID = 2 WHERE MailboxTypeID = 1 AND VirtualDomainID = $virtualDomainId ";
        PrimitivesQueryOrExit($query, $db);
    }

    public function deleteRedirect(Int $mailboxId)
    {
        $db = get_named_connection_with_db('maildelivery');
        $query = "UPDATE tblMailboxes SET MailboxTypeID = 2 WHERE MailboxTypeID = 5 AND MailboxID = $mailboxId ";
        PrimitivesQueryOrExit($query, $db);
        $query = "DELETE FROM dbMailDelivery.tblRedirects WHERE MailboxID = $mailboxId";
        PrimitivesQueryOrExit($query, $db);
    }

    public function deleteMailbox(Int $mailboxId)
    {
        $this->deleteRedirect($mailboxId);

        $db = get_named_connection_with_db('maildelivery');
        $query = "DELETE FROM dbMailDelivery.tblMailboxes WHERE MailboxID = $mailboxId";
        PrimitivesQueryOrExit($query, $db);
    }

    public function deleteVirtualDomain(Int $virtualDomainId)
    {
        $db = get_named_connection_with_db('maildelivery');

        $query = "DELETE FROM dbMailDelivery.tblMailFilterSettings WHERE intVirtualdomainId = $virtualDomainId";
        PrimitivesQueryOrExit($query, $db);

        $query = "DELETE FROM dbMailDelivery.tblVirtualDomains WHERE VirtualDomainID = $virtualDomainId";
        PrimitivesQueryOrExit($query, $db);
    }

    public function deleteMailboxesForService(Mail_Service $service)
    {
        $db = get_named_connection_with_db('maildelivery');
        $query = "SELECT MailboxID FROM dbMailDelivery.tblMailboxes WHERE ServiceID = " . $service->getServiceId();
        $mailboxes = $this->prim->PrimitivesResultsAsArrayGet(PrimitivesQueryOrExit($query, $db));
        foreach($mailboxes as $mailbox) {
            $this->deleteMailbox(new Int($mailbox['MailboxID']));
        }
        $query = "SELECT VirtualDomainID FROM dbMailDelivery.tblVirtualDomains
                  WHERE ServiceID = " . $service->getServiceId();
        $domains = PrimitivesResultsAsArrayGet(PrimitivesQueryOrExit($query, $db));
        foreach($domains as $domain) {
            $this->deleteVirtualDomain(new Int($domain['VirtualDomainID']));
        }
    }
}
