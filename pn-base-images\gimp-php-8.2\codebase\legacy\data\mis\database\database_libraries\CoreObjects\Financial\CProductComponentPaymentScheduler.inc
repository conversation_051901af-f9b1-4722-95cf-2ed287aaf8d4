<?php
/**
 * Create Product Component Scheduled Payments
 *
 *
 *
 * @package    Core
 * @subpackage Financial
 * @access     public
 * <AUTHOR> <<EMAIL>>
 * @version    $Id: CProductComponentPaymentScheduler.inc,v 1.3 2005-09-28 09:01:39 cblack Exp $
 * @filesource
*/

require_once('/local/data/mis/database/database_libraries/CoreObjects/Financial/CPaymentScheduler.inc');
require_once('/local/data/mis/database/database_libraries/components/CProductComponent.inc');
require_once('/local/data/mis/database/database_libraries/programme-tool-access.inc');

/**
* Product Component Payment Scheduler class
*
* Concrete implementation of the abstact CPaymentScheduler
*
* @access     public
* <AUTHOR> <<EMAIL>>
*/
class CProductComponentPaymentScheduler extends CPaymentScheduler
{

    /**
    * ProductComponentInstance ID
    *
    * @var integer
    * @access private
    */
    var $m_intProductComponentInstanceID = 0;

    /**
     * If we have a contract to link to this should be populated
     *
     * @var int
     */
    var $m_intProductComponentContractID = null;



    ////////////////
    // Constructor
    ////////////////


    /**
    * Contructor for the CProductComponentPaymentScheduler class
    *
    * @param int The ProductComponentInstance ID the charge relates to
    * @return boolean success
    */
    function CProductComponentPaymentScheduler($intProductComponentInstanceID, $strActionerID, $intContractId = null)
    {
        if(!CProductComponent::isValidProductComponentInstanceID($intProductComponentInstanceID)) {
            $this->setError(__FILE__, __LINE__, "Cannot create a scheduled payment entry ".
                            "for non-existent ProductComponentInstance ID '$intProductComponentInstanceID'");

            return false;
        }

        $this->m_intProductComponentInstanceID = $intProductComponentInstanceID;
        $this->m_intProductComponentContractID = $intContractId;
        $this->m_intScheduledPaymentTypeID = CScheduledPayment::getTypeIDByHandle('PRODUCT_COMPONENT');

        //Call parent constructor
        return $this->CPaymentScheduler($strActionerID);

    }

    //////////////
    // Accessors
    //////////////

    /**
    * Set the Product Component Instance ID
    *
    *
    * @access public
    * <AUTHOR>
    * @param  ingeter Product Component Instance ID
    */
    function setProductComponentInstanceID($intProductComponentInstanceID)
    {
        $this->m_intProductComponentInstanceID = $intProductComponentInstanceID;
    }


    ////////////////////
    // Static Methods
    ////////////////////


    ////////////////////////
    // Public Methods
    ////////////////////////


    /**
    * Get all scheduled payments
    *
    * @access public
    * <AUTHOR>
    * @param  boolean Include payments which are not yet due
    * @param  boolean Include payments which have been cancelled
    * @param  boolean Include payments which have been invoiced
    * @param  boolean Exclude payments which are not yet due
    * @return array Scheduled payments
    */
    function getScheduledPayments($bolIncludeNotDue=false, $bolIncludeCancelled=false, $bolIncludeInvoiced=false, $bolExcludeDue=false)
    {
        $dbhConn = get_named_connection_with_db('financial');

        $strQuery = "SELECT sp.intScheduledPaymentID,
                            sp.intScheduledPaymentTypeID,
                            cpc.intProductComponentInstanceID,
                            sp.intAccountID,
                            sp.intSalesInvoiceID,
                            sp.intLineItemID,
                            sp.vchDescription as strDescription,
                            sp.vchActionerID as strActionerID,
                            sp.intAmountExVatPence,
                            sp.intVatPence,
                            UNIX_TIMESTAMP(sp.dteDue) as uxtDateDue,
                            sp.bolOnBilling,
                            UNIX_TIMESTAMP(sp.dteStartPeriodCovered) as uxtStartPeriodCovered,
                            UNIX_TIMESTAMP(sp.dteEndPeriodCovered) as uxtEndPeriodCovered,
                            UNIX_TIMESTAMP(sp.dtmInvoiced) as uxtInvoiced,
                            UNIX_TIMESTAMP(sp.dtmDateExpected) as uxtDateExpected,
                            UNIX_TIMESTAMP(sp.dtmCancelled) as uxtCancelled
                       FROM financial.tblScheduledPayment sp
                 INNER JOIN financial.tblConfigProductComponent cpc
                         ON sp.intScheduledPaymentID = cpc.intScheduledPaymentID
                      WHERE cpc.intProductComponentInstanceID = '{$this->m_intProductComponentInstanceID}'";

        if(!$bolIncludeNotDue) {
            $strQuery .= ' AND sp.dteDue <= NOW()';
        }

        if(!$bolIncludeCancelled) {
            $strQuery .= ' AND sp.dtmCancelled IS NULL';
        }

        if(!$bolIncludeInvoiced) {
            $strQuery .= ' AND sp.dtmInvoiced IS NULL AND sp.intSalesInvoiceID IS NULL';
        }

        if($bolExcludeDue) {
            $strQuery .= ' AND sp.dteDue > NOW()';
        }
        $refResult             = PrimitivesQueryOrExit($strQuery, $dbhConn, 'fetch all scheduled payments');
        $arrScheduledPayments  = PrimitivesResultsAsArrayGet($refResult);

        return $arrScheduledPayments;
    }


    /**
    *
    * Cancel the outstanding scheduled payments.
    * @param $uxtCancelled
    * @param $bolExcludeDue Exclude the payments which not yet due
    *
    * @access public
    * <AUTHOR>
    * @param
    * @return boolean
    */
    function cancelAllOutstandingPayments($uxtCancelled=0, $bolExcludeDue=false)
    {
        $bolIncludeNotDue = true;
        $arrScheduledPayments = $this->getScheduledPayments($bolIncludeNotDue, false, false, $bolExcludeDue);
        $callSuccess = (0 === count($arrScheduledPayments)) ? true : false;

        foreach ($arrScheduledPayments as $arrScheduledPayment) {
            $objScheduledPayment = CScheduledPayment::createInstance($arrScheduledPayment['intScheduledPaymentID']);

            if (CError::isError($objScheduledPayment) || !$objScheduledPayment->cancel()) {
                $errorMsg = (CError::isError($objScheduledPayment)) ? 
                    $objScheduledPayment->getErrorMessage() :
                    'objScheduledPayment->cancel() failed to return true';

                $autoProblemMsg = 'ERROR: Scheduled payment ID ' . $arrScheduledPayment['intScheduledPaymentID'] .
                          ' failed to cancel with error: ' . $errorMsg;
                $autoProblemDesc = 'Scheduled payment failed to cancel';
                pt_raise_autoproblem('BillingFailure', $autoProblemDesc, $autoProblemMsg, '', true);
            } else {
                $callSuccess = true;
            }

            unset($objScheduledPayment);
        }

        return $callSuccess;
    }


    ////////////////////////
    // Private Methods
    ////////////////////////


    /**
    * prvCreatePaymentDetails
    *
    *
    * @access public
    * @access private
    * @static
    * <AUTHOR>
    * @return
    */
    function createPaymentConfig()
    {
        $dbhConn = get_named_connection_with_db('financial');

        $strQuery = "INSERT INTO financial.tblConfigProductComponent
                             SET intScheduledPaymentID = '{$this->m_intScheduledPaymentID}',
                                 intProductComponentInstanceID = '{$this->m_intProductComponentInstanceID}'";

        $refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Insert Product Component Scheduled Payment details');

        if (!empty($this->m_intProductComponentContractID)) {
            $strQuery = "INSERT INTO financial.tblScheduledPaymentTariff
                                 SET intScheduledPaymentId = '{$this->m_intScheduledPaymentID}',
                                     intProductComponentContractID = '{$this->m_intProductComponentContractID}'";

            $refResult = PrimitivesQueryOrExit(
                $strQuery, $dbhConn, 'Insert Config Product Component Product Component Contract details'
            );
        }

        return true;
    }



    /**
    * Fetch the Account ID by ProductComponentInstanceID
    *
    *
    * @access private
    * <AUTHOR>
    * @return integer account id or boolean false with m_objError set
    */
    function getAccountID()
    {
        if($this->m_intProductComponentInstanceID < 1) {
            return false;
        }

        $dbhConn = get_named_connection_with_db('userdata');

        // query is stupidly complicated due to Partner billing and the way everything
        // should be billed against the master account rather than the end user account
        // (the master account's customer_id != end user's customer_id in all cases)
        $strQuery = <<< END_OF_QUERY
SELECT
    a.account_id AS intAccountID
FROM
    userdata.tblProductComponentInstance pci
INNER JOIN
    userdata.components c
        ON c.component_id = pci.intComponentID
INNER JOIN
    userdata.services s
        ON s.service_id = c.service_id
INNER JOIN
    userdata.users u
        ON u.user_id = s.user_id
INNER JOIN
    userdata.customers cu
        ON cu.customer_id = u.customer_id
INNER JOIN
    userdata.services p
        ON cu.primary_user_id = p.user_id
INNER JOIN
    userdata.users pu
        ON p.user_id = pu.user_id
INNER JOIN
    userdata.accounts a
        ON pu.customer_id = a.customer_id
WHERE
    pci.intProductComponentInstanceID = "{$this->m_intProductComponentInstanceID}"
END_OF_QUERY;

        $refResult     = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Fetch account ID by ProductComponentInstanceID');
        $intAccountID  = PrimitivesResultGet($refResult,'intAccountID');

        if ($intAccountID < 1) {
            $this->setError(__FILE__, __LINE__, "Cannot find account id for ProductComponentInstance ID '{$this->m_intProductComponentInstanceID}'");
            return false;
        }

        return $intAccountID;
    }






}

?>
