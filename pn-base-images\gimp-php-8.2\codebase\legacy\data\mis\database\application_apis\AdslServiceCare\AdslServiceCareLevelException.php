<?php

require_once('/local/data/mis/database/database_libraries/programme-tool-access.inc');

class AdslServiceCareLevelException extends Exception
{
	public function __construct($message = null, $code = 0)
	{
		parent::__construct($message, $code);
		$this->raiseAutoProblem($code);
	}
	
	private function raiseAutoProblem($intCode)
	{
		switch ($intCode) {
			case 1: //MISSING_SERVICE_ID
				$strErrorMessage = 'Service ID is missing';
				break;
				
			case 2: //COMPONENT_NOT_CREATED
				$strErrorMessage = 'There has been an error while requesting Enhanced Care. '. 
				                   'Component has not been created';
				break;
				
			case 3: //INVALID_REGRADE_REASON
				$strErrorMessage = 'Regrade Order could not be created because of invalid regrade reason';
				break;
				
			case 4: //INVALID_TICKET_CONTACT
				$strErrorMessage = 'Regrade Order could not be created because of invalid ticket contact id';
				break;
				
			case 5: //REGRADE_NOT_CREATED
				$strErrorMessage = 'There has been an error while creating Regrade Order '. 
				                   'and process has not finished';
				break;
			
			case 6: //INVALID_SELECT_RESULT
				$strErrorMessage = 'There has been an error while instantiating AdslServiceCare object. '. 
				                   'Class could not execute select query to check if user '.
				                   'has enhanced care component.';
				break;
			
			case 7: //MULTIPLE_SELECT_RESULT
				$strErrorMessage = 'There has been an error while instantiating 
				                    AdslServiceCare object. Select query to check if user 
				                    has enhanced care component returned multiple result.';
				break;
			
			case 8: //MISSING_TICKET_TEMPLATE
				$strErrorMessage = 'Ticket template is missing.';
				break;

			case 10: // Other exception with auto-problem
				$strErrorMessage = $this->getMessage();
				break;
			default:
				 return; // do not raise an auto-problem
		}
		
		$strDescription = 'Something went wrong with the Enhanced Care, please investigate 
		                   the accounts/errors shown below:';
		
		$arrProblemDetails = pt_get_autoproblem_details('enhanced_care');
		pt_raise_autoproblem('enhanced_care', $arrProblemDetails['description'], 
		                     $strDescription, $strErrorMessage);

	}
}