<?php

require_once '/local/data/mis/database/class_libraries/Customers/C_Core_Service.php';
require_once '/local/data/mis/database/class_libraries/Products/CProvidedService.php';

/**
 * PlusNet Codebase Classes - C_Core_ADSLService.php
 * @version $Id: C_Core_ADSLService.php,v 1.18 2008-09-16 05:59:03 pmitchell Exp $
 * @see http://dokuwiki.internal.plus.net/
 *
 * @copyright PlusNet plc, www.plus.net
 *
 * This file is part of PlusNet Codebase Classes project.
 * Generated with ArgoUML PRE-0.19.5 on 08.11.2005, 09:43:01
 *
 * <AUTHOR> <<EMAIL>>
 */

/**
 * @include CProvidedService.php
 */

/**
 * @include C_Core_Service.php
 */

/* user defined includes */
// section 127-0-0-1-1d2b9b7:1038865b296:-7fc6-includes begin
// section 127-0-0-1-1d2b9b7:1038865b296:-7fc6-includes end

/* user defined constants */
// section 127-0-0-1-1d2b9b7:1038865b296:-7fc6-constants begin

        define('ADSL_PROVIDED_EVENT_SIGNUP', 1);
        define('ADSL_PROVIDED_EVENT_PRODUCT_CHANGE', 2);
        define('ADSL_PROVIDED_EVENT_MANUAL', 3);
        define('ADSL_PROVIDED_EVENT_REPAIRED', 4);
        define('ADSL_PROVIDED_EVENT_ROLLOUT', 5);
        define('ADSL_PROVIDED_EVENT_AUTOMATED', 6);
        define('ADSL_PROVIDED_EVENT_BY_DELTA_REPORT', 7);
        define('ADSL_PROVIDED_EVENT_BY_MIGRATION_IN_SCRIPT', 8);

        define('SUPPLIER_PRODUCT_ID_MAX_HOME', 19);
        define('SUPPLIER_PRODUCT_ID_MAX_OFFICE', 20);
        define('SUPPLIER_PRODUCT_ID_MAX_WBC', 31); // Fix for Problem:58615
        define('SUPPLIER_PRODUCT_ID_MAX_WBC_ADSL1', 32); // Fix for Problem:60930
        define('SUPPLIER_PRODUCT_ID_MAX_WBC_UNCAPED_UPSTREAM', 33);

// section 127-0-0-1-1d2b9b7:1038865b296:-7fc6-constants end

/**
 * TODO: Describe class C_Core_ADSLService
 *
 * @access public
 * <AUTHOR> Szulc, <<EMAIL>>
 */
class C_Core_ADSLService
        extends C_Core_Service
{
        // --- ATTRIBUTES ---

        /**
         * TODO: Describe attribute intProvisionedProductID
         *
         * @access private
         * @var int
         */
        var $m_intProvisionedProductID = 0;

        /**
         * TODO: Describe attribute intProvisionedServiceID
         *
         * @access private
         * @var int
         */
        var $m_intProvisionedServiceID = 0;

        /**
         * TODO: Describe attribute strSupplierPlatform
         *
         * @access private
         * @var string
         */
        var $_strSupplierPlatform = '';

        /**
         * TODO: Describe attribute strCBUK
         *
         * @access private
         * @var string
         */
        var $m_strCBUK = '';

        /**
         * TODO: Describe attribute strProvisionedProductName
         *
         * @access private
         * @var string
         */
        var $m_strProvisionedProductName = '';

        /**
         * TODO: Describe attribute intProvisionedProductSpeed
         *
         * @access private
         * @var int
         */
        var $m_intProvisionedProductSpeed = 0;

        /**
         * TODO: Describe attribute strProvisionedProductContentionRatio
         *
         * @access private
         * @var int
         */
        var $m_strProvisionedProductContentionRatio = 0;

        /**
         * TODO: Describe attribute strRealmProvisioned
         *
         * @access private
         * @var strRealmProvisioned
         */
        var $m_strProvisionedRealm = '';

        /**
         * TODO: Describe attribute objProvidedService
         *
         * @access private
         * @var CProvidedService
         */
        var $m_objProvidedService = null;

        // --- OPERATIONS ---

        /**
         * Class constructor. Accepts CBUK or ServiceID as parameters
         *
         * @access public
         * <AUTHOR> Szulc, <<EMAIL>>
         * @param string
         * @param string
         * @param int
         * @return int
         */
        function C_Core_ADSLService($strCBUK, $strFTIP = '', $intServiceID = false)
        {
                $returnValue = (int) 0;

                if ($strCBUK and $strFTIP) {

                        $intTmpServiceID = $this->getServiceIDbyCBUKandFTIP($strCBUK, $strFTIP);
                        if ((!$intTmpServiceID) AND $strCBUK) {
                                $intTmpServiceID = $this->getServiceIDbyCBUK($strCBUK);
                        }
                        if ((!$intTmpServiceID) AND $strFTIP) {
                                $intTmpServiceID = $this->getServiceIDbyFTIP($strFTIP);
                        }
                        $intServiceID = $intTmpServiceID?$intTmpServiceID:$intServiceID;
                }

                if ($intServiceID) {
                        $this->C_Core_Service($intServiceID);
                        if ($this->getServiceID()) {
                                $this->getProvisionedProductData();
                                $this->getProvidedService(true);
                        }
                }
        }


        function isOnMaxProduct()
        {
                return in_array(
                    $this->getProvisionedProductID(),
                    array(
                        SUPPLIER_PRODUCT_ID_MAX_HOME,
                        SUPPLIER_PRODUCT_ID_MAX_OFFICE,
                        SUPPLIER_PRODUCT_ID_MAX_WBC,
                        SUPPLIER_PRODUCT_ID_MAX_WBC_ADSL1,
                        SUPPLIER_PRODUCT_ID_MAX_WBC_UNCAPED_UPSTREAM
                    )
                );
        }

        /**
         * TODO: Describe method getServiceIDbyCBUKandFTIP
         *
         * @access public
         * <AUTHOR> Szulc, <<EMAIL>>
         * @param string
         * @param string
         * @return int
         */
        function getServiceIDbyCBUKandFTIP($strCBUK, $strFTIP = '')
        {
                $returnValue = (int) 0;

                $resDB = get_named_connection_with_db('adsl');
                $strQuery = sprintf(
                    'SELECT service_id FROM install_diary WHERE bt_circuit_number=\'%s\' AND'.
                    ' bt_reference_number=\'%s\' ORDER BY date_active DESC LIMIT 1',
                    PrimitivesRealEscapeString($strCBUK, $resDB), PrimitivesRealEscapeString($strFTIP, $resDB)
                );
                if ($resReturn = PrimitivesQueryOrExit($strQuery, $resDB)) {
                        $arrResult = PrimitivesResultGet($resReturn);
                        $returnValue = isset($arrResult['service_id'])?$arrResult['service_id']:0;
                }

                return $returnValue;
        }

        /**
         * TODO: Describe method getServiceIdByCbuk
         *
         * @access public
         * <AUTHOR> Szulc, <<EMAIL>>
         * @param string
         * @return int
         */
        function getServiceIdByCbuk($strCBUK)
        {
                $returnValue = (int) 0;

                $resDB = get_named_connection_with_db('adsl');
                $strQuery = sprintf(
                    'SELECT service_id FROM install_diary WHERE bt_circuit_number=\'%s\' ORDER BY'.
                    ' date_active DESC LIMIT 1', PrimitivesRealEscapeString($strCBUK, $resDB)
                );
                if ($resReturn = PrimitivesQueryOrExit($strQuery, $resDB)) {
                        $arrResult = PrimitivesResultGet($resReturn);
                        $returnValue = isset($arrResult['service_id'])?$arrResult['service_id']:0;
                }

                return $returnValue;
        }

        /**
         * TODO: Describe method getServiceIdByFtip
         *
         * @access public
         * <AUTHOR> Szulc, <<EMAIL>>
         * @param string
         * @return int
         */
        function getServiceIdByFtip($strFTIP)
        {
                $returnValue = (int) 0;

                $resDB = get_named_connection_with_db('adsl');
                $strQuery = sprintf(
                    'SELECT service_id FROM install_diary WHERE bt_reference_number=\'%s\' ORDER BY '.
                    'date_active DESC LIMIT 1', PrimitivesRealEscapeString($strFTIP, $resDB)
                );
                if ($resReturn = PrimitivesQueryOrExit($strQuery, $resDB)) {
                        $arrResult = PrimitivesResultGet($resReturn);
                        $returnValue = isset($arrResult['service_id'])?$arrResult['service_id']:0;
                }

                return $returnValue;
        }

        /**
         * TODO: Describe method getCBUK
         *
         * @access public
         * <AUTHOR> Szulc, <<EMAIL>>
         * @return string
         */
        function getCBUK()
        {
                $returnValue = (string) '';

                // section 127-0-0-1-1cd280b:103ef30aa9d:-7fde begin

                  $returnValue = $this->m_strCBUK;

                // section 127-0-0-1-1cd280b:103ef30aa9d:-7fde end

                return $returnValue;
        }

        /**
         * TODO: Describe method setCBUK
         *
         * @access public
         * <AUTHOR> Szulc, <<EMAIL>>
         * @param string
         * @return void
         */
        function setCBUK($strCBUK)
        {
                // section 127-0-0-1-1cd280b:103ef30aa9d:-7fdc begin

                  $this->m_strCBUK = $strCBUK;

                // section 127-0-0-1-1cd280b:103ef30aa9d:-7fdc end
        }

        /**
         * TODO: Describe method getProvisionedProductData
         *
         * @access public
         * <AUTHOR> Szulc, <<EMAIL>>
         * @return array
         */
        function getProvisionedProductData()
        {
                $returnValue = array();

                // section 127-0-0-1-b0095d:103ef741f3f:-7f9c begin

                        if ($this->getServiceID()) {
                                if (is_array($arrResult = adslGetProvisionedService($this->getServiceID(), TRUE)) and isset($arrResult['intProvisionedServiceID']) and $arrResult['intProvisionedServiceID']) {
                                        $this->setProvisionedServiceID($arrResult['intProvisionedServiceID']);
                                        $this->setProvisionedProductID($arrResult['intSupplierProductID']);
                                        $this->setProvisionedProductName($arrResult['vchProductCode']);
                                        $this->setProvisionedProductSpeed($arrResult['intRealMaxDownstream']);
                                        $this->setProvisionedProductContentionRatio($arrResult['vchContention']);
                                        $this->setProvisionedRealm($arrResult['vchRealmProvisioned']);
                                        $this->setSupplierPlatform($arrResult['vchSupplierPlatform']);
                                        $returnValue = $arrResult;
                                }
                        }

                // section 127-0-0-1-b0095d:103ef741f3f:-7f9c end

                return $returnValue;
        } // function getProvisionedProductData()

        /**
         * TODO: Describe method setProvisionedProduct
         *
         * @access public
         * <AUTHOR> Szulc, <<EMAIL>>
         * @param int
         * @param boolean
         * @return int
         */
        function setProvisionedProduct($intBTProductID, $bolActive = true)
        {
                $returnValue = (int) 0;

                // section 127-0-0-1-1d2b9b7:1038865b296:-7f83 begin

                if (!$_SERVER['HTTP_HOST']) {// command line execution
                    printf("Setting provisioned BTProduct for user to product ID: %d, Current BTProduct: %d\n", $intBTProductID, $this->getProvisionedProductID());
                }
                $intADSLProvisionedServiceID = adslSetProvisionedService($this->getServiceID(), $intBTProductID, ADSL_PROVISIONED_EVENT_BULK, false);
                if ($intADSLProvisionedServiceID and $bolActive) {
                    if (!$_SERVER['HTTP_HOST']) { // command line execution
                        echo "Setting provisioned service ID: $intADSLProvisionedServiceID status to ACTIVE\n";
                    }
                    adslActivateProvisionedService($this->getServiceID());
                    $this->getProvisionedProductData();
                }

                $returnValue = $intADSLProvisionedServiceID;

                // section 127-0-0-1-1d2b9b7:1038865b296:-7f83 end

                return $returnValue;
        } // function setProvisionedProduct($intBTProductID, $bolActive = true)

        /**
         * TODO: Describe method getProvisionedServiceId
         *
         * @access public
         * <AUTHOR> Szulc, <<EMAIL>>
         * @return int
         */
        function getProvisionedServiceId()
        {
                $returnValue = (int) 0;

                // section 127-0-0-1--32e11d30:10750f1cb63:-7fce begin

                  $returnValue = $this->m_intProvisionedServiceID;

                // section 127-0-0-1--32e11d30:10750f1cb63:-7fce end

                return $returnValue;
        }

        /**
         * TODO: Describe method setProvisionedServiceId
         *
         * @access public
         * <AUTHOR> Szulc, <<EMAIL>>
         * @param int
         * @return void
         */
        function setProvisionedServiceId($intProvisionedServiceID)
        {
                // section 127-0-0-1--32e11d30:10750f1cb63:-7fcc begin

                $this->m_intProvisionedServiceID = $intProvisionedServiceID;

                // section 127-0-0-1--32e11d30:10750f1cb63:-7fcc end
        }

        /**
         * TODO: Describe method getProvisionedProductID
         *
         * @access public
         * <AUTHOR> Szulc, <<EMAIL>>
         * @return int
         */
        function getProvisionedProductID()
        {
                $returnValue = (int) 0;

                // section 127-0-0-1-b0095d:103f40c51bb:-7fe6 begin

                  $returnValue = $this->m_intProvisionedProductID;

                // section 127-0-0-1-b0095d:103f40c51bb:-7fe6 end

                return $returnValue;
        }

        /**
         * TODO: Describe method setProvisionedProductID
         *
         * @access public
         * <AUTHOR> Szulc, <<EMAIL>>
         * @param int
         * @return void
         */
        function setProvisionedProductID($intProvisionedProductID)
        {
                // section 127-0-0-1-b0095d:103f40c51bb:-7fe4 begin

                  $this->m_intProvisionedProductID = $intProvisionedProductID;

                // section 127-0-0-1-b0095d:103f40c51bb:-7fe4 end
        }

        /**
         * TODO: Describe method getProvisionedProductName
         *
         * @access public
         * <AUTHOR> Szulc, <<EMAIL>>
         * @return string
         */
        function getProvisionedProductName()
        {
                $returnValue = (string) '';

                // section 127-0-0-1-b0095d:103f40c51bb:-7fef begin

                  $returnValue = $this->m_strProvisionedProductName;

                // section 127-0-0-1-b0095d:103f40c51bb:-7fef end

                return $returnValue;
        }

        /**
         * TODO: Describe method setProvisionedProductName
         *
         * @access public
         * <AUTHOR> Szulc, <<EMAIL>>
         * @param string
         * @return void
         */
        function setProvisionedProductName($strProvisionedProductName)
        {
                // section 127-0-0-1-b0095d:103f40c51bb:-7fe1 begin

                  $this->m_strProvisionedProductName = $strProvisionedProductName;

                // section 127-0-0-1-b0095d:103f40c51bb:-7fe1 end
        }

        /**
         * TODO: Describe method getProvisionedProductSpeed
         *
         * @access public
         * <AUTHOR> Szulc, <<EMAIL>>
         * @return int
         */
        function getProvisionedProductSpeed()
        {
                $returnValue = (int) 0;

                // section 127-0-0-1-b0095d:103f40c51bb:-7fed begin

                  $returnValue = $this->m_intProvisionedProductSpeed;

                // section 127-0-0-1-b0095d:103f40c51bb:-7fed end

                return $returnValue;
        }

        /**
         * TODO: Describe method setProvisionedProductSpeed
         *
         * @access public
         * <AUTHOR> Szulc, <<EMAIL>>
         * @param int
         * @return void
         */
        function setProvisionedProductSpeed($intSpeed)
        {
                // section 127-0-0-1-b0095d:103f40c51bb:-7fde begin

                  $this->m_intProvisionedProductSpeed = $intSpeed;

                // section 127-0-0-1-b0095d:103f40c51bb:-7fde end
        }

        /**
         * TODO: Describe method getProvisionedProductContentionRatio
         *
         * @access public
         * <AUTHOR> Szulc, <<EMAIL>>
         * @return string
         */
        function getProvisionedProductContentionRatio()
        {
                $returnValue = (string) '';

                // section 127-0-0-1-b0095d:103f40c51bb:-7feb begin

                  $returnValue = $this->m_strProvisionedProductContentionRatio;

                // section 127-0-0-1-b0095d:103f40c51bb:-7feb end

                return $returnValue;
        }

        /**
         * TODO: Describe method setProvisionedProductContentionRatio
         *
         * @access public
         * <AUTHOR> Szulc, <<EMAIL>>
         * @param string
         * @return void
         */
        function setProvisionedProductContentionRatio($strContentionRatio)
        {
                // section 127-0-0-1-b0095d:103f40c51bb:-7fdb begin

                  $this->m_strProvisionedProductContentionRatio = $strContentionRatio;

                // section 127-0-0-1-b0095d:103f40c51bb:-7fdb end
        }

        /**
         * @access public
         * <AUTHOR> Jones <<EMAIL>>
         * @return string
         */
        function getProvisionedRealm()
        {
                return $this->m_strProvisionedRealm;
        }

        /**
         * TODO: Describe method setProvisionedRealm
         *
         * @access public
         * <AUTHOR> Jones <<EMAIL>>
         * @param string
         * @return void
         */
        function setProvisionedRealm($strProvisionedRealm)
        {
                $this->m_strProvisionedRealm = $strProvisionedRealm;
        }

        /**
         * Function to get the supplier platform
         *
         * @access public
         * @return string
         */
        function getSupplierPlatform()
        {
                return $this->_strSupplierPlatform;
        }

        /**
         * Function to set the supplier platform
         *
         * @access public
         * @param string
         * @return void
         */
        function setSupplierPlatform($_strSupplierPlatform)
        {
                $this->_strSupplierPlatform = $_strSupplierPlatform;
        }


        /**
         * TODO: Describe method getProvidedService
         *
         * @access public
         * <AUTHOR> Szulc, <<EMAIL>>
         * @param boolean
         * @return CProvidedService
         */
        function getProvidedService($bolForceReload = false)
        {
                $returnValue = null;

                // section 127-0-0-1-b0095d:103f40c51bb:-7fbf begin
                if ($bolForceReload or ! is_object($this->m_objProvidedService)) {
                        $this->m_objProvidedService =& CProvidedService::getProvidedServiceForServiceId($this->getServiceId());
                }
                $returnValue = $this->m_objProvidedService;

                // section 127-0-0-1-b0095d:103f40c51bb:-7fbf end

                return $returnValue;
        }

        /**
        * getConnectionProfile
        *
        * Get the connection profile for a service
        *
        * <AUTHOR> Jones" <<EMAIL>>
        * @access       public
        * @return       array   Connection profile of ADSL service
        */
        function getConnectionProfile()
        {
                if (! is_object($this->m_objProvidedService)) {
                        $this->getProvidedService(TRUE);
                }

                return array('intConnectionProfileID' => $this->m_objProvidedService->m_objConnectionProfile->m_intId,
                             'strDisplayName' => $this->m_objProvidedService->m_objConnectionProfile->m_strName,
                             'strEllacoyaHandle' => $this->m_objProvidedService->m_objConnectionProfile->m_strEllacoyaHandle,
                             'intMaxDownstream' => $this->m_objProvidedService->m_objConnectionProfile->m_intMaxDownstream,
                             'intMaxUpstream' => $this->m_objProvidedService->m_objConnectionProfile->m_intMaxUpstream,
                             'intRGrpID' => $this->m_objProvidedService->m_objConnectionProfile->m_intRGrpID,
                             'bolCanBeCustom' => $this->m_objProvidedService->m_objConnectionProfile->m_bolCanBeCustom,
                             'bolRestricted' => $this->m_objProvidedService->m_objConnectionProfile->m_bolRestricted,
                             'bolTimeout' => $this->m_objProvidedService->m_objConnectionProfile->m_bolTimeout,
                             'bolDefault' => $this->m_objProvidedService->m_objConnectionProfile->m_bolDefault);
        } // function getConnectionProfile()

        /**
         * Sets new connection profile for provided service
         * If none is passed we get the valid default connection profile
         *
         * @access public
         *
         * <AUTHOR> Szulc" <<EMAIL>>
         * <AUTHOR> Marek" <<EMAIL>>
         *
         * @uses CConnectionProfile::getDefaultProfileByServiceID()
         * @uses CProvidedService::setNewConnectionProfile()
         * @uses CProvidedService::saveConnectionProfile()
         *
         * @param object $objConnectionProfile
         * @param int    $intEventType
         * @param boolean $bolMarkCustomerForDisconnect Set to false if you don't
         *                want to mark customer for disconnect (default true)
         *
         * @return mixed new ProvidedServiceID on success
         *               false on failure
         */
        function setConnectionProfile($objConnectionProfile = null, $intEventType = 0, $bolMarkCustomerForDisconnect = true)
        {
                $objProvidedService =& $this->getProvidedService(true); // re-instantiate the provided service object

                // No provided service - probably new signup then
                if (!is_object($objProvidedService)) {
                        $objConnectionProfile = CConnectionProfile::getProfileByServiceID($this->getServiceId());
                        $mixReturnValue = CProvidedService::setNewProvidedService($this->getServiceId(), $objConnectionProfile, $intEventType);
                } else {
                        // get default profile if connection profile has not been passed
                        if (!is_object($objConnectionProfile)) {
                                $objConnectionProfile = CConnectionProfile::getDefaultProfileByServiceID($this->getServiceId());
                        }

                        $objProvidedService->setNewConnectionProfile($objConnectionProfile);
                        $mixReturnValue = $objProvidedService->saveConnectionProfile($intEventType, $bolMarkCustomerForDisconnect);
                }

                $this->getProvidedService(true);

                return $mixReturnValue;
        }

        /**
         * TODO: Describe method getProvidedServiceID
         *
         * @access public
         * <AUTHOR> Szulc, <<EMAIL>>
         * @return int
         */
        function getProvidedServiceID()
        {
                $returnValue = (int) 0;

                // section 127-0-0-1-b0095d:103f40c51bb:-7fd8 begin

                if (is_object($this->m_objProvidedService)) {
                  $returnValue = $this->m_objProvidedService->getId();
                }

                // section 127-0-0-1-b0095d:103f40c51bb:-7fd8 end

                return $returnValue;
        }

        /**
         * TODO: Describe method getProvidedServiceSpeed
         *
         * @access public
         * <AUTHOR> Szulc, <<EMAIL>>
         * @return int
         */
        function getProvidedServiceSpeed()
        {
                $returnValue = (int) 0;

                // section 127-0-0-1-b0095d:103f40c51bb:-7fbc begin

                if (is_object($this->m_objProvidedService)) {
                  $objConnectionProfile = $this->m_objProvidedService->getConnectionProfile();
                  $returnValue = $objConnectionProfile->getMaxDownstream();
                }

                // section 127-0-0-1-b0095d:103f40c51bb:-7fbc end

                return $returnValue;
        }

        /**
         * TODO: Describe method canChangeConnectionProfile
         *
         * @access public
         * <AUTHOR> Szulc, <<EMAIL>>
         * @return boolean
         */
        function canChangeConnectionProfile()
        {
                $returnValue = (bool) false;

                // section 127-0-0-1--32bda35e:1076f388902:-7feb begin

                $objProfileGroup = CConnectionProfileGroup::getInstanceForSdi($this->getServiceDefinitionId());
                if (is_object($objProfileGroup)) {
                        $arrProfiles = $objProfileGroup->getProfiles();
                        $objProvidedService = & $this->getProvidedService();
                        if (is_object($objProvidedService) AND is_array($arrProfiles)) {
                                $objCurrentProfile = & $objProvidedService->getConnectionProfile();
                                if (is_object($objCurrentProfile) and !$objCurrentProfile->isRestricted()) {
                                        $intAvailableProfiles = 0;
                                        $bolFoundCurrentProfile = false;
                                        $bolFoundOppositeTimeout = false;

                                        foreach ($arrProfiles as $objP) {
                                                // Ignore anything that's not a valid profile
                                                if (!is_object($objP) OR $objP->getId() == 0) {
                                                        continue;
                                                }

                                                ++$intAvailableProfiles;

                                                if (stristr($objP->getName(), 'Residential Plus') AND $objP->isTimeout() == $objCurrentProfile->isTimeout()) {
                                                        $bolFoundCurrentProfile = true;
                                                }

                                                // If there's a connection profile with the same RGRP ID
                                                if ($objP->getRgrpId() == $objCurrentProfile->getRgrpId()) {
                                                        $bolFoundCurrentProfile = true;
                                                }

                                                // If there's a connection profile with a different RGRP ID and a different timeout value
                                                if ($objP->getRgrpId() != $objCurrentProfile->getRgrpId() AND $objP->isTimeout() != $objCurrentProfile->isTimeout()) {
                                                        $bolFoundOppositeTimeout = true;
                                                }
                                        }

                                        if ($intAvailableProfiles > 1 AND $bolFoundCurrentProfile AND $bolFoundOppositeTimeout) {
                                                $returnValue = true;
                                        }
                                }
                        }
                }

                // section 127-0-0-1--32bda35e:1076f388902:-7feb end

                return $returnValue;
        }

        /**
         * Function to check the Provisioned as ADSL2 or MAXDSL
         *
         * @return boolean
         */
        function isProvisionedAsADSLOrMaxDSL()
        {
                return in_array($this->getProvisionedProductID(), array(SUPPLIER_PRODUCT_ID_MAX_HOME, SUPPLIER_PRODUCT_ID_MAX_OFFICE, SUPPLIER_PRODUCT_ID_MAX_WBC));
        }

} /* end of class C_Core_ADSLService */