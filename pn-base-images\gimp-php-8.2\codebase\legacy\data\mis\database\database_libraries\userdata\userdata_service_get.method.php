<?php

/**
 * Legacy getter for the userdata.services information
 *
 * @param integer $serviceId The service Id
 * @param boolean $freshCopy Do we want a fresh copy from the database
 *
 * @return array
 */
function split_userdata_service_get($serviceId, $freshCopy = false)
{
    static $arrUserdataServiceGetCache = array();
    $maxCacheSize = (defined('USERDATA_GET_SERVICE_CACHE_SIZE') ? (int) USERDATA_GET_SERVICE_CACHE_SIZE : 15000);
    $bolNoCache  = (defined('USERDATA_DO_NOT_CACHE_SERVICES') && true == USERDATA_DO_NOT_CACHE_SERVICES ? true : false);

    $query_count_template = "SELECT count(*) AS mycount FROM services AS s WHERE s.service_id = %d";

    $query_data_template = <<<EOQ
SELECT 
    s.*,
    vc.short_brand,
    sdrs.vchReportSector AS strReportSector
FROM 
    userdata.services                                   AS s
    LEFT JOIN products.visp_config                      AS vc   ON s.isp = vc.isp
    LEFT JOIN products.tblServiceDefinitionReportGroup  AS sdrg ON s.type = sdrg.intServiceDefinitionId
    LEFT JOIN products.tblServiceDefinitionReportSector AS sdrs ON sdrg.intReportSectorId = sdrs.intReportSectorId
WHERE 
    s.service_id = %d
EOQ;

    if (!$freshCopy && !$bolNoCache && isset($arrUserdataServiceGetCache[$serviceId])) {
        return $arrUserdataServiceGetCache[$serviceId];
    }

    // make sure service_id is numeric
    if (!is_numeric($serviceId) || $serviceId == 0 ) {

        $strErrorMessage = "Invalid Service ID passed into userdata_service_get()" .
            "\nValue passed was: [$serviceId]" .
            (isset($_SERVER) && is_array($_SERVER) ? "\nRequest was: " . dss($_SERVER) : '') .
            "\nBackTrace:\n".print_r(debug_backtrace(false), true);

        error_log('WARNING: ' . __FILE__ . ':' . __LINE__ . ' - ' . $strErrorMessage);
        return false;
    }

    $serviceId = (int) $serviceId;

    // By default, we check the reporting DB - if the record can't be found there, we do a follow query on the master DB
    $strDb = ($freshCopy ? 'userdata' : 'userdata_reporting');
    $connection = get_named_connection($strDb);

    // For some bizarre reason, the full query below will fail if the service_id is invalid, with the error message 
    //  "Table 'products.visp_config' doesn't exist"
    // The reason for this is unclear, as the table does exist and the same query works perfectly well when
    // ran directly in mysql.  And since we're using mysql_query() directly, there's (presumably) no plusnet-specific 
    // mangling taking place before the query gets interpreted.
    // We therefore do a qnd lookup against the services table *only* before attempting to do the full query
    $query = sprintf($query_count_template, $serviceId);
    $result = mysql_query($query, $connection) or report_error(__FILE__, __LINE__, mysql_error($connection));
    $service = mysql_fetch_array($result, MYSQL_ASSOC);
    mysql_free_result($result);

    if ($service && $service['mycount'] == 0) {
        // Fall back to the master DB - this connection will also be used by the "data" query below
        $connection = get_named_connection('userdata');

        $result = mysql_query($query, $connection) or report_error(__FILE__, __LINE__, mysql_error($connection));
        $service = mysql_fetch_array($result, MYSQL_ASSOC);
        mysql_free_result($result);
    }

    if (!$service) {
        report_error(__FILE__, __LINE__, "Query $query failed");
    }

    if ($service['mycount'] == 0) {
        return false;
    }

    // Now get the data from the DB
    $query = sprintf($query_data_template, $serviceId);
    $result = mysql_query($query, $connection) or report_error(__FILE__, __LINE__, mysql_error($connection));
    $service = mysql_fetch_array($result, MYSQL_ASSOC);
    mysql_free_result($result);

    if (!$service) {
        report_error(__FILE__, __LINE__, "Query $query failed");
    }

    $service['password'] = Crypt_Crypt::decrypt($service['password'], 'services');

    if ($bolNoCache) {
        return $service;
    }

    if ($maxCacheSize >= count($arrUserdataServiceGetCache)) {
        array_shift($arrUserdataServiceGetCache);
    }

    $arrUserdataServiceGetCache[$serviceId] = $service;

    return $service;
}
