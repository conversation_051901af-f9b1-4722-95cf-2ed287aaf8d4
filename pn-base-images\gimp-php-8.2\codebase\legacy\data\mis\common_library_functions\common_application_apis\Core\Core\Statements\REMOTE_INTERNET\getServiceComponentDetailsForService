server: itw
role: slave
rows: single
statement:

SELECT
  sd.service_definition_id,
  sd.name,
  sd.isp,
  NULL AS minimum_charge,
  NUll AS intTariffID,
  sd.initial_charge,
  sd.type,
  sd.password_visible_to_support,
  sd.requires,
  sd.date_created,
  sd.end_date,
  sd.signup_via_portal,
  sd.blurb,
  NULL AS vchContract,
  NULL AS vchPaymentFrequency,
  sc.name AS strComponentName
FROM
  dbRemoteInternet.services s
INNER JOIN dbRemoteInternet.components c
  ON s.service_id = c.service_id
INNER JOIN dbRemoteInternet.tblServiceComponentProduct scp
  ON c.component_type_id = scp.intServiceComponentId
INNER JOIN dbRemoteInternet.tblServiceComponentProductType scpt
  USING (intServiceComponentProductTypeID)
INNER JOIN dbRemoteInternet.tblProductComponentInstance pci
  ON pci.intComponentID = c.component_id
INNER JOIN dbRemoteInternet.service_components sc
  ON sc.service_component_id = scp.intServiceComponentID
INNER JOIN products.service_definitions sd
  ON (sd.service_definition_id = s.type)
WHERE
  scpt.vchHandle = 'INTERNET_CONNECTION'
AND
  c.status IN ('active','queued-activate','queued-reactivate')
AND
  pci.dtmEnd IS NULL
AND
  s.service_id = :intServiceId
