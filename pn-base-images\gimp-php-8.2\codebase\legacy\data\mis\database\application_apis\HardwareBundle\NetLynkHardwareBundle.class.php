<?php
/**
 * NetLynkHardwareBundle.class.php
 *
 * @package HardwareBundle
 * <AUTHOR> <EMAIL>
 */
/**
 * NetLynkHardwareBundle Class
 *
 * @package HardwareBundle
 * <AUTHOR> <EMAIL>
 */
class NetLynkHardwareBundle extends HardwareBundle
{
    /**
     * @var string
     */
    var $m_strSupplierTag = 'NetLynk';

    /**
     * @var string
     */
    var $m_strClassPrefix = 'NetLynk';

    /**
     * Const defined for the test mode file path
     */
    const TEST_MODE_FILE_NAME = '/local/codebase2005/modules/Framework/Config/ServerRoles/NetLynkTestMode.role';

    /**
     * Call the parent function PlacePendingOrder
     * to place the orders
     *
     * @return void
     */
    public function PlaceOrder()
    {
        $this->PlacePendingOrder();
    }

    /**
     * Actually Cancel the order
     *
     * Cancel the order as per suppliers requirements
     *
     * @access protected
     * @return void
     */
    function DoCancelOrder()
    {
        global $my_id;

        $strTicketRaisingUser = (empty($my_id)) ? SCRIPT_USER : $my_id;

        //Raise a ticket to BSC to cancel the hardware order
        $arrService = $this->GetService();
        $intServiceId = (int)$arrService['service_id'];
        $strTicketBody
            = 'The hardware order on this account for the component('.
            $this->m_intComponentID.') has been cancelled,'
            . ' Please cancel the order with the supplier.';
        $intTeamId = PHPLibTeamIdGetByName('Partner - Hardware Ordering');
        tickets_ticket_add(
            'Script', $intServiceId, 0, 0, 'Open', $strTicketRaisingUser,
            $strTicketBody, 0, $intTeamId
        );

        //Update the component with the correct status
        userdata_component_set_status($this->m_intComponentID, 'destroyed');
    }//end of function:DoCancelOrder()


    /**
     * Generate customer information for the track request
     *
     * @param DOMDocument $dom DOMDocument
     *
     * @return DOMDocument $customerInformation
     */
    public function GenerateCustomerInformation(DOMDocument $dom)
    {
        $arrLoginInformation = $this->getNetLynkLoginInformation();
        $strReferralCode     = $arrLoginInformation['strReferralCode'];
        $strPassword         = $arrLoginInformation['strPassword'];

        //<CustomerInformation>
        $customerInformation = $dom->createElement('CustomerInformation');
        $name = $dom->createElement('Name', 'BTMB');
        $refferalCode = $dom->createElement('ReferralCode', $strReferralCode);
        $password = $dom->createElement('Password', $strPassword);
        $customerInformation->appendChild($name);
        $customerInformation->appendChild($refferalCode);
        $customerInformation->appendChild($password);
        //<CustomerInformation>

        return $customerInformation;
    }

    /**
     * Send an order for the hardware bundle.
     * Generate the order string for this hardware order
     *
     * @param DOMDocument $dom DOMDocument
     *
     * @return string The actual order line
     */
    public function GenerateOrderString(DOMDocument $dom)
    {
        $arrUser            = $this->GetUser();
        $arrService         = $this->GetService();
        $arrDeliveryAddress = $this->GetDeliveryAddress();

        $strSalutation  = $arrUser['salutation'];
        $strForeName    = $arrUser['forenames'];
        $strSUrname     = $arrUser['surname'];
        $intComponentId = $this->GetHardwareBundleID();
        $intServiceId   = (int)$arrService['service_id'];

        $strHouse    = $arrDeliveryAddress['house'];
        $strTown     = $arrDeliveryAddress['town'];
        $strPostcode = $arrDeliveryAddress['postcode'];
        $strCountry  = $arrDeliveryAddress['country'];
        $strStreet   = $arrDeliveryAddress['street'];
        $strCounty   = $arrDeliveryAddress['county'];

        $strOrderDate            = date('Y-m-d\\Th:i:s');
        $strCustomerRequiredDate = date('Y-m-d\\Th:i:s');

        $isRMA = ($this->m_strOverallStatusTag == 'awaiting_rma') ? true : false;

        // Output each of the items
        foreach ($this->GetItems() as $arrItem) {

            $strProductCode = $arrItem['strProductCode'];
            $intQuantity    = $arrItem['intQuantity'];
        }

        if ($arrService['isp'] == 'partner') {

            $businessActor = Auth_BusinessActor::getActorByExternalUserId($intServiceId);
            $strUsername   = $businessActor->getUsername();
            $endUser       = Reseller_EndUser::getEndUserByUsername($strUsername);

            try {

                $endUserProvisioning  = new Reseller_EndUserProvisioning($endUser);
                $strRealm = $endUserProvisioning->getRealmProvisioned();
                if (strpos($strRealm,'@') === false) {
                    $strAdslRealm = $endUser->getUsername().'@'.$endUserProvisioning->getRealmProvisioned();
                } else {
                    $strAdslRealm = $endUser->getUsername().'-'.$endUserProvisioning->getRealmProvisioned();
                }
            }
            catch(Reseller_UserNotProvitionedException $objException) {

                $strAdslRealm = null;
            }

        } else {

            $adslApplicationDetails = get_adsl_application_details($intServiceId);
            $strAdslRealm = $arrService['username'] . '@' . $adslApplicationDetails['strRealmProvisioned'];
        }

        require_once('/local/data/mis/database/crypt_config.inc');
        require_once('/local/data/mis/common_library_functions/class_libraries/Crypt/Crypt.class.php');

        /* P 57774 We need to first check if there is a radius entry. If there is we need to
         * sent the RADIUS password for hardware ordering
         */
        $strAdslPassword = $this->getRadiusPassword($arrService);
        if (empty($strAdslPassword) && isset($endUser)) {

            $strEncryptedPassword = $endUser->getEncryptedPassword();
            $strAdslPassword = Crypt_Crypt::decrypt($strEncryptedPassword, 'services');
        }

        $arrLoginInformation = $this->getNetLynkLoginInformation();
        $strReferralCode     = $arrLoginInformation['strReferralCode'];
        $strPassword         = $arrLoginInformation['strPassword'];

        //<Order>
        $order = $dom->createElement('Order');

        //<Version>
        $version = $dom->createElement('Version', '1.1');
        $order->appendChild($version);
        //</Version>

        //<CustomerInformation>
        $customerInformation = $dom->createElement('CustomerInformation');
        $name = $dom->createElement('Name', 'BTMB');

        $refferalCode = $dom->createElement('ReferralCode');
        $refferalCode->appendChild($dom->createTextNode($strReferralCode));

        $password = $dom->createElement('Password');
        $password->appendChild($dom->createTextNode($strPassword));

        $customerInformation->appendChild($name);
        $customerInformation->appendChild($refferalCode);
        $customerInformation->appendChild($password);

        $order->appendChild($customerInformation);
        //</CustomerInformation>

        //<Reference>
        $reference = $dom->createElement('Reference');
        $reference->appendChild($dom->createTextNode($intComponentId . (($isRMA) ? '-RMA' : '')));
        $order->appendChild($reference);
        //</Reference>

        //<OrderDate>
        $orderDate = $dom->createElement('OrderDate');
        $orderDate->appendChild($dom->createTextNode($strOrderDate));
        $order->appendChild($orderDate);
        //</OrderDate>

        //<CRD>
        $crd = $dom->createElement('CRD');
        $crd->appendChild($dom->createTextNode($strCustomerRequiredDate));
        $order->appendChild($crd);
        //</CRD>

        //<DeliveryMethod>
        $deliveryMethod = $dom->createElement('DeliveryMethod');
        $carrier = $dom->createElement('Carrier', 'Parcel Force');
        $service = $dom->createElement('Service', 'ND24H');

        $deliveryMethod->appendChild($carrier);
        $deliveryMethod->appendChild($service);

        $order->appendChild($deliveryMethod);
        //</DeliveryMethod>

        //<DeliveryParty>
        $deliveryParty = $dom->createElement('DeliveryParty');
        $party = $dom->createElement('Party');

        $ident = $dom->createElement('Ident');
        $ident->appendChild($dom->createTextNode($intServiceId));

        $title = $dom->createElement('Title');
        $title->appendChild($dom->createTextNode($strSalutation));

        $firstName = $dom->createElement('FirstName');
        $firstName->appendChild($dom->createTextNode($strForeName));

        $lastName = $dom->createElement('LastName');
        $lastName->appendChild($dom->createTextNode($strSUrname));

        $telephone = $dom->createElement('Telephone');
        $companyName = $dom->createElement('CompanyName');

        $party->appendChild($ident);
        $party->appendChild($title);
        $party->appendChild($firstName);
        $party->appendChild($lastName);
        $party->appendChild($telephone);
        $party->appendChild($companyName);

        $deliveryParty->appendChild($party);

        $address = $dom->createElement('Address');
        $premisesName = $dom->createElement('PremisesName');
        $thoroughFareNumber = $dom->createElement('ThoroughfareNumber');

        $thoroughFareName = $dom->createElement('ThoroughfareName');
        $thoroughFareName->appendChild($dom->createTextNode($strHouse));

        $addressLine2 = $dom->createElement('AddressLine2');
        $addressLine2->appendChild($dom->createTextNode($strStreet));

        $addressLine3 = $dom->createElement('AddressLine3');
        $addressLine3->appendChild($dom->createTextNode($strCounty));

        $postTown = $dom->createElement('PostTown');
        $postTown->appendChild($dom->createTextNode($strTown));

        $postCode = $dom->createElement('PostCode');
        $postCode->appendChild($dom->createTextNode($strPostcode));

        $country = $dom->createElement('Country');
        $country->appendChild($dom->createTextNode($strCountry));

        $address->appendChild($premisesName);
        $address->appendChild($thoroughFareNumber);
        $address->appendChild($thoroughFareName);
        $address->appendChild($addressLine2);
        $address->appendChild($addressLine3);
        $address->appendChild($postTown);
        $address->appendChild($postCode);
        $address->appendChild($country);

        $deliveryParty->appendChild($address);

        $order->appendChild($deliveryParty);
        //</DeliveryParty>

        //<SupplierPartList>
        $supplierPartList = $dom->createElement('SupplierPartList');
        $supplierPart = $dom->createElement('SupplierPart');
        $partName = $dom->createElement('PartName');

        $partSKU = $dom->createElement('PartSKU');
        $partSKU->appendChild($dom->createTextNode($strProductCode . (($isRMA) ? '-RMA' : '')));

        $partCount = $dom->createElement('PartCount');
        $partCount->appendChild($dom->createTextNode($intQuantity));

        $notes = $dom->createElement('Notes');
        $configuration = $dom->createElement('Configuration');

        $userConfig = $dom->createElement('ConfigurationItem');
        $userConfigName = $dom->createElement('ConfigName', 'username');
        $userConfig->appendChild($userConfigName);

        $userConfigValue = $dom->createElement('Value');
        $userConfigValue->appendChild($dom->createTextNode($strAdslRealm));

        $userConfig->appendChild($userConfigValue);

        $configuration->appendChild($userConfig);

        $passConfig = $dom->createElement('ConfigurationItem');
        $passConfigName = $dom->createElement('ConfigName', 'password');
        $passConfig->appendChild($passConfigName);

        $passConfigValue = $dom->createElement('Value');
        $passConfigValue->appendChild($dom->createTextNode($strAdslPassword));

        $passConfig->appendChild($passConfigValue);

        $configuration->appendChild($passConfig);

        $supplierPart->appendChild($partName);
        $supplierPart->appendChild($partSKU);
        $supplierPart->appendChild($partCount);
        $supplierPart->appendChild($notes);
        $supplierPart->appendChild($configuration);

        $supplierPartList->appendChild($supplierPart);
        //</SupplierPartList>

        $order->appendChild($supplierPartList);
        //</Order>

        return $order;
    }

    /**
     * Retrieves Referral Code and Passsword for the NetLynk Gateway.
     * Referal code and password are returned with respect to the mode in which the script is running
     * That is if we have file in the path self::TEST_MODE_FILE_NAME then the function will return the
     * test Referral Code and Password on the other case it returns the orginal password
     *
     * @return array with strReferralCode and strPassword as key
     */
    private function getNetLynkLoginInformation()
    {
        if (file_exists(self::TEST_MODE_FILE_NAME)) {
            return array('strReferralCode' => '112486486112', 'strPassword' => 'HhP1CO35WtkYfyHKx9bo');
        } else {
            return array('strReferralCode' => '3846851',      'strPassword' => 'GXXjJ3KMdj0p1EJLnxgU');
        }
    }

    /**
     * Get the radius password of a user
     *
     * @param array $arrService Service
     *
     * @return string
     */
    private function getRadiusPassword($arrService)
    {
        $adaptor = Db_Manager::getAdaptor('Core', Db_Manager::DEFAULT_TRANSACTION);
        $data = $adaptor->getRadiusUserByUsernameIsp($arrService['username'], $arrService['isp']);
        if (isset($data['strEncryptedPassword']) && !empty($data['strEncryptedPassword'])) {

            $strPassword = Crypt_Crypt::decrypt($data['strEncryptedPassword'], 'users');
        } else {
            $strPassword = null;
        }

        return $strPassword;
    }

    /**
     * Inserts hardware order feedback to database
     * including device serial number and config hardware bundle id
     *
     * @param string $strSerialNumber SerialNumber
     *
     * @return boolean
     */
    public function saveHardwareOrderFeedback($strSerialNumber)
    {
        $intPartnerId = (defined('PARTNER_ID')) ? PARTNER_ID : 0;

        $dbhConnection = get_named_connection_with_db('common_adsl');

        $strQuery
            = "INSERT INTO tblHWFeedbackRecord
            SET
                intConfigHardwareBundleID = '%d',
                intPartnerID = '%d',
                vchSerialNumber = '%s'
            ";

        $strQuery =  sprintf(
            $strQuery,
            mysql_real_escape_string($this->GetBundleId()),
            mysql_real_escape_string($intPartnerId),
            mysql_real_escape_string($strSerialNumber)
        );

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

        if (PrimitivesAffectedRowsGet($dbhConnection)) {

            return true;
        }

         return false;
    }

    /**
     * Get status updates for orders associated with NetLynk
     *
     * @see HardwareBundle::PollForOrderUpdate()
     *
     * @return void
     */
    public function PollForOrderUpdate()
    {
        $strClassName = $this->m_strClassPrefix . 'HardwareBundleSupplier';

        // Get the current order status.
        $supplier = new $strClassName();

        $this->setMessage(date('H:i:s')." Fetching Latest OrderStatus\n");

        $arrNewStatus = $supplier->FetchLatestOrderStatus($this);

        if (empty($arrNewStatus)) {

            $this->setMessage(date('H:i:s')." Latest Order Status empty.\n");
            return false;
        }

        switch ($this->GetStatusTag()) {

            case 'not_ordered':
            case 'rejected':
            case 'cancelled':
                $this->setMessage(date('H:i:s')." No need to update component, already in a final state\n");
                // No need to update component, already in a final state,
                return false;
                break;
        }

        $strNewStatus = $arrNewStatus['strStatusTag'];

        $strCurrentStatus = $this->GetStatusTag();

        // Make sure we've been returned a sensible status, as we don't
        // want to mess this component up.
        if ($strNewStatus != '' && $strNewStatus != false) {

            //Only update the status if it has been changed
            if ($strNewStatus != $strCurrentStatus) {

                // Update the object
                $this->SetStatusTag(-1, $strNewStatus);

                $this->SetWhenLastUpdated();

                // Set the When_Dispatched flag
                $this->SetWhenDispatched();

                // Save the new info
                $binResult = $this->Commit();

                if ($binResult == true) {

                    $this->setMessage(
                        date('H:i:s')." Status of hardware order changed from ".$strCurrentStatus." to ".
                        $strNewStatus. "\n"
                    );

                    $this->AddContact(
                        "Status of hardware order changed from '$strCurrentStatus' to '$strNewStatus'"
                    );
                } else {
                    $this->setMessage(date('H:i:s')." Failed while saving the changes to database\n");
                }
            } else {
                $this->setMessage(date('H:i:s')." Latest order status not changed\n");
            }
        } else if ($strNewStatus === false) {

            $this->setMessage(date('H:i:s')." Unable to get a reply from the supplier. Not unexpected\n");

            // We were unable to get a reply from the supplier. Not
            // unexpected, so just return false silently.
            return false;
        } else {

            $this->setMessage(date('H:i:s')." Decoding the response from the supplier resulted in a blank status\n");
            // We shouldn't be here. All replies from the supplier should be recognisable, so we return false. Other
            // code will handle this as though the supplier just didn't reply.
            // DJM: 16/10/2012: error logging disabled as it was generating too much output in the error log
            // As per above, this error condition will be handled elsewhere in the code

            return false;
        }
    }
}
