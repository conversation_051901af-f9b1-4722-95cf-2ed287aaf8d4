server: coredb
role: slave
rows: single
statement:

    SELECT sd.service_definition_id,
           sd.name,
           sd.isp,
           t.intCostIncVatPence/100 AS minimum_charge,
           t.intTariffID,
           sd.initial_charge,
           sd.type,
           sd.password_visible_to_support,
           sd.requires,
           sd.date_created,
           sd.end_date,
           sd.signup_via_portal,
           sd.blurb,
           cl.vchHandle AS vchContract,
           pf.vchHandle AS vchPaymentFrequency,
           sc.name AS strComponentName,
           sc.service_component_id
      FROM userdata.services s
INNER JOIN userdata.components c
        ON s.service_id = c.service_id
INNER JOIN products.tblServiceComponentProduct scp
        ON c.component_type_id = scp.intServiceComponentId
INNER JOIN products.tblServiceComponentProductType scpt
        ON scpt.intServiceComponentProductTypeID = scp.intServiceComponentProductTypeID
INNER JOIN userdata.tblProductComponentInstance pci
        ON pci.intComponentID = c.component_id
INNER JOIN dbProductComponents.tblProductComponent pc
        ON pci.intProductComponentID = pc.intProductComponentID
INNER JOIN dbProductComponents.tblTariff t
        ON pci.intTariffID = t.intTariffID
INNER JOIN dbProductComponents.tblContractLength cl
        ON cl.intContractLengthID = t.intContractLengthID
INNER JOIN dbProductComponents.tblPaymentFrequency pf
        ON (pf.intPaymentFrequencyID = t.intPaymentFrequencyID)
INNER JOIN products.service_components sc
        ON sc.service_component_id = scp.intServiceComponentID
INNER JOIN products.service_definitions sd
        ON sd.service_definition_id = s.type
     WHERE scpt.vchHandle = 'INTERNET_CONNECTION'
       AND pc.vchHandle = 'SUBSCRIPTION'
       AND c.status IN ('active','queued-activate','queued-reactivate')
       AND pci.dtmEnd IS NULL
       AND s.service_id = :intServiceId
