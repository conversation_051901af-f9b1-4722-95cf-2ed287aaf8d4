server: coredb
role: slave
rows: multiple
statement:

SELECT
		s.service_id,
		s.user_id,
		s.isp,
		s.username,
		s.password,
		s.cli_number,
		s.type,
		s.status,
		s.enddate,
		s.next_invoice,
		s.invoice_period,
		s.next_invoice_warned,
		s.invoice_day,
		s.authorised_switch_payment,
		s.bolMailOptOut
FROM
		transactions.transactions t
		INNER JOIN dbCreditDetails.credit_details cd
			ON cd.credit_details_id = t.credit_details_id
		INNER JOIN userdata.accounts a
			ON a.account_id = cd.intAccountId
		INNER JOIN userdata.users u
			ON u.customer_id = a.customer_id
		INNER JOIN userdata.services s
			ON s.user_id = u.user_id
WHERE
			t.transaction_id = :intTransactionId
			AND length(s.username) < 30
