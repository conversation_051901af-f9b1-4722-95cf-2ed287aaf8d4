server: coredb
role: slave
rows: multiple
statement:

SELECT 
		s.service_id,
		s.user_id,
		s.isp,
		s.username,
		s.password,
		s.cli_number,
		s.type,
		s.status,
		s.enddate,
		s.next_invoice,
		s.invoice_period,
		s.next_invoice_warned,
		s.invoice_day,
		s.authorised_switch_payment,
		s.bolMailOptOut
FROM 
		dbCreditDetails.credit_details cd
		INNER JOIN dbCreditDetails.tblCreditDetailsStatus cds
			ON cds.intCreditDetailsStatusId = cd.intCreditDetailsStatusId
		INNER JOIN userdata.accounts a
			ON a.account_id = cd.intAccountId
		INNER JOIN userdata.customers c
			ON c.customer_id = a.customer_id
		INNER JOIN userdata.services s
			ON s.user_id = c.primary_user_id
WHERE
		cds.vchHandle = 'ACTIVE' AND cd.credit_details_id = :intCreditDetailsId
		and length(s.username) < 30
