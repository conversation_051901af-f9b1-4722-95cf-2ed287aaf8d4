<?php


	//////////////////////////////////////////////////
	// Name: split_PrimitivesInsertIdGet
	//
	// Purpose: Get the ID generated from the previous INSERT operation
	//
	// Arguments: $dbConnection (the database connection handle)
	//
	// Returns: returns the ID generated for an AUTO_INCREMENT column by the
	// previous INSERT query using the given link_identifier.
	// If link_identifier isn't specified, the last opened link is assumed.
	//////////////////////////////////////////////////////////////

	function split_PrimitivesInsertIdGet ($dbConnection)
	{
		// simply return the insert id from mysql
		return mysql_insert_id ($dbConnection['handle']);

	}

?>