<?php


/////////////////////////////////////////////////////////////////////
// Name      : split_tickets_ticket_get
// Purpose   : Return a Question record
// Arguments : $ticket_id (The ID of the required ticket)
// Returns   : A Question record
//////////////////////////////////////////////////////////////////////

function split_tickets_ticket_get(
    $ticket_id,
    $dont_cache = false,
    $bolUseArchive = false
) {
    global $global_ticket_cache;

    // If the cached version is acceptable, use it.
    if (!$dont_cache && isset($global_ticket_cache[$ticket_id])) {
        // Make the entry the most recently used one
        // (move the entry to the end of the array!)
        $ticket = $global_ticket_cache[$ticket_id];
        unset($global_ticket_cache[$ticket_id]);
        $global_ticket_cache[$ticket_id] = $ticket;

        return $ticket;
    }

    // If the entry isn't in the cache, make sure that there is room for it...
    if (!isset($global_ticket_cache[$ticket_id])) {
        // If the cache is full, discard the least recently used entry
        if (is_array($global_ticket_cache) && count($global_ticket_cache) >= 30) {
            // Discard the cache entry at the beginning of the array
            reset($global_ticket_cache);
            list($key) = each($global_ticket_cache);
            unset($global_ticket_cache[$key]);
        }
    } else {
        // The entry is in the cache, so remove it from the array
        unset($global_ticket_cache[$ticket_id]);
    }

    $strDb = $bolUseArchive ? 'tickets_archive' : 'tickets';

    $connection = get_named_connection($strDb, !$bolUseArchive);

    if (!$connection && $bolUseArchive == true) {
        return false;
    }

    $ticket_id = addslashes($ticket_id);

    // We had to split the Sms 2 stuff out to another table which introduces a left outer join here.
    // Get the basic Question information
    $result = mysql_query(
        "SELECT SQL_NO_CACHE *
                                 FROM tickets
                                 LEFT
                                OUTER
                                 JOIN tblTicketSms
                                   ON tickets.ticket_id = tblTicketSms.intTicketId
                                WHERE tickets.ticket_id = '$ticket_id'",
        $connection
    )
    or report_error(__FILE__, __LINE__, mysql_error($connection));

    $ticket = mysql_fetch_array($result, MYSQL_ASSOC);

    mysql_free_result($result);

    if (!empty($ticket)) {

        $query = 'SELECT th.intComplaintTicketHistoryId,
                             tc.ticket_id AS intTicketId,
                             th.dtmFlaggedAsComplaint,
                             th.intFlaggedAsComplaintByActorId,
                             baf.username AS strFlaggedAsComplaintByUsername,
                             th.intFlaggedAsComplaintTicketContactId,
                             th.dtmResolved,
                             th.intResolvedByActorId,
                             bar.username AS strResolvedByUsername,
                             th.intResolvedTicketContactId,
                             adr.dtmADRSent
                        FROM tblComplaintTicketHistory th
                        JOIN ticket_contacts tc
                          ON th.intFlaggedAsComplaintTicketContactId = tc.contact_id
                        LEFT
                        JOIN PlusnetSession.tblBusinessActor baf
                          ON th.intFlaggedAsComplaintByActorId = baf.actorID
                        LEFT
                        JOIN PlusnetSession.tblBusinessActor bar
                          ON th.intResolvedByActorId = bar.actorID
                        LEFT
                        JOIN tblComplaintTicketADR adr
                          ON tc.ticket_id = adr.intTicketId
                       WHERE tc.ticket_id = %d
                       ORDER BY dtmFlaggedAsComplaint ASC';

        $query = sprintf(
            $query,
            $ticket_id
        );

        $dbConnection = get_named_connection_with_db($strDb, !$bolUseArchive);
        $result = PrimitivesQueryOrExit($query, $dbConnection, 'query failed on', false);
        $complaintInfo = PrimitivesResultsAsArrayGet($result);
        $ticket['complaintHistory'] = $complaintInfo;

        $billingComplaintInfo = ticketsAddBillingComplaint($ticket_id, $strDb);

        $ticket['billingComplaintHistory'] = $billingComplaintInfo;

        if ($ticket && !$bolUseArchive) {
            // Append this entry to the end of the cache
            $global_ticket_cache[$ticket_id] = $ticket;
        }
    }
    return $ticket;
}

/**
 * Gets the billing complaint history for a ticket and adds this info to the tickets
 * array (as an array within an array - called billingComplaintInfo)
 *
 * @param String $ticket_id ticket ID
 *
 * @param String $strDb     database
 *
 * @return $billingComplaintInfo
 */
function ticketsAddBillingComplaint(
    $ticket_id,
    $strDb
) {
    $billingComplaintInfo = array();
    $query = '
        SELECT
              tc.ticket_id as intTicketId,
               bc.intFlaggedAsBillingComplaintContactId,
               bcr.vchBillingComplaintReason as strBillingComplaintReason
          FROM ticket_contacts tc
          JOIN tblBillingComplaint bc
            ON tc.contact_id = bc.intFlaggedAsBillingComplaintContactId
          LEFT
          JOIN tblBillingComplaintReason bcr
            ON bcr.intBillingComplaintReasonId = bc.intBillingComplaintReasonId
          WHERE tc.ticket_id = %d
          ORDER BY intFlaggedAsBillingComplaintContactId ASC';

    $query = sprintf($query, $ticket_id);

    $dbConnection = get_named_connection_with_db($strDb);
    $result = PrimitivesQueryOrExit($query, $dbConnection, 'query failed on', false);
    $billingComplaintInfo = PrimitivesResultsAsArrayGet($result);
    return $billingComplaintInfo;
}
