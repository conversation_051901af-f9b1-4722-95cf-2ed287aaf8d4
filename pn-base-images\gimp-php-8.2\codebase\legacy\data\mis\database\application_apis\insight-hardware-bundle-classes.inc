<?php
	///////////////////////////////////////////////////////////////////////////
	// hardware-bundle-classes.inc
	// <PERSON>, October 2002
	// Revised <PERSON>, June 2004
	// $Id: insight-hardware-bundle-classes.inc,v 1.6 2007-06-29 08:13:20 kprzybyszewski Exp $
	//
	// This file contains all classes used by the hardware bundle system for Insight
	// hardware.
	//
	// IMPORTANT:
	// The database has been designed to allow each item of the order to be
	// handled by a different supplier, thereby having its own status. However,
	// these classes have been written with the assumption that the supplier
	// and status for each item in an order are the same.
	//
	// Functions marked *** STATIC *** don't require an instansiated object to be used.
	// i.e. You can just call classname::functionname()
	//
	// Hungarian notation used:
	//
	// str : String
	// int : Integer
	// arr : Array of items (not a row from a database)
	// r   : Record (associative array, eg a row from the database)
	// bin : Boolean
	// h   : Handle (eg database connection or result, or a file handle)
	// m_  : Class member variable (shouldn't be touched outside the class)
	//

	/*
	Please do not remove this file ! We do not use Insight as hardware supplier any more but we have Insight ordered hardware in our database.
	*/

	include '/local/data/mis/database/application_apis/HardwareBundle/InsightUKHardwareBundle.class.php';
	include '/local/data/mis/database/application_apis/HardwareBundle/InsightUKHardwareBundleSupplier.class.php';
	include '/local/data/mis/database/application_apis/HardwareBundle/InsightUKHardwareBundleType.class.php';
	include '/local/data/mis/database/application_apis/HardwareBundle/InsightUKHardwareRmaProcess.class.php';
?>
