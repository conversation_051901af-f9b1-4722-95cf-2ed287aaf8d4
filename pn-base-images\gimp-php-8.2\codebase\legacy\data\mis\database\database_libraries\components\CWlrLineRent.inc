<?php

/**
 * Access library for WLR Line Rental component
 * $Id: CWlrLineRent.inc,v 1.27.2.2 2009/07/06 07:48:21 ssmith Exp $
 *
 * @package    Core
 * @subpackage WLR
 * @access     public
 * <AUTHOR> <<EMAIL>>
 */

require_once '/local/data/mis/database/database_libraries/components/CWlrProductComponent.inc';
require_once '/local/data/mis/database/database_libraries/components/CWlrProduct.inc';
require_once '/local/data/mis/database/application_apis/wlr/spg/Wlr_OrderType_CancelOther.class.php';
require_once '/local/data/mis/database/application_apis/wlr/spg/Wlr_Xml_Validation.class.php';
require_once '/local/data/mis/database/database_libraries/programme-tool-access.inc';
require_once '/local/data/mis/database/database_libraries/components/CWlrFavouriteNumber.inc';
require_once '/local/data/mis/database/database_libraries/components/CWlrProvisionType.class.php';

/**
 * WLR Line Rental Component
 *
 * Component for handling the Line Rental charge
 *
 * @access  public
 * @package Core
 * <AUTHOR> <<EMAIL>>
 */
class CWlrLineRent extends CWlrProductComponent
{
    //
    // Properties
    //
    public $arrWlrLineRentConfig;

    /**
     * Keeps sum of all favourite number discounts
     *
     * @var integer
     */
    private $intFavouriteNumberDiscountPence = 0;

    /**
     * Constructor
     *
     * @param int $intProductComponentInstanceId Product component instance id
     *
     * @return CWlrLineRent
     */
    public function CWlrLineRent($intProductComponentInstanceId)
    {
        return $this->CProductComponent($intProductComponentInstanceId);
    }

    //
    // Configurator Methods
    //

    /////////////////////////////////////////////////////////////////////////////////////
    // These are inherited from CWlrProductComponent                                   //
    //     enable()                                                                    //
    //     disable()                                                                   //
    //     destroy()                                                                   //
    // For functionality pertinent to WLR Line Rent only, define onEnable, onDisable   //
    // and onDestroy methods here.                                                     //
    /////////////////////////////////////////////////////////////////////////////////////

    /**
     * Functionality for destroying the Line Rental component
     *
     * @param array $arrArgs Arguments
     *
     * <AUTHOR> Zaki" <<EMAIL>>
     * @access   private
     *
     * @return   boolean
     */
    public function onDestroy($arrArgs = array())
    {
        if (!\Plusnet\Feature\FeatureToggleManager::isOnFiltered(
            'RBM_MIGRATION_COMPLETE',
            null,
            null,
            null,
            $this->getServiceID()
        )
        ) {
            $bolScheduleCharges = isset($arrArgs['bolScheduleCharges']) ? $arrArgs['bolScheduleCharges'] : true;
            $bolPayCallCharges = isset($arrArgs['bolPayCallCharges']) ? $arrArgs['bolPayCallCharges'] : true;

            $arrInvoiceItems = array();
            $floAmountToCharge = 0;

            if ($bolScheduleCharges === true) {
                // Take any outstanding payments
                $arrScheduledPaymentID = $this->scheduleCharges(time());
                if (!empty($arrScheduledPaymentID) && $bolPayCallCharges) {
                    foreach ($arrScheduledPaymentID as $intScheduledPaymentID) {
                        $objScheduledPayment = new CProductComponentScheduledPayment($intScheduledPaymentID);

                        $arrInvoiceItems[] = array(
                            'amount'      => $objScheduledPayment->getBillingAmount(),
                            'description' => $objScheduledPayment->getDescription(),
                            'gross'       => true
                        );
                        $floAmountToCharge += $objScheduledPayment->getBillingAmount();
                    }

                    $intInvoiceID = CWlrProduct::takePayment(
                        $this->getServiceID(),
                        $arrInvoiceItems,
                        $floAmountToCharge,
                        'Outstanding Call Charges'
                    );

                    if (isset($intInvoiceID) && $intInvoiceID > 0) {
                        foreach ($arrScheduledPaymentID as $intScheduledPaymentID) {
                            $objScheduledPayment = new CProductComponentScheduledPayment($intScheduledPaymentID);

                            // DJM2013: as intLineItemId isn't populated above, we can't pass it to this method!
                            //$objScheduledPayment->markAsInvoiced($intInvoiceID, $arrInvoiceItems[0]['intLineItemId']);
                            $objScheduledPayment->markAsInvoiced($intInvoiceID, null);
                        }
                    }
                }
            }
        }
        // Terminate the current user config
        $this->expireUserConfig();

        return true;
    }

    /**
     * On Enable
     *
     * @param array $arrArgs Arguments
     *
     * @return boolean
     */
    public function onEnable($arrArgs = array())
    {
        if (isset($arrArgs['bolSyncStartWithEnableDate']) && isset($arrArgs['uxtEnableDate'])) {

            $strQuery = "UPDATE tblConfigWlrLineRent SET dtmStart = FROM_UNIXTIME({$arrArgs['uxtEnableDate']})
                         WHERE intProductComponentInstanceID = '{$this->getProductComponentInstanceID()}'";
            $dbhRead = get_named_connection_with_db('userdata');
            PrimitivesQueryOrExit($strQuery, $dbhRead, 'CWlrLineRent::onEnable', false);
        }

        return true;
    }

    //
    // Public Methods
    //

    /**
     * Get the Line Rent product start date
     *
     * <AUTHOR>
     * @access   public
     *
     * @return   date
     */
    public function getStartDate()
    {
        $arrConfig = $this->getUserConfig();

        return $arrConfig['dtmStart'];
    }

    /**
     * Get the Line Rent product component config
     *
     * <AUTHOR> Smith" <<EMAIL>>
     * @access   public
     *
     * @return   array   Table result array
     */
    public function getUserConfig()
    {
        $intProductComponentInstanceId = $this->getProductComponentInstanceID();

        $strQuery = "SELECT cwlr.*, ts.vchHandle AS vchSupplierHandle,
                            UNIX_TIMESTAMP(cwlr.dtmExpectedTransferDate) as uxtActivationDate,
                            sp.vchHandle as vchSupplierPlatform
                       FROM tblConfigWlrLineRent AS cwlr
                 INNER JOIN products.tblSupplierPlatform as sp
                         ON cwlr.intSupplierPlatformId = sp.intSupplierPlatformID
                  LEFT JOIN products.tblSupplier AS ts
                         ON ts.intSupplierID = sp.intSupplierID
                      WHERE cwlr.intProductComponentInstanceID = '{$intProductComponentInstanceId}'
                        AND dtmEnd IS NULL";

        // Use master DB, under some circumstances (ie. after self::expireUserConfig)
        // it can return old data what causes duplicate database entries
        $dbhRead = get_named_connection_with_db('userdata');

        if (false === ($result = PrimitivesQueryOrExit($strQuery, $dbhRead, 'CWlrLineRent::getUserConfig'))) {
            return false;
        }

        $arrResults = PrimitivesResultGet($result);

        return (isset($arrResults) && !empty($arrResults)) ? $arrResults : false;
    }

    /**
     * Creates the entry in the WLR Line Rent config table
     *
     * @param string $strCli                  Telephone number to provision
     * @param int    $intCallPlanId           The Call Plan ID of the user's product
     * @param string $strBTRefNumber          BT account ref number
     * @param string $strHandle               Supplier platform handle
     * @param string $strDateOfChange         Optional date of change - defaults to now for new entry due to signup
     *                                        or passed date for a change of address.
     * @param int    $intSupplierPlatformId   Optional Suppier Platform id
     * @param int    $strExpectedTransferDate Expected Transfer Date (optional)
     *
     * <AUTHOR> Jones" <<EMAIL>>
     *
     * @return   integer The ID of the config
     */
    public function createUserConfig(
        $strCli,
        $intCallPlanId,
        $strBTRefNumber,
        $strHandle,
        $strDateOfChange = '',
        $intSupplierPlatformId = '',
        $strExpectedTransferDate = ''
    ) {
        if (!isset($intCallPlanId) || !is_numeric($intCallPlanId)) {
            $intOldCallPlanId = $intCallPlanId;
            // Get the call plan ID
            $arrCallPlan = $this->getCallPlan();

            $intCallPlanId = $arrCallPlan['intCallPlanId'];

            if (!isset($intCallPlanId) || !is_numeric($intCallPlanId)) {
                pt_raise_autoproblem(
                    'Wlr - Incorrect CallPlanId',
                    'AUTO-PROBLEM: intCallPlanId has not been set!',
                    "(CWlrLineRent.inc - createUserConfig) intOldCallPlanId =
                        $intOldCallPlanId, intCallPlanId = $intCallPlanId, strCli = $strCli",
                    "CLI: $strCli is also affected.\n"
                );
            }
        } // if (!isset($intCallPlanId) || !is_numeric($intCallPlanId))

        if ('' != $strDateOfChange) {

            $strDateOfChange = "'$strDateOfChange'";
        } else {

            $strDateOfChange = 'NOW()';
        }

        if ('' != $strExpectedTransferDate) {

            $strExpectedTransferDate = "'$strExpectedTransferDate'";
        } else {

            // If no expected transfer date is given then default to NULL
            $strExpectedTransferDate = 'NULL';
        }

        $intBTActivationCharge = $this->getBTActivationCharge();

        // added safety, as I've seen CLIs with *leading* whitespace in places...
        // that could *potentially* cause orders to be rejected by the provisioning
        // system if a BT ref number or CLI is apparently too long, all because of
        // unnecessary whitespace.

        // we also need to make sure there's no intra string whitespace as well, so we replace as well as trim

        $strCli = trim(str_replace(' ', '', $strCli));
        $strBTRefNumber = trim(Wlr_Xml_Validation::MakeBTAccountNumberCompatibleWithProvisioning($strBTRefNumber));

        if ('' == $strHandle) {
            $strHandle = CWlrProvisionType::WLR2;
        }

        if ('' == $intSupplierPlatformId) {

            $objCWlrProvisionType = new CWlrProvisionType();
            $intSupplierPlatformId = $objCWlrProvisionType->getSupplierPlatformIdFromHandle($strHandle);
        }

        $intProductComponentInstanceID = $this->getProductComponentInstanceID();

        $dbhWrite = get_named_connection_with_db('userdata');

        if (empty($intBTActivationCharge)) {
            $intBTActivationCharge = 0;
        }

        $strCreateConfig = "INSERT INTO tblConfigWlrLineRent
                            (
                                intProductComponentInstanceID,
                                vchCli,
                                intSupplierPlatformId,
                                vchSupplierReference,
                                intCallPlanId,
                                dtmStart,
                                intBTActivationCharge,
                                dtmExpectedTransferDate
                            )
                            SELECT
                               '{$intProductComponentInstanceID}',
                               '{$strCli}',
                               '{$intSupplierPlatformId}',
                               '{$strBTRefNumber}',
                               '{$intCallPlanId}',
                               $strDateOfChange,
                               '{$intBTActivationCharge}',
                               $strExpectedTransferDate
                            FROM products.tblSupplier
                            WHERE vchHandle = 'BT'
                            LIMIT 1";
        if (false === (PrimitivesQueryOrExit(
                $strCreateConfig,
                $dbhWrite,
                'CWlrLineRent::createConfig{CreateConfig}',
                true
            )
            )
        ) {
            // Above will fatal, but if we change this we probably want to handle it better
            return false;
        }

        $intConfigId = PrimitivesInsertIdGet($dbhWrite);

        return $intConfigId;
    }

    /**
     * Duplicate an old User Config
     *
     * @param int  $intFromLineRentInsId  Line rental id from
     * @param int  $intNewCallPlanId      New call plan id
     * @param bool $bolHpRef2009Migration If HpRef2009 migration
     *
     * @return bool
     */
    public function transferUserConfig($intFromLineRentInsId, $intNewCallPlanId, $bolHpRef2009Migration = false)
    {
        $intProductComponentId = $this->getProductComponentInstanceID();

        $dbhWrite = get_named_connection_with_db('userdata');

        $intNewCreditLimit = 'NULL';

        if ($bolHpRef2009Migration) {
            $strGetOldCreditLimit = "SELECT intCustomCreditLimitPence
                                       FROM tblConfigWlrLineRent
                                      WHERE intProductComponentInstanceID = '$intFromLineRentInsId'
                                        AND dtmStart IS NOT NULL
                                        AND dtmEnd IS NULL";
            $resResult = PrimitivesQueryOrExit(
                $strGetOldCreditLimit,
                $dbhWrite,
                'CWlrLineRent::transferUserConfig',
                true
            );
            $intOldCreditLimt = PrimitivesResultGet($resResult, 'intCustomCreditLimitPence');

            if ($intOldCreditLimt > 0) {
                $intNewCreditLimit = $intOldCreditLimt;
            }
        }

        $arrConfigWlrLineRent = array();

        $strQuery = "SELECT vchCli, intOrderID, intSupplierPlatformId,
                            vchSupplierReference,
                            intBTActivationCharge,
                            dtmExpectedTransferDate
                       FROM tblConfigWlrLineRent
                      WHERE intProductComponentInstanceID = {$intFromLineRentInsId}
                        AND dtmStart IS NOT NULL
                        AND dtmEnd IS NULL
                   ORDER BY intConfigID DESC
                      LIMIT 1";

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhWrite, 'CWlrLineRent::transferUserConfig', true);
        $arrConfigWlrLineRent = PrimitivesResultGet($resResult);

        if (empty($arrConfigWlrLineRent)) {

            $strTemplateName = 'activeWlrLineRentConfigMissing';
            $arrData = array(
                'serviceId'                     => $this->getServiceID(),
                'intProductComponentInstanceId' => $intFromLineRentInsId,
                'intProductComponentId'         => $intProductComponentId,
                'backTrace'                     => print_r(debug_backtrace(), true)
            );

            // Raise an auto problem (P3) detailing about the
            // missing active Wlr line rent component
            $this->raiseAutoProblem($strTemplateName, $arrData);

            // Trying with inactive Wlr line rent component
            $strQuery = "SELECT vchCli,
                                intOrderID,
                                intSupplierPlatformId,
                                vchSupplierReference,
                                intBTActivationCharge,
                                dtmExpectedTransferDate
                           FROM tblConfigWlrLineRent
                          WHERE intProductComponentInstanceID = {$intFromLineRentInsId}
                       ORDER BY intConfigID DESC
                          LIMIT 1";

            $resResult = PrimitivesQueryOrExit($strQuery, $dbhWrite, 'CWlrLineRent::transferUserConfig', true);
            $arrConfigWlrLineRent = PrimitivesResultGet($resResult);

            if (empty($arrConfigWlrLineRent)) {

                // Defaulting to WLR2 in the absence of a Wlr line rent component
                $strHandle = CWlrProvisionType::WLR2;
                $objCWlrProvisionType = new CWlrProvisionType();

                $arrConfigWlrLineRent['intSupplierPlatformId'] =
                    $objCWlrProvisionType->getSupplierPlatformIdFromHandle($strHandle);
            }
        }

        $strQuery = "UPDATE tblConfigWlrLineRent
                        SET vchCli = '{$arrConfigWlrLineRent['vchCli']}',
                            intOrderID = '{$arrConfigWlrLineRent['intOrderID']}',
                            intSupplierPlatformId = '{$arrConfigWlrLineRent['intSupplierPlatformId']}',
                            vchSupplierReference = '{$arrConfigWlrLineRent['vchSupplierReference']}',
                            intCallPlanId = '{$intNewCallPlanId}',
                            dtmStart = NOW(),
                            intBTActivationCharge = '{$arrConfigWlrLineRent['intBTActivationCharge']}',";
        if ($arrConfigWlrLineRent['dtmExpectedTransferDate'] == null
            || $arrConfigWlrLineRent['dtmExpectedTransferDate'] == "0000-00-00 00:00:00"
        ) {
            $strQuery .= "dtmExpectedTransferDate = NULL,";
        } else {
            $strQuery .= "dtmExpectedTransferDate = '{$arrConfigWlrLineRent['dtmExpectedTransferDate']}',";
        }
        $strQuery .= "intCustomCreditLimitPence = {$intNewCreditLimit}
                       WHERE intProductComponentInstanceID = {$intProductComponentId}";

        if (false === (PrimitivesQueryOrExit($strQuery, $dbhWrite, 'CWlrLineRent::transferUserConfig', true))) {
            return false;
        }

        if (PrimitivesAffectedRowsGet($dbhWrite) == 0) {
            // We failed to update the existing service, so we better create a new one instead!
            $strQuery = "INSERT INTO tblConfigWlrLineRent
                                 SET vchCli = '{$arrConfigWlrLineRent['vchCli']}',
                                     intOrderID = '{$arrConfigWlrLineRent['intOrderID']}',
                                     intSupplierPlatformId = '{$arrConfigWlrLineRent['intSupplierPlatformId']}',
                                     vchSupplierReference = '{$arrConfigWlrLineRent['vchSupplierReference']}',
                                     intCallPlanId = '{$intNewCallPlanId}',
                                     dtmStart = NOW(),
                                     intBTActivationCharge = '{$arrConfigWlrLineRent['intBTActivationCharge']}',";
            if (!($arrConfigWlrLineRent['dtmExpectedTransferDate'] == null
                || $arrConfigWlrLineRent['dtmExpectedTransferDate'] == "0000-00-00 00:00:00")
            ) {
                $strQuery .= "dtmExpectedTransferDate = '{$arrConfigWlrLineRent['dtmExpectedTransferDate']}',";
            }
            $strQuery .= "intCustomCreditLimitPence = {$intNewCreditLimit},
                                     intProductComponentInstanceID = {$intProductComponentId}";

            if (false === (PrimitivesQueryOrExit($strQuery, $dbhWrite, 'CWlrLineRent::transferUserConfig', true))) {
                return false;
            }
        }

        // We do the expiration of the old config here, because we can't duplicate CLI
        $objOldLineRentComponent = &CProductComponent::createInstance($intFromLineRentInsId);
        $objOldLineRentComponent->expireUserConfig();

        return true;
    }

    /**
     * Expire a user config, either because we are creating a new one or terminating an old one
     *
     * @param string $strDateOfChange  Optional date of change - defaults to now for new entry due to signup
     *                                 or passed date for a change of address.
     *
     * @return bool
     */
    public function expireUserConfig($strDateOfChange = '')
    {
        if (false === ($arrConfig = $this->getUserConfig()) || empty($arrConfig)) {
            return false;
        }

        if ('' != $strDateOfChange) {

            $strDateOfChange = "'$strDateOfChange'";
        } else {

            $strDateOfChange = 'NOW()';
        }

        $intConfigId = $arrConfig['intConfigID'];

        $dbhWrite = get_named_connection_with_db('userdata');

        $strExpireConfig = "UPDATE tblConfigWlrLineRent \n" .
            "  SET dtmEnd = $strDateOfChange \n" .
            "  WHERE intConfigID = $intConfigId \n";

        if (false === (PrimitivesQueryOrExit($strExpireConfig, $dbhWrite, 'CWlrLineRent::expireUserConfig', true))) {
            return false;
        }

        return true;
    }

    /**
     * Update user config
     *
     * @param array $arrNewConfig New config
     *
     * @return array
     */
    public function updateUserConfig($arrNewConfig)
    {
        $arrUserConfig = $this->getUserConfig();

        if (isset($arrNewConfig['vchCli'])) {
            $arrUserConfig['vchCli'] = $arrNewConfig['vchCli'];
        }

        if (isset($arrNewConfig['vchSupplierReference'])) {
            $arrUserConfig['vchSupplierReference'] = $arrNewConfig['vchSupplierReference'];
        }

        if (isset($arrNewConfig['intBTActivationCharge'])) {
            $arrUserConfig['intBTActivationCharge'] = $arrNewConfig['intBTActivationCharge'];
        }

        $intConfigID = $arrUserConfig['intConfigID'];

        $db = get_named_connection_with_db('userdata');

        $vchSupplierReference = PrimitivesRealEscapeString($arrUserConfig['vchSupplierReference'], $db);

        // We need to ensure that there's no whitespace in the CLI before writing to the DB
        $strCli = trim(str_replace(' ', '', $arrUserConfig['vchCli']));
        $strCli = PrimitivesRealEscapeString($strCli, $db);

        $strQuery =
            "UPDATE tblConfigWlrLineRent SET vchCli = '$strCli', vchSupplierReference = '$vchSupplierReference'" .
            " WHERE intConfigID = $intConfigID";

        return PrimitivesQueryOrExit($strQuery, $db, 'CWlrLineRent::updateCli', false);
    }

    /**
     * Get the Line Rent product component config
     *
     * <AUTHOR>
     * @return   array
     */
    public function getConfig()
    {
        if (!$this->arrWlrLineRentConfig) {

            $dbhRead = get_named_connection_with_db('dbProductComponents_reporting');

            $strWlrLineRentConfig
                = "SELECT intCancellationFeePence/100 AS intCancellationFee,
                          intDowngradeFeePence/100 AS decDowngradeFee,
                          intNoDDAdminChargesPence,
                          intNewProvideFeePence,
                          intBTActivationCharge,
                          intCallBarringChargePence,
                          intRenumberingChargePence,
                          bolDisplaySplitCharge  " .
                " FROM userdata.tblProductComponentInstance pci \n" .
                " INNER JOIN tblProductComponent pc \n" .
                " ON pc.intProductComponentID = pci.intProductComponentID \n" .
                " INNER JOIN tblProductComponentConfig pcc \n" .
                " ON pcc.intProductComponentID = pc.intProductComponentID \n" .
                " INNER JOIN tblWlrLineRentConfig cc \n" .
                " ON cc.intProductComponentConfigID = pcc.intProductComponentConfigID \n" .
                ' WHERE pci.intProductComponentInstanceID = ' . $this->getProductComponentInstanceID() . " \n" .
                ' AND pcc.intServiceComponentProductID  = ' . $this->getServiceComponentProductID() . " \n";


            if (false === ($resWlrLineRentConfig = PrimitivesQueryOrExit(
                    $strWlrLineRentConfig,
                    $dbhRead,
                    'CWlrLineRent::getConfig',
                    true
                )
                )
            ) {
                // If we turn off exit on failure above we should handle failure here effectively
                $bolFalse = false;

                return $bolFalse;
            }

            if (PrimitivesNumRowsGet($resWlrLineRentConfig) > 0) {
                $this->arrWlrLineRentConfig = PrimitivesResultGet($resWlrLineRentConfig);
            }
        }

        return $this->arrWlrLineRentConfig;
    }

    /**
     * Changes cli assigned to the line rent component
     *
     * @param string $strNewCli       A new telephone number
     * @param string $strDateOfChange An optional date that the number changed on (yyyy-mm-dd [hh:mm:ss]),
     *                                blank to use NOW
     *
     * <AUTHOR> <<EMAIL>>
     *
     * @return   int     A new config ID or false if usuccessfull
     */
    public function changeCli($strNewCli, $strDateOfChange = '')
    {
        //Passing a null handle, so that createUserConfig can use intSupplierPlatformId
        //returned from $arrOldConfig.
        $strHandle = '';

        //get the current config
        $arrOldConfig = $this->getUserConfig();

        if (!$arrOldConfig) {
            return false;
        }
        // Double-check we're not trying to change to cli to the same as it is already...
        if ($strNewCli == $arrOldConfig['vchCli']) {

            // This is not an error, just a no-op
            return $arrOldConfig['intConfigID'];
        }

        //expire current config
        if (!($this->expireUserConfig($strDateOfChange))) {
            return false;
        }

        //create a new one using old tariff plan and BT ref number but new cli
        $newConfigId = $this->createUserConfig(
            $strNewCli,
            $arrOldConfig['intCallPlanId'],
            $arrOldConfig['vchSupplierReference'],
            $strHandle,
            $strDateOfChange,
            $arrOldConfig['intSupplierPlatformId'],
            $arrOldConfig['dtmExpectedTransferDate']
        );

        return $newConfigId;
    }

    /**
     * Gets the call plan details for this Line Rent Product instance
     *
     * <AUTHOR> Jones" <<EMAIL>>
     * @access   public
     *
     * @return   array   Call plan details
     */
    public function getCallPlan()
    {
        $dbhRead = get_named_connection_with_db('userdata_reporting');

        $strCallPlan = 'SELECT cp.intCallPlanId, cp.intProductComponentConfigId, ' .
            '       cp.vchDisplayName, cp.intChargingIntervalSeconds, ' .
            '       IF(cwlr.intCustomCreditLimitPence IS NOT NULL, cwlr.intCustomCreditLimitPence, ' .
            '       cp.intCreditLimitPence) AS intCreditLimitPence, ' .
            '       t.intCostIncVatPence ' .
            '  FROM userdata.tblProductComponentInstance pci ' .
            '    INNER JOIN userdata.tblConfigWlrLineRent cwlr ' .
            '      ON pci.intProductComponentInstanceId = cwlr.intProductComponentInstanceId ' .
            '    INNER JOIN userdata.components com ' .
            '      ON pci.intComponentID = com.component_id ' .
            '    INNER JOIN products.service_components sc ' .
            '      ON com.component_type_id = sc.service_component_id ' .
            '    INNER JOIN products.tblServiceComponentProduct scp ' .
            '      ON sc.service_component_id = scp.intServiceComponentId ' .
            '    INNER JOIN dbProductComponents.tblProductComponentConfig pcc ' .
            '      ON scp.intServiceComponentProductId = pcc.intServiceComponentProductId ' .
            '    INNER JOIN dbProductComponents.tblCallPlan cp ' .
            '      ON pcc.intProductComponentConfigId = cp.intProductComponentConfigId ' .
            '    INNER JOIN dbProductComponents.tblTariff t ' .
            '      ON pci.intTariffID = t.intTariffID ' .
            '    WHERE cp.dtmEnd IS NULL' .
            '    AND cwlr.dtmEnd IS NULL' .
            '    AND pci.intProductComponentInstanceID = ' . $this->getProductComponentInstanceID();
        if (false === ($resCallPlan = PrimitivesQueryOrExit($strCallPlan, $dbhRead, '', true))) {
            // If we disable exit_on_failure handle this event
            return false;
        }

        $intRows = PrimitivesNumRowsGet($resCallPlan);
        switch ($intRows) {
            case 0:
                return false;
                break;
            case 1:
                return PrimitivesResultGet($resCallPlan);
                break;
            default:
                throw new OutOfRangeException(
                    "Multiple call plans returned for WLR Line Rent Product Component : "
                    . $this->getProductComponentInstanceID()
                );
                break;
        } // switch ($intRows)

        // Should never get here
        return false;
    }

    /**
     * Update expected transfer date
     *
     * @param uxt $uxtTransferDate Transfer date
     *
     * @access   public
     *
     * @return bool
     */
    public function updateExpectedTransferDate($uxtTransferDate)
    {
        $arrUserConfig = $this->getUserConfig();

        $intUserConfigId = $arrUserConfig['intConfigID'];

        $dbhWrite = get_named_connection_with_db('userdata');

        $strUpExpTransDate = "UPDATE tblConfigWlrLineRent \n" .
            "  SET dtmExpectedTransferDate = FROM_UNIXTIME($uxtTransferDate) \n" .
            "  WHERE intConfigID = $intUserConfigId";

        if (false === (PrimitivesQueryOrExit(
                $strUpExpTransDate,
                $dbhWrite,
                'CWlrLineRent::updateExpectedTransferDate',
                false
            )
            )
        ) {
            return false;
        }

        return true;
    }

    /**
     * Get expected transfer date
     *
     * @access   public
     *
     * @return date/bool
     */
    public function getExpectedTransferDate()
    {
        $arrUserConfig = $this->getUserConfig();

        $intUserConfigId = $arrUserConfig['intConfigID'];

        $dbhRead = get_named_connection_with_db('userdata_reporting');

        $strGetExpTransDate = "SELECT UNIX_TIMESTAMP(dtmExpectedTransferDate) AS uxtExpectedTransferDate\n" .
            "  FROM tblConfigWlrLineRent \n" .
            "  WHERE intConfigID = $intUserConfigId";

        if (false === ($resExpectedTransferDate = PrimitivesQueryOrExit(
                $strGetExpTransDate,
                $dbhRead,
                'CWlrLineRent::getExpectedTransferDate',
                false
            )
            )
        ) {
            return false;
        }

        $uxtExpectedTransferDate = PrimitivesResultGet($resExpectedTransferDate, 'uxtExpectedTransferDate');

        return $uxtExpectedTransferDate;
    }

    /**
     * Get Scheduled cancellation
     *
     * @access   public
     *
     * @return bool/date
     */
    public function getScheduledCancellation()
    {
        $arrUserConfig = $this->getUserConfig();

        $intProductComponentInstanceID = $arrUserConfig['intProductComponentInstanceID'];

        $dbhRead = get_named_connection_with_db('userdata_reporting');

        $strGetExpTransDate = "SELECT dtmExpectedTransferDate FROM tblConfigWlrLineRent \n" .
            "  WHERE intProductComponentInstanceID = $intProductComponentInstanceID";

        if (false === ($resExpectedTransferDate = PrimitivesQueryOrExit(
                $strGetExpTransDate,
                $dbhRead,
                'CWlrLineRent::getExpectedTransferDate',
                false
            )
            )
        ) {
            return false;
        }

        $dtmExpectedTransferDate = PrimitivesResultGet($resExpectedTransferDate, 'dtmExpectedTransferDate');

        return $dtmExpectedTransferDate;
    }

    /**
     * Cancel cancel other order
     *
     * @param bool $bolCancelledByThirdPartySoDontTriggerAnOrderSubmission If cancelled by 3rd party
     *
     * @access   public
     *
     * @return bool
     */
    public function cancelCancelOtherOrder($bolCancelledByThirdPartySoDontTriggerAnOrderSubmission = false)
    {
        $strOrderTriggerClause = $bolCancelledByThirdPartySoDontTriggerAnOrderSubmission ? ', dtmEnd=NOW()' : '';

        $intProdCompInstanceID = $this->getProductComponentInstanceID();
        $dbhWrite = get_named_connection_with_db('wlr_prov');
        $strUpdateCancelOrder = "UPDATE dbWLRProvisioning.tblCancelOtherOrder
                                 SET bolCustomerChoseToStay=true {$strOrderTriggerClause}
                                 WHERE intLineRentProductComponentInstanceID = $intProdCompInstanceID";

        // ssmith: unless I'm missing the point somewhere, I don't think we should be setting
        // the dtmEnd field to now() ?? Correct me if I'm wrong.

        // Update: turns out that we can have Cancel Other-esq situations caused by a 3rd-party.
        // In this case we can use the dtmEnd field to signal this. We still set the flag that represents
        // the customer chose to stay with us (they did, by proxy - 3rd party said so)
        // Kind of non-intuitive, as it would have probably been best to use the dtmStart/dtmEnd fields for
        // the provisioning system to recognise as submitting a Cancel Other order, and have a field like
        // 'bolCancelledByThirdParty' instead that would exclude it from the provisioning system.

        // too risky to change the system now though, as I don't want to risk breaking any existing stuff :(

        if (false === (PrimitivesQueryOrExit(
                $strUpdateCancelOrder,
                $dbhWrite,
                'CWlrLineRent::cancelCancelOtherOrder',
                false
            )
            )
        ) {
            return false;
        }

        return true;
    }

    /**
     * Is transferrng out
     *
     * @return boolean
     */
    public function isTransferringOut()
    {
        $intLineRentId = $this->getProductComponentInstanceID();

        $dbRead = get_named_connection_with_db('wlr_prov_reporting');

        $strQuery = "SELECT COUNT(*) AS n FROM tblCancelOtherOrder
                     WHERE intLineRentProductComponentInstanceID = $intLineRentId
                     AND bolCustomerChoseToStay = 0";

        $res = PrimitivesQueryOrExit($strQuery, $dbRead, 'Is customer transferring out?', false);

        if (!$res) {
            return false;
        }

        return PrimitivesResultGet($res, 'n') ? true : false;
    }

    /**
     * Has transferred out
     *
     * @return boolean
     */
    public function hasTransferredOut()
    {
        $intLineRentId = $this->getProductComponentInstanceID();

        $dbRead = get_named_connection_with_db('wlr_prov_reporting');

        $strQuery = "SELECT IF(dteExpectedCancellationDate <= NOW(), 1, 0) AS bolHasTranferred
                     FROM tblCancelOtherOrder
                     WHERE intLineRentProductComponentInstanceID = $intLineRentId
                     AND bolCustomerChoseToStay = 0";

        $res = PrimitivesQueryOrExit($strQuery, $dbRead, 'Has customer transferred out?', false);

        if (!$res) {
            return false;
        }

        return PrimitivesResultGet($res, 'bolHasTranferred') ? true : false;
    }

    ////////////////////////////////////////////////////////////////////////////////

    /*
        ------------------------------
        getPotentialCancelOtherOrder()
        ------------------------------

        Returns information about a line rent component's Cancel Other status.

        Returns false on error, or an array of details on success.

        The 'potential' part of the method name means exactly that - if no Cancel Other
        order is available, it will return an empty array.
    */

    /**
     * Get potential cancel other order
     *
     * @return type
     */
    public static function getPotentialCancelOtherOrder()
    {
        self::includeLegacyFiles();

        $arrResult = Wlr_OrderType_CancelOther::getPotentialCancelOtherOrder($this->getProductComponentInstanceID());

        return ($arrResult == false) ? false : $arrResult;
    }

    ////////////////////////////////////////////////////////////////////////////////

    /**
     * WLR Product Component status change logger.
     *
     * @param string $strStatus Status
     *
     * @return void
     */
    public function logStatusChange($strStatus)
    {
        $objEventLogger = $this->prvGetEventLogger();

        switch ($strStatus) {
            case 'CONFIGURED':
                $objEventLogger->logStatusChange('WlrLineRentConfiguration');
                break;
            case 'ACTIVE':
                $objEventLogger->logStatusChange('WlrLineRentActivation');
                break;
            case 'DEACTIVE':
                $objEventLogger->logStatusChange('WlrLineRentDeactivation');
                break;
            case 'DESTROYED':
                $objEventLogger->logStatusChange('WlrLineRentDestruction');
                break;
            default:
                break;
        }
    }

    /**
     * Cancellation fee
     *
     * @return type
     */
    public function getCancellationFee()
    {
        $arrConfig = $this->getConfig();

        return $arrConfig['intCancellationFee'];
    }

    /**
     * Renumbering free
     *
     * @return type
     */
    public function getRenumberingFee()
    {
        $arrConfig = $this->getConfig();

        return $arrConfig['intRenumberingChargePence'];
    }

    /**
     * get BT activation charge
     *
     * @return type
     */
    public function getBTActivationCharge()
    {
        $arrConfig = $this->getConfig();

        return $arrConfig['intBTActivationCharge'];
    }

    /**
     * Get new provide fee
     *
     * @return type
     */
    public function getNewProvideFee()
    {
        $arrConfig = $this->getConfig();

        return ($arrConfig['intNewProvideFeePence'] / 100);
    }

    /**
     * get unpaid credit
     *
     * @return type
     */
    public function getUnpaidCredit()
    {
        $arrCallsToCharge = $this->calculateCharges();
        $intUnpaidCredit = 0;
        foreach ($arrCallsToCharge as $arrCall) {
            $intUnpaidCredit += $arrCall['intAmountToCharge'];
        }

        return $intUnpaidCredit;
    }

    /**
     * Calculate cancellation charges
     *
     * @return boolean
     */
    public function calculateCancellationCharges()
    {
        $arrReturn = array();
        $intUnpaidCredit = $this->getUnpaidCredit();

        if ($intUnpaidCredit > 0) {
            $arrUnpaidCredit = CFinancialHelper::splitVat($intUnpaidCredit);
            $arrReturn [] = array(
                'intAmountExVatPence'   => $arrUnpaidCredit['exvat'],
                'intVatPence'           => $arrUnpaidCredit['vat'],
                'vchDescription'        => 'Home Phone unpaid credit ',
                'uxtStartPeriodCovered' => time(),
                'uxtEndPeriodCovered'   => time()
            );
        }

        $intCancellationFee = $this->getCancellationFee();
        if ($intCancellationFee > 0) {
            $arrCancellationFee = CFinancialHelper::splitVat($intCancellationFee);
            $arrReturn[] = array(
                'intAmountExVatPence'   => $arrCancellationFee['exvat'],
                'intVatPence'           => $arrCancellationFee['vat'],
                'vchDescription'        => 'Home Phone cancellation fee',
                'uxtStartPeriodCovered' => time(),
                'uxtEndPeriodCovered'   => time()
            );
        }

        if (count($arrReturn) == 0) {
            return true;
        } else {
            return $arrReturn;
        }

        return $arrReturn;
    }

    /**
     * Method appies dicount to all calls made to favourite numbers
     *
     * @access private
     *
     * @return void
     */
    private function applyFavouriteNumbersDiscount()
    {
        $intServiceID = $this->getServiceID();

        $intFavPciID = CProductComponent::getProductComponentInstance($intServiceID, 'FavouriteNumbers');
        if ($intFavPciID === false) {
            return;
        }

        $objCWlrFavouriteNumbers = new CWlrFavouriteNumber($intFavPciID);
        $arrFavouriteNumbers = $objCWlrFavouriteNumbers->getFavouriteNumbers();
        if (count($arrFavouriteNumbers) == 0) {
            return;
        }

        $intDiscountPercent = $objCWlrFavouriteNumbers->getDiscount($intFavPciID);
        if (!is_numeric($intDiscountPercent) || $intDiscountPercent <= 0) {
            pt_raise_autoproblem(
                'Wlr - CWlrLineRent::applyFavouriteNumbersDiscount()',
                'AUTO-PROBLEM: Failed to retrive discount percent from DB',
                'Script failed to select discount percent from database',
                "Affected customer/discount-PCI: $intServiceID/$intFavPciID\n"
            );

            return;
        }

        $floFactor = $intDiscountPercent / 100;

        // Note that the calls are linked to the "$this" PCI (i.e. a LineRent PCI), not the "favorite number" PCI above
        $intPciID = $this->getProductComponentInstanceID();
        $strCliList = "'" . implode("','", $arrFavouriteNumbers) . "'";
        $strQuery = <<<EOQ
SELECT
    intCallId, CEIL(intCustomerPricePence * $floFactor) AS intDiscount
FROM
    tblCall
WHERE
    vchDestinationCli IN ($strCliList)
    AND intSchedulePaymentId IS NULL
    AND intCustomerPricePence > 0
    AND intProductComponentInstanceId = $intPciID
EOQ;

        $dbConn = get_named_connection_with_db('wlr_calls');
        $arrDiscounts = PrimitivesResultsAsArrayGet(PrimitivesQueryOrExit($strQuery, $dbConn));

        if (count($arrDiscounts) > 0) {
            $arrInsertData = array();

            foreach ($arrDiscounts as $arrDiscount) {
                $intCallID = $arrDiscount['intCallId'];
                $intDiscount = $arrDiscount['intDiscount'];

                $this->intFavouriteNumberDiscountPence += $intDiscount;

                $arrInsertData[] = "($intCallID, $intDiscount)";
            }

            // insert all discounts
            $strInsertData = implode(',', $arrInsertData);
            $strQuery = "INSERT INTO tblFavouriteNumbersDiscount (intCallId, intDiscountPence) VALUES $strInsertData";

            PrimitivesQueryOrExit($strQuery, $dbConn);
        }
    }

    /**
     * calculate the charges for all outstanding calls that are due to be billed
     *
     * <AUTHOR>
     *
     * @return interger $intAmountToCharge
     */
    public function calculateCharges()
    {
        $arrCallsToCharge = array();

        // Get all the call charges
        $arrWlrLineRentInsId = CWlrProduct::getAllLineRentInstanceIdFromServiceId($this->getServiceID());
        if ($arrWlrLineRentInsId === false || empty($arrWlrLineRentInsId)) {
            return false;
        }

        $dbConnection = get_named_connection_with_db('wlr_calls');

        // HOTFIX: revert change for BRP-1080
        $strQuery = "SELECT DT.intAmountToCharge, DT.intCallId, DT.uxtCallTime
                       FROM (SELECT intCustomerPricePence
                                   - SUM(if(intCreditPenceRedeemed IS NULL, 0, intCreditPenceRedeemed))
                                   - SUM(if(fnd.intDiscountPence IS NULL, 0, fnd.intDiscountPence))
                                   AS intAmountToCharge,
                                   c.intCallId,
                                   UNIX_TIMESTAMP(c.dtmStart) AS uxtCallTime
                              FROM tblCall c
                         LEFT JOIN tblPrepaidCreditRedemption cr
                                ON c.intCallId = cr.intCallId
                         LEFT JOIN tblFavouriteNumbersDiscount fnd ON c.intCallId = fnd.intCallId
                             WHERE c.intProductComponentInstanceId IN (" . implode(', ', $arrWlrLineRentInsId) . ")
                               AND c.intSchedulePaymentId IS NULL
                               AND c.dtmProcessCompleted IS NOT NULL
                               AND dtmQueried IS NULL
                          GROUP BY c.intCallId, c.dtmStart, c.intCustomerPricePence) AS DT
                      WHERE DT.intAmountToCharge > 0";

        $refResult = PrimitivesQueryOrExit($strQuery, $dbConnection);
        $arrCallsToCharge = PrimitivesResultsAsArrayGet($refResult);

        return $arrCallsToCharge;
    }

    /**
     * calculate the charges for all outstanding calls that are due to be billed and 'no DD admin charge' if necessary
     * Returns false if an error occurred, or an array containing the scheduled payment IDs
     *
     * @param uxt $uxtDateOfRun Date of run
     *
     * @return array
     */
    public function scheduleCharges($uxtDateOfRun)
    {
        $arrAccount = userdata_account_get_by_service($this->getServiceID());

        // Cancel call charges over 3 months old
        CScheduledPayment::cancelCallCharges($arrAccount['account_id'], $uxtDateOfRun);

        $this->applyFavouriteNumbersDiscount();
        $arrCallsToCharge = $this->calculateCharges();
        if (count($arrCallsToCharge) == 0) {
            return array();
        }

        $intAmountToCharge = 0;
        $arrCallId = array();
        foreach ($arrCallsToCharge as $arrCallToCharge) {
            $arrCallId[] = $arrCallToCharge['intCallId'];
            $intAmountToCharge += $arrCallToCharge['intAmountToCharge'];
        }

        // We set the start date to whichever is the earlier of 30 days ago (from the given run date) or the
        // customer's activation date
        $arrConfig = $this->getUserConfig();
        $uxtActivationDate = $arrConfig['uxtActivationDate'];
        $uxt30DaysEarlier = $uxtDateOfRun - (30 * 24 * 60 * 60);
        $uxtStartDate = ($uxt30DaysEarlier < $uxtActivationDate ? $uxtActivationDate : $uxt30DaysEarlier);

        // If a fav-number discount has been applied, we add a note to the payment description
        $strSchedulePaymentDesc = 'Call charge for the period to ' . date('d/m/Y', $uxtDateOfRun);
        if ($this->intFavouriteNumberDiscountPence > 0) {
            $strDiscountValue = sprintf('�%01.2f', ($this->intFavouriteNumberDiscountPence / 100));
            $strSchedulePaymentDesc .= " (including $strDiscountValue Favourite Five discount)";
        }

        $objPaymentScheduler = new CProductComponentPaymentScheduler($this->getProductComponentInstanceID(), '');
        $objPaymentScheduler->addIncVatAmount(
            $intAmountToCharge,
            $uxtDateOfRun,
            $strSchedulePaymentDesc,
            $uxtStartDate,
            $uxtDateOfRun
        );

        if (empty($objPaymentScheduler->m_intScheduledPaymentID)) {
            return false;
        }

        // Payment successfully generated: we need to record this against the original calls
        $strCallIdList = implode(',', $arrCallId);
        $intSpID = $objPaymentScheduler->m_intScheduledPaymentID;
        $strQuery = "UPDATE tblCall set intSchedulePaymentId = $intSpID WHERE intCallId IN ($strCallIdList)";

        $dbConn = get_named_connection_with_db('wlr_calls');
        PrimitivesQueryOrExit($strQuery, $dbConn);

        return array($intSpID);
    }

    /**
     * On Schedule Cancellation
     *
     * @return boolean
     */
    public function onScheduledCancellation()
    {
        $arrCancellationCharges = array();
        //First thing we do is add the schedule payment for cancellation fee
        $intCancellationFee = $this->getCancellationFee();
        if ($intCancellationFee > 0) {
            $objPaymentScheduler = new CProductComponentPaymentScheduler($this->getProductComponentInstanceID(), '');
            $objPaymentScheduler->addIncVatAmount(
                $intCancellationFee * 100,
                time(),
                'Home Phone Cancellation fee',
                time(),
                time()
            );
        }

        //get all the pending schedule payments, how ckever is that !!!!!!
        $objPaymentScheduler = new CProductComponentPaymentScheduler($this->getProductComponentInstanceID(), '');
        $arrPendingSchedulePayments = $objPaymentScheduler->getScheduledPayments(true);

        if (count($arrPendingSchedulePayments) > 0) {

            foreach ($arrPendingSchedulePayments as $arrPendingSchedulePayment) {

                $arrCancellationCharges[] = array(
                    'intAmountExVatPence'   => $arrPendingSchedulePayment['intAmountExVatPence'],
                    'intVatPence'           => $arrPendingSchedulePayment['intVatPence'],
                    'vchDescription'        => $arrPendingSchedulePayment['strDescription'],
                    'uxtStartPeriodCovered' => $arrPendingSchedulePayment['uxtStartPeriodCovered'],
                    'uxtEndPeriodCovered'   => $arrPendingSchedulePayment['uxtEndPeriodCovered'],
                    'intScheduledPaymentID' => $arrPendingSchedulePayment['intScheduledPaymentID']
                );
            }
        }

        if (count($arrCancellationCharges) > 0) {
            return $arrCancellationCharges;
        }

        return true;
    }

    /**
     * Function to return boolean value for creating �0 schedule payment
     *
     * @access   protected
     * <AUTHOR> Zaki" <<EMAIL>>
     *
     * @return boolean
     */
    public function displaySplitCharge()
    {
        $arrConfig = $this->getConfig();

        return (count($arrConfig) > 0 && $arrConfig['bolDisplaySplitCharge']) ? true : false;
    }

    /**
     * Determine the tariff ID of the line rental product component that is
     * associated with a particular service component.
     *
     * @param integer $intServiceComponentId - service component ID of product
     *
     * @static
     * @access public
     *
     * @return mixed - tariff ID on success, false on failure
     **/
    public static function getTariffIdFromServiceComponent($intServiceComponentId)
    {
        $dbhConn = get_named_connection_with_db('product_reporting');

        $intServiceComponentId = PrimitivesRealEscapeString($intServiceComponentId, $dbhConn);

        $strQuery = "SELECT
                        t.intTariffID
                     FROM
                        dbProductComponents.tblProductComponentConfig AS pcc
                     INNER JOIN dbProductComponents.tblTariff AS t
                        ON t.intProductComponentConfigID = pcc.intProductComponentConfigID
                     INNER JOIN products.tblServiceComponentProduct AS scp
                        ON scp.intServiceComponentProductID = pcc.intServiceComponentProductID
                     INNER JOIN dbProductComponents.tblProductComponent AS pc
                        ON pc.intProductComponentID = pcc.intProductComponentID
                     WHERE
                        scp.intServiceComponentID = '{$intServiceComponentId}'
                        AND t.dtmEnd IS NULL
                        AND pc.vchHandle = 'WlrLineRent'
                        AND pcc.dtmEnd IS NULL";

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConn, __METHOD__, false);

        if ($resResult == false) {
            return false;
        }

        return PrimitivesResultGet($resResult, 'intTariffID');
    }

    /**
     * Get the cost of the line rent product component
     *
     * @access public
     *
     * @return integer if set or false if not
     **/
    public function getCurrentCost()
    {
        $arrTariff = CProductComponent::getTariffDetails($this->m_intTariffID);

        return (isset($arrTariff['intCostIncVatPence']) && $arrTariff['intCostIncVatPence'] >= 0)
            ? $arrTariff['intCostIncVatPence'] : false;

    }

    /**
     * Get the line rental object instance that is associated with a given WLR product object.
     *
     * @param object $wlrProduct WLR product object to acquire line rental object from.
     *
     * @static
     * @access public
     *
     * @return CWlrLineRent
     **/
    public static function getInstanceFromProduct(CWlrProduct $wlrProduct)
    {
        return CProductComponent::createInstanceFromComponentID(
            $wlrProduct->getComponentID(),
            'WlrLineRent',
            array('UNCONFIGURED', 'ACTIVE')
        );
    }

    /**
     * Raises an auto problem using the template stored in
     * /local/codebase2005/content/templatedautoproblems/PLUSNET/WORKPLACE_ISP_ADMIN
     *
     * @param string $strTemplate     Template name
     * @param array  $arrTemplateData Array of data to be passed on to the template
     *
     * @return void
     */
    protected function raiseAutoProblem($strTemplate, $arrTemplateData)
    {
        $busTierSession = Auth_BusTierSession::getCurrent();

        if (!empty($busTierSession) && is_object($busTierSession)) {

            $actorId = $busTierSession->actorId;
        }

        $autoProblemClient = BusTier_BusTier::getClient('autoproblem');
        $autoProblem = $autoProblemClient->prepareAutoProblem(
            $strTemplate,
            $arrTemplateData,
            Auth_BusinessActor::get($actorId)
        );

        $autoProblem->raiseProblem();
    }

    /**
     * Non-concurrent scheduled payments for a WLR line rental product component
     * instance consist of just the line rental charge which is applicable on
     * products such as Anytime and Business Daytime. It's that extra fiver you
     * pay on top of the regular monthly subscription. (of which, remember, the
     * actual subscription is handled by the "SUBSCRIPTION" product component
     * instance)
     *
     * This method overrides the one defined the base class, CProductComponent
     *
     * @param integer $uxtStart Start date
     * @param integer $uxtEnd   End date
     *
     * @return array|boolean
     **/
    protected function getNonConcurrentScheduledPaymentForPeriod($uxtStart, $uxtEnd)
    {
        if (!$this->_validateStartEndPeriod($uxtStart, $uxtEnd)) {
            return false;
        }


        $strCallPlanDisplayName = $this->getInvoiceProductComponentDisplayName($this->getTariffID());


        // We must read from the master coredb to avoid any replication lag.

        $dbhConnection = get_named_connection_with_db('financial');

        $strQuery = "
            SELECT sp.intScheduledPaymentId
              FROM financial.tblScheduledPayment sp
        INNER JOIN financial.tblConfigProductComponent cpc
                ON sp.intScheduledPaymentId = cpc.intScheduledPaymentId
        INNER JOIN userdata.tblProductComponentInstance pci
                ON cpc.intProductComponentInstanceId = pci.intProductComponentInstanceId
             WHERE pci.intProductComponentInstanceId = {$this->m_intProductComponentInstanceID}
               AND sp.dteEndPeriodCovered   >= FROM_UNIXTIME({$uxtStart})
               AND sp.dteStartPeriodCovered <= FROM_UNIXTIME({$uxtEnd})
               AND sp.dtmCancelled IS NULL
               AND sp.vchDescription LIKE '%{$strCallPlanDisplayName}%'";

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection, __METHOD__, false);

        return PrimitivesResultsAsArrayGet($resResult);
    }

    /**
     * This method has been copied/edited from:
     *
     * Wlr_OrderType_DebtManagement::ApplyCallBarringCharges
     *
     * It has been moved into CWlrLineRent because the WLR3 process requires
     * means to be able to apply call barring charges on an account.
     *
     * The original version of this method was obviously part of the WLR2
     * provisioning process - which is now technically redundant code.
     *
     * The behaviour of this version of the method stays true to the original
     * WLR2 version. The only changes that have taken place are to simplify
     * the query slightly (in terms of what it returns and joins required)
     * and to use the line rental product component instance ID as the
     * starting point instead of the component ID.
     *
     * @return void
     **/
    public function applyCallBarringCharge()
    {
        $intProductComponentInstanceID = $this->getProductComponentInstanceID();

        $dbhConnection = get_named_connection_with_db('dbProductComponents');

        $strQuery = "
            SELECT
               sc.name,
               config.intCallBarringChargePence,
               UNIX_TIMESTAMP(pcs.dteNextInvoice) AS uxtNextInvoice
            FROM
               userdata.components c
            INNER JOIN products.service_components AS sc
               ON sc.service_component_id = c.component_type_id
            INNER JOIN userdata.tblProductComponentInstance pci
               ON pci.intComponentID = c.component_id
            INNER JOIN dbProductComponents.tblProductComponentConfig pcc
               ON pcc.intProductComponentID = pci.intProductComponentID
            INNER JOIN products.tblServiceComponentProduct scp
               ON scp.intServiceComponentProductID = pcc.intServiceComponentProductID
               AND c.component_type_id = scp.intServiceComponentID
            INNER JOIN dbProductComponents.tblWlrLineRentConfig config
               ON config.intProductComponentConfigId = pcc.intProductComponentConfigId
            INNER JOIN userdata.tblProductComponentInstance pcs
               ON pcs.intComponentID = c.component_id
            INNER JOIN dbProductComponents.tblProductComponent pc2
               ON pc2.intProductComponentID = pcs.intProductComponentID
               AND pc2.vchHandle = 'SUBSCRIPTION'
            WHERE
               pci.intProductComponentInstanceID = {$intProductComponentInstanceID}
            ";

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);
        $arrResult = PrimitivesResultGet($resResult);

        $intCallBarringChargePence = $arrResult['intCallBarringChargePence'];

        if ($intCallBarringChargePence > 0) {

            $uxtNextInvoice = $arrResult['uxtNextInvoice'];
            $strProductName = $arrResult['name'];

            $strDescription = $strProductName . ' call barring charge';

            $floCallBarringCharge = $intCallBarringChargePence / 100;

            $arrInvoiceItems[] = array(
                'amount'      => $floCallBarringCharge,
                'description' => $strDescription,
                'gross'       => true
            );

            $objPaymentScheduler = new CProductComponentPaymentScheduler(
                $intProductComponentInstanceID,
                ''
            );

            $objPaymentScheduler->addIncVatAmount(
                $intCallBarringChargePence,
                $uxtNextInvoice,
                $strDescription,
                time(),
                time()
            );

            $strTicketBody =
                "The {$strDescription} of &pound;" . number_format($floCallBarringCharge, 2) .
                " has been added to the subscription bill scheduled to be " .
                "invoiced for " . date('jS F Y', $uxtNextInvoice) . '.';

            tickets_ticket_add(
                'Script',
                $this->getServiceID(),
                '',
                '',
                'Closed',
                0,
                $strTicketBody
            );
        }
    }
}
