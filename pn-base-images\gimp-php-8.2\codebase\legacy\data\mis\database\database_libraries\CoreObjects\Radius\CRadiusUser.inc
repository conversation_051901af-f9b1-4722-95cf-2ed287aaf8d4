<?php
	/**
	* This file declares the CRadiusUser class 
	*
	* @package    Core
	* @subpackage Radius
	* <AUTHOR>
	* @version    $Id: CRadiusUser.inc,v 1.5 2007-08-09 07:45:53 msimons Exp $
	* @filesource
	*/

	/**
	* Requires
	*/
	
	require_once('/local/data/mis/database/database_libraries/CDAO.inc');
	require_once '/local/data/mis/database/crypt_config.inc';

	/**
	* This class declares the RADIUS User class.
	* Extends functionality in CDAO (Data Access Object)
	*
	* @package  Core
	* @access   public
	*/

	class CRadiusUser extends CDAO
	{
		//
		// Private member variables
		//
		
		
		/**
		* An array of variables that are valid for this class
		*
		* @access private
		* @var    array
		*/
		var $m_arrValidVariables = array('intRadiusID',
		                                 'strUsername',
		                                 'strISP',
		                                 'strCLI',
		                                 'strPassword',
										 'strIPAddress',
										 'strNetmask',
										 'intServiceID',
		                                 'bolActive',
										 'intQuotaID',
		                                 'bolPasswordVisibleToSupport',
		                                 'intAdditionalFlags',
		                                 'intOmitFlags',
		                                 'bolAllowCLIOrPassword');

		
		/**
		*
		* @var integer
		* @access private
		*/
		var $m_intRadiusID = '';

		
		/**
		*
		* @var string
		* @access private
		*/
		var $m_strUsername = '';	

		
		/**
		*
		* @var string
		* @access private
		*/
		var $m_strISP = '';	

		
		/**
		*
		* @var string
		* @access private
		*/
		var $m_strCLI = '0';	

		
		/**
		*
		* @var string
		* @access private
		*/
		var $m_strPassword = '';	

		
		/**
		*
		* @var string
		* @access private
		*/
		var $m_strIPAddress = 'pool';	

		
		/**
		*
		* @var string
		* @access private
		*/
		var $m_strNetmask = '***************';	

		
		/**
		*
		* @var integer
		* @access private
		*/
		var $m_intServiceID = 0;	

		
		/**
		*
		* @var boolean
		* @access private
		*/
		var $m_bolActive = true;	

		
		/**
		*
		* @var integer
		* @access private
		*/
		var $m_intQuotaID = 1;	

		
		/**
		*
		* @var boolean
		* @access private
		*/
		var $m_bolPasswordVisibleToSupport = false ;	

		
		/**
		*
		* @var integer
		* @access private
		*/
		var $m_intAdditionalFlags = 0;	

		
		/**
		*
		* @var integer
		* @access private
		*/
		var $m_intOmitFlags = 0;

		
		/**
		*
		* @var boolean
		* @access private
		*/
		var $m_bolAllowCLIOrPassword = false;	
		
		
		//
		// STATIC Methods
		//

		/**
		* Create the new object
		*
		* Creates a new instance of a CRadiusUser object
		*
		* <code>$objRadiusUser = &CRadiusUser::Create(2);</code>
		*
		* @access public
		* @static
		* @param the radius id of the user if you wish to instantiate an existing user. Defaults to 0 (new user).
		* @returns CRadiusUser instance
		*/ 
		public static function Create($intID=0)
		{
			$objNewObject = new CRadiusUser();

			if ($intID > 0)
			{
				$objNewObject->SetID($intID);
				
				$bolFound = $objNewObject->Refresh();
				
				if(! $bolFound)
				{
					return false;
				}
			}

			return $objNewObject;

		}


		//
		// Public Methods
		//

		
		
		

	
		/*
		* Retrieve a user by username and isp
		*
		* <code>
		* // Want to forget the changes made to this object
		* $objObject->Retrieve($strUsername, $strISP);
		* </code>
		*
		* @access public
		* @param 
		* @param 
		* @param 
		* @return boolean true if found + populates object members, false if does not exist
		*/

		function Retrieve ($strUsername, $strISP, $intServiceID)
		{
			$dbConnID = get_named_connection_with_db('radius');

			//Construct Query
			$strQuery = "SELECT radius_id as intRadiusID,
			                    username as strUsername,
			                    isp as strISP,
			                    cli as strCLI,
								password as strPassword,
								vchEncryptedPassword as strEncryptedPassword,
								ip_address as strIPAddress,
								ip_netmask as strNetmask,
								service_id as strServiceID,
			                    IF(active = 'Y', 'true', 'false') as bolActive,
								quota_id as intQuotaID,
			                    IF(password_visible_to_support = 'Y', 'true', 'false') as bolPasswordVisibleToSupport,
			                    additional_flags as intAdditionalFlags,
			                    omit_flags as intOmitFlags,
			                    IF(allow_cli_or_password = 'Y', 'true', 'false') as bolAllowCLIOrPassword			                    
			               FROM users
			              WHERE username_isp = '". addslashes($strUsername). '_'. addslashes($strISP). "' AND service_id = $intServiceID";
						  
			$resResult = PrimitivesQueryOrExit($strQuery, $dbConnID);
			
			$arrResult = PrimitivesResultsAsArrayGet($resResult);

			if(count($arrResult) == 0)
			{
				return false;
			}

			if($arrResult[0]['strEncryptedPassword'] != '') {
				$arrResult[0]['strPassword'] = Crypt_Crypt::decrypt($arrResult[0]['strPassword'], 'users');
			}

			//Set all of the Member Variables
			$this->SetAll($arrResult[0]);
			$this->SetID($arrResult[0]['intRadiusID']);

			return true;

		}


		/*
		* Refresh the object's data from the database
		*
		* This method discards the object's current data (if any) and
		* re-retrieves the information from the database.
		* For example:
		* <code>
		* // Want to forget the changes made to this object
		* $objObject->Refresh();
		* </code>
		*
		* @access public
		* @void
		*
		*/
		function Refresh ()
		{
			if ( $this->m_intID == 0 )
			{
				return false;
			}

			$dbConnID = get_named_connection_with_db('radius');

			//Construct Query
			$strQuery = "SELECT radius_id as intRadiusID,
			                    username as strUsername,
			                    isp as strISP,
			                    cli as strCLI,
								password as strPassword,
								vchEncryptedPassword as strEncryptedPassword,
								ip_address as strIPAddress,
								ip_netmask as strNetmask,
								service_id as strServiceID,
			                    IF(active = 'Y', 'true', 'false') as bolActive,
								quota_id as intQuotaID,
			                    IF(password_visible_to_support = 'Y', 'true', 'false') as bolPasswordVisibleToSupport,
			                    additional_flags as intAdditionalFlags,
			                    omit_flags as intOmitFlags,
			                    IF(allow_cli_or_password = 'Y', 'true', 'false') as bolAllowCLIOrPassword			                    
			               FROM users
			              WHERE radius_id = '{$this->m_intID}'";
						  
			$resResult = PrimitivesQueryOrExit($strQuery, $dbConnID);

			$arrResult = PrimitivesResultsAsArrayGet($resResult);

			if(count($arrResult) == 0)
			{
				return false;			
			}

			if($arrResult[0]['strEncryptedPassword'] != '') {
				$arrResult[0]['strPassword'] = Crypt_Crypt::decrypt($arrResult[0]['strEncryptedPassword'], 'users');
			}

			//Set all of the Member Variables
			$this->SetAll($arrResult[0]);
			
			return true;
		}


		/**
		* Save the object's data to the database
		*
		* Saves the current state of the object to the database
		*
		* @access public
		*/

		function Save()
		{
			if($this->m_intID == 0)
			{
				return $this->prvSaveInsert();
			}
			else
			{
				return $this->prvSaveUpdate();
			}
		}

		//
		// Private Functions
		//

		/**
		* Insert a new user
		*
		* Insert a new user
		*
		* @access private
		* @return new ID or false
		*/
		function prvSaveInsert()
		{
			$dbConnID = get_named_connection_with_db('radius');

			if ($this->m_bolActive)
			{
				$strActive = 'Y';
			}
			else
			{
				$strActive = 'N';
			}

			if ($this->m_bolPasswordVisibleToSupport)
			{
				$strPasswordVisible = 'Y';
			}
			else
			{
				$strPasswordVisible = 'N';
			}

			if ($this->m_bolAllowCLIOrPassword)
			{
				$strCLIOrPassword = 'Y';
			}
			else
			{
				$strCLIOrPassword = 'N';
			}

			// Check for the existence of the user in RADIUS already
			$strFindQuery = "SELECT radius_id
			                   FROM users
							  WHERE username = '". addslashes($this->m_strUsername). "'
							    AND isp = '". addslashes($this->m_strISP). "'";

			$resResult = PrimitivesQueryOrExit($strFindQuery, $dbConnID, 'Find existing RADIUS user');
			$arrResult = PrimitivesResultsAsArrayGet($resResult);

			$strEncryptedPassword = Crypt_Crypt::encrypt($this->m_strPassword, 'users');

			if (count($arrResult) > 0)
			{
				// The user already existed in RADIUS
				$intRadiusID = $arrResult[0]['radius_id'];

				// Just update their password - leave everything else alone
				$strQuery = "UPDATE users
				                SET password = '',
				                SET vchEncryptedPassword = '" .addslashes($strEncryptedPassword). "'
							  WHERE radius_id = '$intRadiusID' LIMIT 1";
							  
				$resResult = PrimitivesQueryOrExit($strQuery, $dbConnID, 'Update existing RADIUS user');
			}
			else
			{
				//Construct Query to add a new user to RADIUS
				$strQuery = "INSERT INTO users
				                        (username_isp,
				                         cli,
										 password,
										 vchEncryptedPassword,
				                         ip_address,
				                         ip_netmask,
				                         service_id,
				                         active,
				                         quota_id,
				                         password_visible_to_support,
				                         additional_flags,
				                         omit_flags,
				                         allow_cli_or_password,
				                         username,
				                         isp)
				                 VALUES ('". addslashes($this->m_strUsername). '_'. addslashes($this->m_strISP). "',
				                         '". addslashes($this->m_strCLI)       . "',
				                         '',
				                         '". addslashes($strEncryptedPassword)  . "',
				                         '". addslashes($this->m_strIPAddress) . "',
				                         '". addslashes($this->m_strNetmask)   . "',
				                         '". addslashes($this->m_intServiceID) . "',
				                         '". $strActive . "',
				                         {$this->m_intQuotaID},
				                         '". $strPasswordVisible. "',
				                         {$this->m_intAdditionalFlags},
				                         {$this->m_intOmitFlags},
				                         '". $strCLIOrPassword . "',
				                         '". addslashes($this->m_strUsername)  . "',
				                         '". addslashes($this->m_strISP)  . "')";
				$resResult = PrimitivesQueryOrExit($strQuery, $dbConnID, 'Add new RADIUS user');

				$intRadiusID = PrimitivesInsertIdGet($dbConnID);

			}
			
			if($intRadiusID > 0)
			{
				$this->SetID($intRadiusID);
				return true;
			}
			else
			{
				return false;
			}
			
		}	


		/**
		* Update an existing user
		*
		* Update changes made to this object to tblRadiusUser
		* NOTE:  Incomplete. Will only update some fields.
		* If you need other fields updating, add them here, but check first
		* that it is legal to update them!!
		*
		* @access private
		* @return true or false
		*/
		function prvSaveUpdate()
		{
			$dbConnID = get_named_connection_with_db('radius');

			// This query is incomplete.  It only contains some of the updatable fields.
			// If you need to update a field which is not here, check that is it allowable then add the SET entry here.

			$strActive = 'Y';
			if(!$this->m_bolActive)
			{
				$strActive = 'N';
			}

			$strEncryptedPassword = Crypt_Crypt::encrypt($this->m_strPassword, 'users');

			//Construct Query
			$strQuery = "UPDATE users
			                SET password = '',
			                    vchEncryptedPassword = '". addslashes($strEncryptedPassword). "',
			                    active = '$strActive'
			              WHERE radius_id  = '{$this->m_intID}'";

			$resResult = PrimitivesQueryOrExit($strQuery, $dbConnID, 'Update an exisiting RADIUS user');

			//Data has changed so refresh
			return $this->Refresh();
		}

		/**
		* Set the password of the object
		* 
		* <AUTHOR>
		* @access private
		* @param  str Password
		*/
		function _setPassword($strPassword)
		{
			$this->m_strPassword = $strPassword;

		} // End of function _setPassword

		/**
		* Set the password of the object
		* 
		* <AUTHOR>
		* @access public
		* @param  str Password
		*/
		function setPassword($strPassword)
		{
			$this->_setPassword($strPassword);

		} // End of function setPassword

		/**
		* Set whether user is active
		* 
		* <AUTHOR>
		* @access private
		* @param  bol Active
		*/
		function _setActive($bolActive)
		{
			$this->m_bolActive = $bolActive;

		} // End of function _setActive

		/**
		* Set the status of the user
		* 
		* <AUTHOR>
		* @access public
		* @param  bol Active
		*/
		function setActive($bolActive)
		{
			$this->_setActive($bolActive);

		} // End of function setActive

		/**
		* Get the username
		* 
		* <AUTHOR>
		* @access private
		* @return str Username
		*/
		function _getUsername()
		{
			return $this->m_strUsername;

		} // End of function _getUsername

		/**
		* Get the username
		* 
		* <AUTHOR>
		* @access public
		* @return str Username
		*/
		function getUsername()
		{
			return $this->_getUsername();

		} // End of function getUsername

		/**
		* Get the password
		* 
		* <AUTHOR>
		* @access private
		* @return str Password
		*/
		function _getPassword()
		{
			return $this->m_strPassword;

		} // End of function _getPassword

		/**
		* Get the password
		* 
		* <AUTHOR>
		* @access public
		* @return str Password
		*/
		function getPassword()
		{
			return $this->_getPassword();

		} // End of function getPassword

	}
?>
