server: itw
role: master
rows: single
statement:

DELETE 
	dbRemoteInternet.tblUserAddress, 
	dbRemoteInternet.tblAddressHasType
FROM 
	dbRemoteInternet.tblUserAddress  
INNER JOIN dbRemoteInternet.tblAddressHasType ON
	dbRemoteInternet.tblUserAddress.intAddressId = userdata.tblAddressHasType.intAddressId 
INNER JOIN dbRemoteInternet.tblAddressType ON
	dbRemoteInternet.tblAddressHasType.intAddressTypeId = userdata.tblAddressType.intAddressTypeId
WHERE 
	dbRemoteInternet.tblAddressType.vchHandle = :vchHandle 
	AND dbRemoteInternet.tblUserAddress.intUserId = :intUserId
