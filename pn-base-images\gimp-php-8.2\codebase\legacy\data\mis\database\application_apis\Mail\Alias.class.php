<?php
require_once ("/local/data/mis/database/database_libraries/mail-class.inc");
require_once(SMARTY_LIBRARY);

/**
 * Aliases are part of a mailbox and should be accessed and modified
 * using the mailbox which holds an array of alias objects
 */
final class Mail_Alias
{
    /**
     * @var array of values from userdata.tblMailAlias
     */
    protected $_arrAliasData = array();

    /**
     * @var Mail_Db instance
     */
    protected $_objDb = null;

	/**
	 * Returns an alias object specified by alias id
	 *
	 * @param int id
	 * @return Mail_Alias
	 * @throws Mail_Exception
	 */
	public static function findById($intId)
	{
		$intId = (int)$intId;
		if ($intId <= 0) {
			return false;
		}
		$strQuery = "SELECT intMailAliasID, intConfigEmailID, vchName " .
		            "FROM userdata.tblMailAlias " .
		            "WHERE intMailAliasID = " . $intId .
		            " LIMIT 1";
		$arrAlias = Mail_Db::getInstance()->getSingleRow($strQuery, 'userdata');
		$objAlias = new Mail_Alias($arrAlias);
		if ($objAlias instanceof Mail_Alias) {
			return $objAlias;
		}
		else {
			throw new Mail_Exception('Alias id '.$intId.' not found');
		}
	}
		
    /**
     * Returns a array of mailbox aliases with keys as alias names
     *
     * @param int id
     * @return mixed array or null if not found
     * @throws Mail_Exception
     */
    public static function findByEmailId($id)
    {
        $strQuery = 'SELECT intMailAliasID, intConfigEmailID, vchName, vchHandle as strStatus '
                  . 'FROM userdata.tblMailAlias '
                  . 'INNER JOIN dbProductComponents.tblStatus using (intStatusID) '
                  . "WHERE vchHandle IN('QUEUED_ACTIVATE', 'ACTIVE') "
                  . 'AND intConfigEmailID = ' . (int)$id . ' '
                  . ' ORDER BY vchName';
        $arrAliases = Mail_Db::getInstance()->getArray($strQuery, 'userdata');
        if($arrAliases) {
            $arrReturn = array();
            foreach ($arrAliases as $row) {
                $objAlias = new Mail_Alias($row);
                $arrReturn[$row['vchName']] = $objAlias;
            }
            return $arrReturn;
        }
    }

    /**
     * Creates new alias object
     *
     * @param array row from tblMailAlias
     */
    public function __construct($row = null)
    {
        $this->_objDb = Mail_Db::getInstance();

        if(is_array($row)) {
            foreach($row as $key => $value) {
                $this->$key = $value;
            }
        }
    }

    /**
     * Saves newly created alias to dbMailAlias as queued-activate
     *
     * @param integer intEmailId
     * @throws Mail_Exception
     */
    public function create($intEmailId)
    {
        $this->intConfigEmailID = $intEmailId;
        $strQuery = 'INSERT INTO tblMailAlias SET '
                  . "intConfigEmailID = $intEmailId, "
                  . "vchName = '$this->vchName', "
                  . 'intStatusID = (SELECT intStatusID FROM dbProductComponents.tblStatus '
                  . "WHERE vchHandle = 'QUEUED_ACTIVATE')";
        $this->strStatus = 'QUEUED_ACTIVATE';
        $this->intMailAliasID = $this->_objDb->insert($strQuery,'userdata');
    }

    /**
     * Changes status to active and updates maildb
     *
     * @param Mail_MailApi $objMailApi
     * @throws Mail_Exception
     */
    public function enable(Mail_MailApi $objMailApi)
    {
        if('QUEUED_ACTIVATE' !== strtoupper($this->strStatus)) {
            throw new Mail_Exception("Trying to enable alias with status $this->strStatus");
        }

        try {
            $this->_objDb->begin('userdata', 'mailauth', 'maildelivery');

            $objMailApi->createAlias($this->vchName);
            $this->_changeStatus('ACTIVE');

            $this->_objDb->commit('userdata', 'mailauth', 'maildelivery');
        } catch (Mail_Exception $e) {
            $this->_objDb->rollback('userdata', 'mailauth', 'maildelivery');
            throw new Mail_Exception("Enabling alias $this->vchName for email_id $this->intMailAliasID failed");
        }
    }

    /**
     * Destroys alias in tblMailAlias and maildb
     *
     * @param Mail_MailApi $objMailApi
     * @throws Mail_Exception
     */
    public function destroy(Mail_MailApi $objMailApi)
    {
        $this->_changeStatus('QUEUED_DESTROY');
        try {
            $this->_objDb->begin('userdata', 'mailauth', 'maildelivery');
            //remove alias from maildb
            $objMailApi->destroyAlias($this->vchName);
            //remove alias from userdata
            $strQuery = "DELETE FROM tblMailAlias WHERE "
                      . "intMailAliasID = '{$this->intMailAliasID}' LIMIT 1 ";
            $this->_objDb->query($strQuery, 'userdata');

            $this->_objDb->commit('userdata', 'mailauth', 'maildelivery');
        } catch (Mail_Exception $e) {
            $this->_objDb->rollback('userdata', 'mailauth', 'maildelivery');
            throw new Mail_Exception("Destroying alias $this->vchName for email_id $this->intMailAliasID failed");
        }
    }

    /**
     * Refreshes the alias in Mail DB
     */
    public function refresh(Mail_MailApi $objMailApi)
    {
            $objMailApi->createAlias($this->vchName);
    }
    /**
     * Changes alias status
     *
     * @param string $strNewStatus
     * @throws Mail_Exception
     */
    protected function _changeStatus($strNewStatus)
    {
        $strQuery = 'UPDATE tblMailAlias SET '
                  . 'intStatusID = (SELECT intStatusID FROM dbProductComponents.tblStatus '
                                 . "WHERE vchHandle = '$strNewStatus') "
                  . "WHERE intMailAliasID = $this->intMailAliasID";
        $this->_objDb->query($strQuery, 'userdata');
        $this->strStatus = $strNewStatus;
    }

    public function __set($name, $value)
    {
        if(in_array($name, array('intMailAliasID', 'intConfigEmailID', 'vchName', 'strStatus'))) {
            $this->_arrAliasData[$name] = $value;
        } else {
            throw new Mail_Exception("Trying to set invalid property: $name");
        }
    }

    public function __get($name)
    {
        if(isset($this->_arrAliasData[$name])) {
            return $this->_arrAliasData[$name];
        } else {
            throw new Mail_Exception("Trying to get null property: $name");
        }
    }
}