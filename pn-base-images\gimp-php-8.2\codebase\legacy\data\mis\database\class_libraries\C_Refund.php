<?php
/*
 * C_Refund
 * class that handles the processing of a refund
 * The actual transaction (for credit cards)
 * or storing Direct debit details for later processing
 * It also creates a credit note and a refund
 */

## Direct requirements of this class

//require_once(USERDATA_ACCESS_LIBRARY);
setlocale(LC_MONETARY, 'en_GB');

require_once(FINANCIAL_ACCESS_LIBRARY);
require_once(SECURE_TRANSACTION_ACCESS_LIBRARY);
require_once(TICKETS_ACCESS_LIBRARY);
require_once(DIRECT_DEBIT_ACCESS_LIBRARY);
require_once LOCAL_FINANCIAL_SETTINGS;

require_once(dirname(__FILE__) . '/C_Simple_Error.php');

class C_Refund extends C_Simple_Error
{

    const REFUND_TYPE_AUTOMATIC   = 'automatic';
    const REFUNF_TYPE_MANUAL      = 'manual';
    const REFUND_TYPE_CREDIT_NOTE = 'credit_note';

    const REFUND_METHOD_CC     = 'cc';
    const REFUND_METHOD_DD     = 'dd';
    const REFUND_METHOD_CHEQUE = 'cheque';

    var $m_intServiceID;
    var $m_intInvoiceID;
    var $m_floAmount;
    var $m_strPaymentMethod;
    var $m_strRefundReason;
    var $m_strRefundReasonOther;
    var $m_floInvoiceAmount;
    var $m_floInvoiceBalance;
    var $m_intAccountID;
    var $m_intCustomerID;
    var $m_arrPossibleCreditNoteReasons;
    var $m_arrInvoiceItems;
    var $m_strInvoiceDescription;
    var $m_intCreditNoteID;
    var $m_intCreditNoteTypeID;
    var $m_intTransactionID;
    var $m_intCreditDetailsID;
    var $m_intUserID;
    var $m_strUserNames;
    var $m_bolCreditNoteOnly;
    var $m_intRefundTicketId = 0;
    var $m_bolCcProviderChanged;
    var $m_decOriginalVatRate;
    var $m_strRefundType;
    var $m_strRefundMethod;
    var $suppressAutomaticRefundTicket = false;

    function C_Refund($intInvoiceID, $intServiceID)
    {

        if (!$intInvoiceID || $intInvoiceID==0) {

            $this->addError('Invoice ID not set');
        }

        if (!$intServiceID || $intServiceID==0) {

            $this->addError('Service ID not set');
        }

        if (count($this->getErrors()) > 0) {

            return false;
        }

        $this->setInvoiceID($intInvoiceID);
        $this->setServiceID($intServiceID);
        $arrInvoice = financial_sales_invoice_get_full($this->getInvoiceID());
        $this->setInvoiceAmount($arrInvoice['gross_value']);

        $this->setInvoiceBalance($arrInvoice['invoice_balance']);
        $this->setInvoiceDescription($arrInvoice['invoice_desc']);
        $this->setAccountID($arrInvoice['account_id']);
        $arrAccount = userdata_account_get($this->getAccountID());

        $this->setCreditDetailsID($arrAccount['credit_details_id']);
        $this->setCustomerID($arrAccount['customer_id']);
        $arrUser = userdata_customer_get_primary_user($this->getCustomerID());
        $this->setUserNames(
            $arrUser['salutation'] . ' ' . $arrUser['forenames'] .
            ' ' . $arrUser['surname']
        );

        $this->setUserID($arrUser['user_id']);
        $this->setPossibleCreditNoteReasons(financialGetCreditNoteType());
        $this->setInvoiceItems($arrInvoice['items']);

        $this->setCreditNoteOnly(false);
        $this->setOriginalVatRate($arrInvoice['vat_applied']);
    }

    /**
     * Set to true to suppress the service notice sent to inform of the refund when it's been processed
     * by credit card or direct debit.
     *
     * Note: will not suppress manual refund tickets (e.g cheque refunds or where there's been a problem
     *       and a manual actiion is required.
     **/
    public function setSuppressAutomaticRefundTicket($suppress)
    {
        $this->suppressAutomaticRefundTicket = $suppress;
    }


    # Sets: service id
    # Validates: invoice id is integer and greater than zero
    # Returns: true on success, false on failure
    function setServiceID($intServiceID)
    {

        if ($intServiceID>0) {

            $this->m_intServiceID = $intServiceID;

            return true;
        }

        return false;
    }

    # Returns: Service ID
    function getServiceID()
    {

        return $this->m_intServiceID;
    }

    # Sets: amount
    # Validates: amount is numeric and greater than zero
    # Returns: true on success, false on failure

    # Sets: invoice id
    # Validates: invoice id is integer and greater than zero
    # Returns: true on success, false on failure
    function setInvoiceID($intInvoiceID)
    {

        $intInvoiceID = (int)$intInvoiceID;
        if ($intInvoiceID>0) {

            $this->m_intInvoiceID = $intInvoiceID;

            return true;
        }

        return false;
    }

    # Returns: Invoice ID
    function getInvoiceID()
    {

        return $this->m_intInvoiceID;
    }

    # Returns: Original invoice vat rate
    function setOriginalVatRate($decVatRate)
    {

        $this->m_decOriginalVatRate = $decVatRate;
    }

    # Returns: Original invoice vat rate
    function getOriginalVatRate()
    {

        return $this->m_decOriginalVatRate;
    }

    # Sets: amount
    # Validates: amount is numeric and greater than zero
    # Returns: true on success, false on failure
    function setAmount($floAmount)
    {
        if (is_numeric($floAmount) && $floAmount>0) {

            $this->m_floAmount = $floAmount;

            return true;
        }

        return false;
    }

    # Returns: amount
    function getAmount()
    {

        return $this->m_floAmount;
    }

    # Sets: refund method (eg. cc, dd or cheque)
    # Validates: refund method should not be empty
    # Returns: true on success, false on failure
    function setPaymentMethod($strPaymentMethod)
    {

        if ($strPaymentMethod!='') {

            $this->m_strPaymentMethod = $strPaymentMethod;

            return true;
        }

        return false;
    }

    # Returns: current selected refund type
    function getPaymentMethod()
    {

        return $this->m_strPaymentMethod;
    }

    # Returns: refund reason
    function getRefundReason()
    {

        return $this->m_strRefundReason;
    }

    # Sets: refund reason
    # Validates: refund reason should not be empty
    # Returns: true on success, false on failure
    function setRefundReason($strReason)
    {
        if ($strReason!='') {

            $this->m_strRefundReason = $strReason;
            $this->setCreditNoteTypeID(
                $this->getCreditNoteTypeByHandle($strReason)
            );

            if ($strReason!='other') {

                $this->setInvoiceDescription(
                    $this->getCreditNoteNameByHandle($strReason)
                );
            }

            return true;
        }

        return false;
    }

    function getCreditNoteTypeByHandle($strHandle)
    {
        $dbhConnection = get_named_connection_with_db('financial');
        $strQuery  = "SELECT usiCreditNoteTypeID FROM " .
            "financial.vblCreditNoteType WHERE vchTag = '".$strHandle."'";
        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

        return PrimitivesResultGet($resResult, 'usiCreditNoteTypeID');
    }

    function getCreditNoteNameByHandle($strHandle)
    {

        $dbhConnection = get_named_connection_with_db('financial');
        $strQuery  = "SELECT vchCreditNoteType FROM " .
            "financial.vblCreditNoteType WHERE vchTag = '".$strHandle."'";
        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

        return PrimitivesResultGet($resResult, 'vchCreditNoteType');
    }

    # Returns: refund reason other
    function getRefundReasonOther()
    {

        return $this->m_strRefundReasonOther;
    }

    # Sets: refund reason other
    # Validates: refund reason should not be empty
    # Returns: true on success, false on failure
    function setRefundReasonOther($strReasonOther)
    {
        if ($strReasonOther!='') {

            $this->m_strRefundReasonOther = $strReasonOther;
            $this->setInvoiceDescription($strReasonOther);

            return true;
        }

        return false;
    }

    function setInvoiceAmount($floAmount)
    {
        if (is_numeric($floAmount)) {

            $this->m_floInvoiceAmount = $floAmount;

            return true;
        }

        return false;
    }

    function getInvoiceAmount()
    {

        return $this->m_floInvoiceAmount;
    }

    function setInvoiceBalance($floAmount)
    {
        if (is_numeric($floAmount)) {

            $this->m_floInvoiceBalance = $floAmount;

            return true;
        }

        return false;
    }

    function getInvoiceBalance()
    {

        return $this->m_floInvoiceBalance;
    }

    function setAccountID($intAccountID)
    {
        $intAccountID = (int)$intAccountID; // type cast required

        if ($intAccountID!=0) {

            $this->m_intAccountID = $intAccountID;

            return true;
        }

        return false;
    }

    function getAccountID()
    {

        return $this->m_intAccountID;
    }

    function setCustomerID($intCustomerID)
    {

        $intCustomerID = (int)$intCustomerID; // type cast required
        if ($intCustomerID!=0) {

            $this->m_intCustomerID = $intCustomerID;

            return true;
        }

        return false;
    }

    function getCustomerID()
    {

        return $this->m_intCustomerID;
    }

    function setUserID($intUserID)
    {
        $intUserID = (int)$intUserID; // type cast required

        if ($intUserID!=0) {

            $this->m_intUserID = $intUserID;

            return true;
        }

        return false;
    }

    function getUserID()
    {

        return $this->m_intUserID;
    }

    function setPossibleCreditNoteReasons($arrReasons)
    {

        if (is_array($arrReasons)) {

            $this->m_arrPossibleCreditNoteReasons = $arrReasons;

            return true;
        }

        return false;
    }

    function getPossibleCreditNoteReasons()
    {

        return $this->m_arrPossibleCreditNoteReasons;
    }

    function setInvoiceItems($arrItems)
    {

        if (is_array($arrItems)) {

            $this->m_arrInvoiceItems = $arrItems;

            return true;
        }

        return false;
    }

    function getInvoiceItems()
    {

        return $this->m_arrInvoiceItems;
    }

    function setInvoiceDescription($strDescription)
    {
        if ($strDescription!='') {

            $this->m_strInvoiceDescription = $strDescription;

            return true;
        }

        return false;
    }

    function getInvoiceDescription()
    {

        return $this->m_strInvoiceDescription;
    }

    function setCreditNoteID($intCreditNoteID)
    {

        if (is_int($intCreditNoteID)) {

            $this->m_intCreditNoteID = $intCreditNoteID;

            return true;
        }

        return false;
    }

    function getCreditNoteID()
    {

        return $this->m_intCreditNoteID;
    }

    function setCreditNoteTypeID($intCreditNoteTypeID)
    {

        $this->m_intCreditNoteTypeID = $intCreditNoteTypeID;

        return true;
    }

    function getCreditNoteTypeID()
    {

        return $this->m_intCreditNoteTypeID;
    }

    function getTransactionID()
    {

        return $this->m_intTransactionID;
    }

    function setTransactionID($intTransactionID)
    {

        $this->m_intTransactionID = $intTransactionID;

        return true;
    }

    function setCreditDetailsID($intCreditDetailsID)
    {

        $this->m_intCreditDetailsID = $intCreditDetailsID;

        return true;
    }

    function getCreditDetailsID()
    {

        return $this->m_intCreditDetailsID;
    }

    function setUserNames($strUserNames)
    {

        $this->m_strUserNames = $strUserNames;
        return true;
    }

    function getUserNames()
    {

        return $this->m_strUserNames;
    }

    function setCreditNoteOnly($bolCreditNoteOnly)
    {

        if (is_bool($bolCreditNoteOnly)) {

            $this->m_bolCreditNoteOnly = $bolCreditNoteOnly;

            return true;
        }

        return false;
    }

    function isCreditNoteOnly()
    {

        return $this->m_bolCreditNoteOnly;
    }

    function validate()
    {

        if ($this->getAmount() <= 0) {

            $this->addError('Amount incorrect '. $this->getAmount());
        }

        if (!$this->getRefundReason()) {

            $this->addError('Refund reason not set');
        }

        if (count($this->getErrors()) > 0) {

            return false;
        }

        return true;
    }


    /**
     *  This function actually does the processing of the refund
     *  It either credits the cc or creates tickets for DD/Cheque processing
     *
     * @return boolean
     */
    function process()
    {

        if (!$this->validate()) {

            return false;
        }

        $arrService = userdata_service_get($this->m_intServiceID);

        if (strlen($arrService['isp'])>0) {

            $strIsp = strtolower($arrService['isp']);
        } else {

            $strIsp = null;
        }


        if (self::REFUND_TYPE_CREDIT_NOTE == $this->getRefundType()) {

            $this->_createCreditNote();

        } elseif (self::REFUNF_TYPE_MANUAL == $this->getRefundType()) {

            $this->setRefundMethod(self::REFUND_METHOD_CHEQUE);
            $this->_createCreditNote();
            $this->_createRefund();
            $intTicketId = $this->_createRefundTicket();

        } elseif (self::REFUND_TYPE_AUTOMATIC == $this->getRefundType()) {

            //Set refund method base on payment method used to pay for invoice
            $strRefundMethod = $this->getPaymentMethodByInvoiceID();

            if (!empty($strRefundMethod)) {

                $this->setRefundMethod($strRefundMethod);

                if ($this->_createTransaction($strIsp)) {

                    $this->_createCreditNote();
                    $this->_createRefund();
                    $this->_createRefundTicket();
                }

            } else {

                $this->addError(
                    "Unable to identify payment method used to pay for invoice. ID" . $this->getInvoiceID()
                );
            }

        } else {

            $this->addError('Unsupported refund type selected');
            return false;
        }

        if (count($this->getErrors()) > 0) {

            return false;
        }

        return true;
    }

    /**
     * Method to create new transaction and carry out refund procedures.
     * Base on method that was used to pay for invoice - decide which refund method to use.
     *
     * @param string $strIsp
     *
     * @return bool
     */
    function _createTransaction($strIsp = null)
    {
        global $auth;

        $bolSuccess = false;
        $strError = '';
        $strTransactionName = 'TRANSACTION_REFUND_TRANSACTION';

        if (self::REFUND_METHOD_CC == $this->getRefundMethod()) {

            // Trying to get the payware transaction id
            try {

                Dbg_Dbg::write(
                    'C_Refund::_createTransaction(): Getting receipts for invoice' .
                    ' ' . $this->getInvoiceID(),
                    'IspPayments'
                );

                $arrReceipts = Financial_Receipt::getReceiptsByInvoiceId(
                    $this->getInvoiceID(),
                    $strTransactionName
                );

                if (!empty($arrReceipts)) {

                    $intTransactionIdFromReceipt =
                        $arrReceipts[0]->getTransactionId();

                    $objTransactionDao = IspPayments_TransactionDao::get(
                        $intTransactionIdFromReceipt,
                        $strTransactionName
                    );

                    /* @var $objTransactionDao IspPayments_TransactionDao */
                    $intPaywareTransactionId =
                        $objTransactionDao->getPaywareTransactionId();

                    Dbg_Dbg::write(
                        "C_Refund::_createTransaction(): Payware transaction ID " .
                        "found: $intPaywareTransactionId",
                        'IspPayments'
                    );
                }

            } catch (Exception $objException) {

                $intPaywareTransactionId = null;

                $strError = 'No valid payware transaction ID found to process the refund.';

                Dbg_Dbg::write(
                    "C_Refund::_createTransaction(): $strError " . $objException->getMessage(),
                    'IspPayments'
                );
            }

            // If payware transaction id is not empty, attempt a card refund
            if (!empty($intPaywareTransactionId)) {

                $objDbAdaptor = Db_Manager::getAdaptor(
                    'IspPayments',
                    $strTransactionName,
                    true
                );

                Dbg_Dbg::write(
                    'C_Refund::_createTransaction(): Creating new transaction...',
                    'IspPayments'
                );

                $objNewTransaction = new IspPayments_TransactionDao(
                    $strTransactionName,
                    true
                );

                //Use credit details id of card that was used to pay for transaction
                //that we are trying refund
                $objNewTransaction->setCreditDetailsId($objTransactionDao->getCreditDetailsId());
                $objNewTransaction->setAmount($this->getAmount());
                $objNewTransaction->setBatch('no');

                $objNewTransaction->write();

                Dbg_Dbg::write(
                    'C_Refund::_createTransaction(): Transaction saved: ' .
                    print_r($objNewTransaction, 1),
                    'IspPayments'
                );

                $intTransactionId = $objNewTransaction->getTransactionId();
                $intAmount = bcmul($this->getAmount(), '100');

                /* @var $auth Example_Auth */
                $objSession = $auth->getBusTierSession();

                try {

                    Dbg_Dbg::write(
                        "C_Refund::_createTransaction(): Creating Buynet " .
                        "Transaction object...",
                        'IspPayments'
                    );

                    $objBuynetTransaction = new Buynet_Transaction(
                        'O',
                        $intAmount,
                        $intTransactionId,
                        $intPaywareTransactionId
                    );

                    $objBuynetTransaction->authToken =
                        PAYWARE_PROVIDER_BUYNET_AUTHTOKEN;

                    Dbg_Dbg::write(
                        "C_Refund::_createTransaction(): Creating Buynet " .
                        "User object...",
                        'IspPayments'
                    );

                    $objBuynetUser = IspPayments_PaywareManager::createBuynetUserFromMerchantId(
                            $objTransactionDao->getMerchantId(),
                            $strTransactionName
                        );

                    $objSoapTransaction = new SoapSession_Buynet_Transaction(
                        $objSession,
                        $objBuynetUser
                    );

                    // If it's partial refund don't try cancellation when refunding
                    if ($this->getAmount() < $this->getInvoiceAmount()) {

                        $bolTryCancelation = false;
                    } else {

                        $bolTryCancelation = true;
                    }

                    Dbg_Dbg::write(
                        "C_Refund::_createTransaction():Sending refund request..." .
                        print_r($objBuynetUser, 1) .
                        print_r($objBuynetTransaction, 1),
                        'IspPayments'
                    );

                    $objPaywareResponse = $objSoapTransaction->refund(
                        $objBuynetTransaction,
                        $bolTryCancelation
                    );

                    Dbg_Dbg::write(
                        "C_Refund::_createTransaction(): Payware response " .
                        "received: " . print_r($objPaywareResponse, 1),
                        'IspPayments'
                    );

                } catch (SoapFault $objSoapException) {

                    $strErrorMessage = 'SoapFault thrown' .
                        $objSoapException->getMessage();

                    error_log("C_Refund::_createTransaction(): $strErrorMessage");

                    $objPaywareResponse = new IspPayments_PaywareResponse();
                    $objPaywareResponse->setResponseCode(-1);
                    $objPaywareResponse->setResponseMessage($strErrorMessage);

                } catch (Exception $objException) {

                    $objPaywareResponse = null;
                    $strError = 'Payment request failed! Internal error occured';
                    Dbg_Dbg::write(
                        "C_Refund::_createTransaction(): Refund request failed" .
                        $objException->getMessage(),
                        'IspPayments'
                    );
                }

                if (isset($objPaywareResponse) &&
                    !$objPaywareResponse->isSuccessful()) {

                    $strError = $objPaywareResponse->getResponseMessage();
                }

                if (!empty($strError)) {

                    $this->addError(
                        'CC Transaction failed. The returned message was ' .
                        $strError
                    );
                    $bolSuccess = false;
                } else {

                    $bolSuccess = true;

                    Dbg_Dbg::write(
                        "C_Refund::_createTransaction(): Refund transaction " .
                        "success, updating the refund transaction. " .
                        "Transation id :" . $intTransactionId,
                        'IspPayments'
                    );

                    // Update the transaction with the status,
                    // payware transaction and merchant
                    // This was not there before COF,
                    // the transaction was left unprocessed and
                    // no merchant or payware transaction id were getting updated.
                    $objRefundTransaction = IspPayments_TransactionDao::get(
                        $intTransactionId, $strTransactionName
                    );

                    $objRefundTransaction->setTransactionStatus('complete');
                    $objRefundTransaction->setPaywareTransactionId(
                        $objPaywareResponse->getTransactionId()
                    );

                    $strClientId =
                        $objPaywareResponse->getBuynetUser()->getClientId();

                    $intMerchantId = NULL;

                    if (!empty($strClientId)) {

                        $intMerchantId = $objDbAdaptor->getMerchantId($strClientId);
                    }

                    $objRefundTransaction->setMerchantId($intMerchantId);
                    $objRefundTransaction->write();

                    Dbg_Dbg::write(
                        "C_Refund::_createTransaction(): Refund transaction " .
                        "updated with status, merchant id and ".
                        "payware transaction id",
                        'IspPayments'
                    );
                }

                Db_Manager::commit($strTransactionName);

            } else {

                $this->addError('No valid payware transaction ID found to process CC refund.');
                $bolSuccess = false;
            }

        } elseif (self::REFUND_METHOD_DD == $this->getRefundMethod()) {

            //Get active DD instruction for service
            $activeDd = direct_debit_active_instruction_get(
                $this->getServiceID()
            );

            if (!empty($activeDd)) {
                $dbhConnection = get_named_connection_with_db('financial');

                $encryptedAccountNumber = $this->encryptPaymentData($activeDd['bank_account_number']);
                $encryptedSortCode = $this->encryptPaymentData($activeDd['bank_sort_code']);

                if ($encryptedSortCode == false || $encryptedAccountNumber == false) {
                    $this->addError('Encryption failed for direct debit account.');
                    return false;
                }

                  $strQuery = "INSERT INTO tblDDRefund (".
                               "intServiceID, ".
                               "intInvoiceID, ".
                               "decAmount, ".
                               "vchName, ".
                               "vchSortCode, ".
                               "vchAccNumber ) VALUES (".
                               "'" . PrimitivesRealEscapeString($this->getServiceID(), $dbhConnection) . "'," .
                               "'" . PrimitivesRealEscapeString($this->getInvoiceID(), $dbhConnection) . "'," .
                               "'" . PrimitivesRealEscapeString($this->getAmount(), $dbhConnection) . "'," .
                               "'" . PrimitivesRealEscapeString($activeDd['name'], $dbhConnection) . "'," .
                               "'" . PrimitivesRealEscapeString($encryptedSortCode, $dbhConnection) . "'," .
                               "'" . PrimitivesRealEscapeString($encryptedAccountNumber, $dbhConnection) . "')";

                $resType = PrimitivesQueryOrExit($strQuery, $dbhConnection);
                $intTransactionId = 0;
                $bolSuccess = true;

            } else {

                /* If we there isn't any active DDI,
                 * refund will be done by cheque */
                $this->addError(
                    'Could not find direct debit instructions on this account.'
                );

                $bolSuccess = false;
            }

        } elseif (self::REFUND_METHOD_CHEQUE == $this->getRefundMethod()) {

            // It's a cheque refund - manual process
            $bolSuccess = true;

        } else {

            $this->addError('No appropriate refund method found.');

            return false;
        }

        $this->setTransactionID($intTransactionId);

        return $bolSuccess;
    }

    function _createCreditNote()
    {
        $intCreditNoteID = financial_sales_invoice_add(
            $this->getAccountID(),
            -$this->getAmount(),
            $this->getOriginalVatRate(),
            "Account Credit Note",
            '',
            $this->getInvoiceID()
        );

        financial_sales_invoice_modify($intCreditNoteID, 'fully_paid');

        if ($this->getAmount() < $this->getInvoiceAmount()) {

            $strStatus = 'part_paid';
        }

        if ($this->getAmount() >= $this->getInvoiceAmount()) {

            $strStatus = 'fully_refunded';
        }

        financial_sales_invoice_modify($this->getInvoiceID(), $strStatus);

        // Modify the original invoice
        financial_invoice_balance_add(
            $this->getInvoiceID(),
            -$this->getAmount()
        );

        userdata_account_balance_add(
            $this->getAccountID(),
            -$this->getAmount()
        );

        financialUpdateCreditTypeID(
            $intCreditNoteID,
            $this->getCreditNoteTypeID()
        );

        if ($this->getRefundReasonOther()) {

            financialUpdateCreditNoteOtherDescription(
                $intCreditNoteID,
                $this->getRefundReasonOther()
            );
        }

        financial_sales_invoice_item_add(
            $intCreditNoteID,
            $this->getInvoiceDescription(),
            1,
            0,
            -$this->getAmount()
        );

        $this->setCreditNoteID($intCreditNoteID);
        return true;
    }

    /**
     *
     * Creates refund and adjust account balance
     */
    function _createRefund()
    {
        $strPayment='';

        if (self::REFUND_METHOD_DD == $this->getRefundMethod()) {

            $strPayment='DDEBIT';

        } elseif (self::REFUND_METHOD_CHEQUE == $this->getRefundMethod()) {

            $strPayment = 'CHEQUE';
        }

        financial_sales_refund_add(
            $this->getInvoiceID(),
            $this->getAmount(),
            NULL,
            $this->getTransactionID(),
            '',
            NULL,
            $strPayment
        );

        // Modify the original invoice
        financial_invoice_balance_add(
            $this->getInvoiceID(), $this->getAmount()
        );

        userdata_account_balance_add($this->getAccountID(), $this->getAmount());
    }

    /*
     * Find payment method for current invoice
     */
    function getPaymentMethodByInvoiceID()
    {

        $paymentMethod = null;

        // Defaults to the payment method used for original invoice
        $arrReceipt = financial_sales_receipt_get_by_invoice_id(
            $this->getInvoiceID()
        );

        if (!empty($arrReceipt)) {

            $receiptPaymentMethod = !empty($arrReceipt['payment_method'])
                ? strtoupper($arrReceipt['payment_method']) : '';

            switch ($receiptPaymentMethod) {

                case 'DDEBIT': {

                    return self::REFUND_METHOD_DD;
                    break;
                }

                case 'CHEQUE': {

                    return self::REFUND_METHOD_CHEQUE;
                    break;
                }

                default: {

                    if ((!empty($receiptPaymentMethod) && !empty($arrReceipt['transaction_id'])) ||
                        (!empty($arrReceipt['payware_transaction_id']))) {

                        return self::REFUND_METHOD_CC;

                    } else {

                        return self::REFUND_METHOD_CHEQUE;
                    }
                }
            }
        }

        return $paymentMethod;
    }

    public static function isValidRefund($intInvoiceId)
    {
        $arrAllowed = array();
        $strError = NULL;
        $dbhConnection = get_named_connection_with_db('financial_reporting');

        $intInvoiceId = PrimitivesRealEscapeString($intInvoiceId, $dbhConnection);

        $strQuery = "SELECT IF(si.invoice_date >= current_date() -
                    INTERVAL 13 MONTH, 1, 0) as bolRefundAllowed
                          FROM transactions.transactions t
                    INNER JOIN sales_receipts sr
                            ON sr.transaction_id = t.transaction_id
                    INNER JOIN sales_invoices si
                            ON si.sales_invoice_id = sr.sales_invoice_id
                         WHERE si.sales_invoice_id = '$intInvoiceId'
                            AND t.payware_transaction_id IS NOT NULL";

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);
        $arrAllowed = PrimitivesResultGet($resResult);

        if (count($arrAllowed) > 0 && !$arrAllowed['bolRefundAllowed']) {

            $strError = 'Invoice is more than 13 months old, so cannot be refunded';
        }

        return $strError;
    }

    /**
     * Creates ticket for selected refund type
     *
     * @return int
     */
    protected function _createRefundTicket()
    {
        $intTicketId = null;
        $intTeamId = 1;
        $strTicketStaus = 'Closed';

        $strRefundReason = null;
        $suppressTicket = false;


        if (self::REFUND_METHOD_CHEQUE == $this->getRefundMethod()) {

            $phpLibClient = new PhpLib_Client();

            $intTeamId = $phpLibClient->getTeamIdByHandle('ABS_CHEQUE_REFUNDS');

            $strTicketStaus = 'Open';

            $strTicketText = "Customer requires a refund by cheque." .
                "\n\nThe refund amount is " . sprintf("%01.2f", $this->getAmount()) .
                "\nThe original invoice ID is " . $this->getInvoiceID() .
                "\nThe credit note ID is " . $this->getCreditNoteID() .
                "\nThe refund reason: \n". $this->getRefundReason() . $this->getRefundReasonOther();

        } elseif(self::REFUND_METHOD_DD == $this->getRefundMethod()) {

            $suppressTicket = $this->suppressAutomaticRefundTicket;

            $strTicketText = "This is a Service Notice to confirm that" .
                " your refund of ".sprintf("%01.2f", $this->getAmount()) . " is now being processed." .
                "\nYou don't need to take any action, we'll simply ensure that you get your refund.".
                "\nPlease allow up to 14 days for the money to arrive in your account.\n";

        } elseif (self::REFUND_METHOD_CC == $this->getRefundMethod()) {

            $suppressTicket = $this->suppressAutomaticRefundTicket;

            $strTicketText="Dear ".$this->getUserNames() .
                "\n\nThis is a Service Notice to confirm that your refund of " .
                sprintf("%01.2f", $this->getAmount()) . " has now been credited to your credit/debit card. " .
                "\nNo further action on your part is required. Thank you.\n";
        }

        if (!$suppressTicket) {
            $intTicketId = tickets_ticket_add(
                'Script',
                $this->getServiceID(),
                '',
                '',
                $strTicketStaus,
                0,
                $strTicketText,
                0,
                $intTeamId
            );

            if (empty($intTicketId)) {

                $this->addError('Failed to create refund ticket');
            }

            return $intTicketId;
        }
        return null;
    }

    /**
     * Set ID of ticket raised when manual refund method set
     *
     * @param int $intTicketId
     */
    function setTicketId($intTicketId)
    {
        if (true == is_numeric($intTicketId)) {

            $this->m_intRefundTicketId = $intTicketId;
        }
    }

    /**
     * Return raised ticket id when manual refund method set
     *
     * @return int
     */
    function getTicketId()
    {
        return $this->m_intRefundTicketId;
    }

    /**
     * Sets refund type
     * @param string $strRefundType
     */
    public function setRefundType($strRefundType)
    {
        $this->m_strRefundType = $strRefundType;
    }

    /**
     * Returns refund type
     * @return string
     */
    public function getRefundType()
    {
        return $this->m_strRefundType;
    }

    /**
     * Sets refund method
     * @param string $strRefundMethod
     */
    public function setRefundMethod($strRefundMethod)
    {
        $this->m_strRefundMethod = $strRefundMethod;
    }

    /**
     * Returns refund method
     * @return string
     */
    public function getRefundMethod()
    {
        return $this->m_strRefundMethod;
    }

    /**
     * Encrypts Given Payment Data
     *
     * @param string $strPaymentData Payment detail string to encrypt
     *
     * @return string Encrypted payment details string
     */
    protected function encryptPaymentData($strPaymentData)
    {
        $objEncryption = EncryptMan_Encryption::getInstance('payments');
        $strEncryptedData = '';

        // Remove non-numeric characters (spaces, hypens, etc)
        $strPaymentData = preg_replace('/\D/', '', $strPaymentData);

        try {
            $strEncryptedData = $objEncryption->encrypt($strPaymentData);
        } catch (Exception $e) {
            error_log($e->getMessage());
            return false;
        }

        return $strEncryptedData;
    }
}
