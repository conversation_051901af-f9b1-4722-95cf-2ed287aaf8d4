<?php

/**
 * Removes entry from summary table about open tickets
 *
 * @package DatabaseLibraries
 * <AUTHOR> <<EMAIL>>
 * @since   October 2002
 */

/**
 * Removes entry from summary table about open tickets
 *
 * @param int $ticket_id                 ID of the ticket
 * @param int $partner_id                Taken from partners.partners.partner_id, or '0' if plusnet
 * @param int $complaintResolvedReasonId ID of the Complaint Resolved Reason
 *
 * @return boolean
 */
function split_tickets_open_ticket_summary_delete($ticket_id, $partner_id, $complaintResolvedReasonId=null)
{
    $query = "DELETE
                FROM open_tickets
               WHERE ticket_id = '%d'
                 AND partner_id = '%d'";

    $query = sprintf(
        $query,
        $ticket_id,
        $partner_id
    );


    $connection = get_named_connection('common_tickets');

    $result = mysql_query($query, $connection)
    or report_error(__FILE__, __LINE__, mysql_error($connection));

    if (mysql_affected_rows($connection)) {

        //Check to see if we need to resolve any open complaints when closing the ticket
        $ticket = TicketClient_Ticket::get($ticket_id);

        if ($ticket->isOpenComplaint()) {

            if (!class_exists('Auth_BusTierSession')) {
                require_once('/local/data/mis/portal_modules/phplib/BusTierSession.class.php');
            }

            try {
                $objBusinessActor = Auth_BusTierSession::getCurrentBusinessActor();
                $ticket->resolveComplaint($objBusinessActor, $complaintResolvedReasonId);
            } catch (Exception $e) {
                $strProblemContent = 'Please refer to the error log on the server where exception was thrown';
                $intProblemId = pt_raise_autoproblem(
                    'AuthObjectNotAvailable',
                    'AuthObject is not availible. Failed to resolve complaint on ticket closure',
                    $strProblemContent
                );

                throw new Exception('File:'.__FILE__.':'.__LINE__.": Failed to resolve open complaint on closure of ticket $ticket_id. Raised P2: $intProblemId.");
            }
        }
        return true;
    }

    return false;
}
