<?php

	/////////////////////////////////////////////////////////////////////
	// File:     config-games-access.inc
	// Purpose:  Access mini-library for Generic Games component
	/////////////////////////////////////////////////////////////////////

	/////////////////////////////////////////////////////////////////////
	// Data

	$global_component_configurators["59"] = "config_games_configurator";

	// Data
	/////////////////////////////////////////////////////////////////////

	// Hack to insert the component configurator array into PHP5's global scope
	// if it's not already there
	if(!isset($GLOBALS['global_component_configurators']))
	{
		$GLOBALS['global_component_configurators'] = $global_component_configurators;
	}
	else
	{
		foreach($global_component_configurators as $intIndex => $strConfigurator)
		{
			$GLOBALS['global_component_configurators'][$intIndex] = $strConfigurator;
		}
	}

	/////////////////////////////////////////////////////////////
	// Function:  config_games_auto_configure
	// Purpose:   'unconfigured' -> 'queued-activate' state
	//            transition handler for auto-configuration
	// Arguments: $component_id
	/////////////////////////////////////////////////////////////

	function config_games_auto_configure ($component_id) {

		// Do nothing - games does not require configuration

	}


	/////////////////////////////////////////////////////////////
	// Function:  config_games_auto_destroy
	// Purpose:   * -> 'queued-destroy' state
	//            transition handler for auto-destruction
	// Arguments: $component_id
	/////////////////////////////////////////////////////////////

	function config_games_auto_destroy ($component_id) {

		$component = userdata_component_get ($component_id);

		switch ($component["status"]) {

		    // Destroy me
		    case "active":
		    case "deactive":
		    case "unconfigured":
			userdata_component_set_status ($component_id, "destroyed");
			break;

		    // These aren't the droids you're looking for. Move along.
		    case "queued-reactivate":
		    case "queued-deactivate":
		    case "queued-activate":
		    case "queued-deconfigure":
		    case "queued-destroy":
		    case "destroyed":
			break;

		    // The sky is falling!
		    default:
			break;

		}
	}



	/////////////////////////////////////////////////////////////
	// Function:  config_games_auto_enable
	// Purpose:   
	// Arguments: $component_id
	/////////////////////////////////////////////////////////////

	function config_games_auto_enable ($component_id) {

		// Do nothing
	}



	/////////////////////////////////////////////////////////////
	// Function:  config_games_auto_disable
	// Purpose:   
	// Arguments: $component_id
	/////////////////////////////////////////////////////////////

	function config_games_auto_disable ($component_id) {

		// Do nothing
	}


	function config_games_configurator ($component_id, $action) {

		switch ($action) {

		    case "auto_configure":
			config_games_auto_configure ($component_id);
			break;

		    case "auto_disable":
			config_games_auto_disable ($component_id);
			break;

		    case "auto_enable":
			config_games_auto_enable ($component_id);
			break;

		    case "auto_refresh":
			// Nothing to do here
			break;

		    case "auto_destroy":
			config_games_auto_destroy ($component_id);
			break;

		    default:
			break;

		}
	}
?>
