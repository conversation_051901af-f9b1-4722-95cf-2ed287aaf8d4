<?php
/**
* @package DatabaseAccessCommon
*/
/**
* @package DatabaseAccessCommon
*/
class NetLynkHardwareBundleType extends HardwareBundleType
{
	// note: the following should be private, but this is an old skool style
	// class, so you'll need to use your imagination.

	/**
	* @var string
	* @access private
	*/
	var $m_strClassPrefix = 'NetLynk';

	/**
	* @var string
	* @access private
	*/
	var $m_strSupplierTag = 'NetLynk';

	/**
	* old skool constructor
	*/
	function NetLynkHardwareBundleType()
	{
		$this->HardwareBundleType();
	}
}
