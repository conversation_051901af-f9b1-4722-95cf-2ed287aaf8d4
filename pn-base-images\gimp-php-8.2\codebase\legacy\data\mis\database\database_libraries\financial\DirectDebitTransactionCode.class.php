<?php
/**
 * DirectDebitTransactionCode Class File
 *
 * @category  LegacyCodebase
 * @package   LegacyCodebase
 * <AUTHOR> <lchu<PERSON><PERSON><PERSON>@plus.net>
 *
 * @copyright 2010 PlusNet
 * @since     File available since 2010-01-26
 */
/**
 * DirectDebitTransactionCode Class
 *
 * @category  LegacyCodebase
 * @package   LegacyCodebase
 * <AUTHOR> <lchu<PERSON><PERSON><PERSON>@plus.net>
 *
 * @copyright 2010 PlusNet
 */
class DirectDebitTransactionCode
{
	/**
	 * First transaction
	 */
	const FIRST_TRANSACTION       = 'FIRST_TRANSACTION';

	/**
	 * Regular (reoccurring) transaction 
	 */
	const REGULAR_TRANSACTION     = 'REGULAR_TRANSACTION';

	/**
	 * Resubmited transaction 
	 */
	const REPRESENTED_TRANSACTION = 'REPRESENTED_TRANSACTION';

	/**
	 * Final transaction - bank will mark DD instructions as expired 
	 */
	const FINAL_TRANSACTION       = 'FINAL_TRANSACTION';

	/**
	 * @var Integer
	 */
	protected $strDDTransactionCode;

	/**
	 * @var String
	 */
	protected $strHandle;

	/**
	 * @var string
	 */
	protected $strDescription;

	/**
	 * Populate the object from the handle
	 *
	 * @param String $strHandle Handle
	 * @desc Table desc transactions.tblDDTransactionCode;
	 * +------------------------+---------------------+------+-----+-------------------+----------------+
	 * | Field                  | Type                | Null | Key | Default           | Extra          |
	 * +------------------------+---------------------+------+-----+-------------------+----------------+
	 * | intDDTransactionCodeId | tinyint(3) unsigned |      | PRI | NULL              | auto_increment |
	 * | chrDDTransactionCode   | char(2)             |      | UNI |                   |                |
	 * | vchHandle              | varchar(30)         |      | UNI |                   |                |
	 * | vchDescription         | varchar(50)         | YES  |     | NULL              |                |
	 * | stmLastUpdate          | timestamp           | YES  |     | CURRENT_TIMESTAMP |                |
	 * +------------------------+---------------------+------+-----+-------------------+----------------+
	 *
	 * @return DirectDebitTransactionCode
	 */
	public function populateByHandle($strHandle)
	{
		$this->includeLegacyFiles();

		$strQuery = "SELECT 
				      chrDDTransactionCode as strDDTransactionCode,
				      vchHandle as strHandle,
				      vchDescription as strDescription
				    FROM transactions.tblDDTransactionCode 
				    WHERE 
				      vchHandle = '$strHandle'";
		
		$arrDetails = $this->executeSql($strQuery);

		if (count($arrDetails) > 0) {
			$this->strDDTransactionCode = $arrDetails['strDDTransactionCode'];
			$this->strHandle      = $arrDetails['strHandle'];
			$this->strDescription     = $arrDetails['strDescription'];

		} else {
			throw new InvalidHandle_Exception("Invalid Handle: $strHandle");

		}
	}

	/**
	 * Execute the query
	 *
	 * @param string $strQuery query
	 *
	 * @return array
	 */
	protected function executeSql($strQuery)
	{
		$dbhConnection = get_named_connection_with_db('transactions_reporting')
		                 or report_error(__FILE__, __LINE__, 'Failed to connect to database');
		$resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

		return PrimitivesResultGet($resResult);
	}

	/**
	 * Returns direct debit transaction code
	 *
	 * @return string
	 */
	public function getDDTransactionCode()
	{
		return $this->strDDTransactionCode;
	}

	/**
	 * Returns transaction code handle
	 *
	 * @return string
	 */
	public function getHandle()
	{
		return $this->strHandle;
	}
	
	/**
	 * Returns transaction code description
	 *
	 * @return string
	 */
	public function getDescription()
	{
		return $this->strDescription;
	}

	/**
	 * Includes lagacy files
	 * @return void
	 */
	private function includeLegacyFiles()
	{
		require_once '/local/data/mis/database/standard_include.inc';
		require_once '/local/data/mis/database/database_libraries/sql_primitives.inc';
		require_once '/local/data/mis/database/class_libraries/InvalidHandleException.class.php';
	}
}