<?php

	/////////////////////////////////////////////////////////////////////
	// File:     config-basic-email-access.inc
	// Purpose:  Access mini-library for basic email
	/////////////////////////////////////////////////////////////////////

	/////////////////////////////////////////////////////////////////////
	// Data

	$global_component_configurators["512"]  = "config_basic_email_configurator";	// plus.net
	$global_component_configurators["513"] = "config_basic_email_configurator";	// f9
	$global_component_configurators["514"] = "config_basic_email_configurator";	// freeonline

	// Data
	/////////////////////////////////////////////////////////////////////
	
	// Hack to insert the component configurator array into PHP5's global scope
	// if it's not already there
	if(!isset($GLOBALS['global_component_configurators']))
	{
		$GLOBALS['global_component_configurators'] = $global_component_configurators;
	}
	else
	{
		foreach($global_component_configurators as $intIndex => $strConfigurator)
		{
			$GLOBALS['global_component_configurators'][$intIndex] = $strConfigurator;
		}
	}

	require_once '/local/data/mis/database/crypt_config.inc';

	/////////////////////////////////////////////////////////////
	// Function:  config_basic_email_add
	// Purpose:
	// Arguments:
	/////////////////////////////////////////////////////////////

	function config_basic_email_add_mailbox($component)
	{
		$dbConnCore = get_named_connection_with_db("userdata");

		// Get the data
		$arrService = userdata_service_get($component['service_id']);
		$intServiceID = $arrService['service_id'];
		$strIsp = $arrService['isp'];
		$strUsername = $arrService['username'];
		$strPassword = $arrService['password'];

		$strEncryptedPassword = Crypt_Crypt::encrypt($strPassword, 'tblConfigBasicEmail');
		
		$component_id = $component['component_id'];
		if ($strIsp=='plus.net')
		{
			$strDBIsp = 'plusnet';
		}
		else
		{
			$strDBIsp = $strIsp;
		}

		// Create entry
		$strQuery = "INSERT INTO userdata.tblConfigBasicEmail (intComponentID, strIsp, strUsername, strPassword, strStatus)
						VALUES 
						('$component_id', '$strDBIsp', '$strUsername', '$strEncryptedPassword', 'queued-activate')";
		$resResult = PrimitivesQueryOrExit($strQuery, $dbConnCore);

	}
	
	function config_basic_email_remove_mailbox($component)
	{

		$dbConnCore = get_named_connection_with_db("userdata");

		$strQuery = "UPDATE tblConfigBasicEmail SET strStatus='queued-destroy' WHERE intComponentID='".$component['component_id']."'";

		$resResult = PrimitivesQueryOrExit($strQuery, $dbConnCore);

	}
	function config_basic_email_add ($component)
	{
		$component_id = $component['component_id'];
		
		userdata_component_set_status($component_id, 'queued-activate');

		config_basic_email_add_mailbox($component);
		
		//userdata_component_set_status($component_id, 'active');
	}

	/////////////////////////////////////////////////////////////
	// Function:  config_basic_email_delete
	// Purpose:
	// Arguments:
	/////////////////////////////////////////////////////////////

	function config_basic_email_delete($component) 
	{
		$component_id = $component['component_id'];
		
		userdata_component_set_status($component_id, 'queued-destroy');
		
		config_basic_email_remove_mailbox($component);
		
		//userdata_component_set_status($component_id, 'destroyed');

	} // function config_basic_email_delete


	/////////////////////////////////////////////////////////////
	// Function:  config_basic_email_get
	// Purpose:
	// Arguments:
	/////////////////////////////////////////////////////////////

	function config_basic_email_get($email_id) {

		$connection = get_named_connection("userdata");

		$email_id = addslashes($email_id);

		$query = 'SELECT * ' .
		           'FROM config_email ' .
		          "WHERE email_id = '$email_id'";

		$result = mysql_query($query, $connection)
			or report_error(__FILE__, __LINE__, mysql_error($connection));

		$mailbox = mysql_fetch_array($result, MYSQL_ASSOC);

		mysql_free_result($result);

		return $mailbox;
	}


	/////////////////////////////////////////////////////////////
	// Function:  config_basic_email_get_by_login
	// Purpose:
	// Arguments:
	/////////////////////////////////////////////////////////////

	function config_basic_email_get_by_login($component_id, $login) {

		$connection = get_named_connection ("userdata");

		$component_id = addslashes ($component_id);
		$login = addslashes ($login);

		$query = 'SELECT * ' .
		           'FROM config_email ' .
		          "WHERE component_id = '$component_id' " .
		            "AND login = '$login' " .
		            'AND status IN ("queued-activate", "active", "queued-reactivate")';

		$result = mysql_query($query, $connection)
			or report_error(__FILE__, __LINE__, mysql_error($connection));

		$mailbox = mysql_fetch_array($result, MYSQL_ASSOC);

		mysql_free_result($result);

		return $mailbox;
	}


	/////////////////////////////////////////////////////////////
	// Function:  config_basic_email_get_by_component
	// Purpose:
	// Arguments:
	/////////////////////////////////////////////////////////////

	function config_basic_email_get_by_component($component_id) {

		$connection = get_named_connection("userdata");

		$component_id = addslashes ($component_id);

		$query = 'SELECT * ' .
		           'FROM config_email ' .
		          "WHERE component_id = '$component_id' " .
		            'AND status IN ("active", "queued-activate", "queued-reactivate") ' .
		       'ORDER BY box_number';

		$result = mysql_query($query, $connection)
			or report_error(__FILE__, __LINE__, mysql_error($connection));

		$objCrypt = Crypt_Crypt::getCryptProvider('config_email');

		while ($mailbox = mysql_fetch_array($result, MYSQL_ASSOC)) {

			if($objCrypt->canDecrypt()) {
				$mailbox['password'] = $objCrypt->decrypt($mailbox['password']);
			}

			$mailboxes[] = $mailbox;
		}

		$objCrypt->release();

		mysql_free_result($result);

		return $mailboxes;

	} // function config_email_get_by_component




	/////////////////////////////////////////////////////////////
	// Function:  config_basic_email_alter
	// Purpose:
	// Arguments:
	/////////////////////////////////////////////////////////////

	function config_basic_email_alter($email_id, $login, $password,
	                            $redirect_to, $mlist_options, $delivery_method,
	                            $smtp_delivery_ip)
	{
		$connection = get_named_connection('userdata');

		$password         = addslashes(Crypt_Crypt::encrypt($password, 'config_email'));
		$email_id         = addslashes($email_id);
		$login            = addslashes($login);
		$redirect_to      = addslashes($redirect_to);
		$mlist_options    = addslashes($mlist_options);
		$delivery_method  = addslashes($delivery_method);
		$smtp_delivery_ip = addslashes($smtp_delivery_ip);

		// Update the mailbox
		$update = "UPDATE config_email
		              SET login = '$login', 
		                  password = '$password', 
		                  redirect_to = '$redirect_to', 
		                  mlist_options = '$mlist_options', 
		                  delivery_method = '$delivery_method',
		                  smtp_delivery_ip = '$smtp_delivery_ip', 
		                  status = 'queued-reactivate'
		            WHERE
		                  email_id = '$email_id' AND
		                  status NOT IN ('destroyed','queued-destroy')";

		mysql_query($update, $connection)
			or report_error(__FILE__, __LINE__, mysql_error($connection));

		// Get the component ID
		$query = "SELECT component_id FROM config_email WHERE email_id='$email_id'";

		$component_id = mysql_result(mysql_query($query, $connection), 0, 0);

		userdata_component_set_status($component_id, 'queued-reactivate');

	} // function config_basic_email_alter


	/////////////////////////////////////////////////////////////
	// Function:  config_basic_email_auto_configure
	// Purpose:   'unconfigured' -> 'queued-activate' state
	//	    transition handler for auto-configuration
	// Arguments: $component_id
	/////////////////////////////////////////////////////////////

	function config_basic_email_auto_configure($component_id) {

		// Find username and password
		$component = userdata_component_get($component_id);

		switch ($component['status']) {

			case 'unconfigured':
				// OK
				break;

			case 'active':
			case 'queued-activate':
			case 'queued-reactivate':
			case 'deactive':
			case 'queued-deactivate':
			case 'queued-deconfigure':
			case 'queued-destroy':
			case 'destroyed':
				// These aren't the droids you're looking for
				return;
				break;

			default:
				// The sky is falling!
				break;
		}

		// Create the configuration
		config_basic_email_add($component);


	} // function config_email_auto_configure


	/////////////////////////////////////////////////////////////
	// Function:  config_email_auto_destroy
	// Purpose:   * -> 'queued-destroy' state
	//	    transition handler for auto-destruction
	// Arguments: $component_id
	/////////////////////////////////////////////////////////////

	function config_basic_email_auto_destroy($component_id) {

		$component = userdata_component_get($component_id);

		switch ($component['status']) {

			// Free transition to 'destroyed', do not pass Go.
			case 'unconfigured':
				userdata_component_set_status($component_id, 'destroyed');
				break;

			// We have or may have a configuration, pass to the reaper
			case 'active':
			case 'queued-activate':
			case 'queued-reactivate':
			case 'deactive':
			case 'queued-deactivate':
			case 'queued-deconfigure':
			case 'queued-destroy':
				config_basic_email_delete($component);
				break;

			// There already
			case 'destroyed':
				break;

			// The sky is falling!
			default:
				break;
		}

	} // function config_email_auto_destroy


	/////////////////////////////////////////////////////////////
	// Function:  config_email_auto_refresh
	// Purpose:
	// Arguments: $component_id
	/////////////////////////////////////////////////////////////

	function config_basic_email_set_default_password($component_id, $password) {

		$connection = get_named_connection('userdata');

		$strEncryptedPassword = addslashes(Crypt_Crypt::encrypt($password, 'config_email'));

		$update = 'UPDATE config_email ' .
		             "SET password = '$strEncryptedPassword', " .
		                ' status = "queued-reactivate" ' .
		           "WHERE component_id = '$component_id' " .
		             'AND type = "basic"' .
		             'AND status IN ("active", "queued-activate", "queued-reactivate")';

		mysql_query($update, $connection)
			or report_error(__FILE__, __LINE__, mysql_error($connection));

	} // function config_email_set_default_password


	function config_basic_email_auto_refresh($component_id) {

		$component = userdata_component_get($component_id);

		switch ($component['status']) {

			// Nothing to do here
			case 'unconfigured':
			case 'queued-deconfigure':
			case 'queued-destroy':
			case 'destroyed':
				break;

			// Would happen anyway
			case 'queued-activate':
			case 'queued-deactivate':
			case 'deactive':
				break;

			case 'queued-reactivate':
				reactivate_individual_basic_mailboxes($component_id);
				break;

			// Needs update
			case 'active':
				$service = userdata_service_get($component['service_id']);

				$password = $service['password'];

				config_basic_email_set_default_password($component_id, $password);
				userdata_component_set_status($component_id, 'queued-reactivate');
				reactivate_individual_basic_mailboxes($component_id);

				break;

			// The sky is falling!
			default:
				break;
		}

	} // function config_email_auto_refresh


	function config_basic_email_configurator($component_id, $action) {

		switch ($action) {

			case 'auto_configure':
				config_basic_email_auto_configure($component_id);
				break;

			case 'auto_disable':
				// FIXME
				break;

			case 'auto_enable':
				// FIXME
				break;

			case 'auto_refresh':
				config_basic_email_auto_refresh($component_id);
				break;

			case 'auto_destroy':
				config_basic_email_auto_destroy($component_id);
				break;

			default:
				break;

		}

	} // function config_email_configurator


	/////////////////////////////////////////////////////////////////
	// Function : config_email_find_missing_qactivate_components
	// Purpose  : Find all components which are email components
	//	    but do not have a matching recod in config_email
	//	    which are queued activated or reactivated
	// Arguments: Array of all email component type ids
	// Returns  : Array of all found email component_ids
	/////////////////////////////////////////////////////////////////

	function config_basic_email_find_missing_qactivate_components($array_email_components)
	{
		$connection = get_named_connection('userdata');

		for ($count = 0; $count < count($array_email_components); ++$count)
		{
			$type_id = addslashes($array_email_components[$count]);

			if (($count+1) == count($array_email_components))
			{
				$type_list = $type_list . $type_id;
			}
			else
			{
				$type_list = $type_list . $type_id . ",";
			}
		}

		$query = 'SELECT components.component_id, config_email.email_id ' .
		           'FROM components ' .
		           'LEFT JOIN config_email ' .
		             'ON components.config_id =  config_email.email_id ' .
		          "WHERE components.component_type_id in ($type_list) " .
		            'AND components.status IN ("queued-reactivate", "queued-activate") ' .
		         'HAVING config_email.email_id is null';

		$result = mysql_query($query, $connection)
				or report_error(__FILE__, __LINE__, mysql_error($connection));

		$missing_email_components = array();

		while ($row = mysql_fetch_row($result))
		{
			$missing_email_components[] = $row[0];
		}

		mysql_free_result($result);

		return $missing_email_components;

	} // function config_email_find_missing_qactivate_components


	/////////////////////////////////////////////////////////////////
	// Function : config_email_find_missing_qdestroy_components
	// Purpose  : Find all components which are email components
	//	    but do not have a matching recod in config_email
	//	    which are queued destroy
	// Arguments: Array of all email component type ids
	// Returns  : Array of all found email component_ids
	/////////////////////////////////////////////////////////////////

	function config_basic_email_find_missing_qdestroy_components($array_email_components)
	{
		$connection = get_named_connection('userdata');

		for ($count = 0; $count < count($array_email_components); ++$count)
		{
			$type_id = addslashes($array_email_components[$count]);

			if (($count + 1) == count($array_email_components))
			{
				$type_list = $type_list . $type_id;
			}
			else
			{
				$type_list = $type_list . $type_id . ',';
			}
		}

		$query = 'SELECT components.component_id, config_email.email_id ' .
		           'FROM components ' .
		      'LEFT JOIN config_email ' .
		             'ON components.config_id =  config_email.email_id ' .
		          "WHERE components.component_type_id IN ($type_list) " .
		            'AND components.status = "queued-destroy" ' .
		         'HAVING config_email.email_id IS NULL';

		$result = mysql_query($query, $connection)
				or report_error(__FILE__, __LINE__, mysql_error($connection));

		$missing_email_components = array();

		while ($row = mysql_fetch_row($result))
		{
			$missing_email_components[] = $row[0];
		}

		mysql_free_result($result);

		return $missing_email_components;

	} // function config_email_find_missing_qdestroy_components


	/////////////////////////////////////////////////////////////////
	// Function : config_email_find_by_username_isp
	// Purpose  : Find ig there is a email id already for given
	//	      usermame or isp
	// Arguments: $username, $isp
	// Returns  : $email_id
	/////////////////////////////////////////////////////////////////

	function config_basic_email_find_by_username_isp($username, $isp)
	{
		$connection = get_named_connection('userdata');

		$username = addslashes($username);
		$isp      = addslashes($isp);

		$query = 'SELECT email_id ' .
		           'FROM config_email ' .
		          "WHERE username = '$username' " .
		            "AND isp = '$isp'" .
		            "AND status IN ('active', 'queued-activate', 'queued-reactivate')";

		$result = mysql_query($query, $connection)
				   or report_error(__FILE__, __LINE__, mysql_error($connection));

		$details = mysql_fetch_array($result);

		$email_id = $details['email_id'];

		return $email_id;

	} // function config_email_find_by_username_isp


	///////////////////////////////////////////////////////////////////////
	// Function  : reactivate_individual_basic_mailboxes
	// Purpose   : sets all active mailboxes/redirects/mailing lists to queued-reactivate
	// Arguments : $component_id
	///////////////////////////////////////////////////////////////////////

	function reactivate_individual_basic_mailboxes($component_id)
	{
		$connection = get_named_connection('userdata');

		$query_string = "UPDATE config_email
		                    SET status = 'queued-reactivate'
		                  WHERE component_id = '$component_id'
		                    AND status='active'";

		$query = mysql_query($query_string, $connection)
		         or report_error(__FILE__, __LINE__, mysql_error($connection));
	} // function reactivate_individual_basic_mailboxes

?>
