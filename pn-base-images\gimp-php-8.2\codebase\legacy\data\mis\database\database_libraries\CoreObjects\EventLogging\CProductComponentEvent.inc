<?php
	/**
	* This file declares the CProductComponentEvent class 
	*
	* @package    Core
	* @subpackage Error Logging
	* <AUTHOR>
	* @version    $Id: CProductComponentEvent.inc,v 1.4 2006-07-04 04:52:35 fzaki Exp $ 
	* @filesource
	*/

	require_once('/local/data/mis/database/database_libraries/CoreObjects/EventLogging/CEvent.inc');

	class CProductComponentEvent extends CEvent
	{

		/**
		* Product Component Instance ID (userdata.tblProductComponentInstance.intProductComponentInstanceID) 
		*
		* @var integer
		* @access private
		*/
		var $m_intProductComponentInstanceID = 0;


		/**
		* Contructor for the CProductComponentEvent class
		* Load a product component instance
		* 
		* @param int The product component instanceID
		* <AUTHOR>
		* @return boolean success
		*/
		
		function CProductComponentEvent($intProductComponentInstanceID)
		{
			//Set the member variable m_intProductComponentInstanceID
			$this->m_intProductComponentInstanceID = $intProductComponentInstanceID;
		
			return true;
		}


		//////////////
		// Accessors
		//////////////

		/**
		* Returns the component ID
		*
		* @access public
		* <AUTHOR>
		* @return integer component ID
		*/
		function getProductComponentInstanceID()
		{
			if($this->m_intProductComponentInstanceID < 1) {
				return false;
			}
		
			return $this->m_intProductComponentInstanceID;
		} 
	
	
	
		//////////////////////
		// Public Methods
		/////////////////////

		
		/**
		* Log the event for PlusTalkClearCredit

		* @access public
		* <AUTHOR>
		* @return boolean true/false
		*/
		function logPlusTalkClearCredit($intAmountPaid, $intCurrentAmount)
		{
			$intEventTypeID = $this->prvGetEventTypeID('PlusTalkClearCredit');

			$dbhConn  = get_named_connection_with_db('componentEventLogging');
			$intComponentEventID = $this->logEvent($intEventTypeID);

			if(isset($intComponentEventID))
			{
				$strQuery = 'INSERT INTO tblPlusTalkClearCredit(intComponentEventID, '.
				            'intAmountPaid, intCurrentAmount) VALUES ('.
				            "'$intComponentEventID', '$intAmountPaid', '$intCurrentAmount')";

				PrimitivesQueryOrExit($strQuery, $dbhConn,'Insert a componnet event');
				
				return TRUE;
			}
			
			return FALSE;
		}

		
		
		
		/**
		* Log the event PlusTalkPAYGCredit
		
		* @access public
		* <AUTHOR>
		* @return boolean true/false
		*/
		function logPlusTalkPAYGCredit($intAmountPaidPence, $intAmountFromPence, $intAmountToPence)
		{
			$intEventTypeID = $this->prvGetEventTypeID('PlusTalkCreditPurchase');

			$dbhConn  = get_named_connection_with_db('componentEventLogging');
			$intProductComponentEventID = $this->logEvent($intEventTypeID);

			if(isset($intProductComponentEventID))
			{
				$strQuery = 'INSERT INTO tblPlusTalkCreditPurchase(intProductComponentEventID, '.
				            'intAmountPaidPence, intAmountFromPence, intAmountToPence) VALUES ('.
				            "'$intProductComponentEventID', '$intAmountPaidPence', '$intAmountFromPence', '$intAmountToPence')";

				PrimitivesQueryOrExit($strQuery, $dbhConn,'Insert a componnet event');
				
				return TRUE;
			}
			
			return FALSE;

		}
		
		/**
		* Log the event PlusTalkPAYGCredit
		
		* @access public
		* <AUTHOR>
		* @return boolean true/false
		*/
		function logWlrPaygCredit($intAmountPaidPence, $intAmountFromPence, $intAmountToPence)
		{
			$intEventTypeID = $this->prvGetEventTypeID('WlrCreditPurchase');

			$dbhConn  = get_named_connection_with_db('componentEventLogging');
			$intProductComponentEventID = $this->logEvent($intEventTypeID);

			if(isset($intProductComponentEventID))
			{
				$strQuery = 'INSERT INTO tblWlrCreditPurchase(intProductComponentEventID, '.
				            'intAmountPaidPence, intAmountFromPence, intAmountToPence) VALUES ('.
				            "'$intProductComponentEventID', '$intAmountPaidPence', '$intAmountFromPence', '$intAmountToPence')";

				PrimitivesQueryOrExit($strQuery, $dbhConn,'CProductComponentEvent->logWlrPaygCredit');
				
				return TRUE;
			}
			
			return FALSE;

		}
		

		/**
		* Log the event PlusTalkVoicemailDecrease 
		* @access public
		* <AUTHOR>
		* @return boolean true/false
		*/
		function logPlusTalkVoicemailDecrease($intAllocationFrom, $intAllocationTo, $strReasonHandle)
		{
			$dbhConn  = get_named_connection_with_db('componentEventLogging');
			$intEventTypeID = $this->prvGetEventTypeID('PlusTalkVoicemailDecrease');
			$intProductComponentEventID = $this->logEvent($intEventTypeID);
			$intReasonID = $this->prvVMDecReasonHandleToID($strReasonHandle);
			
			if($intProductComponentEventID && $intReasonID)
			{
				$strQuery = 'INSERT INTO tblPlusTalkVoicemailDecrease(intProductComponentEventID, '.
				            'intAllocationFrom, intAllocationTo, intPlusTalkVoicemailDecreaseReasonID) VALUES ('.
				            "'$intProductComponentEventID', '$intAllocationFrom', '$intAllocationTo', '$intReasonID')";

				PrimitivesQueryOrExit($strQuery, $dbhConn,'Insert a componnet event');
				
				return TRUE;


			}
			
			return FALSE;
			
		}
	
		/**
		* Log the event PlusTalkVoicemailIncrease 
		*
		* @access public
		* <AUTHOR>
		* @return boolean true/false
		*/
		function logPlusTalkVoicemailIncrease($intAllocationFrom, $intAllocationTo, $intAmountPaidPence)
		{
			$dbhConn  = get_named_connection_with_db('componentEventLogging');
			$intEventTypeID = $this->prvGetEventTypeID('PlusTalkVoicemailIncrease');
			$intProductComponentEventID = $this->logEvent($intEventTypeID);
			
			if($intProductComponentEventID)
			{
				$strQuery = 'INSERT INTO tblPlusTalkVoicemailIncrease(intProductComponentEventID, '.
				            'intAllocationFrom, intAllocationTo, intAmountPaidPence) VALUES ('.
				            "'$intProductComponentEventID', '$intAllocationFrom', '$intAllocationTo', '$intAmountPaidPence')";

				PrimitivesQueryOrExit($strQuery, $dbhConn,'Insert a componnet event');
				
				return TRUE;


			}
			
			return FALSE;

		}

		
		/**
		* Log the event PlusTalkCancelation 
		*
		* @access public
		* <AUTHOR>
		* @return boolean true/false
		*/
		function logPlusTalkCancellation($uxtCancellationDate)
		{
			$dbhConn  = get_named_connection_with_db('componentEventLogging');
			
			$intEventTypeID = $this->prvGetEventTypeID('PlusTalkCancellation');
			$intComponentEventID = $this->logEvent($intEventTypeID);
			
			if($uxtCancellationDate < 0)
			{
				//Cancellation date makes no sense.
				return FALSE;
			}
			
			if($intComponentEventID)
			{
				$strQuery = "INSERT INTO tblPlusTalkCancellation 
				                     SET intComponentEventID = '$intComponentEventID',
				                         dtmCreated = NOW(),
				                         dteDue = FROM_UNIXTIME('$uxtCancellationDate')";

				PrimitivesQueryOrExit($strQuery, $dbhConn,'Insert a plustalk cancellation event');
				
				return TRUE;
			}
			
			return FALSE;

		}

		

		/**
		* Log the event DDIRegister
		*
		* @access public
		* <AUTHOR>
		* @return boolean true/false
		*/
		function logDDIRegister($intDDIID, $intAmountPaid, $intDDICount)
		{
			$dbhConn  = get_named_connection_with_db('componentEventLogging');
			$intEventTypeID = $this->prvGetEventTypeID('DDIRegister');
			$intProductComponentEventID = $this->logEvent($intEventTypeID);
			
			if($intProductComponentEventID)
			{
				$strQuery = 'INSERT INTO tblDDIRegister '. 
				            '(intProductComponentEventID, intDDIID, intAmountPaidPence, intDDICount) VALUES ('.
				            "'$intProductComponentEventID', '$intDDIID', '$intAmountPaid', '$intDDICount')";
				PrimitivesQueryOrExit($strQuery, $dbhConn,'Insert a componnet event');
				
				return TRUE;


			}
			
			return FALSE;
			
		}
	
		/**
		* Log the event DDIRemove
		*
		* @access public
		* <AUTHOR>
		* @return boolean true/false
		*/
		function logDDIRemove($intDDIID, $strReasonHandle)
		{
			$dbhConn  = get_named_connection_with_db('componentEventLogging');
			$intEventTypeID = $this->prvGetEventTypeID('DDIRemove');
			$intProductComponentEventID = $this->logEvent($intEventTypeID);
			$intReasonID = $this->prvDDIRemoveReasonHandleToID($strReasonHandle);
			
			if($intProductComponentEventID && $intReasonID)
			{
				$strQuery = 'INSERT INTO tblDDIRemove(intProductComponentEventID, '.
				            'intDDIID, intDDIRemoveReasonID) VALUES ('.
				            "'$intProductComponentEventID', '$intDDIID', '$intReasonID')";

				PrimitivesQueryOrExit($strQuery, $dbhConn,'Insert a componnet event');
				
				return TRUE;


			}
			
			return FALSE;
			
		}
		
		/**
		* Log the event for component status change 
		*
		* @access public
		* <AUTHOR>
		* @return boolean true/false
		*/
		function logStatusChange($strStatusChangeHandle)
		{
			$intEventTypeID = $this->prvGetEventTypeID($strStatusChangeHandle);
			if($this->logEvent($intEventTypeID))
			{
				return TRUE;
			}
			return FALSE;
			
		}
		
		/**
		* Log the event in for a product component 
		
		* @access public
		* <AUTHOR>
		* @return intEventTypeID if successfull otherwise boolean false
		*/
		function logEvent($intEventTypeID)
		{
			$dbhConn  = get_named_connection_with_db('componentEventLogging');
			$strQuery = 'INSERT INTO tblProductComponentEvent (intProductComponentInstanceID, '.
			            "intProductComponentEventTypeID, dtmEventDate) VALUES ('$this->m_intProductComponentInstanceID', ".
				    "'$intEventTypeID', NOW())";
			
			if(PrimitivesQueryOrExit($strQuery, $dbhConn,'Insert a product component event')) {
				return PrimitivesInsertIdGet($dbhConn);
			}
			
			return FALSE;
			
		}
		
        /**
         * True static cache for event types
         * Returns boolean false if the event is not found
         *
         * @param str $strEventTypeHandle The component event handle
         *
         * @return int
         */
        public static function staticGetEventTypeID($strEventTypeHandle)
        {
            static $dataCache = array();

            // As there's ~100 of these, we cache them all
            if (empty($dataCache)) {
                $query = "SELECT intProductComponentEventTypeID, vchHandle FROM tblProductComponentEventType";

                $conn = get_named_connection_with_db('componentEventLogging_reporting');
                $resResult = PrimitivesQueryOrExit($query, $conn, 'Get product component event type ID');

                // Convert into a simple key => value lookup table
                $tmpList   = PrimitivesResultsAsArrayGet($resResult, 'vchHandle');
                foreach ($tmpList as $tmpHandle => $eventType) {
                    $dataCache[$tmpHandle] = $eventType['intProductComponentEventTypeID'];
                }
            }

            if (!array_key_exists($strEventTypeHandle, $dataCache)) {
                return false;
            }

            return $dataCache[$strEventTypeHandle];
        }

        /**
         * Fetch a component event ID for this component given an event type.  Caches data
         * NOTE: the old code implemented a non-static cache, and has a method to count how many events have
         * been requested.  We therefore populate $this->m_arrEventTypes to maintain backwards compatibility...
         * Returns boolean false if the event is not found
         *
         * @param str $strEventTypeHandle The component event handle
         *
         * @return int
         */
        public function prvGetEventTypeID($strEventTypeHandle)
        {
            // Store the value in the "legacy" caching mechanism for backwards compatibility
            $pcetID = CProductComponentEvent::staticGetEventTypeID($strEventTypeHandle);

            if (!array_key_exists($strEventTypeHandle, $this->m_arrEventTypes)) {
                $this->m_arrEventTypes[$strEventTypeHandle] = $pcetID;
            }

            return $pcetID;
        }

		/**
		* Fetch the Voice Mail Decrease reason ID from handle
		*
		* @access private
		* <AUTHOR>
		* @param  string The reason handle
		* @return integer The intPlusTalkVoicemailDecreaseReasonID
		*/
		function prvVMDecReasonHandleToID($strReasonHandle)
		{
			$dbhConn  = get_named_connection_with_db('componentEventLogging');
			$strQuery = 'SELECT intPlusTalkVoicemailDecreaseReasonID '.
			            'FROM tblPlusTalkVoicemailDecreaseReason WHERE '.
			            "vchHandle = '$strReasonHandle'";

			$resResult = PrimitivesQueryOrExit($strQuery, $dbhConn,'Get MailDecreaseReasonID');
			$arrReasonID = PrimitivesResultGet($resResult);
		
				if( (isset($arrReasonID['intPlusTalkVoicemailDecreaseReasonID']) ) &&  $arrReasonID['intPlusTalkVoicemailDecreaseReasonID']  > 0)
				{
					return $arrReasonID['intPlusTalkVoicemailDecreaseReasonID'];
				}
				return FALSE;
			
		}
		
		/**
		* Fetch the DDI Remove Reason ID for handle
		*
		* @access private
		* <AUTHOR>
		* @param  string The reason handle
		* @return integer The intDDIRemoveReasonID
		*/
		function prvDDIRemoveReasonHandleToID($strReasonHandle)
		{
			$dbhConn  = get_named_connection_with_db('componentEventLogging');
			$strQuery = 'SELECT intDDIRemoveReasonID '.
			            'FROM tblDDIRemoveReason WHERE '.
			            "vchHandle = '$strReasonHandle'";
			
			$resResult = PrimitivesQueryOrExit($strQuery, $dbhConn,'Get MailDecreaseReasonID');
			$arrReasonID = PrimitivesResultGet($resResult);
		
				if( (isset($arrReasonID['intDDIRemoveReasonID']) ) &&  $arrReasonID['intDDIRemoveReasonID']  > 0)
				{
					return $arrReasonID['intDDIRemoveReasonID'];
				}
				return FALSE;
			
		}
	
	}

?>
