<?php

/**
 * Access library for WLR Call Features Bundle
 *
 * @package    Core
 * @subpackage WLR
 * @access     public
 * <AUTHOR>
 * @version    $Id: CWlrCallFeaturesBundle.php,v 1.9.2.3 2009/06/29 09:02:09 fzaki Exp $
 * @filesource
 */


require_once '/local/data/mis/database/database_libraries/components/CWlrProductComponent.inc';

/**
 * WLR Product Component
 *
 * Component for handling the Call Features Bundle
 *
 * @access     public
 * <AUTHOR>
 */
class CWlrCallFeaturesBundle extends CWlrProductComponent
{
    /**
     * Properties
     */

    var $m_intMinimumNoOfCallFeatures;
    var $m_floMonthlyCost;

    /**
     * Constructor
     */

    function CWlrCallFeaturesBundle($intProductComponentInstanceId)
    {
        $this->CWlrProductComponent($intProductComponentInstanceId);
        $this->m_intMinimumNoOfCallFeatures = $this->getMinimumNoOfCallFeatures();
        $this->m_floMonthlyCost = $this->getCurrentCost();

        return true;
    }

    /**
     * Method to get product component config
     *
     * @access   public
     * <AUTHOR> <<EMAIL>>
     * @return   array
     */

    function getConfig()
    {
        $arrBundleConfig = array();

        $dbhRead = get_named_connection_with_db('dbProductComponents');

        $strBundleConfig = "SELECT intMinimumNoOfCallFeatures \n" .
            "  FROM userdata.tblProductComponentInstance pci \n" .
            "    INNER JOIN tblProductComponent pc \n" .
            "      ON pc.intProductComponentID = pci.intProductComponentID \n" .
            "    INNER JOIN tblProductComponentConfig pcc \n" .
            "      ON pcc.intProductComponentID = pc.intProductComponentID \n" .
            "    INNER JOIN tblWlrCallFeaturesBundleConfig cc \n" .
            "      ON cc.intProductComponentConfigID = pcc.intProductComponentConfigID \n" .
            '  WHERE pci.intProductComponentInstanceID = ' . $this->getProductComponentInstanceID() . " \n" .
            '    AND pcc.intServiceComponentProductID  = ' . $this->getServiceComponentProductID() . " \n";
        if (false === ($resBundleConfig = PrimitivesQueryOrExit($strBundleConfig, $dbhRead, 'CWlrCallFeaturesBundle::getConfig', true))) {
            // If we turn off exit on failure above we should handle failure here effectively
            $bolFalse = false;

            return $bolFalse;
        }

        if (PrimitivesNumRowsGet($resBundleConfig) > 0) {
            $arrBundleConfig = PrimitivesResultGet($resBundleConfig);
        }

        return $arrBundleConfig;
    } // function getConfig()

    /**
     * Method to get minimum number of call features
     *
     * @access   public
     * <AUTHOR> <<EMAIL>>
     * @return   int (or false in case of failure)
     */

    function getMinimumNoOfCallFeatures()
    {
        if (!isset($this->m_intMinimumNoOfCallFeatures)) {
            $arrConfig = $this->getConfig();

            if (isset($arrConfig['intMinimumNoOfCallFeatures'])) {
                $this->m_intMinimumNoOfCallFeatures = $arrConfig['intMinimumNoOfCallFeatures'];
            } else {
                $this->m_intMinimumNoOfCallFeatures = false;
            }
        }

        return $this->m_intMinimumNoOfCallFeatures;
    }


    /**
     * Method to check if the number of call features on the account is correct (compared to the call fatures bundle)
     *
     * @access public
     * <AUTHOR>
     * @return boolean - true if bundle is correct
     */

    function isAmountOfCallFeaturesCorrect()
    {
        //todo
    }

    /**
     * Static method to find out type of call feature bundle by number of components
     *
     * @access public
     *
     * @param  $intNoOfActiveComponents - number of the active call features in the bundle
     *
     * <AUTHOR>
     * @return int - type of the call feature bundle, or null if $intNoOfActiveComponents<2 (or false in case of
     *             failure)
     */
    function getTypeByAmountOfFeatures($intNoOfActiveCallFeatures, $intHomePhoneComponentId)
    {
        if (!isset($intNoOfActiveCallFeatures) || !isset($intHomePhoneComponentId))
            return false;

        $arrBundles = CWlrCallFeaturesBundle::_getBundleByFeatureCount($intNoOfActiveCallFeatures, $intHomePhoneComponentId, array('pcc.intProductComponentID'));
        $intType = isset($arrBundles['intProductComponentID'])
            ? $arrBundles['intProductComponentID']
            : false;

        return $intType;
    } // function getTypeByAmountOfFeatures($intNoOfActiveCallFeatures,$intHomePhoneComponentId)

    /**
     * Get the handle of the Features Bundle determined by number of call features
     *
     * <AUTHOR> Jones" <<EMAIL>>
     * @static
     * @access    public
     *
     * @param    integer
     * @param    integer
     *
     * @return    string
     */
    function getHandleByFeatureCount($intCallFeatures, $intServiceComponentId)
    {
        $arrBundles = CWlrCallFeaturesBundle::_getBundleByFeatureCount($intCallFeatures,
                                                                       $intServiceComponentId,
                                                                       array('pc.vchHandle AS vchProductComponentHandle'));

        $strHandle = isset($arrBundles['vchProductComponentHandle']) ? $arrBundles['vchProductComponentHandle'] : false;

        return $strHandle;
    } // function getHandleByFeatureCount($intCallFeatures, $intServiceComponentId)

    /**
     * Static method to find out type of call feature bundle by number of components
     *
     * @access public
     *
     * @param  $intNoOfActiveComponents - number of the active call features in the bundle
     *
     * <AUTHOR>
     * @return int - type of the call feature bundle, or null if $intNoOfActiveComponents<2 (or false in case of
     *             failure)
     */

    function getCostByAmountOfFeatures($intNoOfActiveCallFeatures, $intHomePhoneComponentId)
    {
        if (!isset($intNoOfActiveCallFeatures) || !isset($intHomePhoneComponentId))
            return false;

        $dbhRead = get_named_connection_with_db('unprivileged_reporting');

        $strQuery = "SELECT
                      t.intCostIncVatPence
                   FROM
                      products.tblServiceComponentProduct scp
                   INNER JOIN dbProductComponents.tblProductComponentConfig pcc
                      ON pcc.intServiceComponentProductID=scp.intServiceComponentProductID AND scp.intServiceComponentID='$intHomePhoneComponentId'
                   INNER JOIN dbProductComponents.tblWlrCallFeaturesBundleConfig cfbc
                      ON pcc.intProductComponentConfigID=cfbc.intProductComponentConfigID
                   INNER JOIN dbProductComponents.tblTariff AS t
                      ON t.intProductComponentConfigID = pcc.intProductComponentConfigID
                      AND t.dtmEnd IS NULL
                   WHERE
                      intMinimumNoOfCallFeatures<='$intNoOfActiveCallFeatures'
                   ORDER BY
                      intMinimumNoOfCallFeatures DESC
                   LIMIT 1";

        if (false === ($res = PrimitivesQueryOrExit($strQuery, $dbhRead, 'CWlrCallFeaturesBundle::getCallFeatureBundleType', true))) {
            return false;
        }

        $arrRet = (PrimitivesNumRowsGet($res) > 0)
            ? PrimitivesResultGet($res)
            : array();

        $intType = count($arrRet) == 1
            ? $arrRet['intCostIncVatPence']
            : false;

        return $intType;
    }


    /**
     * Static. Returns all possible bundle types.  Caches data
     *
     * @return arr - returns all possible bundle types
     */
    public static function getAllTypes()
    {
        self::includeLegacyFiles();

        // DJM2015: all calls to this appear to be static, so it's been made formally static to reduce Strict warnings
        // Similarly, nothing appears to pass $strComponentType as a parameter (and it was unused anyway!),
        // so it's been removed...
        static $dataCache = null;

        if (is_null($dataCache)) {
            $strQuery = <<<EOQ
SELECT DISTINCT
    pcc.intProductComponentID
FROM
    tblWlrCallFeaturesBundleConfig       AS wbc
    INNER JOIN tblProductComponentConfig AS pcc ON pcc.intProductComponentConfigId = wbc.intProductComponentConfigId
EOQ;
            $dbConn = get_named_connection_with_db('dbProductComponents_reporting');
            $result = PrimitivesQueryOrExit($strQuery, $dbConn);
            $dataCache = (PrimitivesNumRowsGet($result) > 0) ? PrimitivesResultsAsListGet($result) : array();
        }

        return $dataCache;
    }

    /**
     * Static function to get monthly cost for a given combination of product and call feature bundle
     *
     * @access public
     * <AUTHOR>
     * @return int - monthly cost
     */
    function getMonthlyCost($intServiceComponentId, $strBundleHandle)
    {
        $intServiceComponentId = intval($intServiceComponentId);
        if (!$intServiceComponentId)
            return false;

        $db = get_named_connection_with_db('dbProductComponents_reporting');

        $strBundleHandle = PrimitivesRealEscapeString($strBundleHandle, $db);

        $strQuery = "SELECT
                        t.intCostIncVatPence
                     FROM
                        dbProductComponents.tblWlrCallFeaturesBundleConfig AS cfbc
                     INNER JOIN dbProductComponents.tblProductComponentConfig AS pcc
                        ON pcc.intProductComponentConfigID = cfbc.intProductComponentConfigId
                     INNER JOIN products.tblServiceComponentProduct AS scp
                        USING(intServiceComponentProductID)
                     INNER JOIN dbProductComponents.tblProductComponent pc
                        ON pc.intProductComponentID = pcc.intProductComponentID
                     INNER JOIN dbProductComponents.tblTariff AS t
                        ON t.intProductComponentConfigID = cfbc.intProductComponentConfigId
                        AND t.dtmEnd IS NULL
                     INNER JOIN dbProductComponents.tblPaymentFrequency AS pf
                        ON pf.intPaymentFrequencyID = t.intPaymentFrequencyID
                     WHERE
                        scp.intServiceComponentId = $intServiceComponentId
                        AND pc.vchHandle = '$strBundleHandle'
                        AND pf.vchHandle = 'MONTHLY'";

        $res = PrimitivesQueryOrExit($strQuery, $db, 'CWlrCallFeaturesBundle::getMonthlyCost()', false);

        if (!$res)
            return false;


        return PrimitivesResultGet($res, 'intCostIncVatPence');
    }

    /**
     * Method to get the the monthly cost of CFB
     *
     *
     * @access public
     * <AUTHOR>
     * @return int
     */
    function getCurrentCost()
    {
        $objProduct = CProduct::createInstance($this->getComponentID());

        return CWlrCallFeaturesBundle::getMonthlyCost($objProduct->getComponentTypeID(), $this->getHandle());
    }


    function getMonthlyCostByTypeId($intServiceComponentId, $intProductComponentID)
    {
        $intServiceComponentId = intval($intServiceComponentId);
        if (!$intServiceComponentId)
            return false;

        $db = get_named_connection_with_db('dbProductComponents_reporting');

        $intProductComponentID = PrimitivesRealEscapeString($intProductComponentID, $db);

        $strQuery = "SELECT t.intCostIncVatPence
                     FROM dbProductComponents.tblTariff t
                     INNER JOIN dbProductComponents.tblProductComponentConfig pcc
                             ON pcc.intProductComponentConfigID = t.intProductComponentConfigID
                     INNER JOIN dbProductComponents.tblProductComponent pc
                             ON pc.intProductComponentID = pcc.intProductComponentID
                     INNER JOIN products.tblServiceComponentProduct scp
                             ON scp.intServiceComponentProductID = pcc.intServiceComponentProductID
                     WHERE pc.intProductComponentID = '$intProductComponentID'
                       AND scp.intServiceComponentID = '$intServiceComponentId'
                       AND t.dtmEnd IS NULL";

        $res = PrimitivesQueryOrExit($strQuery, $db, 'CWlrCallFeaturesBundle::getMonthlyCostByTypeId()', false);

        if (!$res)
            return false;


        return PrimitivesResultGet($res, 'intCostIncVatPence');
    }

    /**
     * Method to get the the monthly cost of CFB
     * This is a wrapper function that calls getCurrentCost
     *
     *
     * @access private
     * <AUTHOR>
     * @return int
     */

    function calculateCharges()
    {
        return $this->getCurrentCost();
    }

    /**
     * Method generate pro rata payment details for a bundle
     *
     *
     * @access private
     *
     * @param  $intServiceID
     * @param  $intTariffID
     * @param  $uxtProRataCalculationDate
     * @param  $intAmountToProrata - amount to be prorated
     *
     * <AUTHOR>
     * @return array
     */

    function generateBundleProRataPaymentDetails($intServiceID, $intTariffID, $uxtProRataCalculationDate = false, $intAmountToProrata)
    {

        $uxtProRataCalculationDate = (isset($uxtProRataCalculationDate) && $uxtProRataCalculationDate > 0)
            ? mktime(12, 0, 0, date('m', $uxtProRataCalculationDate), date('d', $uxtProRataCalculationDate), date('Y', $uxtProRataCalculationDate))
            : mktime(12, 0, 0, date('m'), date('d'), date('Y'));

        $uxtProRataNextInvoice = CProductComponent::generateProRataNextInvoiceDate($intServiceID, $intTariffID, $uxtProRataCalculationDate);

        if (isset($uxtProRataNextInvoice) && $uxtProRataNextInvoice > 0) {
            $intProRataCost = CWlrCallFeaturesBundle::generateProRataCost($uxtProRataNextInvoice, $intTariffID, $uxtProRataCalculationDate, $intAmountToProrata);

            if (isset($intProRataCost) && $intProRataCost >= 0) {
                $floProRataCostDisplay = CProductComponent::calculateDisplayCost($intProRataCost, $intTariffID);

                $strProRataDescription = CProductComponent::generateProRataDescription($uxtProRataNextInvoice, $intTariffID, $uxtProRataCalculationDate);

                $uxtProRataContractEnd = CProductComponent::generateProRataContractEndDate($intTariffID, $uxtProRataCalculationDate);

                $arrProRataItems = array();
            }

            return array(
                'uxtProRataNextInvoice'     => $uxtProRataNextInvoice,
                'intProRataCost'            => $intProRataCost,
                'floProRataCostDisplay'     => $floProRataCostDisplay,
                'strProRataDescription'     => $strProRataDescription,
                'uxtProRataContractEnd'     => $uxtProRataContractEnd,
                'uxtProRataCalculationDate' => $uxtProRataCalculationDate,
                'arrProRataItems'           => $arrProRataItems
            );
        }

        return false;
    }

    /**
     * Method to generate prorata cost based on exact amount
     * static
     * @access public
     * <AUTHOR>
     *
     * @param  $uxtProRataNextInvoice
     * @param  $intTariffID
     * @param  $uxtProRataCalculationDate
     * @param  $intAmountToProrata - amount to be prorated
     *
     * @return
     */
    function generateProRataCost($uxtProRataNextInvoice, $intTariffID, $uxtProRataCalculationDate = false, $intAmountToProrata = 0)
    {

        $uxtProRataCalculationDate = (isset($uxtProRataCalculationDate) && $uxtProRataCalculationDate > 0)
            ? $uxtProRataCalculationDate = mktime(12, 0, 0, date('m', $uxtProRataCalculationDate), date('d', $uxtProRataCalculationDate), date('Y', $uxtProRataCalculationDate))
            : $uxtProRataCalculationDate = mktime(12, 0, 0, date('m'), date('d'), date('Y'));

        $intProRataCost = (isset($intAmountToProrata) && $intAmountToProrata !== false)
            ? CProductComponent::calculateProRataCost($uxtProRataNextInvoice, $intTariffID, $intAmountToProrata, $uxtProRataCalculationDate)
            : false;

        return $intProRataCost;
    }


    /**
     * Charges customer for the upgrade of the bundle
     *
     * @access public
     * <AUTHOR>
     *
     * @param  $floToBeProrated - amount to be prorated
     * @param  $objNewBundle    - new bundle object
     *
     * @return boolean true if succes
     */

    function chargeForUpgrade($floDiffToBeProrated, $objNewBundle = null)
    {
        if ($objNewBundle == null)
            return null;

        if (\Plusnet\Feature\FeatureToggleManager::isOnFiltered(
            'RBM_MIGRATION_COMPLETE',
            null,
            null,
            null,
            $objNewBundle->getServiceID()
        )
        ) {
            return true;
        }

        $arrBunldeProRataPaymentDetails = CWlrCallFeaturesBundle::generateBundleProRataPaymentDetails($objNewBundle->getServiceID(), $objNewBundle->getTariffID(), null, $floDiffToBeProrated);
        $floToBeCharged = $arrBunldeProRataPaymentDetails['intProRataCost'];
        $floProRataCostDisplay = $arrBunldeProRataPaymentDetails['floProRataCostDisplay'];

        if ($floToBeCharged <= 0)
            return true;

        $strDescription = $arrBunldeProRataPaymentDetails['strProRataDescription'];

        $uxtNextInvoiceDate = I18n_Date::fromTimestamp($arrBunldeProRataPaymentDetails['uxtProRataNextInvoice'])
            ->getModified(-1, I18n_Date::DAYS)
            ->getTimestamp();

        if ($floToBeCharged <= 300) {
            //raise a schedule payment
            $objPaymentScheduler = new CProductComponentPaymentScheduler($objNewBundle->getProductComponentInstanceID(), '');

            $objPaymentScheduler->addIncVatAmount($floToBeCharged,
                                                  $arrBunldeProRataPaymentDetails['uxtProRataNextInvoice'],
                                                  $strDescription,
                                                  time(),
                                                  $uxtNextInvoiceDate);

            $intSchedulePaymentID = $objPaymentScheduler->m_intScheduledPaymentID;

            if (!isset($intSchedulePaymentID)) {
                $strTicketBody = "A schedule payment could not be be raised for �$floProRataCostDisplay for the cost of '" . $strDescription . "'. Features has been activated, please take this payment manually.";
                tickets_ticket_add('Script', $objNewBundle->getServiceID(), '', '', 'Open', 0, $strTicketBody);
            }
        } else {
            //take immediate payment
            $arrInvoiceItems = array(
                array(
                    'amount'      => $floToBeCharged / 100,
                    'description' => $strDescription,
                    'gross'       => true
                )
            );

            $intInvoiceID = CWlrProduct::takePayment($objNewBundle->getServiceID(),
                                                     $arrInvoiceItems,
                                                     $floToBeCharged / 100,
                                                     $strDescription);

            if (isset($intInvoiceID) && $intInvoiceID > 0) {
                $objPaymentScheduler = new CProductComponentPaymentScheduler($objNewBundle->getProductComponentInstanceID(), '');

                $objPaymentScheduler->addIncVatAmount($floToBeCharged,
                                                      time(),
                                                      $strDescription,
                                                      time(),
                                                      $uxtNextInvoiceDate);
                $intScheduledPaymentID = $objPaymentScheduler->m_intScheduledPaymentID;

                if (($intScheduledPaymentID > 0) && ($intInvoiceID > 0)) {
                    $objScheduledPayment = new CProductComponentScheduledPayment($intScheduledPaymentID);
                    $objScheduledPayment->markAsInvoiced($intInvoiceID, $arrInvoiceItems[0]['intLineItemId']);
                }

                $strTicketBody = "A �$floProRataCostDisplay payment taken for the cost of '" . $strDescription . "'.";
                tickets_ticket_add('Script', $objNewBundle->getServiceID(), '', '', 'Closed', 0, $strTicketBody);

                return true;

            } else {
                $strTicketBody = "A schedule payment could not be be raised for �$floProRataCostDisplay for the cost of '" . $strDescription . "'. Features has been activated, please take this payment manually.";

                tickets_ticket_add('Script', $objNewBundle->getServiceID(), '', '', 'Open', 0, $strTicketBody);

                return false;
            }
        }

    }


    /**
     * Static method to assign correct Call Features Bundle to the WLR product
     * This method should be called in prov script after all call features has been activated
     *
     * @access public
     *
     * @param  $intComponentId      - WLR product component id
     * @param  $objOldBundle        - old call features bundle object (is null if there was no active bundle)
     * @param  $intOldCallFeatureId - id of the previously active call feature, null if there was no call feature or
     *                              there is more than one (they are bundled then)
     *
     * <AUTHOR>
     * @return bool - true in case of success
     */

    static function assignBundle($intComponentId, $intOldCallFeatureId = null, $bolChargeForUpgrade = true)
    {
        self::includeLegacyFiles();
        
        //creating WLR product object
        $objWlrComponent = CComponent::createInstance($intComponentId);

        if (!$objWlrComponent)
            return false;

        //this is before we do any changes to the bundle
        $objOldBundle = $objWlrComponent->getActiveCallFeaturesBundle();

        //get only active Call Features that can be bundled

        $arrFeatures = $objWlrComponent->getActiveBundlableCallFeatures();

        $objNewBundle = null;

        //get unconfigured call features bundle
        $arrBundle = $objWlrComponent->getSelectedProductComponentInstanceIDs(CWlrCallFeaturesBundle::getAllTypes(), array(PRODUCT_COMPONENT_UNCONFIGURED));
        //if there is a unconfigured bundle enable it
        if (isset($arrBundle[0])) {
            $objNewBundle = &CProductComponent::createInstance($arrBundle[0]);
        } //just double check if we really dont need a bundle
        else {
            //get nuber of active call features
            $intNoOfActiveComponents = count($arrFeatures);
            //get type of the new bundle based on number of min call features
            $intProductComponentId = CWlrCallFeaturesBundle::getTypeByAmountOfFeatures($intNoOfActiveComponents, $objWlrComponent->getComponentTypeID());
            //we need a bundle !
            if ($intProductComponentId) {
                $objNewBundle = &CProductComponent::create($objWlrComponent->getServiceId(), $intComponentId, $intProductComponentId, 0, false, 'WLR');
            }

        }

        //no old bundle and no new one
        if (!$objOldBundle && !$objNewBundle) {
            //do nothing :)
            return true;
        }

        //upgrade
        //no old bundle but we have a new one
        if (!$objOldBundle && $objNewBundle) {
            //charge for new bundle. Charge = new cost - previous cost
            $floPreviousCost = 0;
            //if there was a CallFeature on the acc
            if ($intOldCallFeatureId) {
                $objFeature = &CProductComponent::createInstance($intOldCallFeatureId);
                $floPreviousCost = $objFeature->getCurrentCost();
                unset($objFeature);
            }

            $floDiffToBeProrated = $objNewBundle->getCurrentCost() - $floPreviousCost;
            //charge the customer
            if ($floDiffToBeProrated > 0 && $bolChargeForUpgrade) {
                $objNewBundle->enable(array('bolCreateScheduledPayment' => false));
                CWlrCallFeaturesBundle::chargeForUpgrade($floDiffToBeProrated, $objNewBundle);
            } else {
                $objNewBundle->enable();
            }

            //set dates to null
            foreach ($arrFeatures as $intFeatureId) {
                $objFeature = &CProductComponent::createInstance($intFeatureId);
                $objFeature->setNextInvoiceDate(null);
                unset($objFeature);
            }

            return true;
        }

        //downgrade
        //there is an old bundle and no new bundle
        if ($objOldBundle && !$objNewBundle) {
            //destroy old bundle
            $objOldBundle->destroy();

            //set CF dates to next billing date
            $dteNextBillingDate = $objWlrComponent->getNextInvoiceDate($objWlrComponent->getServiceId(), 'WLR');

            foreach ($arrFeatures as $intFeatureId) {
                $objFeature = &CProductComponent::createInstance($intFeatureId);
                $objFeature->setNextInvoiceDate($dteNextBillingDate);
                unset($objFeature);
            }

            return true;
        }

        //there is an old bundle and we have a new one
        if ($objOldBundle && $objNewBundle) {
            //calclaute charge
            $floDiffToBeProrated = $objNewBundle->getCurrentCost() - $objOldBundle->getCurrentCost();

            //destroy old bundle
            $objOldBundle->destroy();

            //if upgrade
            if ($floDiffToBeProrated > 0 && $bolChargeForUpgrade) {
                $objNewBundle->enable(array('bolCreateScheduledPayment' => false));
                //charge the customer
                CWlrCallFeaturesBundle::chargeForUpgrade($floDiffToBeProrated, $objNewBundle);
            } else {
                $objNewBundle->enable();
            }
            //set dates of all active call features to null - we don't want to charge them separetly but as a bundle
            foreach ($arrFeatures as $intFeatureId) {
                $objFeature = &CProductComponent::createInstance($intFeatureId);
                $objFeature->setNextInvoiceDate(null);
                unset($objFeature);
            }

            return true;
        }

        return false;
    }

    /**
     * Slightly more elegant solution for what I wanted
     *
     * <AUTHOR> Jones" <<EMAIL>>
     * @static
     * @access    private
     *
     * @param    integer
     * @param    integer
     * @param    array
     *
     * @return    array
     */
    function _getBundleByFeatureCount($intFeatureCount, $intHomePhoneComponentId, $arrParams = array())
    {
        // Probably like to have a more reliable means of checking that the headings we feed in here

        if (empty($arrParams)) {
            $arrParams = array('pcc.intProductComponentID');
        }

        $dbhRead = get_named_connection_with_db('unprivileged_reporting');

        $strQuery = 'SELECT ' . implode(', ', $arrParams) . "\n" .
            "  FROM products.tblServiceComponentProduct scp \n" .
            "    INNER JOIN dbProductComponents.tblProductComponentConfig pcc \n" .
            "      ON pcc.intServiceComponentProductID = scp.intServiceComponentProductID \n" .
            "        AND scp.intServiceComponentID = '{$intHomePhoneComponentId}' \n" .
            "    INNER JOIN dbProductComponents.tblWlrCallFeaturesBundleConfig cfbc \n" .
            "      ON pcc.intProductComponentConfigID=cfbc.intProductComponentConfigID \n" .
            "    INNER JOIN dbProductComponents.tblProductComponent pc \n" .
            "      ON pcc.intProductComponentID=pc.intProductComponentID \n" .
            "    INNER JOIN dbProductComponents.tblTariff t \n" .
            "      ON t.intProductComponentConfigID=cfbc.intProductComponentConfigID \n" .
            "  WHERE intMinimumNoOfCallFeatures <= '{$intFeatureCount}' \n" .
            "  ORDER BY intMinimumNoOfCallFeatures DESC \n" .
            "  LIMIT 1 \n";

        if (false === ($resBundles = PrimitivesQueryOrExit($strQuery, $dbhRead, 'CWlrCallFeaturesBundle::_getBundleByFeatureCount', false))) {
            return false;
        }

        if (PrimitivesNumRowsGet($resBundles) == 0) {
            return array();
        }

        $arrBundles = PrimitivesResultGet($resBundles);

        return $arrBundles;
    } // function _getBundleByFeatureCount($intFeatureCount, $intHomePhoneComponentId, $arrParams = array())


    function onEnable($arrArgs = array())
    {
        return true;
    }

    function onDisable()
    {
        return true;
    }

    function onDestroy()
    {
        $this->cancelOutStandingScheduledPayments();

        return true;
    }
}

?>
