<?php
/**
 * CGenericComponent.php
 *
 * <AUTHOR> <<EMAIL>>
 */

require_once '/local/data/mis/database/database_libraries/components/CComponent.inc';
/**
 * CGenericComponent
 *
 * Small wrapper around CComponent that implements state change functions as needed.
 * This is needed for generic components that need to implement standard component methods,
 * because CComponent doesn't implement them.
 *
 * <AUTHOR> <<EMAIL>>
 */
class CGenericComponent extends CComponent
{

    /**
     * Component configurator function. Destroys the component.
     *
     * @return boolean False if the component is destroyed, otherwise true
     */
    public function destroy()
    {
        $this->prvSetStatus('destroyed');

        // Sanity check
        if ($this->getStatus() != 'destroyed') {
            return false;
        }
        $this->prvGetEventLogger()->logStatusChange('GenericProductDestroyed');
        return true;
    }
}