<?php

/**
 * Abstract product component base class.
 *
 * @package    Core
 * @access     public
 * <AUTHOR> <<EMAIL>>
 * @version    $Id: CProductComponent.inc,v ******** 2009/07/13 10:45:04 bselby Exp $
 * @filesource
 */
require_once '/local/data/mis/database/database_libraries/CoreObjects/CObject/CObject.inc';
/**
 * Defines for sub product components
 */
configDefineIfMissing('PRODUCT_COMPONENT_SUBSCRIPTION', 1);
configDefineIfMissing('PRODUCT_COMPONENT_PLUSTALK_PAYG', 2);
configDefineIfMissing('PRODUCT_COMPONENT_PLUSTALK_CALLTIME', 3);
configDefineIfMissing('PRODUCT_COMPONENT_PLUSTALK_VOICEMAIL', 4);
configDefineIfMissing('PRODUCT_COMPONENT_PLUSTALK_DDI', 5);
configDefineIfMissing('PRODUCT_COMPONENT_METRONET_MAILBOX', 6);
configDefineIfMissing('PRODUCT_COMPONENT_ADD_ON_PRODUCT_BUNDLE', 7);
configDefineIfMissing('PRODUCT_COMPONENT_WLR_CALLTIME', 8);
configDefineIfMissing('PRODUCT_COMPONENT_WLR_LINERENT', 9);
configDefineIfMissing('PRODUCT_COMPONENT_WLR_PAYG', 10);
configDefineIfMissing('PRODUCT_COMPONENT_WLR_CALLERDISPLAY', 11);
configDefineIfMissing('PRODUCT_COMPONENT_WLR_CALLWAITING', 12);
configDefineIfMissing('PRODUCT_COMPONENT_WLR_CALLDIVERSION', 13);
configDefineIfMissing('PRODUCT_COMPONENT_WLR_CALLBARRING', 14);
configDefineIfMissing('PRODUCT_COMPONENT_WLR_CALLSIGN', 15);
configDefineIfMissing('PRODUCT_COMPONENT_WLR_1471EXTRA', 16);
configDefineIfMissing('PRODUCT_COMPONENT_WLR_CALLMINDER', 17);
configDefineIfMissing('PRODUCT_COMPONENT_WLR_ANONYMOUSCALL', 18);
configDefineIfMissing('PRODUCT_COMPONENT_WLR_CHOOSETOREFUSE', 19);
configDefineIfMissing('PRODUCT_COMPONENT_WLR_VOICEMAIL', 20);
configDefineIfMissing('PRODUCT_COMPONENT_WLR_3WAYCALLING', 21);
configDefineIfMissing('PRODUCT_COMPONENT_WLR_CF_BUNDLE1', 22);
configDefineIfMissing('PRODUCT_COMPONENT_WLR_CF_BUNDLE2', 23);
configDefineIfMissing('PRODUCT_COMPONENT_WLR_CF_BUNDLE3', 24);
configDefineIfMissing('PRODUCT_COMPONENT_WLR_CF_BUNDLE4', 25);
configDefineIfMissing('PRODUCT_COMPONENT_WLR_VOICEMAIL_EXTRA', 26);
configDefineIfMissing('PRODUCT_COMPONENT_WLR_RINGBACK', 27);
configDefineIfMissing('PRODUCT_COMPONENT_WLR_REMINDERCALL', 28);
configDefineIfMissing('PRODUCT_COMPONENT_WLR_CALLBARRING_OCB', 40);
configDefineIfMissing('PRODUCT_COMPONENT_WLR_CALLBARRING_RTCC', 41);
configDefineIfMissing('PRODUCT_COMPONENT_WLR_LINE_RENTAL_SAVER', 43);
configDefineIfMissing('PRODUCT_COMPONENT_TV_KIDS', 45);
configDefineIfMissing('PRODUCT_COMPONENT_TV_HD', 46);
configDefineIfMissing('PRODUCT_COMPONENT_TV_BT_SPORT_LITE', 47);
configDefineIfMissing('PRODUCT_COMPONENT_TV_BT_SPORT', 48);
configDefineIfMissing('PRODUCT_COMPONENT_TV_ENTERTAINMENT_PLUS', 49);
configDefineIfMissing('PRODUCT_COMPONENT_TV_SKY_SPORT', 50);
configDefineIfMissing('PRODUCT_COMPONENT_WLR_CALLBARRING_CPRS', 52);
configDefineIfMissing('PRODUCT_COMPONENT_FREE_STAFF_LINE_RENTAL', 53);

// MAAF:
configDefineIfMissing('PRODUCT_COMPONENT_MAAF_WEBMAIL_BUNDLEDMAILBOXES', 29);
configDefineIfMissing('PRODUCT_COMPONENT_MAAF_WEBMAIL_PACKOF5MAILBOXES', 30);
configDefineIfMissing('PRODUCT_COMPONENT_WLR_FAVOURITE_NUMBER', 31);
configDefineIfMissing('PRODUCT_COMPONENT_WLR_CALL_BARRING_PRS', 32);
configDefineIfMissing('PRODUCT_COMPONENT_WLR_CALL_BARRING_PRS_IC', 33);
configDefineIfMissing('PRODUCT_COMPONENT_WLR_SMART_DIVERT', 34);
configDefineIfMissing('PRODUCT_COMPONENT_WLR_VOICEMAIL_PLUS', 35);
configDefineIfMissing('PRODUCT_COMPONENT_BULL_GUARD', 36);
configDefineIfMissing('PRODUCT_COMPONENT_BULL_GUARD_TRIAL', 37);
configDefineIfMissing('PRODUCT_COMPONENT_PARENTALCONTROL', 38);

// Broadband Scope
configDefineIfMissing('PRODUCT_COMPONENT_BBSCOPE_EUSUPPORT', 39);

/**
 * Product Component
 *
 * Abstract product component base class
 *
 * @access public
 * <AUTHOR> <<EMAIL>>
 */
class CProductComponent extends CObject
{
    /**
     * userdata.tblProductComponentInstance vars
     */

    /**
     * Product Component Instance ID
     *
     * @var integer
     * @access private
     */
    public $m_intProductComponentInstanceID = 0;

    /**
     * Component ID of the Product
     *
     * @var integer
     * @access private
     */
    public $m_intComponentID = 0;

    /**
     * Service ID
     *
     * @var integer
     * @access private
     */
    public $m_intServiceID = 0;

    /**
     * Product Component ID of the Product component instance
     *
     * @var integer
     * @access private
     */
    public $m_intProductComponentID = 0;

    /**
     * Status ID
     *
     * @var integer
     * @access private
     */
    public $m_intStatusID = 0;

    /**
     * Tariff ID
     *
     * @var integer
     * @access private
     */
    public $m_intTariffID = 0;

    /**
     * Next invoice date for this product component
     *
     * @var integer
     * @access private
     */
    public $m_uxtNextInvoice = 0;

    /**
     * Start date of this product component
     *
     * @var integer
     * @access private
     */
    public $m_uxtStart = 0;

    /**
     * End date of this product component
     *
     * @var integer
     * @access private
     */
    public $m_uxtEnd = 0;

    /**
     * userdata config table info
     */

    /**
     * Name of table holding config details for the instance of this product component
     *
     * @var string
     * @access private
     */
    public $m_strUserdataConfigTable = '';

    /**
     * Userdata Config Table ID
     *
     * @var integer
     * @access private
     */
    public $m_intConfigID = 0;

    /**
     * Product Component Type Handle
     *
     * @var string
     * @access private
     */
    public $m_strHandle = '';

    /**
     * Product Component Type Display Name
     *
     * @var string
     * @access private
     */
    public $m_strDisplayName = '';

    /**
     * Config record Start Date
     *
     * @var dtm
     * @access private
     */
    public $m_uxtConfigStart = 0;

    /*
    * Config record End Date
    *
    * @var dtm
    * @access private
    */
    public $m_uxtConfigEnd = 0;

    /**
     * product component table info
     */

    /**
     * Event logger object holder
     *
     * @var obj
     * @access private
     */
    public $m_objEventLogger = null;

    /**
     * The intServiceComponentProductID of the component
     *
     * @var int
     * @access private
     */
    public $m_intServiceComponentProductID = 0;

    /**
     * The intServiceComponentID of the component
     *
     * @var int
     * @access private
     */
    public $m_intServiceComponentID = 0;

    /**
     * Current Contract ID
     *
     * @var integer
     * @access private
     */
    public $m_intContractID = 0;

    /**
     * Contract start date
     *
     * @var integer
     * @access private
     */
    public $m_uxtContractStart = 0;

    /**
     * Contract end date
     *
     * @var integer
     * @access private
     */
    public $m_uxtContractEnd = 0;

    /**
     * Array to store statuses that are applicable for charging
     *
     * @var array
     */
    private static $arrChargableStatuses = array('ACTIVE', 'QUEUED_REACTIVATE', 'QUEUED_DEACTIVATE');

    /**
     * Getter for $arrChargableStatuses
     * @return array
     */
    public static function getChargableStatuses()
    {
        return self::$arrChargableStatuses;
    }
    ////////////////
    // Constructor
    ////////////////

    /**
     * Contructor for the CProductComponent class
     * Load a product component instance
     *
     * @protected
     *
     * @param int $intProductComponentInstanceID The product component instance ID
     *
     * @return boolean success
     */
    public function CProductComponent($intProductComponentInstanceID)
    {
        $this->callIncludeLegacyFiles();
        // Get the basic information about this product component
        $dbhConnection = get_named_connection_with_db('userdata');

        $strQuery = "SELECT pci.intProductComponentInstanceID,
                            pci.intComponentID,
                            pci.intProductComponentID,
                            pci.intStatusID,
                            CASE WHEN stafft.intTariffID IS NOT NULL 
                                THEN stafft.intTariffID
                                ELSE pci.intTariffID
                            END AS intTariffID,
                            UNIX_TIMESTAMP(pci.dteNextInvoice) as uxtNextInvoice,
                            UNIX_TIMESTAMP(pci.dtmStart) as uxtStart,
                            UNIX_TIMESTAMP(pci.dtmEnd) as uxtEnd,
                            c.service_id as intServiceID,
                            pc.vchHandle as strHandle,
                            pc.vchDisplayName as strDisplayName,
                            scp.intServiceComponentProductID,
                            scp.intServiceComponentID
                       FROM tblProductComponentInstance pci
                 INNER JOIN components c
                         ON pci.intComponentID = c.component_id
                 INNER JOIN dbProductComponents.tblProductComponent pc
                         ON pci.intProductComponentID = pc.intProductComponentID
                 INNER JOIN products.tblServiceComponentProduct scp
                         ON scp.intServiceComponentID = c.component_type_id
                 INNER JOIN products.tblServiceComponentProductType scpt
                         ON scp.intServiceComponentProductTypeID = scpt.intServiceComponentProductTypeID
                  LEFT JOIN userdata.components staffc
                         ON staffc.service_id = c.service_id
                        AND staffc.component_type_id = 855
                        AND staffc.status = 'active'         
                        AND pci.intProductComponentID = 1
                  LEFT JOIN userdata.tblProductComponentInstance staffpci
                         ON staffpci.intComponentID = staffc.component_id
                        AND scpt.vchHandle = 'WLR' 
                        AND staffpci.intProductComponentId = " . PRODUCT_COMPONENT_FREE_STAFF_LINE_RENTAL . "
                  LEFT JOIN dbProductComponents.tblTariff stafft
                         ON stafft.intTariffID = staffpci.intTariffID                          
                      WHERE pci.intProductComponentInstanceID = '$intProductComponentInstanceID'";

        $resProductComponent = PrimitivesQueryOrExit(
            $strQuery,
            $dbhConnection,
            'Fetch details for a product component instance'
        );

        $arrProductComponent = PrimitivesResultGet($resProductComponent);

        if (!isset($arrProductComponent['intProductComponentInstanceID'])
            || $arrProductComponent['intProductComponentInstanceID'] < 1
        ) {
            return false;
        }

        $this->m_intProductComponentInstanceID = $arrProductComponent['intProductComponentInstanceID'];
        $this->m_intComponentID = $arrProductComponent['intComponentID'];
        $this->m_intServiceID = $arrProductComponent['intServiceID'];
        $this->m_intProductComponentID = $arrProductComponent['intProductComponentID'];
        $this->m_intStatusID = $arrProductComponent['intStatusID'];
        $this->m_intTariffID = $arrProductComponent['intTariffID'];
        $this->m_uxtNextInvoice = $arrProductComponent['uxtNextInvoice'];
        $this->m_uxtStart = $arrProductComponent['uxtStart'];
        $this->m_uxtEnd = $arrProductComponent['uxtEnd'];
        $this->m_strHandle = $arrProductComponent['strHandle'];
        $this->m_strDisplayName = $arrProductComponent['strDisplayName'];
        $this->m_intServiceComponentProductID = $arrProductComponent['intServiceComponentProductID'];
        $this->m_intServiceComponentID = $arrProductComponent['intServiceComponentID'];

        //Get Contract info
        $strQuery = "SELECT intProductComponentContractID as intContractID,
                            UNIX_TIMESTAMP(dteContractStart) as uxtContractStart,
                            UNIX_TIMESTAMP(dteContractEnd) as uxtContractEnd
                       FROM userdata.tblProductComponentContract
                      WHERE intProductComponentInstanceID = '{$this->m_intProductComponentInstanceID}'
                        AND dtmCancelled IS null
                        AND dtmRenewed IS null
                        AND intNextProductComponentContractID IS null
                   ORDER BY dteContractEnd, dtmCreated
                      LIMIT 1";

        $resContract = PrimitivesQueryOrExit(
            $strQuery,
            $dbhConnection,
            'Fetch details for a product component instance contract'
        );
        $arrContract = PrimitivesResultGet($resContract);

        //Check we found the contract ok...
        if (!isset($arrContract['intContractID']) || $arrContract['intContractID'] < 1) {
            //P69886 - does the service component(MAAF webmail - in this problem) have any billable tariffs against it?
            //if not, then it's not billable and under current rules, we shouldn't be generating contracts for it.
            $isBillableTariff = false;
            if ($this->m_intTariffID > 0) {
                $tariffDetails = CProductComponent::getTariffDetails($this->m_intTariffID);
                if (isset($tariffDetails['intCostIncVatPence'])) {
                    if ($tariffDetails['intCostIncVatPence'] > 0) {
                        $isBillableTariff = true;
                    }
                }
            }

            if ($isBillableTariff) {
                $strContractHandle = self::getContractLengthHandleFromTariffID($this->m_intTariffID);
                // Maybe it doesn't need one because it doesn't have a tariff or isn't configured yet?
                // We should not be setting an error if the contract length is NONE.
                if (($this->m_intTariffID > 0 && 'NONE' != $strContractHandle)
                    || "UNCONFIGURED" != CProductComponent::getStatusHandleFromID($this->m_intStatusID)
                ) {
                    $this->setError(
                        __FILE__,
                        __LINE__,
                        "No current contract found for ProductComponentInstance id " .
                        "'{$this->m_intProductComponentInstanceID}'"
                    );

                    return false;
                }
            }

            $this->m_intContractID = 0;
        } else {
            $this->m_intContractID = $arrContract['intContractID'];
            $this->m_uxtContractStart = $arrContract['uxtContractStart'];
            $this->m_uxtContractEnd = $arrContract['uxtContractEnd'];
        }

        return true;
    }

    /**
     * Wrapper to include legacy files on instance calls - to allow mock that out in unit tests
     *
     * @return void
     */
    public function callIncludeLegacyFiles()
    {
        self::includeLegacyFiles();
    }

    /**
     * Wrapper to include legacy files on static calls - to allow mock them out in unit tests
     *
     * @return void
     */
    public static function includeLegacyFiles()
    {
        if (!defined('CUSTOMER_DETAILS')) {
            require_once DATABASE_LIBRARY_ROOT . '../database_local.inc';
            require_once DATABASE_LIBRARY_ROOT . '../standard_include.inc';

            require_once SQL_PRIMITIVES_LIBRARY;
            require_once USERDATA_ACCESS_LIBRARY;
            require_once DATABASE_LIBRARY_ROOT . 'programme-tool-access.inc';
        }

        require_once COMPONENT_DEFINES_LIBRARY;
        require_once '/local/data/mis/database/database_libraries/CoreObjects/CObject/CObject.inc';
        require_once '/local/data/mis/database/database_libraries/CoreObjects/Utility/CValidator.inc';
        require_once '/local/data/mis/database/database_libraries/CoreObjects/EventLogging/CProductComponentEvent.inc';
        require_once '/local/data/mis/database/database_libraries/CoreObjects/Radius/CPlusTalkRadiusUser.inc';
        require_once '/local/data/mis/database/database_libraries/CoreObjects/Financial/CProductComponentPaymentScheduler.inc';
        require_once '/local/data/mis/database/database_libraries/components/CProduct.inc';
        require_once '/local/data/mis/database/database_libraries/UnbufferedAssociativeResultSet.class.php';

        // Add new call protect feature reference
        if (!defined('PRODUCT_COMPONENT_WLR_CALLPROTECT')) {
            CProductComponent::defineWlrCallProtect();
        }
    }

    /**
     * Refresh instance
     *
     * @param int $intProductComponentInstanceID Product component instance id
     *
     * @return void
     */
    public function refreshInstance($intProductComponentInstanceID)
    {
        $this->CProductComponent($intProductComponentInstanceID);
    }

    //////////////
    // Accessors
    //////////////

    /**
     * Returns the Service ID
     *
     * @access public
     * @return integer
     */
    public function getServiceID()
    {
        return $this->m_intServiceID;
    }

    /**
     * Returns the Service Component Product ID
     *
     * @access public
     * @return integer
     */
    public function getServiceComponentProductID()
    {
        return $this->m_intServiceComponentProductID;
    }

    /**
     * Returns the Service Component ID
     *
     * @access public
     * @return integer
     */
    public function getServiceComponentID()
    {
        return $this->m_intServiceComponentID;
    }

    /**
     * Returns the product component instance ID
     *
     * @access public
     * @return integer
     */
    public function getProductComponentInstanceID()
    {
        return $this->m_intProductComponentInstanceID;
    }

    /**
     * Return a string containing the extended status of the product component.
     * Used in the workplace component list in place of the basic component
     * status if the returned string is not empty.
     *
     * This method defaults to returning an empty string. Override it if
     * you want your particular product component to display something else.
     *
     * @access public
     * @return string The status to display
     */
    public function getExtendedStatus()
    {
        return '';
    }

    /**
     * Return a string describing the current status of the product component.
     * Used in the workplace component list
     *
     * This method defaults to returning an empty string. Override it if
     * you want your particular product component to display something else.
     *
     * @access public
     * @return string The description to display
     */
    public function getStatusDescription()
    {
        return '';
    }

    /**
     * Retrieves the productComponentInstanceID based on service_id,the product Component handle, and date
     *
     * @access public
     *
     * @param int        $intServiceID                 Service ID
     * @param string     $strHandle                    Product Component Handle
     * @param int        $uxtStart                     start date
     * @param int        $uxtEnd                       end date
     * @param string     $strServiceCompProdTypeHandle Service component product ttype handle
     * @param int|string $uxtCancelled                 Cancelled time
     *
     * @return int intProductComponentInstanceID (the first one)
     * CHECKME
     */
    public function getProductComponentInstanceForPeriod(
        $intServiceID,
        $strHandle,
        $uxtStart,
        $uxtEnd,
        $strServiceCompProdTypeHandle = 'PLUSTALK',
        $uxtCancelled = ''
    ) {
        $dbhConnection = get_named_connection_with_db('userdata');

        //Not elegant but dont want to disturb BB phone as this method is used all over the BB phone code to gets
        // mins etc.
        if ($strServiceCompProdTypeHandle == 'WLR') {
            $strWhereClause = $uxtCancelled == ''
                ? " AND (pci.dtmEnd >= FROM_UNIXTIME('$uxtEnd') OR pci.dtmEnd IS null)"
                : " AND pci.dtmEnd >= FROM_UNIXTIME($uxtCancelled)";
        } else {
            $strWhereClause = " AND (pci.dtmEnd <= FROM_UNIXTIME('$uxtEnd') OR pci.dtmEnd IS null)";
        }

        $strQuery = 'SELECT pci.intProductComponentInstanceID ' .
            '  FROM tblProductComponentInstance pci ' .
            ' INNER JOIN dbProductComponents.tblProductComponent pc ' .
            '    ON pci.intProductComponentID = pc.intProductComponentID ' .
            ' INNER JOIN dbProductComponents.tblStatus s ' .
            '    ON pci.intStatusID = s.intStatusID ' .
            ' INNER JOIN components c ' .
            '    ON pci.intComponentID = c.component_id ' .
            ' INNER JOIN products.service_components sc ' .
            '    ON c.component_type_id = sc.service_component_id ' .
            ' INNER JOIN products.tblServiceComponentProduct scp ' .
            '    ON scp.intServiceComponentID = sc.service_component_id ' .
            ' INNER JOIN products.tblServiceComponentProductType scpt ' .
            '    ON scp.intServiceComponentProductTypeID = scpt.intServiceComponentProductTypeID ' .
            ' WHERE ' .
            "       pc.vchHandle = '$strHandle' " .
            "   AND c.service_id = '$intServiceID' " .
            "   AND pci.dtmStart < FROM_UNIXTIME('$uxtEnd') " .
            "$strWhereClause";
        if ($strServiceCompProdTypeHandle != '') {
            $strQuery .= " AND scpt.vchHandle = '$strServiceCompProdTypeHandle' ";
        }
        $strQuery .= " ORDER BY pci.dtmStart DESC";

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection, 'Get the product component instance ID');

        $intProductComponentInstanceID = PrimitivesResultGet($resResult, 'intProductComponentInstanceID');

        if (isset($intProductComponentInstanceID) && $intProductComponentInstanceID > 0) {
            return $intProductComponentInstanceID;
        }

        return false;
    }

    /**
     * getProductComponentInstanceForPeriod() modified to return multiple IDs
     * from multiple component handles in one query
     *
     * @param int          $intServiceID                 Service ID
     * @param array        $arrHandles                   of Product Component Handles
     * @param dtmStartDate $uxtStart                     start date
     * @param dtmEndDate   $uxtEnd                       end date
     * @param string       $strServiceCompProdTypeHandle Product component product type handle
     *
     * @return array
     */
    public function getProductComponentInstanceArrayForPeriod(
        $intServiceID,
        $arrHandles,
        $uxtStart,
        $uxtEnd,
        $strServiceCompProdTypeHandle
    ) {
        if (is_array($arrHandles) and !empty($arrHandles)) {
            $strHandles = implode("','", $arrHandles);
            $strHandles = "'$strHandles'";
        } else {
            return array();
        }

        $dtmStart = "'" . date('Y-m-d', $uxtStart) . "'";
        $dtmEnd = "'" . date('Y-m-d', $uxtEnd) . "'";

        $dbhConnection = get_named_connection_with_db('userdata_reporting');
        $strQuery = 'SELECT pci.intProductComponentInstanceID ' .
            '  FROM tblProductComponentInstance pci ' .
            ' INNER JOIN dbProductComponents.tblProductComponent pc ' .
            '    ON pci.intProductComponentID = pc.intProductComponentID ' .
            ' INNER JOIN dbProductComponents.tblStatus s ' .
            '    ON pci.intStatusID = s.intStatusID ' .
            ' INNER JOIN components c ' .
            '    ON pci.intComponentID = c.component_id ' .
            ' INNER JOIN products.service_components sc ' .
            '    ON c.component_type_id = sc.service_component_id ' .
            ' INNER JOIN products.tblServiceComponentProduct scp ' .
            '    ON scp.intServiceComponentID = sc.service_component_id ' .
            ' INNER JOIN products.tblServiceComponentProductType scpt ' .
            '    ON scp.intServiceComponentProductTypeID = scpt.intServiceComponentProductTypeID ' .
            ' WHERE ' .
            "       pc.vchHandle IN ($strHandles) " .
            "   AND c.service_id = '$intServiceID' " .
            "   AND scpt.vchHandle = '$strServiceCompProdTypeHandle' " .
            "   AND DATE(pci.dtmStart) <= $dtmEnd" .
            "   AND IFNULL(DATE(pci.dtmEnd),CURDATE()) > $dtmStart ";
        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

        return PrimitivesResultsAsListGet($resResult);
    }

    /**
     * Return a list of product component instances for a service by type
     *
     * @param integer $intServiceId String id
     * @param string  $strHandle    Handle
     * @param array   $arrState     State
     *
     * @return array   of integers
     */
    public static function getProductComponentInstanceIdsByType($intServiceId, $strHandle, array $arrState = array())
    {
        self::includeLegacyFiles();

        $arrProductComponentInstanceIds = array();

        if (empty($arrState)) {
            // States perceived as being 'active' and paid for
            $arrState = array(
                'QUEUED_REACTIVATE',
                'ACTIVE',
                'QUEUED_DEACTIVATE',
                'QUEUED_DECONFIGURE',
                'DEACTIVE',
            );
        }

        $dbCon = get_named_connection_with_db('userdata_reporting');

        $intServiceId = (int)$intServiceId;
        if (!$intServiceId) {
            throw new InvalidArgumentException("Invalid service ID");
        }
        $strHandle = PrimitivesRealEscapeString($strHandle, $dbCon);
        foreach ($arrState as $i => $strState) {
            $arrState[$i] = PrimitivesRealEscapeString($strState, $dbCon);
        }
        $strStatus = implode("', '", $arrState);

        $strQuery = "
    SELECT pci.intProductComponentInstanceId
      FROM userdata.components com
INNER JOIN userdata.tblProductComponentInstance pci
        ON com.component_id = pci.intComponentId
INNER JOIN dbProductComponents.tblProductComponent pc
        ON pci.intProductComponentId = pc.intProductComponentId
INNER JOIN dbProductComponents.tblStatus s
        ON pci.intStatusId = s.intStatusId
     WHERE pc.vchHandle = '{$strHandle}'
       AND s.vchHandle IN ('{$strStatus}')
       AND com.service_id = {$intServiceId}";

        $resResult = PrimitivesQueryOrExit($strQuery, $dbCon);

        if (PrimitivesNumRowsGet($resResult) > 0) {
            $arrProductComponentInstanceIds = PrimitivesResultsAsListGet($resResult);
        }

        return $arrProductComponentInstanceIds;
    }

    /**
     * Returns the Component ID of the Product that these is a part of
     *
     * @access public
     * @return integer
     */
    public function getComponentID()
    {
        return $this->m_intComponentID;
    }

    /**
     * Returns the product component id of the product component instance
     *
     * @access public
     * @return integer
     */
    public function getProductComponentID()
    {
        return $this->m_intProductComponentID;
    }

    /**
     * Returns the Status ID of this product component instance
     *
     * @access public
     * @return integer
     */
    public function getStatusID()
    {
        return $this->m_intStatusID;
    }

    /**
     * Returns the status of the product component as a string
     *
     * @access public
     * @return string
     */
    public function getStatus()
    {
        return $this->getStatusHandleFromID($this->m_intStatusID);
    }

    /**
     * Returns the Tariff ID of this product component instance
     *
     * @access public
     * @return integer
     */
    public function getTariffID()
    {
        return $this->m_intTariffID;
    }

    /**
     * Returns the next invoice date for this product component instance
     *
     * @access public
     * @return uxt
     */
    public function getNextInvoiceDate()
    {
        return $this->m_uxtNextInvoice;
    }

    /**
     * Returns the start date for this product component instance
     *
     * @access public
     * @return uxt
     */
    public function getStartDate()
    {
        return $this->m_uxtStart;
    }

    /**
     * Returns the end date for this product component instance
     *
     * @access public
     * @return uxt
     */
    public function getEndDate()
    {
        return $this->m_uxtEnd;
    }

    /**
     * Returns the current contract ID
     *
     * @access public
     * @return uxt
     */
    public function getContractID()
    {
        return $this->m_intContractID;
    }

    /**
     * Returns the contract start date
     *
     * @access public
     * @return uxt
     */
    public function getContractStart()
    {
        return $this->m_uxtContractStart;
    }

    /**
     * Returns the contract end date
     *
     * @access public
     * @return uxt
     */
    public function getContractEnd()
    {
        return $this->m_uxtContractEnd;
    }

    /**
     * Returns the userdata config table for this product component instance
     *
     * @access public
     * @return str
     */
    public function getUserdataConfigTable()
    {
        return $this->m_strUserdataConfigTable;
    }

    /**
     * Returns the Product Component Type Handle
     *
     * @access public
     * @return string
     */
    public function getHandle()
    {
        return $this->m_strHandle;
    }

    /**
     * Returns the Product Component Type Display Name
     *
     * @access public
     * @return string
     */
    public function getDisplayName()
    {
        return $this->m_strDisplayName;
    }

    /**
     * Returns the config id for this product component instance
     *
     * @access public
     * @return int
     */
    public function getConfigID()
    {
        return $this->m_intConfigID;
    }

    /////////////////////
    // Static Methods
    ////////////////////

    /**
     * Create a product component instance
     *
     * Factory method, returns correct subclass based on the type of product component
     *
     * @access public
     * @static
     * <AUTHOR>
     *
     * @param  int $intProductComponentInstanceID Product Component Instance ID
     *
     * @return object|null|boolean product component subclass instance
     */
    public static function createInstance($intProductComponentInstanceID)
    {
        self::includeLegacyFiles();

        /**
         * Added a couple of early return nulls here,
         * as the previous code was returning a non-existent variable
         * "I think" the desired return in these cases was to return false rather than null
         * however, if I change this, then it would be a backward compatibility break
         */
        if (empty($intProductComponentInstanceID)) {
            return null;
        }

        $intProductComponentID = CProductComponent::getProductComponentIdFromInstance($intProductComponentInstanceID);

        if (empty($intProductComponentID)) {
            return null;
        }

        // Work out which sub class to create
        switch ($intProductComponentID) {
            case PRODUCT_COMPONENT_SUBSCRIPTION:
                include_once '/local/data/mis/database/database_libraries/components/CProductComponentSubscription.inc';
                $objProductComponent = new CProductComponentSubscription($intProductComponentInstanceID);
                break;

            case PRODUCT_COMPONENT_PLUSTALK_PAYG:
                include_once '/local/data/mis/database/database_libraries/components/CPlusTalkPAYG.inc';
                $objProductComponent = new CPlusTalkPAYG($intProductComponentInstanceID);
                break;

            case PRODUCT_COMPONENT_PLUSTALK_CALLTIME:
                include_once '/local/data/mis/database/database_libraries/components/CPlusTalkCalltime.inc';
                $objProductComponent = new CPlusTalkCalltime($intProductComponentInstanceID);
                break;

            case PRODUCT_COMPONENT_PLUSTALK_VOICEMAIL:
                include_once '/local/data/mis/database/database_libraries/components/CPlusTalkVoicemail.inc';
                $objProductComponent = new CPlusTalkVoicemail($intProductComponentInstanceID);
                break;

            case PRODUCT_COMPONENT_PLUSTALK_DDI:
                include_once '/local/data/mis/database/database_libraries/components/CPlusTalkDDI.inc';
                $objProductComponent = new CPlusTalkDDI($intProductComponentInstanceID);
                break;

            case PRODUCT_COMPONENT_METRONET_MAILBOX:
                include_once '/local/data/mis/database/database_libraries/components/CMetronetEmailMailbox.inc';
                $objProductComponent = new CMetronetEmailMailbox($intProductComponentInstanceID);
                break;

            case PRODUCT_COMPONENT_ADD_ON_PRODUCT_BUNDLE:
                include_once '/local/data/mis/database/database_libraries/components/CAddOnProductBundleComponent.inc';
                $objProductComponent = new CAddOnProductBundleComponent($intProductComponentInstanceID);
                break;

            case PRODUCT_COMPONENT_WLR_LINERENT:
                include_once '/local/data/mis/database/database_libraries/components/CWlrLineRent.inc';
                $objProductComponent = new CWlrLineRent($intProductComponentInstanceID);
                break;

            case PRODUCT_COMPONENT_WLR_PAYG:
                include_once '/local/data/mis/database/database_libraries/components/CWlrPayg.inc';
                $objProductComponent = new CWlrPayg($intProductComponentInstanceID);
                break;

            case PRODUCT_COMPONENT_WLR_CALLTIME:
                include_once '/local/data/mis/database/database_libraries/components/CWlrCalltime.inc';
                $objProductComponent = new CWlrCalltime($intProductComponentInstanceID);
                break;

            case PRODUCT_COMPONENT_WLR_CALLERDISPLAY:
                include_once '/local/data/mis/database/database_libraries/components/CWlrCallerDisplay.inc';
                $objProductComponent = new CWlrCallerDisplay($intProductComponentInstanceID);
                break;

            case PRODUCT_COMPONENT_WLR_CALLWAITING:
                include_once '/local/data/mis/database/database_libraries/components/CWlrCallWaiting.inc';
                $objProductComponent = new CWlrCallWaiting($intProductComponentInstanceID);
                break;

            case PRODUCT_COMPONENT_WLR_CALLDIVERSION:
                include_once '/local/data/mis/database/database_libraries/components/CWlrCallDiversion.inc';
                $objProductComponent = new CWlrCallDiversion($intProductComponentInstanceID);
                break;

            case PRODUCT_COMPONENT_WLR_CALLSIGN:
                include_once '/local/data/mis/database/database_libraries/components/CWlrCallSign.inc';
                $objProductComponent = new CWlrCallSign($intProductComponentInstanceID);
                break;

            case PRODUCT_COMPONENT_WLR_CALLMINDER:
                include_once '/local/data/mis/database/database_libraries/components/CWlrCallMinder.inc';
                $objProductComponent = new CWlrCallMinder($intProductComponentInstanceID);
                break;

            case PRODUCT_COMPONENT_WLR_ANONYMOUSCALL:
                include_once '/local/data/mis/database/database_libraries/components/CWlrAnonymousCall.inc';
                $objProductComponent = new CWlrAnonymousCall($intProductComponentInstanceID);
                break;

            case PRODUCT_COMPONENT_WLR_VOICEMAIL:
                include_once '/local/data/mis/database/database_libraries/components/CWlrVoicemail.inc';
                $objProductComponent = new CWlrVoiceMail($intProductComponentInstanceID);
                break;

            case PRODUCT_COMPONENT_WLR_3WAYCALLING:
                include_once '/local/data/mis/database/database_libraries/components/CWlr3WayCalling.inc';
                $objProductComponent = new CWlr3WayCalling($intProductComponentInstanceID);
                break;
            case PRODUCT_COMPONENT_WLR_CF_BUNDLE1:
            case PRODUCT_COMPONENT_WLR_CF_BUNDLE2:
            case PRODUCT_COMPONENT_WLR_CF_BUNDLE3:
            case PRODUCT_COMPONENT_WLR_CF_BUNDLE4:
                include_once '/local/data/mis/database/database_libraries/components/CWlrCallFeaturesBundle.php';
                $objProductComponent = new CWlrCallFeaturesBundle($intProductComponentInstanceID);
                break;
            case PRODUCT_COMPONENT_WLR_VOICEMAIL_EXTRA:
                include_once '/local/data/mis/database/database_libraries/components/CWlrVoicemailExtra.inc';
                $objProductComponent = new CWlrVoiceMailExtra($intProductComponentInstanceID);
                break;
            case PRODUCT_COMPONENT_WLR_VOICEMAIL_PLUS:
                include_once '/local/data/mis/database/database_libraries/components/CWlrVoicemailPlus.inc';
                $objProductComponent = new CWlrVoiceMailPlus($intProductComponentInstanceID);
                break;
            case PRODUCT_COMPONENT_WLR_RINGBACK:
                include_once '/local/data/mis/database/database_libraries/components/CWlrRingBack.inc';
                $objProductComponent = new CWlrRingBack($intProductComponentInstanceID);
                break;
            case PRODUCT_COMPONENT_WLR_REMINDERCALL:
                include_once '/local/data/mis/database/database_libraries/components/CWlrReminderCall.inc';
                $objProductComponent = new CWlrReminderCall($intProductComponentInstanceID);
                break;
            case PRODUCT_COMPONENT_BULL_GUARD:
                include_once '/local/data/mis/database/application_apis/SecurityProducts/BullGuardProductComponent.class.php';
                $objProductComponent = new CBullGuardProductComponent($intProductComponentInstanceID);
                break;

            case PRODUCT_COMPONENT_BULL_GUARD_TRIAL:
                include_once '/local/data/mis/database/application_apis/SecurityProducts/BullGuardTrialComponent.class.php';
                $objProductComponent = new CBullGuardTrialComponent($intProductComponentInstanceID);
                break;

            case PRODUCT_COMPONENT_WLR_FAVOURITE_NUMBER:
                include_once '/local/data/mis/database/database_libraries/components/CWlrFavouriteNumber.inc';
                $objProductComponent = new CWlrFavouriteNumber($intProductComponentInstanceID);
                break;

            case PRODUCT_COMPONENT_MAAF_WEBMAIL_BUNDLEDMAILBOXES:
                include_once '/local/data/mis/database/database_libraries/components/MAAF/BundledMailboxes.class.php';
                $objProductComponent = new MAAF_CProductComponent_BundledMailboxes($intProductComponentInstanceID);
                break;
            case PRODUCT_COMPONENT_MAAF_WEBMAIL_PACKOF5MAILBOXES:
                include_once '/local/data/mis/database/database_libraries/components/MAAF/PackOf5Mailboxes.class.php';
                $objProductComponent = new MAAF_CProductComponent_PackOf5Mailboxes($intProductComponentInstanceID);
                break;

            case PRODUCT_COMPONENT_WLR_CALL_BARRING_PRS:
                include_once '/local/data/mis/database/database_libraries/components/CWlrCallBarringPRS.inc';
                $objProductComponent = new CWlrCallBarringPRS($intProductComponentInstanceID);
                break;
            case PRODUCT_COMPONENT_WLR_CALL_BARRING_PRS_IC:
                include_once '/local/data/mis/database/database_libraries/components/CWlrCallBarringPRS_IC.inc';
                $objProductComponent = new CWlrCallBarringPRS_IC($intProductComponentInstanceID);
                break;

            case PRODUCT_COMPONENT_WLR_SMART_DIVERT:
                include_once '/local/data/mis/database/database_libraries/components/CWlrSmartDivert.inc';
                $objProductComponent = new CWlrSmartDivert($intProductComponentInstanceID);
                break;
            case PRODUCT_COMPONENT_WLR_CHOOSETOREFUSE:
                include_once '/local/data/mis/database/database_libraries/components/CWlrChooseToRefuse.inc';
                $objProductComponent = new CWlrChooseToRefuse($intProductComponentInstanceID);
                break;
            case PRODUCT_COMPONENT_WLR_1471EXTRA:
                include_once '/local/data/mis/database/database_libraries/components/CWlr1471Extra.inc';
                $objProductComponent = new CWlr1471Extra($intProductComponentInstanceID);
                break;
            case PRODUCT_COMPONENT_PARENTALCONTROL:
                include_once '/local/data/mis/database/application_apis/SecurityProducts/ParentalControlProductComponent.class.php';
                $objProductComponent = new ParentalControlProductComponent($intProductComponentInstanceID);
                break;
            case PRODUCT_COMPONENT_BBSCOPE_EUSUPPORT:
                include_once '/local/data/mis/database/database_libraries/components/CEndUserSupport.inc';
                $objProductComponent = new CEndUserSupport($intProductComponentInstanceID);
                break;
            case PRODUCT_COMPONENT_WLR_CALLBARRING_OCB:
            case PRODUCT_COMPONENT_WLR_CALLBARRING_RTCC:
                include_once '/local/data/mis/database/database_libraries/components/CWlrCallBarring.inc';
                $objProductComponent = new CWlrCallBarring($intProductComponentInstanceID);
                break;
            case PRODUCT_COMPONENT_WLR_LINE_RENTAL_SAVER:
                include_once '/local/data/mis/database/database_libraries/components/CWlrLineRentalSaver.inc';
                $objProductComponent = new CWlrLineRentalSaver($intProductComponentInstanceID);
                break;
            case PRODUCT_COMPONENT_TV_KIDS:
            case PRODUCT_COMPONENT_TV_HD:
            case PRODUCT_COMPONENT_TV_BT_SPORT_LITE:
            case PRODUCT_COMPONENT_TV_BT_SPORT:
            case PRODUCT_COMPONENT_TV_ENTERTAINMENT_PLUS:
            case PRODUCT_COMPONENT_TV_SKY_SPORT:
                include_once '/local/data/mis/database/database_libraries/components/TVChannelPack.inc';
                $objProductComponent = new TVChannelPack($intProductComponentInstanceID);
                break;
            case PRODUCT_COMPONENT_WLR_CALLPROTECT:
                include_once '/local/data/mis/database/database_libraries/components/CWlrCallProtect.inc';
                $objProductComponent = new CWlrCallProtect($intProductComponentInstanceID);
                break;
            case PRODUCT_COMPONENT_WLR_CALLBARRING_CPRS:
                include_once '/local/data/mis/database/database_libraries/components/CWlrOutgoingCallBarring.php';
                $objProductComponent = new CWlrOutgoingCallBarring($intProductComponentInstanceID);
                break;
            case PRODUCT_COMPONENT_FREE_STAFF_LINE_RENTAL:
                include_once '/local/data/mis/database/database_libraries/components/CWlrFreeStaffLineRent.inc';
                $objProductComponent = new CWlrFreeStaffLineRent($intProductComponentInstanceID);
                break;
            default:
                // Do nothing
                $objProductComponent = false;
                break;
        } // switch end

        return $objProductComponent;
    }

    /**
     * Creates a product instance from a component
     *
     * <AUTHOR> Jones" <<EMAIL>>
     * @access   public
     * @static
     *
     * @param integer $intComponentId            Component ID
     * @param string  $strProductComponentHandle Product Component Handle
     * @param array   $arrStates                 States
     *
     * @return   object  Product Component Instance Object (varies)
     */
    public static function createInstanceFromComponentID(
        $intComponentId,
        $strProductComponentHandle,
        $arrStates = array('QUEUED_ACTIVATE', 'ACTIVE', 'QUEUED_DEACTIVATE')
    ) {
        self::includeLegacyFiles();

        $intProductComponentInstanceId = CProductComponent::getProductComponentInstanceIDOfComponentAndHandle(
            $intComponentId,
            $strProductComponentHandle,
            $arrStates
        );
        if (isset($intProductComponentInstanceId) && $intProductComponentInstanceId > 0) {
            $objProductComponentInstance = CProductComponent::createInstance($intProductComponentInstanceId);
        } else {
            $objProductComponentInstance = false;
        }

        return $objProductComponentInstance;
    } // function &createInstanceFromComponent

    /**
     * Get all Products whose contracts termination notice period has expired,
     * and therefore need their contracts renewing.
     *
     * We do not need to include products that have a tariff with a contract
     * length of NONE.
     *
     * @param int    $uxtDateOfRun               Date of run
     * @param array  $arrRestrictComponentIds    Restrict component ids
     * @param array  $arrRestrictServiceIds      Restrict service ids
     * @param string $orderByHandle              Handle to order
     * @param array  $limitProductComponentTypes Product component types
     *
     * @access public
     * @static
     * <AUTHOR>
     *
     * @return UnbufferedAssociativeResultSet
     */
    public static function getExpiringContracts(
        $uxtDateOfRun = 0,
        $arrRestrictComponentIds = array(),
        $arrRestrictServiceIds = array(),
        $orderByHandle = null,
        $limitProductComponentTypes = array()
    ) {
        self::includeLegacyFiles();

        if (0 == $uxtDateOfRun) {
            $uxtDateOfRun = time();
        }

        $strRestrictComponents = '';
        if (!empty($arrRestrictComponentIds)) {
            foreach ($arrRestrictComponentIds as $intIndex => $intComponentId) {
                // This is only used for testing, so strip out any insane entries.
                if (!is_numeric($intComponentId)) {
                    unset($arrRestrictComponentIds[$intIndex]);
                }
            }

            $strRestrictComponents = "AND c.component_id IN (" . implode(',', $arrRestrictComponentIds) . ")\n ";
        }

        $strRestrictServices = '';

        foreach ($arrRestrictServiceIds as $intServiceId) {
            if (is_numeric($intServiceId) && $intServiceId > 0) {
                $arrValidatedServiceIds[] = $intServiceId;
            }
        }

        if (!empty($arrValidatedServiceIds)) {
            $strRestrictServices = "AND c.service_id IN (" . implode(',', $arrValidatedServiceIds) . ") ";
        }

        $arrProductComponents = array();

        $dbhConn = get_named_connection_with_db('userdata', true, true);

        $strQuery = "SELECT CAST(c.component_id AS UNSIGNED) as intComponentID,
                            pci.intProductComponentInstanceID,
                            pc.vchHandle as strProductComponentHandle,
                            UNIX_TIMESTAMP(con.dteContractEnd) as uxtContractEnd,
                            con.dteContractStart,
                            con.dteContractEnd,
                            cl.vchHandle as strContractLengthHandle,
                            pf.vchHandle as strPaymentFrequencyHandle,
                            t.intTariffID,
                            t.intNoticePeriodDays,
                            t.intNextTariffID
                       FROM userdata.components c
                 INNER JOIN userdata.tblProductComponentInstance pci
                         ON c.component_id = pci.intComponentID
                 INNER JOIN dbProductComponents.tblStatus s
                         ON pci.intStatusID = s.intStatusID
                 INNER JOIN userdata.tblProductComponentContract con
                         ON pci.intProductComponentInstanceID = con.intProductComponentInstanceID
                 INNER JOIN products.tblServiceComponentProduct scp
                         ON c.component_type_id = scp.intServiceComponentID
                 INNER JOIN dbProductComponents.tblProductComponentConfig pcc
                         ON scp.intServiceComponentProductID = pcc.intServiceComponentProductID
                 INNER JOIN dbProductComponents.tblProductComponent pc
                         ON pcc.intProductComponentID = pc.intProductComponentID
                 INNER JOIN dbProductComponents.tblTariff t
                         ON pcc.intProductComponentConfigID = t.intProductComponentConfigID
                        AND pci.intTariffID = t.intTariffID
                 INNER JOIN dbProductComponents.tblPaymentFrequency pf
                         ON t.intPaymentFrequencyID = pf.intPaymentFrequencyID
                 INNER JOIN dbProductComponents.tblContractLength cl
                         ON t.intContractLengthID = cl.intContractLengthID
                      WHERE DATE_SUB(con.dteContractEnd, INTERVAL t.intNoticePeriodDays DAY)
                            <= FROM_UNIXTIME($uxtDateOfRun)
                            {$strRestrictServices}
                        AND con.dtmRenewed IS NULL
                        AND con.dtmCancelled IS NULL
                        AND con.intNextProductComponentContractID IS NULL
                        AND cl.vchHandle <> 'NONE'
                            {$strRestrictComponents}
                        AND s.vchHandle IN ('ACTIVE', 'QUEUED_ACTIVATE', 'QUEUED_REACTIVATE', 'QUEUED_DEACTIVATE',
                            'DEACTIVE')";

        if (!empty($limitProductComponentTypes)) {
            if (isset($limitProductComponentTypes['IN'])) {
                $strQuery .= " AND pc.vchHandle IN " .
                    "('" . PrimitivesRealEscapeString($limitProductComponentTypes['IN'], $dbhConn) . "')";
            }

            if (isset($limitProductComponentTypes['NOT IN'])) {
                $strQuery .= " AND pc.vchHandle NOT IN " .
                    "('" . PrimitivesRealEscapeString($limitProductComponentTypes['NOT IN'], $dbhConn) . "')";
            }
        }

        if (isset($orderByHandle)) {
            $strQuery .= " ORDER BY (pc.vchHandle = '$orderByHandle') DESC";
        }

        return new UnbufferedAssociativeResultSet(
            PrimitivesQueryOrExit(
                $strQuery,
                $dbhConn,
                'Get all product component instances with expiring contracts'
            )
        );
    }


    /**
     * Get all product components which are due to be billed
     *
     * @param int   $uxtDateOfRun   unix timestamp of the date of the billing run
     * @param array $serviceIdList  a list of service-ids used to filter the results
     * @param bool  $bolNeverBilled if set, only "one-off payment product" components will be returned
     * @param int   $intLimit       if set, constrains the number of records returned
     *
     * @return return
     */
    public static function getProductComponentsForBilling(
        $uxtDateOfRun = 0,
        $serviceIdList = array(),
        $bolNeverBilled = false,
        $intLimit = -1
    ) {
        self::includeLegacyFiles();

        $uxtDateOfRun = ($uxtDateOfRun > 0 ? $uxtDateOfRun : time());

        $strServiceFilter = '';
        if (!empty($serviceIdList)) {
            $validatedSidList = array_map('intval', $serviceIdList);
            $strServiceFilter = "AND c.service_id IN (" . implode(',', $validatedSidList) . ")";
        }

        $strBillingOperator = ($bolNeverBilled ? '=' : '!=');
        $strBillingFilter = "AND pf.vchHandle $strBillingOperator 'NEVER'";

        $strLimitFilter = ($intLimit > -1 ? "LIMIT $intLimit" : '');

        // NOTE: we filter out Metronet mailboxes as they don't have a next-invoice date set and the renew method
        // therefore simply ignores them
        $strQuery = <<<EOQ
SELECT
    pci.intProductComponentInstanceID,
    c.service_id                                        AS intServiceID,
    c.component_id                                      AS intComponentID,
    pc.vchHandle                                        AS strProductComponentName
FROM
    userdata.tblProductComponentInstance                AS pci
    INNER JOIN dbProductComponents.tblTariff            AS t    ON t.intTariffID = pci.intTariffID
    INNER JOIN dbProductComponents.tblStatus            AS s    ON pci.intStatusID = s.intStatusID
        AND s.vchHandle IN ('ACTIVE', 'QUEUED_ACTIVATE', 'QUEUED_REACTIVATE', 'QUEUED_DEACTIVATE', 'DEACTIVE')
    INNER JOIN dbProductComponents.tblProductComponent  AS pc   ON pc.intProductComponentID = pci.intProductComponentID
        AND pc.vchHandle <> 'METRONET_MAILBOX'
    INNER JOIN dbProductComponents.tblPaymentFrequency  AS pf   ON pf.intPaymentFrequencyID = t.intPaymentFrequencyID
        $strBillingFilter
    INNER JOIN userdata.components                      AS c    ON c.component_id = pci.intComponentID
        $strServiceFilter
    INNER JOIN userdata.services                        AS ser  ON ser.service_id = c.service_id
WHERE
    pci.dteNextInvoice <= FROM_UNIXTIME($uxtDateOfRun)
    AND pci.dtmEnd IS null
    AND ser.status != 'destroyed'
ORDER BY
    c.service_id, intProductComponentInstanceID
$strLimitFilter
EOQ;

        $dbConn = get_named_connection_with_db('userdata');

        return new UnbufferedAssociativeResultSet(
            PrimitivesQueryOrExit($strQuery, $dbConn, 'Get all products which need to be billed today')
        );
    }

    /**
     * Abstract
     *
     * Override to calculate billing amount and schedule the charge
     *
     * @param int $uxtStartDate Start date time
     *
     * @access public
     * <AUTHOR>
     * @return array intScheduledPaymentIDs of the payments created
     */
    public function scheduleCharges($uxtStartDate)
    {
        return array();
    }


    /**
     * Update the next invoice date
     *
     * @access public
     * <AUTHOR>
     * @return boolean success
     */
    public function renew()
    {
        //Move next invoice date

        $uxtNewNextInvoice = CProductComponent::calculatePaymentPeriodEnd(
            $this->m_uxtNextInvoice,
            $this->getTariffID(),
            $this->getServiceID()
        );

        if (0 == $uxtNewNextInvoice) {
            $this->setError(
                __FILE__,
                __LINE__,
                "Failed to caluclate end of payment period for component instance id "
                . "'{$this->m_intProductComponentInstanceID}' on service id '{$this->m_intServiceID}', "
                . "tariff id '{$this->m_intTariffID}', for period starting '{$this->m_uxtNextInvoice}'"
            );

            return false;
        }

        $dbhConn = get_named_connection_with_db('userdata');

        $strQuery = "UPDATE userdata.tblProductComponentInstance
                        SET dteNextInvoice = FROM_UNIXTIME($uxtNewNextInvoice)
                      WHERE intProductComponentInstanceID = '{$this->m_intProductComponentInstanceID}'";

        if (!PrimitivesQueryOrExit(
            $strQuery,
            $dbhConn,
            'Move the next invoice date for a product component instance'
        )
        ) {
            $this->setError(
                __FILE__,
                __LINE__,
                "Failed to move invoice date for ProductComponentInstanceID '{$this->m_intProductComponentInstanceID}'"
                . "to '" . date('Y-m-d', $uxtNewNextInvoice) . "'"
            );

            return false;
        }

        $this->m_uxtNextInvoice = $uxtNewNextInvoice;

        // Handle BullGuard subscription expiry date renew
        if (COMPONENT_BULL_GUARD == $this->getServiceComponentID()) {
            $objProduct = CComponent::createInstance($this->getComponentID());

            foreach ($objProduct->getProductComponents() as $objProductComponent) {
                if (true == ($objProductComponent instanceof CBullGuardProductComponent)) {
                    $utxContractEnd = $this->getContractEnd();
                    if ($utxContractEnd) {
                        $utxExpiryDate = mktime(
                            0,
                            0,
                            0,
                            date('m', $utxContractEnd),
                            date('d', $utxContractEnd) + 1,
                            date('Y', $utxContractEnd)
                        );

                        $objProductComponent->setExpiryDate($utxExpiryDate);
                    }
                    break;
                }
            }
            unset($objProductComponent, $objProduct);
        }

        return true;
    }

    /**
     * Sets the next invoice date
     *
     * @param int|bool $uxtNextInvoice Next invoice time
     *
     * @access public
     * <AUTHOR>
     * @return boolean success
     */
    public function setNextInvoiceDate($uxtNextInvoice = null)
    {
        $dbhConn = get_named_connection_with_db('userdata');

        $strQuery = "UPDATE userdata.tblProductComponentInstance ";
        if ($uxtNextInvoice == null) {
            $strQuery .= "SET dteNextInvoice = null";
        } else {
            $strQuery .= "SET dteNextInvoice = FROM_UNIXTIME($uxtNextInvoice)";
        }
        $strQuery .= " WHERE intProductComponentInstanceID = '{$this->m_intProductComponentInstanceID}'";

        if (!PrimitivesQueryOrExit(
            $strQuery,
            $dbhConn,
            'Move the next invoice date for a product component instance'
        )
        ) {
            $this->setError(
                __FILE__,
                __LINE__,
                "Failed to move invoice date for ProductComponentInstanceID '{$this->m_intProductComponentInstanceID}'"
                . "to '" . date('Y-m-d', $uxtNextInvoice) . "'"
            );

            return false;
        }

        $this->m_uxtNextInvoice = $uxtNextInvoice;

        return true;
    }

    /**
     * Sets the contract end date
     *
     * @param int $uxtContractEnd Contract end time
     *
     * @access public
     * <AUTHOR>
     * @return boolean success
     */
    public function setContractEndDate($uxtContractEnd)
    {

        //Check if start date is before the requested end date - problem 46849
        if ($this->m_uxtContractStart >= $uxtContractEnd) {
            require_once '/local/data/mis/database/database_libraries/programme-tool-access.inc';
            $strProblemDescription = "method: setContractEndDate\n\n" . dss($this);
            pt_raise_autoproblem(
                'undefined',
                'Autoproblem: Contract error',
                $strProblemDescription,
                $strProblemDescription,
                false
            );

            return false;
        }

        if (!empty($this->m_intContractID)) {
            $dbhConn = get_named_connection_with_db('userdata');

            $strQuery = "UPDATE userdata.tblProductComponentContract
                            SET dteContractEnd = FROM_UNIXTIME($uxtContractEnd)
                          WHERE intProductComponentContractID = '{$this->m_intContractID}'";

            if (!PrimitivesQueryOrExit($strQuery, $dbhConn, 'Move the contract end date')) {
                $this->setError(
                    __FILE__,
                    __LINE__,
                    "Failed to move contract end date - ContractID '{$this->m_intContractID}' to '"
                    . date('Y-m-d', $uxtContractEnd) . "'"
                );

                return false;
            }

            $this->m_uxtContractEnd = $uxtContractEnd;
        }

        return true;
    }

    /**
     * Sets the contract start date
     *
     * @param int $uxtContractStart Contract start time
     *
     * @access public
     * <AUTHOR>
     * @return boolean success
     */
    public function setContractStartDate($uxtContractStart)
    {
        if (!empty($this->m_intContractID)) {
            $dbhConn = get_named_connection_with_db('userdata');

            $strQuery = "UPDATE userdata.tblProductComponentContract
                SET dteContractStart = FROM_UNIXTIME($uxtContractStart)
                WHERE intProductComponentContractID = '{$this->m_intContractID}'";

            if (!PrimitivesQueryOrExit($strQuery, $dbhConn, 'Move the contract start date', false)) {
                $this->setError(
                    __FILE__,
                    __LINE__,
                    "Failed to move contract start date - ContractID '{$this->m_intContractID}' to '"
                    . date('Y-m-d', $uxtContractStart) . "'"
                );

                return false;
            }

            $this->m_uxtContractStart = $uxtContractStart;
        }

        return true;
    }

    /**
     * Sets the end date of the given contract
     *
     * @param int $uxtContractEnd Contract end
     * @param int $intContractId  Contract id
     *
     * @access public
     * <AUTHOR>
     * @return boolean success
     */
    public function setContractEndDateUsingContractId($uxtContractEnd, $intContractId)
    {
        if (!empty($intContractId)) {
            $dbhConn = get_named_connection_with_db('userdata');

            $strQuery = "UPDATE userdata.tblProductComponentContract
                SET dteContractEnd = FROM_UNIXTIME($uxtContractEnd)
                WHERE intProductComponentContractID = '{$intContractId}'";

            if (!PrimitivesQueryOrExit($strQuery, $dbhConn, 'Move the contract end date', false)) {
                $this->setError(
                    __FILE__,
                    __LINE__,
                    "Failed to move contract end date - ContractID '{$intContractID}' to '"
                    . date('Y-m-d', $uxtContractEnd) . "'"
                );

                return false;
            }

            $this->m_uxtContractEnd = $uxtContractEnd;
        }

        return true;
    }

    /**
     * Abstract
     *
     * Override to calculate charges to be applied on a cancellation request
     *
     * @access public
     * <AUTHOR>
     *
     * @return array
     */
    public function calculateCancellationCharges()
    {
        //strDescription
        //intAmountExVatPence
        //intVatPence
        //uxtStartPeriodCovered if any
        //uxtEndPeriodCovered if any
        return array();
    }

    /**
     * Abstract
     *
     * Override to calculate charges to be applied at a billing run
     *
     * @access public
     * <AUTHOR>
     *
     * @return array
     */
    public function calculateCharges()
    {
        //strDescription
        //intAmountExVatPence
        //intVatPence
        //uxtStartPeriodCovered if any
        //uxtEndPeriodCovered if any
        return array();
    }


    /**
     *
     * Override to perform actions on the creation of a scheduled cancellation event
     *
     * @access public
     * <AUTHOR>
     * @return boolean success
     */
    public function onScheduledCancellation()
    {
        return true;
    }

    /**
     * Static function to get the product component id from a product component instance id
     *
     * @access public
     * @static
     * <AUTHOR>
     *
     * @param  int $intProductComponentInstanceID Product Component Instance ID
     *
     * @return int Product Component ID
     */
    public static function getProductComponentIdFromInstance($intProductComponentInstanceID)
    {
        self::includeLegacyFiles();

        $dbhConnection = get_named_connection_with_db('userdata');

        $strQuery = 'SELECT intProductComponentID ' .
            ' FROM tblProductComponentInstance ' .
            " WHERE intProductComponentInstanceID = '$intProductComponentInstanceID'";

        $resResult = PrimitivesQueryOrExit(
            $strQuery,
            $dbhConnection,
            'Get product component id for a product component instance'
        );

        $intProductComponentID = PrimitivesResultGet($resResult, 'intProductComponentID');

        return $intProductComponentID;
    }

    /**
     * Internal helper method to retrieve/cache status handles.  As there's only 9 of these, we cache in memory
     *
     * @return array
     */
    public static function getStatusHandleList()
    {
        self::includeLegacyFiles();

        static $dataCache = array();

        if (empty($dataCache)) {
            $conn = get_named_connection_with_db('dbProductComponents_reporting');
            $query = "SELECT intStatusID, vchHandle, vchDisplayName FROM tblStatus";
            $resResult = PrimitivesQueryOrExit($query, $conn, 'Fetch the status id for a PCI');
            $dataCache = PrimitivesResultsAsArrayGet($resResult);
        }

        return $dataCache;
    }

    /**
     * Get the status name of a product component instance from the status id.  As there's so few, we don't bother
     * with caching locally
     * Should be static, but this can cause problems with the (legacy) classes which inherit from this class...
     * Returns boolean false if the ID cannot be found
     *
     * @param int $intStatusID the Status ID
     *
     * @return string
     */
    public function getStatusDisplayNameFromID($intStatusID)
    {
        $handleList = self::getStatusHandleList();

        foreach ($handleList as $handle) {
            if ($handle['intStatusID'] == $intStatusID) {
                return $handle['vchDisplayName'];
            }
        }

        return false;
    }

    /**
     * Get the status handle of a product component instance from the status id.  As there's so few, we don't bother
     * with caching locally
     * Should be static, but this can cause problems with the (legacy) classes which inherit from this class...
     * Returns false if the status cannot be found
     *
     * @param int $intStatusID the Status ID
     *
     * @return mix
     */
    public static function getStatusHandleFromID($intStatusID)
    {
        self::includeLegacyFiles();

        $handleList = self::getStatusHandleList();

        foreach ($handleList as $handle) {
            if ($handle['intStatusID'] == $intStatusID) {
                return $handle['vchHandle'];
            }
        }

        return false;
    }

    /**
     * Wrapper for getStatusHandleFromID to avoid static call (required for unit test mainly)
     *
     * @param int $intStatusID the Status ID
     *
     * @return string
     */
    public function callGetStatusHandleFromID($intStatusID)
    {
        return self::getStatusHandleFromID($intStatusID);
    }

    /**
     * Get the status id of a product component instance from the status handle.  As there's so few, we don't bother
     * with caching locally
     * Should be static, but this can cause problems with the (legacy) classes which inherit from this class...
     *
     * @param string $strHandle the status handle
     *
     * @return int
     */
    public function getStatusIDFromHandle($strHandle)
    {
        $handleList = self::getStatusHandleList();

        foreach ($handleList as $handle) {
            if ($handle['vchHandle'] == $strHandle) {
                return $handle['intStatusID'];
            }
        }

        return '0';
    }

    /**
     * Internal helper method to retrieve/cache product component names.  As there's ~40 of these, we cacne
     * in memory
     *
     * @return array
     */
    public static function getProductComponentList()
    {
        self::includeLegacyFiles();

        static $dataCache = array();

        if (empty($dataCache)) {
            $conn = get_named_connection_with_db('dbProductComponents_reporting');
            $query = "SELECT intProductComponentID, vchHandle, vchDisplayName FROM tblProductComponent";
            $resResult = PrimitivesQueryOrExit($query, $conn, 'Fetch the status id list');
            $dataCache = PrimitivesResultsAsArrayGet($resResult);
        }

        return $dataCache;
    }

    /**
     * Get the product component handle, based on the id.  As there's so few, we don't bother with caching locally
     * Should be static, but this can cause problems with the (legacy) classes which inherit from this class...
     * Returns boolean false if the ID cannot be found
     *
     * @param int $intProductComponentID the component id
     *
     * @return string
     */
    public function getProductComponentHandleByID($intProductComponentID)
    {
        $pcList = self::getProductComponentList();

        foreach ($pcList as $pcData) {
            if ($pcData['intProductComponentID'] == $intProductComponentID) {
                return $pcData['vchHandle'];
            }
        }

        return false;
    }

    /**
     * Get the product component id, based on the handle.  As there's so few, we don't bother with caching locally
     * Should be static, but this can cause problems with the (legacy) classes which inherit from this class...
     * Returns boolean false if the ID cannot be found
     *
     * @param string $strHandle the PC handle
     *
     * @return int
     */
    public function getProductComponentIDByHandle($strHandle)
    {
        $pcList = self::getProductComponentList();

        foreach ($pcList as $pcData) {
            if ($pcData['vchHandle'] == $strHandle) {
                return $pcData['intProductComponentID'];
            }
        }

        return false;
    }

    /**
     * Get the display name of a product component. As there's so few, we don't bother with caching locally
     * Should be static, but this can cause problems with the (legacy) classes which inherit from this class...
     * Returns boolean false if the ID cannot be found
     *
     * @param int $intProductComponentID the Product Component ID
     *
     * @return string
     */
    public function getProductComponentDisplayName($intProductComponentID)
    {
        $pcList = self::getProductComponentList();

        foreach ($pcList as $pcData) {
            if ($pcData['intProductComponentID'] == $intProductComponentID) {
                return $pcData['vchDisplayName'];
            }
        }

        return false;
    }

    /**
     * Get the display name of a product component from the handle.
     * As there's so few, we don't bother with caching locally
     * Should be static, but this can cause problems with the (legacy) classes which inherit from this class...
     * Returns boolean false if the ID cannot be found
     *
     * @param string $strProductComponentHandle the Product Component ID
     *
     * @return string
     */
    public function getProductComponentDisplayNameFromHandle($strProductComponentHandle)
    {
        $pcList = self::getProductComponentList();

        foreach ($pcList as $pcData) {
            if ($pcData['vchHandle'] == $strProductComponentHandle) {
                return $pcData['vchDisplayName'];
            }
        }

        return false;
    }

    /**
     * Helper function to get/cache product information, based on the tariff id. As there's potentially a lot of
     * these, we cache on an individual basis
     * Checks confirm that there's a 1:1 mapping between scp and sc, so we can use the same query
     * to feed both getProductDisplayNameFromTariffID() and getServiceComponentIdFromTariffId()...
     *
     * @param int $intTariffID the tariffID
     *
     * @return array
     */
    public static function getProductDetailsFromTariffID($intTariffID)
    {
        self::includeLegacyFiles();

        static $dataCache = array();

        if (!is_numeric($intTariffID) || $intTariffID < 0) {
            return false;
        }

        if (!array_key_exists($intTariffID, $dataCache)) {
            $query = <<<EOQ
SELECT
    sc.description,
    scp.intServiceComponentID
FROM
    dbProductComponents.tblTariff                               AS t
    INNER JOIN dbProductComponents.tblProductComponentConfig    AS pcc
        ON t.intProductComponentConfigID = pcc.intProductComponentConfigID
    INNER JOIN products.tblServiceComponentProduct              AS scp
        ON scp.intServiceComponentProductID = pcc.intServiceComponentProductID
    INNER JOIN products.service_components                      AS sc
        ON scp.intServiceComponentID = sc.service_component_id
WHERE
    t.intTariffID = $intTariffID
EOQ;
            $conn = get_named_connection_with_db('dbProductComponents_reporting');
            $resResult = PrimitivesQueryOrExit($query, $conn, 'Fetch product data from a tariff ID');
            $dataCache[$intTariffID] = PrimitivesResultGet($resResult);
        }

        return $dataCache[$intTariffID];
    }

    /**
     * Get the display name of a product from the tariff id
     * Should be static, but this can cause problems with the (legacy) classes which inherit from this class...
     * Returns boolean false if the ID cannot be found
     *
     * @param int $intTariffID the Tariff ID
     *
     * @return str
     */
    public function getProductDisplayNameFromTariffID($intTariffID)
    {
        $productData = CProductComponent::getProductDetailsFromTariffID($intTariffID);

        if (!is_array($productData)) {
            return false;
        }

        return (isset($productData['description']) ? $productData['description'] : false);
    }

    /**
     * Retrieves Service Component ID based on tariff ID
     * Should be static, but this can cause problems with the (legacy) classes which inherit from this class...
     * Returns boolean false if the ID cannot be found
     *
     * @param  integer $intTariffID Tariff id
     *
     * @return integer
     */
    public function getServiceComponentIdFromTariffId($intTariffID)
    {
        $productData = CProductComponent::getProductDetailsFromTariffID($intTariffID);

        if (!is_array($productData)) {
            return false;
        }

        return (isset($productData['intServiceComponentID']) ? $productData['intServiceComponentID'] : false);
    }

    ///////////////////////////////
    // Static Validator Methods
    //////////////////////////////


    /**
     * Validates the format of a component instance ID
     *
     * @access public
     * @static
     * <AUTHOR>
     *
     * @param  integer $intProductComponentInstanceID ProductComponentInstanceID
     *
     * @return boolean true if matches, false if not
     */
    public static function isValidProductComponentInstanceIDFormat($intProductComponentInstanceID)
    {
        self::includeLegacyFiles();

        return ctype_digit($intProductComponentInstanceID);
    }

    /**
     * Validates a component instance ID
     *
     * Expensive, as it requires a database hit. Only use if really required,
     * such as for financial actions.
     * Otherwise, try isValidProductComponentInstanceIDFormat
     *
     * @access public
     * @static
     * <AUTHOR>
     *
     * @param  integer $intProductComponentInstanceID ProductComponentInstanceID
     *
     * @return boolean true if matches, false if not
     */
    public static function isValidProductComponentInstanceID($intProductComponentInstanceID)
    {
        self::includeLegacyFiles();

        if (!CProductComponent::isValidProductComponentInstanceIDFormat($intProductComponentInstanceID)) {
            return false;
        }

        $dbhConn = get_named_connection_with_db('userdata');

        return CValidator::idExistsInTable(
            $dbhConn,
            $intProductComponentInstanceID,
            'userdata',
            'tblProductComponentInstance',
            'intProductComponentInstanceID'
        );
    }


    /**
     * Write a new Product Component Instance and return the newly created object
     *
     * @access public
     * <AUTHOR>
     *
     * @param int      $intServiceID          Service id
     * @param int      $intComponentID        Component ID of main product component
     * @param int      $intProductComponentID Product Component ID
     * @param int      $intTariffID           Tariff ID (optional - pass in chosen tariff id otherwise it will use the
     *                                        default
     * @param int|bool $uxtNextInvoiceDate    Contract Start Date - pass in date to start contract from or false to
     *                                        start now
     * @param string   $strProductHandle      Product handle
     * @param int|bool $uxtStart              Start time
     *
     * @return obj Product Component Instance
     */
    public static function create(
        $intServiceID,
        $intComponentID,
        $intProductComponentID,
        $intTariffID = 0,
        $uxtNextInvoiceDate = false,
        $strProductHandle = '',
        $uxtStart = false
    ) {
        self::includeLegacyFiles();
        /*
         * Invalid $intProductComponentId must always be > 0
         * Fix for P56047 which caused billing to die because it was 0.
         */
        if ($intProductComponentID < 1) {
            return false;
        }

        // If tariff id is 0 then get the default tariff id based on the subscription tariff id
        if ($intTariffID == 0) {
            // If the following minor change breaks stuff or causes strange behaviour
            // (such as product components not having tariffs correctly defined inside
            // userdata.tblProductComponentInstance) then short of rearranging all
            // broken tariffs to fit the formula in getFirstDefaultTariffID() [ahem,
            // not gonna happen] the alternative is to have a conditional which calls
            // getFirstDefaultTariffID for product component ID 9 and getDefaultTariffID
            // for everything else.

            $intTariffID = CProductComponent::getFirstDefaultTariffID($intComponentID, $intProductComponentID);
            if ($intTariffID === false) {
                return false;
            }
        }

        $bolProvision = ($strProductHandle == 'WLR' || $strProductHandle == 'INTERNET_CONNECTION' ||
            $strProductHandle == 'YOUVIEW_TV') ? true : false;

        //check if this is a subscription component and the invoice date is not set - error
        if ('SUBSCRIPTION' == CProductComponent::getProductComponentHandleByID($intProductComponentID) &&
            false === $uxtNextInvoiceDate &&
            false === $bolProvision
        ) {
            return false;
        }

        //This will calculate next invoice date for non-subscripton components.
        if (false == $uxtNextInvoiceDate && false === $bolProvision && $intTariffID) {
            $uxtNow = time();
            $uxtNextInvoiceDate = CProductComponent::calculatePaymentPeriodEnd($uxtNow, $intTariffID, $intServiceID);
        }
        if ($uxtStart === false || !is_numeric($uxtStart) || $uxtStart < 1 || $uxtStart == '') {
            $uxtStart = time();
        }

        $intStatusID = CProductComponent::getStatusIDFromHandle('UNCONFIGURED');

        if (!isset($intStatusID) || $intStatusID < 1) {
            return false;
        }

        $dbhConnection = get_named_connection_with_db('userdata');

        $strNextInvoice = (isset($uxtNextInvoiceDate) && $uxtNextInvoiceDate > 0)
            ? "FROM_UNIXTIME('$uxtNextInvoiceDate')" : 'null';

        $strQuery = "INSERT INTO tblProductComponentInstance
                             SET intComponentID = '$intComponentID',
                                 intProductComponentID = '$intProductComponentID',
                                 intStatusID = '$intStatusID',
                                 intTariffID = '$intTariffID',
                                 dteNextInvoice = $strNextInvoice,
                                 dtmStart = FROM_UNIXTIME({$uxtStart})";
        PrimitivesQueryOrExit($strQuery, $dbhConnection);

        $intProductComponentInstanceID = PrimitivesInsertIdGet($dbhConnection);

        if (!isset($intProductComponentInstanceID) || $intProductComponentInstanceID < 1) {
            return false;
        }

        return CProductComponent::createInstance($intProductComponentInstanceID);
    }

    /**
     * Get the default tariff id for a product component
     *
     * @access public
     * <AUTHOR>
     *
     * @param  int $intComponentID        Component ID
     * @param  int $intProductComponentID Product Component ID
     *
     * @return tun  array|bool
     */
    public static function getDefaultTariffID($intComponentID, $intProductComponentID)
    {
        self::includeLegacyFiles();

        // Check to see if there is only one tariff id applicable for the product component on the component
        $dbhConnection = get_named_connection_with_db('userdata');

        $strQuery = 'SELECT t.intTariffID ' .
            '  FROM components c ' .
            ' INNER JOIN products.tblServiceComponentProduct scp ' .
            '    ON scp.intServiceComponentID = c.component_type_id ' .
            ' INNER JOIN dbProductComponents.tblProductComponentConfig pcc ' .
            '    ON scp.intServiceComponentProductID = pcc.intServiceComponentProductID ' .
            '   AND pcc.dtmStart < NOW() ' .
            '   AND (pcc.dtmEnd >= NOW() OR pcc.dtmEnd IS NULL) ' .
            "   AND pcc.intProductComponentID = '$intProductComponentID' " .
            ' INNER JOIN dbProductComponents.tblTariff t ' .
            '    ON pcc.intProductComponentConfigID = t.intProductComponentConfigID ' .
            '   AND t.dtmStart < NOW() ' .
            '   AND (t.dtmEnd >= NOW() OR t.dtmEnd IS NULL) ' .
            " WHERE c.component_id = '$intComponentID'";

        $resResult = PrimitivesQueryOrExit(
            $strQuery,
            $dbhConnection,
            'Get default tariff id for a product component id'
        );

        $arrTariffID = PrimitivesResultsAsArrayGet($resResult);

        switch (count($arrTariffID)) {
            case 0:
                return 0;  // no tariff for this product component
            case 1:
                return $arrTariffID[0]['intTariffID'];
            default:
                return false;  // multiple tariffs: error
        }
    }

    /**
     * Get first default tariff id
     *
     * @param int    $intComponentID        Component id
     * @param int    $intProductComponentID Product component id
     * @param string $strType               Type
     *
     * @return bool
     */
    public function getFirstDefaultTariffID($intComponentID, $intProductComponentID, $strType = 'DEFAULT')
    {
        $dbhConnection = get_named_connection_with_db('userdata');

        $strQuery = 'SELECT t.intTariffID ' .
            '  FROM components c ' .
            ' INNER JOIN products.tblServiceComponentProduct scp ' .
            '    ON scp.intServiceComponentID = c.component_type_id ' .
            ' INNER JOIN dbProductComponents.tblProductComponentConfig pcc ' .
            '    ON scp.intServiceComponentProductID = pcc.intServiceComponentProductID ' .
            '   AND pcc.dtmStart < NOW() ' .
            '   AND (pcc.dtmEnd >= NOW() OR pcc.dtmEnd IS NULL) ' .
            "   AND pcc.intProductComponentID = '$intProductComponentID' " .
            ' INNER JOIN dbProductComponents.tblTariff t ' .
            '    ON pcc.intProductComponentConfigID = t.intProductComponentConfigID ' .
            '   AND t.dtmStart < NOW() ' .
            '   AND (t.dtmEnd >= NOW() OR t.dtmEnd IS NULL) ' .
            ' INNER JOIN dbProductComponents.tblTariffType tt ' .
            '    ON t.intTariffTypeID = tt.intTariffTypeID ' .
            " WHERE c.component_id = '$intComponentID'" .
            "   AND tt.vchHandle = '$strType'" .
            ' ORDER BY intCostIncVatPence LIMIT 1 ';

        $resResult = PrimitivesQueryOrExit(
            $strQuery,
            $dbhConnection,
            'Get default tariff id for a product component id'
        );

        $arrTariffID = PrimitivesResultsAsArrayGet($resResult);

        if (isset($arrTariffID) && sizeof($arrTariffID) == 1) {
            return $arrTariffID[0]['intTariffID'];
        }

        return false;
    }

    /**
     * Get the default tariff id for a product component based on another tariff id
     *
     * @access public
     * <AUTHOR>
     *
     * @param  int $intTariffID           Tariff ID
     * @param  int $intProductComponentID Product Component ID
     *
     * @return array|bool
     */
    public function getTariffIDDefaultTariffID($intTariffID, $intProductComponentID)
    {
        // Check to see if there is only one tariff id applicable for the product component on the component
        $dbhConnection = get_named_connection_with_db('dbProductComponents');

        $strQuery = 'SELECT DISTINCT t2.intTariffID ' .
            '  FROM tblTariff t1 ' .
            ' INNER JOIN dbProductComponents.tblProductComponentConfig pcc1 ' .
            '    ON t1.intProductComponentConfigID = pcc1.intProductComponentConfigID ' .
            '   AND pcc1.dtmStart < NOW() ' .
            '   AND (pcc1.dtmEnd >= NOW() OR pcc1.dtmEnd IS NULL) ' .
            ' INNER JOIN dbProductComponents.tblProductComponentConfig pcc2 ' .
            '    ON pcc1.intServiceComponentProductID = pcc2.intServiceComponentProductID ' .
            '   AND pcc2.dtmStart < NOW() ' .
            '   AND (pcc2.dtmEnd >= NOW() OR pcc1.dtmEnd IS NULL) ' .
            "   AND pcc2.intProductComponentID = '$intProductComponentID' " .
            ' INNER JOIN tblTariff t2 ' .
            '    ON pcc2.intProductComponentConfigID = t2.intProductComponentConfigID ' .
            '   AND t2.dtmStart < NOW() ' .
            '   AND (t2.dtmEnd >= NOW() OR t2.dtmEnd IS NULL) ' .
            " WHERE t1.intTariffID = '$intTariffID'";

        $resResult = PrimitivesQueryOrExit(
            $strQuery,
            $dbhConnection,
            'Get default tariff id for a product component id'
        );

        $arrTariffID = PrimitivesResultsAsArrayGet($resResult);

        if (isset($arrTariffID) && sizeof($arrTariffID) == 1) {
            return $arrTariffID[0]['intTariffID'];
        }

        return false;
    }

    /**
     * Checks whether tariff should give product component instance a next invoice date (ie should be billed on a
     * regular basis)
     *
     * @access public
     * <AUTHOR>
     *
     * @param int $intServiceID              Service ID
     * @param int $intTariffID               Tariff ID
     * @param int $uxtProRataCalculationDate Pro-rata calculation date
     *
     * @return uxt Pro Rata Next Invoice Date
     */
    public static function generateProRataNextInvoiceDate($intServiceID, $intTariffID, $uxtProRataCalculationDate = 0)
    {
        self::includeLegacyFiles();

        // DJM2014: revert to previous known-good behaviour: there's some concern over date calculations
        // and we don't have time to review and confirm if there's an issue or not
        if (isset($uxtProRataCalculationDate) && $uxtProRataCalculationDate > 0) {
            $uxtProRataCalculationDate = mktime(
                12,
                0,
                0,
                date('m', $uxtProRataCalculationDate),
                date('d', $uxtProRataCalculationDate),
                date('Y', $uxtProRataCalculationDate)
            );
        } else {
            $uxtProRataCalculationDate = mktime(
                12,
                0,
                0,
                date('m'),
                date('d'),
                date('Y')
            );
        }

        $strPaymentFrequencyHandle = CProductComponent::getPaymentFrequencyHandleFromTariffID($intTariffID);

        $intMonths = self::getMonthsFromPaymentFrequencyHandle($strPaymentFrequencyHandle);

        /**
         * Userdata access library to get userdata service record
         */
        require_once USERDATA_ACCESS_LIBRARY;

        //We need to get the fresh data from database as for new provides we set the next invoice date just before
        // calling this function
        $arrService = userdata_service_get($intServiceID, true);

        $uxtBillingDate = strtotime($arrService['next_invoice']);

        $intAccountBillingDay = date('d', $uxtBillingDate);

        if (!isset($intAccountBillingDay) || $intAccountBillingDay < 1 || $intAccountBillingDay > 31) {
            return false;
        }

        // Set the date to the 28th in case of long months etc.
        $intProductBillingDay = ($intAccountBillingDay > date('t')) ? date('t') : $intAccountBillingDay;

        $uxtNextInvoice = mktime(
            12,
            0,
            0,
            date('m', $uxtProRataCalculationDate),
            $intProductBillingDay,
            date('Y', $uxtProRataCalculationDate)
        );

        if ($intMonths > 0 &&
            date('Ymd', $uxtNextInvoice) > date('Ymd', $uxtProRataCalculationDate)
        ) {
            $intMonths -= 1;
        }

        $uxtNextInvoice = mktime(
            12,
            0,
            0,
            date('m', $uxtProRataCalculationDate) + $intMonths,
            28,
            date('Y', $uxtProRataCalculationDate)
        );
        if ($intAccountBillingDay > date('t', $uxtNextInvoice)) {
            $uxtNextInvoice = mktime(
                12,
                0,
                0,
                date('m', $uxtNextInvoice),
                date('t', $uxtNextInvoice),
                date('Y', $uxtNextInvoice)
            );
        } else {
            $uxtNextInvoice = mktime(
                12,
                0,
                0,
                date('m', $uxtNextInvoice),
                $intAccountBillingDay,
                date('Y', $uxtNextInvoice)
            );
        }
        //Comenting this out as it does not work for Annual BB customers
        /*
        if (date('Ymd', $uxtNextInvoice) < date('Ymd', $uxtBillingDate)
        && date('Ymd', $uxtBillingDate) >= date('Ymd', $uxtProRataCalculationDate)
        ) {
            $uxtNextInvoice = $uxtBillingDate;
        }
        */

        return $uxtNextInvoice;
    }

    /**
     * Gets the payment frequency handle from the tariff id.  Results are cached.
     * Should be static, but this can cause problems with the (legacy) classes which inherit from this class...
     *
     * @param int $intTariffID the Tariff ID
     *
     * @return str
     */
    public function getPaymentFrequencyHandleFromTariffID($intTariffID)
    {
        static $dataCache = array();

        $intTariffID = (int)$intTariffID;

        if (!in_array($intTariffID, $dataCache)) {
            $query = <<<EOQ
SELECT
    pf.vchHandle AS strPaymentFrequencyHandle
FROM
    tblTariff t
    INNER JOIN tblPaymentFrequency pf ON t.intPaymentFrequencyID = pf.intPaymentFrequencyID
WHERE
    intTariffID = $intTariffID
EOQ;

            $conn = get_named_connection_with_db('dbProductComponents_reporting');
            $resResult = PrimitivesQueryOrExit($query, $conn, 'Get the payment frequency handle for a tariff');

            $dataCache[$intTariffID] = PrimitivesResultGet($resResult, 'strPaymentFrequencyHandle');
        }

        return $dataCache[$intTariffID];
    }

    /**
     * Gets the customer sector handle from the tariff id.  Results are cached.
     * Should be static, but this can cause problems with the (legacy) classes which inherit from this class...
     *
     * @param int $intTariffID the Tariff ID
     *
     * @return str
     */
    public function getCustomerSectorHandleFromTariffID($intTariffID)
    {
        static $dataCache = array();

        $intTariffID = (int)$intTariffID;

        if (!in_array($intTariffID, $dataCache)) {
            $query = <<<EOQ
SELECT
    cs.vchHandle as strHandle
FROM
    dbProductComponents.tblTariff                               AS t
    INNER JOIN dbProductComponents.tblProductComponentConfig    AS pcc
        ON t.intProductComponentConfigID = pcc.intProductComponentConfigID
    INNER JOIN products.tblServiceComponentProduct              AS scp
        ON pcc.intServiceComponentProductID = scp.intServiceComponentProductID
    INNER JOIN products.tblCustomerSector                       AS cs
        ON scp.intCustomerSectorID = cs.intCustomerSectorID
WHERE
    t.intTariffID = $intTariffID
EOQ;

            $conn = get_named_connection_with_db('dbProductComponents_reporting');
            $resResult = PrimitivesQueryOrExit($query, $conn, 'Get the customer sector handle for a tariff');

            $dataCache[$intTariffID] = PrimitivesResultGet($resResult, 'strHandle');
        }

        return $dataCache[$intTariffID];
    }

    /**
     * Gets the contract length handle from the tariff id.  Caches data
     * Should be static, but this can cause problems with the (legacy) classes which inherit from this class...
     *
     * @param int $intTariffID the Tariff ID
     *
     * @return str
     */
    public function getContractLengthHandleFromTariffID($intTariffID)
    {
        static $dataCache = array();

        $intTariffID = (int)$intTariffID;

        if (!in_array($intTariffID, $dataCache)) {
            $query = <<<EOQ
SELECT
    cl.vchHandle
FROM
    tblTariff                       AS t
    INNER JOIN tblContractLength    AS cl ON t.intContractLengthID = cl.intContractLengthID
WHERE t.intTariffID = $intTariffID
EOQ;

            $conn = get_named_connection_with_db('dbProductComponents_reporting');
            $resResult = PrimitivesQueryOrExit($query, $conn, 'Get the contract length handle for a tariff');

            $dataCache[$intTariffID] = PrimitivesResultGet($resResult, 'vchHandle');
        }

        return $dataCache[$intTariffID];
    }

    /**
     * Gets the contract length id from the contract length handle.  Data is cached.
     * Returns 0 if the handle is not found
     *
     * @param string $strContractLengthHandle the contract handle
     *
     * @return int
     */
    public static function getContractLengthIdFromContractLengthHandle($strContractLengthHandle)
    {
        self::includeLegacyFiles();

        // As there's only 18 entries in the table, we cache everything in a single pass
        static $dataCache = array();

        if (empty($dataCache)) {
            $query = 'SELECT vchHandle, intContractLengthID FROM tblContractLength';
            $conn = get_named_connection_with_db('dbProductComponents_reporting');
            $resResult = PrimitivesQueryOrExit($query, $conn, 'Get the contract length id from handle');
            $tmpList = PrimitivesResultsAsArrayGet($resResult, 'vchHandle');

            foreach ($tmpList as $tmpHandle => $tmpCL) {
                $dataCache[$tmpHandle] = $tmpCL['intContractLengthID'];
            }
        }

        return (array_key_exists($strContractLengthHandle, $dataCache) ? $dataCache[$strContractLengthHandle] : 0);
    }

    /**
     * Return cost pre day base on cost of period and payment frequency
     *
     * @access public
     * <AUTHOR>
     *
     * @param  int    $intCostIncVatInPence      CostIncVatInPence
     * @param  string $strPaymentFrequencyHandle PaymentFrequencyHandle
     *
     * @return float CostPerDay
     */
    public function calculateProductCostPerDay($intCostIncVatInPence, $strPaymentFrequencyHandle)
    {
        $arrPaymentFrequencyInDays = array(
            'ANNUAL'    => 365,
            'QUARTERLY' => 90,
            'MONTHLY'   => 30
        );

        $intDaysInPeriod = $arrPaymentFrequencyInDays["$strPaymentFrequencyHandle"];

        if ($intDaysInPeriod > 0) {
            $floCostPerDay = $intCostIncVatInPence / $intDaysInPeriod;
        } else {
            $floCostPerDay = 0;
        }

        return $floCostPerDay;
    }


    /**
     * Return date from which we should start charging Customer after change of payment frequency
     *
     * @access public
     * <AUTHOR>
     *
     * @param  int $intServiceID        Service ID
     * @param  int $intExistingTariffID Existing Tariff ID
     * @param  int $intNewTariffID      New Tariff ID
     * @param  int $uxtCalculationDate  Pro Rata Calculation Date
     *
     * @return int last paid day date
     */
    public function getPaidTillDate($intServiceID, $intExistingTariffID, $intNewTariffID, $uxtCalculationDate = 0)
    {
        if ($uxtCalculationDate == 0) {
            $uxtCalculationDate = time();
        }

        //get customer start date
        $uxtCurrentContractStartDate = CProductComponent::getCustomerExistingContractStartDate($intServiceID);

        //get information about existing tariff
        $arrExistingTariffDetails = CProduct::getTariffDetails($intExistingTariffID);

        //calculate cost per day of existing tariff
        $floExistingCostPerDay = CProductComponent::calculateProductCostPerDay(
            $arrExistingTariffDetails['intCostIncVatInPence'],
            $arrExistingTariffDetails['strPaymentFrequencyHandle']
        );

        //get information of new tariff
        $arrNewTariffDetails = CProduct::getTariffDetails($intNewTariffID);

        //caluculate cost per day of new tariff
        $floNewCostPerDay = CProductComponent::calculateProductCostPerDay(
            $arrNewTariffDetails['intCostIncVatInPence'],
            $arrNewTariffDetails['strPaymentFrequencyHandle']
        );

        //calculate number of days from contract start day till date (only whole days)
        $intNumberOfDaysTillDate = ceil(($uxtCalculationDate - $uxtCurrentContractStartDate) / (24 * 3600));

        //calculate amount paid till date base on existing tariff
        $floAmountPaidTillDate = $intNumberOfDaysTillDate * $floExistingCostPerDay;

        //calculate remaining amount already paid
        $floPaidRemainingAmount = $arrExistingTariffDetails['intCostIncVatInPence'] - $floAmountPaidTillDate;

        //calculate number of already paid days base on new tariff cost
        $floAlreadyPaidDays = floor($floPaidRemainingAmount / $floNewCostPerDay);

        //calculate last paid date
        $uxtProRataCalculationDate = $uxtCalculationDate + ($floAlreadyPaidDays * 24 * 3600);

        return $uxtProRataCalculationDate;
    }

    /**
     * Return details needed for pro rata payments
     * - Can be passed optional product type to handle product specific items
     * - Can be passed optional date var to state when pro rata items work from (i.e. instead of now)
     *
     * @param int $intServiceID                         the Service ID
     * @param int $intTariffID                          the Tariff ID
     * @param str $strServiceComponentProductTypeHandle the Service Component Product Type Handle
     * @param uxt $uxtProRataCalculationDate            the Pro Rata Calculation Date
     *
     * @return arr Pro Rata Payment Details
     */
    public static function generateProRataPaymentDetails(
        $intServiceID,
        $intTariffID,
        $strServiceComponentProductTypeHandle = '',
        $uxtProRataCalculationDate = false
    ) {
        self::includeLegacyFiles();

        $uxtProRataCalculationDate = CProductComponent::generateUxtMiddayTimestamp($uxtProRataCalculationDate);

        $uxtProRataNextInvoice = CProductComponent::generateProRataNextInvoiceDate(
            $intServiceID,
            $intTariffID,
            $uxtProRataCalculationDate
        );

        if (isset($uxtProRataNextInvoice) && $uxtProRataNextInvoice > 0) {
            $intProRataCost = CProductComponent::generateProRataCost(
                $uxtProRataNextInvoice,
                $intTariffID,
                $uxtProRataCalculationDate
            );

            if (isset($intProRataCost) && $intProRataCost >= 0) {
                $floProRataCostDisplay = CProductComponent::calculateDisplayCost($intProRataCost, $intTariffID);
                $strProRataDescription = CProductComponent::generateProRataDescription(
                    $uxtProRataNextInvoice,
                    $intTariffID,
                    $uxtProRataCalculationDate
                );

                // Using legacy functions as to not cause transaction issues
                // Transaction issue not present for partner, so instantiate core service object in the if statement
                $arrService = userdata_service_get($intServiceID);
                if ('partner' == $arrService['isp']) {
                    $objService = new Core_Service($intServiceID);
                    $strProRataDescription = self::modifyInvoiceDescriptionForPartner(
                        $strProRataDescription,
                        $objService
                    );
                }

                $uxtProRataContractEnd = CProductComponent::generateProRataContractEndDate(
                    $intTariffID,
                    $uxtProRataCalculationDate
                );

                $arrProRataItems = array();

                switch ($strServiceComponentProductTypeHandle) {
                    case 'PLUSTALK':
                        // Get the pro rata inclusive minutes
                        $intProductIncludedMinutes = CProductComponent::getIncludedMinutesByTariffID($intTariffID);

                        if (isset($intProductIncludedMinutes) && $intProductIncludedMinutes > 0) {
                            $intCalltimeDefaultTariffID = CProductComponent::getTariffIDDefaultTariffID(
                                $intTariffID,
                                PRODUCT_COMPONENT_PLUSTALK_CALLTIME
                            );

                            if (isset($intCalltimeDefaultTariffID) && $intCalltimeDefaultTariffID > 0) {
                                $uxtCalltimeProRataNID = CProductComponent::generateProRataNextInvoiceDate(
                                    $intServiceID,
                                    $intCalltimeDefaultTariffID,
                                    $uxtProRataCalculationDate
                                );

                                if (isset($uxtCalltimeProRataNID) && $uxtCalltimeProRataNID > 0) {
                                    $arrProRataItems['intInclusiveMinutes'] = CProductComponent::calculateProRataItems(
                                        $uxtCalltimeProRataNID,
                                        $intCalltimeDefaultTariffID,
                                        $intProductIncludedMinutes,
                                        $uxtProRataCalculationDate
                                    );
                                }
                            }
                        }
                        break;
                    default:
                        // Do nothing
                        break;
                }

                return array(
                    'uxtProRataNextInvoice'     => $uxtProRataNextInvoice,
                    'intProRataCost'            => $intProRataCost,
                    'floProRataCostDisplay'     => $floProRataCostDisplay,
                    'strProRataDescription'     => $strProRataDescription,
                    'uxtProRataContractEnd'     => $uxtProRataContractEnd,
                    'uxtProRataCalculationDate' => $uxtProRataCalculationDate,
                    'arrProRataItems'           => $arrProRataItems
                );
            }
        }

        return false;
    }

    /**
     * Get the number of included minutes on a given tariff id.  Data is cached
     * Returns false if an issue occurs
     * Should be static, but this can cause problems with the (legacy) classes which inherit from this class...
     *
     * @param  int $intTariffID the Tariff ID
     *
     * @return int
     */
    public function getIncludedMinutesByTariffID($intTariffID)
    {
        static $dataCache = array();

        $intTariffID = (int)$intTariffID;

        if (!array_key_exists($intTariffID, $dataCache)) {
            $query = <<<EOQ
SELECT
    cc.intMinutes
FROM
    tblTariff                            AS t
    INNER JOIN tblProductComponentConfig AS pcc ON t.intProductComponentConfigID = pcc.intProductComponentConfigID
        AND pcc.dtmStart < NOW()
        AND (pcc.dtmEnd >= NOW() OR pcc.dtmEnd IS NULL)
    INNER JOIN tblProductComponentConfig AS pcc2 ON pcc.intServiceComponentProductID = pcc2.intServiceComponentProductID
        AND pcc2.dtmStart < NOW()
        AND (pcc2.dtmEnd >= NOW() OR pcc2.dtmEnd IS NULL)
    INNER JOIN tblConfigCalltime         AS cc ON pcc2.intProductComponentConfigID = cc.intProductComponentConfigID
WHERE
    t.intTariffID = $intTariffID
EOQ;

            $conn = get_named_connection_with_db('dbProductComponents_reporting');
            $resResult = PrimitivesQueryOrExit($query, $conn, 'Get the contract length handle for a tariff');
            $intMins = PrimitivesResultGet($resResult, 'intMinutes');

            $dataCache[$intTariffID] = (isset($intMins) && $intMins > 0 ? $intMins : false);
        }

        return $dataCache[$intTariffID];
    }

    /**
     * Get pro rata contract end date from the tariff id, as a unix timestamp.  Data is cached
     * Returns false if the end date cannot be calculated
     * Should be static, but this can cause problems with the (legacy) classes which inherit from this class...
     *
     * @param int      $intTariffID               the Tariff ID
     * @param int|bool $uxtProRataCalculationDate the Pro Rata Calculation Date
     *
     * @return int|bool
     */
    public function generateProRataContractEndDate($intTariffID, $uxtProRataCalculationDate = false)
    {
        // DJM2014: reverted back to previous known-good behaviour as there's some concern over the date
        // calculations and we don't have time to review and verify
        if (isset($uxtProRataCalculationDate) && $uxtProRataCalculationDate > 0) {
            $uxtProRataCalculationDate = mktime(
                12,
                0,
                0,
                date('m', $uxtProRataCalculationDate),
                date('d', $uxtProRataCalculationDate),
                date('Y', $uxtProRataCalculationDate)
            );
        } else {
            $uxtProRataCalculationDate = mktime(12, 0, 0, date('m'), date('d'), date('Y'));
        }

        $strContractLengthHandle = CProductComponent::getContractLengthHandleFromTariffID($intTariffID);

        $intContractLengthNumMonths = 0;

        $intContractLengthNumMonths = self::getMonthsFromContractHandle($strContractLengthHandle);
        if ($intContractLengthNumMonths > 0) {
            $dbhConnection = get_named_connection_with_db('dbProductComponents');
            $strQuery = "SELECT UNIX_TIMESTAMP(DATE_ADD(DATE_SUB(FROM_UNIXTIME('$uxtProRataCalculationDate'), "
                . "INTERVAL 1 DAY), INTERVAL $intContractLengthNumMonths MONTH)) as uxtContractEndDate";
            $resResult = PrimitivesQueryOrExit(
                $strQuery,
                $dbhConnection,
                'Get the contract end date from now using the tariff contract length'
            );
            $uxtContractEndDate = PrimitivesResultGet($resResult, 'uxtContractEndDate');

            return $uxtContractEndDate;
        }

        return false;
    }

    /**
     * Calculate the display cost taking into account customer sector.
     * The cost is returned in "pounds" as a float (e.g. 12.99).
     * As all the methods called by this have caching implemented, there's no point in implementing caching here
     * Should be static, but this can cause problems with the (legacy) classes which inherit from this class...
     *
     * @param int $intCost     the Cost (in pence)
     * @param int $intTariffID the Tariff ID
     *
     * @return flo
     */
    public function calculateDisplayCost($intCost, $intTariffID)
    {
        require_once DATABASE_LIBRARY_ROOT . 'CoreObjects/Financial/CFinancialHelper.inc';

        $arrPricing = CFinancialHelper::splitVat($intCost, true);
        $strCustomerSectorHandle = CProductComponent::getCustomerSectorHandleFromTariffID($intTariffID);

        $floDisplayCost = ($strCustomerSectorHandle == 'BUSINESS' ? $arrPricing['exvat'] : $arrPricing['incvat']);
        $floDisplayCost = number_format(($floDisplayCost / 100), 2);

        return $floDisplayCost;
    }

    /**
     * Generate a generic pro rata invoice description
     * Should be static, but this can cause problems with the (legacy) classes which inherit from this class...
     *
     * @param uxt $uxtProRataNextInvoice     the pro-rata next-invoice date
     * @param int $intTariffID               the Tariff ID
     * @param uxt $uxtProRataCalculationDate the pro-rata calculation date
     *
     * @return str Description
     */
    public function generateProRataDescription(
        $uxtProRataNextInvoice,
        $intTariffID,
        $uxtProRataCalculationDate = false
    ) {
        // As this call returns cached data, there's no point in doing any further caching here
        $strDisplayName = CProductComponent::getInvoiceProductComponentDisplayName($intTariffID);

        $uxtProRataCalculationDate = CProductComponent::generateUxtMiddayTimestamp($uxtProRataCalculationDate);

        // Set the date to as being the day before the next invoice date
        $uxtTo = mktime(
            12,
            0,
            0,
            date('m', $uxtProRataNextInvoice),
            date('d', $uxtProRataNextInvoice) - 1,
            date('Y', $uxtProRataNextInvoice)
        );

        $strTo = date('d-m-Y', $uxtTo);
        $strFrom = date('d-m-Y', $uxtProRataCalculationDate);
        $strDescription = "$strDisplayName pro rata charge for the period $strFrom to $strTo";

        return $strDescription;
    }

    /**
     * Generate the description for the scheduled payment
     * Should be static, but this can cause problems with the (legacy) classes which inherit from this class...
     *
     * @param int $intTariffID  the Tariff ID
     * @param uxt $uxtStartDate the Start Date
     * @param uxt $uxtEndDate   the End Date
     *
     * @return str Description
     */
    public function getSchedulePaymentDescription($intTariffID, $uxtStartDate, $uxtEndDate)
    {
        // As this call returns cached data, there's no point in doing any further caching here
        $strDisplayName = CProductComponent::getInvoiceProductComponentDisplayName($intTariffID);

        $strStart = date('d/m/Y', $uxtStartDate);
        $strEnd = date('d/m/Y', $uxtEndDate);
        $strDescription = "$strDisplayName charge for the period $strStart to $strEnd";

        return $strDescription;
    }

    /**
     * Get the display name for a product component based on its tariff id and type
     *
     * @param  int $intTariffID Tariff ID
     *
     * @return str
     */
    public function getInvoiceProductComponentDisplayName($intTariffID)
    {
        // As these calls return cached data, there's no point in doing any further caching here
        $intProductComponentID = CProductComponent::getProductComponentIDFromTariffID($intTariffID);

        if ($intProductComponentID == PRODUCT_COMPONENT_SUBSCRIPTION) {
            return CProductComponent::getProductDisplayNameFromTariffID($intTariffID);
        }

        return CProductComponent::getProductComponentDisplayName($intProductComponentID);
    }

    /**
     * Work out the pro rata amount to charge (inc vat pence) based on the tariff and next invoice date
     * Should be static, but this can cause problems with the (legacy) classes which inherit from this class...
     *
     * @param uxt $uxtProRataNextInvoice     The Pro Rata Next Invoice Date
     * @param int $intTariffID               The Tariff ID
     * @param int $uxtProRataCalculationDate The Inc Vat Pro Rata Charge (pence)
     * @param int $intAmountToProrata        An amount to pro-rata
     *
     * @return float
     */
    public function generateProRataCost(
        $uxtProRataNextInvoice,
        $intTariffID,
        $uxtProRataCalculationDate = false,
        $intAmountToProrata = 0
    ) {
        // As most/all of the methods called below cache their data, further caching isn't required here

        // Most of the "child" product components simply use CProductComponent::getPaymentFrequencyCost()
        $intPCID = CProductComponent::getProductComponentIDFromTariffID($intTariffID);
        switch ($intPCID) {
            case PRODUCT_COMPONENT_SUBSCRIPTION:
                include_once '/local/data/mis/database/database_libraries/components/CProductComponentSubscription.inc';
                $intProRataCost = CProductComponentSubscription::getPaymentFrequencyCost($intTariffID);
                break;
            case PRODUCT_COMPONENT_PLUSTALK_PAYG:
                include_once '/local/data/mis/database/database_libraries/components/CPlusTalkPAYG.inc';
                $intProRataCost = CPlusTalkPAYG::getPaymentFrequencyCost($intTariffID);
                break;
            case PRODUCT_COMPONENT_PLUSTALK_CALLTIME:
                include_once '/local/data/mis/database/database_libraries/components/CPlusTalkCalltime.inc';
                $intProRataCost = CPlusTalkCalltime::getPaymentFrequencyCost($intTariffID);
                break;
            case PRODUCT_COMPONENT_PLUSTALK_VOICEMAIL:
                include_once '/local/data/mis/database/database_libraries/components/CPlusTalkVoicemail.inc';
                $intProRataCost = CPlusTalkVoicemail::getPaymentFrequencyCost($intTariffID);
                break;
            case PRODUCT_COMPONENT_PLUSTALK_DDI:
                include_once '/local/data/mis/database/database_libraries/components/CPlusTalkDDI.inc';
                $intProRataCost = CPlusTalkDDI::getPaymentFrequencyCost($intTariffID);
                break;
            case PRODUCT_COMPONENT_METRONET_MAILBOX:
                include_once '/local/data/mis/database/database_libraries/components/CMetronetMailbox.inc';
                $intProRataCost = CMetronetMailbox::getPaymentFrequencyCost($intTariffID);
                break;
            default:
                //HACK - will work until we have the same call features for HPA and HPA+
                $intServiceComponentId = CProductComponent::getServiceComponentIdFromTariffId($intTariffID);
                $arrCallFeatures = CWlrCallFeature::getAllCallFeaturesTypes($intServiceComponentId);
                $arrCallFeaturesBundle = CWlrCallFeaturesBundle::getAllTypes();
                if (in_array($intPCID, $arrCallFeatures)) {
                    $intProRataCost = CWlrCallFeature::getMonthlyCostByTypeId($intServiceComponentId, $intPCID);
                } elseif (in_array($intPCID, $arrCallFeaturesBundle)) {
                    $intProRataCost = CWlrCallFeaturesBundle::getMonthlyCostByTypeId($intServiceComponentId, $intPCID);
                } else {
                    $intProRataCost = CProductComponent::getPaymentFrequencyCost($intTariffID);
                }
                break;
        }

        if (isset($intProRataCost) && $intProRataCost !== false) {
            // DJM2013: there's no point in processing $uxtProRataCalculationDate, as all we do is pass it through!)
            //$uxtProRataCalculationDate = CProductComponent::generateUxtMiddayTimestamp($uxtProRataCalculationDate);
            return CProductComponent::calculateProRataCost(
                $uxtProRataNextInvoice,
                $intTariffID,
                $intProRataCost,
                $uxtProRataCalculationDate
            );
        }

        return false;
    }

    /**
     * Get the default payment frequency cost for the given tariff. This function can be overridden if required
     *
     * @param int $intTariffID the Tariff ID
     *
     * @return int
     */
    public function getPaymentFrequencyCost($intTariffID)
    {
        $arrTariff = CProductComponent::getTariffDetails($intTariffID);

        if (empty($arrTariff) || !isset($arrTariff['intCostIncVatPence'])) {
            return false;
        }

        return ($arrTariff['intCostIncVatPence'] >= 0 ? $arrTariff['intCostIncVatPence'] : false);
    }

    /**
     * Get the product component id from a tariff id.  Caches data
     * Should be static, but this can cause problems with the (legacy) classes which inherit from this class...
     *
     * @param int $intTariffID the Tariff ID
     *
     * @return int
     */
    public function getProductComponentIDFromTariffID($intTariffID)
    {
        static $dataCache = array();
        $intTariffID = (int)$intTariffID;

        // As there's lots of tariffs, we cache on demand
        if (!array_key_exists($intTariffID, $dataCache)) {
            $query = <<<EOQ
SELECT
    pcc.intProductComponentID
FROM
    tblTariff                               AS t
    INNER JOIN tblProductComponentConfig    AS pcc ON t.intProductComponentConfigID = pcc.intProductComponentConfigID
        AND pcc.dtmStart < NOW()
        AND (pcc.dtmEnd >= NOW() OR pcc.dtmEnd IS NULL)
WHERE
    t.intTariffID = $intTariffID
EOQ;

            $conn = get_named_connection_with_db('dbProductComponents_reporting');
            $resResult = PrimitivesQueryOrExit($query, $conn, 'Get the product component ID from tariff ID');

            $dataCache[$intTariffID] = PrimitivesResultGet($resResult, 'intProductComponentID');
        }

        return $dataCache[$intTariffID];
    }

    /**
     * Simple helper function which returns a timestamp "rounded" to 12:00:00 on the day of the supplied UXT
     * Returns the timestamp for today if the value is supplied
     *
     * @param uxt $uxtTS the original timestamp
     *
     * @return uxt
     */
    public static function generateUxtMiddayTimestamp($uxtTS = false)
    {
        self::includeLegacyFiles();

        $uxtTS = (!empty($uxtTS) ? $uxtTS : time());
        $uxtNewTS = mktime(12, 0, 0, date('m', $uxtTS), date('d', $uxtTS), date('Y', $uxtTS));

        return $uxtNewTS;
    }

    /**
     * Work out the pro-rata cost for the given tariff.  Value returned is in pence.
     * Should be static, but this can cause problems with the (legacy) classes which inherit from this class...
     *
     * @param uxt $uxtProRataNextInvoice     the Pro Rata Next Invoice date
     * @param int $intTariffID               the Tariff ID
     * @param int $intPaymentFrequencyCost   the Payment Frequency Cost (pence)
     * @param uxt $uxtProRataCalculationDate the pro-rata calculation date
     *
     * @return int Pro Rata Cost (pence)
     */
    public function calculateProRataCost(
        $uxtProRataNextInvoice,
        $intTariffID,
        $intPaymentFrequencyCost,
        $uxtProRataCalculationDate = false
    ) {
        $strPaymentFrequencyHandle = CProductComponent::getPaymentFrequencyHandleFromTariffID($intTariffID);
        $intMonths = self::getMonthsFromPaymentFrequencyHandle($strPaymentFrequencyHandle);
        if ($intMonths == 0) {
            return false;
        }

        $uxtProRataNextInvoice = CProductComponent::generateUxtMiddayTimestamp($uxtProRataNextInvoice);
        $uxtProRataCalculationDate = CProductComponent::generateUxtMiddayTimestamp($uxtProRataCalculationDate);
        $intNumDaysDifference = floor(($uxtProRataNextInvoice - $uxtProRataCalculationDate) / 86400);

        $uxtTodayNextPayMonth = CProduct::getNextMonthsDate($intMonths, $uxtProRataCalculationDate);
        $intNumDaysTillTodayNextPayMonth = floor(($uxtTodayNextPayMonth - $uxtProRataCalculationDate) / 86400);

        $floProRataCost = (($intPaymentFrequencyCost / $intNumDaysTillTodayNextPayMonth) * $intNumDaysDifference);

        return round($floProRataCost, 0);
    }

    /**
     * Work out the pro rata amount for any integer item (Wrapper function for calculateProRataCost)
     * Should be static, but this can cause problems with the (legacy) classes which inherit from this class...
     *
     * @param uxt $uxtProRataNextInvoice     the Pro Rata Next Invoice date
     * @param int $intTariffID               the Tariff ID
     * @param int $intItem                   the item
     * @param uxt $uxtProRataCalculationDate the Pro Rata Next Invoice date
     *
     * @return int
     */
    public function calculateProRataItems(
        $uxtProRataNextInvoice,
        $intTariffID,
        $intItem,
        $uxtProRataCalculationDate = false
    ) {
        return CProductComponent::calculateProRataCost(
            $uxtProRataNextInvoice,
            $intTariffID,
            $intItem,
            $uxtProRataCalculationDate
        );
    }

    /**
     * Check if the (WLR) tariff that the Customer is currently on is free
     *
     * @param int    $intServiceID                         the Service ID
     * @param string $strServiceComponentProductTypeHandle the Service Component Product Type Handle
     *
     * @return bool
     */
    public function isFreeTariffByServiceID($intServiceID, $strServiceComponentProductTypeHandle)
    {
        $intExistingTariffID = CProductComponent::getExistingTariffID(
            $intServiceID,
            $strServiceComponentProductTypeHandle
        );

        if (empty($intExistingTariffID) && ($strServiceComponentProductTypeHandle == 'PLUSTALK')) {
            // this means that the customer is on PAYG, which is free
            return true;
        }

        return CProductComponent::isFreeTariff($intExistingTariffID);
    }

    /**
     * Check if the given tariff is free
     * Should be static, but this can cause problems with the (legacy) classes which inherit from this class...
     *
     * @param int $intTariffID the Tarrif ID
     *
     * @return bool true if it is FREE, false otherwise
     */
    public function isFreeTariff($intTariffID)
    {
        $arrTariffDetails = CProductComponent::getTariffDetails($intTariffID);

        if ($arrTariffDetails['strContractLengthHandle'] == 'NONE'
            && $arrTariffDetails['strPaymentFrequencyHandle'] == 'NEVER'
        ) {
            return true;
        }

        return false;
    }

    /**
     * Get the record for a tariff id.  Data is cached
     *
     * @param int $intTariffID the Tariff ID
     *
     * @return array
     */
    public static function getTariffDetails($intTariffID)
    {
        self::includeLegacyFiles();
        require_once '/local/data/mis/database/database_libraries/components/CProductComponentUtil.php';
        $util = new CProductComponentUtil();

        return $util->getTariffDetails($intTariffID);
    }

    /**
     * Get a record from dbProductComponents.tblProductComponentConfigId.  Data is cached
     * Should be static, but this can cause problems with the (legacy) classes which inherit from this class...
     *
     * @param  int $intProductComponentConfigId the product-component-config id
     *
     * @return array
     */
    public function getProductComponentConfigDetails($intProductComponentConfigId)
    {
        static $dataCache = array();

        $intProductComponentConfigId = (int)$intProductComponentConfigId;

        if (!array_key_exists($intProductComponentConfigId, $dataCache)) {
            $strQuery = <<<EOQ
SELECT
    pcc.intProductComponentConfigID,
    pcc.intServiceComponentProductID,
    pcc.intProductComponentID,
    pcc.intDefaultQuantity,
    pcc.intMinQuantity,
    pcc.intMaxQuantity,
    UNIX_TIMESTAMP(pcc.dtmStart)    AS uxtStartDate,
    UNIX_TIMESTAMP(pcc.dtmEnd)      AS uxtEndDate
FROM
    tblProductComponentConfig       AS pcc
WHERE
    intProductComponentConfigID = $intProductComponentConfigId
EOQ;

            $conn = get_named_connection_with_db('dbProductComponents_reporting');
            $resResult = PrimitivesQueryOrExit($strQuery, $conn, 'Get the product component config data');

            $dataCache[$intProductComponentConfigId] = PrimitivesResultGet($resResult);
        }

        return $dataCache[$intProductComponentConfigId];
    }

    /**
     * getProductComponentInstanceIdsFromServiceId - gets all ProductComponentInstanceIds of a service id with the
     * given status
     *
     * @access public
     * <AUTHOR>
     *
     * @param int   $intServiceId Service id
     * @param array $arrStates    States
     *
     * @return arrProductComponentInstanceID
     *
     */
    public static function getProductComponentInstanceIdsFromServiceId($intServiceId, $arrStates = array())
    {
        self::includeLegacyFiles();
        if (empty($arrStates)) {
            $arrStates = array(
                'ACTIVE',
                'QUEUED_ACTIVATE',
                'QUEUED_REACTIVATE',
                'QUEUED_DEACTIVATE',
                'QUEUED_DECONFIGURE',
                'DEACTIVE',
                'QUEUED_DESTROY',
                'UNCONFIGURED'
            );
        } // if (empty($arrStates))

        $dbhConnection = get_named_connection_with_db('userdata_reporting');
        $intServiceId = addslashes($intServiceId);


        $strQuery = "SELECT intProductComponentInstanceID
                       FROM tblProductComponentInstance pci
                 INNER JOIN components c
                         ON c.component_id = pci.intComponentID
                 INNER JOIN dbProductComponents.tblProductComponent pc
                         ON pc.intProductComponentID = pci.intProductComponentID
                 INNER JOIN dbProductComponents.tblStatus s
                         ON pci.intStatusID = s.intStatusID
                 INNER JOIN products.service_components sc
                         ON c.component_type_id = sc.service_component_id
                 INNER JOIN products.tblServiceComponentProduct scp
                         ON scp.intServiceComponentID = sc.service_component_id
                 INNER JOIN products.tblServiceComponentProductType scpt
                         ON scp.intServiceComponentProductTypeID = scpt.intServiceComponentProductTypeID
            ";

        $strQuery .= "
            WHERE c.service_id = '$intServiceId'
            AND (pci.dtmEnd >= NOW() OR pci.dtmEnd IS null) ";

        //problem 29684 fix
        if ($strProductComponentHandle != 'DDI') {
            $strQuery .= " AND s.vchHandle IN ('" . implode("', '", $arrStates) . "') ";
        }

        $resResult = PrimitivesQueryOrExit(
            $strQuery,
            $dbhConnection,
            'Get the product component instance IDs',
            false
        );

        return PrimitivesResultsAsListGet($resResult);
    }

    /**
     * Retrieves the productComponentInstanceID based on service_id and the product Component handle
     *
     * @access public
     * <AUTHOR>
     *
     * @param int    $intServiceID                 Service       ID
     * @param string $strProductComponentHandle    Product Component Handle
     * @param array  $arrStates                    States
     *
     * @param string $strServiceCompProdTypeHandle Service component product type handle
     *
     * @return int intProductComponentInstanceID
     */
    public static function getProductComponentInstance(
        $intServiceID,
        $strProductComponentHandle,
        $arrStates = array(),
        $strServiceCompProdTypeHandle = ''
    ) {
        self::includeLegacyFiles();

        if (empty($arrStates)) {
            $arrStates = array(
                'ACTIVE',
                'QUEUED_ACTIVATE',
                'QUEUED_REACTIVATE',
                'QUEUED_DEACTIVATE',
                'QUEUED_DECONFIGURE',
                'DEACTIVE',
                'QUEUED_DESTROY',
                'UNCONFIGURED'
            );
        } // if (empty($arrStates))

        $dbhConnection = get_named_connection_with_db('userdata');

        $strQuery = "SELECT MAX(intProductComponentInstanceID) AS intProductComponentInstanceID
                       FROM tblProductComponentInstance pci
                 INNER JOIN components c
                         ON c.component_id = pci.intComponentID
                 INNER JOIN dbProductComponents.tblProductComponent pc
                         ON pc.intProductComponentID = pci.intProductComponentID
                 INNER JOIN dbProductComponents.tblStatus s
                         ON pci.intStatusID = s.intStatusID
                 INNER JOIN products.service_components sc
                         ON c.component_type_id = sc.service_component_id
                 INNER JOIN products.tblServiceComponentProduct scp
                         ON scp.intServiceComponentID = sc.service_component_id
                 INNER JOIN products.tblServiceComponentProductType scpt
                         ON scp.intServiceComponentProductTypeID = scpt.intServiceComponentProductTypeID
                         ";
        if ($strServiceCompProdTypeHandle != '') {
            $strQuery .= " AND scpt.vchHandle = '$strServiceCompProdTypeHandle' ";
        }
        $strQuery .= "
                      WHERE c.service_id = '$intServiceID'
                        AND pc.vchHandle = '$strProductComponentHandle'
                        AND (pci.dtmEnd >= NOW() OR pci.dtmEnd IS null) ";

        //problem 29684 fix
        if ($strProductComponentHandle != 'DDI') {
            $strQuery .= " AND s.vchHandle IN ('" . implode("', '", $arrStates) . "') ";
        }

        $resResult = PrimitivesQueryOrExit(
            $strQuery,
            $dbhConnection,
            'Get the product component instance ID',
            false
        );

        $intProductComponentInstanceID = PrimitivesResultGet($resResult, 'intProductComponentInstanceID');

        if (isset($intProductComponentInstanceID) && $intProductComponentInstanceID > 0) {
            return $intProductComponentInstanceID;
        }

        return false;
    }

    /**
     * Retrieves the active productComponentInstanceID based on service_id and the product Component handle
     *
     * @access public
     * <AUTHOR>
     *
     * @param int    $intServiceID                 Service ID
     * @param string $strHandle                    Product Component Handle
     * @param string $strServiceCompProdTypeHandle Servie component product type handle
     *
     * @return int intProductComponentInstanceID
     */
    public function getActiveProductComponentInstance($intServiceID, $strHandle, $strServiceCompProdTypeHandle)
    {
        $dbhConnection = get_named_connection_with_db('userdata');

        $strQuery = 'SELECT pci.intProductComponentInstanceID ' .
            '  FROM tblProductComponentInstance pci ' .
            ' INNER JOIN dbProductComponents.tblProductComponent pc ' .
            '    ON pci.intProductComponentID = pc.intProductComponentID ' .
            "   AND pc.vchHandle = '$strHandle' " .
            ' INNER JOIN dbProductComponents.tblStatus s ' .
            '    ON pci.intStatusID = s.intStatusID ' .
            '   AND s.vchHandle = "ACTIVE" ' .
            ' INNER JOIN components c ON pci.intComponentID = c.component_id ' .
            "   AND c.service_id = '$intServiceID' " .
            '   AND c.status = "active" ' .
            ' INNER JOIN products.service_components sc ' .
            '    ON c.component_type_id = sc.service_component_id ' .
            ' INNER JOIN products.tblServiceComponentProduct scp ' .
            '    ON scp.intServiceComponentID = sc.service_component_id ' .
            ' INNER JOIN products.tblServiceComponentProductType scpt ' .
            '    ON scp.intServiceComponentProductTypeID = scpt.intServiceComponentProductTypeID ' .
            "   AND scpt.vchHandle = '$strServiceCompProdTypeHandle' " .
            ' WHERE pci.dtmStart < NOW() ' .
            '   AND (pci.dtmEnd >= NOW() OR pci.dtmEnd IS NULL)';


        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection, 'Get the product component instance ID');

        $intProductComponentInstanceID = PrimitivesResultGet($resResult, 'intProductComponentInstanceID');

        if (isset($intProductComponentInstanceID) && $intProductComponentInstanceID > 0) {
            return $intProductComponentInstanceID;
        }

        return false;
    }

    /**
     * Get the contract end date for a particular user
     *
     * @access public
     * <AUTHOR>
     *
     * @param int    $intServiceID                 Service ID
     * @param string $strServiceCompProdTypeHandle Service component product type handle
     * @param string $strPCIHandle                 Optional pci handle
     *
     * @return int Contract End Date | bool
     */
    public function getCustomerExistingContractExpiredDate(
        $intServiceID,
        $strServiceCompProdTypeHandle = 'PLUSTALK',
        $strPCIHandle = 'WlrLineRent'
    ) {
        //Get the current subscription PCI
        // bselby - Use the WlrLineRent component so it works for both annual and monthly contracts
        // This was discussed with Farhan as a way of getting the correct date for both annual and monthly customers
        //
        // mstarbuck - make the above the default, but optionally allow us to specify SUBSCRIPTION, as this was
        // breaking home phone migrations
        $intSubscriptionProductComponentInstanceID = CProductComponent::getActiveProductComponentInstance(
            $intServiceID,
            $strPCIHandle,
            $strServiceCompProdTypeHandle
        );

        if (!isset($intSubscriptionProductComponentInstanceID) || $intSubscriptionProductComponentInstanceID < 1) {
            return false;
        }

        $objSubscriptionProductComponentInstance = CProductComponent::createInstance(
            $intSubscriptionProductComponentInstanceID
        );

        if (!isset($objSubscriptionProductComponentInstance) || !is_object($objSubscriptionProductComponentInstance)) {
            return false;
        }

        //Get its contract end date
        $uxtContractEnd = $objSubscriptionProductComponentInstance->getContractEnd();

        unset($objSubscriptionProductComponentInstance);

        return $uxtContractEnd;
    }

    /**
     * Get the contract start date for a particular user
     *
     * @access public
     * <AUTHOR>
     *
     * @param int    $intServiceID                 Service ID
     * @param string $strServiceCompProdTypeHandle Service component product type handle
     *
     * @return uxt Contract End Date
     */
    public static function getCustomerExistingContractStartDate(
        $intServiceID,
        $strServiceCompProdTypeHandle = 'PLUSTALK'
    ) {
        self::includeLegacyFiles();

        $intSubscriptionProductComponentInstanceID = CProductComponent::getActiveProductComponentInstance(
            $intServiceID,
            'SUBSCRIPTION',
            $strServiceCompProdTypeHandle
        );

        if (isset($intSubscriptionProductComponentInstanceID) && $intSubscriptionProductComponentInstanceID > 0) {
            $objSubscriptionProductComponentInstance = CProductComponent::createInstance(
                $intSubscriptionProductComponentInstanceID
            );

            if (isset($objSubscriptionProductComponentInstance)
                && is_object($objSubscriptionProductComponentInstance)
            ) {
                $uxtContractStart = $objSubscriptionProductComponentInstance->getContractStart();

                unset($objSubscriptionProductComponentInstance);

                return $uxtContractStart;
            }
        }

        return false;
    }

    /**
     * @access public
     * @static
     *
     * @param integer $intServiceID                         service ID of account to work with
     * @param string  $strServiceComponentProductTypeHandle PLUSTALK, WLR, INTERNET_CONNECTION, MAAF_WEBMAIL etc.
     * @param string  $strProductComponentHandle            which specific product-component
     *                                                      aspect, e.g. SUBSCRIPTION, WlrLineRent, SECURITY_BULLGUARD,
     *                                                      etc.
     *
     * @return integer tariff ID
     **/
    public static function getExistingTariffIdForProductComponent(
        $intServiceID,
        $strServiceComponentProductTypeHandle,
        $strProductComponentHandle
    ) {
        self::includeLegacyFiles();

        $dbhConnection = get_named_connection_with_db('userdata');

        $intServiceID = PrimitivesRealEscapeString($intServiceID, $dbhConnection);
        $strServiceComponentProductTypeHandle = PrimitivesRealEscapeString(
            $strServiceComponentProductTypeHandle,
            $dbhConnection
        );
        $strProductComponentHandle = PrimitivesRealEscapeString($strProductComponentHandle, $dbhConnection);

        $strQuery = 'SELECT pci.intTariffID ' .
            '  FROM components c ' .
            ' INNER JOIN products.tblServiceComponentProduct scp ' .
            '    ON c.component_type_id = scp.intServiceComponentID ' .
            ' INNER JOIN products.tblServiceComponentProductType scpt ' .
            '    ON scp.intServiceComponentProductTypeID = scpt.intServiceComponentProductTypeID ' .
            "   AND scpt.vchHandle = '$strServiceComponentProductTypeHandle' " .
            ' INNER JOIN tblProductComponentInstance pci ' .
            '    ON c.component_id = pci.intComponentID ' .
            '   AND pci.dtmStart < NOW() ' .
            '   AND (pci.dtmEnd >= NOW() OR pci.dtmEnd IS NULL) ' .
            ' INNER JOIN dbProductComponents.tblStatus s ' .
            '    ON pci.intStatusID = s.intStatusID ' .
            '   AND s.vchHandle = "ACTIVE" ' .
            ' INNER JOIN dbProductComponents.tblProductComponent pc ' .
            '    ON pci.intProductComponentID = pc.intProductComponentID ' .
            "   AND pc.vchHandle = '{$strProductComponentHandle}' " .
            " WHERE c.service_id = '$intServiceID'" .
            ' ORDER BY dtmStart DESC ' .
            ' LIMIT 1 ';
        $resResult = PrimitivesQueryOrExit(
            $strQuery,
            $dbhConnection,
            'Get the tariff ID for a particular service id and service component product type'
        );

        $intTariffID = PrimitivesResultGet($resResult, 'intTariffID');

        return $intTariffID;
    }

    /**
     * Get the current tariff id for a service ids subscription
     *
     * @param  int    $intServiceID                         Service ID
     * @param  string $strServiceComponentProductTypeHandle Service Component Product Type Handle
     *
     * @return int Tariff ID
     */
    public function getExistingTariffID($intServiceID, $strServiceComponentProductTypeHandle)
    {
        return self::getExistingTariffIdForProductComponent(
            $intServiceID,
            $strServiceComponentProductTypeHandle,
            'SUBSCRIPTION'
        );
    }

    /**
     * Get a product component id on a service component for a particular handle
     *
     * @access public
     * @static
     * <AUTHOR>
     *
     * @param int    $intComponentID            Component ID
     * @param string $strProductComponentHandle Product Component Handle
     * @param array  $arrStates                 Statuses
     *
     * @return int Product Component ID
     */
    public static function getProductComponentInstanceIDOfComponentAndHandle(
        $intComponentID,
        $strProductComponentHandle,
        $arrStates = array('ACTIVE')
    ) {
        self::includeLegacyFiles();

        $dbhConnection = get_named_connection_with_db('userdata');

        $strHandle = addslashes($strProductComponentHandle);

        $strStates = '"' . implode('","', $arrStates) . '"';


        $strQuery = 'SELECT pci.intProductComponentInstanceID ' .
            '  FROM tblProductComponentInstance pci ' .
            ' INNER JOIN dbProductComponents.tblProductComponent pc ' .
            '    ON pci.intProductComponentID = pc.intProductComponentID ' .
            "   AND pc.vchHandle = '$strProductComponentHandle' " .
            ' INNER JOIN dbProductComponents.tblStatus s ' .
            '    ON pci.intStatusID = s.intStatusID ' .
            "   AND s.vchHandle IN ($strStates) " .
            " WHERE pci.intComponentID = '$intComponentID' " .
            '   AND pci.dtmStart <= NOW() ' .
            '   AND (pci.dtmEnd >= NOW() OR pci.dtmEnd IS NULL)';

        $resResult = PrimitivesQueryOrExit(
            $strQuery,
            $dbhConnection,
            'Get the product component instance ID of a particular type on a component'
        );

        $intProductComponentInstanceID = PrimitivesResultGet($resResult, 'intProductComponentInstanceID');

        return $intProductComponentInstanceID;
    }

    /**
     * Returns the current cost of the product component instance
     *
     * @access public
     * @return int Pence cost inc vat
     */
    public function getCurrentCost()
    {
        // You MUST override this function
        return false;
    }

    /**
     * Returns the details for scheduled payment
     *
     * @access public
     * @return arr Scheduled payment details
     */
    public function getScheduledPaymentDetails()
    {
        // You MUST override this function
        return false;
    }

    /**
     * Configurator Methods  - Override for your specific component
     */

    /**
     * Refreshes the current state of the component
     *
     * If the component is in a queued state
     *
     * @access public
     * @return boolean False if the component is destroyed, otherwise true
     */
    public function refreshCurrentState()
    {
        //You MUST override this function
        return 0;
    }

    /**
     * onent
     *
     * If the component is in a queued state
     *
     * @access public
     * @return boolean False if the component is destroyed, otherwise true
     */
    public function reEnable()
    {
        //You must override this function
        return true;
    }

    /**
     * Component configurator function. Enables the component.
     *
     * YOU SHOULD OVERRIDE THIS FUNCTION IF YOUR ENABLE REQUIREMENT DOES NOT FOLLOW THE SAME ORDER OF ACTIONS
     *
     * @access public
     * <AUTHOR>
     * @return integer -1 on error, 0 on success
     */
    public function enable()
    {
        $strStatusHandle = $this->getStatusHandleFromID($this->getStatusID());
        switch ($strStatusHandle) {
            case 'QUEUED_REACTIVATE':
            case 'QUEUED_DEACTIVATE':
            case 'DEACTIVE':
                return $this->reEnable();
                break;
            case 'ACTIVE':
                //Nothing to do here
                return true;
                break;
            case 'DESTROYED':
            case 'QUEUED_DESTROY':
                //nothing to do here
                $this->setError(
                    __FILE__,
                    __LINE__,
                    'Error enabling product component instance [' . $this->m_intProductComponentInstanceID .
                    '] because it is at status [' . $strStatusHandle . ']'
                );

                return false;
                break;
            case 'UNCONFIGURED':
            case 'QUEUED_ACTIVATE':
                //Continue to enable
                break;
        }

        // Set state to queued-activate
        $this->prvSetStatus('QUEUED_ACTIVATE');

        // Add config entry
        $intConfigID = $this->prvWriteUserdataConfigRecord();

        if (!isset($intConfigID) || $intConfigID < 1) {
            $this->setError(
                __FILE__,
                __LINE__,
                'Error enabling product component instance [' . $this->m_intProductComponentInstanceID .
                '] because the config record could not be created.'
            );

            return false;
        }

        if ($this->isContractCreatable()) {
            $bolSuccess = $this->createContract();

            if (!$bolSuccess) {
                return false;
            }
        }

        // Set status to active
        $this->prvSetStatus('ACTIVE');

        $this->refreshInstance($this->m_intProductComponentInstanceID);

        return true;
    }

    /**
     * A function to decide whether or not we should be creating a contract
     * or not for this product component
     *
     * This checks whether or not you have an actrive contract
     * and if the contract handle is NONE or not
     *
     * @return boolean
     */
    private function isContractCreatable()
    {
        $strContractLengthHandle = self::getContractLengthHandleFromTariffID($this->m_intTariffID);

        // If you don't have an active contract and the contract handle
        // for the tariff is not NONE then we are good to go for creating a new contract, otherwise not so much
        if ('NONE' != $strContractLengthHandle && !$this->hasActiveContract()) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Check if this product component has a current and active contract
     *
     * @param bool     $bolReEnable  Shall re-enable
     * @param int|bool $uxtDateOfRun Date of run
     *
     * @access private
     * <AUTHOR>
     * @return boolean result
     */
    public function hasActiveContract($bolReEnable = false, $uxtDateOfRun = false)
    {
        $dbhConn = get_named_connection_with_db('userdata');

        $time = 'NOW()';
        if (!empty($uxtDateOfRun)) {
            $time = date("'Y-m-d'", $uxtDateOfRun);
        }

        $strQuery = "SELECT count(*) as intActiveContracts
                       FROM userdata.tblProductComponentContract
                      WHERE dtmCancelled IS null
                        AND dteContractEnd > $time
                        AND dtmRenewed IS null
                        AND intNextProductComponentContractID IS null
                        AND intProductComponentInstanceID = '{$this->m_intProductComponentInstanceID}'";
        if (!$bolReEnable) {
            $strQuery .= " AND dteContractStart <= $time";
        }

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Check if a product component has an active contract');

        $intActiveContracts = PrimitivesResultGet($resResult, 'intActiveContracts');

        return (boolean)$intActiveContracts;
    }

    /**
     * Create a contract entry, starting now.
     *
     * @param array $arrArgs Arguments
     *
     * <AUTHOR>
     * @return boolean success
     */
    public function createContract($arrArgs = array())
    {
        // Initially set this to true, because if there's no tariff ID then we will just pretend that everything's happy
        $result = true;

        if ($this->m_intTariffID) {
            // We're going to do something so now make this false
            $result = false;


            // Stephen's random notes on Billing Get Well 2010 stuff!
            //
            // FIXME: uxtEnableDate is a WLR'ism and doesn't belong in CProductComponent::createContract()
            //
            // Rationale: a contract has no concept of an "enable" date. A contract has a "start" and "end" date.
            //
            // Because of this, the presence of a contract start date (uxtContractStart) should override
            // any "enable" date that is also supplied.
            //
            // An "enable" date isn't always the same as a contract "start" date - contract start dates are for
            // billing purposes, while an "enable" date is to flag when a service was activated.

            $uxtContractStart = time();   // by default, let's assume "now"

            if (isset($arrArgs['uxtEnableDate'])) {
                // This piece of code remains here to maintain backward compatibility with the WLR system
                // until it has had all its incorrect references of "uxtEnableDate" changed to "uxtContractStart".
                // Also, sadly because the CInternetConnectionProduct code started off life as a copy-paste
                // of WLR code, it too also carries around this particular bit of bad baggage, thus has
                // the same problem.

                $uxtContractStart = $arrArgs['uxtEnableDate'];
            }

            if (isset($arrArgs['uxtContractStart'])) {
                // Regardless of whether uxtEnableDate exists, if a uxtContractStart field exists
                // then use that instead, overriding both the other two alternatives.

                $uxtContractStart = $arrArgs['uxtContractStart'];
            }

            // IMO, uxtEnableDate and uxtContractStart should be mutually exclusive in terms
            // of which exists in the $arrArgs array.
            // Client code that calls ->createContract() shouldn't just supply its own $arrArgs data
            // willy-nilly to createContract(). It should never include "uxtEnableDate" in the first
            // place because its meaningless in a contract context.
            //
            // End of Stephen's random notes on Billing Get Well 2010 stuff!

            $bolRetentionOffer = (isset($arrArgs['bolRetentionOffer']) && $arrArgs['bolRetentionOffer'] == true);

            $uxtContractEnd = (isset($arrArgs['uxtContractEnd']) && $arrArgs['uxtContractEnd'] > 0)
                ? $arrArgs['uxtContractEnd']
                : CProductComponent::calculateContractEnd(
                    $uxtContractStart,
                    $this->m_intTariffID,
                    $this->m_intServiceID,
                    $bolRetentionOffer
                );

            // Check if start date is before the requested end date - problem 46849
            // We want to compare the start and end date whilst ignoring the time component.
            if (self::generateUxtMiddayTimestamp($uxtContractStart)
                > self::generateUxtMiddayTimestamp($uxtContractEnd)
            ) {
                require_once '/local/data/mis/database/database_libraries/programme-tool-access.inc';
                $strProblemDescription = "method: CProductComponent::createContract\n\n"
                    . "End date before the start date for PCI '{$this->m_intProductComponentInstanceID}',"
                    . " start '{$uxtContractStart}' end '{$uxtContractEnd}', "
                    . " service id '{$this->m_intServiceID}', "
                    . " tariff id '{$this->m_intTariffID}'"
                    . " arguments " . var_export($arrArgs, true) . "'"
                    . " this " . dss($this);
                pt_raise_autoproblem(
                    'undefined',
                    'Autoproblem: Contract error',
                    $strProblemDescription,
                    $strProblemDescription,
                    false
                );

                $this->setError(__FILE__, __LINE__, $strProblemDescription);

                return false;
            }

            $this->requireLegacyFiles();
            $strProductName = CProductComponent::getProductDisplayNameFromTariffID($this->m_intTariffID);

            /**
             * If we are adding a mailbox to metronet parent account, we take the payment for
             * the first year via a manual invoice from w/p and automatically from portal.
             * So, we need to skip scheduled payment creation for the first year.
             * Scheduld payments will be generated on contract renewals.
             */
            if (CBillingMetronetSpecific::isMetronetMasterAccount($this->m_intServiceID)
                && strpos($strProductName, 'Mailbox') != false
            ) {
                $arrArgs['bolCreateScheduledPayment'] = false;
            }

            $result = $this->doCreateContract($this->m_intTariffID, $uxtContractStart, $uxtContractEnd, $arrArgs);
            if (false !== $result) {
                // $result is an integer, so we store it and turn it into a boolean to match the functionality of this
                // method
                $this->m_intContractID = $result;
                $result = true;
            }
        }

        return $result;
    }

    /**
     * Retreives all the contracts for the given period. If $bolStrict is set to false, then any contracts which
     * overlap the period will be returned. If set to true then only contracts which fall wholey within the period
     * will be returned
     *
     * @param integer $uxtPeriodStart The start date of the period
     * @param integer $uxtPeriodEnd   The end date of the period
     * @param boolean $bolStrict      Retrieve contracts wholly within the period
     *                                Date()*
     *
     * @return array
     */
    public function getContractsForPeriod($uxtPeriodStart, $uxtPeriodEnd, $bolStrict = false)
    {
        $arrContracts = array();

        // Since not bolStrict is more likely we put it first. Also, brain-hurt logic.
        $strQueryPeriod = (!$bolStrict)
            ? "AND (pcc.dteContractEnd >= FROM_UNIXTIME({$uxtPeriodStart}) " .
            "AND pcc.dteContractStart <= FROM_UNIXTIME({$uxtPeriodEnd}))"
            : "AND (pcc.dteContractStart >= FROM_UNIXTIME({$uxtPeriodStart}) " .
            "AND pcc.dteContractEnd <= FROM_UNIXTIME({$uxtPeriodEnd}))";

        $strQuery = "SELECT pcc.intProductComponentContractId, pcc.intTariffID,
                            UNIX_TIMESTAMP(pcc.dteContractStart) AS uxtContractStart,
                            UNIX_TIMESTAMP(pcc.dteContractEnd) AS uxtContractEnd
                       FROM userdata.tblProductComponentContract pcc
                      WHERE pcc.intProductComponentInstanceId = {$this->m_intProductComponentInstanceID}
                        AND pcc.dtmCancelled IS NULL
                            {$strQueryPeriod}";

        $dbhConnection = get_named_connection_with_db('userdata');

        $resResult = PrimitivesQueryOrExit(
            $strQuery,
            $dbhConnection,
            'CProductComponent::getContractsForPeriod',
            false
        );
        if (false !== $resResult) {
            $arrContracts = PrimitivesResultsAsArrayGet($resResult);
        }

        return $arrContracts;
    }

    /**
     * Enable the component following
     *
     * @param int $intExistingProductComponentInstanceID Existing product component instance id
     *
     * @return bool
     */
    public function enableFollowingProductChange($intExistingProductComponentInstanceID)
    {
        $objExistingProductComponent = CProductComponent::createInstance($intExistingProductComponentInstanceID);

        if ($this->prvCopyExistingProductComponentConfig($objExistingProductComponent)) {
            //change status to 'active rather than using enable function
            $this->prvSetStatus('ACTIVE');

            $this->refreshInstance($this->m_intProductComponentInstanceID);

            return true;
        }

        return false;
    }

    /**
     * Component configuration function. Configure the component
     *
     * YOU SHOULD OVERRIDE THIS FUNCTION IF YOUR CONFIGURE REQUIREMENT DOES NOT FOLLOW THE SAME ORDER OF ACTIONS
     *
     * @access   private
     * <AUTHOR> Jones" <<EMAIL>>
     * @return   boolean true if the component was successfully configured
     */
    public function configure()
    {
        // Set state to queued-activate
        $this->prvSetStatus('QUEUED_ACTIVATE');

        // Since we're moving out of a configured state, create the contract!
        // Don't worry. It won't do this again later if it already has one!
        // No need to create the contract if there isn't one.
        if ($this->isContractCreatable()) {
            $bolSuccess = $this->createContract();

            if (!$bolSuccess) {
                return false;
            }
        }

        $this->refreshInstance($this->m_intProductComponentInstanceID);

        return true;
    }

    /**
     * Component configurator function. Disables the component.
     *
     * YOU SHOULD OVERRIDE THIS FUNCTION IF YOUR DISABLE REQUIREMENT DOES NOT FOLLOW THE SAME ORDER OF ACTIONS
     *
     * @access private
     * <AUTHOR>
     * @return integer -1 on error, 0 on success
     */
    public function disable()
    {
        // Set state to queued-activate
        $this->prvSetStatus('QUEUED_DEACTIVATE');

        //Nothing to do...

        // Set status to active
        $this->prvSetStatus('DEACTIVE');

        $this->refreshInstance($this->m_intProductComponentInstanceID);

        return true;
    }

    /**
     * Component configurator function. Destroys the component.
     *
     * YOU SHOULD OVERRIDE THIS FUNCTION IF YOUR DESTROY REQUIREMENT DOES NOT FOLLOW THE SAME ORDER OF ACTIONS
     *
     * @access public
     * <AUTHOR>
     *
     * @param  array $arrArgs Array of optional arguments
     *
     * @return integer -1 on error, 0 on success
     */
    public function destroy($arrArgs = array())
    {
        $uxtCancelled = isset($arrArgs['uxtCancelled']) ? $arrArgs['uxtCancelled'] : '';
        // Set state to queued-activate
        $this->prvSetStatus('QUEUED_DESTROY');

        // Cancel any remaining contracts on this PCI
        $this->cancelAllExistingContracts($arrArgs);

        // Add config entry
        $bolRemoveUserdataConfigRecord = $this->prvRemoveUserdataConfigRecord();

        if (isset($bolRemoveUserdataConfigRecord) && $bolRemoveUserdataConfigRecord == true) {
            // Set status to active
            $this->prvSetStatus('DESTROYED', $uxtCancelled);

            $this->refreshInstance($this->m_intProductComponentInstanceID);

            return true;
        }

        return false;
    }

    /**
     * Sets the product component instance status
     *
     * <AUTHOR>
     * @private
     *
     * @param string     $strStatusHandle  Status Handle
     * @param int|string $uxtEffectiveDate Effective date
     *
     * @return bool Success or failure
     */
    public function prvSetStatus($strStatusHandle, $uxtEffectiveDate = '')
    {
        $strStatusHandle = strtoupper($strStatusHandle);

        $intStatusID = CProductComponent::getStatusIDFromHandle($strStatusHandle);

        if (isset($intStatusID) && $intStatusID > 0) {
            $dbhConnection = get_named_connection_with_db('userdata');

            // If destroyed then set the end date
            if ($strStatusHandle == 'DESTROYED') {
                // This is for invoice reconciliation, the dtmEnd should be the end date
                // when BT has removed the service not when we receive it, its nornally a day behind
                $strDateEnd = $uxtEffectiveDate == '' ? 'NOW()' : "FROM_UNIXTIME($uxtEffectiveDate)";

                $strQuery = 'UPDATE tblProductComponentInstance ' .
                    " SET intStatusID  = '$intStatusID', " .
                    "     dtmEnd = $strDateEnd " .
                    " WHERE intProductComponentInstanceID = '{$this->m_intProductComponentInstanceID}'";
            } else {
                $strQuery = 'UPDATE tblProductComponentInstance ' .
                    " SET intStatusID  = '$intStatusID' " .
                    " WHERE intProductComponentInstanceID = '{$this->m_intProductComponentInstanceID}'";
            }

            PrimitivesQueryOrExit($strQuery, $dbhConnection, 'Update the product component instance status');

            $this->m_intStatusID = $intStatusID;

            return true;
        }

        return false;
    }

    /**
     * Get contract details
     *
     * @access public
     * @static
     * <AUTHOR>
     *
     * @param     integer $intContractID the product component contract ID
     *
     * @return array
     */
    public function getContractDetails($intContractID)
    {
        $dbhConn = get_named_connection_with_db('userdata');

        $strQuery = "SELECT intProductComponentContractID,
                            intProductComponentInstanceID,
                            intTariffID,
                            IF(intNextProductComponentContractID IS NULL, 0, intNextProductComponentContractID)
                                AS intNextProductComponentContractID,
                            IF(dteContractStart IS NULL, 0, UNIX_TIMESTAMP(dteContractStart))
                                AS uxtContractStart,
                            IF(dteContractEnd IS NULL, 0, UNIX_TIMESTAMP(dteContractEnd))
                                AS uxtContractEnd,
                            IF(dtmCreated IS NULL, 0, UNIX_TIMESTAMP(dtmCreated))
                                AS uxtCreated,
                            IF(dtmCancelled IS NULL, 0, UNIX_TIMESTAMP(dtmCancelled))
                                AS uxtCancelled,
                            IF(dtmRenewed IS NULL, 0, UNIX_TIMESTAMP(dtmRenewed))
                                AS uxtRenewed
                       FROM userdata.tblProductComponentContract
                      WHERE intProductComponentContractID = '$intContractID'";

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Get the product component instance ID');

        $arrContract = PrimitivesResultGet($resResult);

        return $arrContract;
    }

    /**
     * Get details of the previous contract
     *
     * @param int $intContractID Contract id
     *
     * @access public
     * <AUTHOR>
     * @return array
     */
    public function getPreviousContractDetails($intContractID)
    {

        $dbhConn = get_named_connection_with_db('userdata_reporting');

        $strQuery = "SELECT intProductComponentContractID,
                            intProductComponentInstanceID,
                            intTariffID,
                            IF(intNextProductComponentContractID IS NULL, 0, intNextProductComponentContractID)
                                AS intNextProductComponentContractID,
                            IF(dteContractStart IS NULL, 0, UNIX_TIMESTAMP(dteContractStart))
                                AS uxtContractStart,
                            IF(dteContractEnd IS NULL, 0, UNIX_TIMESTAMP(dteContractEnd))
                                AS uxtContractEnd,
                            IF(dtmCreated IS NULL, 0, UNIX_TIMESTAMP(dtmCreated))
                                AS uxtCreated,
                            IF(dtmCancelled IS NULL, 0, UNIX_TIMESTAMP(dtmCancelled))
                                AS uxtCancelled,
                            IF(dtmRenewed IS NULL, 0, UNIX_TIMESTAMP(dtmRenewed))
                                AS uxtRenewed
                       FROM userdata.tblProductComponentContract
                      WHERE intNextProductComponentContractID = '$intContractID'";

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Get the product component instance ID', false);
        if (!$resResult) {
            return false;
        }
        $arrContract = PrimitivesResultGet($resResult);

        return $arrContract;
    }


    /**
     * Renew a contract.
     *
     * @param int|bool $intAssignedNextTariffID Assigned next tariff id
     * @param int|bool $uxtDateOfRun            Date of run
     *
     * @access public
     * <AUTHOR>
     *
     * @return bool
     */
    public function renewContract($intAssignedNextTariffID = false, $uxtDateOfRun = false)
    {
        $arrOldTariff = array();
        $arrNewTariff = array();
        $arrOldContract = array();

        /********* START --- TEMP CODE FOR HP REFRESH 2009. TO BE REMOVED ONCE WE HAVE SWITCHED ALL THE CUSTOMERS *****/
        $intServiceComponentId = $this->getServiceComponentID();

        if ($this->getProductComponentID() == PRODUCT_COMPONENT_SUBSCRIPTION and
            ($intServiceComponentId == SCID_WLR_ANYTIME_PLUS || $intServiceComponentId == SCID_WLR_EVENING_WEEKEND)
        ) {
            //handle the forced switch to new products
            $this->handleHpRefresh2009Migration();
        }
        /************END --- TEMP CODE FOR HP REFRESH 2009. TO BE REMOVED ONCE WE HAVE SWITCHED ALL THE CUSTOMERS *****/


        //Get old and new tariff details

        $arrOldTariff = CProductComponent::getTariffDetails($this->m_intTariffID);

        if (!count($arrOldTariff)) {
            $this->setError(
                __FILE__,
                __LINE__,
                "Unable to find old Tariff details for tariff id '{$this->m_intTariffID}'"
            );

            return false;
        }

        if (!$arrOldTariff['bolAutoRenew']) {
            return true;
        }

        // If a specific tariff has been passed in make sure it's valid
        if (false !== $intAssignedNextTariffID) {
            $arrAssignedNewTariff = CProductComponent::getTariffDetails($intAssignedNextTariffID);

            if (empty($arrAssignedNewTariff)) {
                $intAssignedNextTariffID = false;
            }
        } // end if

        // If there is a next tariff ID, use it
        // otherwise call the next tariff selector
        if (false !== $intAssignedNextTariffID) {
            $intNewTariffId = $intAssignedNextTariffID;
        } else {
            $intNewTariffId = $this->getNextTariff($arrOldTariff, $uxtDateOfRun);
        }

        $arrNewTariff = CProductComponent::getTariffDetails($intNewTariffId);

        if (!count($arrNewTariff)) {
            $this->setError(
                __FILE__,
                __LINE__,
                "Unable to find new Tariff details for tariff id '{$intNewTariffId}'"
            );

            return false;
        }

        //Get old and new contract details

        $arrOldContract = CProductComponent::getContractDetails($this->m_intContractID);
        if (!count($arrOldContract)) {
            $this->setError(
                __FILE__,
                __LINE__,
                "Unable to find Contract details for contract id '{$this->m_intContractID}'"
            );

            return false;
        }

        $uxtNewContractStart = strtotime('+1 day', $arrOldContract['uxtContractEnd']);
        $uxtNewContractEnd = CProductComponent::calculateContractEnd(
            $uxtNewContractStart,
            $intNewTariffId,
            $this->m_intServiceID
        );

        if (false === $uxtNewContractEnd) {
            $this->setError(
                __FILE__,
                __LINE__,
                "Unable to calculate contract end date for PCI '{$this->m_intProductComponentInstanceID}'"
                . ", start '{$uxtNewContractStart}', service id '{$this->m_intServiceID}', "
                . "tariff id '{$intNewTariffId}'"
            );

            return false;
        }

        //Check if start date is before the requested end date - problem 46849
        if ($uxtNewContractStart >= $uxtNewContractEnd) {
            require_once '/local/data/mis/database/database_libraries/programme-tool-access.inc';
            $strProblemDescription = "method: renewContract\n\n"
                . "End date before the start date for PCI '{$this->m_intProductComponentInstanceID}',"
                . " start '{$uxtNewContractStart}' end '{$uxtNewContractEnd}', "
                . " service id '{$this->m_intServiceID}', "
                . " tariff id '{$intNewTariffId}'"
                . dss($this);
            pt_raise_autoproblem(
                'undefined',
                'Autoproblem: Contract error',
                $strProblemDescription,
                $strProblemDescription,
                false
            );

            $this->setError(__FILE__, __LINE__, $strProblemDescription);

            return false;
        }

        $arrArgs = array();
        $arrArgs['bolCreateScheduledPayment'] = $this->shouldGenerateScheduledPaymentsForContract();

        // Create new tblProductComponentContract entry
        $intNewContractID = $this->doCreateContract(
            $intNewTariffId,
            $uxtNewContractStart,
            $uxtNewContractEnd,
            $arrArgs
        );

        if (false === $intNewContractID) {
            return false;
        }

        $dbhConn = get_named_connection_with_db('userdata');

        $strQuery = "UPDATE userdata.tblProductComponentContract
                        SET dtmRenewed = NOW(),
                            intNextProductComponentContractID = '{$intNewContractID}'
                      WHERE intProductComponentContractID = '{$this->m_intContractID}'";

        PrimitivesQueryOrExit($strQuery, $dbhConn, 'Mark an old product component contract as renewed');

        $this->m_intContractID = $intNewContractID;

        // If changing tariff then set the product component instance tariff to the new one
        if ($this->m_intTariffID != $intNewTariffId) {
            $strQuery = 'UPDATE tblProductComponentInstance ' .
                "   SET intTariffID = '{$intNewTariffId}' " .
                " WHERE intProductComponentInstanceID = '{$this->m_intProductComponentInstanceID}'";

            PrimitivesQueryOrExit(
                $strQuery,
                $dbhConn,
                'Update product component instance with new tariff'
            );

            $this->m_intTariffID = $intNewTariffId;
        }

        return true;
    }

    /**
     * Get the ID of the next tariff
     * This function might not look like much, but it's designed to be
     * overridden in cases where the rules aren't so simple
     *
     * @see CWlrLineRentalSaver::getNextTariff
     *
     * @param array    $arrOldTariff Array of old tariff details
     * @param int|bool $uxtDateOfRun DateOfRun
     *
     * @return integer The ID of the next tariff
     */
    protected function getNextTariff(array $arrOldTariff, $uxtDateOfRun = false)
    {
        return $arrOldTariff['intNextTariffID'];
    }

    /**
     * Do the forced switch to new component
     * Temp function. To be removed after we have switched all the customers for HPR 2009
     *
     * @access   public
     * <AUTHOR> Zaki" <<EMAIL>>
     *
     * @return bool
     */
    private function handleHpRefresh2009Migration()
    {
        //Set some variables
        static $arrNewComponents = array(SCID_WLR_PN_TALK_ANYTIME, SCID_WLR_PN_TALK_EW);
        static $arrNewCompMap = array(
            SCID_WLR_ANYTIME_PLUS    => SCID_WLR_PN_TALK_ANYTIME,
            SCID_WLR_EVENING_WEEKEND => SCID_WLR_PN_TALK_EW
        );

        $intServiceId = $this->getServiceID();
        $dbhConn = get_named_connection_with_db('userdata');

        //Only get the active components
        //And the ones to whom we have given atleast 30 days notice. This will take care of interim accounts that
        //signed up after we gave the initial notification.
        $strQuery = "SELECT serviceid
                       FROM HPRefreshDataSets ds
                 INNER JOIN userdata.components c
                         ON c.service_id = ds.serviceid
                      WHERE ds.serviceid = '$intServiceId'
                        AND c.component_type_id IN (510, 515)
                        AND intScheduleEventId is null
                        AND c.status = 'active'
                        AND ds.sentdate <=  CURRENT_DATE - INTERVAL 30 DAY";

        $resResult = PrimitivesQueryOrExit(
            $strQuery,
            $dbhConn,
            'Get the service_id for the account to schedule hp comp change',
            false
        );
        if (!$resResult) {
            return false;
        }

        if (PrimitivesNumRowsGet($resResult) > 0) {
            $intComponentId = $this->getComponentID();
            $intServiceComponentId = $this->getServiceComponentID();
            $intNewServiceComponentId = $arrNewCompMap[$intServiceComponentId];
            $strContractHandle = CProductComponent::getContractLengthHandleFromTariffID($this->getTariffID());

            //If any pending product change event to new components dont do any thing.
            if ($intComponentId > 0) {
                $arrProdChangeEvents = CProductChange::getExistingEvents($intComponentId, true);
                if (count($arrProdChangeEvents) > 0) {
                    $arrSimplifiedProductChangeEvents = array();
                    foreach ($arrProdChangeEvents as $arrProdChangeEvent) {
                        $arrSimplifiedProductChangeEvents[] = $arrProdChangeEvent;
                    }
                    foreach ($arrSimplifiedProductChangeEvents as $arrEvent) {
                        $intNewComponentID = $arrEvent['intNewComponentID'];
                        if ($intNewComponentID > 0) {
                            $arrComponent = userdata_component_get($intNewComponentID);
                            if (in_array($arrComponent['component_type_id'], $arrNewComponents)) {
                                $strUpdate = "UPDATE HPRefreshDataSets
                                    SET intScheduleEventId = '{$arrEvent['intScheduledEventID']}'
                                    WHERE serviceid = $intServiceId";
                                PrimitivesQueryOrExit($strUpdate, $dbhConn);

                                return;
                            } else {
                                // We have a schdule product change to a legacy component type, cancel it
                                $objProdChangeEvent = new CProductChange($arrEvent['intProductChangeID']);
                                if (is_object($objProdChangeEvent)) {
                                    $bolRes = $objProdChangeEvent->cancel();
                                    if (!$bolRes) {
                                        // In case we fail cancel the event we will return false as we dont want to
                                        // schedule another product change
                                        return;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            $arrProductPaymentFrequencyOptions = CProduct::getProductPaymentFrequencyOptions(
                $intNewServiceComponentId,
                'SUBSCRIPTION',
                $strContractHandle
            );
            //Lets schedule the product change
            $arrReturn = CWlrProduct::scheduleComponentChange(
                $intServiceId,
                $intNewServiceComponentId,
                $arrProductPaymentFrequencyOptions[0]['intTariffID']
            );

            echo("Scheduling an account change for HPR2009 migration for component '$intComponentId' \n");
            //update HPRefreshDataSets so it does not get picked up again
            $strUpdate = "UPDATE HPRefreshDataSets
                          SET intScheduleEventId = '{$arrReturn['intScheduledEventID']}'
                          WHERE serviceid = $intServiceId";

            PrimitivesQueryOrExit($strUpdate, $dbhConn);
        }
    }

    /**
     * Creates a contract with the specified parameters, but checks to see if
     * there are any over-lapping contracts or if the contract exists already.
     *
     * @param int   $intTariffId      Tariff Id of the contract
     * @param int   $uxtContractStart Contract start date to create
     * @param int   $uxtContractEnd   Contract end date to create
     * @param array $arrArgs          Extra parameters
     *
     * @return integer
     */
    private function doCreateContract($intTariffId, $uxtContractStart, $uxtContractEnd, $arrArgs = array())
    {
        $bolCreateScheduledPayment = isset($arrArgs['bolCreateScheduledPayment'])
            ? $arrArgs['bolCreateScheduledPayment']
            : true;
        $intFreeMonths = isset($arrArgs['intPrepaidCount']) ? $arrArgs['intPrepaidCount'] : 0;
        $intContractId = null; // Somewhere by the end of all this we want a number in here

        $objPaymentStart = isset($arrArgs['objPaymentStart'])
            ? $arrArgs['objPaymentStart']
            : I18n_Date::fromTimestamp($uxtContractStart);

        // We need to perform some sanity-checking before we create this requested contract.
        // There are two kinds of contract that can be created:
        //   1. Pro-rata contract, which can begin on any day but must end on the day before
        //      their normal invoice day
        //   2. Regular contract, which begins on their invoice day and ends the day before
        //      the following invoice day
        // This is regardless of invoice period

        // We need to get a refreshed instance, as during account activation
        // the details in this array will have changed.
        // Cannot use Core_Service due to the transaction issue
        $service = userdata_service_get($this->m_intServiceID, true);
        $intRealLastDay = $service['invoice_day'];
        $intInvoiceDay = $service['invoice_day'];

        // Sanity check for the month we're in.
        $intInvoiceDay = ($intInvoiceDay <= date('t', $uxtContractEnd))
            ? $intInvoiceDay : date('t', $uxtContractEnd);
        $intLastDay = ($intInvoiceDay == 1) ? date('t', $uxtContractEnd) : ($intInvoiceDay - 1);

        if (date('j', $uxtContractEnd) != $intLastDay) {
            // The requested contract end day is not the same as the service's last invoice day
            // Therefore, don't create it, and raise an AP
            $strMessage = 'The CProductComponent::doCreateContract method was asked to create a new contract but ' .
                'the end-date specified did not fall on the day before the service\'s invoice_day. This means ' .
                'it\'s highly probable that the requested contract is incorrect and will lead to double-billing ' .
                'issues. Therefore CProductComponent::doCreateContract is refusing to create the contract. This ' .
                'also may mean that the service in question does not have a valid contract for the current ' .
                "period.\n\n" .
                'N.B.: *ALL* services mentioned on this problem *MUST* be investigated to ensure that they have ' .
                'been billed correctly in the past, as they may need refunding or some other manual fixing to ' .
                'ensure they will be billed correctly in the future!';

            $strVerbose = "Creating contract:\n" .
                "Service Id: {$this->m_intServiceID}\n" .
                "Product Component Instance ID: {$this->m_intProductComponentInstanceID}\n" .
                'Contract Start: ' . date('d/m/Y', $uxtContractStart) . "\n" .
                'Contract End: ' . date('d/m/Y', $uxtContractEnd) . "\n" .
                "Service Real Last Day: {$intRealLastDay}\n" .
                "Tariff ID: {$intTariffId}\n";

            $strVerbose .= "\nTrace: " . Logging::getCallStackAsString();
            pt_raise_autoproblem(
                'Contract Error',
                'Product Component Contract Error: Invalid Contract Requested',
                $strMessage,
                $strVerbose
            );

            $this->setError(__FILE__, __LINE__, 'Product Component Contract Error: Invalid Contract Requested');

            return false;
        } else {
            // The contract we're being asked to create appears sane .
            // Get any contracts from the DB that begin or end within the period we're trying to
            // create the new contract for
            $arrContracts = $this->getContractsForPeriod($uxtContractStart, $uxtContractEnd);

            if (!empty($arrContracts)) {
                foreach ($arrContracts as $arrContract) {
                    if ($arrContract['intTariffID'] == $intTariffId &&
                        date('d-m-y', $arrContract['uxtContractStart']) <= date('d-m-y', $uxtContractStart) &&
                        date('d-m-y', $arrContract['uxtContractEnd']) == date('d-m-y', $uxtContractEnd)
                    ) {
                        // Another contract exists in
                        $intContractId = $arrContract['intProductComponentContractId'];
                        // If we're reusing an existing contract then we don't want to create the scheduled payment
                        // regardless of what we said earlier. The existing contract probably has them already.
                        $bolCreateScheduledPayment = false;
                    } else {
                        // In all other circumstances we want to cancel the existing contract
                        // and create the new one. If we get to here that means the contract
                        // in the DB does not have an end date of the day before invoice day
                        // and so is out-of-sync. Don't worry. We raise an AP about this later
                        $arrContractIds[] = $arrContract['intProductComponentContractId'];
                    }
                }

                if (!empty($arrContractIds)) {
                    $this->cancelContracts($arrContractIds);
                }
            }
        }

        if (!empty($intContractId) || !empty($arrContractIds)) {
            // We did something that wasn't straight-forward that we think we probably shouldn't have had to do, so
            // we need to raise a P2 auto-problem to alert us to the fact that this has happened. Doesn't help us
            // in tracking down the cause, really, because it's already happened. We're just seeing the affect of
            // whatever it was that went wrong.
            $strMessage = 'The CProductComponent::doCreateContract method detected duplicate or over-lapping '
                . 'contracts and was forced to take action to avoid collisions which lead to double-billing.'
                . ' This should not  happen, and indicates that some other process is not performing the '
                . 'correct behaviour.' . "\n\n" . 'N.B.: *ALL* services mentioned on this problem *MUST* '
                . 'be investigated to ensure that they have been billed correctly in the past, as they '
                . 'may need refunding or some other manual fixing to ensure they they will be billed '
                . 'correctly in the future!';

            $strVerbose = "Creating contract:\n" .
                "Service Id: {$this->m_intServiceID}\n" .
                "Product Component Instance ID: {$this->m_intProductComponentInstanceID}\n" .
                'Contract Start: ' . date('d/m/Y', $uxtContractStart) . "\n" .
                'Contract End: ' . date('d/m/Y', $uxtContractEnd) . "\n" .
                "Tariff ID: {$intTariffId}\n";

            if (!empty($intContractId)) {
                $strVerbose .= "Identical Contract Detected (used): {$intContractId}\n";
            }

            if (!empty($arrContractIds)) {
                $strVerbose .= 'Over-lapping Contracts cancelled: ' . implode(', ', $arrContractIds) . "\n";
            }

            $strVerbose .= "\nTrace: " . Logging::getCallStackAsString();

            pt_raise_autoproblem(
                'Contract Error',
                'Product Component Contract Error: Duplicate/Over-lapping contract(s) detected',
                $strMessage,
                $strVerbose
            );
        }

        if (empty($intContractId)) {
            // We didn't detect an identical contract, so it's safe for us to create one
            $strQuery = "
INSERT INTO userdata.tblProductComponentContract
        SET intProductComponentInstanceID = '{$this->m_intProductComponentInstanceID}',
            intTariffID                   = '{$intTariffId}',
            dteContractStart              = FROM_UNIXTIME($uxtContractStart),
            dteContractEnd                = FROM_UNIXTIME($uxtContractEnd),
            dtmCreated                    = NOW()";

            $dbhConn = get_named_connection_with_db('userdata');
            PrimitivesQueryOrExit($strQuery, $dbhConn, 'Create a contract for a product component');
            $intContractId = PrimitivesInsertIdGet($dbhConn);
        }

        if ($bolCreateScheduledPayment) {
            //Create all scheduled payments for the duration of the contract.
            $arrScheduledPaymentDetails = $this->generateScheduledPaymentsDetailsForContract(
                $intTariffId,
                $objPaymentStart,
                $intFreeMonths
            );
            if (false === $arrScheduledPaymentDetails) {
                return false;
            }
            $this->createScheduledPayments($arrScheduledPaymentDetails, $intContractId);
        }

        return $intContractId;
    }

    /**
     * Cancel a contract
     *
     * @param array $arrArgs Arguments
     *
     * @access   private
     * <AUTHOR> Jones" <<EMAIL>>
     *
     * @return bool
     */
    public function cancelContract($arrArgs = array())
    {
        $uxtCancelled = (isset($arrArgs['uxtCancelled'])
            && is_numeric($arrArgs['uxtCancelled'])
        ) ? $arrArgs['uxtCancelled'] : time();

        $dbhWrite = get_named_connection_with_db('userdata');

        // Don't set the cancel date to anything less than the contract start date
        $strQuery = "UPDATE userdata.tblProductComponentContract \n" .
            "  SET dtmCancelled = FROM_UNIXTIME($uxtCancelled) \n" .
            "  WHERE intProductComponentContractID = '{$this->m_intContractID}' \n" .
            "    AND dteContractStart <= FROM_UNIXTIME($uxtCancelled) \n" .
            "    AND dtmCancelled IS null \n" .
            "    AND dteContractEnd >= FROM_UNIXTIME($uxtCancelled) \n";

        PrimitivesQueryOrExit(
            $strQuery,
            $dbhWrite,
            'CProductComponent::cancelContract',
            true
        );

        return true;
    }

    /**
     * Cancels the contracts identified by the given contracts IDs
     *
     * @param array $arrContractIdsToCancel The contract IDs to cancel
     *
     * @return boolean
     */
    private function cancelContracts($arrContractIdsToCancel)
    {
        $strIn = implode(', ', $arrContractIdsToCancel);

        $strQuery = "
UPDATE userdata.tblProductComponentContract
   SET dtmCancelled = NOW()
 WHERE intProductComponentContractId IN ({$strIn})";

        $dbhConnection = get_named_connection_with_db('userdata');

        PrimitivesQueryOrExit(
            $strQuery,
            $dbhConnection,
            'CProductComponent::cancelContracts',
            false
        );

        return (PrimitivesAffectedRowsGet($dbhConnection) > 0);
    }

    /**
     * Cancels the existing contracts on an account for a particular ProductComponentInstanceId given
     *
     * @param array $arrArgs Arguments
     *
     * <AUTHOR> Appukuttan
     *
     * @return boolean
     */
    public function cancelAllExistingContracts($arrArgs = array())
    {
        $dbhConnection = get_named_connection_with_db('userdata');

        $cancelled = PrimitivesRealEscapeString(
            (isset($arrArgs['uxtCancelled']))
                ? "FROM_UNIXTIME({$arrArgs['uxtCancelled']})"
                : 'NOW()',
            $dbhConnection
        );

        //Set the cancelled date to current time for all the existing contracts for an account
        $strQuery = " UPDATE userdata.tblProductComponentContract"
            . " SET dtmCancelled = {$cancelled}"
            . " WHERE intProductComponentInstanceID = '"
            . PrimitivesRealEscapeString($this->m_intProductComponentInstanceID, $dbhConnection)
            . "' AND dtmCancelled IS null"
            . " AND dteContractEnd >= {$cancelled}";

        PrimitivesQueryOrExit($strQuery, $dbhConnection, 'CProductComponent::cancelPreviousContracts', true);

        return true;
    }

    /**
     * Calculate the end date of a contract given the start date and the tariff
     *
     * @access private
     * <AUTHOR>
     *
     * @param int     $uxtContractStart  The start of the contract we want to calculate end date for
     * @param int     $intTariffID       Tariff Id of the contract
     * @param int     $intServiceId      The service we are trying to deal with
     * @param boolean $bolRetentionOffer Is the contract a retention offer
     *
     * @return int
     */
    public function calculateContractEnd($uxtContractStart, $intTariffID, $intServiceId, $bolRetentionOffer = false)
    {
        require_once '/local/data/mis/database/database_libraries/components/CProductComponentUtil.php';
        $util = new CProductComponentUtil();

        return $util->calculateContractEnd($uxtContractStart, $intTariffID, $intServiceId, $bolRetentionOffer);
    }

    /**
     * Get the date of the start of the current payment period for use in scheduled payments
     *
     * @access private
     * @static
     * <AUTHOR>
     *
     * @param  integer $uxtEndDate   End date
     * @param  integer $intTariffID  tariff id
     * @param  integer $intServiceId Service id
     *
     * @return unix    timestamp of the date
     */
    public function calculatePaymentPeriodStart($uxtEndDate, $intTariffID, $intServiceId)
    {
        if (!$uxtEndDate || empty($intTariffID)) {
            return false;
        }

        $arrTariff = CProductComponent::getTariffDetails($intTariffID);

        $arrService = userdata_service_get($intServiceId);

        // DJM2013: the call to getBillingDateFromDateAndPeriod can (and frequently does!) cause an auto-problem
        // to be triggered.  As this auto-problem serves no functional purpose, this is now suppressed
        // See http://jira.internal.plus.net/browse/LCST-425 for further details
        // Note that we reset the "suppressed" flag after each call, to avoid hiding other, valid failure scenarios
        $oldSuppressValue = Core_BillingDate_Facade::suppressAutoProblem(true);

        $objBillingDate = Core_BillingDate_Facade::getBillingDateFromDateAndPeriod(
            $intServiceId,
            date('Y-m-d', $uxtEndDate),
            CProductComponent::getMonthsFromPaymentFrequencyHandle($arrTariff['strPaymentFrequencyHandle']),
            $arrService['invoice_day']
        );

        Core_BillingDate_Facade::suppressAutoProblem($oldSuppressValue);

        return $objBillingDate->getPrevBillingDate()->getBillingDate()->getTimestamp();
    }

    /**
     * Get the date of the end of the current payment period for use in scheduled payments.  Returns a timestamp
     * representing the end-date for the payment period.
     *
     * @param int $uxtStartDate Start date
     * @param int $intTariffID  tariff id
     * @param int $intServiceId Service id
     *
     * @return int
     */
    public function calculatePaymentPeriodEnd($uxtStartDate, $intTariffID, $intServiceId)
    {
        if (!$uxtStartDate || empty($intTariffID)) {
            return false;
        }

        $arrTariff = CProductComponent::getTariffDetails($intTariffID);

        $arrService = userdata_service_get($intServiceId, true);

        // DJM2013: the call to getBillingDateFromDateAndPeriod can (and frequently does!) cause an auto-problem
        // to be triggered.  As this auto-problem serves no functional purpose, this is now suppressed
        // See http://jira.internal.plus.net/browse/LCST-425 for further details
        // Note that we reset the "suppressed" flag after each call, to avoid hiding other, valid failure scenarios
        $oldSuppressValue = Core_BillingDate_Facade::suppressAutoProblem(true);

        $objBillingDate = Core_BillingDate_Facade::getBillingDateFromDateAndPeriod(
            $intServiceId,
            date('Y-m-d', $uxtStartDate),
            CProductComponent::getMonthsFromPaymentFrequencyHandle($arrTariff['strPaymentFrequencyHandle']),
            $arrService['invoice_day']
        );

        Core_BillingDate_Facade::suppressAutoProblem($oldSuppressValue);

        return $objBillingDate->getNextBillingDate()->getBillingDate()->getTimestamp();
    }

    /**
     * Returns scheduled payments for component instance
     * Only for subscribtion, call feature and TV channel pack payments
     *
     * @param int    $uxtDueDate Scheduled payment due date in Mysql format YYYY-MM-DD
     * @param string $strOrderBy Order by
     *
     * @return array
     */
    public function getScheduledPayments($uxtDueDate = null, $strOrderBy = null)
    {
        $strRestrictedDueDate = !empty($uxtDueDate) ? " AND dteDue = DATE(FROM_UNIXTIME($uxtDueDate))" : '';

        $strOrderBy = (!empty($strOrderBy)) ? "ORDER BY $strOrderBy" : null;

        $intProductComponentInstanceId = $this->getProductComponentInstanceID();

        $dbhConnection = get_named_connection_with_db('userdata_reporting');
        $intProductComponentInstanceId = addslashes($intProductComponentInstanceId);

        //Get only subscription payments and payments for call features and TV channel packs
        $strQuery = "
            SELECT
                sp.intScheduledPaymentID,
                sp.dteStartPeriodCovered,
                sp.dteEndPeriodCovered,
                sp.dteDue,
                pc.vchHandle,
                IF (DATE_SUB(DATE_ADD(sp.dteStartPeriodCovered, INTERVAL 1 MONTH), INTERVAL 1 DAY)
                    = sp.dteEndPeriodCovered, true, false) AS bolIsMonth
            FROM
                financial.tblScheduledPayment sp
                INNER JOIN financial.tblConfigProductComponent cpc
                    ON (sp.intScheduledPaymentID = cpc.intScheduledPaymentID)
                INNER JOIN userdata.tblProductComponentInstance pci
                    ON (pci.intProductComponentInstanceID = cpc.intProductComponentInstanceID)
                INNER JOIN dbProductComponents.tblProductComponent pc
                    ON (pci.intProductComponentID = pc.intProductComponentID)
            WHERE
                pci.intProductComponentInstanceID = '$intProductComponentInstanceId'
                AND intSalesInvoiceID IS null
                AND dtmCancelled IS null
                AND dtmFailed IS null
                AND dteDue < dteEndPeriodCovered
                AND (pc.vchHandle = 'SUBSCRIPTION' OR pc.vchHandle LIKE 'Wlr%' OR pc.vchHandle LIKE 'TvChannelPack%')
                $strRestrictedDueDate
                $strOrderBy";

        $resResult = PrimitivesQueryOrExit(
            $strQuery,
            $dbhConnection,
            'get Sheduled Payments',
            false
        );

        return PrimitivesResultsAsArrayGet($resResult);
    }

    /**
     * scheduledPaymentExists
     *
     * @param mixed $uxtStart Start time
     * @param mixed $uxtEnd   End time
     *
     * @return boolean
     */
    protected function scheduledPaymentExists($uxtStart, $uxtEnd)
    {
        return (count($this->getScheduledPaymentForPeriod($uxtStart, $uxtEnd)) > 0);
    }

    /**
     * Utility method used by getScheduledPaymentForPeriod and
     * getNonConcurrentScheduledPaymentForPeriod.
     *
     * @param mixed $uxtStart Start time
     * @param mixed $uxtEnd   End time
     *
     * @return boolean
     **/
    protected function _validateStartEndPeriod($uxtStart, $uxtEnd)
    {
        if (!ctype_digit($uxtStart) && (!is_int($uxtStart) || $uxtStart < 0)) {
            $this->setError(
                __FILE__,
                __LINE__,
                'Invalid uxtStart variable supplied'
            );

            return false;
        }

        if (!ctype_digit($uxtEnd) && (!is_int($uxtEnd) || $uxtEnd < 0)) {
            $this->setError(
                __FILE__,
                __LINE__,
                'Invalid uxtEnd variable supplied'
            );

            return false;
        }

        if ($uxtStart >= $uxtEnd) {
            $this->setError(
                __FILE__,
                __LINE__,
                "Start period ({$uxtStart}) is ahead (or same as) of end period ({$uxtEnd})"
            );

            return false;
        }

        return true;
    }

    /**
     * getScheduledPaymentForPeriod
     *
     * For the date period supplied, this method should return *ALL* scheduled
     * payments linked to the product component instance that this object is using.
     *
     * @param mixed $uxtStart Start time
     * @param mixed $uxtEnd   End time
     *
     * @return array|boolean
     */
    protected function getScheduledPaymentForPeriod($uxtStart, $uxtEnd)
    {
        if (!$this->_validateStartEndPeriod($uxtStart, $uxtEnd)) {
            return false;
        }

        // We must read from the master coredb to avoid any replication lag.
        // The slightest whiff of replication lag in this case would otherwise
        // mean that the system wouldn't "see" a given scheduled payment, thus
        // would end up double-billing an account elsewhere. (due to the creation
        // of a scheduled payment for a period it can't "see")

        $dbhConnection = get_named_connection_with_db('financial');

        $strQuery = "
    SELECT sp.intScheduledPaymentId
      FROM financial.tblScheduledPayment sp
INNER JOIN financial.tblConfigProductComponent cpc
        ON sp.intScheduledPaymentId = cpc.intScheduledPaymentId
INNER JOIN userdata.tblProductComponentInstance pci
        ON cpc.intProductComponentInstanceId = pci.intProductComponentInstanceId
     WHERE pci.intProductComponentInstanceId = {$this->m_intProductComponentInstanceID}
       AND sp.dteEndPeriodCovered   >= FROM_UNIXTIME({$uxtStart})
       AND sp.dteStartPeriodCovered <= FROM_UNIXTIME({$uxtEnd})
       AND sp.dtmCancelled IS null";

        $resResult = PrimitivesQueryOrExit(
            $strQuery,
            $dbhConnection,
            'check ScheduledPayment exists',
            false
        );

        return PrimitivesResultsAsArrayGet($resResult);
    }


    /**
     * This method helps work around the problem that certain product component
     * instances can generate multiple scheduled payments for the *same* period
     * of time but for different purposes, meaning that technically more than one
     * sheduled payment for a given period isn't actually double-billing.
     *
     * Consider WLR for instance - its WlrLineRent product component instance
     * supports a variety of charges:
     *
     *  - (additional) monthly line rental charge (n/a on PAYG/E&W products)
     *  - actual cost of calls made
     *  - call barring charges (e.g. going over credit limit)
     *  - non-Direct-Debit-payment charges
     *
     * All these particular types of charge hang off the WlrLineRent product component
     * instance for an account and it's perfectly reasonable to have a monthly
     * subscription, line rental and call charges all for the same period, of which
     * neither of these conflict with each other - they're all independent charges
     * covering different things.
     *
     * Thus, I would say they can all be "concurrent".
     *
     * The purpose of this method is to return back information about scheduled
     * payments on a product component instance that shouldn't otherwise exist
     * more than once for a given time period.
     *
     * By default it looks for all scheduled payments, therefore child classes
     * can override this method with a more specialised version.
     *
     * @param integer $uxtStart Start time
     * @param integer $uxtEnd   End time
     *
     * @return array|boolean
     **/
    protected function getNonConcurrentScheduledPaymentForPeriod($uxtStart, $uxtEnd)
    {
        return $this->getScheduledPaymentForPeriod($uxtStart, $uxtEnd);
    }

    /**
     * Get the number of months representation of a contract handle
     *
     * @param string $strHandle Contract Handle
     *
     * @return integer
     */
    public static function getMonthsFromContractHandle($strHandle)
    {
        self::includeLegacyFiles();

        return ProductComponent_Tariff::getContractLengthByHandle($strHandle);
    }

    /**
     * Get the number of months representation of a payment frequency
     *
     * @param string $strHandle Payment frequency
     *
     * @return integer
     */
    private static function getMonthsFromPaymentFrequencyHandle($strHandle)
    {
        self::includeLegacyFiles();

        return ProductComponent_Tariff::getPaymentFrequencyByHandle($strHandle);
    }

    /**
     * Returns Reseller_Discount object for given service
     *
     * @param Core_Service $objService Service
     *
     * @uses   Reseller_DiscountHelper::getByServiceId()
     * @uses   Reseller_DiscountHelper::getDiscount()
     *
     * <AUTHOR> Chudzinski <<EMAIL>>
     *
     * @return Reseller_Discount
     */
    public function getPartnerDiscount(Core_Service $objService)
    {
        $helper = Reseller_DiscountHelper::getByServiceId(new Int($objService->getServiceId()));

        return $helper->getDiscount(new Int($objService->getServiceDefinitionId()));
    }

    /**
     * Modifies partner invoice description for scheduled payments
     *
     * It is based on invoice description with scheduled payment period.
     * For partner user, discount and customer information is added as well.
     *
     * @param string            $strInvoiceDescription Base invoice description
     * @param Core_Service      $objService            Service object
     * @param Reseller_Discount $objDiscount           Partner discount object
     *
     * <AUTHOR> Chudzinski <<EMAIL>>
     *
     * @return string
     */
    protected static function modifyInvoiceDescriptionForPartner(
        $strInvoiceDescription,
        Core_Service $objService,
        Reseller_Discount $objDiscount = null
    ) {
        self::includeLegacyFiles();

        // include enduser secondary ID (e.g. original ID for migrated BBScope endusers) in invoice description
        if (Reseller_EndUser::isPartnerEndUser($objService->getUsername())) {
            // prepend slash, trimming to nothing if secondary ID is empty or blanks
            $strSecondaryId = rtrim('/' . Reseller_EndUser::getEndUserByUsername($objService->getUsername())
                                        ->getSecondaryId(), '/ ');
        }

        $strInvoiceDescription .= " ({$objService->getUsername()}$strSecondaryId)";

        if (!is_null($objDiscount) && ($objDiscount->getPercentageDiscount()->getValue() > 0)) {
            $strInvoiceDescription .= ' (Discount at ' . $objDiscount->getPercentageDiscount() . '%)';
        }

        $objTagHelper = ResellerTag_TagHelper::getByUsername(new String($objService->getUsername()));
        $strInvoiceDescription .= $objTagHelper->getInvoiceItemDescriptionModifier();

        return $strInvoiceDescription;
    }

    /**
     * Applies discount if avaliable for partner
     *
     * @param int               $intAmountIncVatPence Amount including VAT in pence
     * @param Reseller_Discount $objDiscount          Discount
     *
     * <AUTHOR> Chudzinski <<EMAIL>>
     *
     * @uses   Reseller_Discount::getPercentageDiscount()
     * @uses   Reseller_Discount::applyDiscountPence()
     *
     * @return int If discount avaliable returns discounted amount inc VAT in pence
     */
    public function applyPartnerDiscount($intAmountIncVatPence, Reseller_Discount $objDiscount)
    {
        if ($objDiscount->getPercentageDiscount()->getValue() > 0) {
            $intAmountIncVatPence = $objDiscount->applyDiscountPence(new Int($intAmountIncVatPence))->getValue();
        }

        return $intAmountIncVatPence;
    }

    /**
     * Generates scheduled payment details for given contract details
     *
     * It returns an array in the following format:
     * <pre>
     * array(
     *     array(
     *         'intAmountIncVatPence'        => [Int],
     *         'objDueDate'                  => [I18n_Date],
     *         'strPeriodInvoiceDescription' => [String],
     *         'objStartPeriod'              => [I18n_Date],
     *         'objEndPeriod'                => [I18n_Date],
     *     )
     * )
     * </pre>
     *
     * @param int       $intTariffId                        Tariff Id
     * @param I18n_Date $objStartDate                       Subscription start date
     * @param int       $intFreeMonths                      Number of months customer won't be charged for (starting
     *                                                      form subscription start date)
     * @param I18n_Date $objContractEndDate                 Contract end date
     * @param int       $intRemainingDeviationPriceInMonths Number of months customer should be charged at the
     *                                                      deviation price
     *
     * <AUTHOR> Marek <<EMAIL>>
     *
     * @uses   CProduct::getTariffDetails()
     * @uses   CProductComponent::getMonthsFromPaymentFrequencyHandle()
     * @uses   CProductComponent::getMonthsFromContractHandle()
     * @uses   CProductComponent::getInvoiceProductComponentDisplayName()
     * @uses   ProductComponent_TariffDeviation
     * @uses   CProductComponent::generateInvoiceDescriptionForScheduledPayment()
     *
     * @return array
     */
    public function generateScheduledPaymentsDetailsForContract(
        $intTariffId,
        I18n_Date $objStartDate,
        $intFreeMonths = 0,
        I18n_Date $objContractEndDate = null,
        $intRemainingDeviationPriceInMonths = null
    ) {
        $arrDetails = array();

        if (empty($intTariffId)) {
            $this->setError(
                __FILE__,
                __LINE__,
                "Invalid Tariff ID  '{$intTariffId}' passed to "
                . "CProductComponent::generateScheduledPaymentsDetailsForContract()"
            );

            return false;
        }

        if (!isset($intFreeMonths) || ($intFreeMonths < 0)) {
            $intFreeMonths = 0;
        }

        //get tariff details
        $arrTariffDetails = CProduct::getTariffDetails($intTariffId);

        // Nothing to pay - do not generate scheduled payment details
        if ($arrTariffDetails['intCostIncVatInPence'] <= 0 && $this->displaySplitCharge() == false) {
            return $arrDetails;
        }

        $intContractLengthInMonths = self::getMonthsFromContractHandle($arrTariffDetails['strContractHandle']);
        $strInvoiceDescription = $this->getInvoiceProductComponentDisplayName($intTariffId) . ' charge ';

        $objTariffDeviation = new ProductComponent_TariffDeviation($intTariffId);

        // Get uptodate information about contract for this component
        $objContract = ProductComponent_Contract::getCurrent($this->m_intProductComponentInstanceID);

        // If there is no current contract it will mean that a new contract is just about to start
        // and it will start with customer's next invoice date
        $uxtContractStartDate = ($objContract instanceof ProductComponent_Contract)
            ? $objContract->getStartDate()
            : $this->m_uxtNextInvoice;

        $service = userdata_service_get($this->m_intServiceID, true);

        // get discountable product component
        $arrDiscountableStates = array(
            'ACTIVE',
            'QUEUED_ACTIVATE',
            'QUEUED_REACTIVATE',
            'QUEUED_DEACTIVATE',
            'DEACTIVE'
        );
        $intInternetConnectionSubscriptionInstanceId = self::getProductComponentInstance(
            $this->getServiceID(),
            'SUBSCRIPTION',
            $arrDiscountableStates,
            'INTERNET_CONNECTION'
        );

        // Our billing info is for the specific instance tariff, but we must remember the customer's
        // usual invoice day.
        try {
            $paymentDate = Core_BillingDate_Facade::getBillingDateFromDateAndPeriod(
                $this->getServiceID(),
                $service['next_invoice'],
                $arrTariffDetails['strPaymentFrequencyHandle'],
                $service['invoice_day']
            );

            //Fix for problem 63169 - Get servicePeriod from the I18n_Date object passed into this function
            $servicePeriod = $paymentDate->getBillingServicePeriodForDate($objStartDate);

            //Fix for problem 63169 - Create new Core_BillingDate object from the $servicePeriod
            $paymentDate = Core_BillingDate_Facade::getBillingDateFromDateAndPeriod(
                $this->getServiceID(),
                $servicePeriod->getStart()->toMysql(),
                $arrTariffDetails['strPaymentFrequencyHandle'],
                $service['invoice_day']
            );

            $paymentDatesForContract = $paymentDate->getBillingDatesForMonths(
                new UnsignedInt($intContractLengthInMonths)
            );

            $intFrequencyPeriodInMonths = (int)$paymentDate->getBillingPeriod()->getValue();

            // Ben and Stephen say:
            // This is a counter to remove payment dates from the begining of the array
            // So, if you wanted to create scheduled payments for Jan, Feb, Mar, but you had
            // one month free, then we actually want: Feb,Mar AND NOT Jan.
            // Please note that this is destructive for $intFreeMonths
            while ($intFreeMonths > 0) {
                array_shift($paymentDatesForContract);
                $intFreeMonths--;
            }

            foreach ($paymentDatesForContract as $paymentDate) {
                $createPayment = true;

                // Only generate payment details if its due before the end of the contract
                if (is_null($objContractEndDate)
                    || $paymentDate->getBillingDate()->getTimestamp() < $objContractEndDate->getTimestamp()
                ) {
                    $objStartPeriod = $paymentDate->getBillingServicePeriod()->getStart();
                    $objEndPeriod = $paymentDate->getBillingServicePeriod()->getEnd();

                    $intPrice = $objTariffDeviation->getPrice($uxtContractStartDate, $objEndPeriod->getTimestamp());
                    if (!is_null($intPrice) && (is_null($intRemainingDeviationPriceInMonths)
                            || $intRemainingDeviationPriceInMonths > 0)
                    ) {
                        $intAmountIncVatPence = $intPrice;
                        $intRemainingDeviationPriceInMonths--;
                    } else {
                        $intAmountIncVatPence = $arrTariffDetails['intCostIncVatInPence'];
                    }

                    $strPeriodInvoiceDescription
                        = $strInvoiceDescription . 'for the period ' . $objStartPeriod . ' to ' . $objEndPeriod . ' ';

                    // Using legacy functions as to not cause transaction issues
                    // Transaction issue not present for partner, so instantiate
                    // core service object in the if statement
                    $arrService = userdata_service_get($this->getServiceID());
                    if ('partner' == $arrService['isp']) {
                        $objDiscount = null;
                        $objService = new Core_Service($this->getServiceID());

                        // only do discounts for internet subscription component
                        if (($intSubscriptionProductComponentInstanceId !== false)
                            && ($this->getProductComponentInstanceID() == $intInternetConnectionSubscriptionInstanceId)
                        ) {
                            $objDiscount = $this->getPartnerDiscount($objService);
                            $intAmountIncVatPence = $this->applyPartnerDiscount($intAmountIncVatPence, $objDiscount);
                        }

                        $strPeriodInvoiceDescription = self::modifyInvoiceDescriptionForPartner(
                            $strPeriodInvoiceDescription,
                            $objService,
                            $objDiscount
                        );
                    }

                    $arrScheduledPaymentDetails['intAmountIncVatPence'] = $intAmountIncVatPence;
                    $arrScheduledPaymentDetails['objDueDate'] = $paymentDate->getBillingDate();
                    $arrScheduledPaymentDetails['strPeriodInvoiceDescription'] = $strPeriodInvoiceDescription;
                    $arrScheduledPaymentDetails['objStartPeriod'] = $objStartPeriod;
                    $arrScheduledPaymentDetails['objEndPeriod'] = $objEndPeriod;

                    $arrDetails[] = $arrScheduledPaymentDetails;
                }
            }
        } catch (Core_Exception $exc) {
            // Core_Exception occurs inside one of the Core objects (obviously).
            // Specifically - if the invoice date and the invoice day don't correlate then
            // we've got nonsense data somewhere. It's better we don't do anything and raise
            // a problem.
            $strMessage = 'An exception was thrown when attempting to generate the scheduled payment details '
                . 'for an account. No scheduled payments were created, which means the service will '
                . 'not be billed until the situation is resolved.';

            $strVerbose = "Creating scheduled payment:\n"
                . "Service Id: {$this->m_intServiceID}\n"
                . "Product Component Instance ID: {$this->m_intProductComponentInstanceID}\n"
                . "Payment Frequency: {$arrTariffDetails['strPaymentFrequencyHandle']}\n"
                . 'Invoice Date: ' . $service['next_invoice'] . "\n"
                . "Invoice Period: {$service['invoice_period']}\n"
                . "Invoice Day: {$service['invoice_day']}\n"
                . "Exception Message: {$exc->getMessage()}\n";
            $strVerbose .= "\nTrace: " . Logging::getCallStackAsString();
            pt_raise_autoproblem(
                'Scheduled Payment Invalid',
                'Scheduled Payment error: Failed to generate scheduled payment details',
                $strMessage,
                $strVerbose
            );

            $this->setError(
                __FILE__,
                __LINE__,
                'Scheduled Payment error: Failed to generate scheduled payment details'
            );
            $arrDetails = false;
        }

        return $arrDetails;
    }

    /**
     * Creates scheduled payments from given details
     *
     * @param array $arrDetails    Scheduled payments details {@see
     *                             CProductComponent::generateScheduledPaymentsDetailsForContract()}
     * @param int   $intContractId Contract id
     *
     * <AUTHOR> Marek <<EMAIL>>
     *
     * @uses   CProductComponentPaymentScheduler::addIncVatAmount()
     *
     * @return int
     */
    public function createScheduledPayments(array $arrDetails, $intContractId = null)
    {
        $intNumber = 0;

        foreach ($arrDetails as $arrScheduledPaymentDetails) {
            $intAmountIncVatPence = $arrScheduledPaymentDetails['intAmountIncVatPence'];
            $objDueDate = $arrScheduledPaymentDetails['objDueDate'];
            $strPeriodInvoiceDescription = $arrScheduledPaymentDetails['strPeriodInvoiceDescription'];
            $objStartPeriod = $arrScheduledPaymentDetails['objStartPeriod'];
            $objEndPeriod = $arrScheduledPaymentDetails['objEndPeriod'];

            $createPayment = true;

            // Is there an existing scheduled payment for this period?
            $scheduledPayments = $this->getNonConcurrentScheduledPaymentForPeriod(
                $objStartPeriod->getTimestamp(),
                $objEndPeriod->getTimestamp()
            );

            foreach ($scheduledPayments as $scheduledPayment) {
                $scheduledPayment = new CScheduledPayment($scheduledPayment['intScheduledPaymentId']);

                if ($scheduledPayment->hasBeenInvoiced()) {
                    // This scheduled payment has already been invoiced. We don't want to create another
                    // for the same period
                    $createPayment = false;
                } else {
                    // The scheduled payment in the DB hasn't been invoiced. We should just cancel it.
                    $scheduledPayment->cancel();
                }
            }

            if ($createPayment) {
                // Go and and create the scheduled payment
                $objPaymentScheduler = new CProductComponentPaymentScheduler(
                    $this->m_intProductComponentInstanceID,
                    '',
                    $intContractId
                );
                $intResult = $objPaymentScheduler->addIncVatAmount(
                    $intAmountIncVatPence,
                    $objDueDate->getTimestamp(),
                    $strPeriodInvoiceDescription,
                    $objStartPeriod->getTimestamp(),
                    $objEndPeriod->getTimestamp()
                );

                if (false !== $intResult) {
                    $intNumber++;
                }
            }
        }

        return $intNumber;
    }

    /**
     * Check if the product component is subscription component
     * @access public
     *
     * <AUTHOR> Chudzinski <<EMAIL>>
     *
     * @return bool true if it is subscription component, false otherwise
     */
    public function isSubscriptionComponent()
    {
        return ('SUBSCRIPTION' == $this->m_strHandle);
    }

    /**
     * Check if the product component has scheduled payment for given due date
     * @access public
     *
     * <AUTHOR> Chudzinski <<EMAIL>>
     *
     * @param  int $uxtDueDate Due date as unix timestamp
     *
     * @return bool
     */
    public function hasScheduledPaymentForDueDate($uxtDueDate)
    {
        $arrScheduledPayments = $this->getScheduledPayments($uxtDueDate);

        return (0 < count($arrScheduledPayments));
    }

    /**
     * Marks Call Feature for remove.
     *
     * Provisioning script will pick up all call features with status QUEUED_DESTROY.
     *
     * @access public
     * <AUTHOR>
     *
     * @return void
     */
    public function markForRemoval()
    {
        //todo: set product component status to 'queued-destroyed'
        $this->prvSetStatus('QUEUED_DESTROY');
    }

    /**
     * Set a new record in the config table
     *
     * @access private
     * <AUTHOR>
     * @return config id (or true if nothing to do)
     */
    public function prvWriteUserdataConfigRecord()
    {
        // Check whether there is an active config record
        if (isset($this->m_intConfigID) && $this->m_intConfigID > 0) {
            $this->prvEndCurrentUserdataConfigRecord();
        }

        return $this->prvInsertUserdataConfigRecord();
    } // end of function prvWriteUserdataConfigRecord

    /**
     * Inserts a record to the userdata config table for the product component instance
     *
     * @access private
     * <AUTHOR>
     * @return int Config ID (or true if nothing to do)
     */
    public function prvInsertUserdataConfigRecord()
    {
        if ($this->prvHasValidUserdataConfigTable()) {
            return $this->prvInsertOwnUserdataConfigRecord();
        }

        return true;
    }

    /**
     * You should override this function to handle extra config items for the product component
     *
     * Performs a specific insert for the relevant details required for the relevant product component settings
     *
     * @access private
     * <AUTHOR>
     * @return int Config ID (or true if nothing to do)
     */
    public function prvInsertOwnUserdataConfigRecord()
    {
        // You MUST override this method if your product component has a config table
        return true;
    }

    /**
     * Removes (or unsets) userdata config table and associated records
     *
     * @access private
     * <AUTHOR>
     * @return bool Success or Failure (true if nothing to do)
     */
    public function prvRemoveUserdataConfigRecord()
    {
        // Check whether there is an active config record
        if (isset($this->m_intConfigID) && $this->m_intConfigID > 0) {
            $this->prvEndCurrentUserdataConfigRecord();
        }

        return $this->prvRemoveOwnConfigRecords();
    }

    /**
     * Removes any associated records with the product component
     *
     * @access private
     * <AUTHOR>
     * @return bool Success or Failure (true if nothing to do)
     */
    public function prvRemoveOwnConfigRecords()
    {
        // You MUST override this method if the product component has additional table records that are associated
        // outside of the config table
        return true;
    }

    /**
     * Set the end date of the config record
     *
     * @access private
     * <AUTHOR>
     * @return bool Success or Failure
     */
    public function prvEndCurrentUserdataConfigRecord()
    {
        if ($this->prvHasValidUserdataConfigTable() && $this->m_intConfigID > 0) {
            $dbhConnection = get_named_connection_with_db('userdata');

            $strQuery = "UPDATE {$this->m_strUserdataConfigTable} " .
                ' SET dtmEnd = NOW() ' .
                " WHERE intConfigID = '{$this->m_intConfigID}'";

            PrimitivesQueryOrExit($strQuery, $dbhConnection);
        } // end if

        return false;
    }

    /**
     * Checks whether the product component instance has a config table and it is valid
     *
     * @access private
     * <AUTHOR>
     * @return bool
     */
    public function prvHasValidUserdataConfigTable()
    {
        if (isset($this->m_strUserdataConfigTable) && strlen($this->m_strUserdataConfigTable) > 0) {
            $dbhConnection = get_named_connection_with_db('userdata');

            $bolTableExists = PrimitivesTableExists($dbhConnection, $this->m_strUserdataConfigTable);

            if (isset($bolTableExists) && $bolTableExists === true) {
                return true;
            }
        } // end if

        return false;
    }

    /**
     * Returns the Event logger object, creating it the first time it is used
     *
     * @access protected
     * <AUTHOR>
     * @return CProductComponentEvent
     */
    public function prvGetEventLogger()
    {
        if ($this->m_objEventLogger == null) {
            $this->m_objEventLogger = new CProductComponentEvent($this->m_intProductComponentInstanceID);
        }

        return $this->m_objEventLogger;
    }

    /**
     *  Returns the boolean value to craete 0 schedule payment
     * Child class override this method eg CWlrLineRent
     *
     * @access   public
     * <AUTHOR> Zaki" <<EMAIL>>
     *
     * @return boolean false
     */
    public function displaySplitCharge()
    {
        return false;
    }

    /**
     * Determines which product components have a tariff value for a given service component ID.  Caches data
     * Returns either an array (which *can* be empty) or boolean false if an error occurs
     * Should be static, but this can cause problems with the (legacy) classes which inherit from this class...
     *
     * @param int $intServiceComponentId the service component id
     *
     * @return array
     */
    public function getChargeableProductComponentsFromServiceComponent($intServiceComponentId)
    {
        static $dataCache = array();

        $intServiceComponentId = (int)$intServiceComponentId;

        if (!in_array($intServiceComponentId, $dataCache)) {
            $query = <<<EOQ
SELECT
    DISTINCT pc.vchHandle
FROM
    dbProductComponents.tblProductComponent                     AS pc
    INNER JOIN dbProductComponents.tblProductComponentConfig    AS pcc
        ON pcc.intProductComponentID = pc.intProductComponentID
        AND pcc.dtmEnd IS null
    INNER JOIN dbProductComponents.tblTariff                    AS t
        ON t.intProductComponentConfigID = pcc.intProductComponentConfigID
        AND t.intCostIncVatPence > 0
        AND t.dtmEnd IS null
    INNER JOIN products.tblServiceComponentProduct              AS scp
        ON scp.intServiceComponentProductID = pcc.intServiceComponentProductID
WHERE
    scp.intServiceComponentID = $intServiceComponentId
EOQ;

            $conn = get_named_connection_with_db('product_reporting');
            $resResult = PrimitivesQueryOrExit($query, $conn, __METHOD__, false);

            $dataCache[$intServiceComponentId] = ($resResult ? PrimitivesResultsAsListGet($resResult) : false);
        }

        return $dataCache[$intServiceComponentId];
    }

    /**
     * Returns an array of chargeable components from service id
     *
     * @param integer $intServiceId                            Service id
     * @param array   $arrServiceComponentTypeHandlesToExclude An array of product component handles that
     *                                                         we don't want to include.
     *
     * @return array of chargeable components
     */
    public static function getChargeableComponentsFromServiceId(
        $intServiceId,
        array $arrServiceComponentTypeHandlesToExclude = array()
    ) {
        self::includeLegacyFiles();

        $dbhConnection = get_named_connection_with_db("userdata_reporting");

        $strComponentTypeHandlesToExcludeSql = '';

        if (count($arrServiceComponentTypeHandlesToExclude) > 0) {
            foreach ($arrServiceComponentTypeHandlesToExclude as $intKey => $strHandle) {
                $arrServiceComponentTypeHandlesToExclude[$intKey] = "'$strHandle'";
            }

            $strComponentTypeHandlesToExcludeSql = implode(',', $arrServiceComponentTypeHandlesToExclude);

            $strComponentTypeHandlesToExcludeSql = " AND scpt.vchHandle NOT IN ($strComponentTypeHandlesToExcludeSql) ";
        }

        $strQuery = "
        (
            SELECT c.component_id,
                   c.component_type_id,
                   c.status,
                   c.description,
                   pc.intProductComponentID,
                   pc.vchDisplayName AS strChargeableComponentType,
                   sc.name AS strChargeableComponentName,
                   pc.vchHandle,
                   CASE WHEN stafft.intTariffID IS NOT NULL
                      THEN stafft.intTariffID
                      ELSE t.intTariffID
                   END AS intTariffID,
                   CASE WHEN stafft.intTariffID IS NOT NULL
                      THEN sum(stafft.intCostIncVatPence/100)
                      ELSE sum(t.intCostIncVatPence/100) 
                   END AS decChargeableComponentPrice,
                   pf.vchDisplayName AS strChargeableComponentPaymentFrequency,
                   0 AS bolLegacyComponent,
                   scpt.vchHandle as vchComponentTypeHandle
              FROM userdata.tblProductComponentInstance pci
        INNER JOIN userdata.components c
                ON pci.intComponentId = c.component_id
        INNER JOIN dbProductComponents.tblProductComponent pc
                ON pci.intProductComponentId = pc.intProductComponentId
        INNER JOIN dbProductComponents.tblTariff t
                ON pci.intTariffId = t.intTariffId
        INNER JOIN dbProductComponents.tblPaymentFrequency pf
                ON t.intPaymentFrequencyID = pf.intPaymentFrequencyID
        INNER JOIN products.service_components sc
                ON c.component_type_id = sc.service_component_id
        INNER JOIN products.tblServiceComponentProduct scp
                ON scp.intServiceComponentID = sc.service_component_id
        INNER JOIN products.tblServiceComponentProductType scpt
                ON scp.intServiceComponentProductTypeID = scpt.intServiceComponentProductTypeID
               AND c.service_id = '{$intServiceId}'
               AND t.intCostIncVatPence > 0
               AND pci.dtmEnd IS null
               AND c.status != 'destroyed'
               AND pci.intStatusID != 9
        $strComponentTypeHandlesToExcludeSql
         LEFT JOIN userdata.components staffc
                ON staffc.service_id = c.service_id
               AND staffc.component_type_id = 855
               AND staffc.status = 'active'
               AND scpt.vchHandle = 'WLR'             
               AND pci.intProductComponentId = 1
         LEFT JOIN userdata.tblProductComponentInstance staffpci
                ON staffpci.intComponentID = staffc.component_id
               AND staffpci.intProductComponentId = " . PRODUCT_COMPONENT_FREE_STAFF_LINE_RENTAL . "
         LEFT JOIN dbProductComponents.tblTariff stafft
                ON stafft.intTariffID = staffpci.intTariffID 
          GROUP BY c.component_id,
                   c.status,
                   pc.intProductComponentID,
                   pc.vchDisplayName,
                   sc.name,
                   pc.vchHandle,
                   t.intTariffID,
                   pf.vchDisplayName,
                   c.component_type_id,
                   c.description,
                   scpt.vchHandle
        )
        UNION ALL
        (
            SELECT c.component_id,
                   c.component_type_id,
                   c.status,
                   c.description,
                   null AS intProductComponentID,
                   'Subscription' AS strChargeableComponentType,
                   sc.name AS strChargeableComponentName,
                   null AS vchHandle,
                   null AS intTariffID,
                   gross_monthly_charge AS decChargeableComponentPrice,
                   'Monthly' AS strChargeableComponentPaymentFrequency,
                   1 AS bolLegacyComponent,
                   null as vchComponentTypeHandle
              FROM userdata.components c
        INNER JOIN products.service_components sc
                ON sc.service_component_id = c.component_type_id
        INNER JOIN products.service_component_charges scc
                ON scc.service_component_id = sc.service_component_id
             WHERE c.service_id = '{$intServiceId}'
               AND scc.component_status = c.status
               AND c.status != 'destroyed'
        )";

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection, __METHOD__, false);
        $arrChargeableComponents = PrimitivesResultsAsArrayGet($resResult);
        $arrService = userdata_service_get($intServiceId);
        // if the user has a pound domain component, they may have opted out of paying for it
        foreach ($arrChargeableComponents as $key => $arrChargeableComponent) {
            if ($arrChargeableComponent['component_type_id'] == COMPONENT_POUND_CO_UK_DOMAIN) {
                if (userdata_pound_domain_decision($intServiceId) == 'no') {
                    unset($arrChargeableComponents[$key]);
                }
            }
            if ($arrChargeableComponent['strChargeableComponentName'] == 'Enhanced Care' &&
                $arrChargeableComponent['bolLegacyComponent'] == 1 && $arrService['isp'] == 'partner'
            ) {
                unset($arrChargeableComponents[$key]);
            }
            // If we've got a non-dd surcharge component, remove it here from the chargeable components
            // (as these charges no longer apply - SALES-1801)
            if ($arrChargeableComponent['component_type_id'] == COMPONENT_NON_DD_SURCHARGE) {
                unset($arrChargeableComponents[$key]);
            }
            // If there is a pending LRS contract replace the queued-active Monthly Line Rental with the LRS price
            if ($arrChargeableComponent['strChargeableComponentType'] == "Subscription"
                && $arrChargeableComponent['vchComponentTypeHandle'] == "WLR"
                && $arrChargeableComponent['status'] == 'queued-activate'
            ) {
                $componentId = $arrChargeableComponent['component_id'];
                $strLrsQuery = "select c.component_id,
                                    c.component_type_id,
                                    c.status,
                                    c.description,
                                    pc.intProductComponentID,
                                    pc.vchDisplayName AS strChargeableComponentType,
                                    sc.name AS strChargeableComponentName,
                                    pc.vchHandle,
                                    t.intTariffID,
                                    sum(t.intCostIncVatPence/100) AS decChargeableComponentPrice,
                                    pf.vchDisplayName AS strChargeableComponentPaymentFrequency,
                                    0 AS bolLegacyComponent,
                                    scpt.vchHandle as vchComponentTypeHandle
                                from userdata.tblProductComponentInstance as pci
                                inner join dbProductComponents.tblProductComponent as pcc
                                  ON pcc.vchHandle = 'LINE_RENTAL_SAVER'  
                                  and pcc.intProductComponentID = pci.intProductComponentID
                                inner join userdata.tblPendingPrepaidContract as ppc
                                  ON pci.intProductComponentInstanceID = ppc.intProductComponentInstanceID
                                inner join dbProductComponents.tblTariff as t
                                  ON t.intTariffID = ppc.intTariffID
                                INNER JOIN userdata.components c
                                  ON pci.intComponentId = c.component_id
                                INNER JOIN dbProductComponents.tblProductComponent pc
                                  ON pci.intProductComponentId = pc.intProductComponentId
                                INNER JOIN products.service_components sc
                                  ON c.component_type_id = sc.service_component_id
                                INNER JOIN products.tblServiceComponentProduct scp
                                  ON scp.intServiceComponentID = sc.service_component_id
                                INNER JOIN products.tblServiceComponentProductType scpt
                                  ON scp.intServiceComponentProductTypeID = scpt.intServiceComponentProductTypeID
                                INNER JOIN dbProductComponents.tblPaymentFrequency pf
                                  ON t.intPaymentFrequencyID = pf.intPaymentFrequencyID
                                  AND pci.dtmEnd IS null
                                  AND c.status != 'destroyed'
                                WHERE pci.intComponentId = {$componentId}
                                GROUP BY c.component_id,
                                         c.status,
                                         pc.intProductComponentID,
                                         pc.vchDisplayName,
                                         sc.name,
                                         pc.vchHandle,
                                         t.intTariffID,
                                         pf.vchDisplayName,
                                         c.component_type_id,
                                         c.description,
                                         scpt.vchHandle";
                $resLRSDetailsResult = PrimitivesQueryOrExit($strLrsQuery, $dbhConnection, __METHOD__, false);
                $arrPendingLRSComponents = PrimitivesResultsAsArrayGet($resLRSDetailsResult);
                if (sizeof($arrPendingLRSComponents) > 0) {
                    $arrChargeableComponents[$key] = $arrPendingLRSComponents[0];
                }
            }
        }

        return $arrChargeableComponents;
    }

    /**
     * Set the current tariff
     *
     * @param int $tariffId tariffId integer The tariff
     *
     * @return void
     */
    protected function updateTariff($tariffId)
    {
        $dbhConn = get_named_connection_with_db('userdata');

        $strQuery = 'UPDATE tblProductComponentInstance ' .
            "   SET intTariffID = '{$tariffId}' " .
            " WHERE intProductComponentInstanceID = '{$this->m_intProductComponentInstanceID}'";

        PrimitivesQueryOrExit($strQuery, $dbhConn, 'Update product component instance with new tariff');

        $this->m_intTariffID = $tariffId;
    }


    /**
     * Should payments for this product component be set up when the contract is renewed?
     * @return bool
     */
    protected function shouldGenerateScheduledPaymentsForContract()
    {
        return true;
    }

    /**
     * To include LegacyCodebase files
     *
     * @return void
     */
    protected function requireLegacyFiles()
    {
        require_once '/local/www/database-admin/scripts/financial/CBillingMetronetSpecific.inc';
    }

    /**
     * Function to cancel outstanding schedule payment
     * other than the schedule payment in the current for current billing period
     *
     * @return void
     */
    protected function cancelOutStandingScheduledPayments()
    {
        $scheduledPayments = $this->getScheduledPayments();

        if (empty($scheduledPayments)) {
            return;
        }

        foreach ($scheduledPayments as $schedulePaymentDetails) {
            $schedulePayment = new CScheduledPayment($schedulePaymentDetails['intScheduledPaymentID']);
            $schedulePayment->cancel();
        }
    }

    /**
     * Function to get the product component id and component id for the
     * user's service id
     *
     * @param serviceId $serviceId int
     *
     * @return arrContracts $arrContracts array
     */
    public static function getProductsByServiceId($serviceId)
    {
        self::includeLegacyFiles();

        $arrContracts = array();
        $strQuery = "SELECT
                intProductComponentID,
                intProductComponentInstanceId,
                C.component_type_id,
                C.component_id,
                scpt.vchHandle
                FROM
                  userdata.components C
                INNER JOIN userdata.tblProductComponentInstance PC
                  ON C.component_id=PC.intComponentID
                INNER JOIN products.service_components sc
                  ON sc.service_component_id = C.component_type_id
                INNER JOIN products.tblServiceComponentProduct scp
                  ON scp.intServiceComponentId = sc.service_component_id
                INNER JOIN products.tblServiceComponentProductType scpt
                  ON scpt.intServiceComponentProductTypeID = scp.intServiceComponentProductTypeID
                AND C.service_id='$serviceId'
                AND C.status='active'
                GROUP BY component_type_id
                ORDER BY intProductComponentInstanceId";

        $dbhConnection = get_named_connection_with_db('userdata');

        $resResult = PrimitivesQueryOrExit(
            $strQuery,
            $dbhConnection,
            'Get Products By Service'
        );
        if (false !== $resResult) {
            $arrContracts = PrimitivesResultsAsArrayGet($resResult);
        }

        return $arrContracts;
    }

    /**
     * Function to get the product component id of wlr call feature
     * @return void
     */
    public static function defineWlrCallProtect()
    {
        $dbhConnection = get_named_connection_with_db('dbProductComponents');
        $query = "SELECT intProductComponentID FROM tblProductComponent WHERE vchHandle = 'WlrCallProtect'";

        $queryResult = PrimitivesQueryOrExit(
            $query,
            $dbhConnection,
            'Get WLR Product Component ID'
        );

        $productComponentID = PrimitivesResultGet($queryResult, 'intProductComponentID');

        configDefineIfMissing('PRODUCT_COMPONENT_WLR_CALLPROTECT', $productComponentID);
    }

    /**
     * Check whether the Product Component Instance ID is One Time Charged
     *
     * @param  int $intProductComponentInstanceID Product Component Instance ID
     *
     * @return bool
     */
    public static function isOTCProductComponentInstanceId($intProductComponentInstanceID)
    {
        self::includeLegacyFiles();

        $dbhConnection = get_named_connection_with_db('userdata');

        $strQuery = " SELECT " .
            "    pf.vchHandle As vchHandle " .
            " FROM " .
            "    dbProductComponents.tblTariff as t" .
            " LEFT JOIN " .
            "    dbProductComponents.tblPaymentFrequency pf" .
            " ON " .
            "    t.intPaymentFrequencyId = pf.intPaymentFrequencyID" .
            " LEFT JOIN " .
            "    dbProductComponents.tblProductComponentConfig pcc" .
            " ON " .
            "    t.intProductComponentConfigID = pcc.intProductComponentConfigID " .
            " LEFT JOIN " .
            "    products.tblServiceComponentProduct scp " .
            " ON " .
            "    scp.intServiceComponentProductID = pcc.intServiceComponentProductID " .
            " LEFT JOIN " .
            "    userdata.components c " .
            " ON " .
            "    scp.intServiceComponentID = c.component_type_id " .
            " INNER JOIN " .
            "    userdata.tblProductComponentInstance pc " .
            " ON " .
            "    c.component_id = pc.intComponentID " .
            " WHERE " .
            "    pf.vchHandle = 'ONE_OFF' " .
            " AND " .
            "    pc.intProductComponentInstanceID = '$intProductComponentInstanceID'";

        $queryResult = PrimitivesQueryOrExit(
            $strQuery,
            $dbhConnection
        );

        if (PrimitivesNumRowsGet($queryResult) > 0) {
            return true;
        }

        return false;
    }
}
