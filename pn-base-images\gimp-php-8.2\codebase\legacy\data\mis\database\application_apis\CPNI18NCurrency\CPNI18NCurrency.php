<?php
/*
 * Created on Mar 2, 2005
 *
 * To change the template for this generated file go to
 * Window - Preferences - PHPeclipse - PHP - Code Templates
 */

/**
 * Currency Wrapper Object
 *
 * @package    Internationalisation
 * @access     public
 *
 * <AUTHOR>
 *
 * @version    $Id: CPNI18NCurrency.php,v 1.3 2005-06-03 17:28:06 nwood Exp $
 * @filesource
 *
 */

require_once(INCLUDE_PATH_CCURRENT_USER);
class CPNI18NCurrency
{
    // Member variables

    /**
     * The currency code of this instance
     *
     *
     * @access private
     * @var    str the standard 3 letter code e.g. GBP, EUR
     */
    var $m_strCurrencyCode = '';


    /**
     * The value of this instance
     *
     *
     * @access private
     * @var    float the actual float vaule of this currency variable (pounds
     * and pence or euros and cents)
     */
    var $m_floCurrencyValue = 0.0;

    // Constructor(s)

      /**
     * Constructor for the internationalised currency object
     *
     * Allows the creation of a currency object. No validation is currently
     * preformed so the requested currency code must exist.
     *
     * It should only be called bu the object factories 'CreateFromDivisorValue'
     * and 'CreateFromCurrencyAndDivisorValue'
     * <AUTHOR>
     * @access private
     *
     *
     * @param string strCurrencyCode, The international 3 letter code of
     *                                this  currency e.g. GBP,EUR,USD
     * @param float floCurrencyValue  The pounds and pence (or equivelent)
     *                                values  of this currency object
     *
     * @return none none
     * @throws none none
     *
     */

    function &CPNI18NCurrency($strCurrencyCode,$floCurrencyValue)
    {
        $this->m_strCurrencyCode  = $strCurrencyCode;
        $intDecimalPlaces = CPNI18NCurrency::GetDecimalPlaces($strCurrencyCode);

        $this->m_floCurrencyValue = round($floCurrencyValue,$intDecimalPlaces);

    } // func: CPNI18NCurrency

    /**
     * Return the divisor value for the currency i.e. pence in the pound
     *
     * Return the divisor value for the currency i.e. pence in the pound
     * Since Pounds, Euros and US Dollars are all 1:100 I can't imagine we'll
     * have to deal with any others soon but I want to allow for it.
     *
     * <AUTHOR>
     * @access public
     * @static
     *
     * @param string strCurrencyCode The international code of the currency your
     * interested in. If called as an instance method and without a currency
     * code it uses the instances currency code.
     *
     * @return ObjectReference a reference to the newly created object
     * @throws none none
     *
     */

    // TODO usage example for method  CreateFromDivisorValue

    function GetDivisorValue($strCurrencyCode = '')
    {
        $arrCurrencyCodes = &CPNI18NCurrency::GetStaticCurrencyCodes();
        if($strCurrencyCode !='')
        {
            if(isset($arrCurrencyCodes[$strCurrencyCode]['intDivisor']))
            {
                return $arrCurrencyCodes[$strCurrencyCode]['intDivisor'];
            }
            else
            {
                return 0;
            }
        }

        if(isset($arrCurrencyCodes[$this->m_strCurrencyCode]['intDivisor']))
        {
            return $arrCurrencyCodes[$this->m_strCurrencyCode]['intDivisor'];
        }

        return 0;

    } // func: GetDivisorValue

    /**
     * Return the decimal places for the currency e.g 2 for pounds
     *
     * Return the decimal places for the currency e.g 2 for pounds
     * Since Pounds, Euros and US Dollars are all 2 I can't imagine we'll
     * have to deal with any others soon but I want to allow for it.
     *
     * <AUTHOR>
     * @access public
     * @static
     *
     * @param string strCurrencyCode The international code of the currency your
     * interested in. If called as an instance method and without a currency
     * code it uses the instances currency code.
     *
     * @return ObjectReference a reference to the newly created object
     * @throws none none
     *
     */

    // TODO usage example for method  CreateFromDivisorValue

    function GetDecimalPlaces($strCurrencyCode = '')
    {
        $arrCurrencyCodes = &CPNI18NCurrency::GetStaticCurrencyCodes();
        if($strCurrencyCode !='')
        {
            if(isset($arrCurrencyCodes[$strCurrencyCode]['intDecimalPlaces']))
            {
                return $arrCurrencyCodes[$strCurrencyCode]['intDecimalPlaces'];
            }
            else
            {
                return 0;
            }
        }

        if(isset($arrCurrencyCodes[$this->m_strCurrencyCode]['intDecimalPlaces']))
        {
            return $arrCurrencyCodes[$this->m_strCurrencyCode]['intDecimalPlaces'];
        }

        return 0;

    } // func: GetDivisorValue

    /**
     * Get the value of this currency instance
     *
     * Get the value of this currency instance
     *
     *
     * <code>
     * </code>
     *
     * <AUTHOR>
     * @access public
     *
     * @return double the current float (pounds and pence) value of this object
     * @throws none none
     *
     */

    // TODO usage example for method GetValueAsFloat

    function GetValueAsFloat()
    {
            return $this->m_floCurrencyValue;
    } // func: GetValueAsFloat

    /**
     * Get the value as divisor e.g as pence for currency 'GBP'
     *
     * Get the value as divisor e.g as pence for currency 'GBP'
     *
     *
     * <code>
     * </code>
     *
     * <AUTHOR>
     * @access public
     *
     * @return integer the current integer (pence) value of this object
     * @throws none none
     *
     */

    // TODO usage example for method GetValueAsDivisor

    function GetValueAsDivisor()
    {
        $intDivisor= $this->GetDivisorValue();
        return (int) round($this->m_floCurrencyValue *$intDivisor,0);
    } // func: GetValueAsDivisor

    /**
     * Get the currency code for this currency instance
     *
     * Get the currency code for this currency instance
     *
     * <code>
     * </code>
     *
     * <AUTHOR>
     * @access public
     *
     * @return string the currency code of this object
     * @throws none none
     *
     */

    // TODO usage example for method GetCurrencyCode

    function GetCurrencyCode()
    {
            return $this->m_strCurrencyCode;
    } // func: GetCurrencyCode

    /**
     * Get the (localised) local currency name
     *
     * Get the currency name translating it using the
     * current user locale
     *
     * <code>
     * </code>
     *
     * <AUTHOR>
     * @access public
     *
     * @param string strMimeType the mime type of the display format to be used
     * 'text/html' for web pages, 'text/plain' for emails
     *
     * @return string The localised currency name
     * @throws none none
     *
     */

    // TODO usage example for method GetLocalCurrencyName

    function GetCurrencyLocalName($strMimeType='text/html',$strCurrencyCode='')
    {
            if($strCurrencyCode=='')
            {
                $strCurrencyCode = $this->m_strCurrencyCode;
            }

            // Let GetText take care of any  translations to be performed
            $arrCurrencyCodes = &CPNI18NCurrency::GetStaticCurrencyCodes();

            return _($arrCurrencyCodes[$strCurrencyCode]['arrLocalCurrencyName'][$strMimeType]);

    } // func: GetCurrencyLocalName

    function &GetStaticCurrencyCodes()
    {

    /**
     * List of currency codes
     *
     *
     * @access private
     * @var    array simple array of currency codes
     */
    static $arrCurrencyCodes = array(
        'GBP' => array('strCurrencyCode'           => 'GBP',
              'arrLocalCurrencyName'          => array('text/html'  => 'Pound',
                                                       'text/plain' => 'Pound'),
              'arrInternationalCurrencyName'   => array('text/html'  => 'British Pound',
                                                       'text/plain' => 'British Pound'),
              'arrCurrencySymbol'             => array('text/html'  => '&pound;',
                                                       'text/plain' => '�'),
              'intDivisor'                    => 100,
              'intDecimalPlaces'              => 2,
             'arrLocalDivisorName'           => array('text/html'  => 'Pence',
                                                      'text/plain' => 'Pence'),
             'arrInternationalDivisorName'   => array('text/html'  => 'New Pence',
                                                      'text/plain' => 'New Pence'),
             'arrDivisorSymbol'              => array('text/html'  => 'p',
                                                      'text/plain' => 'p'),
             'arrCountriesUsedBy'            => array('GBR')
         ),

       'EUR' => array('strCurrencyCode'               => 'EUR',
                      'arrLocalCurrencyName'          => array('text/html'  => 'Euro',
                                                               'text/plain' => 'Euro'),
                      'arrInternationalCurrencyName'  => array('text/html'  => 'Euro',
                                                               'text/plain' => 'Euro'),
                      'arrCurrencySymbol'             => array('text/html'  => '&euro;',
                                                               'text/plain' => 'euro'),
                      'intDivisor'                    => 100,
                      'intDecimalPlaces'              => 2,
                      'arrLocalDivisorName'           => array('text/html'  => 'Cent',
                                                               'text/plain' => 'Cent'),
                        'arrInternationalDivisorName'   => array('text/html'  => 'Cent',
                                                               'text/plain' => 'Cent'),
                        'arrDivisorSymbol'              => array('text/html'  => 'c',
                                                               'text/plain' => 'c'),
                        'arrCountriesUsedBy'            => array('DE')
         )
     );

        return $arrCurrencyCodes;
    } // func: GetStaticCurrencyCodes
    /**
     * Get the (localised) offical currency name
     *
     * Get the currency name translating it using the
     * current user locale
     *
     * <code>
     * </code>
     *
     * <AUTHOR>
     * @access public
     *
     * @param string strMimeType the mime type of the display format to be used
     * 'text/html' for web pages, 'text/plain' for emails
     *
     * @return string The localised offical currency name
     * @throws none none
     *
     */

    // TODO usage example for method GetOfficalCurrencyName

    function GetCurrencyOfficalName($strMimeType='text/html',$strCurrencyCode='')
    {
            if($strCurrencyCode=='')
            {
                $strCurrencyCode = $this->m_strCurrencyCode;
            }

            // Let GetText take care of any  translations to be performed
            $arrCurrencyCodes = &CPNI18NCurrency::GetStaticCurrencyCodes();
            return _($arrCurrencyCodes[$strCurrencyCode]['arrInternationalCurrencyName'][$strMimeType]);

    } // func: GetCurrencyOfficalName


    /**
     * Get the symbol for this currency
     *
     * Get the currency symbol translating it using the current user locale
     *
     * <code>
     * </code>
     *
     * <AUTHOR>
     * @access public
     *
     * @param string strMimeType the mime type of the display format to be used
     * 'text/html' for web pages, 'text/plain' for emails
     *
     * @return string The currency symbol
     * @throws none none
     *
     */

    // TODO usage example for method GetCurrencySymbol

      function GetCurrencySymbol($strMimeType='text/html',$strCurrencyCode='')
      {
              if($strCurrencyCode=='')
            {
                $strCurrencyCode = $this->m_strCurrencyCode;
            }

            // Let GetText take care of any  translations to be performed
            $arrCurrencyCodes = &CPNI18NCurrency::GetStaticCurrencyCodes();
            return _($arrCurrencyCodes[$strCurrencyCode]['arrCurrencySymbol'][$strMimeType]);

      } // func: GetCurrencySymbol



    /**
     * Get the symbol for this currency's divisor e.g. 'p' for GBP
     *
     * Get the currency's divisor symbol translating it using the current user
     * locale
     *
     * <code>
     * </code>
     *
     * <AUTHOR>
     * @access public
     *
     * @param string strMimeType the mime type of the display format to be used
     * 'text/html' for web pages, 'text/plain' for emails
     *
     * @return string The currency symbol
     * @throws none none
     *
     */

    // TODO usage example for method GetDivisorSymbol

      function GetDivisorSymbol($strMimeType='text/html',$strCurrencyCode='')
      {
              if($strCurrencyCode=='')
            {
                $strCurrencyCode = $this->m_strCurrencyCode;
            }

            // Let GetText take care of any  translations to be performed
            $arrCurrencyCodes = &CPNI18NCurrency::GetStaticCurrencyCodes();
            return _($arrCurrencyCodes[$strCurrencyCode]['arrDivisorSymbol'][$strMimeType]);

      } // func: GetDivisorSymbol


    /**
     *Get the (localised) local divisor name e.g. 'Pence' for 'GBP'
     *
     * Get the local divisor name translating it using the current user locale
     *
     * <code>
     * </code>
     *
     * <AUTHOR>
     * @access public
     *
     * @param string strMimeType the mime type of the display format to be used
     * 'text/html' for web pages, 'text/plain' for emails
     *
     * @return string The localised divisor name
     * @throws none none
     *
     */

    // TODO usage example for method GetDivisorLocalName

    function GetDivisorLocalName($strMimeType='text/html',$strCurrencyCode='')
    {
            if($strCurrencyCode=='')
            {
                $strCurrencyCode = $this->m_strCurrencyCode;
            }

            // Let GetText take care of any  translations to be performed
            $arrCurrencyCodes = &CPNI18NCurrency::GetStaticCurrencyCodes();

            return _($arrCurrencyCodes[$strCurrencyCode]['arrLocalDivisorName'][$strMimeType]);

    } // func: GetDivisorLocalName

    /**
     * Get the (localised) offical divisor name e.g. 'New Pence' for 'GBP'
     *
     * Get the offical divisor name translating it using the current user locale
     *
     * <code>
     * </code>
     *
     * <AUTHOR>
     * @access public
     *
     * @param string strMimeType the mime type of the display format to be used
     * 'text/html' for web pages, 'text/plain' for emails
     *
     * @return string The localised offical divisor name
     * @throws none none
     *
     */

    // TODO usage example for method GetOfficalCurrencyName

    function GetDivisorOfficalName($strMimeType='text/html',$strCurrencyCode='')
    {
            if($strCurrencyCode=='')
            {
                $strCurrencyCode = $this->m_strCurrencyCode;
            }

            // Let GetText take care of any  translations to be performed
            $arrCurrencyCodes = &CPNI18NCurrency::GetStaticCurrencyCodes();
            return _($arrCurrencyCodes[$strCurrencyCode]['arrInternationalDivisorName'][$strMimeType]);

    } // func: GetDivisorOfficalName

  /* expected functions in final version

    GetCurrencyCode()
      GetCurrencySymbol($strMimeType='text/html')
      GetDivisorSymbol($strMimeType='text/html')

      GetCurrencyPrintfString($bolIncludeCurrencySymbol = true, $intMinLengthBeforeDigits='',  $strMimeType='');
      GetDivisorPrintfString($bolIncludeCurrencySymbol = true, $intMinLength='', $strMimeType='');
  */


      // Static Methods

    /**
     * Get the numeric and monetary format parameters for all locales
     *
     * Get the numeric and monetary format parameters for all locales
     *
     * <code>
     * </code>
     *
     * <AUTHOR>
     * @access private
     * @static
     *
     * @return array structured array of locale, monetary format options,
     * numeric format options
     * @throws none none
     *
     */

    function GetParsedLocalesAndFormats()
    {
        static $arrParsedFormats = array();
        static $arrUnparsedFormats =array(
                                   array('strLocale' => 'en_UK.UTF-8',
                         'arrMonetaryFormat' => array('intDecimals' => 2,
                                                          'strDecimalPoint' => '.',
                                                              'strThousandsSeperator' => ',',
                                                              'bolCurrencySymbolPreceeds'=> true,
                                                                        'bolCurrencySymbolSeperatedBySpace' => false,
                                                                        'bolDivisorSymbolPreceeds'          => false,
                                                                        'bolDivisorSymbolSeperatedBySpace'  => false
                                                                     ),

                                         'arrNumericFormat'  => array('intDecimals' => 0,
                                                                      'strDecimalPoint' => '.',
                                                                      'strThousandsSeperator' => ','
                                                                     ),

                                         'arrDateAndTimeFormat' =>  array('strShortDate'          => 'd/M/Y',
                                                                 'strLongDate'           => 'D, dS of F Y',
                                                                 'strShortDateAndTime'   => 'H:m:s d/M/Y',
                                                                 'strLongDateAndTime'    => 'H:m:s D, dS of F Y',
                                                                 'strShortTime'          => 'H:m:s',
                                                                 'strLongTime'           => 'H:m:s',
                                                                     )

                                          ),
                                   array('strLocale' => 'de_DE.UTF-8',
                         'arrMonetaryFormat' => array('intDecimals' => 2,
                                                                        'strDecimalPoint' => '.',
                                                                      'strThousandsSeperator' => ',',
                                                                      'bolCurrencySymbolPreceeds'=> true,
                                                                        'bolCurrencySymbolSeperatedBySpace' => false,
                                                                        'bolDivisorSymbolPreceeds'          => false,
                                                                        'bolDivisorSymbolSeperatedBySpace'  => fals
                                                                     ),
                                         'arrNumericFormat'  => array('intDecimals' => 0,
                                                                      'strDecimalPoint' => '.',
                                                                      'strThousandsSeperator' => ','
                                                                     ),

                                         'arrDateAndTimeFormat' =>  array('strShortDate'          => 'd/M/Y',
                                                                 'strLongDate'           => 'D, dS of F Y',
                                                                 'strShortDateAndTime'   => 'H:m:s d/M/Y',
                                                                 'strLongDateAndTime'    => 'H:m:s D, dS of F Y',
                                                                 'strShortTime'          => 'H:m:s',
                                                                 'strLongTime'           => 'H:m:s',
                                                                     )


                                          )
                                     );

        if(count($arrParsedFormats) == 0)
        {
            foreach($arrUnparsedFormats as $arrUnparsedFormat)
            {
                $arrParsedLocale = CPage::ParseLocaleString($arrUnparsedFormat['strLocale']);
                $arrParsedFormats[] = array_merge($arrUnparsedFormat,$arrParsedLocale);
            }
        }

        return $arrParsedFormats;
    } // func: GetParsedLocalesAndFormats


    /**
     * Get an array of the format parameters for currency under this locale
     *
     * Get an array of the format parameters for currency under this locale
     *
     * <code>
     * </code>
     *
     * <AUTHOR>
     * @access private
     * @static
     *
     * @return array array formating configuration for the selected locale
     * @throws none none
     *
     */

      function GetLocalisedCurrencyFormatForLocale($strLocale)
      {
          $arrDefaultFormat =  array('intDecimals'                       => 2,
                                     'strDecimalPoint'                   => '.',
                                   'strThousandsSeperator'             => ',',
                                   'bolCurrencySymbolPreceeds'         => true,
                                     'bolCurrencySymbolSeperatedBySpace' => false,
                                     'bolDivisorSymbolPreceeds'          => false,
                                     'bolDivisorSymbolSeperatedBySpace'  => false
                                  );

          static $arrFormatForLocale = array();

return $arrDefaultFormat;
          if(isset($arrFormatForLocale[$strLocale]))
          {
              return $arrFormatForLocale[$strLocale];
          }

          // Parse
          // TODO move ParseLocaleString out of CPAGE
          $arrRequestedLocale = CPage::ParseLocaleString($strLocale);
          $arrParsedLocales = CPNI18NCurrency::GetParsedLocalesAndFormats();
          $strBestFitLocale = CPage::prvFindBestLocaleMatch($arrParsedLocales,
                                                            $arrRequestedLocale['strLanguage'],
                                                            $arrRequestedLocale['strCountry'],
                                                            $arrRequestedLocale['strCharset']);

          foreach($arrParsedLocales as $arrParsedLocale)
          {
              if($strBestFitLocale == $arrParsedLocale['strUnparsedLocale'])
              {
                  $arrFormatForLocale[$strLocale] = $arrParsedLocale['arrMonetaryFormat'];
                  return $arrFormatForLocale[$strLocale];
              }
          }

          return $arrDefaultFormat;

      } // func: GetLocalisedCurrencyFormatForLocale

    /**
     * Get an array of the format parameters for dates under this locale
     *
     * Get an array of the format parameters for dates under this locale
     *
     * <code>
     * </code>
     *
     * <AUTHOR>
     * @access private
     * @static
     *
     * @return array array formating configuration for the selected locale
     * @throws none none
     *
     */

      function GetLocalisedDateAndTimeFormatForLocale($strLocale)
      {
          $arrDefaultFormat =  array('strShortDate'          => 'd/M/Y',
                                         'strLongDate'           => 'D, dS of F Y',
                                         'strShortDateAndTime'   => 'H:m:s d/M/Y',
                                         'strLongDateAndTime'    => 'H:m:s d/M/Y',
                                         'strShortTime'          => 'H:m:s',
                                         'strLongTime'           => 'H:m:s',
                                  );

          static $arrFormatForLocale = array();

return $arrDefaultFormat;

          if(isset($arrFormatForLocale[$strLocale]))
          {
              return $arrFormatForLocale[$strLocale];
          }

          // Parse
          // TODO move ParseLocaleString out of CPAGE
          $arrRequestedLocale = CPage::ParseLocaleString($strLocale);
          $arrParsedLocales = CPNI18NCurrency::GetParsedLocalesAndFormats();
          $strBestFitLocale = CPage::prvFindBestLocaleMatch($arrParsedLocales,
                                                            $arrRequestedLocale['strLanguage'],
                                                            $arrRequestedLocale['strCountry'],
                                                            $arrRequestedLocale['strCharset']);

          foreach($arrParsedLocales as $arrParsedLocale)
          {
              if($strBestFitLocale == $arrParsedLocale['strUnparsedLocale'])
              {
                  $arrFormatForLocale[$strLocale] = $arrParsedLocale['arrDateAndTimeFormat'];
                  return $arrFormatForLocale[$strLocale];
              }
          }

          return $arrDefaultFormat;

      } // func: GetLocalisedNumberFormatForLocale

    /**
     * Get an array of the format parameters for numbers under this locale
     *
     * Get an array of the format parameters for numbers under this locale
     *
     * <code>
     * </code>
     *
     * <AUTHOR>
     * @access private
     * @static
     *
     * @return array array formating configuration for the selected locale
     * @throws none none
     *
     */

        function GetLocalisedNumberFormatForLocale($strLocale)
        {
        $arrDefaultFormat =  array('intDecimals' => 0,
                                   'strDecimalPoint' => '.',
                                   'strThousandsSeperator' => ','
                                  );

          static $arrFormatForLocale = array();

return $arrDefaultFormat;
          if(isset($arrFormatForLocale[$strLocale]))
          {
              return $arrFormatForLocale[$strLocale];
          }

          // Parse
          // TODO move ParseLocaleString out of CPAGE
          $arrRequestedLocale = CPage::ParseLocaleString($strLocale);
          $arrParsedLocales = CPNI18NCurrency::GetParsedLocalesAndFormats();
          $strBestFitLocale = CPage::prvFindBestLocaleMatch($arrParsedLocales,
                                                            $arrRequestedLocale['strLanguage'],
                                                            $arrRequestedLocale['strCountry'],
                                                            $arrRequestedLocale['strCharset']);

          foreach($arrParsedLocales as $arrParsedLocale)
          {
              if($strBestFitLocale == $arrParsedLocale['strUnparsedLocale'])
              {
                  $arrFormatForLocale[$strLocale] = $arrParsedLocale['arrNumericFormat'];
                  return $arrFormatForLocale[$strLocale];
              }
          }

          return $arrDefaultFormat;

      } // func: GetLocalisedNumberFormatForLocale

    /**
     * Get a simple list of all supported currency codes
     *
     * Get a simple list of all supported currency codes
     *
     * <code>
     * </code>
     *
     * <AUTHOR>
     * @access public
     * @static
     *
     * @return array simple list of all supported currency codes
     * @throws none none
     *
     */

     function GetAllCurrencyCodes()
     {
         static $arrJustCodes = array();

         if(count($arrJustCodes) < 1)
         {
             $arrCurrencyCodes = &CPNI18NCurrency::GetStaticCurrencyCodes();

             foreach($arrCurrencyCodes as $index => $arrDetails)
             {
                 $arrJustCodes[$arrDetails['strCurrencyCode']] = $arrDetails['strCurrencyCode'];
             }
         }

         return $arrJustCodes;

     } // func: GetAllCurrencyCodes

    /**
     * Return a currency object intalising the intial value in pence,cents etc
     *
     * Return a currency object intalising the intial value in pence,cents etc
     *
     * <AUTHOR>
     * @access public
     *
     * @return ObjectReference a reference to the newly created object
     * @throws none none
     *
     */

    // TODO usage example for method  CreateFromDivisorValue

    function CreateFromDivisorValue($strCurrencyCode,$intPenceValue)
    {

        $intPenceValue    = round($intPenceValue,0);
        $intDivisor       = CPNI18NCurrency::GetDivisorValue($strCurrencyCode);

         $objCurrency = new CPNI18NCurrency($strCurrencyCode,$intPenceValue/$intDivisor);

         return $objCurrency;

    } // func: CreateFromDivisorValue
    /**
     * Get a short date string for a date under this locale
     *
     * Get a short date string for a date under this locale
     *
     * <code>
     * </code>
     *
     * <AUTHOR>
     * @access private
     * @static
     *
     * @return string
     * @throws none none
     *
     */

    function GetShortDate($uxtDate)
    {
        // Hunt for localised monetary or localised number format tokens
        $strLocale = CCurrentUser::GetPreferance('LOCALE');

        $arrDateAndTimeFormats = CPNI18NCurrency::GetLocalisedDateAndTimeFormatForLocale($strLocale);
        return date($arrDateAndTimeFormats['strShortDate'],$uxtDate);

    } // func: GetShortDate

      /**
     * Format in sprintf style with I18N  number and currency support
     *
     * This function is a wrapper for the sprintf function which added two new
     * format type '%m' and '%n'.
     *
     * '%m' formats floating point numbers as currency according to the locale's
     * currency format. Currency symbols/codes are NOT added. The default number
     * of desimal places is equal to the local currency '%.{decimal_places}m'
     * can be used to override this to display rounded or other currency values.
     * Padding is not currently supported.
     *
     * '%n' formats floating point numbers as currency according to the locale's
     * numeric format.  The default number of desimal places is equal to the
     * local currency '%.{decimal_places}n' can be used to override this to
     * display rounded values. Padding is not currently supported.
     *
     * N.B. numbers formated in this way may contain non-numeric values such as
     * ",'!" and should not be inserted into the database. This functuon FORMATS
     * FOR DISPLAY ONLY
     *
     * <AUTHOR>
     * @access public
     * @static
     *
     * @param string $strFormat, the sprintf format to use
     * @param mixed  like sprintf this function takes a variable number of
     * parameters 2..n
     *
     * @return string the resulant internationalised string
     * @throws none none
     *
     */

    // TODO create usage example for CPNI18NCurrency::I18Nsprintf

      function I18Nsprintf($strFormat)
    {
        $arrArgs = func_get_args();
        array_shift($arrArgs); // First arg is format

        $strFormatTokenParser="'%([1-9]+\\$)*([^%bcdeufFosxXmn])*([%bcdeufFosxXmn])'";
        $arrLocalisedTokens = array();
        $intNumTokens = preg_match_all($strFormatTokenParser,$strFormat,$arrLocalisedTokens,PREG_SET_ORDER);

        // Hunt for localised monetary or localised number format tokens
        $strLocale = CCurrentUser::GetPreferance('LOCALE');

        $arrLocalisedNumberFormat    = CPNI18NCurrency::GetLocalisedNumberFormatForLocale($strLocale);
        $arrLocalisedCurrencyFormat  = CPNI18NCurrency::GetLocalisedCurrencyFormatForLocale($strLocale);

        for($intIndex = 0; $intIndex < $intNumTokens; $intIndex++)
        {
            $intParameterIndex =$intIndex;

            // Deal with parameter order changes
            if(isset($arrLocalisedTokens[$intIndex][1]) && $arrLocalisedTokens[$intIndex][1] != '')
            {
                $arrLocalisedTokens[$intIndex][1] = preg_replace('%\$%','',$arrLocalisedTokens[$intIndex][1]);
                $intParameterIndex =  $arrLocalisedTokens[$intIndex][1]-1;
            }

            $strSelectedParameter = $arrArgs[$intParameterIndex];

            switch($arrLocalisedTokens[$intIndex][3])
            {
                case 'm':
                    // TODO add support for rounding specifications e.g. %.5m

                    $intDecimalPlaces = CPNI18NCurrency::prvSelectDecimalPlaces($strSelectedParameter,
                                                                            $arrLocalisedTokens[$intIndex][2],
                                                                            $arrLocalisedCurrencyFormat);

                    $arrArgs[$intParameterIndex] = number_format($strSelectedParameter,
                                        $intDecimalPlaces,
                                        $arrLocalisedCurrencyFormat['strDecimalPoint'],
                                        $arrLocalisedCurrencyFormat['strThousandsSeperator']);

                    $strSearch = "\"{$arrLocalisedTokens[$intIndex][0]}\"";

                    $strSearch = str_replace('?','\?',"\"{$arrLocalisedTokens[$intIndex][0]}\"");

                    $strFormat = preg_replace($strSearch,'%s',$strFormat,1);
                    break;

                case 'n':
                    // TODO add support for rounding specifications e.g. %.5n

                    $intDecimalPlaces = CPNI18NCurrency::prvSelectDecimalPlaces($strSelectedParameter,
                                                                            $arrLocalisedTokens[$intIndex][2],
                                                                            $arrLocalisedNumberFormat);

                    $arrArgs[$intParameterIndex] = number_format($strSelectedParameter,
                                        $intDecimalPlaces,
                                        $arrLocalisedNumberFormat['strDecimalPoint'],
                                        $arrLocalisedNumberFormat['strThousandsSeperator']);



                    $strSearch = "\"{$arrLocalisedTokens[$intIndex][0]}\"";

                    $strSearch = str_replace('?','\?',"\"{$arrLocalisedTokens[$intIndex][0]}\"");

                    $strFormat = preg_replace($strSearch,'%s',$strFormat,1);
                    break;

                default:
            } // token type
        }

        // build parameter list from transformed array
        $strArgumentList = '';

        foreach($arrArgs as $strArg)
        {
            $strArgumentList .= ",'$strArg'";
        }

        // Perform the sprintf on the transformed array
        $strCommand = "\$strResult =  sprintf('$strFormat'$strArgumentList);";
        $strResult='';
        eval($strCommand);

        return $strResult;

    } // func: I18Nsprintf

    function prvSelectDecimalPlaces($floValue,$strFormatAdjusters,$arrLocalisedFormat)
    {

        $intDecimalPlaces = $arrLocalisedFormat['intDecimals'];
        $arrDecimals =array();

        if(preg_match('%^([\?0-9]+)$%',$strFormatAdjusters,$arrDecimals))
        {
            $strDecimals = $arrDecimals[1];

            if($strDecimals=='?')
            {
                $strFormatAdjusters = (float) $floValue;

                $intDecimalPlaces = strlen((((float) $floValue) - floor($floValue))) -2;

            }
            else
            {
                $intDecimalPlaces =  $strDecimals;
            }
        }
         return $intDecimalPlaces;
    } //  func: prvSelectDecimalPlaces

    // Getters

    // Setters

} // class: CPNI18NCurrency

?>
