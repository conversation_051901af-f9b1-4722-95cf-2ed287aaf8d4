<?php
/**
 * Webmail product component
 *
 * @package components.MAAF
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @version $Id: Webmail.class.php,v 1.11 2009-04-08 14:31:06 ablagnys Exp $
 */

/**
 * Needed requirements
 */
require_once '/local/data/mis/database/database_libraries/components/CProduct.inc';
require_once '/local/data/mis/database/database_libraries/components/MAAF/FreeDomainName.class.php';

require_once '/local/data/mis/database/class_libraries/Brightview/BrightViewMail.class.php';
require_once '/local/data/mis/database/class_libraries/Brightview/BrightViewMailBox.class.php';
require_once '/local/data/mis/database/database_libraries/components/MAAF/BundledMailboxes.class.php';
require_once '/local/data/mis/database/database_libraries/components/MAAF/PackOf5Mailboxes.class.php';

require_once '/local/data/mis/database/application_apis/Mail/BrightviewApi.class.php';

require_once '/local/data/mis/database/database_libraries/components/Exception/InvalidSoulstoneEntryException.class.php';

/**
 * Main product class for webmail component
 *
 * <AUTHOR> <<EMAIL>>
 */
class MAAF_CProduct_Webmail extends CProduct
{
    const   COMPONENT_HANDLE = 'MAAF_WEBMAIL';
    const   COMPONENT_TYPE_ID = COMPONENT_MAAF_WEBMAIL;
    const   EMAIL_PATTERN = '/^[-0-9a-zA-Z._+&]+@([-0-9a-zA-Z]+[.])+[a-zA-Z]{2,6}$/';
    /**
     * Relaxed version of the email address checker
     * if some bodged address that will actually work has managed to get into our mail db
     * then this reg exp will allow things to keep functioning for it - i.e. updates to ironport will still work
     * currently the only modification is that it allows "_" in the domain
     */
    const   RELAXED_EMAIL_PATTERN = '/^[-0-9a-zA-Z._+&]+@([-0-9a-zA-Z_]+[.])+[a-zA-Z]{2,6}$/';
    /**
     * List of acceptable statuses
     *
     * @var array
     */
    protected static $arrStatuses = array('unconfigured', 'active', 'queued-activate', 'queued-reactivate',
                                          'deactive', 'queued-deactivate', 'queued-deconfigure',
                                          'queued-destroy', 'destroyed');
    /**
     * Database connection
     *
     * @var resource
     */
    protected $objMailBox = null;

    /**
     * Free domain name
     *
     * @var MAAF_Component_FreeDomainName
     */
    public $objFreeDomainName = null;

    /**
     * Mail API
     */
    protected $objMailApi;

    /**
     * Component details array
     *
     * @var array
     */
    protected $arrComponentDetails = array();

    /**
     * Array for singleton representation of cproduct instances
     *
     * @var array
     */
    protected static $arrCProductInstance = array();

    /**
     * Main mailbox
     *
     * @var string
     */
    protected $strMainMailbox = null;

    /**
     * arrMailboxMBQuotas - array of different mailbox
     * quitas in MB for each component type id
     *
     * @var array
     */
    protected $arrMailboxMBQuotas = array(
                                            COMPONENT_MAAF_WEBMAIL => 100,
                                            COMPONENT_WAITROSE_WEBMAIL => 50,
                                            COMPONENT_GREENBEE_WEBMAIL => 100
                                          );

    /**
     * Holds the information related to the service this component belongs to
     *
     * @var array
     */
    private $userdata = array();

    /**
     * Object constructor. Singleton implementation
     *
     * <AUTHOR> Bargiel <<EMAIL>>
     *
     * @param integer   $intComponentID Component Id
     *
     * @throws Exception
     */
    protected function __construct($intComponentID)
    {
        parent::__construct($intComponentID);

        // Building mailbox object
        $arrUserdata = $this->userdata = userdata_service_get($this->getServiceID());
        $this->objMailApi = Mail_BrightviewApi::fromServiceArray($arrUserdata);

        // If the service status is 'queued-destroy', then the business actor entries
        // will be set as inactive, so we will not be able to fetch the realm.
        $bolIsActive = $arrUserdata['status'] == 'queued-destroy' ? false : true;

        $strRealm = userdataGetFrameworkAuthRealm($this->getServiceID(), $arrUserdata['username'], $bolIsActive);

        if(!$strRealm)
        {
            $arrApplication = get_adsl_application_details(ADSLGetInstallDiaryServiceId($this->getServiceID()));
            $strRealm = $arrApplication ? $arrApplication['strRealmProvisioned'] : NULL;
        }

        if(!$strRealm && $arrUserdata['status'] != 'queued-destroy') {
            throw new Exception('Could not retrieve Realm for service id: '.$this->getServiceID().' and username: '.$arrUserdata['username']);
        }

        $this->objMailBox = BrightViewMail::getMail($arrUserdata['username'], $strRealm);

        try {
            // check that the component is created correctly by making sure it has a master mailbox
            $this->validateMasterMailbox();
        } catch (InvalidSoulstoneEntryException $e) {
            if ($arrUserdata['status'] != 'queued-destroy') {
                // Problem: 63885 If the service is 'queued-destroy', then we do not need a valid Soulstone entry.
                throw $e;
            }
        }

        $this->objFreeDomainName = MAAF_Component_FreeDomainName::getComponentByServiceId($this->getServiceID());
    }

    /**
     * getMailboxQuota returns Mailbox quota in MB
     *
     * @return Integer
     */
    private function getMailboxQuota()
    {
        return $this->arrMailboxMBQuotas[$this->getComponentTypeID()];
    }

    /**
     * Method gets mailbox based on multipop id
     *
     * <AUTHOR> Bargiel <<EMAIL>>
     *
     * @param $intMultipopId
     *
     * @return BrightViewMailBox
     */
    public static function getMailBox($intMultipopId)
    {
        return BrightViewMail::getMailBoxDetails($intMultipopId);
    } // end of method getMailBox()

    /**
     * Method edits existing mailbox
     *
     * <AUTHOR> Bargiel <<EMAIL>>
     *
     * @param BrightViewMailBox $objMailbox     Mailbox object
     * @param string            $strNewAlias    New mailbox alias
     * @param string            $strNewPassword New mailbox password
     *
     * @throws Exception
     *
     * @return array
     */
    public function editMailbox(BrightViewMailBox $objMailbox, $strNewPassword)
    {
        $arrErrors = array();

        // Modify password
        if (!empty($strNewPassword)) {
            if (!$this->isPasswordValid($strNewPassword)) {
                $arrErrors['strPassword'] = 'invalid';
            } else {
                $authdb = get_named_connection_with_db('mailauth');
                PrimitivesQueryOrExit("BEGIN", $authdb);

                $arrDomains = array();
                if($this->objFreeDomainName->getDomainName()) $arrDomains[] = $this->objFreeDomainName->getDomainName();

                if( $objMailbox->getMultiPopId() == 0  || $objMailbox->getMultiPopId() == '' ) {
                    $alias = 'bv.master';
                    $arrDomains[] = $this->objMailBox->getRealm();
                } else {
                    $alias = $objMailbox->getAlias();
                }

                $this->objMailApi->setMailboxPassword(
                                                   new String($alias),
                                                   new String(crypt($strNewPassword)),
                                                   $arrDomains
                                                     );


                if($objMailbox->changePassword($strNewPassword)) {
                    PrimitivesQueryOrExit("COMMIT", $authdb);
                } else {
                    PrimitivesQueryOrExit("ROLLBACK", $authdb);
                }

                $arrErrors['strPassword'] = 'valid';
            }
        }

        // Return errors (is any)
        return $arrErrors;

    } // end of editMailbox()

    /**
     * Function returns total number of mailboxes customer can have
     *
     * <AUTHOR> Bargiel <<EMAIL>>
     *
     * @throws Exception
     *
     * @return integer
     */
    public function getNumberOfTotalMailboxes()
    {
        $intTotalNumberOfMailboxes  = MAAF_CProductComponent_BundledMailboxes::MAILBOXES_NUMBER;
        $intTotalNumberOfMailboxes += MAAF_CProductComponent_PackOf5Mailboxes::MAILBOXES_NUMBER *
                                      MAAF_CProductComponent_PackOf5Mailboxes::getNumberOfInstances($this->getComponentID());

        return $intTotalNumberOfMailboxes;

    } // end of function getNumberOfTotalMailboxes()

    /**
     * Singleton method gets component based on component id
     *
     * <AUTHOR> Bargiel <<EMAIL>>
     *
     * @param integer $intComponentInstance
     *
     * @return MAAF_CProduct_Webmail
     */
    public static function getCProduct($intCProductInstance)
    {
        if (isset(self::$arrCProductInstance[$intCProductInstance]) &&
            self::$arrCProductInstance[$intCProductInstance] instanceof MAAF_CProduct_Webmail) {
            return self::$arrCProductInstance[$intCProductInstance];
        }

        $objTempInstance = new MAAF_CProduct_Webmail($intCProductInstance);

        if ($objTempInstance->getComponentID()=='') {
            throw new Exception("CProduct $intCProductInstance has not been found");
        }

        $objTempInstance->validateMasterMailbox();

        self::$arrCProductInstance[$intCProductInstance] = $objTempInstance;

        return self::$arrCProductInstance[$intCProductInstance];

    } // end of method getCProduct()

    /**
     * Method returns master mailbox name
     *
     * <AUTHOR> Bargiel <<EMAIL>>
     *
     * @throws Exception
     *
     * @return string
     */
    public function getMasterMailbox()
    {
        $this->validateMasterMailbox();

        return $this->objMailBox->getUserName().'@'.$this->objMailBox->getRealm();

    } // end of method getMasterMailbox()

    /**
     * Method returns list of mailboxes registered on free domain
     *
     * <AUTHOR> Bargiel <<EMAIL>>
     *
     * @throws Exception
     *
     * @return array
     */
    public function getListOfMailboxes()
    {
        $this->validateMasterMailbox();

        $arrMailboxes = array();
        while ($objMail = $this->objMailBox->read()) {

            $arrMailboxes[] = $objMail;
        }
        $this->objMailBox->reset();
        return $arrMailboxes;

    } //end of method getListOfMailboxes()

    /**
     * Method returns list of mailboxes registered on free domain
     * as associative array
     *
     * <AUTHOR> Sourkova <<EMAIL>>
     *
     * @throws Exception
     *
     * @return array
     */
    public function getListOfMailboxesAsAssoc($bolFlush = FALSE)
    {
        if(TRUE === $bolFlush) {
            $this->objMailBox = BrightViewMail::getMail($this->objMailBox->getUserName(), $this->objMailBox->getRealm());
        }

        $this->validateMasterMailbox();

        $arrMailboxes = array();
        while ($objMail = $this->objMailBox->read()) {
            $arrMailboxes[$objMail->getMultiPopId()] = $objMail->getAlias();
        }
        $this->objMailBox->reset();
        return $arrMailboxes;

    } //end of method getListOfMailboxesAsAssoc()

    /**
     * Method adds new mailbox
     *
     * <AUTHOR> Bargiel <<EMAIL>>
     *
     * @param string $strAlias    Mailbox alias
     * @param string $strPassword Mailbox password
     *
     * @throws Exception
     *
     * @return void
     */
    public function addMailbox($strAlias, $strPassword)
    {
        // Simple validation
        $this->validateMasterMailbox();
        $this->validateFreeDomain();

        $bolHasMailboxComponentBeenFound = false;

        foreach($this->getProductComponents() as $objMailboxComponent) {

            // If component has no space on it then take an other one
            if ($objMailboxComponent->isFullOfMailboxes()) {
                continue;
            }

            $strDomain = $this->objFreeDomainName->getDomainName();

            // Create new mailbox on soulstone
            $objNewMailbox = $this->objMailBox->createNewMailBox($strAlias, $strDomain, $strPassword);
            $objMailboxComponent->addMailIDToConfiguration($objNewMailbox->getMailId());

            // Create new mailbox on maildb
            $this->objMailApi->createAdditionalMailBox(new String($strAlias),
                                                       new String($strDomain),
                                                       new String(crypt($strPassword)),
                                                       new Int($this->getMailboxQuota()),
                                                       new String($objNewMailbox->getUserName()));

            // this could quite possibly be the first additional maiblox added if it is then
            // we need to make sure the master mailbox cathall is created as long as its not disabled
            $this->setDefaulMailbox($this->objMailBox->getCatchallMultipopId($this->objFreeDomainName instanceof MAAF_Component_FreeDomainName ? $this->objFreeDomainName->getDomainName() : ''));


            $bolHasMailboxComponentBeenFound = true;
            break;
        }

        if (!$bolHasMailboxComponentBeenFound) {
            throw new Exception('Mailbox can not be created. Components are full');
        }


    } // end of method addMailbox

    /**
     * Method deletes given mailbox
     *
     * <AUTHOR> Bargiel <<EMAIL>>
     *
     * @param BrightViewMailBox $objMailbox Mailbox to delete
     *
     * @throws Exception
     *
     * @return void
     */
    public function deleteMailbox(BrightViewMailBox $objMailbox)
    {
        $this->validateMasterMailbox();

        $authdb = get_named_connection_with_db('mailauth');
        $delivdb = get_named_connection_with_db('maildelivery');

        PrimitivesQueryOrExit("BEGIN", $authdb);
        PrimitivesQueryOrExit("BEGIN", $delivdb);

        $this->objMailApi->deleteMailbox(
            new String($objMailbox->getAlias()),
            new String($this->objFreeDomainName->getDomainName())
        );

        if($this->objMailBox->deleteMailbox($objMailbox->getMultipopId())) {
            PrimitivesQueryOrExit("COMMIT", $authdb);
            PrimitivesQueryOrExit("COMMIT", $delivdb);
            MAAF_CProductComponent_Mailboxes::deleteMailIDFromConfiguration($objMailbox->getMailId());
        } else {
            PrimitivesQueryOrExit("ROLLBACK", $authdb);
            PrimitivesQueryOrExit("ROLLBACK", $delivdb);
        }
    } // end of method deleteMailbox()

    /**
     * Method creates forward
     *
     * <AUTHOR> Bargiel <<EMAIL>>
     *
     * @param BrightViewMailBox $objMailbox
     * @param string $strForwardEmail
     *
     * @throws Exception
     */
    public function createForward(BrightViewMailBox $objMailbox, $strForwardEmail)
    {

        $delivdb = get_named_connection_with_db('maildelivery');
        PrimitivesQueryOrExit("BEGIN", $delivdb);

        if($objMailbox->getStatus() === BrightViewMailBox::STATUS_EXTERNAL ||
           $objMailbox->getStatus() === BrightViewMailBox::STATUS_EXTERNALOFF) {

            $this->objMailApi->remapForward(
                new String($objMailbox->getAlias()),
                new String($this->objFreeDomainName->getDomainName()),
                new String($strForwardEmail)
            );
        } else {

            $this->objMailApi->createForward(
                new String($objMailbox->getAlias()),
                new String($this->objFreeDomainName->getDomainName()),
                new String($strForwardEmail)
            );
        }

        if($objMailbox->forwardTo($strForwardEmail)) {
            PrimitivesQueryOrExit("COMMIT", $delivdb);
        } else {
            PrimitivesQueryOrExit("ROLLBACK", $delivdb);
        }
    } // end of method createForward()

    /**
     * Method removes forward from mailbox
     *
     * <AUTHOR> Bargiel <<EMAIL>>
     *
     * @param BrightViewMailBox $objMailbox
     *
     * @throws Exception
     *
     * @return void
     */
    public function removeForward(BrightViewMailBox $objMailbox)
    {
        $delivdb = get_named_connection_with_db('maildelivery');
        PrimitivesQueryOrExit("BEGIN", $delivdb);

        $this->objMailApi->removeForward(
            new String($objMailbox->getAlias()),
            new String($this->objFreeDomainName->getDomainName())
        );

        if($objMailbox->removeForward()) {
            PrimitivesQueryOrExit("COMMIT", $delivdb);
        } else {
            PrimitivesQueryOrExit("ROLLBACK", $delivdb);
        }
    } // end of method removeForward()

    /**
     * Method sets default mailbox
     *
     * <AUTHOR> Bargiel <<EMAIL>>
     *
     * @param integer $intMultipopId Id of mailbox that will be set as default mailbox
     *
     * @throws Exception
     *
     * @return void
     */
    public function setDefaulMailbox($intMultipopId)
    {
        if(0 < $intMultipopId && !$this->isMyMailbox($intMultipopId))
            return;

        // Simple validation
        $this->validateMasterMailbox();
        $this->validateFreeDomain();

        // Remove existing catchalls - the same as setting catch all on master
        $this->objMailBox->setCatchAllOnMaster($this->objFreeDomainName->getDomainName());

        switch(intval($intMultipopId)) {
        case -1:  // disable
            $this->objMailBox->disableCatchAll($this->objFreeDomainName->getDomainName());
            $this->objMailApi->disableCatchAll(
                new String($this->objFreeDomainName->getDomainName())
            );
            break;
                              /* Problem 53915 - comment from previous version of this file - do not delete it.
                               * To avoid spammers signing up hundreds of account with bad cards and using the free email address:
                               * "Configurator change - we don't want Webmail to be activated on creation of user account,
                               * users should either phone in to have it activated, or it will be activated on activation of ADSL."
                               */
        case 0:   // master is catchall
            $this->objMailApi->setMasterAsCatchAll(
                new String($this->objFreeDomainName->getDomainName())
            );
            break;
        default:
            // Get mailbox based in multipopId
            $objSelectedMailbox = $this->getMailBox($intMultipopId);

            // Set catch all to alias@domain
            if (is_object($objSelectedMailbox)) {
                $this->objMailBox->setCatchAll($this->objFreeDomainName->getDomainName(), $objSelectedMailbox->getAlias());
                $this->objMailApi->setCatchAll(new String($this->objFreeDomainName->getDomainName()),
                                               new String($objSelectedMailbox->getAlias()));
            }

        }
    } // end of method setDefaulMailbox()

    /**
     * Method checks is catchall is disabled
     *
     * <AUTHOR> Bargiel <<EMAIL>>
     *
     * @return boolean
     */
    public function isCatchAllDisabled()
    {
        // Simple validation
        $this->validateMasterMailbox();
        $this->validateFreeDomain();

        return $this->objMailBox->isCatchAllDisabled($this->objFreeDomainName->getDomainName());
    } // end of method isCatchAllDisabled()

    /**
     * Method configures product
     *
     * <AUTHOR> Bargiel <<EMAIL>>
     *
     * @throws Exception
     *
     * @return void
     *
     * @todo Implement me
     */
    public static function configure($intComponentID, $strAction)
    {
        $objWebmail = new MAAF_CProduct_Webmail($intComponentID);

        switch($strAction) {
            case "auto_configure":
                $objWebmail->checkBundledMailboxesAndFixIfNeeded();
                if ($objWebmail->getStatus() != 'unconfigured') {
                    $objWebmail->disable();
                    $objWebmail->refreshCurrentState();
                    $objWebmail->setStatus('unconfigured');
                }
                break;
            case "auto_disable":
                $objWebmail->disable();
                break;
            case "auto_enable":
                $objWebmail->enable();
                $objWebmail->refreshCurrentState();
            break;
            case "auto_refresh":
                $objWebmail->checkBundledMailboxesAndFixIfNeeded();
                $objWebmail->refreshCurrentState();
                break;
            case "auto_destroy":
                $objWebmail->destroy();
                break;
        }


    } // end of method configure()

    /**
     * Method enables the component
     *
     * <AUTHOR> Bargiel <<EMAIL>>
     *
     * @throws Exception
     *
     * @return integer
     */
    public function enable()
    {
        if (!$this->canBeEnabled()) {
            return -1;
        }

        $this->prvSetStatus('queued-activate');

        // Enable master mailbox
        $this->validateMasterMailbox();
        $this->objMailBox->enableMailbox();

        $this->objMailApi->createMasterMailbox(new String($this->objMailBox->getRealm()),
                                               new String($this->objMailBox->getPasswd()),
                                               new Int($this->getMailboxQuota()),
                                               new String($this->objMailBox->getUserName()));

        if(!$this->enableProductComponents()) {
            return -1;
        }

        $this->prvSetStatus('active');

        $this->refreshInstance($this->getComponentID());

        $this->configureSmtpAuth();

        if ($this->getStatus() != 'active') {
            return -1;
        }

        $this->logStatusChange('ACTIVE');

        return 0;
    } // end of method enable()

    /**
     * Method disables the component
     *
     * <AUTHOR> Bargiel <<EMAIL>>
     *
     * @throws Exception
     *
     * @return integer
     *
     */
    public function disable()
    {
        if (!$this->canBeDisabled()) {
            return -1;
        }

        $this->prvSetStatus('queued-deactivate');

        // Disable master mailbox
        $this->validateMasterMailbox();
        $this->objMailBox->disableMailbox();
        $this->getListOfMailboxesAsAssoc(TRUE); // called to get a fresh version of $this->objMailBox after the status and options update
        $this->refreshMailUsers();

        if(! $this->disableProductComponents()) {
            return -1;
        }

        //User was sucessfully deactivated
        $this->prvSetStatus('deactive');
        $this->configureSmtpAuth();
        $this->logStatusChange('deactive');

        return 0;

    } // end of method disable()

    /**
     * Method destroys the component
     *
     * <AUTHOR> Bargiel <<EMAIL>>
     *
     * @throws Exception
     *
     * @return void
     *
     * @todo Implement full method
     */
    public function destroy()
    {
        $this->setStatus('queued-destroy');

        try {
            // Archive master mailbox
            $this->validateMasterMailbox();
            $this->objMailBox->archiveMailbox();
            $this->objMailApi->destroy();
        } catch (InvalidSoulstoneEntryException $e) {
            if ($this->userdata['status'] == 'queued-destroy') {
                // Problem: 63885 If the service is 'queued-destroy', then we do not need a valid Soulstone entry.
                // Just log the service details and continue
                InvalidSoulstoneEntryException::appendToNotificationLog($this->getServiceID());
            } else {

                throw $e;
            }
        }

        if(!$this->destroyProductComponents()) {
            throw new Exception('Error during component destroying');
        }

        //User was succesfully deactivated
        $this->setStatus('destroyed');
        $this->logStatusChange('DESTROYED');

    } // End of method destroy()

    /**
     * Method sets status of the component
     *
     * <AUTHOR> Bargiel <<EMAIL>>
     *
     * @param string $strStatus New Status of the component
     *
     * @throws Exception
     *
     * @return void
     */
    public function setStatus($strStatus)
    {
        if (!in_array($strStatus, self::$arrStatuses)) {
            throw new Exception("Unknown status $strStatus");
        }

        $resDBHandle = get_named_connection_with_db('userdata');

        $strQuery = 'UPDATE components
                        SET status  = "'.mysql_real_escape_string($strStatus).'"
                      WHERE component_id = "'.mysql_real_escape_string($this->m_intComponentID).'"';
        $this->doQuery($strQuery, 'Update the component status');

        $this->m_strStatus = $strStatus ;

    } // End of method setStatus()

    /**
     * Refreshes the current state of the component
     *
     * If the component is in a queued state
     *
     * <AUTHOR> Bargiel <<EMAIL>>
     *
     * @throws Exception
     *
     * @return void
     */
    public function refreshCurrentState()
    {
        // if we're in a queued state attempt to action it
        // breaks show deliberate fall throughs.
        switch($this->m_strStatus)
        {
            case 'queued-activate':
            case 'queued-reactivate':
                $this->enable();
                $this->refreshMailConfigs(); // this will also refresh domains
                return $this->refreshMailUsers();
                break;

            case 'queued-deactivate':
                return $this->disable();
                break;

            case 'queued-destroy':
                return $this->destroy();
                break;

            case 'active':
                $this->refreshMailConfigs(); // this will also refresh domains
                return $this->refreshMailUsers();
                break;

            case 'deactive':
                // Nothing to do
                break;
            default:
                //not in a queued state
                break;
        }
    } // end of method refreshCurrentState()

    /**
     * Method is responsible for quering database
     *
     * <AUTHOR> Bargiel <<EMAIL>>
     *
     * @throws Exception
     *
     * @return void
     */
    protected function doQuery($strQuery, $strMessage = '')
    {
        if (!$this->resDBHandle) {
            $this->resDBHandle = get_named_connection_with_db('userdata');
        }

        if (!PrimitivesQueryOrExit($strQuery, $this->resDBHandle, $strMessage, false)) {
            throw new Exception('Error during database query');
        }

    } // end of method doQuery()

    /**
     * Returns the full name of this Product
     *
     * <AUTHOR> Bargiel <<EMAIL>>
     *
     * @return string
     */
    function getFullName()
    {
        return "MadAsAFish Webmail";
    } // end of method getFullName()

    /**
     * Component status change logger.
     *
     * <AUTHOR> Bargiel <<EMAIL>>
     *
     * @param string $strStatus Component status
     *
     * @return void
     */
    public function logStatusChange($strStatus)
    {
        $objEventLogger = $this->prvGetEventLogger();

        switch (strtoupper($strStatus)) {
            case 'ACTIVE':
                $objEventLogger->logStatusChange('MaafWebMailActivation');
                break;
            case 'DEACTIVE':
                $objEventLogger->logStatusChange('MaafWebMailDeactivation');
                break;
            case 'DESTROYED':
                $objEventLogger->logStatusChange('MaafWebMailDestruction');
            default;
                break;
        }
    } // end of method logStatusChange()

    /**
     * Method checks if domain alias is valid
     *
     * <AUTHOR> Bargiel <<EMAIL>>
     *
     * @param string $strAlias Alias to validate
     *
     * @return boolean
     */
    public function isAliasValid($strAlias)
    {
        $intLength = strlen($strAlias);

        if ($intLength<3 || $intLength>16) {
            return false;
        }

        //in alias all letters, digits, and also signs .-_ are allowed
        if (0 == preg_match( '/^[a-z0-9]+[._-]?[a-z0-9]+$/i', $strAlias)) {
            return false;
        }

        if(strtolower($strAlias) == 'bv.master' || strtolower($strAlias == 'catchall.email')) {
            return false;
        }

        return true;
    } // end of method isAliasValid()

    /**
     * Method check if password is valid
     *
     * <AUTHOR> Bargiel <<EMAIL>>
     *
     * @param string $strPassword Password to validate
     *
     * @return boolean
     */
    public function isPasswordValid($strPassword)
    {
        $intLength = strlen($strPassword);

        if ($intLength < 6 || $intLength > 8) {
            return false;
        }

        if (!ctype_alnum($strPassword)) {
            return false;
        }

        return true;
    } // end of method isPasswordValid()

    /**
     * Method checks if objMailbox if object of class BrightViewPasswd
     * if not it throws an exception
     *
     * <AUTHOR> Bargiel <<EMAIL>>
     *
     * @throws InvalidSoulstoneEntryException
     *
     * @return void
     */
    protected function validateMasterMailbox()
    {
        if (!$this->objMailBox instanceof BrightViewPasswd) {
            throw new InvalidSoulstoneEntryException('No master mailbox');
        }
    } // end of method validateMasterMailbox()

    /**
     * Method checks if objFreeDomainName property is an instance of BrightViewDomain class.
     * If not it throws an exception
     *
     * <AUTHOR> Bargiel <<EMAIL>>
     *
     * @throws Exception
     *
     * @return void
     */
    public function validateFreeDomain()
    {
        if (!$this->objFreeDomainName instanceof MAAF_Component_FreeDomainName) {
            throw new Exception('No free domain');
        }
    } // end of method validateFreeDomain()

    /**
     * Method checks if mailbox belongs to this service id
     *
     * <AUTHOR> Sourkova <<EMAIL>>
     *
     * @throws Exception
     *
     * @return bool
     */
    public function isMyMailbox($intMultiPopId)
    {
        return array_key_exists($intMultiPopId, $this->getListOfMailboxesAsAssoc());
    } // end of method isMyMailbox()

    /**
     * Method checks if webmail component has assigned bundled email products
     *
     * <AUTHOR> Stefaniak <<EMAIL>>
     *
     * @throws Exception
     *
     * @return void
     */
    public function checkBundledMailboxesAndFixIfNeeded()
    {
        $bolBundleNotFound = true;
        foreach($this->getProductComponents() as $objProductComponent)
        {
            if(PRODUCT_COMPONENT_MAAF_WEBMAIL_BUNDLEDMAILBOXES == $objProductComponent->getProductComponentID())
            {
                $bolBundleNotFound = false;
                break;
            }
        }

        if($bolBundleNotFound)
        {
            $objNewInstance = CProductComponent::create($this->getServiceID(),$this->getComponentID(), PRODUCT_COMPONENT_MAAF_WEBMAIL_BUNDLEDMAILBOXES);
        }

    } // end of method checkBundledMailboxesAndFix()

    /**
     * Deativate the domain
     */
    public function deactivateDomain()
    {
        $objComponent = MAAF_Component_FreeDomainName::getQueuedDestroyComponentByServiceId($this->getServiceID());

        if ($objComponent instanceof MAAF_Component_FreeDomainName) {

            $this->objMailApi->deleteVirtualDomainAndMailboxes(new String($objComponent->getDomainName()));
        }
    }

    private function refreshMailConfigs()
    {
        // the following try/catch will create the master mailbox and visp domain if they where missing
        // then we just have to worry about the additional mailboxes
        try {
            $this->validateMasterMailbox();
            $this->validateFreeDomain();
            $this->objMailApi->validateVirtualDomain(new String($this->objFreeDomainName->getDomainName()));
        } catch (Exception $e) {
            $this->enable();
        }


        // if there are any additional mailboxes, this will auto-magically add the domain into the mail delivery system (if missing).
        // if there are no additional mailboxes and the account has a domain then we'll just add the domain into
        // the mail delivery system, even though nothing is going to use it yet

        //so additional mailboxes eh? lets get em...
        $arrAdditionalSoulstoneMailConfigs = $this->getListOfMailboxesAsAssoc(TRUE);
        if($this->objFreeDomainName instanceof MAAF_Component_FreeDomainName) {

            $arrConfigsToRemove = $this->objMailApi->getAdditionalMailConfigsForService();


            // now lets get rid of redundant records in mail delivery system,
            // while we are doing that we will try and sync the mail config types too :)

            // Remove all mail configs that are in MailDelivery but are not in soulstone anymore
            // 1. Substract all soulstone configs
            // 2. If anything is left it means it shouldn't be there so delete it

            foreach($arrConfigsToRemove as $intKey => $arrMDConfig) { // for every mail delivery config

                foreach($arrAdditionalSoulstoneMailConfigs as $intMultipopId => $strSSConfig) { // for every entry in soulstone
                    if ($strSSConfig == $arrMDConfig['LocalPart']) { // try and get a match on mail delivery and soulstone localparts

                        // try and sync mail config types (and check that redirects are going to the correct place)

                        // get all details about the soulstone mailbox in hand
                        /*BrightViewMailBox*/ $objBVMailbox = $this->getMailBox($intMultipopId);

                        // soulstone uses an additional record to store that a mailbox is also a catchall
                        // we dont want this record in the db - so if we come accross it lets move on!
                        if($objBVMailbox->isCatchAll()) {
                            continue 1; //foreach($arrAdditionalSoulstoneMailConfigs
                        }

                        // is the soulstone config a redirect? and is the mail delivery config not a redirect
                        if($objBVMailbox->getStatus() == 'external' && $arrMDConfig['MailboxTypeID'] != 5) {
                            //create redirect
                            $this->objMailApi->createForward(new String($arrMDConfig['LocalPart']),new String($this->objFreeDomainName->getDomainName()),new String($objBVMailbox->getExternal()));
                        }
                        // is the soulstone config a redirect? and do the destinations match??
                        if($objBVMailbox->getStatus() == 'external' && $arrMDConfig['Destination'] != $objBVMailbox->getExternal() ) {
                            //remap redirect
                            $this->objMailApi->remapForward(new String($arrMDConfig['LocalPart']),new String($this->objFreeDomainName->getDomainName()),new String($objBVMailbox->getExternal()));
                        }
                        // if soulstone says this is a mailbox and mail delivery says its not then make it a mailbox
                        if($objBVMailbox->getStatus() == 'active' && $arrMDConfig['MailboxTypeID'] == 5) {
                            //delete redirect
                            $this->objMailApi->removeForward(new String($arrMDConfig['LocalPart']),new String($this->objFreeDomainName->getDomainName()));
                        }

                        // now as we know the two configs are in sync and exist in both dbs we remove it from the array of configs to bin :)
                        unset($arrConfigsToRemove[$intKey]);
                        continue 2; // foreach($arrConfigsToRemove)
                    }
                }
            }

            // anything left in arrConfigsToRemove should be removed
            foreach($arrConfigsToRemove as $arrRemove) {
                $this->objMailApi->deleteMailbox(new String($arrRemove['LocalPart']), new String($this->objFreeDomainName->getDomainName()));
            }

            // now we have got rid of all the chaff in mail delivery system are we missing anything??
            $arrAdditionalMailDeliveryMailConfigs = $this->objMailApi->getAdditionalMailConfigsForService();
            foreach($arrAdditionalSoulstoneMailConfigs as $intMultipopId => $strSSConfig) {

                foreach($arrAdditionalMailDeliveryMailConfigs as $intKey => $arrMailDeliveryConfig) {

                    if ($strSSConfig == $arrMailDeliveryConfig['LocalPart']) { // try and get a match on mail delivery and soulstone localparts

                        // we have found a match so we dont need to add it

                        continue 2; // foreach($arrAdditionalSoulstoneMailConfigs as $intMultipopId => $strSSConfig)
                    }
                }

                // for the current $strSSConfig we didnt manage to find an entry in mail delivery system
                // so we need to add it

                // so, get mailbox details from soulstone
                /*BrightViewMailBox*/ $objBVMailbox = $this->getMailBox($intMultipopId);

                // soulstone uses an additional record to store that a mailbox is also a catchall
                // we dont want this record in the db - so if we come accross it lets move on!
                if($objBVMailbox->isCatchAll()) {
                    continue 1; //foreach($arrAdditionalSoulstoneMailConfigs
                }

                // now add the mailbox on the mail delivery db (which by default will also add the mail user too!)
                $this->objMailApi->createAdditionalMailBox(new String($objBVMailbox->getAlias()),
                                                       new String($objBVMailbox->getDomain()),
                                                       new String($objBVMailbox->getPasswd()),
                                                       new Int($this->getMailboxQuota()),
                                                       new String($objBVMailbox->getUserName()));

            }

            // now that all is present and correct we need to just double check the catchall
            // i.e. is the right mailbox set to catchall in the mail delivery system

            //check current catchall in soulstone and reset it on all systems to that
            // we will get an exception thrown here when deactivating a domain, this is expected so wrap in try catch and do nothing, its fine
            try {
                $this->setDefaulMailbox($this->objMailBox->getCatchallMultipopId($this->objFreeDomainName instanceof MAAF_Component_FreeDomainName ? $this->objFreeDomainName->getDomainName() : ''));
            } catch(Exception $e) {
                //do nothing
            }
        }

    }


    private function refreshMailUsers()
    {
        // refreshMailConfigs() should have just been called so we know we have all users in the auth db
        // now we need to make sure that all flags and passwords are set correctly

        // configure options as per soulstone
        // only ones we worry about are webmail,pop3 and imap
        // smtp is ignored and our business rules are used - see $this->configureSmtpAuth();
        $this->setOptionFlags($this->objMailBox->getOptionsAsBits());

        // configure intPaidForFlag (smtp / intPaidFlag)
        $this->configureSmtpAuth();

        // now we need to refresh the password for each user from soulstone to mail db
        // we know that maildb now has all mailboxes that it should have so...
        $arrAllMailboxes = array_merge($this->objMailApi->getAdditionalMailConfigsForService(), $this->objMailApi->getMasterMailConfigsForService());

        foreach($arrAllMailboxes as $arrMailbox) {

            $localpart = $arrMailbox['LocalPart'];
            if($arrMailbox['LocalPart'] == 'bv.master') {
                $localpart = $arrMailbox['Username'];
                $password = $this->objMailBox->getPasswd();
            } else {
                $objBVMailBox = BrightViewMailBox::fromAliasAndDomain($localpart, $arrMailbox['Domain']);
                $password = $objBVMailBox->getPasswd();
            }

            if($password != '') {
                $this->objMailApi->setMailboxPassword(
                                                   new String($arrMailbox['LocalPart']),
                                                   new String($password),
                                                   array(new String($arrMailbox['Domain']))
                                                 );
            }
        }
        // now all flags have been reset to match soulstone and so has every password
        // so we should be sorted now
        // the end.


        return 0;
    }




    private function configureSmtpAuth()
    {
        $this->objMailApi->autoConfigurePaidForFlag($this);
    }



    /**
     * Sets low-level parameters for all mailboxes in PlusNet maildb
     *
     * @access public
     * @param $intOptionBits - bitmask of options to be enabled. Any flag/bit that isn't set here will
     *                         be disabled. The flags currently actively supported are defined as:
     *
     *                          -  BrightViewPasswd::OPT_SMTP
     *                          -  BrightViewPasswd::OPT_POP3
     *                          -  BrightViewPasswd::OPT_WEBMAIL
     *
     **/

    public function setOptionFlags($intOptionBits)
    {
        $this->objMailApi->setOptionFlags($intOptionBits);
    }

} // end of class MAAF_Component_Webmail

