<?php
	/**
	 * Enter description here...
	 *
	 */
	interface Core_IServiceDao
	{
		public static function get($intServiceId, $strTransaction = Db_Manager::DEFAULT_TRANSACTION);

		public function getServiceId();

		public function setServiceId($intServiceId);

		public function setUserId($intUserId);

		public function setUsername($strUsername);

		public function setPassword($strPassword);

		public function setCliNumber($strCliNumber);

		public function setType ($intType);

		public function setStatus($strStatus);

		public function setEndDate($strDate);

		public function setNextInvoice($strDate);

		public function setInvoicePeriod($strPeriod);

		public function setNextInvoiceWarned($strNextInvoiceWarned);

		public function setInvoiceDay($intDay);

		public function setAuthorisedSwitchPayment($strAuthorisedSwitchPayment);

		public function setMailOptOut($intMailOptOut);

	}

?>