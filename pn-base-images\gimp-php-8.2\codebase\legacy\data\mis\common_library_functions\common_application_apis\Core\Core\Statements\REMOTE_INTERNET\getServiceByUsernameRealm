server: itw
role: slave
rows: single
statement:

    SELECT CAST(s.service_id AS UNSIGNED) AS intServiceId
      FROM RemoteInternetSession.tblBusinessActor AS ba
INNER JOIN RemoteInternetSession.tblAuthenticationRealm AS ar ON (ar.authenticationRealmID = ba.authenticationRealmID)
INNER JOIN RemoteInternetSession.tblUserType AS ut ON (ut.userTypeID = ba.userTypeID)
INNER JOIN dbRemoteInternet.services s ON (s.service_id = ba.externalUserID)
     WHERE ba.username  = :strUsername
       AND ar.realm = :strRealm
