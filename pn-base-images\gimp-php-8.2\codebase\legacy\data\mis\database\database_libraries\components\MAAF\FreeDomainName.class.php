<?php
/**
 * Webmail product component
 *
 * @package components.MAAF
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @version $Id: FreeDomainName.class.php,v 1.9 2009-03-19 17:40:39 bselby Exp $
 */

/**
 * Needed requirements
 */
require_once '/local/data/mis/database/database_libraries/components/CComponent.inc';
require_once '/local/data/mis/database/class_libraries/Brightview/BrightViewDomain.class.php';
require_once '/local/data/mis/database/database_libraries/components/MAAF/Webmail.class.php';
require_once '/local/data/mis/database/database_libraries/mailer_functions.inc';
require_once '/local/data/mis/database/database_libraries/product-access.inc';

/**
 * Component class for classes  Free domain name component
 *
 * <AUTHOR> <<EMAIL>>
 */
class MAAF_Component_FreeDomainName extends CComponent
{
	/**
	 * Array for singleton representation of component instances
	 *
	 * @var array
	 */
	protected static $arrComponentInstance = null;

	/**
	 * Database connections
	 *
	 * @var array
	 */
	protected static $arrDBHandle = array();

	/**
	 * Component details
	 *
	 * @var array
	 */
	protected $arrComponentDetails = array();

	/**
	 * Service data
	 *
	 * @var array
	 */
	protected $arrService;

	/**
	 * User username
	 *
	 * @var string
	 */
	protected $strUsername = '';

	/**
	 * User realm
	 *
	 * @var string
	 */
	protected $strRealm = '';

	/**
	 * Domain object
	 *
	 * @var BrightViewDomain
	 */
	protected $objDomain = null;

	/**
	 * Comnponent type Id constance
	 */
	const COMPONENT_TYPE_ID = COMPONENT_MAAF_FREEDOMAIN;
	
	/*
	 * Holds max failure limit for destory method  
	 */
	const DESTROY_FAILURE_MAXIMUM = 10;
	

	/**
	 * Method configures free domain name component
	 *
	 * <AUTHOR> Bargiel <<EMAIL>>
	 *
	 * @param integer $intComponentID  Component to configure
	 * @param integer $strAction       Configuration action
	 *
	 * @throws Exception
	 *
	 * @return void
	 */

	public function getDomain()
	{
		return $this->objDomain;
	}
	public static function configureComponent($intComponentID, $strAction)
	{
		$objFreeDomainName = new MAAF_Component_FreeDomainName($intComponentID);
		switch($strAction) {
			case "auto_configure":
			case "auto_disable":
			case "auto_enable":
			case "auto_refresh":
			// No action is done for domain component by the configurator.
			// Domain gets activated by /local/www/database-admin/scripts/domains/activateMaafDomains.php
			// Only action needed by the configurator is the destroy
			break;

		    case "auto_destroy":
		    	$objFreeDomainName->destroy();
				break;
		}

	} // end of method configureComponent

	/**
	 * Component constructor
	 *
	 * <AUTHOR> Bargiel <<EMAIL>>
	 *
	 * @param integer $intComponentID
	 */
	protected function __construct($intComponentID)
	{
		// Component details
		$this->arrComponentDetails = userdata_component_get($intComponentID);
		$this->arrService = userdata_service_get($this->arrComponentDetails['service_id']);
		$this->strUsername = $this->arrService['username'];
		$this->strRealm	   = userdataGetFrameworkAuthRealm($this->arrService['service_id'],
		                                                   $this->strUsername);
		$this->objDomain   = BrightViewDomain::fromUserNameAndRealm($this->strUsername, $this->strRealm, true);

		parent::__construct($intComponentID);
	} // end if __construct

	/**
	 * Method registers new domain
	 *
	 * <AUTHOR> Bargiel <<EMAIL>>
	 *
	 * @param string $strDomainName
	 * @param string $strTag
	 * @param string $strAddress1
	 * @param string $strAddress2
	 * @param string $strTown
	 * @param string $strCounty
	 * @param string $strPostCode
	 *
	 * @throws Exception
	 *
	 * @return void
	 */
	public function registerDomain($strDomainName, $strTag, $strAddress1, $strAddress2, $strTown, $strCounty, $strPostCode)
	{
		// Change component status to invalid just in case BV communication fails
		$this->prvSetStatus('invalid');

		$arrData = array();
		$arrData['domainname'] = $strDomainName;
		$arrData['username'] = $this->strUsername;
		$arrData['realm'] = $this->strRealm;
		$arrData['tag'] = $strTag;
		$arrData['address1'] = $strAddress1;
		$arrData['address2'] = $strAddress2;
		$arrData['town'] = $strTown;
		$arrData['county'] = $strCounty;
		$arrData['postcode'] = $strPostCode;
		$arrData['optout'] = 'Yes';

		BrightViewDomain::register($arrData);

		// Change component status to queued-activate (if it's not in this status yet)
		$this->prvSetStatus('queued-activate');

		self::createAnEntryInConfigurationTable($strDomainName);


		// Create a webmail component so that we can send it a refresh signal.
		// Refreshing it causes the virtual domain stuff in our maildb to be populated appropriately.

		$objWebmail = MAAF_CProduct_Webmail::getProductByProductTypeHandle($this->arrService['service_id'], MAAF_CProduct_Webmail::COMPONENT_HANDLE);

		if (is_object($objWebmail)) {

			MAAF_CProduct_Webmail::configure($objWebmail->getComponentID(), 'auto_refresh');
		}

	} // end of registerDomain

	/**
	 * Method creates entry in configuration table for new registered domain
	 *
	 * <AUTHOR> Bargiel <<EMAIL>>
	 *
	 * @param string $strDomainName
	 *
	 * @throws Exception
	 *
	 * @return void
	 */
	protected function createAnEntryInConfigurationTable($strDomainName)
	{
		$strQuery = 'INSERT INTO userdata.tblConfigMaafFreeDomainName SET intComponentID='.mysql_real_escape_string($this->getComponentID()).', ';
		$strQuery .= 'vchDomainName = "'.mysql_real_escape_string($strDomainName).'"';

		self::doQuery($strQuery, 'userdata');

	} // end of method createAnEntryInConfigurationTable()

	/**
	 * Method transfers domain
	 *
	 */
	public function transferDomain($strTag)
	{
		// Simple validation
		$this->validateDomainObject();

		return $this->objDomain->transfer($strTag);
	} // end of method transferDomain()

	/**
	 * Method gets domain status
	 *
	 * <AUTHOR> Bargiel <<EMAIL>>
	 *
	 * @return string
	 */
	public function getDomainStatus()
	{
		// Simple validation
		$this->validateDomainObject();

		return $this->objDomain->getStatus();
	} // end of method getDomainStatus()

	/**
	 * Method gets user name
	 *
	 * <AUTHOR> Bargiel <<EMAIL>>
	 *
	 * @return string
	 */
	public function getUserName()
	{
		return $this->strUsername;
	} // end of method getUserName()

	/**
	 * Method gets domain name for a given service Id
	 *
	 * <AUTHOR> Bargiel <<EMAIL>>
	 *
	 * @throws Exception
	 *
	 * @return string
	 */
	public function getDomainName()
	{
		// Simple validation
		$this->validateDomainObject();

		return $this->objDomain->getDomain();
	} // end of method getDomainName()

	/**
	 * Singleton method gets component based on component id
	 *
	 * <AUTHOR> Bargiel <<EMAIL>>
	 *
	 * @param integer $intComponentID Component id
	 *
	 * @throws Exception
	 *
	 * @return MAAF_Component_FreeDomainName
	 */
	public static function getComponent($intComponentID)
	{
		if (isset(self::$arrComponentInstance[$intComponentID]) &&
			self::$arrComponentInstance[$intComponentID] instanceof MAAF_Component_FreeDomainName) {

			return self::$arrComponentInstance[$intComponentID];
		}

		$objTempInstance = new MAAF_Component_FreeDomainName($intComponentID);

		if ($objTempInstance->getComponentID()=='') {
			throw new Exception("Component $intComponentID has not been found");
		}

		self::$arrComponentInstance[$intComponentID] = $objTempInstance;

		return self::$arrComponentInstance[$intComponentID];

	} // end of method getComponent()

	/**
	 * Methods gets component based on component id
	 *
	 * <AUTHOR> Bargiel <<EMAIL>>
	 *
	 * @param integer $intServiceId Service Id
	 *
	 * @return MAAF_Component_FreeDomainName
	 */
	public static function getComponentByServiceId($intServiceId)
	{
		$objComponent = parent::getComponentByServiceComponentId($intServiceId, self::COMPONENT_TYPE_ID);

		if($objComponent) {

			return self::getComponent($objComponent->getComponentID());
		} else {

			return null;
		}
	}

	/**
	 * Get the free domain name queued-destroy component for the service id
	 *
	 * We need the ability to pull back the queued-destroy component for
	 * free domain as otherwise the Webmail::deactivateDomain call fails, as
	 * that uses the internal object which is setup using
	 * FreeDomainName::getComponentByServiceId which doesn't look for
	 * 'queued-destroy' components
	 *
	 * @param $intServiceId
	 * @return MAAF_Component_FreeDomainName
	 */
	public static function getQueuedDestroyComponentByServiceId($intServiceId)
	{
		$arrComponents = userdata_component_find(array('service_id' => $intServiceId,
		                                               'type'       => array(self::COMPONENT_TYPE_ID),
		                                               'status'     => array('queued-destroy')));

		if (!empty($arrComponents) && isset($arrComponents[0]['component_id'])) {

			return self::getComponent($arrComponents[0]['component_id']);
		}

		return null;
	}

	/**
	 * Method refreshes component current status
	 *
	 * <AUTHOR> Bargiel <<EMAIL>>
	 *
	 * @return boolean False if the component is destroyed, otherwise true
	 */
	public function refreshCurrentState()
	{
		// if we're in a queued state attempt to action it
		// breaks show deliberate fall throughs.
		switch($this->m_strStatus)
		{
			case 'queued-activate':
			case 'queued-reactivate':
				return $this->enable();
				break;

			case 'queued-deactivate':
				return $this->disable();
				break;

			case 'queued-destroy':
				return $this->destroy();
				break;

			case 'active':
				// Update domain from soulstone server
				$this->validateDomainObject();

				// Validate domain data
				if ($this->objDomain->isDomainEmpty()) {
					$this->prvSetStatus('invalid');
					userdata_component_set_configuration($this->getComponentID(), $this->getConfigID(), 'Missing domain');
				} else if ($this->objDomain->getStatus()!='Registered') {
					$this->prvSetStatus('invalid');
					 userdata_component_set_configuration($this->getComponentID(), $this->getConfigID(), 'Incorrect domain status '.$this->objDomain->getStatus());
				} else {
					userdata_component_set_configuration($this->getComponentID(), $this->getConfigID(), $this->objDomain->getDomain());
				}
			case 'deactive':
				// Nothing to do
				break;
			default:
				//not in a queued state
				break;
		}

	} // end of method refreshCurrentState()

	/**
	 * Method enables the component
	 *
	 * <AUTHOR> Bargiel <<EMAIL>>
	 *
	 * @return boolean False if the component is destroyed, otherwise true
	 */
	public function enable()
	{
		// Set status to enable and log operation
		if (!$this->canBeEnabled())
		{
			return -1;
		}

		//
		$this->prvSetStatus('queued-activate');

		// Simple validation
		$this->validateDomainObject();


		// Update domain from soulstone server
		userdata_component_set_configuration($this->getComponentID(), $this->getConfigID(), $this->objDomain->getDomain());

		//
		$this->prvSetStatus('active');

		if ($this->getStatus() != 'active')
		{
			return -1;
		}

		$objWebmail = MAAF_CProduct_Webmail::getProductByProductTypeHandle($this->arrService['service_id'], MAAF_CProduct_Webmail::COMPONENT_HANDLE);

		if (is_object($objWebmail)) {

				MAAF_CProduct_Webmail::configure($objWebmail->getComponentID(), 'auto_refresh');
		}

		return 0;

	} // end of method enable()

	/**
	 * Method disables the component
	 *
	 * <AUTHOR> Bargiel <<EMAIL>>
	 *
	 * @return boolean False if the component is destroyed, otherwise true
	 */
	public function disable()
	{
		// Validation
		$this->validateDomainObject();

		// Suspend domain
		$this->objDomain->suspend();

		$this->prvSetStatus('deactive');

	} // end of method disable()

	/**
	 * Method destroys the component
	 *
	 * <AUTHOR> Bargiel <<EMAIL>>
	 *
	 * @return boolean False if the component is destroyed, otherwise true
	 */
	public function destroy($strAction='')
	{
		// Simple validation
		$this->validateDomainObject();

		$objWebmail = MAAF_CProduct_Webmail::getProductByProductTypeHandle($this->arrService['service_id'], MAAF_CProduct_Webmail::COMPONENT_HANDLE);
		
		if(!$objWebmail instanceof CComponent) {
			throw new Exception('MAAF_CProduct_Webmail::getProductByProductTypeHandle() failed for the service_id'.$this->arrService['service_id']);						
		}
		switch ($strAction)
		{
			case 'transfer':
					$this->objDomain->transfer($this->objDomain->getTag());
					$this->prvSetStatus('queued-destroy');
					$objWebmail->deactivateDomain();
					break;

			case 'detag':
					$this->objDomain->detag();
					$this->prvSetStatus('queued-destroy');
					$objWebmail->deactivateDomain();
					break;

			case 'destroy':
					if($this->getStatus() == 'queued-destroy')
					{
						$objWebmail->deactivateDomain();
						$this->prvSetStatus('destroyed');
					}

			default:
					$this->objDomain->detag();
					$this->prvSetStatus('queued-destroy');
					$objWebmail->deactivateDomain();
					$this->prvSetStatus('destroyed');

		}

		return true;
	} // end of method destroy()

	/**
	 * Process domain resubission
	 *
	 * <AUTHOR> Kurylowicz <<EMAIL>>
	 *
	 * @param array $arrDomainComponentMapping
	 * @return array
	 */
	private static function reSubmitDomains($arrDomainComponentMapping)
	{
		if (0 == count($arrDomainComponentMapping)) {

			return array();
		}

		$arrResubmitedDomains = array();

		$strQuery = 'SELECT * FROM cashew.domain WHERE status IS NULL AND nominet IS NULL AND changedate < DATE_SUB(NOW(), INTERVAL 48 HOUR) AND domain ';

		if (1 == count($arrDomainComponentMapping)) {

			$strQuery .= ' = ?';
		}
		else {

			$strQuery .=  ' IN (' .substr(str_repeat("?,", count($arrDomainComponentMapping)), 0, -1) . ')';
		}

		$objStmt = BVDB::db()->prepare($strQuery);
		$objStmt->execute(array_keys($arrDomainComponentMapping));

		foreach ($objStmt->fetchAll() as $arrRow)
		{
			$objDomain = BrightViewDomain::fromMailId($arrRow['mailid']);
			if ($objDomain->submitRegistration()) {

				// Add service notice to note domain re-submission
				$strTicketText = "Domain {$arrRow['domain']} was resubmited to Nominet because we have no reply for last registration request in last 48h";
				$strTicketStatus = 'Closed';

				$arrResubmitedDomains[] = $arrRow['domain'];
			}
			else {

				$strTicketText = "Failed to resubmit domain {$arrRow['domain']} to Nominet";
				$strTicketStatus = 'Open';

				error_log(__METHOD__ . ' :' . $strTicketText);
			}

			tickets_ticket_add('Script', $arrDomainComponentMapping[$arrRow['domain']]['service_id'], 0, 0, $strTicketStatus, 0, $strTicketText);
		}

		return $arrResubmitedDomains;
	}

	/**
	 * Method returns list of domains that have been
	 * marked as registered and checks if they changed status on BV soulstone. If so
	 * it activates the component
	 *
	 * <AUTHOR> Bargiel <<EMAIL>>
	 *
	 * @return array returns activated domain names.
	 */
	public static function processDomains()
	{
		// Get domain list
		$strQuery = 'SELECT cmfdn.vchDomainName,cmfdn.intComponentID,c.status,c.service_id FROM tblConfigMaafFreeDomainName cmfdn INNER JOIN components c ON cmfdn.intComponentID = c.component_id WHERE c.status = '. "'queued-activate'";
		$resResult = self::doQuery($strQuery, 'userdata');

		$arrResult = PrimitivesResultsAsArrayGet($resResult);

		unset($resResult);
		$arrActivatedDomains = array();

		if (count($arrResult) > 0) {

	 		$arrDomainComponentMapping = array();
			$arrDomainNames = array();
			$strQuery = '';

			$strQuery = 'SELECT * FROM cashew.domain WHERE status = "Registered" AND domain IN (';

			foreach($arrResult as $arrDomain) {
				$strQuery .= '?,';
				$arrDomainNames[] = mysql_real_escape_string($arrDomain['vchDomainName']);
				$arrDomainComponentMapping[$arrDomain['vchDomainName']] = $arrDomain;
			}

			$strQuery = substr($strQuery, 0, strlen($strQuery) - 1).')';
			unset($arrResult);
			$stmt = BVDB::db()->prepare($strQuery);
			$stmt->execute($arrDomainNames);

			$arrDomains = array();
			//store Domain names to an array.
			while ($row = $stmt->fetch()) {

				$arrDomains[] = $row;
			}

			// Activate domains if any
			if (count($arrDomains) > 0) {

				// Activate components
				foreach($arrDomains as $arrDomain) {

					// Activate component
					$objFreeDomainComponent = new MAAF_Component_FreeDomainName($arrDomainComponentMapping[$arrDomain['domain']]['intComponentID']);

					if ($objFreeDomainComponent->getComponentID()) {

						if ($objFreeDomainComponent->enable() == 0) {

							$arrActivatedDomains[] = $arrDomain['domain'];
						}

						//Send mail to customer
						$intServiceId = $objFreeDomainComponent->getServiceId();

						//set template variables needed for mail contents
						$arrMailVariables = array();
						$arrProductVispTags = product_visp_tags_get_all();
						$arrMailVariables['strCustomerName'] = $objFreeDomainComponent->strUsername;
						$arrMailVariables['strWebsite'] = $objFreeDomainComponent->objDomain->getDomain();
						$arrMailVariables['strRealm'] =$objFreeDomainComponent->objDomain->getRealm();
						$arrMailVariables['strTechSupportNo'] = $arrProductVispTags['madasafish']['support_phone_number'];
						//send  mail to customer
						mailer_send_customer_mail($intServiceId, '/local2/data/mis/madasafish/latest/myaccountdx/internet/domain-name/templates/activation_email.txt', $arrMailVariables, TRUE, TRUE);

						// Create ticket on customer account
						$strTicketBody = "Registration of domain " . $arrDomain['domain'] . " complete.";
						tickets_ticket_add ("Script", $objFreeDomainComponent->getServiceId(), "", "", "Closed", 0, $strTicketBody);
					}
				}
			}

			unset($arrDomains);

			// Now try to re-submit domains that has stucked in queue-activate state because Nominet failed to return responce for registration request
			$arrResubmitedDomains = self::reSubmitDomains($arrDomainComponentMapping);
		}

		//Get the queued-destroy components
		$strQuery = 'SELECT cmfdn.vchDomainName,cmfdn.intComponentID,c.status FROM tblConfigMaafFreeDomainName cmfdn INNER JOIN components c ON cmfdn.intComponentID = c.component_id WHERE c.status = '. "'queued-destroy'";
		$resResult = self::doQuery($strQuery, 'userdata');

		$arrResult = PrimitivesResultsAsArrayGet($resResult);

		if (count($arrResult) > 0) {

			$strQueryToExecuteInDomains = 'SELECT * FROM cashew.domain WHERE status IN ("Detagged", "Transferred Out") AND domain IN (';
			$arrDestroyedDomainsFromDomains = MAAF_Component_FreeDomainName::destroyDomains($strQueryToExecuteInDomains, $arrResult);


			$strQueryToExecuteInDomainsAudit = 'SELECT * FROM cashew.domain_audit WHERE status IN ("Detagged", "Transferred Out") AND domain IN (';
			$arrDestroyedDomainsFromDomainsAudit = MAAF_Component_FreeDomainName::destroyDomains($strQueryToExecuteInDomainsAudit, $arrResult);
		}

		$arrProcessedDomainsInfo = array(
						'arrActivatedDomains'                 => $arrActivatedDomains,
						'arrResubmitedDomains'				  => $arrResubmitedDomains,
						'arrDestroyedDomainsFromDomains'      => $arrDestroyedDomainsFromDomains,
						'arrDestroyedDomainsFromDomainsAudit' => $arrDestroyedDomainsFromDomainsAudit
		);

		$arrProcessedDomainsInfo = array_filter($arrProcessedDomainsInfo);

		return $arrProcessedDomainsInfo;

	} // end of method processDomains()

	/**
	 * Method returns the Transfer out fee for a particular component excluding VAT.
	 *
	 * <AUTHOR> <<EMAIL>>
	 *
	 * @param integer $intServiceComponentId the service component id of a component
	 *
	 * @return array $arrTransferOutFee associative array containing the transfer out fee for that component excluding VAT.
	 */
	public static function getTransferOutFee($intServiceComponentId)
	{
	    $connection = get_named_connection_with_db('product');

	    $strQuery = "SELECT decTransferOutFeeExVatPence as floTransferOutFeeExVatPence FROM tblMaafDomainConfig WHERE intServiceComponentId = $intServiceComponentId";

	    if (false !== ($resGetComponent = PrimitivesQueryOrExit($strQuery, $connection, 'MAAF_Component_FreeDomainName::getTransferOutFee', true, true)))
	    {
	        if (PrimitivesNumRowsGet($resGetComponent) > 0)
	        {
	            $floTransferOutFee = PrimitivesResultGet($resGetComponent, 'floTransferOutFeeExVatPence');
	        }
	    }

	    return $floTransferOutFee;
	}

	/**
	 * Method checks if there is no corresponding entry for
	 * username@realm in cashew.domain table
	 *
	 * <AUTHOR> Bargiel <<EMAIL>>
	 *
	 * @throws Exception
	 *
	 * @return void
	 */
	public function isDomainEmpty()
	{
		// Very simple validation
		$this->validateDomainObject();

		return $this->objDomain->getMailId() == null;

	} // end of method isDomainEmpty()

	/**
	 * Method executes query
	 *
	 * @param unknown_type $strSQLQuery
	 * @param unknown_type $strMessage
	 */
	protected static function doQuery($strSQLQuery, $strDatabase)
	{
		if (!isset(self::$arrDBHandle[$strDatabase])) {

			self::$arrDBHandle[$strDatabase] = get_named_connection_with_db($strDatabase);

		}

		if (!$resResult = PrimitivesQueryOrExit($strSQLQuery, self::$arrDBHandle[$strDatabase], $strMessage, false)) {
			echo mysql_error();
			throw new Exception('Error during database query');

		}

		return $resResult;

	} // end of method doQuery()

	/**
	 * Method validates domain object
	 *
	 * <AUTHOR> Bargiel <<EMAIL>>
	 *
	 * @throws Exception
	 *
	 * @return void
	 */
	protected function validateDomainObject()
	{
		if (!$this->objDomain instanceof BrightViewDomain) {
			throw new Exception ('Invalid domain object');
		}
	} // end of method validateDomainObject()

	/**
	 * Function to destroy a domain from domain table or domain_audit table
	 *
	 * @param string $strQuery
	 * @param array $arrDomains
	 * @return array $arrDestroyedDomains
	 */
	public function destroyDomains($strQuery, $arrDomains)
	{
		// Upper limit for destroy method failure 
		$intDestroyMethodFailureCount = 0;
		
		//Holds failed serviceids
		$arrFailedServiceId = array();
	
		foreach($arrDomains as $arrDomain) {
			$strQuery .= '?,';
			$arrDomainNames[] = mysql_real_escape_string($arrDomain['vchDomainName']);
			$arrDomainComponentMapping[$arrDomain['vchDomainName']] = $arrDomain['intComponentID'];
		}

		$strQuery = substr($strQuery, 0, strlen($strQuery) - 1) . ')';
		$stmt = BVDB::db()->prepare($strQuery);
		$stmt->execute($arrDomainNames);

		$arrDomains = array();
		//store Domain names to an array.
		while ($row = $stmt->fetch()) {

			$arrDomains[] = $row;
		}

		if (count($arrDomains) > 0) {

			foreach($arrDomains as $arrDomain) {
				
				$bolDestroyDomain  = false;
				
				// Destroy component
				$objFreeDomainComponent = new MAAF_Component_FreeDomainName($arrDomainComponentMapping[$arrDomain['domain']]);

				if ($objFreeDomainComponent->getComponentID()) {
					
					try{
						
						$bolDestroyDomain =  $objFreeDomainComponent->destroy('destroy');
					}
					catch(Exception $e){
						
						if( $intDestroyMethodFailureCount >= self::DESTROY_FAILURE_MAXIMUM ) {
							
							$strServiceIds = implode(", ", $arrFailedServiceId);
							
							$strProblemText = self::DESTROY_FAILURE_MAXIMUM." or more times the destory() method failed for the serviceids"
							                  .$strServiceIds." Raising an autoproblem for a developer to trace the cause";
							                  
							pt_raise_autoproblem('MAAF Domain Transfer Failed', 'MAAF Domain Transfer Failed', $strProblemText, $strProblemText);
							
							throw new Exception('MAAF_CProduct_Webmail::getProductByProductTypeHandle() failed for the service_ids'.$strServiceIds);
						}
						else{
							
							$arrFailedServiceId[] = $objFreeDomainComponent->getServiceId();
							$intDestroyMethodFailureCount++;
						}
						
					}
					
					if ($bolDestroyDomain) {

						$arrDestroyedDomains[] = $arrDomain['domain'];
					}

					//Create ticket on customer account
					$strTicketBody = "Transfer of domain " . $arrDomain['domain'] . " is being processed.";
					tickets_ticket_add ("Script", $objFreeDomainComponent->getServiceId(), "", "", "Closed", 0, $strTicketBody);
				}
			}
		}

	return $arrDestroyedDomains;
	} //end of function destroyDomains()

	function isPendingAction($intServiceId)
	{
		$strQuery = "SELECT component_id
		               FROM components
		              WHERE component_type_id = 533
		                AND status = 'queued-destroy'
		                AND service_id = $intServiceId ";

		$resResult = self::doQuery($strQuery, 'userdata');

		$arrResult = PrimitivesResultsAsArrayGet($resResult);

		if(count($arrResult) > 0)
		{
			return TRUE;
		}

		return FALSE;


	}

	/**
	 * Function to populate tblConfigMaafFreeDomainName for migrated accounts
	 * <AUTHOR> Mohanan <<EMAIL>>
	 *
	 * @return array $arrDomainsToPopulate
	 */
	public static function populateDomains()
	{
		//get all the active domain components
		$strQuery = "SELECT cmp.component_id, cs.intMailId
					 FROM components cmp
					 INNER JOIN tblConfigSoulstone cs
					 ON cmp.service_id = cs.intServiceId
					 WHERE cmp.status = 'active' and cmp.component_type_id=533";
		$resResult = self::doQuery($strQuery, 'userdata');
		$arrResult = PrimitivesResultsAsArrayGet($resResult);
		unset($resResult);
		$arrDomainsToPopulate = array();

		if (count($arrResult) > 0) {
			//get the domain name from cashew.domain by mail_id
			$strQuery = 'SELECT * FROM cashew.domain WHERE mailid IN (';

			foreach ($arrResult as $arrComponents) {
				$strQuery .= '?,';
				$arrDomainComponentMapping[$arrComponents['intMailId']] = $arrComponents['component_id'];
				$arrEmailIds[] = $arrComponents['intMailId'];
			}

			$strQuery = substr($strQuery, 0, strlen($strQuery) - 1).')';
			$stmt = BVDB::db()->prepare($strQuery);
			$stmt->execute($arrEmailIds);

			while ($row = $stmt->fetch()) {

				$arrDomainsToPopulate[$i]['intComponentId'] = $arrDomainComponentMapping[$row['mailid']];
				$arrDomainsToPopulate[$i]['strDomain'] = $row['domain'];
				$i++;
			}
			// poplulate tblConfigMaafFreeDomainName
			$strQuery = 'INSERT INTO tblConfigMaafFreeDomainName (intComponentID, vchDomainName) VALUES ';

			if (count($arrDomainsToPopulate) > 0) {

				foreach ($arrDomainsToPopulate as $arrDomainToPopulate) {

					$strQuery .= '(' . $arrDomainToPopulate['intComponentId'] . ',"'.$arrDomainToPopulate['strDomain'] . '"),';
				}

				$strQuery = substr($strQuery, 0, strlen($strQuery) - 1);
				$resResult = self::doQuery($strQuery, 'userdata');
				unset($resResult);
			}
		}
		//return populted domains
		return $arrDomainsToPopulate;
	} //end of function populateDomains()

} // end of class MAAF_Component_FreeDomainName
