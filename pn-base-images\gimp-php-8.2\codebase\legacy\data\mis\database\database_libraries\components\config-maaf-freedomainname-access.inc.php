<?php

if (!defined('COMPONENT_MAAF_FREEDOMAIN')) {

    require_once '/local/data/mis/database/database_libraries/components/component-defines.inc';
}

global $global_component_configurators;

$global_component_configurators[COMPONENT_MAAF_FREEDOMAIN] = 'config_FreeDomainName_configurator';

function config_FreeDomainName_configurator($intComponentID, $strAction)
{
    try {
        $arrFreeDomainNameComponent = userdata_component_get($intComponentID);
        MAAF_Component_FreeDomainName::configureComponent($intComponentID, $strAction);

    } catch (Exception $objException) {
        return false;
    }

    return true;
}