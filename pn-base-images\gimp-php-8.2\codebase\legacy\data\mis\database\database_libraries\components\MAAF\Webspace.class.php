<?php
/**
 * Webmail product component
 * 
 * @package components.MAAF
 * 
 * <AUTHOR> <k<PERSON><PERSON><EMAIL>>
 * 
 * @version $Id: Webspace.class.php,v 1.4 2008-10-23 10:30:07 ucholawo Exp $
 */

/**
 * Needed requirements
 */
require_once '/local/data/mis/database/database_libraries/components/CComponent.inc';
require_once '/local/data/mis/database/class_libraries/Brightview/BVDB.class.php';
require_once("/local/data/mis/database/database_libraries/product-access.inc");

/**
 * Component class for classes Webspace component 
 *
 * <AUTHOR> <<EMAIL>>
 */
class MAAF_Component_Webspace extends CComponent 
{
	/**
	 * Array for singleton representation of component instances
	 *
	 * @var array
	 */
	protected static $arrComponentInstance = null;
	
	private $arrComponentDetails = array();
	/**
	 * User username
	 * 
	 * @var string
	 */
	protected $strUsername = '';
	
	/**
	 * User realm
	 * 
	 * @var string
	 */
	protected $strRealm = '';
	
	/**
	 * Component id
	 * 
	 * @var string
	 */
	protected $intComponentId = 0;
	private $bolFtp = false;
	private $strFtpDomain = '';
	private $strDefaultDomain = '';
	private $strQuota = '';
	
	/**
	 * Comnponent type Id constance
	 */
	const COMPONENT_TYPE_ID = COMPONENT_MAAF_WEBSPACE;
	
	/**
	 * Coulstone FTP flag
	 */
	const FTP_FLAG = 4;
	
	public function getUsername() {return $this->strUsername;}
	public function getRealm() {return $this->strRealm;}
	public function getFtp() {return $this->bolFtp;}
	public function getFtpDomain() {return $this->strFtpDomain;}
	public function getDefaultDomain() {return $this->strDefaultDomain;}
	public function getQuota() {return $this->strQuota;}
	
	/**
	 * Method configures Webspace component
	 * 
	 * <AUTHOR> Sourkova <<EMAIL>>
	 * 
	 * @param integer $intComponentID  Component to configure
	 * @param integer $strAction       Configuration action
	 * 
	 * @throws Exception
	 * 
	 * @return void
	 */
	public static function configureComponent($intComponentID, $strAction)
	{
		$objWebspace = new MAAF_Component_Webspace($intComponentID);
		switch($strAction) {
			case "auto_configure":
				if ($objWebspace->getStatus() == 'unconfigured') {
					$objWebspace->enable();	
				} else if ($objWebspace->getStatus() == 'active') {
					$objWebspace->refreshCurrentState();
				}
				break;
		    case "auto_disable":
				$objWebspace->disable();
				break;
		    case "auto_enable":
				$objWebspace->enable();
			break;
		    case "auto_refresh":
				$objWebspace->refreshCurrentState();
				break;
		    case "auto_destroy":
		    	$objWebspace->destroy();
				break;				
		}
		
	} // end of method configureComponent
	
	/**
	 * Component constructor
	 * 
	 * <AUTHOR> Sourkova <<EMAIL>>
	 * 
	 * @param integer $intComponentID
	 */
	protected function __construct($intComponentID)
	{
		// Component details
		$this->arrComponentDetails = userdata_component_get($intComponentID);
		$arrUserdata = userdata_service_get($this->arrComponentDetails['service_id']);
		//
		$this->intComponentId = $intComponentID;
		$this->strUsername = $arrUserdata['username'];
		$this->strRealm	   = userdataGetFrameworkAuthRealm($this->arrComponentDetails['service_id'], $arrUserdata['username']);

		$this->refreshData();
		$this->validateWebspaceObject();
		
		parent::__construct($intComponentID);
		

	} // end if __construct
	


	/**
	 * Method gets Webspace status
	 * 
	 * <AUTHOR> Sourkova <<EMAIL>>
	 * 
	 * @return string
	 */
	public function getWebspaceStatus()
	{
		$this->getStatus();
	} // end of method getWebspaceStatus()
		
	/**
	 * Singleton method gets component based on component id
	 * 
	 * <AUTHOR> Sourkova <<EMAIL>>
	 * 
	 * @param integer $intComponentID Component id
	 * 
	 * @throws Exception
	 * 
	 * @return MAAF_Component_Webspace
	 */
	public static function getComponent($intComponentID)
	{
		if (isset(self::$arrComponentInstance[$intComponentID]) &&
			self::$arrComponentInstance[$intComponentID] instanceof MAAF_Component_Webspace) {
			return self::$arrComponentInstance[$intComponentID];		
		}
		
		$objTempInstance = new MAAF_Component_Webspace($intComponentID);
		
		if ($objTempInstance->getComponentID()=='') {
			throw new Exception("Component $intComponentID has not been found"); 
		}
		
		self::$arrComponentInstance[$intComponentID] = $objTempInstance;
		
		return self::$arrComponentInstance[$intComponentID];
		 
	} // end of method getComponent()
	
	/**
	 * Methods gets component based on component id
	 * 
	 * <AUTHOR> Sourkova <<EMAIL>>
	 * 
	 * @param integer $intServiceId Service Id
	 * 
	 * @return MAAF_Component_Webspace
	 */
	public static function getComponentByServiceId($intServiceId)
	{
		$objComponent = parent::getComponentByServiceComponentId($intServiceId, self::COMPONENT_TYPE_ID);
		
		$intComponentID = $objComponent->getComponentID();
		
		return self::getComponent($intComponentID);
	} // end of method getComponentByServiceId
	
	/**
	 * Method refreshes component current status
	 *
	 * <AUTHOR> Sourkova <<EMAIL>>
	 * 
	 * @return boolean False if the component is destroyed, otherwise true
	 */
	public function refreshCurrentState()
	{
		// if we're in a queued state attempt to action it 
		// breaks show deliberate fall throughs.
		switch($this->m_strStatus)
		{
			case 'queued-activate':
			case 'queued-reactivate':
				return $this->enable();
				break;
				
			case 'queued-deactivate':
				return $this->disable();
				break;
				
			case 'queued-destroy':
				return $this->destroy();
				break;
	
			case 'active':
			case 'deactive':
				// Nothing to do
				break;
			default:
				//not in a queued state
				break;
		}
		
	} // end of method refreshCurrentState()
	
	/**
	 * Method enables the component
	 *
	 * <AUTHOR> Sourkova <<EMAIL>>
	 * 
	 * @return boolean False if the component is destroyed, otherwise true
	 */
	public function enable()
	{
		$this->validateWebspaceObject();
		
		// Set status to enable and log operation
		if (!$this->canBeEnabled())
		{
			return -1;
		}

		$this->prvSetStatus('queued-activate');
		
		$this->enableFtp();

		$this->prvSetStatus('active');

		if ($this->getStatus() != 'active')
		{
			return -1;
		}
			
		return 0; 
		
	} // end of method enable()

	/**
	 * Method disables the component
	 * 
	 * <AUTHOR> Sourkova <<EMAIL>>
	 * 
	 * @return boolean False if the component is destroyed, otherwise true
	 */
	public function disable()
	{
		$this->validateWebspaceObject();
		
		$this->prvSetStatus('queued-activate');
		
		$this->disableFtp();
		
		$this->prvSetStatus('deactive');
	} // end of method disable()
	
	/**
	 * Method destroys the component
	 * 
	 * <AUTHOR> Sourkova <<EMAIL>>
	 * 
	 * @return boolean False if the component is destroyed, otherwise true
	 */
	public function destroy()
	{
		// Disable Webspace
		if( $this->objWebspace)
		$this->objWebspace->disable();
		
		// Destroy
		$this->prvSetStatus('destroyed');
	} // end of method destroy()	
	
	/**
	 * Enable FTP soulstone flag
	 *
	 * <AUTHOR> Sourkova <<EMAIL>>
	 */
	public function enableFtp()
	{
		$this->validateWebspaceObject();
		$objPDOStatement = BVDB::db()->prepare('UPDATE passwd SET options=options | ? WHERE username = ? and realm = ?');
		$objPDOStatement->execute(array(self::FTP_FLAG, $this->strUsername, $this->strRealm));
		$this->bolFtp = true;
	}
	
	/**
	 * Disable FTP soulstone flag
	 *
	 * <AUTHOR> Sourkova <<EMAIL>>
	 */
	public function disableFtp()
	{
		$this->validateWebspaceObject();
		$objPDOStatement = BVDB::db()->prepare('UPDATE passwd SET options=options & ~? WHERE username = ? and realm = ?');
		$objPDOStatement->execute(array(self::FTP_FLAG, $this->strUsername, $this->strRealm));
		$this->bolFtp = false;
	}
	
	/**
	 * Method validates webspace object
	 * 
	 * <AUTHOR> Sourkova <<EMAIL>>
	 * 
	 * @throws Exception
	 * 
	 * @return void
	 */
	protected function validateWebspaceObject()
	{
		if (!$this->strUsername) {
			throw new Exception ('No username found for Component id: '.$this->intComponentId);
		}
		if (!$this->strRealm) {
			throw new Exception ('No realm found for Component id: '.$this->intComponentId);
		}
	} // end of method validateDomainObject()

	/**
	 * Determines whether FTP flag is set or not in Soulstone
	 *
	 * @param string $strUsername
	 * @param string $strRealm
	 * @return bool
	 */
	public static function isFtpEnabled($strUsername, $strRealm)
	{
		return $this->bolFtp;
	}
	
	private function refreshData()
	{
		$stmt = BVDB::db()->prepare('SELECT username, options+0 as options, web_quota FROM passwd WHERE username = ? AND realm = ?');
		$stmt->execute(array($this->strUsername, $this->strRealm));
		$objResult = $stmt->fetch(PDO::FETCH_ASSOC);
		
		$this->bolFtp =  $objResult['options'] & self::FTP_FLAG;
		$this->strQuota = $objResult['web_quota'];
		
		$arrVispDetails = product_users_visp_configuration_get($this->arrComponentDetails['service_id']);
		$this->strFtpDomain = $arrVispDetails['default_customer_ftp_domain'];
		$this->strDefaultDomain = $arrVispDetails['default_customer_homepages_domain'];
	}
	

} // end of class MAAF_Component_Webspace
