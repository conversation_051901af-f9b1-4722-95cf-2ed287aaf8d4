<?php
    require_once ('/local/data/mis/database/application_apis/wlr/spg/Wlr_OrderType.class.php');

    require_once (SQL_PRIMITIVES_LIBRARY);
    require_once (TICKETS_ACCESS_LIBRARY);
    require_once (SMARTY_LIBRARY);

    require_once ('/local/data/mis/database/database_libraries/components/CWlrProduct.inc');
    require_once ('/local/data/mis/database/database_libraries/CoreObjects/Financial/CProductComponentScheduledPayment.inc');
    require_once ('/local/data/mis/database/database_libraries/CoreObjects/Financial/CProductComponentPaymentScheduler.inc');

    define ('WLR_DEBTFEATURE_APPLY_OCB_EXCEPT',  0);
    define ('WLR_DEBTFEATURE_REMOVE_OCB_EXCEPT', 1);
    define ('WLR_DEBTFEATURE_APPLY_RTCC',        2);
    define ('WLR_DEBTFEATURE_REMOVE_RTCC',       3);
    define ('WLR_DEBTFEATURE_APPLY_TOS',         4);
    define ('WLR_DEBTFEATURE_REMOVE_TOS',        5);

    class Wlr_OrderType_DebtManagement extends Wlr_OrderType
    {
        var $intDebtFeatureID;
        var $intDebtMgmtOrderKeyID = 0;

        private static $arrDebtManagementTypesMap = array(
            'OCB'       => array(
                'start' => WLR_DEBTFEATURE_APPLY_OCB_EXCEPT,
                'stop'  => WLR_DEBTFEATURE_REMOVE_OCB_EXCEPT
            ),
            'RTCC'      => array(
                'start' => WLR_DEBTFEATURE_APPLY_RTCC,
                'stop'  => WLR_DEBTFEATURE_REMOVE_RTCC
            ),
            'TOS'       => array(
                'start' => WLR_DEBTFEATURE_APPLY_TOS,
                'stop'  => WLR_DEBTFEATURE_REMOVE_TOS
            )
        );


////////////////////////////////////////////////////////////////////////////////
//
// Constructor
//

        function Wlr_OrderType_DebtManagement($intServiceID, $strPostcode, $strCliNumber, $intComponentID)
        {
            $this->Wlr_OrderType($intServiceID, $strPostcode, $strCliNumber, $intComponentID);

            // this member is actually defined in Wlr_OrderType (this class' base class)
            // opted to not have a SetOrderType method to hint that this is an internal
            // structure *NOT* to be tinkered with.

            $this->intOrderType = WLR_ORDERTYPE_DEBT_MGMT;
        }

////////////////////////////////////////////////////////////////////////////////
//
// Static methods
//

        /*
            ------------------------------
            IsDebtManagementOrderPending()
            ------------------------------

            Determines whether a given product component instance ID is registered as
            having an outstanding debt management order.

            Returns false on *error*, (database error, most likely) 0 if no debt management
            order is pending, and >0 represents the order ID of the debt management order,
            should one exist.
        */

        function IsDebtManagementOrderPending($intProductComponentInstanceID, $arrOrderStatuses = array('COMPLETED') )
        {
            $strOrderStatuses = implode("', '", $arrOrderStatuses);

            $strQuery = "SELECT dm.intDebtManagementOrdersID
                         FROM tblDebtManagementOrders AS dm
                         WHERE dm.intProductComponentInstanceID = '{$intProductComponentInstanceID}'
                         AND dm.intStartDebtManagementOrderID != 0
                         AND dm.intEndDebtManagementOrderID = 0
                         AND EXISTS
                         (
                            SELECT *
                            FROM dbWLRProvisioning.tblWlrOrderHistory oh
                            WHERE oh.intWlrOrderId = dm.intStartDebtManagementOrderID
                            AND oh.intWlrOrderStatusId IN (
                               SELECT intWlrOrderStatusId
                               FROM dbWLRProvisioning.tblWlrOrderStatus
                               WHERE vchHandle IN ('{$strOrderStatuses}')
                            )
                         )";

            $db = get_named_connection_with_db('wlr_prov_reporting');

            $res = PrimitivesQueryOrExit($strQuery, $db, 'IsDebtManagementOrderPending', FALSE);

            if (!$res)
            {
                return false;
            }

            $intStartDebtManagementOrderID = PrimitivesResultGet($res, 'intDebtManagementOrdersID');

            return ($intStartDebtManagementOrderID === false) ? 0 : $intStartDebtManagementOrderID;
        }



        public static function getDebtTypeByProduct($objWlrProduct, $intExistingDebtOrderHint = NULL)
        {
            if (! $objWlrProduct instanceof CWlrProduct) {

                throw new Exception('supplied parameter is not a CWlrProduct object');
            }


            $dbhConn = get_named_connection_with_db('product_reporting');

            if (FALSE == $dbhConn) {

                throw new Exception(__METHOD__ . ': failed to get connection to database');
            }


            if (is_null($intExistingDebtOrderHint)) {

                // Pick whichever one is current and non-expired

                $intServiceComponentProductId = $objWlrProduct->getServiceComponentProductId();

                $strQuery = "SELECT
                                dmt.vchHandle
                             FROM
                                tblWlrDebtManagementType AS dmt
                             INNER JOIN tblWlrDebtManagement AS dm
                                ON dm.intWlrDebtManagementTypeId = dmt.intWlrDebtManagementTypeId
                             WHERE
                                dm.intServiceComponentProductId = {$intServiceComponentProductId}
                                AND dm.dteEffectiveTo IS NULL";

            } else {

                // Pick whichever matches the order we're hinting with

                $strQuery = "SELECT
                                dmt.vchHandle
                             FROM
                                dbWLRProvisioning.tblWlrOrderHistory AS oh
                             INNER JOIN dbWLRProvisioning.tblWlrOrderStatus AS os
                                ON os.intWlrOrderStatusId = oh.intWlrOrderStatusId
                             INNER JOIN dbWLRProvisioning.tblOrderFeature AS orf
                                ON orf.intOrderID = oh.intWlrOrderId
                             INNER JOIN userdata.tblProductComponentInstance AS pci
                                ON pci.intProductComponentInstanceID = orf.intProductComponentInstanceID
                             INNER JOIN userdata.components AS c
                                ON c.component_id = pci.intComponentID
                             INNER JOIN tblServiceComponentProduct AS scp
                                ON scp.intServiceComponentID = c.component_type_id
                             INNER JOIN tblWlrDebtManagement AS dm
                                ON dm.intServiceComponentProductID = scp.intServiceComponentProductID
                             INNER JOIN tblWlrDebtManagementType AS dmt
                                ON dmt.intWlrDebtManagementTypeId = dm.intWlrDebtManagementTypeId
                             WHERE
                                os.vchHandle = 'NEW'
                                AND oh.intWlrOrderId = {$intExistingDebtOrderHint}
                                AND oh.dtmAdded >= dm.dteEffectiveFrom AND
                                   (oh.dtmAdded < dm.dteEffectiveTo OR dm.dteEffectiveTo IS NULL)";

                // Note: as dtmAdded is a DATETIME and dte* are DATE data types, we have a < operator
                // to test dteEffectiveTo. as it's actually set to a day in the future to catch
                // midnight boundaries. The alternative to this would be to use DATE/DATETIME functions
                // in the query, which would probably hurt performance.
            }


            $resResult = PrimitivesQueryOrExit($strQuery, $dbhConn, __METHOD__, FALSE);

            if (FALSE == $resResult) {

                throw new Exception('query failed when fetching debt management details');
            }


            // We should only ever get ONE row back here. If we don't get one row then something
            // in the database isn't configured correctly.

            $intNumRows = PrimitivesNumRowsGet($resResult);

            if ($intNumRows != 1) {

                $intComponentId = $objWlrProduct->getComponentID();

                $strMessage = "mis-configured data when determining callbarring type; {$intNumRows} rows detected instead of 1. " .
                              "Component ID {$intComponentId}, existing debt order ID '{$intExistingDebtOrderHint}'";

                throw new Exception($strMessage);
            }


            $strDebtManagementType = PrimitivesResultGet($resResult, 'vchHandle');

            if (!isset(self::$arrDebtManagementTypesMap[$strDebtManagementType])) {

                $intComponentId = $objWlrProduct->getComponentID();

                $strMessage = "handle-to-constant mapping incorrect; '{$strDebtManagementType}' unrecognised. " .
                              "Component ID {$intComponentId}, existing debt order ID '{$intExistingDebtOrderHint}'";

                throw new Exception($strMessage);
            }


            $arrData = self::$arrDebtManagementTypesMap[$strDebtManagementType];
            $arrData['type'] = $strDebtManagementType;

            return $arrData;
        }



        public static function lookupStartStopOrderIdFromKeyId($intKeyId)
        {
            // using coredb master, as this could be in a time-critical situation

            $dbhConn = get_named_connection_with_db('wlr_prov');

            if (FALSE == $dbhConn) {

                throw new Exception(__METHOD__ . ': failed to get connection to database');
            }


            $strQuery = "SELECT
                            intStartDebtManagementOrderID,
                            intEndDebtManagementOrderID
                         FROM
                            tblDebtManagementOrders
                         WHERE
                            intDebtManagementOrdersID = {$intKeyId}";

            $resResult = PrimitivesQueryOrExit($strQuery, $dbhConn, __METHOD__, FALSE);

            if (FALSE == $resResult) {

                throw new Exception('query failed when fetching debt management start/stop pair');
            }


            $arrData = PrimitivesResultGet($resResult);

            if (empty($arrData)) {

                throw new Exception("no data found for key ID {$intKeyId}");
            }


            return $arrData;
        }


////////////////////////////////////////////////////////////////////////////////
//
// Public methods
//

        function SetDebtFeatureID($intDebtFeatureID)
        {
            $this->intDebtFeatureID = $intDebtFeatureID;

            // hack: change the type of the debt management object depending on whether its
            // starting or stopping debt management. This removes the need to have two classes -
            // one for starting debt management, the other stopping it.
            // Not too keen on it as it has meant duplicating data. :/

            if (in_array($intDebtFeatureID, array(WLR_DEBTFEATURE_APPLY_OCB_EXCEPT, WLR_DEBTFEATURE_APPLY_RTCC, WLR_DEBTFEATURE_APPLY_TOS)))
            {
                $this->intOrderType = WLR_ORDERTYPE_DEBT_MGMT_START;
            }
            else if (in_array($intDebtFeatureID, array(WLR_DEBTFEATURE_REMOVE_OCB_EXCEPT, WLR_DEBTFEATURE_REMOVE_RTCC, WLR_DEBTFEATURE_REMOVE_TOS)))
            {
                $this->intOrderType = WLR_ORDERTYPE_DEBT_MGMT_STOP;
            }
        }

////////////////////////////////////////////////////////////////////////////////

        function GetDebtFeatureID()
        {
            return $this->intDebtFeatureID;
        }

////////////////////////////////////////////////////////////////////////////////

        function GetDebtFeatureString()
        {
            // These are the possible debt management request types:
            // We are only going to be using 'Apply OCB Except' and 'Remove OCB Except'. (I think)
            // Should this array be located elsewhere instead?

            // should actually be static within the class, rather than in an object

            $arrDebtMgmtFeature = array(
                WLR_DEBTFEATURE_APPLY_OCB_EXCEPT  => 'Apply OCB Except',
                WLR_DEBTFEATURE_REMOVE_OCB_EXCEPT => 'Remove OCB Except',
                WLR_DEBTFEATURE_APPLY_RTCC        => 'Apply RTCC',
                WLR_DEBTFEATURE_REMOVE_RTCC       => 'Remove RTCC',
                WLR_DEBTFEATURE_APPLY_TOS         => 'Apply TOS',
                WLR_DEBTFEATURE_REMOVE_TOS        => 'Remove TOS'
            );

            return $arrDebtMgmtFeature[$this->intDebtFeatureID];
        }

////////////////////////////////////////////////////////////////////////////////

        /*
            -----------------------------
            SetDebtManagementOrderKeyID()
            -----------------------------
        */

        function SetDebtManagementOrderKeyID($intKeyID)
        {
            $this->intDebtMgmtOrderKeyID = $intKeyID;
        }

////////////////////////////////////////////////////////////////////////////////

        /*
            -----------------------------
            GetDebtManagementOrderKeyID()
            -----------------------------
        */

        function GetDebtManagementOrderKeyID()
        {
            return $this->intDebtMgmtOrderKeyID;
        }

////////////////////////////////////////////////////////////////////////////////

        /*
            ---------------------------
            RecordDebtManagementStart()
            ---------------------------
        */

        function RecordDebtManagementStart()
        {
            $intLRPCI = $this->GetLineRentProductComponentInstanceID();
            $intOrderID = $this->GetOrderID();

            $strQuery = "INSERT INTO tblDebtManagementOrders (intProductComponentInstanceID, intStartDebtManagementOrderID, dtmStart)
                         VALUES ('{$intLRPCI}', '{$intOrderID}', NOW())";

            $db = get_named_connection_with_db('wlr_prov');

            PrimitivesQueryOrExit($strQuery, $db, 'insert debt management start entry', FALSE);
        }

////////////////////////////////////////////////////////////////////////////////

        /*
            ---------------------------
            RecordDebtManagementStop()
            ---------------------------
        */

        function RecordDebtManagementStop()
        {
            $intOrderID = $this->GetOrderID();

            $strQuery = "UPDATE tblDebtManagementOrders
                         SET intEndDebtManagementOrderID='{$intOrderID}', dtmEnd=NOW()
                         WHERE intDebtManagementOrdersID='{$this->intDebtMgmtOrderKeyID}'";

            $db = get_named_connection_with_db('wlr_prov');

            PrimitivesQueryOrExit($strQuery, $db, 'update debt management stop entry', FALSE);
        }

////////////////////////////////////////////////////////////////////////////////

        /*
            -------------------------
            ApplyCallBarringCharges()
            -------------------------
        */

        function ApplyCallBarringCharges()
        {
            // don't do anything if it's not an apply debt management order
            if ($this->GetOrderType() != WLR_ORDERTYPE_DEBT_MGMT_START)
            {
                return false;
            }

            //call barring charges
            $dbhConnection = get_named_connection_with_db('dbProductComponents');
            $strQuery = 'SELECT
                            pci.intProductComponentInstanceID,
                            sc.name,
                            pc.vchHandle,
                            config.intCallBarringChargePence,
                            UNIX_TIMESTAMP(pcs.dteNextInvoice) AS uxtNextInvoice
                         FROM userdata.components c
                         INNER JOIN products.service_components AS sc
                            ON sc.service_component_id = c.component_type_id
                         INNER JOIN userdata.tblProductComponentInstance pci
                            ON pci.intComponentID = c.component_id
                         INNER JOIN dbProductComponents.tblProductComponent pc
                            ON pc.intProductComponentID = pci.intProductComponentID
                         INNER JOIN dbProductComponents.tblProductComponentConfig pcc
                            ON pcc.intProductComponentID = pc.intProductComponentID
                         INNER JOIN products.tblServiceComponentProduct scp
                            ON scp.intServiceComponentProductID = pcc.intServiceComponentProductID
                            AND c.component_type_id = scp.intServiceComponentID
                         INNER JOIN dbProductComponents.tblWlrLineRentConfig config
                            ON config.intProductComponentConfigId = pcc.intProductComponentConfigId
                         INNER JOIN userdata.tblProductComponentInstance pcs
                            ON pcs.intComponentID = c.component_id
                         INNER JOIN dbProductComponents.tblProductComponent pc2
                            ON pc2.intProductComponentID = pcs.intProductComponentID
                            AND pc2.vchHandle = "SUBSCRIPTION"
                         WHERE c.component_id = '.$this->GetComponentID();

            $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);
            $arrResult = PrimitivesResultGet($resResult);

            $intCallBarringChargePence =  $arrResult['intCallBarringChargePence'];

            if ($intCallBarringChargePence > 0) {

                $intProductComponentInstanceID =  $arrResult['intProductComponentInstanceID'];
                $uxtNextInvoice = $arrResult['uxtNextInvoice'];
                $strProductName = $arrResult['name'];

                $strDescription = $strProductName . ' Call Barring charges';

                $floCallBarringCharge = $intCallBarringChargePence / 100;

                $arrInvoiceItems[] = array('amount'      => $floCallBarringCharge,
                                           'description' => $strDescription,
                                           'gross'       => TRUE);

                $objPaymentScheduler = new CProductComponentPaymentScheduler($intProductComponentInstanceID, '');

                $objPaymentScheduler->addIncVatAmount($intCallBarringChargePence, $uxtNextInvoice,
                                                      $strDescription, time(), time());

                $intScheduledPaymentID = $objPaymentScheduler->m_intScheduledPaymentID;

                $strTicketBody = "The {$strDescription} of &pound;$floCallBarringCharge have been added to the subscription bill scheduled to be invoiced for " . date('jS F Y', $uxtNextInvoice) . '.';

                tickets_ticket_add ('Script', $this->GetServiceID(), '', '', 'Closed', 0, $strTicketBody);
            }


            return true;
        }

        /**
         * Add service notice to the account
         *
         * @return boolean
         */
        function AddServiceNoticeToAccount()
        {
            switch ($this->GetOrderType())
            {
                case WLR_ORDERTYPE_DEBT_MGMT_START:
                    $strTicketFilename = 'nuisance_call_outgoing_calls_stopped_service_notice.txt';
                    break;

                case WLR_ORDERTYPE_DEBT_MGMT_STOP:
                    $strTicketFilename = 'nuisance_call_outgoing_calls_reactivated_service_notice.txt';
                    break;

                default:             // if this ever happens then I don't know *whats* gone wrong!
                    return false;    // (well, I do, but you shouldn't be tinkering with the type of object once created)
            }

            $objSmarty = new Smarty();
            $objSmarty->compile_dir = SMARTY_COMPILE_DIR;
            $objSmarty->template_dir = '/local/www/database-admin/scripts/wlr/provisioning/templates/tickets';

            $strTicketBody = $objSmarty->fetch($strTicketFilename);

            tickets_ticket_add('Script', $this->GetServiceID(), '', '', 'Closed', 0, $strTicketBody);

            unset($objSmarty);

            return true;
        }
    }
