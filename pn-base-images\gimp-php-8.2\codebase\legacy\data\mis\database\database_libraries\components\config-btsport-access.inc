<?php
/**
 * BT Sport Access Configurator
 *
 * @package   LegacyCodebase
 * <AUTHOR> <<EMAIL>>
 * @copyright 2013 Plusnet
 *
 */

require_once '/local/data/mis/database/database_libraries/components/component-defines.inc';
require_once('/local/data/mis/database/database_libraries/sql_primitives.inc');

// Select the BT Sport component IDs
$dbhConn = get_named_connection_with_db('product');

$strQuery = 'SELECT sc.service_component_id
               FROM products.service_components sc
               WHERE sc.name LIKE \'%BT Sport%\'';

$refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Make BT Sport configurator entries');
$arrBTSportComponents = PrimitivesResultsAsArrayGet($refResult);

foreach ($arrBTSportComponents as $arrComponent) {

    $global_component_configurators[(int)$arrComponent['service_component_id']] =
                                   'config_btsport_configurator';
}

if (!isset($GLOBALS['global_component_configurators'])) {
    $GLOBALS['global_component_configurators'] = $global_component_configurators;
} else {
    foreach ($global_component_configurators as $intIndex => $strConfigurator) {
        $GLOBALS['global_component_configurators'][$intIndex] = $strConfigurator;
    }
}

/**
 * Config BTSport Auto Configure
 *
 * @param Int $componentId Component ID
 *
 * @return void
 */
function config_btsport_auto_configure($componentId)
{
    Components_Api::unconfigure($componentId, array());
}

/**
 * Config BTSport Auto Destroy
 *
 * @param Int $componentId Component ID
 *
 * @return void
 */
function config_btsport_auto_destroy($componentId)
{
    $component = userdata_component_get($componentId);

    switch ($component['status']) {
        case 'unconfigured':
        case 'active':
        case 'deactive':
            Components_Api::destroy($componentId, array());
            break;
        default:
            return;
            break;
    }
}

/**
 * Config BTSport Auto Enable
 *
 * @param Int $componentId Component ID
 *
 * @return void
 */
function config_btsport_auto_enable($componentId)
{
    $component = userdata_component_get($componentId);

    if ($component['status'] == 'deactive') {
        Components_Api::activate($componentId, array());
    }
}

/**
 * Config BTSport Auto Disable
 *
 * @param Int $componentId Component ID
 *
 * @return void
 */
function config_btsport_auto_disable($componentId)
{
    $component = userdata_component_get($componentId);

    if ($component['status'] == 'active') {
        userdata_component_set_status($componentId, 'deactive');
    }
}

/**
 * Config BTSport Configurator
 *
 * @param Int    $componentId Component ID
 * @param String $action      Action
 *
 * @return void
 */
function config_btsport_configurator($componentId, $action)
{
    switch ($action) {
        case 'auto_configure':
            config_btsport_auto_configure($componentId);
            break;
        case 'auto_disable':
            config_btsport_auto_disable($componentId);
            break;
        case 'auto_enable':
            config_btsport_auto_enable($componentId);
            break;
        case 'auto_destroy':
            config_btsport_auto_destroy($componentId);
            break;
        default:
            break;
    }
}
