<?php
/**
 * Core ServicePeriod
 *
 * @package    Core
 * @subpackage ServicePeriod
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2010 PlusNet
 * @version    git
 * @since      File available since 2010-06-21
 */

/**
 * Core ServicePeriod
 *
 * Representing a service period start and end time
 *
 * @package    Core
 * @subpackage ServicePeriod
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2010 PlusNet
 */
class Core_ServicePeriod
{
    /**
     * When the service period begins
     *
     * @var I18n_Date
     */
    protected $_start;

    /**
     * When the service period ends
     *
     * @var I18n_Date
     */
    protected $_end;

    /**
     * Set up a Core_ServicePeriod object
     *
     * @param I18n_Date $start When the service period begins
     * @param I18n_Date $end   When the service period ends
     *
     * @return Core_ServicePeriod
     */
    public function __construct(I18n_Date $start, I18n_Date $end)
    {
        if ($start->getTimestamp() > $end->getTimestamp()) {
            throw new InvalidArgumentException(
                "Start date '{$start->toMySql()}' cannot be after end date " .
                "'{$end->toMySql()}'"
            );
        }

        $this->_start = $start;
        $this->_end   = $end;
    }

    /**
     * Return the start datetime of the period
     *
     * @return I18n_Date
     */
    public function getStart()
    {
        return $this->_start;
    }

    /**
     * Return the end datetime of the period
     *
     * @return I18n_Date
     */
    public function getEnd()
    {
        return $this->_end;
    }

    /**
     * Return a day iterator for iterating over each day of the service period
     *
     * @return Core_ServicePeriod_DayIterator
     */
    public function getDayIterator()
    {
        return new Core_ServicePeriod_DayIterator($this);
    }
}
