<?php

	/**
	 * Configurator library for the credit card surcharge component
	 */

	if(!defined('COMPONENT_CREDIT_CARD_SURCHARGE') OR !defined('COMPONENT_NON_DD_SURCHARGE')) {

		require_once '/local/data/mis/database/database_libraries/components/component-defines.inc';
	}

	// Configurator for specific service_component_id
	$global_component_configurators[COMPONENT_CREDIT_CARD_SURCHARGE]  = 'config_credit_card_surcharge_configurator';
	$global_component_configurators[COMPONENT_NON_DD_SURCHARGE]  = 'config_credit_card_surcharge_configurator';


	// Hack to make sure configurators are in global scope
	if(!isset($GLOBALS['global_component_configurators']))
	{
		$GLOBALS['global_component_configurators'] = $global_component_configurators;
	}
	else
	{
		$GLOBALS['global_component_configurators'][COMPONENT_CREDIT_CARD_SURCHARGE] = $global_component_configurators[COMPONENT_CREDIT_CARD_SURCHARGE];
		$GLOBALS['global_component_configurators'][COMPONENT_NON_DD_SURCHARGE] = $global_component_configurators[COMPONENT_NON_DD_SURCHARGE];
	}


	/**
	 * Configurator called by all sorts of bits of code
	 *
	 * The simplest configurator imaginable - the component is either 'active' or
	 * 'destroyed'
	 *
	 * @param int $intComponentId
	 * @param string $strAction
	 * @access public
	 */
	function config_credit_card_surcharge_configurator($intComponentId, $strAction)
	{
		switch(strtolower($strAction)) {

		    case 'auto_destroy':
				userdata_component_set_status($intComponentId, 'destroyed');
			break;

		    default:
                // SALES-1801: default configurator now only has an 'unconfigured' option as we should no longer
                // be adding these components.  If we ever need to re-enable them, then this can change to 'active'
				userdata_component_set_status($intComponentId, 'unconfigured');
			break;

		}
	}

?>
