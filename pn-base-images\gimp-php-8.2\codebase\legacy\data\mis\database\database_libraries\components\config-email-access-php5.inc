<?php

require_once '/local/data/mis/database/crypt_config.inc';

/**
 * PHP5 specific configuration
 */
/////////////////////////////////////////////////////////////
// Function:  config_email_add
// Purpose:
// Arguments:
/////////////////////////////////////////////////////////////

function config_email_add($component_id, $type, $isp,
              $username, $login, $password,
              $redirect_to, $mlist_options, $delivery_method,
              $smtp_delivery_ip) {

    try {
        $objManager = new Mail_Manager($component_id);
        $arrArgs = array('component_id' => $component_id,
                 'type' => $type,
                 'isp' => $isp,
                 'username' => $username,
                 'login' => $login,
                 'password' => $password,
                 'redirect_to' => $redirect_to,
                 'mlist_options' => $mlist_options,
                 'delivery_method' => $delivery_method,
                 'smtp_delivery_ip' => $smtp_delivery_ip);

        // We need to set the status of the component to queued-activate, as the mailbox configuration part expects it.
        userdata_component_set_status($component_id, 'queued-activate');

        //make sure the default mailbox is fact missing before adding it to avoid duplicates

        $objMailbox = null;
        try {

        	$objMailbox = $objManager->getDefaultMailbox();
        } catch (Mail_Exception $e) {

        	$intMailboxID = $objManager->addMailbox($arrArgs);
        	$objManager->disableCatchAll($intMailboxID);
        	$objMailbox = $objManager->getDefaultMailbox();
        }

        $objManager->setDefaultSpamFilterType();

        if (!config_email_partner_has_domain($component_id)) {

                //add aliases
                $objMailbox->addAlias('postmaster');
        }

        // All was OK, set the component status to active
        userdata_component_set_status($component_id, 'active');
        $objManager->refresh();

        return true;
    }
    catch(Exception $e) {
        return false;
    }
}

function config_email_partner_has_domain($component_id)
{
	$component = userdata_component_get($component_id);
	$service_id = $component['service_id'];
	$objDomainHelper = Reseller_DomainHelper::getByServiceId(new Int($service_id));
	return is_null($objDomainHelper) ? FALSE : TRUE;
}

/////////////////////////////////////////////////////////////
// Function:  config_email_alter
// Purpose:
// Arguments:
/////////////////////////////////////////////////////////////

function config_email_alter($email_id, $login, $password,
                            $redirect_to, $mlist_options, $delivery_method,
                            $smtp_delivery_ip) {
    try {
        $objMailbox = Mail_Manager::findMailboxById($email_id);

	$objMailbox->login = $login;
        $objMailbox->password = $password;
        $objMailbox->redirect_to = $redirect_to;
        $objMailbox->mlist_options = $mlist_options;
        $objMailbox->delivery_method = $delivery_method;
        $objMailbox->smtp_delivery_ip = $smtp_delivery_ip;
        if($objMailbox->save()) {
            userdata_component_set_status($objMailbox->component_id, 'active');
            return true;
        }
    }
    catch (Exception $e) {
        return false;
    }

    // save

} // function config_email_alter

    /////////////////////////////////////////////////////////////
    // Function:  config_email_get_by_login
    // Purpose:
    // Arguments:
    /////////////////////////////////////////////////////////////

    function config_email_get_by_login($component_id, $login) {

        $connection = get_named_connection ("userdata");

        $component_id = addslashes ($component_id);
        $login = addslashes ($login);

        $query = 'SELECT email_id, component_id, box_number, type, isp, username, login, password, redirect_to, mlist_options, delivery_method, smtp_delivery_ip, last_access, status ' .
                   'FROM config_email ' .
                  "WHERE component_id = '$component_id' " .
                    "AND login = '$login' " .
                    'AND status IN ("queued-activate", "active", "queued-reactivate")';

        $result = mysql_query($query, $connection)
            or report_error(__FILE__, __LINE__, mysql_error($connection));

        $mailbox = mysql_fetch_array($result, MYSQL_ASSOC);

		$mailbox['password'] = Crypt_Crypt::decrypt($mailbox['password'], 'config_email');

        mysql_free_result($result);

        return $mailbox;
    }


    /////////////////////////////////////////////////////////////
    // Function:  config_email_get_by_component
    // Purpose:
    // Arguments:
    /////////////////////////////////////////////////////////////

    function config_email_get_by_component($component_id) {

        $connection = get_named_connection("userdata");

        $component_id = addslashes ($component_id);

        $query = 'SELECT email_id, component_id, box_number, type, isp, username, login, password, redirect_to, mlist_options, delivery_method, smtp_delivery_ip, last_access, status ' .
                   'FROM config_email ' .
                  "WHERE component_id = '$component_id' " .
                    'AND status IN ("active", "queued-activate", "queued-reactivate") ' .
               'ORDER BY box_number';

        $result = mysql_query($query, $connection)
            or report_error(__FILE__, __LINE__, mysql_error($connection));

		$objCrypt = Crypt_Crypt::getCryptProvider('config_email');

		while ($mailbox = mysql_fetch_array($result, MYSQL_ASSOC)) {

			if($objCrypt->canDecrypt()) {
				$mailbox['password'] = $objCrypt->decrypt($mailbox['password'], 'config_email');
			}

            $mailboxes[] = $mailbox;
		}

		$objCrypt->release();

        mysql_free_result($result);

        return $mailboxes;

    } // function config_email_get_by_component


/////////////////////////////////////////////////////////////
// Function:  config_email_auto_configure
// Purpose:   'unconfigured' -> 'queued-activate' state
//      transition handler for auto-configuration
// Arguments: $component_id
/////////////////////////////////////////////////////////////

function config_email_auto_configure($component_id, $arrOptions = null) {

    // Find username and password
    $component = userdata_component_get($component_id);
    $service = userdata_service_get($component['service_id']);

    $isp      = $service['isp'];
    $username = $service['username'];
    $login    = $arrOptions['strAlias'] ? $arrOptions['strAlias'] : $service['username'];
    $password = $service['password'];

    Mail_Manager::setContext('config_email_auto_configure');

    switch ($component['status']) {

    case 'unconfigured':
        // Create the configuration
        config_email_add($component_id, 'default', $isp, $username, $login, $password, '', '', 'pop3', '');

        break;

    case 'active':
        break;

    case 'queued-activate':
    case 'queued-reactivate':
        return reactivate_individual_mailboxes($component_id, $component['service_id'], $arrOptions);
	break;
    case 'queued-destroy':
        config_email_auto_destroy($component_id);
    case 'destroyed':
        // These aren't the droids you're looking for
        return;
        break;

    default:
        // The sky is falling!
        break;
    }
} // function config_email_auto_configure


/////////////////////////////////////////////////////////////
// Function:  config_email_auto_destroy
// Purpose:   * -> 'queued-destroy' state
//      transition handler for auto-destruction
// Arguments: $component_id
/////////////////////////////////////////////////////////////

function config_email_auto_destroy($component_id) {

    $component = userdata_component_get($component_id);

    switch ($component['status']) {

// Free transition to 'destroyed', do not pass Go.
    case 'unconfigured':
        userdata_component_set_status($component_id, 'destroyed');
        break;
// We have or may have a configuration, pass to the reaper
    case 'active':
    case 'queued-activate':
    case 'queued-reactivate':
    case 'queued-deconfigure':
    case 'queued-destroy':
    case 'destroyed':
        userdata_component_set_status($component_id, 'queued-destroy');
        try {

            $objManager = new Mail_Manager($component_id);
            if ($objManager->destroy()) {
                userdata_component_set_status($component_id, 'destroyed');
            }
            return true;
        }
        catch (Exception $e) {
            return false;
        }
        break;
        // There already

    default:
        break;
    }

} // function config_email_auto_destroy

    function config_email_auto_refresh($component_id, $arrOptions = null) {

        $component = userdata_component_get($component_id);

        switch ($component['status']) {

            // Nothing to do here
            case 'unconfigured':
                config_email_auto_configure($component_id);
                break;
            case 'queued-destroy':
                config_email_auto_destroy($component_id);
                break;
            case 'destroyed':
                break;

            // Needs update
            case 'queued-activate':
            case 'queued-reactivate':
            case 'active':
                return reactivate_individual_mailboxes($component_id, $component['service_id'], $arrOptions);
                break;

            // The sky is falling!
            default:
                break;
        }

    } // function config_email_auto_refresh


    //$intMode=0 - mailbox on
    //$intMode=1 - mailbox off
    //not available for partner accounts who share domains
    function config_email_auto_disable_enable($intComponentId, $intMode = NULL)
    {
    	$arrComponent = userdata_component_get($intComponentId);
    	
    	//partner accounts with a shared domain cannot have their domain disabled
    	//this muust be done at a mailbox level
    	// this is a TODO
    	if($arrComponent['isp'] == 'partner' && config_email_partner_has_domain($intComponentId)) {

    		return false;
    	}

    	if(true === is_null($intMode)) {
		// 0 - mailbox on, 1 - mailbox off
    		$intMode = ('deactive' == $arrComponent['status'] ? 0 : 1);
		}

		switch ($arrComponent['status'])
		{
			case 'active':
			case 'deactive':
    	  		try {

            		$objManager = new Mail_Manager($intComponentId);

            		// Get all availible domains and swotch mailbox on/off
            		$arrVirtualDomains = $objManager->getDefaultMailbox()->getVirtualDomainsDetails();

            		if (is_array($arrVirtualDomains) && count($arrVirtualDomains) > 0) {

            			foreach ($arrVirtualDomains as $intIdx => & $arrDomain) {

            				$objManager->getDefaultMailbox()->toggleBlackhole($arrDomain['VirtualDomainID'], $intMode);
            			}
            		}

            		// Update component status
                	userdata_component_set_status($intComponentId, 1 == $intMode ? 'deactive' : 'active');

            		return true;
        		}
        		catch (Exception $e) {

        			error_log('Failed to configure component. Details: ' . $e->getMessage() . "\nBacktrace: " . $e->getTraceAsString());
        			return false;
        		}

            	break;

            // Don't touch component in other than active/deactive state
            default:
            	break;
    	 }
    }

    function config_email_configurator($component_id, $action, $arrOptions = null) {

        switch ($action) {

            case 'auto_configure':
                config_email_auto_configure($component_id, $arrOptions);
                break;

            case 'auto_refresh':
                return config_email_auto_refresh($component_id, $arrOptions);
                break;

            case 'auto_destroy':
                config_email_auto_destroy($component_id);
                break;

            case 'auto_disable':
            	// Don't handle auto_disable (it's used in TPAR)
                 return true;
                 break;

            case 'auto_enable':
                 return config_email_auto_disable_enable($component_id, 0);
                 break;

            default:
                break;

        }

    } // function config_email_configurator


    /////////////////////////////////////////////////////////////////
    // Function : config_email_find_by_username_isp
    // Purpose  : Find ig there is a email id already for given
    //        usermame or isp
    // Arguments: $username, $isp
    // Returns  : $email_id
    /////////////////////////////////////////////////////////////////

    function config_email_find_by_username_isp($username, $isp)
    {
        $connection = get_named_connection('userdata');

        $username = addslashes($username);
        $isp      = addslashes($isp);

        $query = 'SELECT email_id ' .
                   'FROM config_email ' .
                  "WHERE username = '$username' " .
                    "AND isp = '$isp'" .
                    "AND status IN ('active', 'queued-activate', 'queued-reactivate')";

        $result = mysql_query($query, $connection)
                   or report_error(__FILE__, __LINE__, mysql_error($connection));

        $details = mysql_fetch_array($result);

        $email_id = $details['email_id'];

        return $email_id;

    } // function config_email_find_by_username_isp


    ///////////////////////////////////////////////////////////////////////
    // Function  : reactivate_individual_mailboxes
    // Purpose   : sets all active mailboxes/redirects/mailing lists to queued-reactivate
    // Arguments : $component_id
    ///////////////////////////////////////////////////////////////////////

	function reactivate_individual_mailboxes($component_id, $service_id = null, $arrOptions = array())
	{
		try {
			$objManager = new Mail_Manager($component_id);

			//get mailboxes
			$intCount = $objManager->getMailboxCount('any');

			if(is_null($service_id)) {

				$component = userdata_component_get($component_id);
				$service_id = $component['service_id'];
			}

			//get all domains
			$arrDomains = domain_get_domains_by_user($service_id);
			$intCount = $intCount * count($arrDomains);

			$arrOptions['bolForce'] = isset($arrOptions['bolForce']) ? $arrOptions['bolForce'] : FALSE;
			/**
			* Queue up to run via script as browser would timeout.
			*/
			if($intCount > 30 && $arrOptions['bolForce'] != TRUE) {

				Components_Api::queueSignal($component_id, 'REFRESH');
				Db_Manager::commit();
				return 'Number of mailboxes too high, queued for refresh. Please allow up to 4 hours to complete.';
			}

			if ($objManager->refresh()) {

				userdata_component_set_status($component_id, 'active');
				return true;
			}

			Db_Manager::commit();
		}
		catch (Exception $e) {
			error_log('ERROR: Unable to refresh mail component. Error was: '. $e->getMessage());
			Db_Manager::rollback();
			return 'There was a problem refreshing the requested component.';
		}

    } // function reactivate_individual_mailboxes

?>
