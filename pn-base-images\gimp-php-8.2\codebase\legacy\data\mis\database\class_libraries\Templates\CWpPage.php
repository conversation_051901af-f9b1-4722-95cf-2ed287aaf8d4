<?php

/**
 * PlusNet Codebase Classes - CWpPage.php
 * @version $Id: CWpPage.php,v 1.3 2005-11-18 15:20:55 adoroshko Exp $
 * @see http://dokuwiki.internal.plus.net/
 *
 * @copyright PlusNet plc, www.plus.net
 *
 * This file is part of PlusNet Codebase Classes project.
 * Generated with ArgoUML PRE-0.19.5 on 07.11.2005, 12:59:34
 *
 * <AUTHOR> <<EMAIL>>
 */

/**
 * @include CPage.php
 */

/* user defined includes */
// section 127-0-0-1-252b0afe:1076ab571c4:-7fea-includes begin
// section 127-0-0-1-252b0afe:1076ab571c4:-7fea-includes end

/* user defined constants */
// section 127-0-0-1-252b0afe:1076ab571c4:-7fea-constants begin
// section 127-0-0-1-252b0afe:1076ab571c4:-7fea-constants end

/**
 * TODO: Describe class CWpPage
 *
 * @access public
 * <AUTHOR> <<EMAIL>>
 */
class CWpPage
	extends CSmartyPage
{
	// --- ATTRIBUTES ---

	// --- OPERATIONS ---

	/**
	 * TODO: Describe method CWpPage
	 *
	 * @access public
	 * <AUTHOR> Szulc, <<EMAIL>>
	 * @param string
	 * @return void
	 */
	function CWpPage($strTemplateDir = '')
	{
		// section 127-0-0-1-252b0afe:1076ab571c4:-7fe2 begin

		$this->CSmartyPage($strTemplateDir);

		// section 127-0-0-1-252b0afe:1076ab571c4:-7fe2 end
	}

} /* end of class CWpPage */

?>
