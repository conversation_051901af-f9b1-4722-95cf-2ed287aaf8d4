<?php
/**
 * Cbc_PaygBwMaintenance
 *
 * @package   Cbc
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2011 Plusnet
 * @since     File available since 2011-09-29
 */
/**
 * Cbc_PaygBwMaintenance class
 *
 * Long term plans for this code:
 *
 * Break into smaller classes
 * Get rid of tblCBCPAYGServiceLog and tblCBCBandwidthUsage, use tblConfigCBCPAYG.intUsageStep for everything
 * Select customers that have passed a threshold from CBC, rather than processing the entire base
 *
 * @package   Cbc
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2011 Plusnet
 */
class Cbc_PaygBwMaintenance
{
    const CONFIG_FILE = '/share/admin/portal/config/Cbc_PaygBwMaintenance.ini';

    const SERVICE_SQUASH_BATCH_SIZE = 100;

    const USAGE_THRESHOLD_PERCENT = 0.8;

    const SCRIPT_RUN = 'script';
    const FRONTEND_RUN = 'frontend';

    /**
     * The following 3 constants are return types of function squashWithDtwIfRequired.
     * - SQUASHED_NO_CHARGE       returns when a customer has been squashed but requires no further charge
     * - SQUAHSED_REQUIRES_CHARGE returns when the customer has gone over 2 boundaries, first boundary requires
     *                            them to be charged, second boundary requires them to be squashed
     * - NOT_SQUASHED             returns when a customer has not reached their DTW limit OR doesn't have one set
     */
    const SQUASHED_NO_CHARGE = 1;
    const SQUAHSED_REQUIRES_CHARGE = 2;
    const NOT_SQUASHED = 0;

    public $uxtToday;
    public $strAdslGroupIds;

    /**
     * Product VISP tags
     *
     * @var array
     */
    private $arrVisps = null;

    public $arrServicesToSquash;

    public $arrDtwCustomer = array();

    public $arrErrors;

    public $dtmProcessingLimit;

    /**
     * Whether or not to do 'profiling'
     *
     * @var    bool
     * @access private
     */
    private $bolProfile = false;

    private $bolDebug = false;
    private $intServiceIdForDebug;

    /**
     * Hold a specific profile
     *
     * @var    array
     * @access private
     */
    public $arrProfile = array();

    /**
     * We populate this array when we have to change the usage band for certain accounts
     * This indicates whether an email has been sent.
     *
     * @var array
     * @access public
     */
    public $arrUsageBandUpdate = array();

    /**
     * constructor
     *
     * @param string $strRunType      Type
     * @param string $strScriptHandle Handle
     *
     * @return void
     */
    public function Cbc_PaygBwMaintenance($strRunType = self::FRONTEND_RUN, $strScriptHandle = null)
    {
        $this->includeLegacyFilesHelper();

        $this->uxtToday        = strtotime(date('Y-m-d'));
        $this->strAdslGroupIds = $this->getAllAdslGroups();
        $this->arrErrors       = array();
        $this->strScriptHandle = $strScriptHandle;

        if ($strRunType == self::SCRIPT_RUN) {

            if (!$this->readConfiguration()) {

                $this->setDefaultConfiguration();
            }

        } else {

            $this->bolDebug   = false;
            $this->bolProfile = false;
        }
    }

    /**
     * Helper function to allow us to mock out call
     *
     * @return void
     */
    public function includeLegacyFilesHelper()
    {
        self::includeLegacyFiles();
    }

    /**
     * Include the legacy files.
     *
     * @return void
     */
    public static function includeLegacyFiles()
    {
        require_once '/local/data/mis/database/standard_include.inc';
        require_once '/local/www/database-admin/config/config.inc';
        require_once '/local/data/mis/database/database_libraries/sql_primitives.inc';
        require_once '/local/data/mis/database/application_apis/EmailHandler/EmailHandler.class.php';
        require_once '/local/data/mis/database/database_libraries/sql_primitives.inc';
        require_once '/local/data/mis/database/database_libraries/programme-tool-access.inc';
        require_once '/local/data/mis/database/database_libraries/userdata-access.inc';
        require_once '/local/data/mis/database/database_libraries/radius-access.inc';
        require_once '/local/data/mis/database/database_libraries/view-my-broadband-usage-access.inc';
        require_once '/local/data/mis/database/database_libraries/cbc-access.inc';
        require_once '/local/data/mis/database/database_libraries/payment-limit-access.inc';

        require_once(CONFIG_ACCESS_LIBRARY);
    }

    /**
     * Wrapper on call to legacy function GetAllADSLGroups()
     *
     * @return string Group IDs
     */
    protected function getAllAdslGroups()
    {
        return GetAllADSLGroups();
    }


    /**
     * Wrapper on call to legacy function product_visp_tags_get()
     *
     * @return array of visp tags
     */
    protected function getProductVispTags()
    {
        if ($this->arrVisps == null) {
            $this->arrVisps = product_visp_tags_get(true);
        }

        return $this->arrVisps;
    }

    /**
     * Read configuration
     *
     * @return boolean
     */
    private function readConfiguration()
    {
        if (!file_exists(self::CONFIG_FILE)) {

            print "File ".self::CONFIG_FILE." does not exist !\n";

            return false;

        } else {

            $arrIni = parse_ini_file(self::CONFIG_FILE);
            if (empty($arrIni)) {

                print "Can not read config from ".self::CONFIG_FILE." \n";

                return false;
            }

            $this->bolDebug   = (bool) $arrIni['debug'];
            $this->bolProfile = (bool) $arrIni['profile'];
        }

        return true;
    }

    /**
     * Set default configuration
     *
     * @return void
     */
    private function setDefaultConfiguration()
    {
        $this->bolDebug   = true;
        $this->bolProfile = false;
    }

    /**
     * get errors
     *
     * @return array
     */
    public function getErrors()
    {
        return $this->arrErrors;
    }

    /**
     * Refresh CBC PAYG components for the whole customer base or just a single service
     *
     * @param integer $intSingleServiceId Just run for this service ID
     * @param boolean $bolForceRefresh    Refresh even if it's the user's billing date
     * @param boolean $bolRefreshOnly     Just update component data - don't update service log, squash or emails
     *
     * @return void
     */
    public function run($intSingleServiceId = null, $bolForceRefresh = false, $bolRefreshOnly = false)
    {
        $this->startProfile(__METHOD__);

        // The cost is calculated every time the component is refreshed or the workplace page is loaded.
        // This is an unwanted load on the workplace page.
        // The maintenance script runs every 15 minutes so the database would be updated when the script runs.
        if ($bolRefreshOnly === true) {
            return true;
        }

        $dbReporting = get_named_connection_with_db('userdata_reporting');

        if (!$dbReporting) {

            $this->arrErrors[] = 'Failed to connect to reporting database';

            return;
        }

        $intBatch = 1;
        //if single service id given, we have 1 record to process
        if ($intSingleServiceId > 0) {

            $arrProcessList[] = $intSingleServiceId;

        } else {

            //creating a table in mememory needs the same db handle
            $dbCbc = get_named_connection_with_db('cbc');

            //creates temp table of users to process.
            $this->createUsersToProcess($dbCbc);

            //show script started
            $this->setProcessingStart();

            //get first batch of users from temp table.
            $arrProcessList = $this->getUsersToProcess($intBatch, $dbCbc);
        }

        //moved here as it was resetting the array after each customer processed
        $this->arrServicesToSquash = array();

        while (is_array($arrProcessList) && !empty($arrProcessList)) {

            $this->debug("Processing Batch $intBatch, 100 per batch");

            foreach ($arrProcessList as $intServiceId) {

                $strQuery = "
                SELECT DISTINCT
                  s.service_id AS intServiceId,
                  s.username AS strUsername,
                  s.isp AS strIsp,
                  sd.type AS strType,
                  s.next_invoice AS dteNextInvoice,
                  pf.vchHandle as strFamilyHandle,
                  pv.vchHandle as strVariantHandle,

                  u.salutation AS strSalutation,
                  u.forenames AS strForenames,
                  u.surname AS strSurname,

                  c.component_id AS intComponentId,

                  ccf.intComponentID AS intCbcFlexComponentId,
                  ccf.bolCBCFix,
                  ccf.intThresholdReached,
                  cbcf.intIncludedBandwidthBytes,

                  ap.product_name AS strProductName,
                  ap.isp AS strProductIsp,
                  ap.intMaximumDailyBandwidth,

                  sd.name AS strServiceDefinitionName,

                  cs.intCBCExtraBandwidthStepBytes,
                  cs.decCBCIncludedGigabytePrice,
                  cs.decCBCExtraGigabytePrice,
                  cs.decCBCCapPrice,

                  ccp.intConfigID,
                  ccp.intUsageStep,
                  ccp.decAdditionalCost,
                  ccp.intChargedBandwidthBytes,
                  ccp.intTotalBandwidthBytes,

                  pl.intTotalBytesAvailable,
                  pl.intPaymentLimit,

                  IFNULL(sb.intSquashedBandwidthBytes, 0) AS intSquashedBandwidthBytes,

                  cpsl.intPAYGBandwidthBytes AS intLatestServiceLogBytes

                FROM userdata.services s

                INNER JOIN userdata.users u
                  ON s.user_id = u.user_id

                INNER JOIN userdata.components c
                  ON c.service_id = s.service_id
                  AND c.component_type_id IN(" .
                  COMPONENT_CBC_PAYG_BANDWIDTH . "," .
                  COMPONENT_CBC_PAYG_CALENDAR_MONTH . "," .
                  COMPONENT_CBC_PAYG_END_OF_MONTH . ")
                  AND c.status = 'active'

                INNER JOIN products.adsl_product ap
                  ON ap.service_definition_id = s.type

                INNER JOIN products.service_definitions sd
                  ON ap.service_definition_id = sd.service_definition_id

                INNER JOIN products.tblCBCSpeedProdLink cspl
                  ON cspl.intServiceDefinitionID = s.type

                INNER JOIN products.vblCBCSpeed cs
                  ON cs.intCBCSpeedID = cspl.intCBCSpeedID
                  AND cs.dtmEnd IS NULL

                INNER JOIN userdata.components cf
                  ON cf.service_id = s.service_id
                  AND cf.component_type_id = " . COMPONENT_CBC_FLEX . "
                  AND cf.status = 'active'

                INNER JOIN userdata.tblConfigCBCFlex ccf
                  ON ccf.intConfigID = cf.config_id

                INNER JOIN products.vblCBCFlex cbcf
                  ON ccf.intCBCFlexID = cbcf.intCBCFlexID

                LEFT JOIN userdata.tblConfigCBCPAYG ccp
                  ON ccp.intConfigID = c.config_id

                LEFT JOIN userdata.tblCBCServicePaymentLimit pl
                  ON pl.intServiceID = s.service_id
                  AND pl.dtmStart <= NOW()
                  AND (pl.dtmEnd IS NULL OR pl.dtmEnd > NOW())

                LEFT JOIN userdata.tblSquashedBandwidth sb
                  ON sb.intServiceId = s.service_id

                LEFT JOIN products.tblProductVariant pv ON sd.intProductVariantId = pv.intProductVariantId
                LEFT JOIN products.tblProductFamily pf ON pv.intProductFamilyId = pf.intProductFamilyId

                LEFT JOIN userdata.tblCBCPAYGServiceLog cpsl ON s.service_id = cpsl.intServiceID

                WHERE s.service_id = $intServiceId

                  AND (sb.bolCurrentlySquashed IS NULL OR sb.bolCurrentlySquashed = 0)

                ";

                $this->startProfile(__METHOD__ . '::MainQuery');

                $resResult = PrimitivesQueryOrExit($strQuery, $dbReporting);

                if (!$resResult) {

                    $this->arrErrors[] = 'Main query failed: ' . mysql_error($dbReporting);

                    return;
                }

                $this->endProfile(__METHOD__ . '::MainQuery');

                $arrService = PrimitivesResultGet($resResult);

                //the query above will confirm if the service needs to be processed.
                //So if the following is not set/array and service id not above 0 then skip.
                if (is_array($arrService) && !empty($arrService) && $arrService['intServiceId'] > 0) {

                    if (count($this->arrErrors) > 64) {

                        $this->arrErrors[] = 'Error threshold reached.  Bailing out.';
                        break 2;
                    }

                    $this->processService($arrService);
                }
            }

            $this->debug("Completed processsing batch $intBatch");
            $intBatch++;

            //dont run this if single service id has been given
            if (is_null($intSingleServiceId)) {

                //once this array becomes empty the while loop will end.
                $arrProcessList = $this->getUsersToProcess($intBatch, $dbCbc);

            } else {

                //single service to process break
                break;
            }
        }

        if (is_null($intSingleServiceId)) {

            //if single service id not used we must have a temp table. So lets drop it.
            $strQuery = "DROP TABLE IF EXISTS reporting.tmpServicesToProcess{$this->strScriptHandle}";
            PrimitivesQueryOrExit($strQuery, $dbCbc);

            //state that script finished processing successfully.
            $this->setProcessingStop();
        }

        $this->squashServices();

        $this->endProfile(__METHOD__);
    }

    /**
     * Get last run
     *
     * @return array
     */
    public function getLastRun()
    {
        $dtmDefault = date('Y-m-d H:i:s', mktime(date('H')-1, date('i'), date('s'), date('m'), date('d'), date('Y')));
        if (!$this->strScriptHandle) {
            return $dtmDefault;
        }

        $strQuery = "
         SELECT
          DATE_SUB(dtmStart, INTERVAL 5 MINUTE) as dtmStart
         FROM
          reporting.tblCbcProcessScripts
         WHERE
          vchScriptHandle = '{$this->strScriptHandle}'
         ";

        $dbCbc = get_named_connection_with_db('cbc');
        $resResult = PrimitivesQueryOrExit($strQuery, $dbCbc);
        $arrScript = PrimitivesResultGet($resResult);

        return isset($arrScript['dtmStart']) ? $arrScript['dtmStart'] : $dtmDefault;
    }

    /**
     * Set service Id for debug
     *
     * @param inetger $intServiceIdForDebug Service id
     *
     * @return integer
     */
    private function setServiceIdForDebug($intServiceIdForDebug)
    {
        $this->intServiceIdForDebug = $intServiceIdForDebug;
    }

    /**
     * Process a service
     *
     * @param array $arrService Array of service data
     *
     * @access private
     *
     * @return void
     */
    public function processService($arrService)
    {
        $this->setServiceIdForDebug($arrService['intServiceId']);

        // DJM 2013: semi-hack to avoid this script erroring on the special "TR069" accounts - see P76463
        $arrTr69Users = array(
            'getconnected@force9',
            'getconnected@partner',
            'getconnected@metronet',
            'getstarted@freeonline',
            '<EMAIL>',
            'johnlewissetup@johnlewis',
            '<EMAIL>',
            '<EMAIL>'
        );

        $strTrCheck = $arrService['strUsername'] . '@' . $arrService['strIsp'];

        if (in_array($strTrCheck, $arrTr69Users)) {
            $this->debug("Skipping user [$strTrCheck] as they are a special TR069 account");
            return;
        }

        $this->startProfile(__METHOD__);
        $this->debug("processService: ".$arrService['intServiceId'].", ".$arrService['strIsp']);

        $unkUsage = $this->getUsage($arrService);
        if (!$unkUsage) {

            $this->debug("Unknown usage");
            $this->endProfile(__METHOD__);

            return;
        }

        list($intPeakUsageBytes, $intOffPeakUsageBytes) = $unkUsage;

        $intTotalUsageBytes = $intPeakUsageBytes + $intOffPeakUsageBytes;

        $this->debug("Total usage: $intTotalUsageBytes");

        if (!$this->checkTotalUsage($arrService, $intTotalUsageBytes)) {

            $this->debug("checkTotalUsage failed");
            $this->endProfile(__METHOD__);

            return;
        }

        $intRoundingInterval  = getSteppingByBandwidthStep($arrService['intCBCExtraBandwidthStepBytes']);
        $intNonFreeUsageBytes = $this->getNonFreeUsage(
            $arrService,
            $intPeakUsageBytes,
            $intOffPeakUsageBytes,
            false
        );
        $intChargedUsageBytes = $this->getNonFreeUsage(
            $arrService,
            $intPeakUsageBytes,
            $intOffPeakUsageBytes,
            true
        );

        $this->debug("Total Usage Bytes Before Checking Limits : ".$intTotalUsageBytes);
        $this->debug("Charged Usage Bytes Before Checking Limits : ".$intChargedUsageBytes);

        /*
            If a user has gone over their DTW limit then they need to be squashed. This function is being placed
            after the checkDtw function  to ensure the charged usage bytes have been accounted for within the DTW
            limit.
        */

        $intHasBeenSquashedWithDtw = $this->squashWithDtwIfRequired($arrService, $intChargedUsageBytes);

        $this->checkDtw($arrService, $intTotalUsageBytes, $intChargedUsageBytes);

        /*
            If the user has been not squashed with DTW or is not over their usage cap then continue
            to add extra usage and charge them accordingly, or squash them for going over their included usage limit.
            The usage cap check has been added in case the user is already squashed, we don't want to go ahead and
            charge the user.
        */

        switch ($intHasBeenSquashedWithDtw) {
            case self::NOT_SQUASHED:
                $this->debug('Post DTW Squash Actions = NOT SQUASHED');
                break;
            case self::SQUAHSED_REQUIRES_CHARGE:
                $this->debug('Post DTW Squash Actions = SQUASHED and REQUIRES CHARGING');
                break;
            case self::SQUASHED_NO_CHARGE:
                $this->debug('Post DTW Squash Actions = SQUASHED and NO CHARGE REQUIRED');
                break;
        }

        if ($intHasBeenSquashedWithDtw === self::NOT_SQUASHED ||
            $intHasBeenSquashedWithDtw === self::SQUAHSED_REQUIRES_CHARGE
        ) {

            $intChargedUsageBytes = $this->checkInvoiceDatesBeforeSquashing($arrService, $intChargedUsageBytes);

            $intUsageStep = $arrService['intUsageStep'];

            $this->debug("intNonFreeUsageBytes: $intNonFreeUsageBytes");
            $this->processUsageBand($arrService, $intNonFreeUsageBytes);

            if ($this->needsSquashing($arrService, $intChargedUsageBytes)) {

                $this->debug("Need squashing");
                $this->removeSquashedUsage($arrService, $intTotalUsageBytes, $intChargedUsageBytes);

                $this->addToSquashQueue($arrService);
                $this->debug("Adding to squash queue");
            }

            if ($this->isMetronetOverIncludedLimit($arrService, $intChargedUsageBytes)) {

                $this->debug("metronetIncludedLimitReached");
                $this->metronetIncludedLimitReached($arrService, $intRoundingInterval);
            }

            $arrUsageLevels = $this->getUsageLevels($arrService, $intChargedUsageBytes);

            if ($arrUsageLevels) {

                $this->updatePaygServiceLog($arrService, $arrUsageLevels, $intRoundingInterval, $intNonFreeUsageBytes);

                // whats the new usage step value? its the old value plus the number of usage steps we've just charged f
                // or...
                $intUsageStep += (end($arrUsageLevels) - $arrService['intLatestServiceLogBytes'])
                    / $arrService['intCBCExtraBandwidthStepBytes'];
            }

            if (isset($arrUsageLevels) && count($arrUsageLevels) > 0) {

                // n.b. count($arrUsageLevels) is the number of steps we are charging for
                // on this run (not the total number of steps used so far by this user)
                $floAdditionalCost = $this->calcAdditionalCost(
                    $arrService,
                    count($arrUsageLevels),
                    $intRoundingInterval
                );

            } else {

                $floAdditionalCost = $arrService['decAdditionalCost'];
            }

            $this->setPaygConfig(
                $arrService,
                $intTotalUsageBytes,
                $intChargedUsageBytes,
                $intUsageStep,
                $floAdditionalCost
            );
        }

        if (!self::autoSetCbcBandwidthThreshold($arrService['intServiceId'])) {

            $this->debug("ERROR: autoSetCbcBandwidthThreshold Failed for SID {$arrService['intServiceId']}");
        }

        $this->endProfile(__METHOD__);
    }

    /**
     * Check to see if it is the customers invoice day
     *
     * @param array   $arrService           Array of service data
     * @param integer $intChargedUsageBytes Total amount of charged usage bytes
     *
     * @access private
     *
     * @return integer $intChargedUsageBytes charged usage bytes
     */
    public function checkInvoiceDatesBeforeSquashing($arrService, $intChargedUsageBytes)
    {
        /*
            We need to stop a user being squashed on their invoice day but allow more extra usage/additional charges
        */

        // First we need to check what type of billing
        $strSPType = vmbuGetCBCBillingPeriodType($arrService['intServiceId']);

        if ($strSPType == 'CALENDAR_MONTH') {

            // Need to go from first of the month for CALENDAR_MONTH type
            $compareDate = date('Y-m-01');

        } elseif ($strSPType == 'END_OF_MONTH') {

            // its always last day of the current month
            $compareDate = date('Y-m-t');

        } elseif (!empty($arrService['dteNextInvoice'])) {

            // cbc doesnt run on invoice dates, it uses invoice day
            // as it is important that anual/quartely payers get reset every month...
            // get invoice day
            $invoiceDay = date('d', strtotime($arrService['dteNextInvoice']));

            // if the invoice day is > max days in this month, change the day to last day in this month
            $invoiceDay = $invoiceDay > date('t') ? date('t') : $invoiceDay;

            // if the invoice day has already happened this month then bump the invoice month along
            // n.b. the last day in month check above will never enter into this scenario so no need
            // to worry about bumping the month along and the last day not being the last day of the next invoice month
            $invoiceMonth = $invoiceDay < date('d') ?
                date('m', mktime(0, 0, 0, date('m')+1, $invoiceDay, date('Y'))) : date('m');

            // create the compare date
            $compareDate = date(
                'Y-m-d',
                mktime(0, 0, 0, $invoiceMonth, $invoiceDay, date('Y'))
            );

        } else {

            $compareDate = null;
        }

        if ($compareDate !== null && date('Y-m-d') == $compareDate) {

            $strAdslGroupsFormatted = $this->strAdslGroupIds;

            $intDaysChargeableBytes = getADaysChargeableBytes(
                $arrService['username'],
                $arrService['isp'],
                strtotime($compareDate),
                $strAdslGroupsFormatted
            );

            if (isset($intDaysChargeableBytes) && $intDaysChargeableBytes > 0) {

                $intChargedUsageBytes =- $intDaysChargeableBytes;
            }
        }

        $this->debug('Charged Usage Bytes after invoice dates have been checked : '.$intChargedUsageBytes);

        return $intChargedUsageBytes;
    }

    /**
     * Squash Users For Going Over DTW Limit
     *
     * @param array   $arrService           Array of service data
     * @param integer $intChargedUsageBytes Total amount of charged usage bytes
     *
     * @access private
     *
     * @return string
     */
    public function squashWithDtwIfRequired($arrService, $intChargedUsageBytes)
    {
        if (!empty($arrService['intTotalBytesAvailable'])) {

            /*
                Check to see if the user has set DTW and whether or not they are already squashed.
                If they are using DTW and are not squashed then check their current usage before squashing.
            */

            if ($this->isUsingDtw($arrService['intTotalBytesAvailable'])
                && !bolDialupRestrictedDueToUsageCap($arrService['intServiceId'])
            ) {

                $intChargedUsageBytes = $this->checkInvoiceDatesBeforeSquashing($arrService, $intChargedUsageBytes);

                /* This IF statement has to check whether someone has gone over their DTW limit but because of a bug
                 that allow users to add parts of a step, e.g 1.8Gigs in a 2Gig step, we have to add the step value on
                 to compensate.
                 Adding the step value on will only work if the bug has been triggered,
                 so to stop it squashing users who have added the correct step amount we have to check
                 that the standard step limit has not been reached at the same time.

                 However, it is possible for someone to have added the correct step amount, e.g 2Gigs, or 4Gigs in a
                 2Gig step, and therefore we have to allow for that too.
                 This is done by adding the standard step check as an additional condition for squashing.
                */

                $this->debug(
                    'Included Bandwidth Bytes before DTW squash check = '.$arrService['intIncludedBandwidthBytes']
                );
                $this->debug('Charged Usage Bytes before DTW squash check = '.$intChargedUsageBytes);
                $this->debug(
                    'Usage Step Bytes before DTW squash check = '.$arrService['intCBCExtraBandwidthStepBytes']
                );

                error_log(
                    'Included Bandwidth Bytes before DTW squash check = '.$arrService['intIncludedBandwidthBytes']
                );
                error_log('Charged Usage Bytes before DTW squash check = '.$intChargedUsageBytes);
                error_log('Usage Step Bytes before DTW squash check = '.$arrService['intCBCExtraBandwidthStepBytes']);
                error_log('Total Bytes Available before DTW squash check = '.$arrService['intTotalBytesAvailable']);

                if ( ( (($arrService['intIncludedBandwidthBytes'] + $intChargedUsageBytes + $arrService['intCBCExtraBandwidthStepBytes'])
                    >= $arrService['intTotalBytesAvailable'])
                    && (($arrService['intIncludedBandwidthBytes'] + $intChargedUsageBytes)
                    > $arrService['intTotalBytesAvailable']) )
                    || ($arrService['intIncludedBandwidthBytes'] + $intChargedUsageBytes)
                    > $arrService['intTotalBytesAvailable']
                ) {

                    /* SQUASH */

                    error_log('SQUASHING ON DTW');

                    $this->arrDtwCustomer[] = $arrService['intServiceId'];

                    $this->addToSquashQueue($arrService);

                    $this->debug("Squashing on DTW for service id : {$arrService['intServiceId']}");

                    // if the charged usage bytes, minus the ammount they have already paid for is greaterr than their
                    // step bytes then this means that the user has gone over 2 boundaries.
                    if ($intChargedUsageBytes -
                        bcmul($arrService['intUsageStep'], $arrService['intCBCExtraBandwidthStepBytes']) >
                        $arrService['intCBCExtraBandwidthStepBytes']
                    ) {
                        return self::SQUAHSED_REQUIRES_CHARGE;
                    }

                    return self::SQUASHED_NO_CHARGE;
                }
            }
        }

        return self::NOT_SQUASHED;
    }

    /**
     * Check total usage
     *
     * @param array   $arrService         Service details
     * @param integer $intTotalUsageBytes Total usage bytes
     *
     * @return boolean
     */
    public function checkTotalUsage($arrService, $intTotalUsageBytes)
    {
        $dbReporting = get_named_connection_with_db('userdata_reporting');

        $strQuery = "
         SELECT
          datediff(date_add(now(),INTERVAL 1 DAY), dtmStart) as intServicePeriodDays
         FROM
          tblVMBUServicePeriod
         WHERE
          dtmEnd IS NULL AND intServiceID = '{$arrService['intServiceId']}'
         ";

        $resResult = PrimitivesQueryOrExit($strQuery, $dbReporting);
        $intServicePeriodDays = PrimitivesResultGet($resResult, 'intServicePeriodDays');

        if (!is_int($intServicePeriodDays) || $intServicePeriodDays <= 0) {
            $intServicePeriodDays = date('t', mktime(0, 0, 0, date('m')-1, date('d'), date('Y')));
        }

        if ($intTotalUsageBytes > $arrService['intMaximumDailyBandwidth'] * $intServicePeriodDays) {

            $this->addError(
                $arrService,
                "total usage of $intTotalUsageBytes is higher than expected maximum."
            );

            return false;
        }

        return true;
    }

    /**
     * Determine if a customer is using DTW
     *
     * @param integer $intTotalUsageBytesAvailable Total amount of usgae bytes available
     *
     * @access private
     *
     * @return boolean
     */
    public function isUsingDtw($intTotalUsageBytesAvailable)
    {
        // Nothing to do if user did not set DTW limit
        if (empty($intTotalUsageBytesAvailable) || 0 == $intTotalUsageBytesAvailable) {
            return false;

        } else {
            return true;
        }
    }

    /**
     * Check for DTW
     *
     * @param array   $arrService            Service details
     * @param integer &$intTotalUsageBytes   Total usage bytes
     * @param integer &$intChargedUsageBytes Charged usage bytes
     *
     * @return void
     */
    public function checkDtw($arrService, &$intTotalUsageBytes, &$intChargedUsageBytes)
    {
        // Nothing to do if user did not set DTW limit

        $bolIsUsingDtw = false;

        if (!empty($arrService['intTotalBytesAvailable'])) {

            $bolIsUsingDtw = $this->isUsingDtw($arrService['intTotalBytesAvailable']);
        }

        if ($bolIsUsingDtw == false) {
            return;
        }

        // If limit is set we know that it is equal or greater then included bandwidth bytes
        // (user cannot set DTW limit below product cost).
        // So the following is zero or greater

        $this->debug('DTW - Total Bytes Available before calculation : '.$arrService['intTotalBytesAvailable']);
        $this->debug('DTW - Included Bandwidth Bytes before calculation : '.$arrService['intIncludedBandwidthBytes']);
        $this->debug('DTW - Charged Usage Bytes before calculation : '.$intChargedUsageBytes);

        $intBytesBaseForCalculation = $arrService['intTotalBytesAvailable'] - $arrService['intIncludedBandwidthBytes'];

        // Calculate how much bytes should be removed
        $intBytesToRemove = $intChargedUsageBytes - $intBytesBaseForCalculation;

        if ($intBytesToRemove > 0) {

            $intTotalUsageBytes -= $intBytesToRemove;
            $intChargedUsageBytes -= $intBytesToRemove;
        }

        $this->debug('DTW - Total Usage Bytes after calculation : '.$intTotalUsageBytes);
        $this->debug('DTW - Charged Usage Bytes after calculation : '.$intChargedUsageBytes);

    } // End of method checkDtw()

    /**
     * Works with the intThresholdReached flag to determine when to send over-threshold emails
     * and whatever else we want to extend this to.
     *
     * @param array   $arrService           Account Details
     * @param integer $intNonFreeUsageBytes Non Free Usage Bytes
     *
     * @return void
     */
    public function processUsageBand($arrService, $intNonFreeUsageBytes)
    {
        switch ($arrService['strProductIsp']) {
            case 'plus.net':
            case 'freeonline':
            case 'force9':
            case 'vodafone':
            case 'johnlewis':
                $newProductFamilies = array(
                    ProductFamily_Legacy::BBYW_PRODUCT_FAMILY_HANDLE,
                    ProductFamily_Value::PRODUCT_FAMILY_HANDLE,
                    ProductFamily_ValueSolus::PRODUCT_FAMILY_HANDLE,
                    ProductFamily_ValueDualPlay::PRODUCT_FAMILY_HANDLE,
                    ProductFamily_Bpr09::PRODUCT_FAMILY_HANDLE,
                    ProductFamily_Mps::PRODUCT_FAMILY_HANDLE,
                    ProductFamily_JohnLewis2011::PRODUCT_FAMILY_HANDLE,
                    ProductFamily_Res2012::PRODUCT_FAMILY_HANDLE,
                    ProductFamily_PlusnetSolus2013::PRODUCT_FAMILY_HANDLE,
                    ProductFamily_PlusnetDualPlay2013::PRODUCT_FAMILY_HANDLE,
                    ProductFamily_Brp12Solus::PRODUCT_FAMILY_HANDLE,
                    ProductFamily_Brp12Dual::PRODUCT_FAMILY_HANDLE
                );

                if (!in_array($arrService['strFamilyHandle'], $newProductFamilies)) {

                    // No usage threshold on older products
                    return false;
                }
                // Take note of the fall through
            case 'partner':
            case 'madasafish':
                $intThreshold = $this->getUsageThreshold($arrService['intIncludedBandwidthBytes']);
                break;
            case 'greenbee':
            case 'waitrose':
                $intThreshold = $arrService['intIncludedBandwidthBytes'] - 268435456;
                break;
            default:
                return false;
        }

        $this->debug("intPreBreachThreshold: $intThreshold");
        $this->debug("intThresholdReached: ".$arrService['intThresholdReached']);

        switch ($arrService['intThresholdReached']) {
            // Below Threshold
            case 0:

                if ($intNonFreeUsageBytes >= $intThreshold &&
                    $intNonFreeUsageBytes < $arrService['intIncludedBandwidthBytes']) {

                    // We've gone above threshold. Change usage band and send email
                    $this->setUsageBand($arrService, 1);
                    $this->sendPreBreachEmail($arrService);
                }
                break;
            // Above threshold
            case 1:
                // Usage is below threshold again. Either customer was billed, or changed
                // their flex option, so we need to change their usage band back down.
                if ($intNonFreeUsageBytes < $intThreshold) {

                    $this->setUsageBand($arrService, 0);
                }
                break;
            default:
                return false;
            break;
        }

        return true;
    }

    /**
     * setUsageBand
     *
     * @param array   $arrService          Account Details
     * @param integer $intThresholdReached Threashold reached
     *
     * @return void
     */
    public function setUsageBand($arrService, $intThresholdReached)
    {
        $this->arrUsageBandUpdate[$arrService['intCbcFlexComponentId']] = $intThresholdReached;
        $dbhConnection = get_named_connection_with_db('userdata');

        $intThresholdReached = PrimitivesRealEscapeString($intThresholdReached, $dbhConnection);

        $strQuery = "
                     UPDATE userdata.tblConfigCBCFlex
                     SET intThresholdReached   = {$intThresholdReached}
                     WHERE intComponentID = {$arrService['intCbcFlexComponentId']}";

        PrimitivesQueryOrExit($strQuery, $dbhConnection, 'PaygBandwidthMaintenance', false);
    }

    /**
     * getUsageThreshold
     *
     * @param integer $intBytes bytes
     *
     * @access public
     *
     * @return void
     */
    public function getUsageThreshold($intBytes)
    {
        return bcmul($intBytes, self::USAGE_THRESHOLD_PERCENT);
    }

    /**
     * getNonFreeUsage
     *
     * @param array   $arrService           array of service details
     * @param integer $intPeakUsageBytes    Peak Usage Bytes
     * @param integer $intOffPeakUsageBytes Off Peak Usage Bytes
     * @param boolean $bolChargedOnly       boolean value
     *
     * @access public
     *
     * @return void
     */
    public function getNonFreeUsage($arrService, $intPeakUsageBytes, $intOffPeakUsageBytes, $bolChargedOnly = false)
    {
        $bolOffPeakChargeable = ($arrService['strIsp'] == 'metronet' ||
                                 $arrService['strProductName'] == 'Broadband Pay As You Go Basic');

        // Where intSquashedBandwidthBytes is peak usage only
        $intChargedUsageBytes = $intPeakUsageBytes + ($bolOffPeakChargeable ? $intOffPeakUsageBytes : 0) -
            $arrService['intSquashedBandwidthBytes'];

        if ($bolChargedOnly) {

            $intChargedUsageBytes = $intChargedUsageBytes - $arrService['intIncludedBandwidthBytes'];
        }

        return ($intChargedUsageBytes > 0) ? $intChargedUsageBytes : 0;
    }

    /**
     * Checks for squashed needs
     *
     * @param array   $arrService           Service details
     * @param integer $intChargedUsageBytes Charged usage bytes
     *
     * @return boolean
     */
    public function needsSquashing($arrService, $intChargedUsageBytes)
    {
        return ($arrService['bolCBCFix'] && $intChargedUsageBytes &&
            $intChargedUsageBytes >= $arrService['intLatestServiceLogBytes']);
    }

    /**
     * Deduct bandwidth used while squashed from the total and charged values
     *
     * @param array   $arrService            Array of service data
     * @param integer &$intTotalUsageBytes   Total usage in bytes
     * @param integer &$intChargedUsageBytes Charged usage in bytes
     *
     * @access private
     *
     * @return void
     */
    public function removeSquashedUsage($arrService, &$intTotalUsageBytes, &$intChargedUsageBytes)
    {
        $intBytesToRemove = $intChargedUsageBytes - intval($arrService['intLatestServiceLogBytes']);

        $intTotalUsageBytes -= $intBytesToRemove;
        $intChargedUsageBytes -= $intBytesToRemove;
    }

    /**
     * Get squashing email variables
     *
     * @param array $arrService Service details
     *
     * @return array
     */
    public function getSquashingEmailVars($arrService)
    {
        $strCurrentAllowance = substr(
            ($arrService['intIncludedBandwidthBytes'] + $arrService['intLatestServiceLogBytes']),
            0,
            -9
        ) . 'GB';

        return array(
            'current_usage_allowance' => $strCurrentAllowance,
            'billing_date'            => $arrService['dteNextInvoice']
        );
    }

    /**
     * Checks for metronet included limits
     *
     * @param array   $arrService           Service details
     * @param integer $intChargedUsageBytes Charged usage bytes
     *
     * @return boolean
     */
    public function isMetronetOverIncludedLimit($arrService, $intChargedUsageBytes)
    {
        return ($intChargedUsageBytes && $arrService['strIsp'] == 'metronet' &&
            strtotime($arrService['dteNextInvoice']) >= $this->uxtToday);
    }

    /**
     * Calculate additional cost
     *
     * @param array   $arrService              Service details
     * @param integer $intAdditionalUsageSteps Additioal usage step
     * @param integer $intRoundingInterval     Interval
     *
     * @return float
     */
    public function calcAdditionalCost($arrService, $intAdditionalUsageSteps, $intRoundingInterval)
    {
        $this->startProfile(__METHOD__);

        if (bcmul($intRoundingInterval, 2) == GIGABYTE) {

            $intExtraCostAddedOnThisRun = $intAdditionalUsageSteps * ( $arrService['decCBCExtraGigabytePrice'] * 0.5 );

        } else {

            $intExtraCostAddedOnThisRun = $intAdditionalUsageSteps * ( $arrService['decCBCExtraGigabytePrice'] *
                ($arrService['intCBCExtraBandwidthStepBytes'] / $intRoundingInterval) );
        }

        $floAdditionalCost = $arrService['decAdditionalCost'] + $intExtraCostAddedOnThisRun;
        if ($floAdditionalCost > $arrService['decCBCCapPrice']) {

            $floAdditionalCost = $arrService['decCBCCapPrice'];
        }

        $this->endProfile(__METHOD__);

        return $floAdditionalCost;
    }

    /**
     * Get usage levels
     *
     * @param array   $arrService       Service details
     * @param integer $intNewUsageBytes New usage in bytes
     *
     * @return array
     */
    public function getUsageLevels($arrService, $intNewUsageBytes)
    {
        $this->startProfile(__METHOD__);

        // Only do this in GB steps (as MB will do too many entries)

        $intStepping = $arrService['intCBCExtraBandwidthStepBytes'];

        $intPreviousUsageStep = $arrService['intUsageStep'];

        // work out number of usage steps being charged for since the last PAYG charge
        if ($intNewUsageBytes > 0) {

            $intUsageBytesDelta = $intNewUsageBytes - $arrService['intLatestServiceLogBytes'];
            $this->debug(
                "intUsageBytesDelta = $intUsageBytesDelta = intNewUsageBytes - intLatestServiceLogBytes = ".
                "$intNewUsageBytes - {$arrService['intLatestServiceLogBytes']}"
            );

            if ($intUsageBytesDelta > 0) {

                //how many steps are in the intUsageBytesDelta?
                $intNewUsageStep = ceil($intUsageBytesDelta/$intStepping) + $intPreviousUsageStep;
                $this->debug(
                    "how many steps are in the intUsageBytesDelta? $intNewUsageStep = ".
                    "ceil($intUsageBytesDelta / $intStepping)"
                );

            } else {

                $intNewUsageStep = $intPreviousUsageStep;
            }

        } else {

            $intNewUsageStep = 0;
        }

        $this->debug("Number Of Steps Already Charged For: $intPreviousUsageStep");
        $this->debug("Number Of Steps To Charge For Now (Total): $intNewUsageStep");

        $arrUsageLevels = array();
        if ($intNewUsageStep > $intPreviousUsageStep) {

            $intStep = $intPreviousUsageStep + 1;

            // get the number of bytes the user can use without going over dtw
            $intChargedExtraUsageLimitBytes = $arrService['intTotalBytesAvailable'] -
                $arrService['intIncludedBandwidthBytes'];

            // i.e. if the user has a dtw set above their subscription cost...
            if ($intChargedExtraUsageLimitBytes > 0) {

                // get the number of steps the user can go over by without going over dtw
                // 1. get PAYG bw bytes remaining
                $intBytesRemaining = $intChargedExtraUsageLimitBytes - $arrService['intLatestServiceLogBytes'];

                // 2. get number of steps within this remaining bytes
                $intStepsRemaining = floor($intBytesRemaining/$intStepping);

                if ($intNewUsageStep > $intStepsRemaining) {

                    $intNewUsageStep = $intStepsRemaining;
                }
            }

            // for every new usage step being charged for create an element in an array for the usage that this step
            // refers to
            $intNewStepsBeingChargedFor = $intNewUsageStep - $intPreviousUsageStep;
            for ($i = 1; $i <= $intNewStepsBeingChargedFor; $i++) {

                $arrUsageLevels[] = $arrService['intLatestServiceLogBytes'] + ( $i * $intStepping );
            }

        } elseif ($intNewUsageStep < $intPreviousUsageStep) {

            $arrUsageLevels[] = $intNewUsageStep * $intStepping;
        }

        $this->endProfile(__METHOD__);

        return $arrUsageLevels;
    }

    /**
     * Generates usage email data
     *
     * @param array $arrService           Service information
     * @param array $arrUsageLevels       Usage levels
     * @param int   $intRoundingInterval  Rounding Interval
     * @param int   $intNonFreeUsageBytes Number of non free usage bytes
     *
     * @return array of email usage data
     */
    public function getUsageEmailData($arrService, $arrUsageLevels, $intRoundingInterval, $intNonFreeUsageBytes)
    {
        if (Core_Service::isJlpVisp($arrService['strIsp'])) {
            return array();
        }

        if ($arrService['strIsp'] == 'johnlewis' && $this->isUsingDtw($arrService['intTotalBytesAvailable'])) {
            // john lewis account and Data Transfer watch enabled, so supress the email being sent
            return array();
        }

        $this->startProfile(__METHOD__);

        $arrEmails = array();

        if ($arrService['strIsp'] == 'metronet'   // No usage emails for these products
            || $arrService['strProductName'] == 'Broadband Pay As You Go Basic'
        ) {

            $this->endProfile(__METHOD__);

            return $arrEmails;
        }

        $strUnit = $this->getUnit($intRoundingInterval);

        $productVispTags = $this->getProductVispTags();

        $arrAdditionalVars = array(
            'strCustomerName'        => "{$arrService['strSalutation']} ".
                                        "{$arrService['strForenames']} {$arrService['strSurname']}",
            'strBrand'               => $productVispTags[$arrService['strIsp']]['full_name'],
            'intVISPSupportNumber'   => $productVispTags[$arrService['strIsp']]['support_phone_number'],
            'strStrapline'           => $productVispTags[$arrService['strIsp']]['tag_line'],
            'strPortalURL'           => $productVispTags[$arrService['strIsp']]['portal_main_page_url'],
            'strSupportEmailAddress' => $productVispTags[$arrService['strIsp']]['support_email_address'],
        );
        $arrAdditionalVars['strURL'] = str_replace('portal', '', $arrAdditionalVars['strPortalURL']);

        foreach ($arrUsageLevels as $intBytes) {

            if (!$intBytes) {
                continue;
            }

            $arrEmail = array(
                'arrAdditionalVars' => $arrAdditionalVars
            );

            $intDecimalPlaces = (($arrService['intCBCExtraBandwidthStepBytes'] == bcdiv(GIGABYTE, 2)) ? 1 : 0);

            $intPreviousBytes = round(
                ($arrService['intIncludedBandwidthBytes'] + $intBytes - $arrService['intCBCExtraBandwidthStepBytes']) /
                $intRoundingInterval,
                $intDecimalPlaces
            );

            $arrEmail['arrAdditionalVars']['intShowPreviousBandwidthGB'] = $intPreviousBytes;
            $arrEmail['arrAdditionalVars']['strShowPreviousBandwidth'] = $intPreviousBytes . $strUnit;
            $intSteppingByVisp = $this->getSteppingByVISP($arrService['strIsp']);
            $arrEmail['arrAdditionalVars']['intTotalUsageBytes'] = round(($intNonFreeUsageBytes/$intSteppingByVisp), 2);

            $intExtraGBStep = round(
                $arrService['intCBCExtraBandwidthStepBytes'] / $intRoundingInterval,
                $intDecimalPlaces
            );
            $arrEmail['arrAdditionalVars']['strExtraGBStep'] = $intExtraGBStep . $strUnit;

            // This calculates the allowance, a customer has. This is not the included bandwidth.
            $arrEmail['arrAdditionalVars']['strBandwidthAllowance'] = ($intPreviousBytes - $intExtraGBStep) . $strUnit;

            if (($arrEmail['arrAdditionalVars']['intShowPreviousBandwidthGB'] < 30
                && $arrService['strProductIsp'] == 'madasafish')
                || $arrService['strProductIsp'] != 'madasafish'
            ) {

                $intFactor = $arrService['intCBCExtraBandwidthStepBytes'] / $intRoundingInterval;
                $intFactor = ($intDecimalPlaces == 1) ? $intFactor : ceil($intFactor);

                $arrEmail['arrAdditionalVars']['intTotalBandwidthGB']
                    = $arrEmail['arrAdditionalVars']['intShowPreviousBandwidthGB'] + $intFactor;

                // IF cost per megabyte then show price in pence
                if ($arrService['intCBCExtraBandwidthStepBytes'] == MEGABYTE) {

                    $arrEmail['arrAdditionalVars']['strFlexBandwidthAdditionalCost']
                        = round(ceil(GIGABYTE / MEGABYTE) * $arrService['decCBCIncludedGigabytePrice'], 2) . 'p';
                    $arrEmail['arrAdditionalVars']['strExtraBandwidthAdditionalCost']
                        = round(ceil(GIGABYTE / MEGABYTE) * $arrService['decCBCExtraGigabytePrice'], 2) . 'p';

                } else {

                    $arrEmail['arrAdditionalVars']['strFlexBandwidthAdditionalCost']
                        = number_format(round($intFactor * $arrService['decCBCIncludedGigabytePrice'], 2), 2, '.', ' ');
                    $arrEmail['arrAdditionalVars']['strExtraBandwidthAdditionalCost']
                        = number_format(round($intFactor * $arrService['decCBCExtraGigabytePrice'], 2), 2, '.', ' ');
                }

                $bolOverMax = false;

            } else {

                $bolOverMax = true;
            }

            switch ($arrService['strProductIsp']) {
                case 'partner':
                    $arrEmail['strEmailTemplate'] = 'PartnerCBCEmail_PAYG_ExtraBandwidthAdded';
                    break;
                case 'madasafish':
                    $arrEmail['strEmailTemplate']
                        = '/local/data/mis/portal_modules/mailtemplates/cbc/CBCEmail_PAYG_ExtraBandwidthAdded' .
                        ($bolOverMax ? 'OverMaxIncluded_eb331' : '_eb330') . '.txt';
                    break;
                case 'plus.net':
                case 'johnlewis':
                    if ('business' == $arrService['strType']) {
                        $arrEmail['strEmailTemplate'] = 'cbc_payg_bandwidth_added_business';
                    } else {
                        $arrEmail['strEmailTemplate'] = 'cbc_payg_bandwidth_added';
                    }
                    break;
                default:
                    $arrEmail['strEmailTemplate'] = 'cbc_payg_bandwidth_added';
            }

            $arrEmails[] = $arrEmail;
        }

        $this->endProfile(__METHOD__);

        return $arrEmails;
    }

    /**
     * Wrapper on getSteppingByVISP function in cbc-access.inc
     *
     * @param integer $visp VISP
     *
     * @return integer The appropriate ammount of stepping gig / meg as an integer
     */
    protected function getSteppingByVISP($visp)
    {
        return getSteppingByVISP($visp);
    }

    /**
     * Get unit
     *
     * @param integer $intRoundingInterval Interval
     *
     * @return string
     */
    public function getUnit($intRoundingInterval)
    {
        switch ($intRoundingInterval) {
            case MEGABYTE:
                return 'MB';
                break;
            case GIGABYTE:
            case GIBIBYTE:
                return 'GB';
                break;
            case bcdiv(GIGABYTE, 2):
            case bcdiv(GIBIBYTE, 2):
                return 'GB';
                break;
        }
    }

    // Database access etc

    /**
     * createUsersToProcess
     *  - Creates temporary table to easily process accounts from CBC
     *
     * @param array &$dbCbc CBC db details
     *
     * @access public
     * @return void
     */
    public function createUsersToProcess(&$dbCbc)
    {
        $this->debug('Creating temp table for users to process');

        $strQuery = "
         CREATE TEMPORARY TABLE reporting.tmpServicesToProcess{$this->strScriptHandle}(
         intServiceID INT UNSIGNED NOT NULL,
         PRIMARY KEY(intServiceID)
         ) ENGINE=MEMORY";

        PrimitivesQueryOrExit($strQuery, $dbCbc);

        $strQuery = "
          INSERT INTO reporting.tmpServicesToProcess{$this->strScriptHandle}
          SELECT
          DISTINCT
           (u.service_id + 0) AS intServiceID
          FROM
           reporting.tblTotalBandwidthUsed bu
          INNER JOIN reporting.tblBandwidthThreshold bt
            ON bu.radius_id = bt.intRadiusId
            AND (bu.bytes_transfered + bu.bytes_recieved) >= bt.intBandwidthThreshold
            AND bu.filtered = 0
          INNER JOIN
           reporting.users u ON bt.intRadiusId = u.radius_id
          WHERE
           bu.group_id IN({$this->strAdslGroupIds})
          AND
           bu.dtmLastProcessed > '{$this->getLastRun()}'
          AND
           (bu.bytes_transfered + bu.bytes_recieved) > 0
          AND
           u.service_id > 0
         ";

        PrimitivesQueryOrExit($strQuery, $dbCbc);

        $this->debug('Temp table created, processing users');

        return true;
    }

    /**
     * setProcessingStart
     *
     * @access public
     * @return void
     */
    public function setProcessingStart()
    {
        if (!$this->strScriptHandle) {
            return false;
        }

        $strQuery = "
         UPDATE
          reporting.tblCbcProcessScripts
         SET
          dtmStart = now()
         WHERE
          vchScriptHandle = '{$this->strScriptHandle}'
         ";

        $dbCbc = get_named_connection_with_db('cbc');
        PrimitivesQueryOrExit($strQuery, $dbCbc);

        return true;
    }

    /**
     * setProcessingStop
     *
     * @access public
     * @return void
     */
    public function setProcessingStop()
    {
        if (!$this->strScriptHandle) {
            return false;
        }

        $strQuery = "
          UPDATE
           reporting.tblCbcProcessScripts
          SET
           dtmEnd = now()
          WHERE
           vchScriptHandle = '{$this->strScriptHandle}'
         ";

        $dbCbc = get_named_connection_with_db('cbc');
        PrimitivesQueryOrExit($strQuery, $dbCbc);

        return true;
    }

    /**
     * getUsersToProcess - gets users to process from cbc
     *
     * @param integer $intBatch Batch
     * @param array   $dbCbc    CBC db details
     *
     * @access public
     *
     * @return void
     */
    public function getUsersToProcess($intBatch = null, $dbCbc)
    {
        if (!$dbCbc['handle']) {

            $this->addError($arrService, 'Failed to connect to CBC reporting db.');
            $this->endProfile(__METHOD__);

            return false;
        }

        //lets get start and limit values
        $intLimit = 100;
        if (is_null($intBatch) || $intBatch <= 1 || !is_numeric($intBatch)) {

            $intStart = 0;

        } else {

            $intStart = $intLimit * ($intBatch - 1);
        }

        //get users based on criteria
        $strQuery = "
          SELECT
          DISTINCT
           intServiceID
          FROM
           reporting.tmpServicesToProcess{$this->strScriptHandle}
          LIMIT {$intStart}, {$intLimit}
          ";

        $resResult = PrimitivesQueryOrExit($strQuery, $dbCbc, 'Cbc_PaygBwMaintenance::getUsersToProcess', false);

        if (!$resResult) {

            $this->addQueryError($arrService, $strQuery, $dbCbc);
            $this->endProfile(__METHOD__);

            return false;
        }

        $arrResults = PrimitivesResultsAsListGet($resResult);

        return $arrResults;
    }


    /**
     * Get usage
     *
     * @param array $arrService Service details
     *
     * @return void
     */
    public function getUsage($arrService)
    {
        $this->startProfile(__METHOD__);

        $dbCbc = get_named_connection_with_db('cbc_reporting', false);

        if (!$dbCbc['handle']) {

            $this->addError($arrService, 'Failed to connect to CBC reporting db.');

            $this->endProfile(__METHOD__);

            return false;
        }

        $strQuery = "SELECT bu.filtered, SUM(bu.bytes_recieved + bu.bytes_transfered) as intBandwidthBytes
                       FROM tblTotalBandwidthUsed bu
                 INNER JOIN users u
                         ON bu.radius_id = u.radius_id
                      WHERE u.username = '" . PrimitivesRealEscapeString($arrService['strUsername'], $dbCbc) . "'
                        AND u.isp = '" . PrimitivesRealEscapeString($arrService['strIsp'], $dbCbc) . "'
                        AND bu.group_id IN({$this->strAdslGroupIds})
                   GROUP BY filtered";

        $res = PrimitivesQueryOrExit($strQuery, $dbCbc, 'Cbc_PaygBwMaintenance::getUsage', false);

        if (!$res) {

            $this->addQueryError($arrService, $strQuery, $dbCbc);

            $this->endProfile(__METHOD__);

            return false;
        }

        $arrResults = PrimitivesResultsAsArrayGet($res, 'filtered');

        $intPeakUsageBytes = isset($arrResults[FILTER_PEAK]['intBandwidthBytes']) ?
            $arrResults[FILTER_PEAK]['intBandwidthBytes'] : 0;

        $intOffPeakUsageBytes = isset($arrResults[FILTER_OFFPEAK]['intBandwidthBytes']) ?
            $arrResults[FILTER_OFFPEAK]['intBandwidthBytes'] : 0;

        $this->endProfile(__METHOD__);

        $arrUsage = array($intPeakUsageBytes, $intOffPeakUsageBytes);

        return $arrUsage;
    }

    /**
     * Send Pre Breach email
     *
     * @param array $arrService Service details
     *
     * @return boolean
     */
    protected function sendPreBreachEmail($arrService)
    {
        $arrData = array(
            'bolFixed'           => ($arrService['bolCBCFix'] == 1) ? (1) : (0),
            'bolFlex'            => ($arrService['bolCBCFix'] == 0) ? (1) : (0),
            'bolUpsell'          => 0,
            'intIncludedUsageGb' => floor($arrService['intIncludedBandwidthBytes'] / GIGABYTE) . "GB",
            'strProductName'     => $arrService['strProductName'],
            'bolUpsellValue'     => false,
            'bolUpsellPro'       => false
        );

        $arrIsp = array('partner', 'plus.net', 'force9', 'freeonline', 'vodafone');

        if (in_array($arrService['strProductIsp'], $arrIsp)) {

            $intRoundingInterval  = getSteppingByBandwidthStep($arrService['intCBCExtraBandwidthStepBytes']);
            $strUnit = $this->getUnit($intRoundingInterval);
            $intDecimalPlaces = (($arrService['intCBCExtraBandwidthStepBytes'] == bcdiv(GIGABYTE, 2)) ? 1 : 0);
            $intFactor = $arrService['intCBCExtraBandwidthStepBytes'] / $intRoundingInterval;
            $intFactor = ($intDecimalPlaces == 1) ? $intFactor : ceil($intFactor);
            $arrData['intExtraUsageCharge'] = number_format(
                round($intFactor * $arrService['decCBCExtraGigabytePrice'], 2),
                2,
                '.',
                ' '
            );
            $arrData['strUsageStep'] = round(
                $arrService['intCBCExtraBandwidthStepBytes'] / $intRoundingInterval,
                $intDecimalPlaces
            ) . $strUnit;
        }

        $valueFamilies = array('VALUE', 'VALUE_SOLUS', 'VALUE_DUALPLAY');

        if (in_array($arrService['strFamilyHandle'], $valueFamilies)) {

            if ($arrService['strVariantHandle'] == 'STANDARD') {

                $arrData['bolUpsellValue'] = true;

            } elseif ($arrService['strVariantHandle'] == 'PRO') {

                $arrData['bolUpsellPro'] = true;
            }
        }

        if ($arrService['strProductIsp'] === 'madasafish') {  // one to go!

            $strTemplatePath = '/local/data/mis/portal_modules/mailtemplates/cbc/CBCEmail_PreBreach_eb189.txt';

            $this->debug("mailer_send_customer_mail: $strTemplatePath");
            mailer_send_customer_mail($arrService['intServiceId'], $strTemplatePath, $arrData, true, true);

            return true;

        } else {

            if ('partner' == $arrService['strProductIsp']) {

                $strTemplate = 'PartnerCBCEmail_PreBreach';

            } elseif ('business' == $arrService['strType']) {

                $strTemplate = 'broadband_max_usage_business';

            } else {

                $strTemplate = 'broadband_max_usage';
            }

            EmailHandler::sendEmail($arrService['intServiceId'], $strTemplate, $arrData);
            $this->debug("sendPreBreachEmail: $strTemplate");

            return true;
        }
    } // function sendPreBreachEmail($arrService)

    /**
     * Send Squash emails
     *
     * @param array $arrService Service details
     *
     * @return void
     */
    public function sendSquashingEmail($arrService)
    {
        $intCurrentAllowanceBytes = $arrService['intIncludedBandwidthBytes'] + $arrService['intLatestServiceLogBytes'];
        $intCurrentAllowanceGb = round($intCurrentAllowanceBytes / GIGABYTE, 2);

        $strCurrentAllowance = $intCurrentAllowanceGb . 'GB';

        $arrData = array(
            'strActualProduct'         => $arrService['strServiceDefinitionName'],
            'strBillingDate'           => date('F jS Y', strtotime($arrService['dteNextInvoice'])),
            'current_usage_allowance'  => $strCurrentAllowance,
        );

        // Legacy compatibility
        $arrData['billing_date'] = $arrData['strBillingDate'];

        // For John Lewis and Partner accounts use EmailHandler
        if (in_array($arrService['strProductIsp'], array('johnlewis', 'partner'))) {

            if ($arrService['strProductIsp'] == 'partner') {

                $templateHandle = 'PartnerserviceSquashed';
            }

            if ($arrService['strProductIsp'] == 'johnlewis') {

                $templateHandle = 'serviceSquashed';
            }

            $this->sendEmail($arrService['intServiceId'], $templateHandle, $arrData);

            return true;

        } else {

            // Drop back to legacy send email function for all other account types.

            if (false !== strpos($arrService['strServiceDefinitionName'], 'Essential')) {

                $strTemplatePath
                    = '/local/data/mis/database/application_apis/Cbc/email_templates/serviceSquashedEssential.txt';

            } elseif (false !== strpos($arrService['strServiceDefinitionName'], 'Broadband Your Way Option 1')) {

                $strTemplatePath
                    = '/local/data/mis/database/application_apis/Cbc/email_templates/serviceSquashedOption1.txt';

            } elseif (false === strpos($arrService['strServiceDefinitionName'], 'Broadband Your Way Pro')) {

                $strTemplatePath = '/local/data/mis/database/application_apis/Cbc/email_templates/serviceSquashed.txt';

            } else {

                $strTemplatePath
                    = '/local/data/mis/database/application_apis/Cbc/email_templates/serviceSquashedPro.txt';
            }

            $arrVars = $this->getSquashingEmailVars($arrService);

            $this->debug("sendSquashingEmail: mailer_send_customer_mail");
            mailer_send_customer_mail($arrService['intServiceId'], $strTemplatePath, $arrVars, true, true);
        }
    }

    /**
     * Wrapper on call to EmailHandler::sendEmail.
     *
     * @param integer $serviceId      Service ID
     * @param string  $templateHandle Email template handle
     * @param array   $templateData   Email template data
     *
     * @return void
     */
    protected function sendEmail($serviceId, $templateHandle, $templateData)
    {
        EmailHandler::sendEmail($serviceId, $templateHandle, $templateData);
    }

    /**
     * Metronet included limit reached
     *
     * @param array   $arrService          Service details
     * @param integer $intRoundingInterval Interval
     *
     * @return void
     */
    public function metronetIncludedLimitReached($arrService, $intRoundingInterval)
    {
        $this->startProfile(__METHOD__);

        $dbCore = get_named_connection_with_db('userdata', false);

        if (!$dbCore['handle']) {

            $this->endProfile(__METHOD__);

            $this->addError($arrService, 'Failed to connect to core db.');

            return;
        }

        $strSQLPreviousVMBUResetDate = "SELECT DATE(MAX(dtmReset)) AS dteReset FROM tblVMBUBillingResetLog
                                        WHERE intServiceID = {$arrService['intServiceId']}";

        $res = PrimitivesQueryOrExit($strSQLPreviousVMBUResetDate, $dbCore, 'metronetIncludedLimitReached', false);
        if (!$res) {

            $this->addQueryError($arrService, $strSQLPreviousVMBUResetDate, $dbCore);

            $this->endProfile(__METHOD__);

            return;
        }

        $dtePreviousBillingDate = PrimitivesResultGet($res, 'dteReset');

        //for new users - if bandwidth has never been reset...
        if ($dtePreviousBillingDate === '') {

            $dtePreviousBillingDate = date('Y-m-d', strtotime("{$arrService['dteNextInvoice']} - 1 month"));
            //$arrUXTPeriods = UserdataGetMonthlyInvoicePeriod($arrService['intServiceId']);
            //$dtePreviousBillingDate = date('Y-m-d', $arrUXTPeriods['uxtPeriodStart']);
        }

        $strQuery = "SELECT COUNT(*) AS n FROM tblCBCBandwidthUsage
                     WHERE intServiceID = {$arrService['intServiceId']}
                     AND dtmDate > '{$dtePreviousBillingDate}'
                     AND intGigaBytesUsed = {$arrService['intIncludedBandwidthBytes']}";

        $res = PrimitivesQueryOrExit($strQuery, $dbCore, 'metronetIncludedLimitReached', false);

        if (!$res) {

            $this->addQueryError($arrService, $strQuery, $dbCore);

            $this->endProfile(__METHOD__);

            return;
        }

        if (PrimitivesResultGet($res, 'n')) {
            return;
        }

        $strQuery = "INSERT INTO tblCBCBandwidthUsage SET
                     intServiceID = {$arrService['intServiceId']},
                     dtmDate = NOW(),
                     intGigaBytesUsed = {$arrService['intIncludedBandwidthBytes']}";

        $bolSuccess = PrimitivesQueryOrExit($strQuery, $dbCore, 'metronetIncludedLimitReached', false);

        if (!$bolSuccess) {

            $this->addQueryError($arrService, $strQuery, $dbCore);

            $this->endProfile(__METHOD__);

            return;
        }

        $arrAdditionalVars = array(
            'customer_name'     => "{$arrService['strSalutation']} {$arrService['strForenames']} " .
                                   "{$arrService['strSurname']}",
            'included_data_mb'  => $arrService['intIncludedBandwidthBytes'] / $intRoundingInterval,
            'price_ceiling'     => $arrService['decCBCCapPrice'],
            'end_of_month_date' => $arrService['next_invoice']
        );

        mailer_send_customer_mail(
            $arrService['intServiceId'],
            CBC_EMAIL_TEMPLATE_DIR . 'CBCEmail_Metronet_Exceed_Included_Limit.txt',
            $arrAdditionalVars,
            true,
            true
        );

        $this->endProfile(__METHOD__);
    }

    /**
     * Update PAYG logs details
     *
     * @param array   $arrService           Service details
     * @param array   $arrUsageLevels       Usage level
     * @param integer $intRoundingInterval  Interval
     * @param integer $intNonFreeUsageBytes Non free usage in bytes
     *
     * @return void
     */
    public function updatePaygServiceLog($arrService, $arrUsageLevels, $intRoundingInterval, $intNonFreeUsageBytes)
    {
        $this->startProfile(__METHOD__);

        $dbCore = get_named_connection_with_db('userdata', false);

        if (!$dbCore['handle']) {

            $this->addError($arrService, 'Failed to connect to core db.');

            $this->endProfile(__METHOD__);

            return false;
        }

        $arrLogEntries = array();

        //for the service log we only want to maintain one record per user - for efficiency reasons
        $intUsageLevelMaxIndex = count($arrUsageLevels)-1;

        if (!is_null($arrService['intLatestServiceLogBytes'])
            && !is_null($arrUsageLevels[$intUsageLevelMaxIndex])
            && $arrUsageLevels[$intUsageLevelMaxIndex] >= 0
        ) {

            $strQuery = "UPDATE userdata.tblCBCPAYGServiceLog SET
                         intPAYGBandwidthBytes = {$arrUsageLevels[$intUsageLevelMaxIndex]},
                         dtmAdded = NOW()
                         WHERE intServiceID = {$arrService['intServiceId']}";
            $bolSuccess = PrimitivesQueryOrExit($strQuery, $dbCore, 'insertCbcServiceLogEntries', false);

        } elseif (!is_null($arrUsageLevels[$intUsageLevelMaxIndex])
            && $arrUsageLevels[$intUsageLevelMaxIndex] >= 0
            && is_null($arrService['intLatestServiceLogBytes'])
        ) {

            $strQuery = 'INSERT INTO userdata.tblCBCPAYGServiceLog
                         (intServiceID, intPAYGBandwidthBytes, dtmAdded) VALUES ' .
                         "({$arrService['intServiceId']}, {$arrUsageLevels[$intUsageLevelMaxIndex]}, NOW())";
            $bolSuccess = PrimitivesQueryOrExit($strQuery, $dbCore, 'insertCbcServiceLogEntries', false);
        }

        if (!$bolSuccess || !self::autoSetCbcBandwidthThreshold($arrService['intServiceId'])) {

            $this->addQueryError($arrService, $strQuery, $dbCore);

            $this->endProfile(__METHOD__);

            return false;
        }

        // Send out email(s)

        $arrEmails = $this->getUsageEmailData(
            $arrService,
            $arrUsageLevels,
            $intRoundingInterval,
            $intNonFreeUsageBytes
        );

        foreach ($arrEmails as $arrEmail) {

            if ($arrService['strProductIsp'] === 'madasafish') {

                mailer_send_customer_mail(
                    $arrService['intServiceId'],
                    $arrEmail['strEmailTemplate'],
                    $arrEmail['arrAdditionalVars'],
                    true,
                    true,
                    false,
                    '<EMAIL>'
                );

            } else {

                try {

                    EmailHandler::sendEmail(
                        $arrService['intServiceId'],
                        $arrEmail['strEmailTemplate'],
                        $arrEmail['arrAdditionalVars']
                    );

                } catch (EmailHandler_Exception $excEmailHandler) {

                    pt_raise_autoproblem(
                        'EmailHandler generated an error',
                        'EmailHandler generated an error',
                        $excEmailHandler->getMessage(),
                        __FILE__
                    );
                }
            }
        }

        $this->endProfile(__METHOD__);

        return true;
    }

    /**
     * set PAYG configuration
     *
     * @param array   $arrService           Service details
     * @param integer $intTotalUsageBytes   Total bytes usage
     * @param integer $intChargedUsageBytes Charged bytes usage
     * @param integer $intUsageStep         Usage step
     * @param float   $floAdditionalCost    Additional cost
     *
     * @return void
     */
    public function setPaygConfig(
        $arrService,
        $intTotalUsageBytes,
        $intChargedUsageBytes,
        $intUsageStep,
        $floAdditionalCost
    ) {
        $this->startProfile(__METHOD__);

        $dbCore = get_named_connection_with_db('userdata', false);

        if (!$dbCore['handle']) {

            $this->addError($arrService, 'Failed to connect to core db.');

            $this->endProfile(__METHOD__);

            return;
        }

        if (is_null($intUsageStep)) {

            $intUsageStep = 'NULL';
        }

        if ($arrService['intConfigID']) {

            $strQuery = "UPDATE userdata.tblConfigCBCPAYG SET
                                   intComponentId = {$arrService['intComponentId']},
                           intTotalBandwidthBytes = $intTotalUsageBytes,
                         intChargedBandwidthBytes = $intChargedUsageBytes,
                                     intUsageStep = $intUsageStep,
                                decAdditionalCost = $floAdditionalCost,
                                         vchDBSrc = 'CbcPaygBwMaintenance.php'
                         WHERE intConfigID = {$arrService['intConfigID']}";

            $bolSuccess = PrimitivesQueryOrExit($strQuery, $dbCore, 'setPaygConfig', false);

        } else {

            $strQuery = "INSERT INTO userdata.tblConfigCBCPAYG SET
                                   intComponentId = {$arrService['intComponentId']},
                           intTotalBandwidthBytes = $intTotalUsageBytes,
                         intChargedBandwidthBytes = $intChargedUsageBytes,
                                     intUsageStep = $intUsageStep,
                                decAdditionalCost = $floAdditionalCost,
                                         vchDBSrc = 'CbcPaygBwMaintenance.php'";

            $bolSuccess = PrimitivesQueryOrExit($strQuery, $dbCore, 'setPaygConfig', false);
            $intConfigId = PrimitivesInsertIdGet($dbCore);

            if (0 < $intConfigId) {

                $strQueryUpdate = " UPDATE userdata.components
                                    SET config_id = $intConfigId
                                    WHERE component_id = {$arrService['intComponentId']}";
                $bolSuccessUpdate = PrimitivesQueryOrExit($strQueryUpdate, $dbCore, 'setPaygConfig', false);

                if (!$bolSuccessUpdate) {
                    $this->addQueryError($arrService, $strQueryUpdate, $dbCore);
                }
            }
        }

        if (!$bolSuccess) {

            $this->addQueryError($arrService, $strQuery, $dbCore);
        }

        $this->endProfile(__METHOD__);
    }

    /**
     * Send a service notice triggered by DTW
     *
     * @param integer $intServiceId Service Id
     *
     * @access private
     *
     * @return void
     */
    private function sendDtwServiceNotice($intServiceId)
    {
        $this->debug('Send Service Notice for DTW function Called');

        /* create service notice */
        $arrServiceData = userdata_service_get($intServiceId);
        userdata_service_event_add(
            $intServiceId,
            SERVICE_EVENT_USAGE_LIMIT_RESTRICTION_ADDED,
            $arrServiceData['type'],
            $arrServiceData['type']
        );
    }

    /**
     * Send a service notice triggered by DTW
     *
     * @return void
     */
    public function squashServices()
    {
        $this->startProfile(__METHOD__);

        // arrServicesToSquash is now an array of service-related data, as opposed to being
        // simply an array of service IDs

        if (empty($this->arrServicesToSquash)) {

            $this->endProfile(__METHOD__);

            return;
        }

        $objEllacoyaManager = new EllacoyaManager('PLUSNET');

        $intItemsRemaining = count($this->arrServicesToSquash);

        $intOffset = 0;

        do {

            $intBatchSize = min($intItemsRemaining, self::SERVICE_SQUASH_BATCH_SIZE);

            $arrServiceIdBatch = $this->getSquashQueueSegment($intOffset, $intBatchSize);

            $arrResult = $objEllacoyaManager->squashSubscriberBatch($arrServiceIdBatch);

            // if it's not an array, then it's going to be false, thus we can't actually do
            // anything anyway (in terms of sending an email) but at least eventGetAll()
            // later on will determine the possible reason(s) as to why squashSubscriberBatch()
            // returned false.

            if (is_array($arrResult) && isset($arrResult['results'])) {

                foreach ($arrResult['results'] as $intServiceId => $bolResult) {

                    $this->debug('intServiceId = '.$intServiceId);
                    $this->debug('bolResult = '.$bolResult);

                    if ($bolResult) {

                        $this->debug('Service Id  = '.$intServiceId);

                        if (!in_array($intServiceId, $this->arrDtwCustomer)) {

                            $this->debug("Send Non-DTW Email to {$intServiceId}");

                            $this->sendEmailForSuccessfulSquashes($intServiceId);

                        } elseif (in_array($intServiceId, $this->arrDtwCustomer)) {

                            $this->debug("Sending DTW Service Notice to {$intServiceId}");
                            $this->sendDtwServiceNotice($intServiceId);

                            $arrProduct = userdataServiceProductGet($intServiceId);

                            // check if payg bandwidth component is allowed on this customers account
                            $dtwServiceAllowed = product_service_is_allowed_component(
                                $arrProduct['intSDI'],
                                COMPONENT_CBC_PAYG_BANDWIDTH
                            );

                            if ($dtwServiceAllowed == true && $arrProduct['strISP'] != 'metronet') {
                                $this->debug("Sending DTW Email to {$intServiceId}");
                                $this->sendEmailForDtwServices(
                                    $intServiceId,
                                    $this->arrServicesToSquash[(integer) $intServiceId]
                                );
                            }
                        }
                    }
                }
            }

            $intItemsRemaining -= $intBatchSize;
            $intOffset += $intBatchSize;

        } while ($intItemsRemaining > 0);

        $arrEvents = $objEllacoyaManager->eventGetAll();

        foreach ($arrEvents as $arrEvent) {

            $strError = "{$arrEvent['strMessage']} at {$arrEvent['strFile']} line {$arrEvent['intLine']}";

            foreach ($arrEvent['arrArgs'] as $strKey => $unkValue) {

                $strError .= ", $strKey = $strValue";
            }

            $this->arrErrors[] = $strError;
        }

        $this->endProfile(__METHOD__);
    }

    /**
     * Add to Squash Queue
     *
     * @param array $arrService array of service details
     *
     * @return void
     */
    private function addToSquashQueue($arrService)
    {
        // can I safely assume that no service IDs will have leading zeroes? Not 100%
        // sure, so performing cast to remove any rogue leading zeroes.

        $this->arrServicesToSquash[(integer) $arrService['intServiceId']] = $arrService;
    }

    /**
     * Get Squash Queue Segment
     *
     * @param intger $intOffset Offset
     * @param intger $intSize   Size
     *
     * @return array
     */
    private function getSquashQueueSegment($intOffset, $intSize)
    {
        $arrServiceBatch = array_slice($this->arrServicesToSquash, $intOffset, $intSize);

        $arrServiceIds = array();

        foreach ($arrServiceBatch as $arrService) {
            $arrServiceIds[] = $arrService['intServiceId'];
        }

        return $arrServiceIds;
    }

     /**
     * Send an email for successful squashes
     *
     * @param integer $intServiceId Service Id
     *
     * @return void
     */
    private function sendEmailForSuccessfulSquashes($intServiceId)
    {
        // performing cast, as I don't know whether they will have leading zeroes or not?
        $this->sendSquashingEmail($this->arrServicesToSquash[(integer) $intServiceId]);
    }

    /**
     * Send an email triggered by DTW
     *
     * @param integer $intServiceId Service Id
     * @param array   $arrService   array of service details
     *
     * @access public
     *
     * @return void
     */
    private function sendEmailForDtwServices($intServiceId, $arrService)
    {
        $strEmailTemplate = 'dtw_restricted';
        $arrProductDetails = userdataServiceProductGet($intServiceId);
        if ('business' == $arrProductDetails['strProductType']) {

            $strEmailTemplate = 'dtw_restricted_business';
        }

        // Set the DTW limit amount in the additional vars
        $arrAdditionalVars = array();

        $floCurrentFixedRate = GetServiceMiniumPaymentLimit($intServiceId);

        // Convert payment limit to pounds and pence
        $floCurrentPaymentLimit = $arrService['intPaymentLimit'] / 100;

        // next subscription rate
        $floOngoingSubscriptionCost = GetOngoingSubscriptionCostByServiceId($intServiceId);
        if (!empty($floOngoingSubscriptionCost)) {

            $floCurrentPaymentLimit = $floCurrentPaymentLimit - $floOngoingSubscriptionCost;

        } else {

            $floCurrentPaymentLimit = $floCurrentPaymentLimit - $floCurrentFixedRate;
        }
        $arrAdditionalVars['floDataTransferCost'] = $floCurrentPaymentLimit;

        $this->debug('Send Email on DTW for = '.$intServiceId);

        EmailHandler::sendEmail($intServiceId, $strEmailTemplate, $arrAdditionalVars);
    }

    /**
     * Adding error messages
     *
     * @param array  $arrService Array of service data
     * @param string $strMessage Error Message
     *
     * @return void
     */
    public function addError($arrService, $strMessage)
    {
        $this->arrErrors[] = "Service {$arrService['intServiceId']}: $strMessage";
    }

    /**
     * Adding query error messages
     *
     * @param array  $arrService Array of service data
     * @param string $strQuery   sql statement
     * @param array  $db         database details
     *
     * @return void
     */
    public function addQueryError($arrService, $strQuery, $db)
    {
        $this->addError($arrService, "query '$strQuery' failed: " . mysql_error($db['handle']));
    }

    /**
     * Set whether or not to do profiling on parts of process
     *
     * @param boolean $bolProfile boolean value
     *
     * @return void
     */
    public function setProfile($bolProfile)
    {
        $this->bolProfile = $bolProfile;
    }

    /**
     * Start a point of profiling a section of process
     *
     * @param string $strName Name
     *
     * @return void
     */
    public function startProfile($strName)
    {
        if (true === $this->bolProfile) {

            $this->arrProfile[$strName]['floStart'] = microtime(true);
        }

    } // End of function startProfile

    /**
     * End a point of profiling a section of process
     *
     * @param string $strName Name
     *
     * @return void
     */
    public function endProfile($strName)
    {
        if (true === $this->bolProfile && isset($this->arrProfile[$strName]['floStart'])) {

            $this->arrProfile[$strName]['floEnd'] = microtime(true);

            echo "$strName : " . round(
                $this->arrProfile[$strName]['floEnd'] - $this->arrProfile[$strName]['floStart'],
                4
            ) . "\n";

            unset($this->arrProfile[$strName]);

        } // end if

    } // End of function endProfile

    /**
     * Print message, just for helping to debug
     *
     * @param string $strMessage message
     *
     * @return void
     */
    private function debug($strMessage)
    {
        if ($this->bolDebug) {

            print "[" . date('Y-m-d H:i:s') . "] {$this->intServiceIdForDebug}: $strMessage\n";
        }
    }

    /**
     * Auto Set Cbc Bandwidth Threshold
     *
     * @param integer $intServiceId Service ID
     *
     * @return boolean
     */
    public static function autoSetCbcBandwidthThreshold($intServiceId)
    {
        self::includeLegacyFiles();

        if (!ctype_digit($intServiceId)) {
            return false;
        }

        $arrService = self::getThresholdDetails($intServiceId);

        if (empty($arrService)) {
            return false;
        }

        $intThreshold = null;

        // find out the users threshold for pre breach email
        switch ($arrService['strProductIsp']) {
            case 'plus.net':
            case 'freeonline':
            case 'force9':
            case 'vodafone':
            case 'johnlewis':
                $includedProductFamilies = array(
                    ProductFamily_Legacy::BBYW_PRODUCT_FAMILY_HANDLE,
                    ProductFamily_Value::PRODUCT_FAMILY_HANDLE,
                    ProductFamily_ValueSolus::PRODUCT_FAMILY_HANDLE,
                    ProductFamily_ValueDualPlay::PRODUCT_FAMILY_HANDLE,
                    ProductFamily_Bpr09::PRODUCT_FAMILY_HANDLE,
                    ProductFamily_Mps::PRODUCT_FAMILY_HANDLE,
                    ProductFamily_JohnLewis2011::PRODUCT_FAMILY_HANDLE,
                    ProductFamily_Res2012::PRODUCT_FAMILY_HANDLE,
                    ProductFamily_PlusnetSolus2013::PRODUCT_FAMILY_HANDLE,
                    ProductFamily_PlusnetDualPlay2013::PRODUCT_FAMILY_HANDLE,
                    ProductFamily_Brp12Solus::PRODUCT_FAMILY_HANDLE,
                    ProductFamily_Brp12Dual::PRODUCT_FAMILY_HANDLE
                );

                if (!in_array($arrService['strFamilyHandle'], $includedProductFamilies)) {
                    break;
                }

                // Take note of the fall through
            case 'partner':
            case 'madasafish':
                $intThreshold = bcmul($arrService['intIncludedBandwidthBytes'], self::USAGE_THRESHOLD_PERCENT);
                break;
            case 'greenbee':
            case 'waitrose':
                $intThreshold = $arrService['intIncludedBandwidthBytes'] - 268435456;
                break;
            default:
                break;
        }

        // find radius id
        $intRadiusId = self::getRadiusIDFromUsernameIsp($arrService['strUsername'], $arrService['strIsp']);

        if (!ctype_digit($intRadiusId)) {
            return false;
        }

        // get filtered usage from reporting.tblTotalBandwidthUsed
        $arrUsage = self::getTotalBandwidthUsedByType($intServiceId, GetAllADSLGroups());

        if (is_null($intThreshold) || $arrUsage['intChargeableBytes'] > $intThreshold) {

            $intThreshold = $arrService['intIncludedBandwidthBytes'] + $arrService['intLatestServiceLogBytes'];
        }

        return self::setThreshold($intRadiusId, $intThreshold);
    }

    /**
     * Get threshold details for the service ID given.
     *
     * @param integer $serviceId Service ID
     *
     * @return array Threshold details for service ID given
     */
    protected static function getThresholdDetails($serviceId)
    {
        // do query from the master to avoid any kind of lag!
        $dbUserdata = get_named_connection_with_db('userdata');

        // get new threshold from userdata.tblCBCPAYGServiceLog
        $strQuery = "   SELECT DISTINCT
                          s.service_id AS intServiceId,
                          s.username AS strUsername,
                          s.isp AS strIsp,
                          pf.vchHandle as strFamilyHandle,
                          cbcf.intIncludedBandwidthBytes,
                          ap.isp AS strProductIsp,
                          cpsl.intPAYGBandwidthBytes as intLatestServiceLogBytes

                        FROM userdata.services s

                        INNER JOIN userdata.users u
                          ON s.user_id = u.user_id

                        INNER JOIN userdata.components c
                          ON c.service_id = s.service_id
                          AND c.component_type_id IN(" .
                          COMPONENT_CBC_PAYG_BANDWIDTH . "," .
                          COMPONENT_CBC_PAYG_CALENDAR_MONTH . "," .
                          COMPONENT_CBC_PAYG_END_OF_MONTH . ")
                          AND c.status = 'active'

                        INNER JOIN products.adsl_product ap
                          ON ap.service_definition_id = s.type

                        INNER JOIN products.service_definitions sd
                          ON ap.service_definition_id = sd.service_definition_id

                        INNER JOIN products.tblCBCSpeedProdLink cspl
                          ON cspl.intServiceDefinitionID = s.type

                        INNER JOIN products.vblCBCSpeed cs
                          ON cs.intCBCSpeedID = cspl.intCBCSpeedID
                          AND cs.dtmEnd IS NULL

                        INNER JOIN userdata.components cf
                          ON cf.service_id = s.service_id
                          AND cf.component_type_id = " . COMPONENT_CBC_FLEX . "
                          AND cf.status = 'active'

                        INNER JOIN userdata.tblConfigCBCFlex ccf
                          ON ccf.intConfigID = cf.config_id

                        INNER JOIN products.vblCBCFlex cbcf
                          ON ccf.intCBCFlexID = cbcf.intCBCFlexID

                        LEFT JOIN userdata.tblConfigCBCPAYG ccp
                          ON ccp.intConfigID = c.config_id

                        LEFT JOIN userdata.tblCBCServicePaymentLimit pl
                          ON pl.intServiceID = s.service_id
                          AND pl.dtmStart <= NOW()
                          AND (pl.dtmEnd IS NULL OR pl.dtmEnd > NOW())

                        LEFT JOIN userdata.tblSquashedBandwidth sb
                          ON sb.intServiceId = s.service_id

                        LEFT JOIN products.tblProductVariant pv ON sd.intProductVariantId = pv.intProductVariantId
                        LEFT JOIN products.tblProductFamily pf ON pv.intProductFamilyId = pf.intProductFamilyId

                        LEFT JOIN userdata.tblCBCPAYGServiceLog cpsl ON s.service_id = cpsl.intServiceID

                        WHERE s.service_id = $serviceId

                          AND (sb.bolCurrentlySquashed IS NULL OR sb.bolCurrentlySquashed = 0)

                        ";

        $resResult = PrimitivesQueryOrExit($strQuery, $dbUserdata);

        if (!$resResult || PrimitivesNumRowsGet($resResult) == 0) {
            return false;
        }

        return PrimitivesResultGet($resResult);
    }

    /**
     * Set threshold for the radius ID given.
     *
     * @param integer $radiusId  Radius ID
     * @param integer $threshold New Threshold
     *
     * @return boolean
     */
    protected static function setThreshold($radiusId, $threshold)
    {
        $strSqlSetThreshold = "
            REPLACE INTO reporting.tblBandwidthThreshold (intRadiusId,intBandwidthThreshold)
            VALUES ($radiusId, $threshold)";

        $dbCbc = get_named_connection_with_db('cbc');

        $resResult = PrimitivesQueryOrExit($strSqlSetThreshold, $dbCbc);

        return $resResult ? true : false;
    }

    /**
     * Wrapper on radius_get_radius_id_from_username_isp
     *
     * @param string $username Username
     * @param string $isp      ISP
     *
     * @return integer Corresponding radius ID from a username_isp
     */
    protected static function getRadiusIDFromUsernameIsp($username, $isp)
    {
        return radius_get_radius_id_from_username_isp($username, $isp);
    }

    /**
     * Wrapper on getTotalBandwidthUsedByType
     *
     * @param integer $serviceID          Service ID
     * @param string  $adslDialupGroupIds comma seperated string of ADSL Dailup Group IDs
     *
     * @return array Total bandwidth used grouped by filtered (peak) and non-filtered (off-peak)
     */
    protected static function getTotalBandwidthUsedByType($serviceID, $adslDialupGroupIds)
    {
        return getTotalBandwidthUsedByType($serviceID, $adslDialupGroupIds);
    }
}
