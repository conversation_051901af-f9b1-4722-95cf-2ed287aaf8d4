<?php

	/////////////////////////////////////////////////////////////////////
	// File:     config-mysql-access.inc
	// Purpose:  Access mini-library for MySQL
	/////////////////////////////////////////////////////////////////////

	/////////////////////////////////////////////////////////////////////
	// Data

	$global_component_configurators["10"] = "config_mysql_configurator";
	$global_component_configurators["86"] = "config_mysql_configurator";
	$global_component_configurators["85"] = "config_mysql_configurator";

	// Data
	/////////////////////////////////////////////////////////////////////
	// Hack to insert the component configurator array into PHP5's global scope
	// if it's not already there
	if(!isset($GLOBALS['global_component_configurators']))
	{
		$GLOBALS['global_component_configurators'] = $global_component_configurators;
	}
	else
	{
		foreach($global_component_configurators as $intIndex => $strConfigurator)
		{
			$GLOBALS['global_component_configurators'][$intIndex] = $strConfigurator;
		}
	}


	/////////////////////////////////////////////////////////////
	// Function:  config_mysql_auto_configure
	// Purpose:   'unconfigured' -> 'queued-activate' state
	//            transition handler for auto-configuration
	// Arguments: $component_id
	/////////////////////////////////////////////////////////////

	function config_mysql_auto_configure ($component_id) {

		// Do nothing - user must request activation
		userdata_component_set_status ($component_id, 'unconfigured');

	}


	/////////////////////////////////////////////////////////////
	// Function:  config_mysql_auto_destroy
	// Purpose:   * -> 'queued-destroy' state
	//            transition handler for auto-destruction
	// Arguments: $component_id
	/////////////////////////////////////////////////////////////

	function config_mysql_auto_destroy ($component_id) {

		$component = userdata_component_get($component_id);

		switch ($component["status"]) {

		    // Free pass to "destroyed"
		    case "unconfigured":
			userdata_component_set_status ($component_id, "destroyed");
			return;
			break;

		    // Have or may have configuration
		    case "active":
		    case "deactive":
			break;

		    // Shouldn't be here
		    case "queued-reactivate":
		    case "queued-deactivate":
		    case "queued-activate":
		    case "queued-deconfigure":
		    case "queued-destroy":
			return;
			break;

		    // Already there
		    case "destroyed":
			return;
			break;

		    // The sky is falling!
		    default:
			return;
			break;

		}

		$service   = userdata_service_get  ($component['service_id']);
		$username  = $service['username'];

		// Connect to main server
		$chan = get_named_connection('mysql-databases');

		// Find the components databases and the hosts they live on
		$resultid = mysql_query ("select hostname, database_name, uid from mysql_master mm, mysql_available_hosts mh where mh.hostname_uid = mm.hostname_uid and mm.username  = \"$username\"");

		if($resultid)
		{
			while ($resultrow = mysql_fetch_row($resultid)) {
				// Remove the database from the master server
				mysql_query("DELETE FROM mysql_master WHERE uid='" . $resultrow[2] . "'", $chan);

				// Contact the server
				$dbchan = database_connect ('c', "$resultrow[0]", 'portal_user') or report_error ("Couldn't connect: ".mysql_error($dbchan));

				// Remove the database
				mysql_query ("drop database if exists {$resultrow[1]}", $dbchan) or report_error (__FILE__, __LINE__, "Couldn't disable access: ".mysql_error($dbchan));

				// Remove the access permissions
				mysql_select_db ("mysql", $dbchan);
				mysql_query ("delete from user where user=\"$username\"", $dbchan) or report_error ("Couldn't remove access rights: ".mysql_error($dbchan));
				mysql_query ("delete from db where Db='" . $resultrow[1] . "'", $dbchan) or report_error ("Couldn't remove access rights: ".mysql_error($dbchan));
				mysql_query ("flush privileges", $dbchan) or report_error ("Couldn't flush privileges: ".mysql_error($dbchan));
			}
		}
		userdata_component_set_status ($component_id, "destroyed");
	}

	function config_mysql_auto_enable ($component_id) {

		$component = userdata_component_get($component_id);

		switch ($component["status"]) {

		    // Enable me
		    case "deactive":
			break;

		    // Nothing to do here
		    case "active":
		    case "destroyed":
		    case "unconfigured":
			return;
			break;

		    // Shouldn't be here
		    case "queued-reactivate":
		    case "queued-deactivate":
		    case "queued-activate":
		    case "queued-deconfigure":
		    case "queued-destroy":
			return;
			break;

		    // The sky is falling!
		    default:
			return;
			break;

		}

		$service   = userdata_service_get  ($component['service_id']);
		$username  = $service['username'];

		// Connect to main server
		$chan = get_named_connection('mysql-databases');

		// Find the components databases and the hosts they live on
		$resultid = mysql_query ("select hostname from mysql_master mm, mysql_available_hosts mh where mh.hostname_uid = mm.hostname_uid and mm.username  = \"$username\"");

		while ($resultrow = mysql_fetch_row($resultid)) {
			// Contact the server
			$dbchan = database_connect ('c', "$resultrow[0]", 'portal_user') or report_error ("Couldn't connect: ".mysql_error($dbchan));
			mysql_select_db ("mysql", $dbchan) or report_error ("Couldn't select mysql database: ".mysql_error($dbchan));
			// Remove access
			mysql_query ("update user set host=\"%\" where user = \"$username\"") or report_error (__FILE__, __LINE__, "Couldn't disable access: ".mysql_error($dbchan));
			mysql_query ("flush privileges");
		}

		// Mark component as active
		userdata_component_set_status ($component_id, "active");
	}


	function config_mysql_auto_disable ($component_id) {

		$component = userdata_component_get($component_id);

		switch ($component["status"]) {

		    // Disable me
		    case "active":
			break;

		    // Nothing to do here
		    case "deactive":
		    case "destroyed":
		    case "unconfigured":
			return;
			break;

		    // Shouldn't be here
		    case "queued-reactivate":
		    case "queued-deactivate":
		    case "queued-activate":
		    case "queued-deconfigure":
		    case "queued-destroy":
			return;
			break;

		    // The sky is falling!
		    default:
			return;
			break;

		}

		$service   = userdata_service_get  ($component['service_id']);
		$username  = $service['username'];

		// Connect to main server
		$chan = get_named_connection('mysql-databases');

		// Find the components databases and the hosts they live on
		$resultid = mysql_query ("select hostname from mysql_master mm, mysql_available_hosts mh where mh.hostname_uid = mm.hostname_uid and mm.username  = \"$username\"");

		while ($resultrow = mysql_fetch_row($resultid)) {
			// Contact the server
			$dbchan = database_connect ('c', "$resultrow[0]", 'portal_user') or report_error ("Couldn't connect: ".mysql_error($dbchan));
			mysql_select_db ("mysql", $dbchan) or report_error ("Couldn't select mysql database: ".mysql_error($dbchan));
			// Remove access
			mysql_query ("update user set host=\".\" where user = \"$username\"") or report_error (__FILE__, __LINE__, "Couldn't disable access: ".mysql_error($dbchan));
			mysql_query ("flush privileges");
		}

		// Mark component as deactive
		userdata_component_set_status ($component_id, "deactive");
	}


	function config_mysql_configurator ($component_id, $action) {

		switch ($action) {

		    case "auto_configure":
			config_mysql_auto_configure ($component_id);
			break;

		    case "auto_disable":
			config_mysql_auto_disable ($component_id);
			break;

		    case "auto_enable":
			config_mysql_auto_enable ($component_id);
			break;

		    case "auto_refresh":
			// Nothing to do here
			break;

		    case "auto_destroy":
			config_mysql_auto_destroy ($component_id);
			break;

		    default:
			break;

		}

	}


?>
