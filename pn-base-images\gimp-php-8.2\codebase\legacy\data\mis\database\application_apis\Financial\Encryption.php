<?php
/**
 * Encryption / decryption functions used to payment details.
 *
 * @package LegacyCodebase
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
*/

require_once('/local/codebase2005/modules/Framework/Libraries/bootstrap.inc.php');

/**
 * Encrypts payment details by calling EncryptMan_Encryption::encrypt to 
 * keep the encryption code in a central location.
 * 
 * @param string $strPaymentDetails Payment detail string to encrypt
 *
 * @return string Encrypted payment details string
 */
function encryptPaymentDetails($strPaymentDetails)
{
    $objEncryption = EncryptMan_Encryption::getInstance('payments');
    $strDecryptedDetails = '';

    // Remove non-numeric characters (spaces, hypens, etc)
    $strPaymentDetails = preg_replace('/\D/', '', $strPaymentDetails);

    try {
        $strDecryptedDetails = $objEncryption->encrypt($strPaymentDetails);
    } catch (EncryptMan_Exception $e) {
        error_log($e->getMessage());
    }

    return $strDecryptedDetails;
}


/**
 * Decrypts payment details using the EncryptMan_Encryption class.
 * 
 * @param string $strEncryptedString Encrypted string to decrypt
 *
 * @return string Decrypted payment details string
 */
function decryptPaymentDetails($strEncryptedString)
{
    $strDecryptedDetails = '';
    $arrPaymentRegex = array('/^[0-9]{6,16}$/');
    $strEncryptedString = trim($strEncryptedString);
    $objEncryption = EncryptMan_Encryption::getInstance('payments');

    try {
        $strDecryptedDetails = $objEncryption->decrypt($strEncryptedString, $arrPaymentRegex);
    } catch (EncryptMan_InvalidKeyException $e) {
        error_log($e->getMessage());
    } catch (EncrypMan_Exception $ex) {
        error_log($ex->getMessage());
    }

    return $strDecryptedDetails;
}
