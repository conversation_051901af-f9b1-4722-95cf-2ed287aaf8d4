<?php

/**
 * Access library for WLR Product component
 *
 * @package    Core
 * @subpackage WLR
 * @access     public
 * <AUTHOR> <<EMAIL>>
 * @version    $Id: CWlrProductComponent.inc,v 1.12 2009-05-19 15:44:22 bwalton Exp $
 * @filesource
 */
require_once('/local/data/mis/database/database_libraries/components/CProductComponent.inc');

/**
 * WLR Product Component Super-Class
 *
 * Component for handling the Line Rental charge
 *
 * @access     public
 * <AUTHOR> <<EMAIL>>
 */
class CWlrProductComponent extends CProductComponent
{
    //
    // Properties
    //


    //
    // Constructor
    //

    /**
     * WLR Product Component Constructor
     *
     * <AUTHOR> <<EMAIL>>
     * @access public
     *
     * @param integer $intProductComponentInstanceId Product Component Instance ID
     *
     * @return boolean     Only if this is called as a member method of a sub-class
     */
    public function CWlrProductComponent($intProductComponentInstanceId)
    {
        return $this->CProductComponent($intProductComponentInstanceId);
    }

    //
    // Static Methods
    //


    //
    // Public Methods
    //

    /**
     * WLR Product Component renewal
     *
     * <AUTHOR> Jones" <<EMAIL>>
     * @access   public
     * @return   boolean
     */
    public function renew()
    {
        // Just call the CProductComponent::renew method. We don't want to do anything
        // special here, just maintain the inheritence chain.
        return parent::renew();
    }

    //
    // Configurator Methods
    //

    /**
     * Component configurator function. Re-enables the Product Component
     *
     * <AUTHOR> Zaki" <<EMAIL>>
     * @access   public
     * @return   boolean
     */
    public function reEnable()
    {
        $this->prvSetStatus('QUEUED_ACTIVATE');

        // Do we need to add a DoReEnable call here?

        // Set status to active
        $this->prvSetStatus('ACTIVE');

        // Sanity check
        $strStatusHandle = $this->callGetStatusHandleFromID($this->getStatusID());
        if ($strStatusHandle != 'ACTIVE') {
            return false;
        }

        // It worked. Log the event.
        $this->logStatusChange('ACTIVE');

        $this->refreshInstance($this->getProductComponentInstanceID());

        return true;
    }

    /**
     * Component configurator function. Enables the Product Component component
     *
     * @param array $arrArgs Arguments
     *
     * <AUTHOR> Jones" <<EMAIL>>
     * @access   public
     * @return   boolean
     */
    public function enable($arrArgs = array())
    {
        // Are there any pertinent arguments?
        // These likely come through via CWlrProduct::enable
        $uxtContractStart = isset($arrArgs['uxtContractStart']) ? $arrArgs['uxtContractStart'] : time();
        $uxtEnableDate = isset($arrArgs['uxtEnableDate']) ? $arrArgs['uxtEnableDate'] : null;
        if (isset($arrArgs['bundlableCallFeatures']) &&
            in_array($this->getProductComponentInstanceID(), $arrArgs['bundlableCallFeatures'])
        ) {
            $arrArgs['bolCreateScheduledPayment'] = false;
        }

        // Billing Get Well 2010 stuff.
        //
        // uxtEnableDate has been renamed to uxtContractStart which is what it should actually
        // be in this context.
        //
        // bolCreateScheduledPayment helps prevent scheduled payments being generated in the
        // event that the contract that is about to be created is actually covering a pro-rata
        // period - scheduled payments to cover the pro-rata period are created elsewhere,
        // independent of the contract-creation process.
        //
        // If bolCreateScheduledPayment isn't specified as an argument to the enable() method,
        // then propagate it through to createContract() as NULL. The reason for this is that
        // createContract() has its own rules on whether it should create scheduled payments or not
        // and thus it would probably be a bad idea for client code to second-guess what createContract()
        // wants to do by explictly defaulting to either TRUE or FALSE. Using NULL allows for
        // createContract() to do its right thing.

        $bolCreateScheduledPayment = isset($arrArgs['bolCreateScheduledPayment'])
            ? $arrArgs['bolCreateScheduledPayment'] : null;

        $strStatusHandle = $this->callGetStatusHandleFromID($this->getStatusID());
        switch ($strStatusHandle) {
            case 'QUEUED_REACTIVATE':
            case 'DEACTIVE':
                return $this->reEnable();
                break;
            case 'ACTIVE':
                //Nothing to do here
                return true;
                break;
            case 'QUEUED_DEACTIVATE':
            case 'DESTROYED':
            case 'QUEUED_DESTROY':
                //nothing to do here
                $this->setError(
                    __FILE__,
                    __LINE__,
                    'Error enabling product component instance [' . $this->m_intProductComponentInstanceID .
                    '] because it is at status [' . $strStatusHandle . ']'
                );

                return false;
                break;
            case 'UNCONFIGURED':
            case 'QUEUED_ACTIVATE':
                if ($this->getNextInvoiceDate() == '') {
                    $uxtNextInvoiceDate = CProductComponent::generateProRataNextInvoiceDate(
                        $this->getServiceID(),
                        $this->getTariffID(),
                        time()
                    );

                    if (!$this->setNextInvoiceDate($uxtNextInvoiceDate)) {
                        return false;
                    }
                    //Continue to enable
                }

                break;
        } // switch($strStatusHandle)

        $this->prvSetStatus('QUEUED_ACTIVATE');

        // Create the contract on the Product Component Instance
        if (!$this->hasActiveContract()) {
            $arrArgs = array(
                'uxtContractStart'          => $uxtContractStart,
                'bolCreateScheduledPayment' => $bolCreateScheduledPayment
            );

            if (!empty($uxtEnableDate)) {
                $arrArgs['uxtEnableDate'] = $uxtEnableDate;
            }

            $bolSuccess = $this->createContract($arrArgs);

            if (!$bolSuccess) {
                return false;
            }
        } // if(! $this->hasActiveContract())

        // Execute any special functionality
        if (!$this->onEnable($arrArgs)) {
            return false;
        }

        // Now set the component to active
        $this->prvSetStatus('ACTIVE');

        // Sanity check
        $strStatusHandle = $this->callGetStatusHandleFromID($this->getStatusID());
        if ($strStatusHandle != 'ACTIVE') {
            $this->setError(
                __FILE__,
                __LINE__,
                'Error enabling product component instance [' . $this->m_intProductComponentInstanceID .
                '] it is still at status [' . $strStatusHandle . ']'
            );

            return false;
        }

        // It worked. Log the event.
        $this->logStatusChange('ACTIVE');

        $this->refreshInstance($this->getProductComponentInstanceID());

        return true;
    } // function enable()

    /**
     * Component configurator function. Disables the Product Component component
     *
     * <AUTHOR> Jones" <<EMAIL>>
     * @access   public
     * @return   boolean
     */
    public function disable()
    {
        $this->prvSetStatus('QUEUED_DEACTIVATE');

        // Execute any special functionality
        if (!$this->onDisable()) {
            return false;
        }

        // Now set the component to deactive
        $this->prvSetStatus('DEACTIVE');

        // Sanity check
        $strStatusHandle = $this->callGetStatusHandleFromID($this->getStatusID());
        if ($strStatusHandle != 'DEACTIVE') {
            return false;
        }

        // It worked. Log the event.
        $this->logStatusChange('DEACTIVE');

        $this->refreshInstance($this->getProductComponentInstanceID());

        return true;
    } // function disable()

    /**
     * Component configurator function. Destroys the Product Component component
     *
     * @param array $arrArgs Arguments
     *
     * <AUTHOR> Jones" <<EMAIL>>
     * @access   public
     * @return   boolean
     */
    public function destroy($arrArgs = array())
    {
        $bolFullCancellation = isset($arrArgs['bolFullCancellation']) ? $arrArgs['bolFullCancellation'] : true;
        $uxtDestructionDate = isset($arrArgs['uxtDestructionDate']) ? $arrArgs['uxtDestructionDate'] : '';

        $strStatusHandle = $this->callGetStatusHandleFromID($this->getStatusID());

        if ($strStatusHandle == 'DESTROYED') {
            return true;
        }

        $this->prvSetStatus('QUEUED_DESTROY');

        $destructionParams = array('bolScheduleCharges' => $bolFullCancellation);
        if (isset($arrArgs['bolPayCallCharges'])) {
            $destructionParams['bolPayCallCharges'] = $arrArgs['bolPayCallCharges'];
        }
        // Execute any special functionality
        if (!$this->onDestroy($destructionParams)) {
            return false;
        }

        // Cancel the contract for this PC.
        // LCST-404. LRS contract start dates will typically be the same as the billing date, in which case they
        // shouldn't be cancelled - otherwise they won't be available to be transferred
        if ($this->getProductComponentID() != PRODUCT_COMPONENT_WLR_LINE_RENTAL_SAVER) {
            $this->cancelContract(array('uxtCancelled' => $uxtDestructionDate));
        } else {
            $strCancelDate = $uxtDestructionDate != '' ? date("Y-m-d", $uxtDestructionDate) : date("Y-m-d");
            if ($this->getContractStart() < strtotime($strCancelDate)) {
                $this->cancelContract(array('uxtCancelled' => $uxtDestructionDate));
            }
        }

        // Now set the component to active
        $this->prvSetStatus('DESTROYED', $uxtDestructionDate);

        // Sanity check
        $strStatusHandle = $this->callGetStatusHandleFromID($this->getStatusID());
        if ($strStatusHandle != 'DESTROYED') {
            return false;
        }

        // It worked. Log the event.
        $this->logStatusChange('DESTROYED');

        $this->refreshInstance($this->getProductComponentInstanceID());

        return true;
    } // function destroy()


    //
    // Private Methods
    //

    /**
     * WLR Product Component enable functionality
     *
     * <AUTHOR> Jones" <<EMAIL>>
     * @access   private (debateable! protected yes, private no)
     *
     * @param    array $arrArgs Arbitrary chunk of data, usually flags.
     *
     * @return   boolean
     */
    public function onEnable($arrArgs = array())
    {
        // Over-ride this functionality in the sub-class if there is any special
        // functionality that needs to be performed when the sub-component is enabled.

        return true;
    }

    /**
     * WLR Product Component disable functionality
     *
     * <AUTHOR> Jones" <<EMAIL>>
     * @access   private
     * @return   boolean
     */
    public function onDisable()
    {
        // Over-ride this functionality in the sub-class if there is any special
        // functionality that needs to be performed when the sub-component is disabled.

        return true;
    }

    /**
     * WLR Product Component destroy functionality
     *
     * <AUTHOR> Jones" <<EMAIL>>
     * @access   private
     * @return   boolean
     */
    public function onDestroy()
    {
        // Over-ride this functionality in the sub-class if there is any special
        // functionality that needs to be performed when the sub-component is destroyed.

        return true;
    }

    /**
     * WLR Product Component status change logger.
     *
     * @param string $strStatus Status
     *
     * <AUTHOR> Jones" <<EMAIL>>
     * @access   private
     * @return   boolean
     */
    public function logStatusChange($strStatus)
    {
        //
        // Over-ride this functionality in the sub-class!!!
        //

        return false;
    }
} // class CWlrCallerDisplay extends CProductComponent
