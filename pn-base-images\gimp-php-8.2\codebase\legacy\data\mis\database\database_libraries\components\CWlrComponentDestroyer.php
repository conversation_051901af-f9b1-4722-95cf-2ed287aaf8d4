<?php

/**
 * Contains methods related to auto destroy feature of WLR component.
 *
 * PHP version 5.0
 *
 * @category Legacycodebase
 * @package  Legacycodebase
 * <AUTHOR> <<EMAIL>>
 */
require_once '/local/data/mis/database/database_libraries/components/CComponent.inc';
use \Plusnet\Feature\FeatureToggleManager;
use \Plusnet\Feature\FeatureSwitchFilter;

/**
 * Contains methods related to auto destroy feature of WLR component.
 *
 * @category Legacycodebase
 * @package  Legacycodebase
 * <AUTHOR> <<EMAIL>>
 */
class CWlrComponentDestroyer
{

    /**
     * New billing engine feature toggle
     *
     * @var isNewBillingToggleOn boolean
     */
    private $newBillingToggleOn;

    /**
     * WLR component configurator function. Destroys the component.
     *
     * @param integer $intComponentID component id
     *
     * @return void
     */
    public function autoDestroy($intComponentID)
    {
        $bolNewBillingEngineOn = $this->getBillingToggleStatus($this->getServiceId($intComponentID));
        if ($bolNewBillingEngineOn) {
            $this->destroyCComponent($intComponentID);
        }
    }

    /**
     * Get service id from component id
     *
     * @param $intComponentID
     *
     * @return int
     */
    public function getServiceId($intComponentID)
    {
        $objComponent = new CComponent($intComponentID);
        $serviceId = $objComponent->getServiceID();

        return $serviceId;
    }

    /**
     * Get billing toggle status
     *
     * @param int $serviceId service id
     *
     * @return boolean value indicating if the toggle is on or off
     */
    public function getBillingToggleStatus($serviceId)
    {
        if (!isset($this->newBillingToggleOn)) {
            $this->newBillingToggleOn = $this->featureIsEnabled($serviceId);
        }

        return $this->newBillingToggleOn;
    }

    /**
     * Billing feature is enabled for service id
     *
     * @param int $serviceId service id
     *
     * @return bool
     */
    private function featureIsEnabled($serviceId)
    {
        $featureSwitchFilter = FeatureSwitchFilter::createIdsFilter(null, null, $serviceId);

        if (FeatureToggleManager::isOn('SEND_UPDATES_TO_BILLING_API', $featureSwitchFilter)) {
            error_log(__FILE__ ." :EventService SEND_UPDATES_TO_BILLING_API FeatureToggle for service Id:".
                $serviceId . " is enabled");
            return true;
        }

        error_log(__FILE__ . " :EventService SEND_UPDATES_TO_BILLING_API FeatureToggle for service Id:" .
            $serviceId . " is disabled");
        return false;
    }

    /**
     * Loads the current component and destroys it
     *
     * @param ComponentId $intComponentID Component id of the current component
     *
     * @return null
     */
    public function destroyCComponent($intComponentID)
    {
        $wlrComponent = CComponent::createInstance($intComponentID);
        $wlrComponent->destroy();
    }
}
