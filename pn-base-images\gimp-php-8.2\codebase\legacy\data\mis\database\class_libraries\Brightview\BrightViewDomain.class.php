<?php

include_once '/local/data/mis/database/class_libraries/Brightview/BVDB.class.php';
include_once '/local/data/mis/database/class_libraries/Brightview/BrightViewPasswd.class.php';

/**
 * class BrightViewDomain
 * 
 * Class used to interact with BV domain system - cashew_new database in soulstone.
 * Class has got no public constructor. 
 * To obtain an instance of BrightViewDomain class use one of the factory methods: 
 *  1) BrightViewDomain::fromMailId($intMailId) - $intMailId - a valid id from new_auth.passwd table
 *  2) BrightViewDomain::fromName($strDomain)   - $strDomain - domain name
 *  3) BrightViewDomain::fromUserNameAndRealm   - returns a domain for given username and realm (user can have only one domain)
 *  
 * When you got the domain object you can perfom a set of actions on it like:
 *   1) cancell
 *   2) transfer
 *   3) .... 
 * 
 * There is one static method available - register() that accept an array with domain detials in it. 
 * Required data is declared in self::$arrReqForRegister
 * 
 * There are examples of usage at the end of the file
 *
 */


class BrightViewDomain
{
	const CO_UK  = 'co.uk';
	const ORG_UK = 'org.uk';
	const ME_UK  = 'me.uk';
	
	//nominet column
	const ACTION_REGISTER = 'Register';
	const ACTION_CANCEL = 'Cancel';
	const ACTION_DETAG = 'Detag';
	const ACTION_RETAG = 'Retag';
	const ACTION_TRANSFER = 'Transfer';
	const ACTION_ADDRESS = 'Address';
	const ACTION_OPTOUT = 'Optout';
	const ACTION_DNS = 'DNS';
	
	//column status
	//enum starts from 1 - STATUS_REGISTERED = 1
	
	const STATUS_REGISTERED = 'Registered'; //1
	const STATUS_DETAGGED   = 'Detagged'; //2
	const STATUS_REFUSED    = 'Refused'; //3
	const STATUS_TRANSFERRED_OUT = 'Transferred Out'; //4
	const STATUS_NOT_USED = 'Not Used'; //5
	const STATUS_CANCELLED = 'Cancelled'; //6
	const STATUS_HOSTED = 'Hosted'; //7
	const STATUS_SUSPEND = 'Suspend'; //8
	const STATUS_PENDING_TRANSFER_IN = 'Pending Transfer In'; //9
	const STATUS_LOST = 'Lost'; //10
	
	
	protected $intMailId;
	protected $strDomain;
	protected $strDomainSuffix;
	protected $strUserName;
	protected $strRealm;
	protected $strStatus;
	protected $strNominet; //in fact action to perform
	protected $strResponse;
	protected $dteChange;
	protected $dteRegistration;
	protected $dteRenew;
	protected $strTag;
	protected $strAddress1;
	protected $strAddress2;
	protected $strTown;
	protected $strCount;
	protected $strPostCode;
	protected $strOptout;
	protected $strParking;
	protected $strError;
	
	
	private static $arrReqForRegister = array(
										'domainname' => true,
										'username' => true, 
										'realm' => true, 
										'address1' => true, 
										'address2' => false, 
										'town' => true, 
										'county' => false, 
										'postcode' => true, 
										'tag' => false,
										'optout' => false
										);

	public function setMailId($intMailId) {$this->intMailId = $intMailId;}
	public function setDomain($strDomain) {$this->strDomain = $strDomain;}
	public function setDomainSuffix($strDomainSuffix) {$this->strDomainSuffix = $strDomainSuffix;}
	public function setUserName($strUserName) {$this->strUserName = $strUserName;}
	public function getUserName() {return $this->strUserName;}
	public function setRealm($strRealm) {$this->strRealm = $strRealm;}
	public function getRealm() {return $this->strRealm;}
	public function setStatus($strStatus) {$this->strStatus = $strStatus;}
	public function getStatus() {return $this->strStatus;}
	public function setNominet($strNominet) {$this->strNominet = $strNominet;}
	public function setResponse($strResponse) {$this->strResponse = $strResponse;}
	public function setChange($dteChange) {$this->dteChange = $dteChange;}
	public function setRegistration($dteRegistration) {$this->dteRegistration = $dteRegistration;}
	public function setRenew($dteRenew) {$this->dteRenew = $dteRenew;}
	public function setSetup($dteSetup) {$this->dteSetup = $dteSetup;}
	public function setTag($strTag) {$this->strTag = $strTag;}
	public function setAddress1($strAddress1) {$this->strAddress1 = $strAddress1;}
	public function setAddress2($strAddress2) {$this->strAddress2 = $strAddress2;}
	public function setTown($strTown) {$this->strTown = $strTown;}
	public function setCounty($strCounty) {$this->strCounty = $strCounty;}
	public function setOptout($strOptout) {$this->strOptout = $strOptout;}
	public function setPostCode($strPostCode) {$this->strPostCode = $strPostCode;}
	public function setParking($strParking) {$this->strParking = $strParking;}
	public function setError($strError) {$this->strError = $strError;}

	public function getDomain(){return $this->strDomain;}
	public function getMailId(){return $this->intMailId;}
	public function getTag() {return $this->strTag;}
	public function getDomainSuffix() {return $this->strDomainSuffix;}

	public function getAddress1() {return $this->strAddress1;}
	public function getAddress2(){return $this->strAddress2;}
	public function getTown() {return $this->strTown;}
	public function getCounty() {return $this->strCounty;}
	public function getOptout() {return $this->strOptout;}
	public function getPostCode() {return $this->strPostCode;}




	private function __construct(){}

	/**
	 * function fromMailId
	 * A factory method to obtain domain object for a give mailid 
	 *
	 * @param integer $intMailId
	 * @return BrightViewDomain object
	 */

	public static function fromMailId($intMailId)
	{
		$stmt = BVDB::db()->prepare('SELECT d.* FROM cashew.domain d INNER JOIN new_auth.passwd USING (mailid) WHERE d.mailid = ?');
		$stmt->execute(array($intMailId));
		$arr = $stmt->fetch(PDO::FETCH_ASSOC);

		if (0 == count($arr)) return null;

		return self::fromArray($arr);
	}

	/**
	 * function fromName
	 * A factory method to obtain domain object for a given domain name
	 *
	 * @param string $strDomain
	 * @return domain object
	 */
	public static function fromName($strDomain)
	{
		$stmt = BVDB::db()->prepare('SELECT d.* FROM cashew.domain d INNER JOIN new_auth.passwd USING (mailid) WHERE domain = ?');
		$stmt->execute(array($strDomain));
		$arr = $stmt->fetch(PDO::FETCH_ASSOC);

		if (0 == count($arr)) return null;

		return self::fromArray($arr);
	}

	/**
	 * function fromUserNameAndRealm
	 * A factory method used to obtain a domain object for a given username and realm
	 *
	 * @param string $strUserName
	 * @param string $strRealm
	 * @param boolean $bolIgnoreFilters
	 * @return domain object
	 */
	public static function fromUserNameAndRealm($strUserName, $strRealm, $bolIgnoreFilters = false)
	{
		if($bolIgnoreFilters)
		{
			$stmt = BVDB::db()->prepare('SELECT d.* FROM cashew.domain d INNER JOIN new_auth.passwd USING (mailid) WHERE d.username = ?');
			$stmt->execute(array($strUserName.'@'.$strRealm ));
		} else {
			$stmt = BVDB::db()->prepare('SELECT d.* FROM cashew.domain d INNER JOIN new_auth.passwd USING (mailid) WHERE d.username = ? AND (d.nominet = ? OR d.status = ?)');
			$stmt->execute(array($strUserName.'@'.$strRealm, 'Register', 'Registered'));		

		}
		$arr = $stmt->fetch(PDO::FETCH_ASSOC);

		if (0 == count($arr)) return null;

		return self::fromArray($arr);
	}

	private static function fromArray($arr)
	{
		$obj = new self();
		$obj->setMailId($arr['mailid']);
		$obj->setDomain($arr['domain']);
		$obj->setDomainSuffix($arr['domsuffix']);
		$obj->setUserName($arr['username']);
		$obj->setRealm($arr['realm']);
		$obj->setStatus($arr['status']);
		$obj->setNominet($arr['nominet']);
		$obj->setResponse($arr['response']);
		$obj->setChange($arr['changedate']);
		$obj->setRegistration($arr['regdate']);
		$obj->setRenew($arr['renewdate']);
		$obj->setSetup($arr['setupdate']);
		$obj->setTag($arr['tag']);
		$obj->setAddress1($arr['address1']);
		$obj->setAddress2($arr['address2']);
		$obj->setTown($arr['town']);
		$obj->setCounty($arr['county']);
		$obj->setOptout($arr['optout']);
		$obj->setPostCode($arr['postcode']);
		$obj->setParking($arr['parking']);
		$obj->setError($arr['error']);
		
		return $obj;
	}

	private static function checkRegisterData(array $arrData)
	{
		foreach (self::$arrReqForRegister as $key => $val)
		{
			if (empty($arrData[$key]) && $val) throw new Exception("Field $key is required to register domain !");
		}
	}

	/**
	 * function register
	 * A method used to register a domain
	 * 
	 * @param array $arrData
	 * @return boolean
	 */
	public static function register(array $arrData)
	{
		self::checkRegisterData($arrData); //will throw an exception if data is incomplete
		
		$arrMatch = array();
		preg_match('/\.(co\.uk|me\.uk|org\.uk)$/',$arrData['domainname'],$arrMatch);

		if (empty($arrMatch[1])) throw new Exception('Incorrect domain suffix');

		$intMailId = BrightViewPasswd::getMailIdByUserNameAndRealm($arrData['username'], $arrData['realm']);

		if (empty($intMailId)) throw new Exception("Can not find user {$arrData['username']}@{$arrData['realm']}");
		
		//check tag
		if (empty($arrData['tag'])) $arrData['tag'] = 'IND';
		
		if (!empty($arrData['optout']) && 'Yes' != $arrData['optout'])
		{
			throw new Exception("Optout set to incorrect value: {$arrData['optout']}");
		}

		$str  = 'INSERT INTO cashew.domain ';
		$str .= '( mailid, domain, domsuffix, username, realm, nominet, setupdate, tag, address1, address2, town, county, postcode, optout)';
		$str .= 'VALUES';
		$str .= '(:mailid,:domain,:domsuffix,:username,:realm,:nominet, NOW()  ,:tag,:address1,:address2,:town,:county,:postcode,:optout)';

		$stmt = BVDB::db()->prepare($str);

		$stmt->bindValue(':mailid', $intMailId, PDO::PARAM_INT);
		$stmt->bindValue(':domain', $arrData['domainname'], PDO::PARAM_STR);
		$stmt->bindValue(':domsuffix', $arrMatch[1], PDO::PARAM_STR);
		$stmt->bindValue(':username', $arrData['username'].'@'.$arrData['realm'], PDO::PARAM_STR);
		$stmt->bindValue(':realm', $arrData['realm'], PDO::PARAM_STR);
		$stmt->bindValue(':nominet', self::ACTION_REGISTER, PDO::PARAM_STR);
		$stmt->bindValue(':tag', strtoupper($arrData['tag']), PDO::PARAM_STR);
		$stmt->bindValue(':address1', $arrData['address1'], PDO::PARAM_STR);
		$stmt->bindValue(':address2', $arrData['address2'], PDO::PARAM_STR);
		$stmt->bindValue(':town', $arrData['town'], PDO::PARAM_STR);
		$stmt->bindValue(':county', $arrData['county'], PDO::PARAM_STR);
		$stmt->bindValue(':postcode', strtoupper($arrData['postcode']), PDO::PARAM_STR);
		$stmt->bindValue(':optout', $arrData['optout'], PDO::PARAM_STR);
		

		return $stmt->execute();
	}

	/**
	 * Set nominet flag to be picked up and submited for registration
	 *
	 * @return bool
	 */
	public function submitRegistration()
	{
		$stmt = BVDB::db()->prepare('UPDATE cashew.domain SET nominet = ? WHERE domain = ? AND mailid = ?');
		return $stmt->execute(array(self::ACTION_REGISTER, $this->getDomain(), $this->getMailId()));
	}
	
	public function cancel()
	{
		$stmt = BVDB::db()->prepare('UPDATE cashew.domain SET nominet = ? WHERE domain = ? AND mailid = ?');
		return $stmt->execute(array(self::ACTION_CANCEL, $this->getDomain(), $this->getMailId()));
	}

	public function detag()
	{
		$stmt = BVDB::db()->prepare('UPDATE cashew.domain SET nominet = ? WHERE domain = ? AND mailid = ? AND status IN (?,?)');
		return $stmt->execute(array(self::ACTION_DETAG , $this->getDomain(), $this->getMailId(), self::STATUS_REGISTERED, self::STATUS_SUSPEND));
	}

	public function retag()
	{
		$stmt = BVDB::db()->prepare('UPDATE cashew.domain SET nominet = ? WHERE domain = ? AND mailid = ? AND status = ?');
		$stmt->execute(array(self::ACTION_RETAG, $this->getDomain(), $this->getMailId(), self::STATUS_DETAGGED));
	}

	public function transfer($strTag)
	{
		$stmt = BVDB::db()->prepare('UPDATE cashew.domain SET nominet = ?, tag = ? WHERE mailid = ? AND domain = ?');
		return $stmt->execute(array(self::ACTION_TRANSFER, $strTag, $this->getMailId(), $this->getDomain()));
	}

	public function address($strA1, $strA2, $strTown, $strCounty, $strPostcode)
	{
		$str  = ' UPDATE cashew.domain SET nominet = :action, address1 = :address1, address2 = :address2, town = :town, county = :county, postcode = :postcode ';
		$str .= ' WHERE domain = :domain AND mailid = :mailid AND status IN (:registered, :suspend)';
		$stmt = BVDB::db()->prepare($str);
		$stmt->bindValue(':action', self::ACTION_ADDRESS, PDO::PARAM_STR);
		$stmt->bindValue(':address1', $strA1, PDO::PARAM_STR);
		$stmt->bindValue(':address2', $strA2, PDO::PARAM_STR);
		$stmt->bindValue(':town', $strTown, PDO::PARAM_STR);
		$stmt->bindValue(':county', $strCounty, PDO::PARAM_STR);
		$stmt->bindValue(':postcode', strtoupper($strPostcode), PDO::PARAM_STR);
		$stmt->bindValue(':domain', $this->getDomain(), PDO::PARAM_STR);
		$stmt->bindValue(':mailid', $this->getMailId(), PDO::PARAM_INT);
		$stmt->bindValue(':registered', self::STATUS_REGISTERED, PDO::PARAM_STR);
		$stmt->bindValue(':suspend', self::STATUS_SUSPEND, PDO::PARAM_STR);
		
		return $stmt->execute();
	}

	public function registerUpdate($strA1, $strA2, $strTowregisterUpdaten, $strCounty, $strPostcode)
	{
		$str  = ' UPDATE cashew.domain SET nominet = :action, address1 = :address1, address2 = :address2, town = :town, county = :county, postcode = :postcode ';
		$str .= ' WHERE domain = :domain AND mailid = :mailid';
		$stmt = BVDB::db()->prepare($str);
		$stmt->bindValue(':action', self::ACTION_REGISTER, PDO::PARAM_STR);
		$stmt->bindValue(':address1', $strA1, PDO::PARAM_STR);
		$stmt->bindValue(':address2', $strA2, PDO::PARAM_STR);
		$stmt->bindValue(':town', $strTown, PDO::PARAM_STR);
		$stmt->bindValue(':county', $strCounty, PDO::PARAM_STR);
		$stmt->bindValue(':postcode', strtoupper($strPostcode), PDO::PARAM_STR);
		$stmt->bindValue(':domain', $this->getDomain(), PDO::PARAM_STR);
		$stmt->bindValue(':mailid', $this->getMailId(), PDO::PARAM_INT);
		
		return $stmt->execute();
	}

	public function optout()
	{
		$stmt = BVDB::db()->prepare('UPDATE cashew.domain SET nominet = ? WHERE domain = ? AND mailid = ? AND status = ?');
		$stmt->execute(array(self::ACTION_OPTOUT, $this->getDomain(), $this->getMailId(), self::STATUS_DETAGGED));
		if (0 == $stmt->rowCount())
		{
			throw new Exception('Optout failed. Domain does not exist or not in detagged state');
		}
		return true;
	}

	public function activate()
	{
		return $this->updateStatus(self::STATUS_REGISTERED);
	}

	public function suspend()
	{
		return $this->updateStatus(self::STATUS_SUSPEND);
	}

	public function lost()
	{
		return $this->updateStatus(self::STATUS_LOST);
	}

	private function updateStatus($strStatus)
	{
		$stmt = BVDB::db()->prepare('UPDATE cashew.domain SET status = ? WHERE domain = ? AND mailid = ?');
		return $stmt->execute(array($strStatus, $this->getDomain(), $this->getMailId()));
	}
}

//Examples
//$d = BrightViewDomain::fromName('teme-valley.co.uk');
//var_dump($d);
//$d = BrightViewDomain::fromUserNameAndRealm('ianchell','freenetname.co.uk');
//$d = BrightViewDomain::fromMailId(499099);
//$d->lost();
//$d->suspend();
//$d->activate();
//$d->optout(); //must be detagged first
//$d->registerUpdate('Ulica Szklana', 'Numer pobity', 'Warszawa', 'Mazowieckie', '00-950');
//$d->address('Ulica Szklana', 'Numer pobity', 'Warszawa', 'Mazowieckie', '00-950');
//$d->transfer('szmak');
//$d->detag();
//$d->cancel();
/*
BrightViewDomain::register(array(
								'domainname'=>'d.co.uk', 
								'username'=>'002',
								'realm' =>'freenetname.co.uk',
								'address1' =>'add1',
								'address2' =>'add2',
								'town' =>'Sheffield',
								'postcode' =>'s3der4'
								));
								*/
