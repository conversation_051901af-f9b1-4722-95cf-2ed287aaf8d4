<?php
	
	/**
	* Access library for Product Component Scheduled Payments 
	* 
	* 
	*
	* @package    Core
	* @subpackage Financial
	* @access     public
	* <AUTHOR> <<EMAIL>>
	* @version    $Id: CProductComponentScheduledPayment.inc,v 1.5 2007-12-12 15:51:04 swestcott Exp $
	* @filesource
	*/

	
	require_once("/local/data/mis/database/database_libraries/CoreObjects/Financial/CScheduledPayment.inc");
	require_once("/local/data/mis/database/database_libraries/components/CProductComponent.inc");


/**
* Scheduled Payment object 
* 
* Concrete implementation of CScheduledPayment
*
* @access     public
* <AUTHOR> <<EMAIL>>
*/
class CProductComponentScheduledPayment extends CScheduledPayment
{

	/**
	* ProductComponentInstance ID
	*
	* @var integer
	* @access private
	*/
	var $m_intProductComponentInstanceID = 0;
	

	private $strProductTypeHandle = '';

	////////////////
	// Constructor
	////////////////


	/**
	* Contructor for the CProductComponentScheduledPayment class
	* 
	* @param int The ProductComponentInstance ID the charge relates to
	* @return boolean success
	*/
	function CProductComponentScheduledPayment($intScheduledPaymentID)
	{
		$intScheduledPaymentID = intval($intScheduledPaymentID);
	
		if(! CScheduledPayment::isValidScheduledPaymentID($intScheduledPaymentID)) {
			$this->setError(__FILE__, __LINE__, "Cannot create a scheduled payment entry ".
			                "for non-existent Scheduled Payment ID '$intScheduledPaymentID'");

			return false;
		}
	
		$dbhConn = get_named_connection_with_db('financial');

		$strQuery = "SELECT cpc.intConfigProductComponentID as intConfigID,
		                    cpc.intProductComponentInstanceID
		               FROM financial.tblConfigProductComponent cpc
		              WHERE cpc.intScheduledPaymentID = '$intScheduledPaymentID'";

		$refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Construct CProductComponentScheduledPayment');
		$arrConfig = PrimitivesResultsAsArrayGet($refResult);

		if(count($arrConfig) < 1) {
			$this->setError(__FILE__, __LINE__, "Cannot find ProductComponentScheduledPayment for intScheduledPaymentID '$intScheduledPaymentID'");
			return false;
		}
		
		$arrConfig = $arrConfig[0];
	
		$this->m_intConfigID = $arrConfig['intConfigID'];
		$this->m_intProductComponentInstanceID = $arrConfig['intProductComponentInstanceID'];

		$objProductComponent = new CProductComponent($this->m_intProductComponentInstanceID);
		if(is_object($objProductComponent) && ($objProductComponent instanceof CProductComponent)) {

			$intComponentId = $objProductComponent->getComponentID();
			$objProduct = new CProduct($intComponentId);

			if(is_object($objProduct) && ($objProduct instanceof CProduct)) {

				$this->strProductTypeHandle = $objProduct->getProductTypeHandle();
				unset($objProduct);
			}

			unset($objProductComponent);
		}

		$this->m_intScheduledPaymentTypeID = CScheduledPayment::getTypeIDByHandle('PRODUCT_COMPONENT');

		//Call parent constructor
		return $this->CScheduledPayment($intScheduledPaymentID);

	}


	//////////////
	// Accessors
	//////////////


	/**
	* Set the Product Component Instance ID 
	*
	*
	* @access public
	* <AUTHOR>
	* @param  ingeter Product Component Instance ID
	*/
	function setProductComponentInstanceID($intProductComponentInstanceID)
	{
		$this->m_intProductComponentInstanceID = $intProductComponentInstanceID;
	}


	/**
	* Get the Product Component Instance ID 
	*
	* @access public
	* <AUTHOR>
	* @return
	*/
	function getProductComponentInstanceID()
	{
		return $this->m_intProductComponentInstanceID;
	}


	/////////////////////
	// Static Methods
	////////////////////



	////////////////////////
	// Public Methods
	////////////////////////


	/**
	* get tye component id by schedule payment id
	*
	* @access public
	* <AUTHOR> Spencer
	* @param integer none
	* @return integer intComponentID
	*/
	function getComponentId()
	{
		$dbConnection = get_named_connection_with_db('financial_reporting');

		$strQuery = "SELECT pci.intComponentID FROM financial.tblConfigProductComponent cpc
		             INNER JOIN userdata.tblProductComponentInstance pci
		               USING(intProductComponentInstanceID)
		             WHERE cpc.intScheduledPaymentID = {$this->m_intScheduledPaymentID}";

		$resResult = PrimitivesQueryOrExit($strQuery, $dbConnection, 'CScheduledPayment::getComponentId', FALSE);

		if(!$resResult) return FALSE;


		return PrimitivesResultGet($resResult, 'intComponentID');
	}
	


	////////////////////////
	// Private Methods
	////////////////////////

	
	
	/**
	* Cancel all outstanding Scheduled payments
	*
	*
	* @access private
	* <AUTHOR>
	* @return
	*/
	function cancelAllOutstandingScheduledPayments($intProductComponentInstanceID)
	{
		if($intProductComponentInstanceID > 0)
		{
			$uxtCancelled = time();

			$dbhConn = get_named_connection_with_db('financial');

			$strQuery = "SELECT sp.intScheduledPaymentID AS intScheduledPaymentID
			               FROM financial.tblScheduledPayment sp
			              WHERE sp.dtmInvoiced IS NULL 
			                AND sp.dtmCancelled IS NULL 
			                AND sp.intSalesInvoiceID IS NULL 
			                AND sp.dteDue > NOW() 
			                AND sp.intProductComponentInstanceID = $intProductComponentInstanceID ";

			$refResult     = PrimitivesQueryOrExit($strQuery, $dbhConn, 'cancelAllOutstandingScheduledPayments()');
			$arrScheduledPaymentsToCancel  = PrimitivesResultGetAsList($refResult);

			foreach($arrScheduledPaymentsToCancel as $intScheduledPaymentID)
			{
				$this->cancelScheduledPayment($intScheduledPaymentID);
			}
			
			return true;
		}
		
		return false;
	}

	/**
	* Renew the product component instance associated with this scheduled payment
	*
	* <AUTHOR>
	* @return bool
	*/
	function renew()
	{
		$objProductComponentInstance = CProductComponent::createInstance($this->getProductComponentInstanceID());

		if (isset($objProductComponentInstance) && is_object($objProductComponentInstance))
		{
			return $objProductComponentInstance->renew();
		}

		return false;

	} // End of function renew

	public function getProductTypeHandle()
	{
		return (string) $this->strProductTypeHandle;
	}

}

?>
