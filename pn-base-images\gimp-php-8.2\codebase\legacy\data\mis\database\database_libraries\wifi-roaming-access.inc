<?php
/**
 * wifi-roaming-access.inc
 *
 * Access Library for Wifi Roaming
 *
 * @package    LegacyCodebase
 * @subPackage Database Libraries
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2012 Plusnet
 */		

/**
 * Wifi Get Wifi Component Types
 *
 * @return unknown
 */
function wifi_get_wifi_component_types()
{

    $objWifiRoaming = Lib_WifiRoaming::singleton();
    return $objWifiRoaming->wifi_get_wifi_component_types();
}

/**
 * Wifi Get Product Descriptions
 *
 * @return unknown
 */
function wifi_get_product_descriptions()
{

    $objWifiRoaming = Lib_WifiRoaming::singleton();
    return $objWifiRoaming->wifi_get_product_descriptions();
}

/**
 * Bol Wifi Account Has Active Wifi Component
 *
 * @param int $intServiceID Service ID
 *
 * @return unknown
 */
function bolWifiAccountHasActiveWifiComponent($intServiceID)
{

    $objWifiRoaming = Lib_WifiRoaming::singleton();
    return $objWifiRoaming->bolWifiAccountHasActiveWifiComponent($intServiceID);
}

require_once '/local/data/mis/database/database_libraries/Util_LibrarySplitter.class.php';
require_once '/local/data/mis/database/database_libraries/wifiroaming/WifiRoaming.class.php';

