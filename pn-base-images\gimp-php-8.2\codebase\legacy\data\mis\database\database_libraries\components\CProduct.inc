<?php
/**
* Abstract product base class.
*
*
*
* @package    Core
* @access     public
* <AUTHOR> <<EMAIL>>
* @version    $Id: CProduct.inc,v 1.36.2.7 2009/07/09 10:11:37 bselby Exp $
* @filesource
*/

require_once '/local/data/mis/database/database_libraries/components/CComponent.inc';
require_once '/local/data/mis/database/database_libraries/components/CProductHelper.inc';
require_once '/local/data/mis/database/database_libraries/components/CProductComponent.inc';
require_once '/local/data/mis/database/database_libraries/CoreObjects/SystemEvents/CProductChange.inc';
require_once '/local/data/mis/database/database_libraries/CoreObjects/Financial/CScheduledPayment.inc';
require_once '/local/data/mis/database/database_libraries/CoreObjects/SystemEvents/CCancellation.inc';
require_once '/local/data/mis/database/database_libraries/CoreObjects/Financial/CProductComponentPaymentScheduler.inc';

/**
* Product
*
* Abstract product base class for special components which are used as complex products.
*
* @access     public
* <AUTHOR> <<EMAIL>>
*/
class CProduct extends CComponent
{

    const STATUS_DESTROYED = 'DESTROYED';
    //
    // Properties
    //

    /**
    * Product Type Handle (products.tblServiceComponentProductType.vchHandle)
    *
    * @var string
    * @access private
    */
    public $m_strProductTypeHandle = '';

    /**
    * Product Type Description (products.tblServiceComponentProductType.vchDescription)
    *
    * @var string
    * @access private
    */
    public $m_strProductTypeDescription = '';

    /**
    * Product Components
    *
    * Uses lazy instantiation as overhead is quite high.
    * Never access directly, as they may not have been instantiated.
    * use getProductComponent()
    *
    * @var array
    * @access private
    */
    public $m_arrProductComponents = null;

    /**
    * Product Components
    * array of schedule payments that needs to be marked as payed after successfull cancellation
    *
    * @var array
    * @access private
    */
    public $m_arrSchedulePaymentsUponCancellation = array();

    /**
    * Product Components
    * array of invoice items that would be raised against the invoice raised for cancellation charges
    *
    * @var array
    * @access private
    */
    public $m_arrInvoiceItems = array();

    /**
    * Product Components
    * array of Cancellation details
    *
    * @var array
    * @access private
    */
    public $m_arrCancellationDetails = array();

    /**
    * Product Components
    * Check for instant Cancellation
    *
    * @var boolean
    * @access private
    */
    public $m_bolInstantCancellation = TRUE;

    /**
    * Product Components
    * Array of schedule payments to add
    *
    * @var array
    * @access private
    */
    public $m_arrSchedulePaymentsToAdd = array();

    /**
    * Product name to cancel
    * This is only used to raise the cancellation ticket
    *
    * @var string
    * @access private
    */
    public $m_strProductNameToCancel = '';

    /**
     * Service Component Product Id
     *
     * @var int
     */
    protected $intServiceComponentProductId;

    /**
     * Service component product type ID.
     * Useful for when joining across onto tables that link to
     * tblServiceComponentProductType.
     *
     * @var integer
     **/

    protected $intServiceComponentProductTypeId;

    //
    // Constructor
    //

    /**
    * Contructor
    *
    * @protected
    * @param int The component ID
    * @return boolean success
    */
    public function CProduct($intComponentID)
    {
        return $this->refreshInstance($intComponentID);
    }

    //
    // Accessor Methods
    //

    /**
    * Returns the Product Type Handle
    *
    * @access public
    * @return string Product Type Handle
    */
    public function getProductTypeHandle()
    {
        return $this->m_strProductTypeHandle;
    }

    /**
    * Returns the Product Type Description
    *
    * @access public
    * @return string Product Type Description
    */
    public function getProductTypeDescription()
    {
        return $this->m_strProductTypeDescription;
    }

    /**
     * Returns the service component product ID
     *
     * @access public
     * @return integer -- service component product ID
     **/

    public function getServiceComponentProductId()
    {
        return $this->intServiceComponentProductId;
    }

    /**
    * Set the member member variable m_arrSchedulePaymentsUponCancellation
    * This function is called from the cancellation tool to set the schedule payments that
    *
    * @access public
    * <AUTHOR>
    * @return
    */
    public function setSchedulePaymentUponCancellation($arrSchedulePayments)
    {
        $this->m_arrSchedulePaymentsUponCancellation = $arrSchedulePayments;
    }

    /**
    * Set the member member variable m_arrInvoiceItems
    * This function is called from the cancellation tool to set the line items of the cancellation invoice
    *
    *
    * @access public
    * <AUTHOR>
    * @return
    */
    public function setCancellationInvoiceItems($arrInvoiceItems)
    {
        $this->m_arrInvoiceItems = $arrInvoiceItems;
    }

    /**
    * set the member member variable m_arrinvoiceitems
    * this function is called from the cancellation tool to set the line items of the cancellation invoice
    *
    *
    * @access public
    * <AUTHOR>
    * @return
    */
    public function setCancellationDetails($arrCancellationDetails)
    {
        $this->m_arrCancellationDetails = $arrCancellationDetails;
    }

    /**
    * Set the value of boolean Instant cancellation varaiable
    *
    *
    * @access public
    * <AUTHOR>
    * @return
    */
    public function setInstantCancellation($bolInstantCancellation)
    {
        $this->m_bolInstantCancellation = $bolInstantCancellation;
    }

    /**
    * set the array for schedule payments to add
    *
    *
    * @access private
    * <AUTHOR>
    * @return
    */
    public function setSchedulePaymentsToAdd($arrSchedulepaymentsToAdd)
    {
        $this->m_arrschedulepaymentstoadd = $arrSchedulepaymentsToAdd;
    }

    /**
    * set the array for schedule payments to add
    *
    *
    * @access public
    * <AUTHOR>
    * @return
    */
    public function setProductNameToCancel($strProductNameToCancel)
    {
        $this->m_strProductNameToCancel = $strProductNameToCancel;
    }

    //
    // Factory Methods
    //

    /**
    * Gets a Component Product based on Service ID and Service Component ID
    *
    * <AUTHOR> Jones" <<EMAIL>>
    * @static
    * @param    integer Service ID
    * @param    integer Service Component ID
    * @return   Object  CComponent Object
    */
    public function getProductFromServiceComponentId($intServiceId, $intServiceComponentId)
    {
        $objComponent = CComponent::getComponentByServiceComponentId($intServiceId, $intServiceComponentId);
        $objComponent->refreshInstance();

        return $objComponent;
    }

    //
    // Static Methods
    //

    /**
     * Gets a Component Product based on Service ID and Product Type Handle
     * If there are multiple products, the newest one will be returned
     *
     * <AUTHOR> Jones" <<EMAIL>>
     * @static
     * @access   public
     *
     * @param integer $intServiceId         Service ID
     * @param string  $strProductTypeHandle Product Type Handle
     * @param string  $strStatus            (Optional) Status of the product component
     *
     * @return CComponent|boolean A CProduct component (or boolean false on fail)
     */
    public static function getProductByProductTypeHandle($intServiceId, $strProductTypeHandle, $strStatus = null) {
        return CProductHelper::getInstance()->getProductByProductTypeHandle(
            $intServiceId,
            $strProductTypeHandle,
            $strStatus
        );
    }

    /**
     * Gets a Product handle based on Service ID
     *
     * <AUTHOR> Chalam" <<EMAIL>>
     * @static
     * @param    integer Service ID
     *
     * @return   Object  CProduct component
     */
    public static function getProductTypeHandleFromServiceId($intServiceId)
    {
        return CProductHelper::getInstance()->getProductTypeHandleFromServiceId($intServiceId);
    }

    /**
    * Create a new product instance
    *
    * Factory method, returns correct product based on the service_component_id
    *
    * @access public
    * @static
    * <AUTHOR>
    * @param  integer ServiceComponentID
    * @return object The Product instance
    */
    public static function create($intServiceID, $intServiceComponentID, $intTariffID = 0, $uxtNextInvoiceDate = false, $strProductHandle = '', $uxtStartTime = '')
    {
        $bolFalse = false;  // Assign into a variable or references cause notices

        if ($intServiceID <= 0) {
            return $bolFalse;
        }

        if ($intServiceComponentID <= 0) {
            return $bolFalse;
        }

        $uxtStartTime = $uxtStartTime == '' ? false : $uxtStartTime;

        // Add the Service Component Product to the service account
        $intComponentID = userdata_component_add($intServiceID, $intServiceComponentID, '-1', '', 'unconfigured');

        if (isset($intComponentID) && $intComponentID > 0) {
            //Get a list of all the product component ids which make up this product
            $arrProductComponentIDs = CProduct::getDefaultProductComponentIDs($intServiceComponentID);

            foreach ($arrProductComponentIDs as $intProductComponentID) {
                if ($intProductComponentID == PRODUCT_COMPONENT_SUBSCRIPTION && $intTariffID > 0) {
                    if ($strProductHandle == 'WLR') {
                        $objNewProductComponent = CProductComponent::create($intServiceID, $intComponentID, $intProductComponentID, $intTariffID, false, $strProductHandle, $uxtStartTime);
                    } else {
                        $objNewProductComponent = CProductComponent::create($intServiceID, $intComponentID, $intProductComponentID, $intTariffID, $uxtNextInvoiceDate, $strProductHandle);
                    }
                } else {
                    $objNewProductComponent = CProductComponent::create($intServiceID, $intComponentID, $intProductComponentID, 0, false, $strProductHandle, $uxtStartTime);
                }
            } // end foreach

            $objProduct = CComponent::createInstance($intComponentID);

            if (isset($objProduct) && is_object($objProduct)) {
                return $objProduct;
            } // end if
        } // end if

        return $bolFalse;
    } // End of function create

    /**
    * Get an array of all the Product Component IDs which make up a given product by default (service component id)
    *
    * @access public
    * @static
    * <AUTHOR>
    * @param  integer The service component id of the product
    * @return array List of product component ids
    */
    public function getDefaultProductComponentHandles($intServiceComponentID)
    {
        $dbhConn = get_named_connection_with_db('product');

        $strQuery='SELECT pc.vchHandle as strProductComponentHandle ' .
                  '  FROM dbProductComponents.tblProductComponent pc ' .
                  ' INNER JOIN dbProductComponents.tblProductComponentConfig pcc ' .
                  '    ON pc.intProductComponentID = pcc.intProductComponentID' .
                  ' INNER JOIN tblServiceComponentProduct scp ' .
                  '    ON scp.intServiceComponentProductID = pcc.intServiceComponentProductID ' .
                  "   AND scp.intServiceComponentID = '$intServiceComponentID' " .
                  ' WHERE pcc.dtmStart <= NOW() ' .
                  '   AND (pcc.dtmEnd >= NOW() OR pcc.dtmEnd IS NULL) ' .
                  '   AND pcc.intDefaultQuantity > 0';

        $refResult = PrimitivesQueryOrExit($strQuery, $dbhConn,"Fetch default product component handles for service component id '$intServiceComponentID'");
        $arrProductComponentHandles = PrimitivesResultsAsListGet($refResult);

        return $arrProductComponentHandles;
    } // function getDefaultProductComponentHandles($intServiceComponentID)

    /**
    * Get an array of all the Product Component IDs which make up a given product by default (service component id)
    *
    * @access public
    * @static
    * <AUTHOR>
    * @param  integer The service component id of the product
    * @return array List of product component ids
    */
    public function getDefaultProductComponentIDs($intServiceComponentID)
    {
        $dbhConn = get_named_connection_with_db('product');

        $strQuery='SELECT pcc.intProductComponentID as intProductComponentID ' .
                  '  FROM dbProductComponents.tblProductComponentConfig pcc ' .
                  ' INNER JOIN tblServiceComponentProduct scp ' .
                  '    ON scp.intServiceComponentProductID = pcc.intServiceComponentProductID ' .
                  "   AND scp.intServiceComponentID = '$intServiceComponentID' " .
                  ' WHERE pcc.dtmStart <= NOW() ' .
                  '   AND (pcc.dtmEnd >= NOW() OR pcc.dtmEnd IS NULL) ' .
                  '   AND pcc.intDefaultQuantity > 0';

        $refResult = PrimitivesQueryOrExit($strQuery, $dbhConn,"Fetch default product component ids for service component id '$intServiceComponentID'");
        $arrProductComponentIDs = PrimitivesResultsAsListGet($refResult);

        return $arrProductComponentIDs;
    }

    /**
     * Get a simple array containing the Product Service Component types.  Data is cached on first lookup
     * NOTE: a given handle can map to multiple service components!
     * Returns an array of matches, indexed by intServiceComponentID
     *
     * @param str $strHandle the Product handle
     *
     * @return array
     */
    public static function getProductComponentTypes($strHandle)
    {
        // As there's ~900 records, we cache on a first-lookup basis
        static $dataCache = array();

        if (!isset($dataCache[$strHandle])) {
            $query = <<<EOQ
SELECT
    CAST(service_component_id as UNSIGNED)  AS intServiceComponentID,
    name                                    AS strName,
    description                             AS strDescription
FROM
    products.service_components                         AS sc
    INNER JOIN products.tblServiceComponentProduct      AS scp
        ON sc.service_component_id = scp.intServiceComponentID
    INNER JOIN products.tblServiceComponentProductType  AS scpt
        ON scp.intServiceComponentProductTypeID = scpt.intServiceComponentProductTypeID
WHERE
    scpt.vchHandle = '$strHandle'
EOQ;

            $conn = get_named_connection_with_db('product_reporting');
            $resResult = PrimitivesQueryOrExit($query, $conn);
            $dataCache[$strHandle] = PrimitivesResultsAsArrayGet($resResult, 'intServiceComponentID');
        }

        return $dataCache[$strHandle];
    }
    /**
    * Get a list of the Product Component types
    *
    * Get a simple array containing the Product Service Component
    * types.
    *
    * <code>
    * $arrList = CProduct::getAllProductComponentTypes();
    * </code>
    *
    * @access public
    * @static
    * <AUTHOR> Shivnith" <<EMAIL>>
    * @param  string    Product handle
    * @return array service component ID's
    * @throws none none
    */
    public static function getAllProductComponentTypes()
    {
        $arrComponentTypes = array();

        

            $dbhConn = get_named_connection_with_db('product');

            $strQuery ="SELECT CAST(service_component_id as UNSIGNED) as intServiceComponentID,
                               name as strName,
                               description as strDescription
                          FROM products.service_components sc
                    INNER JOIN products.tblServiceComponentProduct scp
                            ON sc.service_component_id = scp.intServiceComponentID
                    INNER JOIN products.tblServiceComponentProductType scpt
                            ON scp.intServiceComponentProductTypeID = scpt.intServiceComponentProductTypeID";

            $refResult = PrimitivesQueryOrExit($strQuery, $dbhConn);
            $arrComponentTypes = PrimitivesResultsAsArrayGet($refResult, 'intServiceComponentID');

        

        return $arrComponentTypes;

    } // function getProductComponentTypes($strHandle)

    /**
    * activate component using intComponentID
    *
    *
    * @access public
    * @static
    * <AUTHOR> Shivnith" <<EMAIL>>
    * @param integer Component ID
    * @return true
    */
    public static function activateComponentByComponentID($intComponentID)
    {        
        $dbhConn = get_named_connection_with_db('userdata');

        $strQuery="UPDATE userdata.components SET status = 'active' WHERE component_id = '$intComponentID' ";

        $refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, __METHOD__, FALSE);        

        return true;
    }
    
    /**
    * get component details using intComponentID
    *
    *
    * @access public
    * @static
    * <AUTHOR> Shivnith" <<EMAIL>>
    * @param integer Component ID
    * @return array component details
    */
    public static function getComponentsByComponentID($intComponentID)
    {        
        $dbhConn = get_named_connection_with_db('userdata');

        $strQuery="SELECT *
                     FROM userdata.components c
                    WHERE c.component_id = '$intComponentID' ";

        $refResult = PrimitivesQueryOrExit($strQuery, $dbhConn);
        $arrComponentDetails = PrimitivesResultsAsArrayGet($refResult);

        return $arrComponentDetails;
    }

    /**
    * Get a list of ServiceComponents as specified by the given handle, including the
    * tariff details for that Service Component
    *
    * <AUTHOR> Jones" <<EMAIL>>
    * @static
    * @param    string  Service Component Product Handle to retrieve (e.g. PLUSTALK, WLR)
    * @return   array   Indexed array of Service Component records
    */
    public static function getServiceComponentsWithTariff($strHandle, $arrArgs = array())
    {
        if ($strHandle == 'WLR') {
            $strContractLengthHandle = 'Monthly';
        } else {
        $strContractLengthHandle = isset($arrArgs['strContractLengthHandle']) ? $arrArgs['strContractLengthHandle'] : '';

        }

        $bolAvailableOnly        = isset($arrArgs['bolAvailableOnly'])        ? $arrArgs['bolAvailableOnly'] : false;
        $strProductComponent     = isset($arrArgs['strProductComponent'])     ? "'{$arrArgs['strProductComponent']}'" : "'SUBSCRIPTION','WlrLineRent'";
        if('madasafish' == $arrArgs['strIsp']) {
            $strCondition = " = 'madasafish'";
        } elseif ('greenbee' == $arrArgs['strIsp']) {
            $strCondition = " = 'greenbee'";
        } else {
            $strCondition = " != 'madasafish' AND sc.isp != 'greenbee'";
        }

        $intServiceDefinitionId = isset($arrArgs['intServiceDefinitionId']) ? $arrArgs['intServiceDefinitionId'] : null;
        $strSdiQuery = '';
        if (!empty($intServiceDefinitionId) && is_numeric($intServiceDefinitionId)) {

            $strSdiQuery = "
                            INNER JOIN products.service_component_config scc
                            ON scc.service_component_id = sc.service_component_id
                            AND scc.service_definition_id = $intServiceDefinitionId
                           ";
        }

        $arrServiceComponentsWithTariff = array();

        $dbhRead = get_named_connection_with_db('unprivileged_reporting');

        $strServiceCompTariff = "SELECT sc.service_component_id AS intServiceComponentId, \n" .
                                "       sc.name AS strName, \n" .
                                "       sc.description AS strDescription, \n" .
                                "       SUM(t.intCostIncVatPence) as intCostIncVatPence, \n" .
                                "       SUM(t.intCostIncVatPence/100) as floPrice \n" .
                                "  FROM products.service_components sc \n" .
                                " INNER JOIN products.tblServiceComponentProduct scp \n" .
                                "    ON sc.service_component_id = scp.intServiceComponentID \n" .
                                " INNER JOIN products.tblServiceComponentProductType scpt \n" .
                                "    ON scp.intServiceComponentProductTypeID = scpt.intServiceComponentProductTypeID \n" .
                                " INNER JOIN dbProductComponents.tblProductComponentConfig pcc \n" .
                                "    ON scp.intServiceComponentProductID = pcc.intServiceComponentProductID \n" .
                                " INNER JOIN dbProductComponents.tblProductComponent pc \n" .
                                "    ON pcc.intProductComponentID = pc.intProductComponentID \n" .
                                "   AND pc.vchHandle IN ($strProductComponent) \n" .
                                $strSdiQuery .
                                " INNER JOIN dbProductComponents.tblTariff t \n" .
                                "    ON pcc.intProductComponentConfigID = t.intProductComponentConfigID \n" .
                                "  LEFT JOIN dbProductComponents.tblContractLength cl \n" .
                                "    ON cl.intContractLengthID = t.intContractLengthID \n" .
                                " WHERE scpt.vchHandle = '{$strHandle}' AND (t.dtmEnd IS NULL OR t.dtmEnd > NOW()) \n".
                                "   AND sc.isp  $strCondition\n";

        if ($bolAvailableOnly === true) {
            $strServiceCompTariff .= "    AND sc.available = 'Yes'";

        }

        if ($strContractLengthHandle <> '') {
            $strServiceCompTariff .= '    AND cl.vchHandle = "'.$strContractLengthHandle.'"';
        }

        // Order by clause
        $strServiceCompTariff .= ' GROUP BY sc.service_component_id, sc.name, sc.description  ' .
                                 ' ORDER BY t.intCostIncVatPence ASC ';

        if (false === ($resServiceComp = PrimitivesQueryOrExit($strServiceCompTariff, $dbhRead, 'CProduct::getServiceComponentsWithTariff{ServiceCompTariff}', true))) {
            // If we disable exit_on_failure then we should handle it gracefully here
            return false;
        }

        if (PrimitivesNumRowsGet($resServiceComp) == 0) {
            // Sub-Component not found
            return false;
        }

        $arrServiceComponentsWithTariff = PrimitivesResultsAsArrayGet($resServiceComp);

        return $arrServiceComponentsWithTariff;
    } // function getServiceComponentsWithTariff($strHandle)

    /**
    * Check that the supplied ServiceComponentID is of the type specified by the handle
    *
    * <AUTHOR> Jones" <<EMAIL>>
    * @static
    * @access   public
    * @param    integer Service Component ID
    * @param    string  Service Product Component Type Handle
    * @return   boolean True if Service Component is of Type Service Product Component
    */
    public function isValidProductComponentType($intServiceComponentId, $strServiceCompTypeHandle)
    {
        if (false !== ($arrProductComponentTypes = CProduct::getProductComponentTypes($strServiceCompTypeHandle))) {
            foreach ($arrProductComponentTypes as $arrProductComponentType) {
                if ($intServiceComponentId == $arrProductComponentType['intServiceComponentID']) {;

                    return true;
                }
            }

            // Nothing match above. We didn't find it!
            return false;
        } else {
            // Can't find the Service Component Product from the Service Component ID... invalid ID?
            return false;
        }

        // Shouldn't end up here
        return false;
    }

    /**
    * isSignupOrUpgrade
    *
    * <AUTHOR> Jones" <<EMAIL>>
    * @static
    * @access   public
    * @param    integer Service ID
    * @return   string  String handle [SIGNUP|UPGRADE]
    */
    public function isSignupOrUpgrade($intServiceID, $strProductHandle)
    {
        $intComponentID = CProduct::getComponentIDByServiceID($intServiceID, $strProductHandle);

        if ($intComponentID > 0) {
            $strSignupOrUpgrade = 'UPGRADE';
        } else {
            $strSignupOrUpgrade = 'SIGNUP';
        }

        return $strSignupOrUpgrade;
    }//isSignupOrUpgrade

    /**
    * Get an array of all the Product Component IDs which are allowed on a given product (service component id)
    *
    *
    * @access public
    * @static
    * <AUTHOR>
    * @param  integer The service component id of the product
    * @return array List of product component ids
    */
    public function getAllowedProductComponentIDs($intServiceComponentID)
    {
        $dbhConn = get_named_connection_with_db('product');

        $strQuery='SELECT pcc.intProductComponentID as intProductComponentID ' .
                  '  FROM dbProductComponents.tblProductComponentConfig pcc ' .
                  ' INNER JOIN tblServiceComponentProduct scp ' .
                  '    ON scp.intServiceComponentProductID = pcc.intServiceComponentProductID ' .
                  "   AND scp.intServiceComponentID = '$intServiceComponentID' " .
                  ' WHERE pcc.dtmStart <= NOW() ' .
                  '   AND (pcc.dtmEnd >= NOW() OR pcc.dtmEnd IS NULL) ' .
                  '   AND pcc.intMaxQuantity > 0';

        $refResult = PrimitivesQueryOrExit($strQuery, $dbhConn,"Fetch allowed product component ids for service component id '$intServiceComponentID'");
        $arrProductComponentIDs = PrimitivesResultsAsListGet($refResult);

        return $arrProductComponentIDs;
    } // function getAllowedProductComponentIDs($intServiceComponentID)

    /**
    * Get if the Customer given by intServiceID is existing product customer
    *
    *
    * @access public
    * @static
    * <AUTHOR>
    * @param integer The service id
    * @param string Product handle
    * @param string Component status
    * @return integer Service component ID
    */
    public function getServiceComponentIDByServiceID($intServiceID, $strProductTypeHandle, $strComponentStatus = 'active')
    {
        $dbhConn = get_named_connection_with_db('product');

        $strQuery="SELECT c.component_type_id
                     FROM userdata.components c
               INNER JOIN products.tblServiceComponentProduct scp
                       ON scp.intServiceComponentID = c.component_type_id
               INNER JOIN products.tblServiceComponentProductType scpt
                       ON scpt.intServiceComponentProductTypeID = scp.intServiceComponentProductTypeID
                      AND scpt.vchHandle = '$strProductTypeHandle'
                    WHERE c.status = '$strComponentStatus'
                      AND c.service_id = '$intServiceID' ";

        $refResult = PrimitivesQueryOrExit($strQuery, $dbhConn,"getCustomerProductComponentID('$intServiceID', '$strProductTypeHandle')");
        $CustomerProductComponentIDs = PrimitivesResultsAsListGet($refResult);

        return $CustomerProductComponentIDs[0];
    } // function getServiceComponentIDByServiceID($intServiceID, $strProductTypeHandle, $strComponentStatus = 'active')

    /**
    * get unix timestamp for the same day in intMonthsNumber in the future
    *
    *
    * @access public
    * @static
    * <AUTHOR>
    * @param  integer Numer of promotion months
    * @param  timestamp Date for which the delay should be calculated (optional)
    * @return timestamp Result of calculation
    */
    public function getNextMonthsDate($intMonthsNumber, $uxtDate = 0)
    {
        if (!(isset($uxtDate) && ($uxtDate > 0))) {
            $uxtDate = time();
        }

        if ($intMonthsNumber < 1) {
            $intMonthsNumber = 0;
        }

        if ($intMonthsNumber > 0) {
            $dbhConn = get_named_connection_with_db('product');

            $strQuery='SELECT UNIX_TIMESTAMP(FROM_UNIXTIME('.$uxtDate.') + INTERVAL '.$intMonthsNumber.' MONTH) AS uxtDate' ;

            $refResult = PrimitivesQueryOrExit($strQuery, $dbhConn,"getNextMonthDate('$intMonthsNumber', '$uxtDate')");
            $arrDate = PrimitivesResultsAsArrayGet($refResult);

            $uxtNextMonthsDate = $arrDate[0]['uxtDate'];
        } // if($intMonthsNumber > 0)
        else {
            $uxtNextMonthsDate = $uxtDate;
        }

        return $uxtNextMonthsDate;
    } // function getNextMonthsDate($intMonthsNumber, $uxtDate = 0)

    /**
    * Get next invoice date in unix timestamp
    *
    * <AUTHOR> don't know who wrote this, but I moved it here. (Rupert)
    * @static
    * @access   public
    * @param    int    $intServiceID                  - the service ID
    * @param    string $strServiceCompProdTypeHandle  - the Service Component Product Type Handle
    * @return   date
    */
    public function getNextInvoiceDate($intServiceID, $strServiceCompProdTypeHandle)
    {
        $intProductComponentInstanceID = CProductComponent::getActiveProductComponentInstance($intServiceID, 'SUBSCRIPTION', $strServiceCompProdTypeHandle);

        if (isset($intProductComponentInstanceID) && $intProductComponentInstanceID > 0) {
            $objProductComponentInstance = CProductComponent::createInstance($intProductComponentInstanceID);

            if (isset($objProductComponentInstance) && is_object($objProductComponentInstance)) {
                $uxtNextInvoice = $objProductComponentInstance->getNextInvoiceDate();

                if (isset($uxtNextInvoice) && $uxtNextInvoice > 0) {
                    return $uxtNextInvoice;
                }
            } // end if
        } // end if

        return false;
    } // End of function getNextBillingDate

    /**
    * Get next billing period
    *
    * <AUTHOR> don't know who wrote this, but I moved it here. (Rupert)
    * @static
    * @access   public
    * @param    int $intServiceID - the service ID
    * @return   date
    */
    public function getNextBillingDate($intServiceID, $strServiceCompProdTypeHandle = 'PLUSTALK')
    {
        $intProductComponentInstanceID = CProductComponent::getActiveProductComponentInstance($intServiceID, 'SUBSCRIPTION', $strServiceCompProdTypeHandle);

        if (isset($intProductComponentInstanceID) && $intProductComponentInstanceID > 0) {
            $objProductComponentInstance = CProductComponent::createInstance($intProductComponentInstanceID);

            if (isset($objProductComponentInstance) && is_object($objProductComponentInstance)) {
                $uxtNextInvoice = $objProductComponentInstance->getNextInvoiceDate();

                if (isset($uxtNextInvoice) && $uxtNextInvoice > 0) {
                    return date('d/m/y', $uxtNextInvoice);
                }
            } // end if
        } // end if

        return false;
    } // End of function getNextBillingDate

    /**
    * Get current billing period
    *
    * <AUTHOR> Jones" <<EMAIL>>
    * @static
    * @access   public
    * @param    integer     Service ID (userdata.services.service_id)
    * @return   array       'uxtStartDate' - Start of billing period
    *                       'uxtEndDate'   - End of billing period
    */
    public function getCurrentBillingPeriod($intServiceID, $strServiceProdCompHandle = 'PLUSTALK')
    {
        $intProductComponentInstanceID = CProductComponent::getActiveProductComponentInstance($intServiceID, 'SUBSCRIPTION', $strServiceProdCompHandle);

        if (isset($intProductComponentInstanceID) && $intProductComponentInstanceID > 0) {
            $strCurrentPeriod = "SELECT UNIX_TIMESTAMP(sp.dteStartPeriodCovered) AS uxtStartDate, \n" .
                                "       UNIX_TIMESTAMP(sp.dteEndPeriodCovered)   AS uxtEndDate \n" .
                                "  FROM financial.tblScheduledPayment sp \n" .
                                " INNER JOIN financial.tblConfigProductComponent cpc \n" .
                                "    ON sp.intScheduledPaymentID = cpc.intScheduledPaymentID \n" .
                                " WHERE cpc.intProductComponentInstanceID = '$intProductComponentInstanceID' \n" .
                                "   AND sp.dteStartPeriodCovered <= NOW() \n".
                                "   AND sp.dteEndPeriodCovered   >= NOW()";

            $dbhConnection = get_named_connection_with_db('financial_reporting');
            $resCurrentPeriod = PrimitivesQueryOrExit($strCurrentPeriod, $dbhConnection);
            $arrPeriod = PrimitivesResultGet($resCurrentPeriod);
            if (!(isset($arrPeriod) && sizeof($arrPeriod) > 0)) {
                $dbhConnection = get_named_connection_with_db('userdata');

                $strQuery = 'SELECT UNIX_TIMESTAMP(ppl.dtmCreationDate)      AS uxtStartDate, ' .
                            '       UNIX_TIMESTAMP(sp.dteStartPeriodCovered) AS uxtEndDate ' .
                            '  FROM financial.tblScheduledPayment sp ' .
                            ' INNER JOIN financial.tblConfigProductComponent cpc ' .
                            '    ON sp.intScheduledPaymentID = cpc.intScheduledPaymentID ' .
                            ' INNER JOIN tblProductComponentInstance pci ' .
                            '    ON cpc.intProductComponentInstanceID = pci.intProductComponentInstanceID ' .
                            "   AND pci.intProductComponentInstanceID = '$intProductComponentInstanceID' " .
                            ' INNER JOIN tblProductPromotionLog ppl ' .
                            '    ON pci.intComponentID = ppl.intComponentID ' .
                            '   AND ppl.dtmCreationDate <= NOW() ' .
                            ' WHERE sp.dteStartPeriodCovered > NOW() ' .
                            ' ORDER BY ppl.dtmCreationDate, sp.dteStartPeriodCovered ' .
                            ' LIMIT 1';

                $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);
                $arrPeriod = PrimitivesResultGet($resResult);
            } // if (!(isset($arrResult) && sizeof($arrResult) > 0))
            if (isset($arrPeriod) && sizeof($arrPeriod) > 0) {
                return array('uxtStartDate' => $arrPeriod['uxtStartDate'],
                             'uxtEndDate'   => $arrPeriod['uxtEndDate']);
            }
        } // if (isset($intProductComponentInstanceID) && $intProductComponentInstanceID > 0)

        return false;
    } // function getCurrentBillingPeriod($intServiceID)

    /**
    * get intServiceComponentID using intComponentID
    *
    *
    * @access public
    * @static
    * <AUTHOR>
    * @param integer Component ID
    * @param array Status
    * @return integer Service component ID
    */
    public static function getServiceComponentIDByComponentID($intComponentID, $arrStatus = array())
    {
        $dbhConn = get_named_connection_with_db('userdata');

        $strQuery= "SELECT c.component_type_id FROM userdata.components c WHERE c.component_id = '$intComponentID' ";

        if (count($arrStatus) > 0) {
            $strStatus = implode(',', $arrStatus);
            $strQuery .= "AND c.status in ($strStatus)";
        }

        $refResult = PrimitivesQueryOrExit($strQuery, $dbhConn,"getServiceComponentIDByComponentID('$intComponentID')");
        $ComponentIDs = PrimitivesResultsAsListGet($refResult);

        return $ComponentIDs[0];
    }

    /**
    * get component details using intComponentID
    *
    *
    * @access public
    * @static
    * <AUTHOR> Marek" <<EMAIL>>
    * @param integer Component ID
    * @return array component details
    */
    public function getComponentDetailsByComponentID($intComponentID)
    {
        $dbhConn = get_named_connection_with_db('userdata');

        $strQuery="SELECT *
                     FROM userdata.components c
                    WHERE c.component_id = '$intComponentID' ";

        $refResult = PrimitivesQueryOrExit($strQuery, $dbhConn);
        $arrComponentDetails = PrimitivesResultsAsArrayGet($refResult);

        return $arrComponentDetails;
    }

    /**
    * Get ServiceComponent Details by intServiceComponentID
    *
    *
    * @access public
    * @static
    * <AUTHOR>
    * @param integer Service component ID
    * @return array Product details
    */
    public function getServiceComponentDetails($intServiceComponentID)
    {
        $dbhConn = get_named_connection_with_db('product');

        $strQuery="SELECT sc.name AS strName,
                          sc.description AS strDescription
                     FROM products.service_components sc
                    WHERE sc.service_component_id = '$intServiceComponentID' ";

        $refResult = PrimitivesQueryOrExit($strQuery, $dbhConn,"getServiceComponentDetails('$intServiceComponentID')");
        $arrServiceComponentDetails = PrimitivesResultsAsArrayGet($refResult);

        return $arrServiceComponentDetails[0];
    } // function getServiceComponentDetails($intServiceComponentID)

    /**
    * check if requested change is an upgrade of contract length
    *
    *
    * @access public
    * @static
    * <AUTHOR>
    * @param string Existing contract handle
    * @param string New contract handle
    * @return boolean Is contract length upgrade or not
    */
    public function isProductContractLengthUpgrade($strExistingContractHandle, $strNewContractHandle)
    {
        $arrContractLengthPriority = array('ANNUAL'    => 12,
                                           'QUARTERLY' => 3,
                                           'MONTHLY'   => 1,
                                           'NONE'      => 0);

        $bolIsContractLengthUpgrade = FALSE;

        if ($arrContractLengthPriority["$strNewContractHandle"] >= $arrContractLengthPriority["$strExistingContractHandle"]) {
            $bolIsContractLengthUpgrade = TRUE;
        }

        return $bolIsContractLengthUpgrade;
    }

    /**
    * check if requested change is an upgrade of monthly cost
    *
    *
    * @access public
    * @static
    * <AUTHOR>
    * @param integer Existing product component ID
    * @param integer New product component ID
    * @return boolean Is product cost upgrade
    */
    public function isProductCostUpgrade($intExistingProductComponentConfigID, $intNewProductComponentConfigID)
    {
        //always compare monthly contract and monthly payment frequency of different product

        $dbhConn = get_named_connection_with_db('product');

        $strQuery = 'SELECT te.intCostIncVatPence AS intExistingCostIncVatInPence, ' .
                    '       tn.intCostIncVatPence AS intNewCostIncVatInPence ' .
                    '  FROM dbProductComponents.tblTariff te ' .
                    ' INNER JOIN dbProductComponents.tblTariff tn ' .
                    '    ON tn.intContractLengthID = te.intContractLengthID ' .
                    '   AND tn.intPaymentFrequencyID = te.intPaymentFrequencyID ' .
                    '   AND tn.intProductComponentConfigID = '.$intNewProductComponentConfigID.' ' .
                    ' INNER JOIN dbProductComponents.tblContractLength cl ' .
                    '    ON cl.intContractLengthID = te.intContractLengthID ' .
                    '   AND cl.vchHandle = "MONTHLY" ' .
                    ' INNER JOIN dbProductComponents.tblPaymentFrequency pf ' .
                    '    ON pf.intPaymentFrequencyID = te.intPaymentFrequencyID ' .
                    '   AND pf.vchHandle = "MONTHLY" ' .
                    ' WHERE te.intProductComponentConfigID = '.$intExistingProductComponentConfigID.' ';

        $refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'CProduct::isProductCostUpgrade');
        $arrTariffDetails = PrimitivesResultsAsArrayGet($refResult);

        $bolIsProductCostUpgrade = FALSE;

        if ($arrTariffDetails[0]['intNewCostIncVatInPence'] >= $arrTariffDetails[0]['intExistingCostIncVatInPence']) {
            $bolIsProductCostUpgrade = TRUE;
        }

        return $bolIsProductCostUpgrade;
    }

    /**
     * Get the details for a given tariff id.  Caches data
     *
     * @param int $intTariffID the Tariff ID
     *
     * @return array
     */
    public static function getTariffDetails($intTariffID)
    {
        static $dataCache = array();
        $intTariffID = (int) $intTariffID;

        if (!array_key_exists($intTariffID, $dataCache)) {
            require_once DATABASE_LIBRARY_ROOT.'CoreObjects/Financial/CFinancialHelper.inc';

            $query = <<<EOQ
SELECT
    t.intTariffID                   AS intTariffID,
    t.intCostIncVatPence            AS intCostIncVatInPence,
    t.intCostIncVatPence/100        AS floCostIncVatInPence,
    sc.name                         AS strProductName,
    cl.vchDisplayName               AS strContractName,
    cl.vchHandle                    AS strContractHandle,
    pf.vchDisplayName               AS strPaymentFrequencyName,
    pf.vchHandle                    AS strPaymentFrequencyHandle,
    cs.vchDisplayName               AS strCustomerSectorName,
    cs.vchHandle                    AS strCustomerSectorHandle,
    t.intProductComponentConfigID   AS intProductComponentConfigID,
    scp.intServiceComponentID       AS intServiceComponentID,
    t.intNextTariffID
FROM
    dbProductComponents.tblTariff                                   AS t
    INNER JOIN dbProductComponents.tblContractLength                AS cl
        ON cl.intContractLengthID = t.intContractLengthID
    INNER JOIN dbProductComponents.tblPaymentFrequency              AS pf
        ON pf.intPaymentFrequencyID = t.intPaymentFrequencyID
    INNER JOIN dbProductComponents.tblProductComponentConfig        AS pcc
        ON pcc.intProductComponentConfigID = t.intProductComponentConfigID
    INNER JOIN products.tblServiceComponentProduct                  AS scp
        ON pcc.intServiceComponentProductID = scp.intServiceComponentProductID
    INNER JOIN products.tblCustomerSector                           AS cs
        ON scp.intCustomerSectorID = cs.intCustomerSectorID
    INNER JOIN products.service_components                          AS sc
        ON sc.service_component_id = scp.intServiceComponentID
WHERE
    t.intTariffID = $intTariffID
EOQ;

            $conn      = get_named_connection_with_db('product_reporting');
            $resResult = PrimitivesQueryOrExit($query, $conn, "CProduct::getTariffDetails($intTariffID)");
            $tmpData   = PrimitivesResultsAsArrayGet($resResult);

            // NOTE; the old code had a foreach loop - which was pointless, as we're using a primary key
            // to retrieve the data - and since this method only ever returned the first row of the results, the
            // processing of the additional rows was completely superfluous...
            if (!empty($tmpData)) {
                $arrRow = $tmpData[0];

                $arrPricing = CFinancialHelper::splitVat($arrRow['intCostIncVatInPence'], true);
                $intCostExVatInPence = round($arrPricing['exvat']);
                $floCostExVat        = bcdiv($intCostExVatInPence, 100, 2);

                // TODO: review the use of this data, as there's a potential bug - floCostExVat is in pounds, not pence!
                $arrRow['intCostExcVatInPence'] = $intCostExVatInPence;
                $arrRow['floCostExcVatInPence'] = $floCostExVat;

                switch(strtoupper($arrRow['strCustomerSectorHandle'])) {
                    case 'BUSINESS':
                        $arrRow['intCostToShow'] = $intCostExVatInPence;
                        $arrRow['floCostToShow'] = $floCostExVat;
                        break;
                    case 'CONSUMER':
                        $arrRow['intCostToShow'] = $arrRow['intCostIncVatInPence'];
                        $arrRow['floCostToShow'] = bcdiv($arrRow['intCostIncVatInPence'], 100, 2);
                        break;
                    default:
                        $arrRow['intCostToShow'] = 0;
                        $arrRow['floCostToShow'] = '0.00';
                        break;
                }

                $dataCache[$intTariffID] = $arrRow;
            }
        }

        // As per the old behaviour...
        return $dataCache[$intTariffID];
    }

       /**
        * Checks if the product has a particular component
        * <AUTHOR> <<EMAIL>>
        * @access public
        * @param string ComponentHandle
        * @param int Service Definition Id of the Product
        */
        public static function productHasComponent($service_definition_id, $strComponentHandle)
        {
        $dbhConnection = get_named_connection_with_db('product_reporting');

                $strQuery = "SELECT COUNT(*) intCount
                               FROM products.service_definitions sd
                         INNER JOIN products.service_component_config scc
                                 ON sd.service_definition_id = scc.service_definition_id
                         INNER JOIN products.tblServiceComponentProduct scp
                                 ON scc.service_component_id = scp.intServiceComponentID
                         INNER JOIN products.tblServiceComponentProductType scpt
                                 ON scp.intServiceComponentProductTypeID = scpt.intServiceComponentProductTypeID
                              WHERE sd.service_definition_id = $service_definition_id
                                AND scpt.vchHandle = '$strComponentHandle'
                                AND (scc.free_quantity > 0 OR scc.default_quantity > 0 OR scc.max_quantity >0)";
        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);
        $intCount = PrimitivesResultGet($resResult, 'intCount');

        if ($intCount > 0) {
            return true;
                }

        return false;

        } // End of function productHasComponent

    /**
    * Get an array of all the Service Component IDs for given $strCustomerSectorHandle and $strProductTypeHandle
    *
    *
    * @access public
    * @static
    * <AUTHOR>
    * @param string Customer sector handle
    * @param string Product type handle
    * @return array List of product
    */
    public function getAllSignupProducts($strCustomerSectorHandle, $strProductTypeHandle)
    {
        $dbhConn = get_named_connection_with_db('product');

        $strQuery="SELECT scp.intServiceComponentID AS intServiceComponentID,
                          sc.name AS ProductName
                     FROM products.service_components sc
                    INNER JOIN products.tblServiceComponentProduct scp
                       ON sc.service_component_id = scp.intServiceComponentID
                    INNER JOIN products.tblServiceComponentProductType scpt
                       ON scp.intServiceComponentProductTypeID = scpt.intServiceComponentProductTypeID
                      AND scpt.vchHandle = '$strProductTypeHandle'
                    INNER JOIN tblCustomerSector cs
                       ON scp.intCustomerSectorID = cs.intCustomerSectorID
                      AND cs.vchHandle = '$strCustomerSectorHandle'
                    WHERE sc.available = 'Yes' ";

        $refResult = PrimitivesQueryOrExit($strQuery, $dbhConn,"getAllSignupProducts '$strCustomerSectorHandle', '$strProductTypeHandle'");
        $arrProducts = PrimitivesResultsAsArrayGet($refResult);

        return $arrProducts;
    }

    /**
    * Get an array of all upgrade products based on intServiceComponentID, strCustomerSectorHandle, strProductTypeHandle
    *
    *
    * @access public
    * @static
    * <AUTHOR>
    * @param integer Service component ID
    * @param string Customer sector handle
    * @param string Product type handle
    * @param array Excluded service component IDs
    * @param int Service def
    * @return array List of products
    */
    public function getAllUpgradeProducts($intServiceComponentID, $strCustomerSectorHandle, $strProductTypeHandle,
                                   $arrExcludedServiceComponentIDs = array(), $intServiceDefinitionId = null)
    {
        $dbhConn = get_named_connection_with_db('product');

        $strExcludedServiceComponentIDs = $intServiceComponentID;

        if (sizeof($arrExcludedServiceComponentIDs) > 0) {
            $strExcludedServiceComponentIDs .= ','.implode(',',$arrExcludedServiceComponentIDs);
        }

        $strQuery="SELECT scp.intServiceComponentID AS intServiceComponentID,
                          sc.name AS ProductName,
                          sc.description as strDescription
                     FROM products.service_components sc
               INNER JOIN products.tblServiceComponentProduct scp
                       ON sc.service_component_id = scp.intServiceComponentID
               INNER JOIN products.tblServiceComponentProductType scpt
                       ON scp.intServiceComponentProductTypeID = scpt.intServiceComponentProductTypeID
                      AND scpt.vchHandle = '$strProductTypeHandle'
               INNER JOIN products.tblCustomerSector cs
                       ON scp.intCustomerSectorID = cs.intCustomerSectorID
                      AND cs.vchHandle = '$strCustomerSectorHandle'
               INNER JOIN products.service_component_config scc
                       ON scc.service_component_id = sc.service_component_id
                    WHERE scp.intServiceComponentID NOT IN ($strExcludedServiceComponentIDs)
                      AND sc.available = 'Yes' ";

        if($intServiceDefinitionId !== null) {
            $strQuery .= "AND scc.service_definition_id = $intServiceDefinitionId";
        }
        $refResult = PrimitivesQueryOrExit($strQuery, $dbhConn,"getAllUpgradeProducts('$intServiceComponentID', '$strCustomerSectorHandle', '$strProductTypeHandle')");
        $arrProducts = PrimitivesResultsAsArrayGet($refResult);

        return $arrProducts;
    }

    /**
    * Get an array of all product contract options for $intServiceComponentID and $strProductComponentHandle
    *
    * <AUTHOR>
    *
    * @param integer $intServiceComponentID     Service Component ID
    * @param string  $strProductComponentHandle Component handle (optional)
    * @param boolean $bolAlwaysShowIncVat       Do we always want to show vat
    * @param boolean $showEndedContracts        Do we want to include ended tariffs/contracts (for backwards
    *  											compatibility, set to true!)
    * @param string  $tariffType                Optional tariff type to get (default to 'DEFAULT')
    *
    * @return array
    */
    public static function getProductContractOptions($intServiceComponentID,
        $strProductComponentHandle = 'SUBSCRIPTION', $bolAlwaysShowIncVat = false, $tariffType = 'DEFAULT'
    )
    {
        $dbhConn = get_named_connection_with_db('product');

        $strQuery = 'SELECT cl.vchHandle AS strContractHandle,
                        cl.vchDisplayName AS strDisplayName,
                        MIN(t.intCostIncVatPence) AS intCostIncVatInPence,
                        cs.vchHandle AS strCustomerSectorHandle, 
                        t.intTariffID
                       FROM dbProductComponents.tblTariff t
                    INNER JOIN dbProductComponents.tblTariffType as tt
                         ON tt.intTariffTypeID = t.intTariffTypeID
                        AND tt.vchHandle = "'.$tariffType.'"
                    INNER JOIN dbProductComponents.tblContractLength cl
                        ON cl.intContractLengthID = t.intContractLengthID
                    INNER JOIN dbProductComponents.tblProductComponentConfig pcc
                        ON pcc.intProductComponentConfigID = t.intProductComponentConfigID
                    INNER JOIN dbProductComponents.tblProductComponent pc
                        ON pc.intProductComponentID = pcc.intProductComponentID
                        AND pc.vchHandle = "' . $strProductComponentHandle . '"
                    INNER JOIN products.tblServiceComponentProduct scp
                        ON pcc.intServiceComponentProductID = scp.intServiceComponentProductID
                        AND scp.intServiceComponentID = ' . $intServiceComponentID . '
                    INNER JOIN products.tblCustomerSector cs
                        ON cs.intCustomerSectorID = scp.intCustomerSectorID
                    WHERE (t.dtmEnd IS NULL OR t.dtmEnd > NOW())
                    GROUP BY t.intContractLengthID, cl.vchHandle, cl.vchDisplayName, cs.vchHandle';

        $refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'getSignupContractOptions ' . $intServiceComponentID);
        $arrContractDefinitions = PrimitivesResultsAsArrayGet($refResult);

        require_once DATABASE_LIBRARY_ROOT.'CoreObjects/Financial/CFinancialHelper.inc';

        foreach ($arrContractDefinitions as $intKey=>$arrRow) {

            $arrPricing = CFinancialHelper::splitVat($arrRow['intCostIncVatInPence'], true);

            $intCostExVatInPence = round($arrPricing['exvat']);
            $floCostExVat = $intCostExVatInPence/100;

            $arrContractDefinitions[$intKey]['intCostExcVatInPence'] = $intCostExVatInPence;
            $arrContractDefinitions[$intKey]['intTariffID'] = $arrRow['intTariffID'];

            switch (strtoupper($arrRow['strCustomerSectorHandle'])) {

                case 'BUSINESS':
                    if ($bolAlwaysShowIncVat) {
                        $arrContractDefinitions[$intKey]['intCostToShow'] = $arrRow['intCostIncVatInPence'];
                        $arrContractDefinitions[$intKey]['floCostToShow'] = $arrRow['intCostIncVatInPence']/100;
                    } else {
                        $arrContractDefinitions[$intKey]['intCostToShow'] = $intCostExVatInPence;
                        $arrContractDefinitions[$intKey]['floCostToShow'] = $floCostExVat;
                    }
                    break;

                case 'CONSUMER':
                    $arrContractDefinitions[$intKey]['intCostToShow'] = $arrRow['intCostIncVatInPence'];
                    $arrContractDefinitions[$intKey]['floCostToShow'] = $arrRow['intCostIncVatInPence']/100;
                    break;

                default:
                    $arrContractDefinitions[$intKey]['intCostToShow'] = 0;
                    $arrContractDefinitions[$intKey]['floCostToShow'] = '0.00';
                    break;
            }
        }

        return $arrContractDefinitions;
    }

    /**
    * get productComponentConfigID for intServiceComponentID and strProductComponentHandle
    *
    *
    * @access public
    * @static
    * <AUTHOR>
    * @param integer Service component ID
    * @param string Product component handle (optional)
    * @return integer Product component config ID
    */
    public function getProductComponentConfigID($intServiceComponentID, $strProductComponentHandle = 'SUBSCRIPTION')
    {
        $dbhConn = get_named_connection_with_db('product');

        $strQuery = 'SELECT pcc.intProductComponentConfigID AS intProductComponentConfigID ' .
                    '  FROM dbProductComponents.tblProductComponentConfig pcc ' .
               ' INNER JOIN dbProductComponents.tblProductComponent pc '.
               '         ON pc.intProductComponentID = pcc.intProductComponentID '.
               ' INNER JOIN products.tblServiceComponentProduct scp '.
               '         ON scp.intServiceComponentProductID = pcc.intServiceComponentProductID '.
                      ' AND pc.vchHandle = "'.$strProductComponentHandle.'" ' .
                    ' WHERE scp.intServiceComponentID = '.$intServiceComponentID.' ' ;

        $refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, "isPAYGProduct('$intServiceComponentID')");
        $arrProductComponentConfigIDs = PrimitivesResultsAsListGet($refResult);

        return $arrProductComponentConfigIDs[0];
    }

    /**
    * get contract length details
    *
    *
    * @access public
    * @static
    * <AUTHOR>
    * @param string Contract length handle
    * @return array Contract length details
    */
    public function getContractLengthDetails($strContractLengthHandle)
    {
        $dbhConn = get_named_connection_with_db('product');

        $strQuery = 'SELECT cl.intContractLengthID AS intContractLengthID, ' .
                    '       cl.vchHandle AS vchHandle, ' .
                    '       cl.vchDisplayName AS vchDisplayName ' .
                    '  FROM dbProductComponents.tblContractLength cl ' .
                    ' WHERE cl.vchHandle = "'.$strContractLengthHandle.'" ' ;

        $refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, "getContractLengthDetails('$strContractLengthHandle')");
        $arrContractLengthDetails = PrimitivesResultsAsArrayGet($refResult);

        return $arrContractLengthDetails[0];
    } // class CProduct extends CComponent

    /**
    * Get an array of all payment frequency options for $intServiceComponentID
    *
    *
    * @access public
    * @static
    * <AUTHOR>
    * @param integer Service component ID
    * @param string Product component handle (optional)
    * @param string Contract handle
    * @param string $tariffType Optional tariff type handle
    * @return array List of frequency options
    */
    public function getProductPaymentFrequencyOptions($intServiceComponentID, $strProductComponentHandle = 'SUBSCRIPTION', $strContractHandle = '', $tariffType = 'DEFAULT')
    {
        if ($intServiceComponentID == '' || $strProductComponentHandle == '') {
            return array();
        }

        $dbhConn = get_named_connection_with_db('product');

        $strQuery = 'SELECT DISTINCT pf.vchHandle AS strPaymentFrequencyHandle, ' .
                    '       pf.vchDisplayName AS strDisplayName, ' .
                    '       t.intTariffID AS intTariffID, ' .
                    '       t.intCostIncVatPence AS intCostIncVatInPence ' .
                    '  FROM dbProductComponents.tblTariff t ' .
               ' INNER JOIN dbProductComponents.tblTariffType AS tt '.
               '         ON tt.intTariffTypeId = t.intTariffTypeId '.
               '        AND tt.vchHandle = "'.$tariffType.'" '.
               ' INNER JOIN dbProductComponents.tblPaymentFrequency pf ' .
                       ' ON pf.intPaymentFrequencyID = t.intPaymentFrequencyID ' .
               ' INNER JOIN dbProductComponents.tblProductComponentConfig pcc '.
               '         ON pcc.intProductComponentConfigID = t.intProductComponentConfigID '.
               ' INNER JOIN products.tblServiceComponentProduct scp '.
               '         ON scp.intServiceComponentProductID = pcc.intServiceComponentProductID '.
                      ' AND scp.intServiceComponentID = '.$intServiceComponentID.' ' .
               ' INNER JOIN dbProductComponents.tblProductComponent pc '.
               '         ON pc.intProductComponentID = pcc.intProductComponentID '.
                      ' AND pc.vchHandle = "'.$strProductComponentHandle.'" ' ;

        if ($strContractHandle != '') {
            $strQuery .= ' INNER JOIN dbProductComponents.tblContractLength cl ' .
                            ' ON cl.intContractLengthID = t.intContractLengthID ' .
                           ' AND cl.vchHandle = "'.$strContractHandle.'" ' ;
        }

        $strQuery .= ' WHERE (t.dtmEnd IS NULL OR t.dtmEnd > NOW()) ' .
                     'GROUP BY t.intPaymentFrequencyID, pf.vchHandle, pf.vchDisplayName, t.intTariffID, ' .
                     't.intCostIncVatPence ';
        $refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, "getSignupContractOptions '$intServiceComponentID'");
        $arrPaymentFrequencyDefinitions = PrimitivesResultsAsArrayGet($refResult);

        require_once DATABASE_LIBRARY_ROOT.'CoreObjects/Financial/CFinancialHelper.inc';

        foreach($arrPaymentFrequencyDefinitions as $intKey=>$arrRow) {

            $arrPricing = CFinancialHelper::splitVat($arrRow['intCostIncVatInPence'], true);

            $intCostExVatInPence = round($arrPricing['exvat']);

            $arrPaymentFrequencyDefinitions[$intKey]['intCostExcVatInPence'] = $intCostExVatInPence;
        }

        return $arrPaymentFrequencyDefinitions;
    }

    /**
    * Get an tariff ID base on $intServiceComponentID, $strContractHandle, $strPaymentFrequencyHandle
    *
    *
    * @access public
    * @static
    * <AUTHOR>
    * @param integer Service component ID
    * @param string Contract handle
    * @param string Payment frequency handle
    * @param strint Product component handle (optional)
    * @param string Tariff type handle (optional)
    * @return integer Tariff ID
    */
    public function getProductTariffID($intServiceComponentID, $strContractHandle, $strPaymentFrequencyHandle, $strProductComponentHandle = 'SUBSCRIPTION', $strTariffType = 'DEFAULT')
    {
        $dbhConn = get_named_connection_with_db('product');

        $strQuery = 'SELECT t.intTariffID AS intTariffID ' .
                    '  FROM dbProductComponents.tblTariff t ' .
               ' INNER JOIN dbProductComponents.tblContractLength cl ' .
                       ' ON cl.intContractLengthID = t.intContractLengthID ' .
                      ' AND cl.vchHandle = "'.$strContractHandle.'" ' .
               ' INNER JOIN dbProductComponents.tblPaymentFrequency pf ' .
                       ' ON pf.intPaymentFrequencyID = t.intPaymentFrequencyID ' .
                      ' AND pf.vchHandle = "'.$strPaymentFrequencyHandle.'" ' .
               ' INNER JOIN dbProductComponents.tblTariffType tt ' .
                       ' ON t.intTariffTypeID = tt.intTariffTypeID ' .
                      ' AND tt.vchHandle = "'.$strTariffType.'" ' .
               ' INNER JOIN dbProductComponents.tblProductComponentConfig pcc '.
               '         ON pcc.intProductComponentConfigID = t.intProductComponentConfigID '.
               ' INNER JOIN products.tblServiceComponentProduct scp '.
               '         ON scp.intServiceComponentProductID = pcc.intServiceComponentProductID '.
                      ' AND scp.intServiceComponentID = '.$intServiceComponentID.' ' .
               ' INNER JOIN dbProductComponents.tblProductComponent pc '.
               '         ON pc.intProductComponentID = pcc.intProductComponentID '.
                      ' AND pc.vchHandle = "'.$strProductComponentHandle.'" ' .
                      ' AND (t.dtmEnd IS NULL OR t.dtmEnd > NOW()) ';

        if (false === ($refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, "getSignupContractOptions '$intServiceComponentID'"))) {
            return false;
        }

        if (PrimitivesNumRowsGet($refResult) > 0) {
            $intTariffID = PrimitivesResultGet($refResult, 'intTariffID');
        } else {
            return false;
        }

        return $intTariffID;
    }

    /**
    * Get the customer sector id from the handle
     * TODO: implement caching
    *
    * @access public
    * <AUTHOR>
    * @param  str Handle
    * @return int Customer Sector ID
    */
    public function getCustomerSectorIDFromHandle($strHandle)
    {
        $dbhConnection = get_named_connection_with_db('product');

        $strHandle = addslashes($strHandle);

        $strQuery = 'SELECT intCustomerSectorID ' .
                    ' FROM tblCustomerSector ' .
                    " WHERE vchHandle = '$strHandle'";

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection, 'Get the customer sector id from the handle');

        $intCustomerSectorID = PrimitivesResultGet($resResult, 'intCustomerSectorID');

        return $intCustomerSectorID;

    }

    /**
     * Get the customer sector handle from service component id
     * Probably should be static, but could cause inheritance problems...
     * Returns boolean false if the service component cannot be found
     *
     * @param int $intServiceComponentID the Service Component ID
     *
     * @return str
     */
    public function getCustomerSectorHandleFromServiceComponentID($intServiceComponentID)
    {
        // There's ~900 of these, but the data is minimal, so we cache in a single pass
        static $dataCache = array();

        $intServiceComponentID = (int) $intServiceComponentID;

        if (empty($dataCache)) {
            $query = <<<EOQ
SELECT
    scp.intServiceComponentID,
    cs.vchHandle
FROM
    tblServiceComponentProduct      AS scp 
    INNER JOIN tblCustomerSector    AS cs   ON cs.intCustomerSectorID = scp.intCustomerSectorID
EOQ;

            $conn = get_named_connection_with_db('product_reporting');
            $resResult = PrimitivesQueryOrExit($query, $conn, 'Get the customer sector handle');
            $tmpList   = PrimitivesResultsAsArrayGet($resResult, 'intServiceComponentID');

            // Convert to a simple key => value hash table lookup
            foreach ($tmpList as $tmpID => $scp) {
                $dataCache[$tmpID] = $scp['vchHandle'];
            }
        }

        return (array_key_exists($intServiceComponentID, $dataCache) ? $dataCache[$intServiceComponentID] : false);
    }

    /**
    * Gets the customer sector handle for a service id
    *
    * @access public
    * @static
    * <AUTHOR>
    * @param  int Service ID
    * @return str Customer Sector Handle
    */
    public function getServiceCustomerSectorHandle($intServiceID)
    {

        /**
        * Userdata access library to get userdata service record
        */
        require_once(USERDATA_ACCESS_LIBRARY);

        $arrService = userdata_service_get($intServiceID);

        if (isset($arrService['type']) && $arrService['type'] > 0) {
            /**
            * Product access library to get service definition record
            */
            require_once(PRODUCT_ACCESS_LIBRARY);

            $arrProduct = product_get_service($arrService['type']);

            if (isset($arrProduct['service_definition_id']) && $arrProduct['service_definition_id'] > 0) {
                if ($arrProduct['type'] == 'business' || stristr($arrProduct['name'], 'bus')) {
                    $strCustomerSectorHandle = 'BUSINESS';
                } elseif (stristr($arrProduct['name'], 'staff')) {
                    $strCustomerSectorHandle = 'STAFF';
                } else {
                    $strCustomerSectorHandle = 'CONSUMER';
                }

                return $strCustomerSectorHandle;

            } // end if

        } // end if

        return false;

    } // End of function getServiceCustomerSectorHandle

    /**
    * Get the active component for a particular service component product
    *
    * @access public
    * @static
    * <AUTHOR>
    * @param  int Service ID
    * @param  str Service Component Product Type Handle
    * @return int
    */
    public static function getComponentIDByServiceID($intServiceID, $strServiceComponentProductTypeHandle, $arrStatus = array('active'))
    {
        $dbhConnection = get_named_connection_with_db('userdata');

        $strQuery = 'SELECT CAST(c.component_id AS UNSIGNED INTEGER) AS intComponentID ' .
                    '  FROM components c ' .
                    ' INNER JOIN products.tblServiceComponentProduct scp ' .
                    '    ON c.component_type_id = scp.intServiceComponentID ' .
                    ' INNER JOIN products.tblServiceComponentProductType scpt ' .
                    '    ON scp.intServiceComponentProductTypeID = scpt.intServiceComponentProductTypeID ' .
                    "   AND scpt.vchHandle = '$strServiceComponentProductTypeHandle' " .
                    " WHERE c.service_id = '$intServiceID' " .
                    ' AND c.status IN (\'' . implode('\', \'', $arrStatus) . '\')';
        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);
        $intComponentID  = PrimitivesResultGet($resResult, 'intComponentID');

        return $intComponentID;
    } // End of function getComponentIDByServiceID

    /**
     * getAllComponentsByServiceId
     * returns all products (ServiceComponentProducts) of specified type (i.e. WLR)
     *
     * <AUTHOR>
     * @param int $intServiceID
     * @param int $strServiceComponentProductTypeHandle
     * @return array in format:
     * array ( intComponentId_1 =>
     *                          array(
     *                              intComponentId_1
     *                              intServiceId
     *                              intComponentTypeId
     *                              intConfigId
     *                              strDescription
     *                              strStatus
     *                              dteCreationDate
     *                              stmTimestamp
     *                          )
     *          .....
     *      )
     *
     */

    public static function getAllComponentsByServiceId($intServiceId = 0, $strServiceComponentProductTypeHandle = '')
    {
        $dbhConnection = get_named_connection_with_db('userdata');

        $strQuery = "SELECT CAST(c.component_id AS UNSIGNED INTEGER) AS intComponentId,
                            service_id as intServiceId,
                            component_type_id as intComponentTypeId,
                            config_id as intConfigId,
                            description as strDescription,
                            status as strStatus,
                            creationdate as dtmCreationDate,
                            timestamp as stmTimestamp
                       FROM components c
                 INNER JOIN products.tblServiceComponentProduct scp
                         ON c.component_type_id = scp.intServiceComponentID
                 INNER JOIN products.tblServiceComponentProductType scpt
                         ON scp.intServiceComponentProductTypeID = scpt.intServiceComponentProductTypeID
                        AND scpt.vchHandle = '$strServiceComponentProductTypeHandle'
                      WHERE c.service_id = $intServiceId ";

        return PrimitivesResultsAsArrayGet(PrimitivesQueryOrExit($strQuery, $dbhConnection), 'intComponentId');
    }

    /**
    * Check whether the product is a staff
    *
    * @static
    * @access public
    * <AUTHOR>
    * @param  int Service Component ID
    * @return bool true (is staff) or false (is not staff)
    */
    public function IsStaffServiceComponentProduct($intServiceComponentID)
    {
        $strCustomerSectorHandle = CProduct::getProductCustomerSectorHandle($intServiceComponentID);

        switch ($strCustomerSectorHandle) {
            case 'STAFF' :
                return true;

                break;

            case 'CONSUMER' :
            case 'BUSINESS' :
            default :
                return false;

        } // end switch

        return false;

    } // End of function IsStaffServiceComponentProduct

    /**
    * Get the customer sector of a service component id
    *
    * @static
    * @access public
    * <AUTHOR>
    * @param  int Service Component ID
    * @return str Customer Sector Handle
    */
    public function getProductCustomerSectorHandle($intServiceComponentID)
    {
        // Check that it is not a staff product (only upgrade to them via workplace)
        $dbhConnection = get_named_connection_with_db('product');

        $strQuery = 'SELECT cs.vchHandle ' .
                    '  FROM tblServiceComponentProductType scpt ' .
                    ' INNER JOIN tblServiceComponentProduct scp ' .
                    '    ON scpt.intServiceComponentProductTypeID = scp.intServiceComponentProductTypeID ' .
                    "   AND scp.intServiceComponentID = '$intServiceComponentID' " .
                    ' INNER JOIN tblCustomerSector cs ON scp.intCustomerSectorID = cs.intCustomerSectorID';

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

        $strHandle  = PrimitivesResultGet($resResult, 'vchHandle');

        return $strHandle;

    } // End of function getProductCustomerSectorHandle

    /**
    * Public Methods
    */

    /**
    * refreshInstance
    *
    * @access public
    * <AUTHOR>
    * @param
    * @return
    */
    public function refreshInstance($intComponentID)
    {
        //Parent constructor
        $this->CComponent($intComponentID);

        //Product type details
        $dbhConn = get_named_connection_with_db('product');

        $strQuery="SELECT pt.vchHandle as strProductTypeHandle, \n" .
                  "       pt.vchDescription as strProductTypeDescription, \n" .
                  "       p.intServiceComponentProductID, \n" .
                  "       p.intServiceComponentProductTypeID \n" .
                  "  FROM products.tblServiceComponentProductType pt \n" .
                  " INNER JOIN products.tblServiceComponentProduct p \n" .
                  "    ON pt.intServiceComponentProductTypeID = p.intServiceComponentProductTypeID \n" .
                  " INNER JOIN products.service_components sc \n" .
                  "    ON p.intServiceComponentID = sc.service_component_id \n" .
                  " INNER JOIN userdata.components c \n" .
                  "    ON sc.service_component_id = c.component_type_id \n" .
                  " WHERE c.component_id = '$intComponentID'";

        $refResult = PrimitivesQueryOrExit($strQuery, $dbhConn,'Fetch Product Type info for CProduct object construction');
        $arrProductDetails = PrimitivesResultGet($refResult);

        if((!$arrProductDetails) || count($arrProductDetails) < 1) {
            return false;
        }

        //m_strProductTypeHandle
        if( (!isset($arrProductDetails['strProductTypeHandle']) ) || $arrProductDetails['strProductTypeHandle'] == '') {
            return false;
        }
        $this->m_strProductTypeHandle = stripslashes($arrProductDetails['strProductTypeHandle']);

        //m_strProductTypeDescription
        if( (!isset($arrProductDetails['strProductTypeDescription']) ) || $arrProductDetails['strProductTypeDescription'] == '') {
            return false;
        }
        $this->m_strProductTypeDescription = stripslashes($arrProductDetails['strProductTypeDescription']);

        $this->intServiceComponentProductId = $arrProductDetails['intServiceComponentProductID'];
        $this->intServiceComponentProductTypeId = $arrProductDetails['intServiceComponentProductTypeID'];

        return true;
    } // function refreshInstance($intComponentID)

    /**
    * Returns the Product Components
    *
    * @access public
    * @return array Product Components
    */
    public function getOptionalProductComponents($arrStatus='')
    {
        $arrOptionalProductComponents=array();
        $arrProductComponentInstanceIDs = $this->getOptionalProductComponentInstanceIDs($arrStatus);
        foreach ($arrProductComponentInstanceIDs as $intID) {
            $arrOptionalProductComponents[$intID] = CProductComponent::createInstance($intID);
        }

        return $arrOptionalProductComponents;
    } // function getOptionalProductComponents()

    /**
    * Returns the Product Components
    *
    * @access public
    * @return array Product Components
    */
    public function getProductComponents()
    {
        if (is_null($this->m_arrProductComponents)) {
            //Instantiate on first use
            $arrProductComponentInstanceIDs = $this->getProductComponentInstanceIDs();

            foreach ($arrProductComponentInstanceIDs as $intID) {
                $this->m_arrProductComponents[$intID] = CProductComponent::createInstance($intID);
            }
        } // if(is_null($this->m_arrProductComponents))

        return $this->m_arrProductComponents;
    } // function getProductComponents()

    /**
    * Calls scheduleCharges on each of this object's product components
    *
    *
    * @access public
    * <AUTHOR>
    * @return boolean success
    */
    public function scheduleCharges()
    {
        $arrReturn = array();

        $arrProductComponents = $this->getProductComponents();

        foreach($arrProductComponents as $objProductComponent) {

            $arrScheduledPaymentIDs = $objProductComponent->scheduleCharges();

            $arrReturn = array_merge($arrReturn, $arrScheduledPaymentIDs);
        }

        return $arrReturn;
    }


    /**
    * Calls calculateCancellationCharges on each of this object's product components
    *
    *
    * @access public
    * <AUTHOR>
    * @return boolean success
    */
    public function calculateCancellationCharges()
    {
        $arrCharges = array();
        $arrProductComponent = $this->getProductComponents();
        foreach($arrProductComponent as $objProductComponent) {

            $arrProductComponentCharges = array();
            $arrProductComponentCharges = $objProductComponent->calculateCancellationCharges();

            if(!empty($arrProductComponentCharges)) {
                $arrCharges[$objProductComponent->getHandle()] = $arrProductComponentCharges;
            }

        }

        return $arrCharges;
    }

    /**
    * Calls calculateCharges on each of this object's product components
    *
    * Calculates charges to be applied at a billing run
    *
    * @access public
    * <AUTHOR>
    * @return boolean success
    */
    public function calculateCharges()
    {
        $arrCharges = array();
        $arrProductComponent = $this->getProductComponents();

        foreach($arrProductComponent as $objProductComponent) {

            $arrProductComponentCharges = array();
            $arrProductComponentCharges = $objProductComponent->calculateCharges();

            if(!empty($arrProductComponentCharges)) {
                $arrCharges[] = $arrProductComponentCharges;
            }

        }

        return $arrCharges;
    }


    /**
    * Calls renew on each of this object's product components
    *
    *
    * @access public
    * <AUTHOR>
    * @return boolean success
    */
    public function renew()
    {
        $arrProductComponents = $this->getProductComponents();

        foreach($arrProductComponents as $objProductComponent) {

            $objProductComponent->renew();

        }
    }

    /**
    * Check that the service component product is valid for a signup to be performed on it
    *
    * @access private
    * <AUTHOR>
    * @return boolean true on success, false on failure with details in m_objError
    */
    public function IsValidToSignup($intSalesInvoiceID=0)
    {
        $dbhConnection = get_named_connection_with_db('userdata');

        $strQuery = 'SELECT count(distinct c2.component_id) as intNumServiceComponentProducts ' .
                    '  FROM components c1 ' .
                    ' INNER JOIN products.tblServiceComponentProduct scp1 ' .
                    '    ON c1.component_type_id = scp1.intServiceComponentID ' .
                    ' INNER JOIN components c2 ' .
                    '    ON c1.service_id = c2.service_id ' .
                    '   AND c1.component_id != c2.component_id ' .
                    '   AND c2.status IN ("unconfigured", "queued-activate", "queued-reactivate", "active") ' .
                    ' INNER JOIN products.tblServiceComponentProduct scp2 ' .
                    '    ON c2.component_type_id = scp2.intServiceComponentID ' .
                    '   AND scp1.intServiceComponentProductTypeID = scp2.intServiceComponentProductTypeID ' .
                    " WHERE c1.component_id = '{$this->m_intComponentID}'";

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

        $intNumServiceComponentProducts = PrimitivesResultGet($resResult, 'intNumServiceComponentProducts');

        if (isset($intNumServiceComponentProducts) && $intNumServiceComponentProducts > 0) {
            return false;
        }

        // Check whether the customer is trying to signup up to a Staff product
        if (CProduct::IsStaffServiceComponentProduct($this->getComponentTypeID())) {
            return false;
        }

        // Check that payment has been taken against the component and invoice
        if (isset($intSalesInvoiceID) && $intSalesInvoiceID > 0) {
            return $this->ValidateSalesInvoiceAgainstComponent($intSalesInvoiceID);
        }

        return true;

    } // End of function IsOnlyServiceComponentProduct

    /**
    * Check that a sales invoice exists against a component
    *
    * @access private
    * @param  int Sales Invoice ID
    * @return bool true (valid invoice) or false (invalid invoice)
    */
    public function ValidateSalesInvoiceAgainstComponent($intSalesInvoiceID)
    {
        $dbhConnection = get_named_connection_with_db('financial');

        $strQuery = 'SELECT count(*) as intNumRecords ' .
                    '  FROM sales_invoices si ' .
                    ' INNER JOIN tblScheduledPayment sp ' .
                    '    ON si.sales_invoice_id = sp.intSalesInvoiceID ' .
                    ' INNER JOIN financial.tblConfigProductComponent cpc ' .
                    '    ON sp.intScheduledPaymentID = cpc.intScheduledPaymentID'.
                    ' INNER JOIN userdata.tblProductComponentInstance pci ' .
                    '    ON cpc.intProductComponentInstanceID = pci.intProductComponentInstanceID ' .
                    "   AND pci.intComponentID = '{$this->m_intComponentID}' " .
                    " WHERE si.sales_invoice_id = '$intSalesInvoiceID'";

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

        $intNumRecords = PrimitivesResultGet($resResult, 'intNumRecords');

        if (isset($intNumRecords) && $intNumRecords > 0) {
            return true;
        }

        return false;

    } // End of function ValidateSalesInvoiceAgainstComponent

    /**
    * Validate a product change
    * - Check it is for the same service component product type
    *
    * @access private
    * <AUTHOR>
    * @param  obj New Product (component)
    * @param  int Sales Invoice ID
    * @return boolean true on success, false on failure with details in m_objError
    */
    public function IsValidToUpgrade($objNewProduct, $intSalesInvoiceID=0)
    {
        // Check that new product is same type as old
        if ($this->getProductTypeHandle() != $objNewProduct->getProductTypeHandle()) {
            return false;
        }

        // Check that payment has been taken against the component and invoice
        if (isset($intSalesInvoiceID) && $intSalesInvoiceID > 0) {
            return $objNewProduct->ValidateSalesInvoiceAgainstComponent($intSalesInvoiceID);
        }

        return true;
    } // End of function IsValidToUpgrade

    /**
    * cancelAllOutstandingProductChange
    *
    * @access public
    * <AUTHOR>
    * @param integer Component ID
    * @return
    */
    public function cancelAllOutstandingProductChange($intComponentID)
    {
        $arrExistingEvents = CProductChange::getExistingEvents($intComponentID, true);

        foreach ($arrExistingEvents as $arrEventDetails) {
            $intProductChangeID = $arrEventDetails['intProductChangeID'];

            if ($intProductChangeID > 0) {
                $objExistingEvent = new CProductChange($intProductChangeID);

                $objExistingEvent->cancel();
            }
        }
    } // function cancelAllOutstandingProductChange($intComponentID)

    /**
    * cancelAllOutstandingProductScheduledActions
    *
    * @access public
    * <AUTHOR>
    * @param integer Component ID
    * @param
    * @return
    */
    public function cancelAllOutstandingProductScheduledActions($intComponentID, $objNewProduct = '')
    {
        //cancel all outstanding scheduled product change
        CProduct::cancelAllOutstandingProductChange($intComponentID);
    }

    /**
    * scheduleProductChange
    *
    * @access public
    * <AUTHOR>
    * @param integer Existing component ID
    * @param integer New component ID
    * @param timestamp Due date
    * @return
    */
    public function scheduleProductChange($intExistingComponentID, $intNewComponentID, $uxtDateDue)
    {
        CProduct::cancelAllOutstandingProductScheduledActions($intExistingComponentID);

        $objChange = CProductChange::create($intExistingComponentID, $intNewComponentID, $uxtDateDue);
    }

    /**
    * Calls component configurator enable method on all product components
    *
    *
    * @access protected
    * <AUTHOR>
    * @return boolean success
    */
    public function enableProductComponents($arrArgs = array())
    {
        $bolError = false;
        $errorMessage = '';
        $objProductComponentSubscription = null;

        foreach(array_keys($this->getProductComponents()) as $intID) {

            $objProductComponent = $this->m_arrProductComponents[$intID];

            if (!$objProductComponent instanceof CProductComponent) {

                $bolError = true;

            } else {

                $strStatusHandle = CProductComponent::getStatusHandleFromID($objProductComponent->getStatusID());
                if (!in_array($strStatusHandle, array('QUEUED_DESTROY', 'DESTROYED'))) {

                    //Check if it's subscription component and continue to enable it as last one
                    if ($objProductComponent instanceof CProductComponentSubscription) {

                        $objProductComponentSubscription = $objProductComponent;
                        continue;
                    }

                    if (!$objProductComponent->enable($arrArgs)) {

                        $error = $objProductComponent->getError();
                        if ($error != false) {

                            if (!empty($errorMessage)) {

                                $errorMessage .= ', ';
                            }
                            $errorMessage .= $error->getErrorMessage() . ' ';
                        }
                        $bolError = true;
                    }
                }
            }

            // Need to assign back, as the enable has probably changed something (such as the state)
            $this->m_arrProductComponents[$intID] = $objProductComponent;
        }

        //Finally enable subscription componet if present
        //It's required to eneble subscription componet after LRS
        if (!empty($objProductComponentSubscription)) {

            if (!$objProductComponentSubscription->enable($arrArgs)) {

                $error = $objProductComponentSubscription->getError();

                if ($error != false) {

                    if (!empty($errorMessage)) {

                        $errorMessage .= ', ';
                    }
                    $errorMessage .= $error->getErrorMessage() . ' ';
                }
                $bolError = true;
            }
        }

        if ($bolError) {

            $this->setError(
                __FILE__,
                __LINE__,
                'Error enabling components ('. $errorMessage .').'
            );

            return false;
        }

        return true;
    }

    /**
    * Calls component configurator disable method on all product components
    *
    *
    * @access protected
    * <AUTHOR>
    * @return boolean success
    */
    public function disableProductComponents()
    {
        $bolError = false;

        foreach(array_keys($this->getProductComponents()) as $intID) {
            $objProductComponent = $this->m_arrProductComponents[$intID];

            if (!$objProductComponent instanceof CProductComponent) {
                $bolError = true;
            } else {
                $strStatusHandle = CProductComponent::getStatusHandleFromID($objProductComponent->getStatusID());
                if ($strStatusHandle == 'ACTIVE') {
                    if (!$objProductComponent->disable()) {
                        $bolError = true;
                    }
                }
            }

            $this->m_arrProductComponents[$intID] = $objProductComponent;
        }

        if ($bolError) {
            return false;
        }

        return true;
    }

    /**
     * Component configurator function. Destroys the component and all product components.
     *
     * @return boolean False if the component is destroyed, otherwise true
     */
    public function destroy()
    {
        $ok = $this->destroyProductComponents();

        if ($ok) {
            $this->prvSetStatus('destroyed');

            // Sanity check
            if ($this->getStatus() == 'destroyed') {
                $this->prvGetEventLogger()->logStatusChange('GenericProductDestroyed');
            } else {
                $ok = false;
            }
        }

        return $ok;
    }

    /**
    * Calls component configurator destroy methods on all product components
    *
    *
    * @access protected
    * <AUTHOR>
    * @return boolean success
    */
    public function destroyProductComponents($arrArgs = array())
    {
        $bolFullCancellation = (isset($arrArgs['bolFullCancellation'])) ? $arrArgs['bolFullCancellation'] : true;
        $bolError = false;

        foreach(array_keys($this->getProductComponents()) as $intID) {
            $objProductComponent = $this->m_arrProductComponents[$intID];

            if (!$objProductComponent instanceof CProductComponent || !$objProductComponent->destroy($arrArgs)) {
                $bolError = true;
            }

            $this->m_arrProductComponents[$intID] = $objProductComponent;
        }

        if ($bolError) {
            return false;
        }

        return true;
    }

    public function destroyUnconfiguredProductComponents($intProdCompInstanceIDs = array())
    {
        foreach($intProdCompInstanceIDs as $intProdCompInstanceID) {
            $this->setStatusToDestroyForProdComponents($intProdCompInstanceID);
        }
    }

    public function setStatusToDestroyForProdComponents($intProdCompInstanceID)
    {
        $intStatusID = CProductComponent::getStatusIDFromHandle(CProduct::STATUS_DESTROYED);
        $dbhConnection = get_named_connection_with_db('userdata');
        $strQuery = 'UPDATE tblProductComponentInstance ' .
                " SET intStatusID  = '$intStatusID', " .
                "     dtmEnd = NOW() " .
                " WHERE intProductComponentInstanceID = '$intProdCompInstanceID'";
        PrimitivesQueryOrExit($strQuery, $dbhConnection, 'Update the product component instance status');
        $this->m_intStatusID = $intStatusID;

        return true;
    }

    //
    // Private Methods
    //

    /**
    * Returns the IDs of each of the product component instances on this component
    *
    * @access private
    * @param  $arrProductComponents - filter by product components
    * @param  $arrStatus - filter by status
    * @param  $intComponentIdArg - if this is given the method can be called staticly
    * <AUTHOR>
    * @return
    */
    public function getProductComponentInstanceIDs($arrProductComponents = array(), $arrStatus = array(), $intComponentIdArg = null)
    {
        $dbhConn = get_named_connection_with_db('userdata');

        $intComponentId = ($intComponentIdArg==null)
                        ? $this->m_intComponentID
                        : $intComponentIdArg;

        $strQuery="SELECT pci.intProductComponentInstanceID as intProductComponentInstanceID
                     FROM userdata.tblProductComponentInstance pci
                    WHERE pci.intComponentID = '$intComponentId'";

        if (is_array($arrProductComponents) && !empty($arrProductComponents)) {
            $strQuery.=" AND intProductComponentID IN (".implode(',',$arrProductComponents).")";
        }

        if ( count($arrStatus) > 0 ) {
            $strQuery.=" AND intStatusID IN (".implode(',',$arrStatus).")";
        }

        $refResult = PrimitivesQueryOrExit($strQuery, $dbhConn,"Fetch product component instance ids for component id '$intComponentId'");
        $arrProductComponentInstanceIDs = PrimitivesResultsAsListGet($refResult);

        return $arrProductComponentInstanceIDs;
    }

    /**
    *
    */
    public function listProductComponentInstanceIdByHandle()
    {
        // Don't use reporting because this causes a replication lag issue during sign up
        $dbhRead = get_named_connection_with_db('userdata');

        $strQuery = "SELECT pci.intProductComponentInstanceID, \n" .
                    "       pc.vchHandle" .
                    "  FROM tblProductComponentInstance pci \n" .
                    " INNER JOIN dbProductComponents.tblProductComponent pc \n" .
                    "    ON pci.intProductComponentID = pc.intProductComponentID " .
                    ' WHERE pci.intComponentID = ' . $this->getComponentID();

        if (false === ($resProdCompIns = PrimitivesQueryOrExit($strQuery, $dbhRead, 'CProduct::listProductComponentInstanceByHandle', true))) {
            return false;
        }

        if (PrimitivesNumRowsGet($resProdCompIns) == 0) {
            return false;
        }

        $arrProdCompIns = PrimitivesResultsAsArrayGet($resProdCompIns);

        foreach ($arrProdCompIns as $idx => $recProdCompIns) {
            $arrProdCompInsIdByHandle[$recProdCompIns['vchHandle']] = $recProdCompIns['intProductComponentInstanceID'];
        }

        return $arrProdCompInsIdByHandle;
    } // function listProductComponentInstanceIdByHandle()

    /**
    * Returns the IDs of optional product component instances on this component
    *
    * @access private
    * <AUTHOR>
    * @return
    */
    public function getOptionalProductComponentInstanceIDs($arrStatus='')
    {
        $dbhConn = get_named_connection_with_db('userdata');

        $strQuery="SELECT pci.intProductComponentInstanceID as intProductComponentInstanceID
                     FROM userdata.tblProductComponentInstance pci
               INNER JOIN dbProductComponents.tblProductComponent pc
                       ON pc.intProductComponentID = pci.intProductComponentID
               INNER JOIN dbProductComponents.tblStatus s
                       ON pci.intStatusID = s.intStatusID
               INNER JOIN dbProductComponents.tblProductComponentConfig pcc
                       ON pcc.intProductComponentID = pc.intProductComponentID
               INNER JOIN products.tblServiceComponentProduct scp
                       ON scp.intServiceComponentProductID = pcc.intServiceComponentProductID
                    WHERE pci.intComponentID = '{$this->m_intComponentID}'
                      AND scp.intServiceComponentID = '{$this->m_intServiceComponentID}'
                      AND pcc.intDefaultQuantity = 0";
        if ($arrStatus!=='') {
            $strQuery.=' AND s.vchHandle IN (\'' . implode('\', \'', $arrStatus) . '\')';
        }

        $refResult = PrimitivesQueryOrExit($strQuery, $dbhConn,"Fetch product component instance ids for component id '{$this->m_intComponentID}'");
        $arrProductComponentInstanceIDs = PrimitivesResultsAsListGet($refResult);

        return $arrProductComponentInstanceIDs;
    }

    /**
    * Log an status change. Override this for your particular event handles for your product.
    *
    * @access private
    * <AUTHOR>
    * @return
    */
    public function logStatusChange($strStatus)
    {
        // This function must be over-ridden by the sub-class
        return false;
    }

    /**
     * Return back high-level information about a product on an account, in an attempt to unify this process.
     *
     * Assumptions: there is no more than 1 strServiceProductComponentTypeHandle on the account in a non-destroyed state.
     * Would probably need an alternative static public method to handle this situation, and maybe refactor
     * this method accordingly.
     *
     * @static
     * @access public
     * @return FALSE = error
     *         empty array = no summary available
     *         populated array = key-value pairs of data, with parent array keys being
     *         'common' and 'extended'
     */

    public static function getSingleProductComponentSummary($intServiceId, $strServiceProductComponentTypeHandle, $arrStatuses)
    {
        // hmm, I wonder if component statuses should be taken into consideration here for some products?

        if (is_array($arrStatuses) && !empty($arrStatuses)) {

            $intComponentId = CProduct::getComponentIDByServiceID($intServiceId, $strServiceProductComponentTypeHandle, $arrStatuses);

        } else {

            $intComponentId = CProduct::getComponentIDByServiceID($intServiceId, $strServiceProductComponentTypeHandle);
        }

        // did the previous call fail?

        if (FALSE === $intComponentId) {
            return FALSE;
        }

        if (0 == $intComponentId) {

            // this isn't an error, simply means they don't have that particular feature
            return array();
        }

        // attempt to create the product

        $objProduct = CProduct::createInstance($intComponentId);

        if (!is_object($objProduct)) {

            // returning false here to actually signal an error. We have a component ID
            // but no product.. definately a bit iffy :-)
            return FALSE;
        }

        if (method_exists($objProduct, 'getFullName')) {

            // getFullName() is in derived class, (should maybe be in CProduct and overridden if required)
            // and is correct method to use

            $strProductName = $objProduct->getFullName();

        } else {

            // meh, getComponentName() is in CComponent base class. Closest match until stuff is refactored

            $strProductName = $objProduct->getComponentName();
        }

        // *all* product components have a Subscription aspect associated with them, therefore
        // lets get that information

        $intSubscriptionInstanceId = CProductComponent::getProductComponentInstanceIDOfComponentAndHandle($intComponentId, 'SUBSCRIPTION');

        if ($intSubscriptionInstanceId > 0) {

            $objSubscription = CProductComponent::createInstance($intSubscriptionInstanceId);

            $arrTariffDetails = CProductComponent::getTariffDetails($objSubscription->getTariffID());

            $floSubscriptionCost = $arrTariffDetails['intCostIncVatPence'] / 100;
        }

        // attempt to access a summarised version of any info this product wants to say about itself

        if (method_exists($objProduct, 'getExtendedSummaryInfo')) {

            $arrExtendedInfo = $objProduct->getExtendedSummaryInfo();

            // $arrExtendedInfo will now either be:

            // array(...) of data on success
            // an empty array on success (if there is no data of interest)
            // FALSE in the case of failing to access any data. (product wants to tell us about the data but it can't due to error)

        } else {

            // empty array = no extra info to tell

            $arrExtendedInfo = array();
        }


        // return back our data in a nice managable form
        return array(
            'common' => array(
                'intComponentId'      => $intComponentId,
                'strProductName'      => $strProductName,
                'strPaymentFrequency' => isset($arrTariffDetails['strPaymentFrequencyHandle']) ? $arrTariffDetails['strPaymentFrequencyHandle'] : '',
                'floSubscriptionCost' => isset($floSubscriptionCost) ? $floSubscriptionCost : 'N/A'
            ),
            'extended' => $arrExtendedInfo
        );
    }



    /**
     * Similar to getProductByProductTypeHandle, except it takes into consideration
     * the component status
     *
     * @param string $strProduct - handle of product (WLR, PLUSTALK, INTERNET_CONNECTION, etc)
     * @param integer $intServiceId - service ID
     * @param array $arrStatuses - array of component statuses
     *
     * @return FALSE = error, or CProduct-based object on success
     */

    public static function getByServiceIdAndStatus($strProduct, $intServiceId, $arrStatuses)
    {
        $bolFalse = FALSE;

        $intComponentId = CProduct::getComponentIDByServiceID($intServiceId, $strProduct, $arrStatuses);


        // component ID 0 or FALSE both a problem

        if (0 == $intComponentId) {
            return $bolFalse;
        }


        $objProduct =  CProduct::createInstance($intComponentId);

        if (!is_object($objProduct)) {
            return $bolFalse;
        }

        return $objProduct;
    }

    public function getTarriffByServiceComponent($intServiceComponentID, $strProductComponentHandle, $strContractLengthHandle)
    {
        $dbhConn = get_named_connection_with_db('dbProductComponents');

        $strQuery = "SELECT intCostIncVatPence/100  as intCost
                       FROM dbProductComponents.tblTariff t
                 INNER JOIN dbProductComponents.tblProductComponentConfig pcc
                         ON pcc.intProductComponentConfigID = t.intProductComponentConfigID
                 INNER JOIN products.tblServiceComponentProduct scp
                         ON scp.intServiceComponentProductID = pcc.intServiceComponentProductID
                 INNER JOIN dbProductComponents.tblProductComponent pc
                         ON pcc.intProductComponentID = pc.intProductComponentID
                 INNER JOIN dbProductComponents.tblContractLength cl
                         ON cl.intContractLengthID = t.intContractLengthID
                      WHERE scp.intServiceComponentID = '$intServiceComponentID'
                        AND pc.vchHandle = '$strProductComponentHandle'
                        AND (t.dtmEnd IS NULL OR t.dtmEnd > NOW())
                        AND cl.vchHandle = '$strContractLengthHandle'";

        if (! $resResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'CProduct:getTarriffByServiceComponent', false)) {
            return false;
        }

        return  PrimitivesResultGet($resResult, 'intCost');
    }

    /**
     * Default return an initial scheduled payment. This is generally determined as
     * being a scheduled payment that covers the first month of subscription taken
     * ahead of time as a set-up fee. Because we don't yet know the period this
     * should cover we give it a start and end period of NULL. When we enable the
     * account we populate these values, and skip creating a scheduled payment for
     * that first month.
     *
     * @return CScheduledPayment|false
     */
    public function getInitialScheduledPayment()
    {
        $objScheduledPayment = false;

        $strQuery = "SELECT sp.intScheduledPaymentID
                       FROM tblScheduledPayment sp
                     INNER JOIN tblInitialContractPayment AS icp
                        ON icp.intScheduledPaymentId = sp.intScheduledPaymentID
                      WHERE sp.dteStartPeriodCovered IS NULL
                        AND sp.dteEndPeriodCovered IS NULL
                        AND icp.intServiceId = {$this->getServiceID()}
                        AND icp.intServiceComponentProductTypeId = {$this->intServiceComponentProductTypeId}";

        $db = get_named_connection_with_db('financial');

        $res = PrimitivesQueryOrExit($strQuery, $db, __METHOD__, false);

        // We have a result. Do something about it
        if (PrimitivesNumRowsGet($res) > 0) {

            $objScheduledPayment = new CProductComponentScheduledPayment(
                PrimitivesResultGet($res, 'intScheduledPaymentID')
            );
        }

        return $objScheduledPayment;
    }

    /**
     * Log a scheduled payment as being one that covers an initial contract subscription.
     *
     * Note to self : should this be handled by CProduct or CProductComponent ?
     * - CProduct because the table is working at a high-level product type level (ADSL, WLR, etc)
     * - CProductComponent because product component instances have "contracts" and scheduled payments.
     *
     * Whichever the case, it doesn't depend on any particular type of product component (e.g. ADSL and WLR)
     * and therefore doesn't belong in more specialised classes such as CWlrProduct and CInternetConnectionProduct.
     *
     * @param integer $intScheduledPaymentId
     *
     * @return boolean
     **/
    public function setTransientInitialContractPaymentFlag($intScheduledPaymentId)
    {
        if (!CScheduledPayment::isValidScheduledPaymentID($intScheduledPaymentId)) {
            return FALSE;
        }

        $dbhConn = get_named_connection_with_db('financial');

        $strQuery = "INSERT INTO
                        financial.tblInitialContractPayment
                     SET
                        intServiceId = {$this->getServiceID()},
                        intServiceComponentProductTypeId = {$this->intServiceComponentProductTypeId},
                        intScheduledPaymentId = {$intScheduledPaymentId}";

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConn, __METHOD__, FALSE);

        return ($resResult != FALSE && PrimitivesAffectedRowsGet($dbhConn) == 1);
    }



    /**
     * Removes the transient initial contract payment flag for
     * a service ID and whatever type of service (product component)
     * the instance of this object represents (ADSL, WLR, etc)
     *
     * @return boolean
     **/

    public function deleteTransientInitialContractPaymentFlag()
    {
        $dbhConn = get_named_connection_with_db('financial');

        $strQuery = "DELETE FROM
                        financial.tblInitialContractPayment
                     WHERE
                        intServiceId = {$this->getServiceID()}
                        AND intServiceComponentProductTypeId = {$this->intServiceComponentProductTypeId}";

        return PrimitivesQueryOrExit($strQuery, $dbhConn, __METHOD__, FALSE);
    }

} // class CProduct extends CComponent

/* Moving the inclusion of TICKET_ACCESS_LIBRARY to the bottom of the file to
   avoid a inclusion loop which tries to use the file before it has been loaded */

if(!defined('CUSTOMER_DETAILS')){
    require_once(TICKETS_ACCESS_LIBRARY);
}
