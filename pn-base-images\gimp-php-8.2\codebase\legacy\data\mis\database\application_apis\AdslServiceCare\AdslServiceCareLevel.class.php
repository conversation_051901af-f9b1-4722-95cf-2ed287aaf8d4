<?php

require_once '/local/data/mis/database/database_libraries/sql_primitives.inc';
require_once PRODUCT_ACCESS_LIBRARY;
require_once USERDATA_ACCESS_LIBRARY;
require_once COMPONENT_DEFINES_LIBRARY;
require_once SECURE_TRANSACTION_ACCESS_LIBRARY;
require_once DATABASE_LIBRARY_ROOT . '/components/config-enhanced-care-access.inc';
require_once 'AdslServiceCareLevelException.php';
require_once '/local/data/mis/database/application_apis/EmailHandler/EmailHandler.class.php';
require_once '/local/data/mis/audit_logging/AuditLogger.php';

use \Plusnet\InventoryEventClient\Context\CancelEnhancedCareContext;
use \Plusnet\InventoryEventClient\Context\UpdateCareLevelContext;
/**
 * AdslServiceCareLevel
 *
 * Class provides functionality to ADSL Service Care Levels
 *
 * @package
 * @version $Id$
 * @copyright PlusNet
 * <AUTHOR> <r<PERSON><PERSON><PERSON><PERSON><PERSON>@plus.net>
 */
class AdslServiceCareLevel
{
    /**
     * strBTCareLevel
     *
     * It's handle used by BT
     *
     * @var string
     * @access private
     */
    private $strBTCareLevel = 'StandardCare';

    /**
     * intComponentId
     *
     * Defines Id of an enhanced care component assigned to service id
     *
     * @var integer
     * @access private
     */
    private $intComponentId = null;

    /**
     * intComponentTypeId
     *
     * @var integer
     * @access private
     */
    private $intComponentTypeId = null;

    /**
     * strDescription
     *
     * Description of the Adsl Service Care Level
     *
     * @var string
     * @access private
     */
    private $strDescription = 'Standard Care';

    /**
     * strHandle
     *
     * Defines
     *
     * @var string
     * @access private
     */
    private $strHandle = 'StandardCare';

    /**
     * intServiceId
     *
     * @var integer
     * @access private
     */
    private $intServiceId = null;

    /**
     * intServiceType
     *
     * @var integer
     * @access private
     */
    private $intServiceType = null;

    /**
     * intAdslRegradeId
     *
     * @var integer
     * @access private
     */
    private $intAdslRegradeId = null;

    /**
     * strComponentStatus
     *
     * @var string
     * @access private
     */
    private $strComponentStatus = '';

    /**
     * strServiceStatus
     *
     * @var string
     * @access private
     */
    private $strServiceStatus = '';

    /**
     * $dtmStartDate
     *
     * @var date
     * @access private
     */
    private $dtmStartDate = '';

    /**
     * __construct
     *
     * @param integer $intServiceId
     * @access public
     * @return void
     */
    public function __construct($intServiceId)
    {
        if ( !is_numeric($intServiceId) ) {
            throw new AdslServiceCareLevelException('MISSING_SERVICE_ID', 1);
        }

        $this->populateData($intServiceId);
    }

    /**
     * Populates object data
     *
     * @access private
     * @param integer $intServiceId
     * @return boolean
     */
    private function populateData($intServiceId)
    {
        $arrDbConnection = get_named_connection_with_db('userdata');
        $this->intServiceId = mysql_real_escape_string($intServiceId);

        $strQuery = '
            SELECT intComponentId, c.vchBTCareLevel, c.vchDescription,
                c.vchHandle, c.status, c.dtmStartDate, s.status as strServiceStatus,
                c.component_type_id as intComponentTypeId, s.type as intServiceType
            FROM services s
            LEFT JOIN
                (SELECT c.service_id, c.component_id AS intComponentId, cl.vchBTCareLevel, cl.vchDescription,
                cl.vchHandle, c.status, clc.dtmStartDate, c.component_type_id
                FROM components c
                INNER JOIN tblConfigAdslServiceCareLevel clc ON c.component_id = clc.intComponentId
                INNER JOIN tblAdslServiceCareLevel cl ON clc.intAdslServiceCareLevelId = cl.intAdslServiceCareLevelId
                WHERE c.status IN ("queued-activate", "active")
            ) c on c.service_id = s.service_id
            WHERE s.service_id = '.$this->intServiceId;

        $resResult = PrimitivesQueryOrExit($strQuery, $arrDbConnection, '', false);

        if ( false == $resResult  || 0 == PrimitivesNumRowsGet($resResult)) {
            throw new AdslServiceCareLevelException('INVALID_SELECT_RESULT', 6);
        }

        if ( 1 < PrimitivesNumRowsGet($resResult) ) {
            throw new AdslServiceCareLevelException('MULTIPLE_SELECT_RESULT', 7);
        }

        // One row is expected
        $arrResult = array();
        $arrResult = PrimitivesResultsAsArrayGet( $resResult );

        $this->intServiceType = $arrResult[0]['intServiceType'];

        // No Enhanced care currently on this account
        if (!$arrResult[0]['intComponentId'])
        {
            return true;
        }

        $this->strBTCareLevel = $arrResult[0]['vchBTCareLevel'];
        $this->intComponentId = $arrResult[0]['intComponentId'];
        $this->intComponentTypeId = $arrResult[0]['intComponentTypeId'];
        $this->strComponentStatus = $arrResult[0]['status'];
        $this->strServiceStatus = $arrResult[0]['strServiceStatus'];
        $this->strDescription = $arrResult[0]['vchDescription'];
        $this->strHandle = $arrResult[0]['vchHandle'];
        $this->dtmStartDate = $arrResult[0]['dtmStartDate'];

        return true;
    }

    /**
     * getBTCareLevel
     *
     * Method returns adsl service care level handle used by BT
     *
     * @access public
     * @return string
     */
    public function getBTCareLevel()
    {
        return $this->strBTCareLevel;
    }

    /**
     * getComponentId
     *
     * It returns component id of the enhanced care component assigned to service id
     *
     * @access public
     * @return integer
     */
    public function getComponentId()
    {
        return (int) $this->intComponentId;
    }

    public function getComponentTypeId()
    {
        return (int) $this->intComponentTypeId;
    }

    /**
     * getComponentStatus
     *
     * It returns Enhanced Care component Status
     *
     * @access public
     * @return string
     */
    public function getComponentStatus()
    {
        return $this->strComponentStatus;
    }

    /**
     * getServiceStatus
     *
     * It returns Service Status
     *
     * @access public
     * @return string
     */
    public function getServiceStatus()
    {
        return $this->strServiceStatus;
    }

    /**
     * getDescription
     *
     * It returns description of Adsl Service Care Level
     *
     * @access public
     * @return string
     */
    public function getDescription()
    {
        return $this->strDescription;
    }

    /**
     * getStartDate
     *
     * @access public
     * @return date
     */
    public function getStartDate()
    {
        return $this->dtmStartDate;
    }

    /**
     * getCareLevelHandle
     *
     * @access public
     * @return string
     */
    public function getCareLevelHandle()
    {
        return $this->strHandle;
    }

    /**
     * isEnhancedCare
     *
     * EnhancedCare is a handle taken from DB.
     * If you change it in DB, change it here too.
     *
     * @param boolean $bolActive
     * @access public
     * @return boolean - true when Enhanced Care is active otherwise false
     */
    public function isEnhancedCare($bolActive = false)
    {
        if ( true === $bolActive ) {
            return ( 'EnhancedCare' == $this->strHandle && 'active' == $this->strComponentStatus );
        }

        return ( 'EnhancedCare' == $this->strHandle );
    }

    /**
     * getCharge
     *
     * Only Enhanced Care has defined charge
     * Charge for Enhanced Care component is defined in products.service_component_charges
     * There is charge only for components in active state
     *
     * @param string $strPaymentFrequency
     * @access public
     * @return float
     */
    public function getCharge($strPaymentFrequency)
    {
        // only charge for enhanced care
        if (product_service_is_allowed_component($this->intServiceId, COMPONENT_ENHANCED_CARE_INCLUSIVE)) {
            return (float) 0;
        }

        if ( 'StandardCare' == $this->strHandle ) {
            return (float) 0;
        }

        $intMultiplier = null;
        switch ( strtolower($strPaymentFrequency) )
        {
            case 'monthly':
                $intMultiplier = 1;
                break;

            case 'quarterly':
                $intMultiplier = 3;
                break;

            case 'half-yearly':
                $intMultiplier = 6;
                break;

            case 'yearly':
                $intMultiplier = 12;
                break;

            case 'never':
            default:
                return (float) 0;
        }

        $floCharge = null;
        $floCharge = product_component_monthly_charge_get(COMPONENT_ENHANCED_CARE, 'active');

        return (float) ($floCharge * $intMultiplier);
    }

    /**
     * getValidHandles - returns valid handles of Care Level
     *
     * @access private
     * @return array
     */
    private function getValidHandles()
    {
        $arrDbConnection = get_named_connection_with_db('userdata');
        $strQuery = 'SELECT vchHandle FROM tblAdslServiceCareLevel';

        $resResult = PrimitivesQueryOrExit($strQuery, $arrDbConnection, '', false);

        $arrResult = array();

        if (false != $resResult) {
            $arrResult = PrimitivesResultsAsArrayGet( $resResult );
        }

        $arrHandles = array();
        foreach ($arrResult as $arrRow) {
            array_push($arrHandles, $arrRow['vchHandle']);
        }

        return $arrHandles;
    }

    /**
     * getAdslRegradeId
     *
     * @access public
     * @return integer
     */
    public function getAdslRegradeId()
    {
        return (int) $this->intAdslRegradeId;
    }

    /**
     * requestEnhancedCare
     *
     * @param boolean $bolRegrade
     * @access public
     * @return mixed - string on failure, boolean on success
     */
    public function requestEnhancedCare($bolRegrade = false)
    {
        require_once ADSL_ACCESS_LIBRARY;

        $intComponentTypeId = self::getAvailableEnhancedCareComponentId($this->intServiceType);

        if(!$intComponentTypeId)
            return false; // We don't want to throw an exception

        $strQuery = 'SELECT component_id FROM components '.
                    "WHERE service_id = '$this->intServiceId'
                     AND status IN ('queued-activate', 'active')
                     AND component_type_id = $intComponentTypeId";

        $arrDbConnection = get_named_connection_with_db('userdata');
        $resResult = PrimitivesQueryOrExit($strQuery, $arrDbConnection, '', false);

        if ( PrimitivesNumRowsGet($resResult) > 0  ) {
            throw new AdslServiceCareLevelException('COMPONENT_EXISTS');
        }

        if ( $bolRegrade ) {

            if ( $this->hasActiveRegrade() ) {
                throw new AdslServiceCareLevelException('ACTIVE_REGRADE');
            }
        }

        $intComponentId = null;
        $intComponentId = userdata_component_add( $this->intServiceId, $intComponentTypeId,
                                                  -1, '', 'unconfigured');

        if ( !is_numeric($intComponentId) || is_null($intComponentId) ) {
            throw new AdslServiceCareLevelException('COMPONENT_NOT_CREATED', 2);
        }

        $this->intComponentId = (int) $intComponentId;

        configure_enhanced_care_component($this->intComponentId, 'auto_configure');

        if ( $bolRegrade ) {
            return $this->initiateRegrade('request');
        } else {
        // This is signup, so send the email
            $this->sendEmail('signup');
        }
        return true;
    }

    /** getAvailableEnhancedCareComponentId - return id of the Enhanced Care component
     * that is available for this product. BPR addition.
     *
     * @param intServiceType
     * @return int
     */
    public static function getAvailableEnhancedCareComponentId($intServiceType)
    {

        if (!is_numeric($intServiceType) || 0>=$intServiceType)
            return null;

        $arrComponents = product_get_components('name', $intServiceType);

        foreach($arrComponents as $arrComponent)
        {
            if (false !== strpos($arrComponent['name'], 'Enhanced Care'))
            {
                return $arrComponent['service_component_id'];
            }
        }

        return null;
    }

    /**
     * cancelEnhancedCare
     *
     * @param boolean $bolRegrade
     * @access public
     * @return boolean
     */
    public function cancelEnhancedCare($bolRegrade = false)
    {
        AuditLogger::functionEntry(__METHOD__);

        global $global_component_configurators;
        require_once ADSL_ACCESS_LIBRARY;

        if ( $bolRegrade ) {

            if ( $this->hasActiveRegrade() ) {
                AuditLogger::functionExit(__METHOD__);
                throw new AdslServiceCareLevelException('ACTIVE_REGRADE');
            }
        }

        if ( $this->hasPendingRegrade() ) {

            $arrRegradeDetails = GetActiveRegradeDetails($this->intServiceId);
            // 68 = BOT regrades team

            $strBody = 'Customer has chosen to withdraw their request for Enhanced Care, however there is an outstanding regrade!';

            tickets_ticket_contacts_add($arrRegradeDetails['intTicketID'], 0, $strBody, 0, 0, 68, 'open', $GLOBALS['my_id']);
        }

        $strConfigurator = $global_component_configurators[$this->getComponentTypeId()];

        if ($strConfigurator != '' && function_exists($strConfigurator)) {

            /** @var \Plusnet\InventoryEventClient\Service\EventService $eventService */
            $eventService = BusTier_BusTier::getClient('inventoryEventService');
            $eventService->takePreChangeSnapshot($this->intServiceId, new CancelEnhancedCareContext());

            $strConfigurator($this->getComponentId(), 'auto_destroy');

            $eventService->takePostChangeSnapshot($this->intServiceId);

        }

        AuditLogger::functionExit(__METHOD__);

        if ( $bolRegrade ) {
            return $this->initiateRegrade('cancel');
        }
        return true;
    }

    /**
     * hasActiveRegrade - checks whether there is active regrade order
     *
     * @access private
     * @return boolean
     */
    public function hasActiveRegrade()
    {
        $arrActiveRegrade = array();
        $arrActiveRegrade = GetActiveRegradeDetails($this->intServiceId);

        if ( count($arrActiveRegrade) > 0 ) {

            $arrStatuses = array('AWAITING SUBMISSION','REGRADE_ORDER_SUBMISSION_ERROR');
            if(!in_array($arrActiveRegrade['vchDisplayName'], $arrStatuses) ) {
                return true;
            }
        }
        return false;
    }

    private function hasPendingRegrade()
    {
        $arrActiveRegrade = array();
        $arrActiveRegrade = GetActiveRegradeDetails($this->intServiceId);

        if ( count($arrActiveRegrade) > 0 ) {

            if('AWAITING SUBMISSION'==$arrActiveRegrade['vchDisplayName']) {
                return true;
            }
        }
        return false;
    }

    /**
     * initiateRegrade
     *
     * @param string $strRegradeReason
     * @access private
     * @return mixed - string on failure, boolean on success
     */
    private function initiateRegrade($strRegradeReason)
    {

        require_once DATABASE_LIBRARY_ROOT . '/regrade-access.inc';
        require_once DATABASE_LIBRARY_ROOT . '/cbc-access.inc';
        require_once DATABASE_LIBRARY_ROOT . '/tickets-access.inc';

        $arrConnection = get_named_connection_with_db('userdata');

        $arrValidReasons = array('request', 'cancel');
        if ( !in_array($strRegradeReason, $arrValidReasons) ) {
            throw new AdslServiceCareLevelException('INVALID_REGRADE_REASON', 3);
        }

        if(!$this->hasPendingRegrade()) {
            $strTicketText = $this->getEmailTemplateText($strRegradeReason);

            //Add the ticket
            $intTicketId = tickets_ticket_add('Script', $this->intServiceId, 0, 0, 'Open',
                                          $GLOBALS['my_id'], $strTicketText, 0,
                                          PHPLibTeamIdGetByName('BOT - DSL Regrades (Automated)'));

            //get the last contact id for the ticket
            $strQuery = "SELECT MAX(contact_id) AS intContactId FROM tickets.ticket_contacts
                         WHERE ticket_id = '$intTicketId'";

            $resResult = PrimitivesQueryOrExit($strQuery, $arrConnection, '', false);

            if ( false == $resResult ) {
                throw new AdslServiceCareLevelException('INVALID_TICKET_CONTACT', 4);
            }

            $intContactId = PrimitivesResultGet($resResult, 'intContactId');

            //workout the release date or get the pending regrade release date
            $dtmReleaseDate = date('Y-m-d', ( time() + (5*24*60*60) ) ) . date(' H:i:s');

            $strActioner = !empty($GLOBALS['my_id']) ? $GLOBALS['my_id'] : SCRIPT_USER ;

            tickets_ticket_snooze($intContactId, $strActioner,
                                  PHPLibTeamIdGetByName('BOT - DSL Regrades (Automated)'),
                                  $dtmReleaseDate);

            $arrServiceData = userdata_service_get($this->intServiceId);
            $this->intAdslRegradeId = initiateAutomatedRegrade($this->intServiceId,
                                                               $arrServiceData['type'],
                                                               $arrServiceData['type'], 0, 0,
                                                               $intTicketId, false,
                                                               '', false, '', '', 'normalRegrade',
                                                               0, 'NULL', false);

            if ( !is_numeric($this->intAdslRegradeId) ) {
                throw new AdslServiceCareLevelException('REGRADE_NOT_CREATED', 5);
            }
        }// if(!$this->hasPendingRegrade())

        // send the emails
        $this->sendEmail($strRegradeReason);
        return true;
    }

    /**
     * updateServiceCareLevelComplete
     *
     * completes an updateServiceCareLevel call if the original call returned a redirect url to the thistle payment system.
     * @param GenericImmediatePaymentApplication_PaymentResponseData $paymentResponseData
     * @return boolean
     */
    public function updateServiceCareLevelComplete($paymentResponseData)
    {
        AuditLogger::functionEntry(__METHOD__);

        /** @var \Plusnet\InventoryEventClient\Service\EventService $eventService */
        $eventService = BusTier_BusTier::getClient('inventoryEventService');
        $eventService->takePreChangeSnapshot($this->intServiceId, new UpdateCareLevelContext());

        $result = enhanced_care_auto_enable_complete($paymentResponseData);

        if ($result) {
            $this->strComponentStatus = 'active';
            $eventService->takePostChangeSnapshot($this->intServiceId);
        }

        AuditLogger::functionExit(__METHOD__);

        return $result;
    }

    /**
     * updateServiceCareLevel
     *
     * @access public
     * @param string $strCareLevel, valid if in array('StandardCare', 'Broadband Enhanced Care');
     * @param GenericImmediatePaymentApplication_PaymentResponseData $paymentResponseData
     * @return void|bool|string
     */
    public function updateServiceCareLevel($strCareLevel = null , $paymentRequestData = null)
    {
        if (is_null($strCareLevel)) {
            $strCareLevel = $this->strBTCareLevel;
        }

        // Make Enhanced Care library aware of new WBC service levels
        // Maintenance Category 5 corresponds to Standard Care
        // Maintenance Category 4 corresponds to Enhanced Care
        $wbcServiceLevelMap = array(
            'Maintenance Category 5' => 'StandardCare',
            'Maintenance Category 4' => 'Broadband Enhanced Care',
        );

        if (isset($wbcServiceLevelMap[$strCareLevel]))
            $strCareLevel = $wbcServiceLevelMap[$strCareLevel];

        $strAction = null;
        $strStatus = $this->strComponentStatus;
        $intComponentId = $this->intComponentId;

        if ( empty($strStatus) || empty($intComponentId) ) {

            $strQuery = 'SELECT component_id, status FROM components
                         WHERE status IN ("queued-activate", "active", "queued-destroy") '.
                        "AND service_id = '$this->intServiceId'
                         AND component_type_id IN ('" . COMPONENT_ENHANCED_CARE . "', '" . COMPONENT_ENHANCED_CARE_INCLUSIVE. "')";

            $arrDbConnection = get_named_connection_with_db('userdata');
            $resResult = PrimitivesQueryOrExit($strQuery, $arrDbConnection, '', false);

            if ( false == $resResult || PrimitivesNumRowsGet($resResult) == 0 ) {
                return false;
            }

            $arrResult = array();
            $arrResult = PrimitivesResultsAsArrayGet( $resResult );

            $intComponentId = $arrResult[0]['component_id'];
            $strStatus = $arrResult[0]['status'];
        }

        // component is awaiting to be destroyed, BT changed care level back to Standard so destroy component
        if ( 'StandardCare' == $strCareLevel && 'queued-destroy' == $strStatus ) {
            $strAction = 'auto_destroy';
        }
        // component has been added to service and is awaiting to be activate, BT set care level to enhanced
        // so enable component. NB: When sending care level to BT, they expect 'Broadband Enhanced Care', and
        // when they respond with care level, they return 'TotalCare'. In the name of consistency :)
        elseif (
            in_array(
                $strCareLevel,
                array('Broadband Enhanced Care', 'TotalCare')
            ) && 'queued-activate' == $strStatus ) {
            $strAction = 'auto_enable';
        }
        else {
            // no changes are required, leave component as it is
            return;
        }

        //return configure_enhanced_care_component($intComponentId, $strAction);
        try {
            return $returnValue = configure_enhanced_care_component($intComponentId, $strAction, $paymentRequestData);
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Get email template text
     *
     * @param string $strHandle
     * @return string
     */
    private function getEmailTemplateText($strHandle)
    {
        switch ( $strHandle ) {
            case 'request':
                $strFileName = 'Request.tpl';
                break;
            case 'cancel':
                $strFileName = 'Cancel.tpl';
                break;
            case 'signup':
                $strFileName = 'Signup.tpl';
        }

        $strPath = '/local/data/mis/database/application_apis/AdslServiceCare/templates/';

        if ( !file_exists($strPath . $strFileName) ) {
            throw new AdslServiceCareLevelException('MISSING_TICKET_TEMPLATE', 8);
        }

        $arrStandardVars = mailer_send_customer_mail($this->intServiceId, $strPath.$strFileName, array(), false, false, true);

        $arrContent = mailer_parse_template($strPath.$strFileName, $arrStandardVars);

        if(false == $arrContent) {
            throw new AdslServiceCareLevelException('MISSING_TICKET_TEMPLATE', 8);
        }

        return $arrContent['strTemplate'];
    }

    /**
     * Send email
     *
     * @param string $strHandle the handle of the required email template
     *
     * @return void
     */
    private function sendEmail($strHandle)
    {
        // Content for signup journey emails is identical as of BCD-1150.
        // Passing 'customerStatus' (new or existing) to the template in
        // case we want to differentiate at a later point.

        $arrData = array();

        switch ($strHandle) {
            case 'request':
                $emailTemplate = 'enhanced-care-signup-business';
                $arrData = array('customerStatus' => 'existing');
                break;
            case 'cancel':
                $emailTemplate = 'enhanced-care-cancelllation-request-business';
                break;
            case 'signup':
                $emailTemplate = 'enhanced-care-signup-business';
                $arrData = array('customerStatus' => 'new');
                break;
        }

        EmailHandler::sendEmail($this->intServiceId, $emailTemplate, $arrData);
    }
}
