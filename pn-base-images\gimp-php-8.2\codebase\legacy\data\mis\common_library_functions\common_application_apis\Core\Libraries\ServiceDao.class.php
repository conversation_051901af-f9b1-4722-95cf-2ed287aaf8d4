<?php
	/**
	 * <p>Core_ServiceDao</p>
	 *
	 * Data Access Object operating on <i>userdata.services</i> table
	 *
	 * @see Core_Service
	 *
	 * @see getServiceDao
	 * @see insertServiceDao
	 * @see updateServiceDao
	 * @see deleteServiceDao
	 *
	 * @package	Core
	 *
	 * <AUTHOR> <<EMAIL>>
	 *
	 * @version	$Id: ServiceDao.class.php,v 1.4 2008-07-15 02:00:51 rmerewood Exp $
	 */
	class Core_ServiceDao extends Db_Object implements Core_IServiceDao
	{
		/**
		 * @access protected
		 * @var string Primary key name
		 */
		protected $strPrimaryKey = 'service_id';

		protected $service_id = null;

		protected $user_id = null;

		protected $isp = '';

		protected $username = '';

		protected $password = '';

		protected $cli_number = '';

		protected $type = null;

		protected $status = '';

		protected $enddate = '';

		protected $next_invoice = '';

		protected $invoice_period = '';

		protected $next_invoice_warned = '';

		protected $invoice_day = null;

		protected $authorised_switch_payment = '';

		protected $bolMailOptOut = null;

		/**
		 * Enter description here...
		 *
		 * @var unknown_type
		 */
		protected $arrSetterFunctions = array('service_id'                => 'ServiceId',
		                                      'user_id'                   => 'UserId',
		                                      'isp'                       => 'Isp',
		                                      'username'                  => 'Username',
		                                      'password'                  => 'Password',
		                                      'cli_number'                => 'CliNumber',
		                                      'type'                      => 'Type',
		                                      'status'                    => 'Status',
		                                      'enddate'                   => 'EndDate',
		                                      'next_invoice'              => 'NextInvoice',
		                                      'invoice_period'            => 'InvoicePeriod',
		                                      'next_invoice_warned'       => 'NextInvoiceWarned',
		                                      'invoice_day'               => 'InvoiceDay',
		                                      'authorised_switch_payment' => 'AuthorisedSwitchPayment',
		                                      'bolMailOptOut'             => 'MailOptOut');

		/**
		 * Enter description here...
		 *
		 */
		public function init() {}

		/**
		 * Fetch a single row from <i>userdata.services</i> table
		 *
		 * @access public
		 * @static
		 *
		 * @uses Db_Object::getObject()
		 *
		 * <AUTHOR> Marek" <<EMAIL>>
		 *
		 * @param int    $intServiceId
		 * @param string $strTransaction
		 *
		 * @return Core_ServiceDao
		 */
		public static function get($intServiceId, $strTransaction = Db_Manager::DEFAULT_TRANSACTION)
		{
			$strDebugSource = __CLASS__ . ' ' . __METHOD__;
			Dbg_Dbg::write("$strDebugSource: Fetching service details for Id $intServiceId", 'Core');

			return parent::getObject(get_class(), $intServiceId, $strTransaction);
		} // public static function get($intServiceId, $strTransaction = Db_Manager::DEFAULT_TRANSACTION)

		/**
		 * Returns service ID
		 *
		 * @access public
		 *
		 * <AUTHOR> Marek" <<EMAIL>>
		 *
		 * @return int
		 */
		public function getServiceId()
		{
			return $this->service_id;
		} // public function getServiceId()

		/**
		 * Returns user ID
		 *
		 * @access public
		 *
		 * <AUTHOR> Marek" <<EMAIL>>
		 *
		 * @return int
		 */
		public function getUserId()
		{
			return $this->user_id;
		} // public function getUserId()


		/**
		 * Returns isp
		 *
		 * @access public
		 * @return string
		 */
		public function getIsp()
		{
			return $this->isp;
		}

		/**
		 * Returns username
		 *
		 * @access public
		 *
		 * <AUTHOR> Marek" <<EMAIL>>
		 *
		 * @return string
		 */
		public function getUsername()
		{
			return $this->username;
		} // public function getUsername()

		/**
		 * Returns password
		 *
		 * @access public
		 *
		 * <AUTHOR> Marek" <<EMAIL>>
		 *
		 * @return string
		 */
		public function getPassword()
		{
			// not sure if there's anything that depends on this...?
			return $this->username;
		} // public function getPassword()

		/**
		 * Return the encrypted password
		 *
		 * @return string
		 */
		public function getEncryptedPassword()
		{
			return $this->password;
		}

		/**
		 * Returns CLI number
		 *
		 * @access public
		 *
		 * <AUTHOR> Marek" <<EMAIL>>
		 *
		 * @return string
		 */
		public function getCliNumber()
		{
			return $this->cli_number;
		} // public function getCliNumber()

		/**
		 * Returns product type
		 *
		 * @access public
		 *
		 * <AUTHOR> Marek" <<EMAIL>>
		 *
		 * @return int
		 */
		public function getType()
		{
			return $this->type;
		} // public function getType()

		/**
		 * Returns customer's service definitions ID
		 *
		 * @access public
		 *
		 * <AUTHOR> Marek" <<EMAIL>>
		 *
		 * @see Core_ServiceDao::getType()
		 *
		 * @return int
		 */
		public function getServiceDefinitionId()
		{
			return $this->getType();
		} // public function getServiceDefinitionId()

		/**
		 * Returns status
		 *
		 * @access public
		 *
		 * <AUTHOR> Marek" <<EMAIL>>
		 *
		 * @return int
		 */
		public function getStatus()
		{
			return $this->status;
		} // public function getStatus()

		/**
		 * Returns end date
		 *
		 * @access public
		 *
		 * <AUTHOR> Marek" <<EMAIL>>
		 *
		 * @return I18n_Date
		 */
		public function getEndDate()
		{
			if (!empty($this->enddate) && !($this->enddate instanceof I18n_Date))
			{
				return I18n_Date::fromString($this->enddate);
			}

			return $this->enddate;
		} // public function getEndDate()

		/**
		 * Returns next invoice date
		 *
		 * @access public
		 *
		 * <AUTHOR> Marek" <<EMAIL>>
		 *
		 * @return I18n_Date
		 */
		public function getNextInvoiceDate()
		{
			if (!empty($this->next_invoice) && !($this->next_invoice instanceof I18n_Date))
			{
				return I18n_Date::fromString($this->next_invoice);
			}

			return $this->next_invoice;
		} // public function getNextInvoiceDate()

		/**
		 * Returns invoice period (ie. monthly, quarterly, yearly, never, half-yearly)
		 *
		 * @access public
		 *
		 * <AUTHOR> Marek" <<EMAIL>>
		 *
		 * @return string
		 */
		public function getInvoicePeriod()
		{
			return $this->invoice_period;
		} // public function getInvoicePeriod()

		/**
		 * Returns next invoice warned (yes, no)
		 *
		 * @access public
		 *
		 * <AUTHOR> Marek" <<EMAIL>>
		 *
		 * @return string
		 */
		public function getNextInvoiceWarned()
		{
			return $this->next_invoice_warned;
		} // public function getNextInvoiceWarned()

		/**
		 * Returns invoice day
		 *
		 * @access public
		 *
		 * <AUTHOR> Marek" <<EMAIL>>
		 *
		 * @return int
		 */
		public function getInvoiceDay()
		{
			return $this->invoice_day;
		} // public function getInvoiceDay()

		/**
		 * Returns authorised_switch_payment (yes, no, immediate)
		 *
		 * @access public
		 *
		 * <AUTHOR> Marek" <<EMAIL>>
		 *
		 * @return int
		 */
		public function getAuthorisedSwitchPayment()
		{
			return $this->authorised_switch_payment;
		} // public function getAuthorisedSwitchPayment()

		/**
		 * Returns mail opt-out flag
		 *
		 * @access public
		 *
		 * <AUTHOR> Marek" <<EMAIL>>
		 *
		 * @return int
		 */
		public function getMailOptOut()
		{
			return $this->bolMailOptOut;
		} // public function getMailOptOut()

		/**
		 * Sets service ID
		 *
		 * @access public
		 *
		 * <AUTHOR> Marek" <<EMAIL>>
		 *
		 * @param int $intServiceId
		 */
		public function setServiceId($intServiceId)
		{
			$this->service_id = $intServiceId;
		} // public function setServiceId($intServiceId)

		/**
		 * Sets user ID
		 *
		 * @access public
		 *
		 * <AUTHOR> Marek" <<EMAIL>>
		 *
		 * @param int $intUserId
		 */
		public function setUserId($intUserId)
		{
			$this->user_id = $intUserId;
		} // public function setUserId($intUserId)

		/**
		 * Sets isp
		 *
		 * @param string $strIsp
		 * @access public
		 */
		public function  setIsp($strIsp)
		{
			$this->isp = $strIsp;
		}

		/**
		 * Sets username
		 *
		 * @access public
		 *
		 * <AUTHOR> Marek" <<EMAIL>>
		 *
		 * @param string $strUsername
		 */
		public function setUsername($strUsername)
		{
			$this->username = $strUsername;
		} // public function setUsername($strUsername)

		/**
		 * Sets password
		 *
		 * @access public
		 *
		 * <AUTHOR> Marek" <<EMAIL>>
		 *
		 * @param string $strPassword
		 */
		public function setPassword($strPassword)
		{
			$this->password = $strPassword;
		} // public function setPassword($strPassword)

		/**
		 * Sets CLI number
		 *
		 * @access public
		 *
		 * <AUTHOR> Marek" <<EMAIL>>
		 *
		 * @param string $strCliNumber
		 */
		public function setCliNumber($strCliNumber)
		{
			$this->cli_number = $strCliNumber;
		} // public function setCliNumber($strCliNumber)

		/**
		 * Sets type (service defintion ID)
		 *
		 * @access public
		 *
		 * <AUTHOR> Marek" <<EMAIL>>
		 *
		 * @param int $intType
		 */
		public function setType($intType)
		{
			$this->type = $intType;
		} // public function setType($intType)

		/**
		 * Sets status
		 *
		 * @access public
		 *
		 * <AUTHOR> Marek" <<EMAIL>>
		 *
		 * @param string $strStatus
		 */
		public function setStatus($strStatus)
		{
			$this->status = $strStatus;
		} // public function setStatus($strStatus)

		/**
		 * Sets end date
		 *
		 * @access public
		 *
		 * <AUTHOR> Marek" <<EMAIL>>
		 *
		 * @param string $strDate
		 */
		public function setEndDate($strDate)
		{
			$this->enddate = $strDate;
		} // public function setEndDate($strDate)

		/**
		 * Sets next invoice date
		 *
		 * @access public
		 *
		 * <AUTHOR> Marek" <<EMAIL>>
		 *
		 * @param string $strDate
		 */
		public function setNextInvoice($strDate)
		{
			$this->next_invoice = $strDate;
		} // public function setNextInvoice($strDate)

		/**
		 * Sets invoice period
		 *
		 * @access public
		 *
		 * <AUTHOR> Marek" <<EMAIL>>
		 *
		 * @param string $strPeriod
		 */
		public function setInvoicePeriod($strPeriod)
		{
			$this->invoice_period = $strPeriod;
		} // public function setInvoicePeriod($strPeriod)

		/**
		 * Sets next invoice warned
		 *
		 * @access public
		 *
		 * <AUTHOR> Marek" <<EMAIL>>
		 *
		 * @param string $strNextInvoiceWarned
		 */
		public function setNextInvoiceWarned($strNextInvoiceWarned)
		{
			$this->next_invoice_warned = $strNextInvoiceWarned;
		} // public function setNextInvoiceWarned($strNextInvoiceWarned)

		/**
		 * Sets invoice day
		 *
		 * @access public
		 *
		 * <AUTHOR> Marek" <<EMAIL>>
		 *
		 * @param int $intDay
		 */
		public function setInvoiceDay($intDay)
		{
			$this->invoice_day = $intDay;
		} // public function setInvoiceDay($intDay)

		/**
		 * Sets authorised_switch_payment
		 *
		 * @access public
		 *
		 * <AUTHOR> Marek" <<EMAIL>>
		 *
		 * @param string $strAuthorisedSwitchPayment
		 */
		public function setAuthorisedSwitchPayment($strAuthorisedSwitchPayment)
		{
			$this->authorised_switch_payment = $strAuthorisedSwitchPayment;
		} // public function setAuthorisedSwitchPayment($strAuthorisedSwitchPayment)

		/**
		 * Sets mail opt-out flag
		 *
		 * @access public
		 *
		 * <AUTHOR> Marek" <<EMAIL>>
		 *
		 * @param int $intMailOptOut
		 */
		public function setMailOptOut($intMailOptOut)
		{
			$this->bolMailOptOut = $intMailOptOut;
		} // public function setMailOptOut($intMailOptOut)

	} // class Core_ServiceDao extends Db_Object implements Core_IServiceDao

?>
