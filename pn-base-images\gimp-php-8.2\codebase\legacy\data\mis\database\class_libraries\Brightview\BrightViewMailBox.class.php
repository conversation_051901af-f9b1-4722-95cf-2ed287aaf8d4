<?php
include_once '/local/data/mis/database/class_libraries/Brightview/BrightViewMail.class.php';
require_once '/local/data/mis/database/crypt_config.inc';

class BrightViewMailBox extends BrightViewPasswd
{
	private $intMultiPopId;
	protected $intMailId;
	private $strDomain;
	private $strAlias;
	private $strExternal;
	private $strStatus;
	private $intCatchAllMultiPopId;
	
	const STATUS_ACTIVE      = 'active';
	const STATUS_DISABLED    = 'disabled';
	const STATUS_EXTERNAL    = 'external';
	const STATUS_EXTERNALOFF = 'externaloff';
	
	private function __construct(){}
	
	/**
	 * method fromResult
	 * Returns new  BrightViewMailBox object based on a passed array
	 *
	 * @param array $arr
	 * @return BrightViewMailBox
	 */
	public static function fromResult($arr)
	{
		$ret = new BrightViewMailBox();
		$ret->setVars($arr);
		$ret->setMultiPopId($arr['multipopid']);
		$ret->setMailId($arr['mailid']);
		$ret->setDomain($arr['domain']);
		$ret->setAlias($arr['alias']); //virtual user
		$ret->setExternal($arr['external']);
		$ret->setStatus($arr['status']);
		$ret->checkForCatchAll();
		return $ret;
	}

	/**
	 * function fromMultiPopId
	 * 
	 * Returns mailbox object based on a primary key
	 *
	 * @param int $intMultiPopId
	 * @return object
	 */
	public static function fromMultiPopId($intMultiPopId)
	{
		$stmt = BVDB::db()->prepare('SELECT * FROM passwd p INNER JOIN multipop m ON (p.mailid = m.mailid) WHERE multipopid = ?');
		$stmt->execute(array($intMultiPopId));
		$arrRes = $stmt->fetchAll(PDO::FETCH_ASSOC);
		if (1 != count($arrRes))
		{
			throw new Exception('Incorrect number of mailboxes');
		}
		$obj = self::fromResult($arrRes[0]);
		return $obj;
	}

	/**
	 * function fromAliasAndDomain
	 * 
	 * Function to return mailbox object based on a mailboxname and domain
	 *
	 * @param string $strAlias
	 * @param string $strDomain
	 * @return object
	 */
	public static function fromAliasAndDomain($strAlias, $strDomain)
	{
		$stmt = BVDB::db()->prepare('SELECT * FROM passwd p INNER JOIN multipop m ON (p.mailid = m.mailid) WHERE alias = ? AND domain = ?');
		$stmt->execute(array($strAlias, $strDomain));
		$arrRes = $stmt->fetchAll(PDO::FETCH_ASSOC);
		if (1 != count($arrRes))
		{
			throw new Exception('Incorrect number of mailboxes');
		}
		$obj = self::fromResult($arrRes[0]);
		return $obj;
	}

	private function checkForCatchAll()
	{
		$stmt = BVDB::db()->prepare('SELECT multipopid FROM multipop WHERE mailid = ? AND alias = "*"');
		$stmt->execute(array($this->intMailId));		
		$this->setCatchAllMultiPopId($stmt->fetchColumn());
	}

	public function getMultiPopId(){return $this->intMultiPopId;}
	
	public function setMultiPopId($intMultiPopId){$this->intMultiPopId = $intMultiPopId;}

	
 	public function setCatchAllMultiPopId($intCatchAllMuliPopId){$this->intCatchAllMuliPopId = $intCatchAllMuliPopId;}
	public function setDomain($strDomain) {$this->strDomain = $strDomain;}
	public function getDomain() {return $this->strDomain;}
	public function setAlias($strAlias) {$this->strAlias = $strAlias;}
	public function getAlias() {return $this->strAlias;}
	public function setExternal($strExternal) {$this->strExternal = $strExternal;}
	public function getExternal() {return $this->strExternal;}
	public function setStatus($strStatus) {$this->strStatus = $strStatus;}
	public function getStatus() {return $this->strStatus;}
	
	public function hasCatchAll(){return !empty($this->intCatchAllMuliPopId);}
	
	public function isCatchAll(){return '*' == $this->strAlias;}
	
	/**
	 * function forwardTo
	 *
	 * sets a forward email on a mailbox 
	 * 
	 * @param string $strEmailAddress
	 * @return boolean
	 */
	public function forwardTo($strEmailAddress)
	{
		$strEmailAddress = filter_var($strEmailAddress, FILTER_VALIDATE_EMAIL);
		
		if (false === $strEmailAddress)
		{
			throw new Exception('Incorrect email address to forward to');
		}
		
		$stmt = BVDB::db()->prepare('UPDATE multipop SET external = ?, status = ? WHERE multipopid = ?');
		
		$bolSuccess = $stmt->execute(array($strEmailAddress, self::STATUS_EXTERNAL, $this->getMultiPopId()));
		
		if ($bolSuccess)
		{
			$this->setExternal($strEmailAddress);
			$this->setStatus(self::STATUS_EXTERNAL);
			return true;
		}
		return false;
	}
	
	/**
	 * function removeForward
	 *
	 * Removes a forward from the mailbox
	 * 
	 * @return boolean
	 */
	public function removeForward()
	{
		$stmt = BVDB::db()->prepare('UPDATE multipop SET external = null, status = "active" WHERE multipopid = ?');
		
		$bolSuccess = $stmt->execute(array($this->getMultiPopId()));
		
		if ($bolSuccess)
		{
			$this->setExternal(null);
			$this->setStatus(self::STATUS_ACTIVE);
			return true;
		}
		return false;	
	}
	
	/**
	 * Function change name
	 * 
	 * Will change the mailbox name 
	 *
	 * @param string $strAlias
	 * @return boolean
	 */
	public function changeName($strAlias)
	{
		$stmt = BVDB::db()->prepare('UPDATE multipop SET alias =? WHERE multipopid = ?');
		
		$bolSuccess = $stmt->execute(array($strAlias, $this->getMultiPopId()));
		
		if ($bolSuccess)
		{
			$this->setAlias($strAlias);
			return true;
		}
		return false;	
	}
	
	/**
	 * function changePassword
	 * 
	 * Will change a mailbox password 
	 *
	 * @param string $strNewPassword
	 * @return boolean
	 */
	public function changePassword($strNewPassword)
	{
		$strNewPassword = Crypt_Crypt::encrypt($strNewPassword, 'maafDes');

		$stmt = BVDB::db()->prepare('UPDATE passwd SET passwd = ? WHERE mailid = ?');
		
		$bolSuccess = $stmt->execute(array($strNewPassword, $this->getMailId()));
		
		if ($bolSuccess)
		{
			$this->setPassword($strNewPassword);
			return true;
		}
		return false;	
	}
	
	/**
	 * Method deactivates mailbox
	 * 
	 * <AUTHOR> Bargiel <<EMAIL>>
	 * 
	 * @throws Exception
	 * 
	 * @return boolean
	 */
	public function deactivate()
	{
		if ($this->getStatus() == 'active') {
			$strSQL = 'UPDATE multipop SET status="disabled" WHERE mailid = ?';
		} else if ($this->getStatus() == 'external') {
			$strSQL = 'UPDATE multipop SET status="externaloff" WHERE mailid = ?'; 
		} else {
			throw new Exception("Unexpected mailbox status {$this->getStatus()}");	
		}
		
		$objStatement = BVDB::db()->prepare($strSQL);
		$bolSuccess = $objStatement->execute(array($this->getMailId()));
		if ($bolSuccess)
		{
			$this->setStatus('disabled');
			return true;
		}
		return false;
		
	} // end of method deactivate()
	
	/**
	 * Method activates the mailbox
	 * 
	 * <AUTHOR> Bargiel <<EMAIL>>
	 * 
	 * @throws Exception
	 * 
	 * @return boolean
	 */
	public function activate()
	{
		if ($this->getStatus() == 'disabled') {
			$strSQL = 'UPDATE multipop SET status="active" WHERE mailid = ?';
		} else if ($this->getStatus() == 'externaloff') {
			$strSQL = 'UPDATE multipop SET status="external" WHERE mailid = ?'; 
		} else {
			throw new Exception("Unexpected mailbox status {$this->getStatus()}");	
		}
		$objStatement = BVDB::db()->prepare($strSQL);
		$bolSuccess = $objStatement->execute(array($this->getMailId()));
		if ($bolSuccess)
		{
			$this->setStatus('active');
			return true;
		}
		return false;
		
	} // end of method activate()
	
	/**
	 * function setToCatchAll
	 * 
	 * Will set a mailbox to catchall mailbox
	 *
	 */
	public function setToCatchAll()
	{
		
		$stmt = BVDB::db()->prepare('DELETE FROM multipop WHERE domain = ? AND alias ="*"');
		if (!$stmt->execute(array($this->getDomain()))) throw new Exception('Removing catch-all failed');
		
		
		$stmt = BVDB::db()->prepare('INSERT INTO multipop (mailid, alias, domain, status) VALUES (?,"*",?,"active")');
		if (!$stmt->execute(array($this->getMailId(), $this->getDomain()))) throw new Exception('Adding catch-all failed');
	}
}


/**
 * Examples
 */

//get mail component for username and realm
/*
$m = BrightViewMail::getMail('ianchell','freenetname.co.uk');

//iterate through mailboxes (excluding forwards)
while ($mail = $m->read())
{
	print_r($mail);
	//set a formard on a mailbox
	$mail->forwardTo('<EMAIL>');
}

//create new catchall mialbox
$m->createNewCatchAllMailBox('im-a-catchll','teme-valley.co.1uk');

//create normail mailbox
$m->createNewMailBox('im-normal-malibox','teme-valley.co.uk');

//create forward
var_dump(BrightViewMail::createNewForward('i-will-be-forwarded1','teme-valley.co.uk','<EMAIL>'));
*/
