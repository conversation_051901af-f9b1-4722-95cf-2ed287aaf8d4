<?php

	/**
	 * SOAP client for PNDesktop
	 * 
	 * <AUTHOR> for PlusNet [<EMAIL>]
	 * @copyright PlusNet 2008
	 * @package PnDesktop
	 * @see SoapClient
	 * @see BusinessTierSession
	 * @see xml_parse
	 * @see tidy_repair_string
	 * 
	 * @todo If porting to a non-PN partner, change how you access the business tier :)
	 */
	
	class PnDesktop_SoapClient
	{
		/**
		 * Constants. mostly configuration
		 */
		const PND_WSDL_LOCATION     = 'http://pndesktop.plus.net:8080/sniper-wsdl-fix/SNIPErEndPoint.wsdl';
		const PND_SOAP_CONN_TIMEOUT = 1;
		const PND_THROW_SOAPFAULTS  = true;
		const PND_DEFAULT_REALM     = '@portal.plus.net';
		const PND_DEFAULT_SENDER    = '<EMAIL>';
		const PND_DATETIME_FORMAT   = 'Y-m-d\TH:i:s\Z';
		const PND_SEND_AFTER_SECS   = 60;
		const PND_SEND_BY_SECS      = 60;
		const PND_TYPE_INFO         = 0;
		const PND_TYPE_ERROR        = 1;
		const PND_TYPE_WARNING      = 2;
		const PND_TYPE_NONE         = 3;
		
		/**
		 * Private or protected variables
		 */
		
		/**
		 * SoapClient object
		 */
		protected $objSoapClient = null;
		
		/**
		 * XmlParser object
		 */
		protected $objXmlParser  = null;
		
		/**
		 * BusinessTierSession object
		 */
		protected $objSession    = null;
		
		/**
		 * Username and realm
		 */
		protected $strUsername   = null;
		protected $strRealm      = null;

		/**
		 * The number of seconds to wait before before failing
		 * 
		 * @var int
		 * @access private
		 */
		private $intReadTimeoutSecs = 1;

		/**
		 * intDefaultSocketTimeout 
		 * 
		 * @var int
		 * @access private
		 */
		private $intDefaultSocketTimeout = 0;

		/**
		 * bolIsValid 
		 * 
		 * @var boolean
		 * @access private
		 */
		private $bolIsValid =FALSE;	

		/**
		 * Constructor and special functions
		 */
	
		/**
		 * Constructor
		 * @return void
		 */
		public function __construct($strUsername, $strRealm = self::PND_DEFAULT_REALM)
		{
			if(!class_exists('SoapClient') OR !function_exists('xml_parse') OR !class_exists('BusinessTierSession'))
			{
				return;  // no point going any further
			}
			
			try
			{
				// connection options
				$arrOptions = array(
				                    'soap_version'       => SOAP_1_2,
						    'connection_timeout' => self::PND_SOAP_CONN_TIMEOUT,
						    'exceptions'         => self::PND_THROW_SOAPFAULTS
				);
				
				// build the client
				$this->setSoapSocketTimeout();
				$this->objSoapClient = new SoapClient(self::PND_WSDL_LOCATION, $arrOptions);	
				$this->restoreDefaultSocketTimeout();
			}
			catch(SoapFault $objSoapFault)
			{
				error_log("SOAP FAULT: PnDesktop_SoapClient construction. The error will be suppressed. ".print_r($objSoapFault,1));
				$this->restoreDefaultSocketTimeout();
				return;
			}
			catch(Exception $objException)
			{
				error_log("Exception: PnDesktop_SoapClient construction. The error will be suppressed. ".print_r($objException,1));
				$this->restoreDefaultSocketTimeout();
				return;
			}
			
			try
			{
				$this->objXmlParser = xml_parser_create();
				$this->objSession = new BusinessTierSession('PLUSNET');
			}
			catch(Exception $objException)
			{
				return;
			}
			
			$this->strUsername = $strUsername;
			$this->strRealm = $strRealm;
			$this->bolIsVaild = TRUE;	
			return true;
		}
	

        /**
         * Set the timeout value
         *
         */
        private function setSoapSocketTimeout()
        {
                if($this->intReadTimeoutSecs > 0)
                {
                        $this->intDefaultSocketTimeout = ini_get('default_socket_timeout');
                        ini_set('default_socket_timeout',$this->intReadTimeoutSecs);
                }
        }


        /**
         * Restore the default timeout value
         *
         */
        private function restoreDefaultSocketTimeout()
        {
                if($this->intReadTimeoutSecs > 0)
                {
                        ini_set('default_socket_timeout',$this->intDefaultSocketTimeout);
                }
        }

	/**
	 * is a vaild object
	 *
	 * 
	 * @access public
	 */
	public function isValid()
	{
		return $this->bolIsValid;
	}

	/**
	 * String representation
	 * @return string
	 */
	public function __toString()
	{
		if($this->objSoapClient)
		{
			return get_class() . ' ' . self::PND_WSDL_LOCATION;
		}
		else
		{
			return get_class();
		}
	}
		
	/**
	 * Destructor because sometimes the client object can hang around
	 * @return void
	 */
	public function __destruct()
	{
		unset($this->objSoapClient);
		unset($this->objXmlParser);
		unset($this->objSession);
	}
		
	/**
	 * Methods
	 */

	private function validateNotificationXml($strXml)	
	{
		$objPayload = new SimpleXMLElement($strXml,LIBXML_NOWARNING);

		if('PlusNetDesktopNotification' != $objPayload->getName() || 8 != count($objPayload))
		{
                	throw new Exception("The notification must contain a single PlusNetDesktopNotification container. $strXml does not.");
		}

		$arrExpected = array('mustSendImmediate','sendAfter','description',
		                     'messageContent','title','hyperlink');

		foreach($arrExpected as $strElement)
		{
			if(!isset($objPayload->$strElement))
			{
				throw new Exception("The PlusNetDesktopNotification is missing element $strElement. XML is $strXml");
			}
		}

		return TRUE;
	}
	
	private function validateNotificationRecipient($strRecipient)
	{
		if(!preg_match('/^[\+A-Za-z0-9]+@[A-Za-z0-9\.]+$/',$strRecipient))
		{
			throw new Exception("the recipent '$strRecipient' is in the wrong format. <actorname>@<authentication realm> ");
		}
	}
	
	/**
	 * Wrapper for asynchronous SNIPERr sendNotification.  We catch the constructor and silently return by design
	 * @return void
	 * @param $strSessionId String
	 * @param $strRecipient String
	 * @param $strSender String
	 * @param $strNotificationXml String
	 */
	public function send_notification($strRecipient, $strNotificationXml, $strSender = self::PND_DEFAULT_SENDER)
	{
		try
		{
			$this->validateNotificationRecipient($strRecipient);
			$this->validateNotificationXml($strNotificationXml);
		}
		catch(Exception $objException) 
		{ 
			error_log("EXCEPTION: PnDesktop_SoapClient send validation failed. It will be suppressed recipient='$strRecipient', strNotificationXml='$strNotificationXml' sender ='$strSender' exception ".print_r($objException,1));
			return; 
		}


		if(is_object($this->objSoapClient) AND is_object($this->objSession) AND isset($this->objSession->sessionID['sessionID']) AND !empty($strNotificationXml))
		{
			try
			{
				$objSessionId = $this->objSession->sessionID;
				$this->setSoapSocketTimeout();
				$this->objSoapClient->sendNotification($objSessionId, $strRecipient, $strSender, $strNotificationXml);
				$this->restoreDefaultSocketTimeout();
			}
			catch(SoapFault $objSoapFault) 
			{ 
				/* this is not the SoapFault you are looking for.... */ 
				error_log("SOAP FAULT: PnDesktop_SoapClient. It will be suppressed ".print_r($objSoapFault,1));
				$this->restoreDefaultSocketTimeout();
				return; 
			}
			catch(Exception $objException) 
			{ 
				error_log("EXCEPTION: PnDesktop_SoapClient. It will be suppressed ".print_r($objException,1));
				$this->restoreDefaultSocketTimeout();
				return; 
			}

		}
	}
	
	/**
	 * XML payload
	 */
	const PND_SEND_NOTIFICATION_XML_PAYLOAD =
	 
	'<?xml version="1.0" encoding="utf-8" standalone="yes" ?>
	<PlusNetDesktopNotification>
		<mustSendImmediate>%s</mustSendImmediate>
		<sendAfter>%s</sendAfter>
		<sendBy>%s</sendBy>
		<description>%s</description>
		<messageContent>%s</messageContent>
		<title>%s</title>
		<type>%s</type>
		<hyperlink>%s</hyperlink>
	</PlusNetDesktopNotification>';
	
	/**
	 * XML payload
	 */
	const PND_SEND_NOTIFICATION_XML_REGX =
	 
	'^[[:space:]]*\<\?xml version="1.0"[[]]encoding="utf-8" standalone="yes" ?>
	<PlusNetDesktopNotification>
		<mustSendImmediate>%s</mustSendImmediate>
		<sendAfter>%s</sendAfter>
		<sendBy>%s</sendBy>
		<description>%s</description>
		<messageContent>%s</messageContent>
		<title>%s</title>
		<type>%s</type>
		<hyperlink>%s</hyperlink>
	</PlusNetDesktopNotification>';
		
	/**
	 * Generate an XML payload for sendNotification wrapper
	 * 
	 * @return string or NULL_EMPTY_STRING
	 * @param $bolMustSendImmediate boolean[optional]
	 * @param $dtmSendAfter timestamp[optional]
	 * @param $dtmSendBy timestamp[optional]
	 * @param $strDescription string[optional]
	 * @param $strMessageContent string
	 * @param $strTitle string
	 * @param $intType integer[optional]
	 * @param $urlHyperlink string[optional]
	 */
	
	public function generate_send_notification_xml($strMessageContent, $strTitle, $bolMustSendImmediate = true, $uxtSendAfter = null, $uxtSendBy = null, $strDescription='alert', $intType = self::PND_TYPE_INFO, $urlHyperlink = '')
	{
		// build variables
		$strMustSendImmediate = $bolMustSendImmediate ? 'true' : 'false';
		
		if(is_null($uxtSendAfter))
		{
			$uxtSendAfter = time() + self::PND_SEND_AFTER_SECS;
		}
		
		if(is_null($uxtSendBy))
		{
			$uxtSendBy = $uxtSendAfter + self::PND_SEND_BY_SECS;
		}
		
		$strSendAfter = date(self::PND_DATETIME_FORMAT, $uxtSendAfter);
		$strSendBy    = date(self::PND_DATETIME_FORMAT, $uxtSendBy);

		$strDescription = htmlentities($strDescription, ENT_QUOTES);
		$strMessageContent = htmlentities($strMessageContent, ENT_QUOTES);
		$strTitle = htmlentities($strTitle, ENT_QUOTES);
		$urlHyperlink = htmlentities($urlHyperlink, ENT_QUOTES);

		// build the string
		$strReturn = sprintf(self::PND_SEND_NOTIFICATION_XML_PAYLOAD, $strMustSendImmediate, $strSendAfter, $strSendBy, $strDescription, $strMessageContent, $strTitle, (string)$intType, $urlHyperlink);
		
		// XML output conversion/validation using tidy
		//$strReturn = tidy_repair_string($strReturn, array('output-xml' => true));

		// check with objXmlParser
		if(is_resource($this->objXmlParser) && xml_parse($this->objXmlParser, $strReturn))
		{
			return $strReturn;
		}
		else
		{
			return '';
		}
		
	}
}
