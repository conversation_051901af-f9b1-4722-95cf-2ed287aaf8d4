<?php

/**
 * Classes - CConnectionProfile.php
 * @version $Id: CConnectionProfile.php,v 1.9 2008-08-20 08:30:05 kp<PERSON><PERSON><PERSON>szewski Exp $
 * @see http://dokuwiki.internal.plus.net/
 *
 * @copyright PlusNet plc, www.plus.net
 *
 * This file is part of Classes project.
 * Generated with ArgoUML PRE-0.19.5 on 03.11.2005, 11:19:29
 *
 * <AUTHOR> <<EMAIL>>
 */

/**
 * @include CConnectionProfileGroup.php
 */

/**
 * @include C_Core_ADSLService.php
 */

/* user defined includes */
// section 127-0-0-1--58ea7bea:1074c2e49b4:-7ff4-includes begin
// section 127-0-0-1--58ea7bea:1074c2e49b4:-7ff4-includes end

/* user defined constants */
// section 127-0-0-1--58ea7bea:1074c2e49b4:-7ff4-constants begin
// section 127-0-0-1--58ea7bea:1074c2e49b4:-7ff4-constants end

/**
 * TODO: Describe class CConnectionProfile
 *
 * @access public
 * <AUTHOR> Szulc, <<EMAIL>>
 */
class CConnectionProfile
{
    // --- ATTRIBUTES ---

    /**
     * TODO: Describe attribute intId
     *
     * @access private
     * @var int
     */
    var $m_intId = 0;

    /**
     * TODO: Describe attribute strName
     *
     * @access private
     * @var string
     */
    var $m_strName = '';

    /**
     * TODO: Describe attribute strEllacoyaHandle
     *
     * @access private
     * @var string
     */
    var $m_strEllacoyaHandle = '';

    /**
     * TODO: Describe attribute intMaxDownstream
     *
     * @access private
     * @var int
     */
    var $m_intMaxDownstream = 0;

    /**
     * TODO: Describe attribute intMaxUpstream
     *
     * @access private
     * @var int
     */
    var $m_intMaxUpstream = 0;

    /**
     * TODO: Describe attribute intRGrpID
     *
     * @access private
     * @var int
     */
    var $m_intRGrpID = 0;

    /**
     * TODO: Describe attribute bolCanBeCustom
     *
     * @access private
     * @var boolean
     */
    var $m_bolCanBeCustom = false;

    /**
     * TODO: Describe attribute bolRestricted
     *
     * @access private
     * @var boolean
     */
    var $m_bolRestricted = false;

    /**
     * TODO: Describe attribute bolTimeout
     *
     * @access private
     * @var boolean
     */
    var $m_bolTimeout = false;

    /**
     * TODO: Describe attribute bolDefault
     *
     * @access private
     * @var boolean
     */
    var $m_bolDefault = false;

    /**
     * TODO: Describe attribute m_bolCapped
     *
     * @access private
     * @var boolean
     */
    var $m_bolCapped = false;

    /**
     * Date from which the connection profile is valid
     *
     * @access private
     * @var string
     */
    var $strValidFromDate = '';

    /**
     * Date on which the connection profile expires
     *
     * @access private
     * @var string
     */
    var $strValidToDate = '';
    // --- OPERATIONS ---

    /**
     * Simple helper function to retrieve/cache connection profile details
     * There's ~1000 of these, but we cache them all in a single pass as some billing processes
     * (e.g. PerformCancellations) do actually request a significant percentage of these!
     *
     * @param int $intConnectionProfileId the connection profile id
     *
     * @return array
     */
    public static function getConnectionProfile($intConnectionProfileId)
    {
        static $dataCache = array();

        $intConnectionProfileId = (int) $intConnectionProfileId;

        if (empty($dataCache)) {
            $query = <<<EOQ
SELECT
    intConnectionProfileID,
    vchName,
    vchEllacoyaHandle,
    intMaxDownstream,
    intMaxUpstream,
    intRGrpID,
    bolCanBeCustom,
    bolRestricted,
    bolTimeout,
    bolDefault,
    bolCapped,
    dtmValidFrom,
    dtmValidTo
FROM
    products.tblConnectionProfile
EOQ;

            // We use the reporting DB, as this information should be "static"
            $conn      = get_named_connection_with_db('product_reporting');
            $resResult = PrimitivesQueryOrExit($query, $conn);
            $dataCache = PrimitivesResultsAsArrayGet($resResult, 'intConnectionProfileID');
        }

        return (array_key_exists($intConnectionProfileId, $dataCache) ? $dataCache[$intConnectionProfileId] : false);
    }

    /**
     * Constructor: pulls connection profile data out of the database.  Connection profile data is cached
     *
     * @param int $intConnectionProfileId the connection profile id
     *
     * @return void
     */
    function CConnectionProfile($intConnectionProfileId)
    {
        $intConnectionProfileId = (int) $intConnectionProfileId;

        if (empty($intConnectionProfileId)) {
            return;
        }

        // Hmm.  The old code always sets m_intId, regardless of whether the profile exists or not
        // Leave as-is for now...
        $this->m_intId = $intConnectionProfileId;

        $arrConnProfile = CConnectionProfile::getConnectionProfile($intConnectionProfileId);

        if (!empty($arrConnProfile)) {
            $this->m_strName            = $arrConnProfile['vchName'];
            $this->m_strEllacoyaHandle  = $arrConnProfile['vchEllacoyaHandle'];
            $this->m_intMaxDownstream   = $arrConnProfile['intMaxDownstream'];
            $this->m_intMaxUpstream     = $arrConnProfile['intMaxUpstream'];
            $this->m_intRGrpID          = $arrConnProfile['intRGrpID'];
            $this->m_bolCanBeCustom     = $arrConnProfile['bolCanBeCustom'];
            $this->m_bolRestricted      = $arrConnProfile['bolRestricted'];
            $this->m_bolTimeout         = $arrConnProfile['bolTimeout'];
            $this->m_bolDefault         = $arrConnProfile['bolDefault'];
            $this->m_bolCapped          = $arrConnProfile['bolCapped'];
            $this->strValidFromDate     = $arrConnProfile['dtmValidFrom'];
            $this->strValidToDate       = $arrConnProfile['dtmValidTo'];
        }
    }

    /**
     * TODO: Describe method getId
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @return int
     */
    function getId()
    {
        $returnValue = (int) 0;

        // section 127-0-0-1-7f71aa59:10755d4dba0:-7ff0 begin

        $returnValue = $this->m_intId;

        // section 127-0-0-1-7f71aa59:10755d4dba0:-7ff0 end

        return $returnValue;
    }

    /**
     * TODO: Describe method getName
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @return string
     */
    function getName()
    {
        $returnValue = (string) '';

        // section 127-0-0-1--4e9e31eb:10750e38b22:-7ff0 begin

        $returnValue = $this->m_strName;

        // section 127-0-0-1--4e9e31eb:10750e38b22:-7ff0 end

        return $returnValue;
    }

    /**
     * TODO: Describe method getName
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @return string
     */
    function getEllacoyaHandle()
    {
        $returnValue = (string) '';

        // section 127-0-0-1--4e9e31eb:10750e38b22:-7ff0 begin

        $returnValue = $this->m_strEllacoyaHandle;

        // section 127-0-0-1--4e9e31eb:10750e38b22:-7ff0 end

        return $returnValue;
    }

    /**
     * TODO: Describe method getMaxDownstream
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @return int
     */
    function getMaxDownstream()
    {
        $returnValue = (int) 0;

        // section 127-0-0-1--32e11d30:10750f1cb63:-7feb begin

        $returnValue = $this->m_intMaxDownstream;

        // section 127-0-0-1--32e11d30:10750f1cb63:-7feb end

        return $returnValue;
    }

    /**
     * TODO: Describe method getMaxUpstream
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @return int
     */
    function getMaxUpstream()
    {
        $returnValue = (int) 0;

        // section 127-0-0-1--32e11d30:10750f1cb63:-7fe6 begin

        $returnValue = $this->m_intMaxUpstream;

        // section 127-0-0-1--32e11d30:10750f1cb63:-7fe6 end

        return $returnValue;
    }

    /**
     * TODO: Describe method getRgrpId
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @return int
     */
    function getRgrpId()
    {
        $returnValue = (int) 0;

        // section 127-0-0-1--32e11d30:10750f1cb63:-7fe1 begin

        $returnValue = $this->m_intRGrpID;

        // section 127-0-0-1--32e11d30:10750f1cb63:-7fe1 end

        return $returnValue;
    }

    /**
     * TODO: Describe method isCustom
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @return boolean
     */
    function isCustom()
    {
        $returnValue = (bool) false;

        // section 127-0-0-1--32e11d30:10750f1cb63:-7fdc begin

        $returnValue = $this->m_bolCanBeCustom;

        // section 127-0-0-1--32e11d30:10750f1cb63:-7fdc end

        return $returnValue;
    }

    /**
     * TODO: Describe method isCapped
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @return boolean
     */
    function isCapped()
    {
        $returnValue = (bool) false;

        // section 127-0-0-1--32e11d30:10750f1cb63:-7fdc begin

        $returnValue = $this->m_bolCapped;

        // section 127-0-0-1--32e11d30:10750f1cb63:-7fdc end

        return $returnValue;
    }

    /**
     * TODO: Describe method isRestricted
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @return boolean
     */
    function isRestricted()
    {
        $returnValue = (bool) false;

        // section 127-0-0-1--32e11d30:10750f1cb63:-7fda begin

        $returnValue = $this->m_bolRestricted;

        // section 127-0-0-1--32e11d30:10750f1cb63:-7fda end

        return $returnValue;
    }

    /**
     * TODO: Describe method isTimeout
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @return boolean
     */
    function isTimeout()
    {
        $returnValue = (bool) false;

        // section 127-0-0-1--32e11d30:10750f1cb63:-7fd8 begin

        $returnValue = $this->m_bolTimeout;

        // section 127-0-0-1--32e11d30:10750f1cb63:-7fd8 end

        return $returnValue;
    }

    /**
     * TODO: Describe method isDefault
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @return boolean
     */
    function isDefault()
    {
        $returnValue = (bool) false;

        // section 127-0-0-1--32e11d30:10750f1cb63:-7fd6 begin

        $returnValue = $this->m_bolDefault;

        // section 127-0-0-1--32e11d30:10750f1cb63:-7fd6 end

        return $returnValue;
    }

    /**
     * Returns valid from date
     *
     * @access public
     *
     * <AUTHOR> Marek <<EMAIL>>
     *
     * @return string
     */
    function getValidFromDate()
    {
        return $this->strValidFromDate;
    }

    /**
     * Returns valid to date
     *
     * @access public
     *
     * <AUTHOR> Marek <<EMAIL>>
     *
     * @return string
     */
    function getValidToDate()
    {
        return $this->strValidToDate;
    }

    /**
     * @access public
     * <AUTHOR> Zaki
     * @param intConnectionProfileID
     * @return strProfileGroupName
     */
    function & getProfileGroup()
    {
        $objConProfileGroup = null ;
        $intConnectionProfileID = $this->getId();

        if($intConnectionProfileID > 0)
        {
            $strQuery = "SELECT pg.intProfileGroupId
                           FROM tblConnectionProfile cp INNER JOIN
                                tblConnProfileGroupProfile cpgp ON
                                cp.intConnectionProfileID = cpgp.intConnectionProfileId INNER JOIN
                                tblConnProfileGroup pg ON
                                pg.intProfileGroupId = cpgp.intProfileGroupId
                          WHERE cp.intConnectionProfileID = $intConnectionProfileID";

            $dbhConnection = get_named_connection_with_db('product');
            $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

            $intProfileGroupId = PrimitivesResultGet($resResult, 'intProfileGroupId');

            if(!empty($intProfileGroupId))
            {
                $objConProfileGroup = new CConnectionProfileGroup($intProfileGroupId);
                if(is_object($objConProfileGroup))
                {
                    return $objConProfileGroup;
                }
            }
        }

        return $objConProfileGroup;
    }

    /**
     * Gets default connection profile for given customer
     *
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @access public
     *
     * @param int $intServiceID ServiceID
     * @param boolean $bolOptedOutOfTimeout (default 0)
     *
     * @return mixed CConnectionProfile object on success
     *               false on failure
     */
    function getDefaultProfileByServiceID($intServiceID, $bolOptedOutOfTimeout = 0)
    {
        if (!preg_match('/^[0-9]+$/',$intServiceID)) {
            return false;
        }

        $objConnectionProfile = false;

        $dbhConnection = get_named_connection_with_db('product');

        $strQuery = 'SELECT
                           cp.intConnectionProfileID
                     FROM
                           userdata.tblProvisionedService ps
                           INNER JOIN userdata.services s ON s.service_id = ps.intServiceID
                           INNER JOIN products.adsl_product ap ON ap.service_definition_id = s.type
                           INNER JOIN products.tblConnProfileGroupProfile cpgp ON cpgp.intSupplierProductID = ps.intSupplierProductID
                           INNER JOIN products.tblConnProfileGroup cpg ON (cpg.intProfileGroupID = cpgp.intProfileGroupID AND ap.intDefaultConnectionProfileGroupId = cpg.intProfileGroupID)
                           INNER JOIN products.tblConnectionProfile cp ON cp.intConnectionProfileID = cpgp.intConnectionProfileID
                     WHERE
                           s.service_id = '.$intServiceID.'
                           AND ps.dtmEnd IS NULL
                           AND cp.bolTimeout = '.$bolOptedOutOfTimeout.'
                           AND cp.dtmValidTo IS NULL
                           AND cpgp.bolDefault = 1';

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

        $intConnectionProfileID = PrimitivesResultGet($resResult, 'intConnectionProfileID');

        if ($intConnectionProfileID)
        {
            $objConnectionProfile = new CConnectionProfile($intConnectionProfileID);
        }
        else
        {
            $strQuery = 'SELECT
                               cp.intConnectionProfileID
                         FROM
                               products.tblConnectionProfile cp
                               INNER JOIN tblConnProfileGroupProfile cpgp ON cpgp.intConnectionProfileID = cp.intConnectionProfileID
                               INNER JOIN tblConnProfileGroup cpg ON cpg.intProfileGroupId = cpgp.intProfileGroupId
                         WHERE
                               cp.intMaxDownstream = 2000
                               AND cp.bolTimeout = 0
                               AND vchHandle = \'OTHER\'
                               AND cp.dtmValidTo IS NULL';

            $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

            $intConnectionProfileID = PrimitivesResultGet($resResult, 'intConnectionProfileID');

            if ($intConnectionProfileID)
            {
                $objConnectionProfile = new CConnectionProfile($intConnectionProfileID);
            }
        }

        return $objConnectionProfile;
    }

    /**
     * Gets connection profile for given customer with a given speed
     *
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @access public
     *
     * @param int $intServiceID ServiceID
     * @param int $intMaxDownstream given speed
     * @param boolean $bolTimeout with timeout or not
     *
     * @return mixed mixed CConnectionProfile object on success
     *               false on failure
     */
    function getProfileByServiceIDAndMaxDownstream($intServiceID, $intMaxDownstream, $bolTimeout = 0)
    {
        if (!preg_match('/^[0-9]+$/',$intServiceID) || empty($intMaxDownstream)) {
            return false;
        }

        $objConnectionProfile = false;

        $dbhConnection = get_named_connection_with_db('product');

        $strQuery = 'SELECT
                           cp.intConnectionProfileID
                     FROM
                           userdata.tblProvisionedService ps
                           INNER JOIN userdata.services s ON s.service_id = ps.intServiceID
                           INNER JOIN products.adsl_product ap ON ap.service_definition_id = s.type
                           INNER JOIN products.tblConnProfileGroupProfile cpgp ON cpgp.intSupplierProductID = ps.intSupplierProductID
                           INNER JOIN products.tblConnProfileGroup cpg ON (cpg.intProfileGroupID = cpgp.intProfileGroupID AND ap.intDefaultConnectionProfileGroupId = cpg.intProfileGroupID)
                           INNER JOIN products.tblConnectionProfile cp ON cp.intConnectionProfileID = cpgp.intConnectionProfileID
                     WHERE
                           s.service_id = '.$intServiceID.'
                           AND ps.dtmEnd IS NULL
                           AND cp.intMaxDownstream = '.$intMaxDownstream.'
                           AND cp.bolTimeout = '.$bolTimeout.'
                           AND cp.dtmValidTo IS NULL';

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

        $intConnectionProfileID = PrimitivesResultGet($resResult, 'intConnectionProfileID');

        if ($intConnectionProfileID)
        {
            $objConnectionProfile = new CConnectionProfile($intConnectionProfileID);
        }

        return $objConnectionProfile;
    }

    /**
     * Gets connection profile for given customer with a given speed
     *
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @access public
     *
     * @param int $intServiceID ServiceID
     * @param boolean $bolTimeout with timeout or not
     *
     * @return mixed mixed CConnectionProfile object on success
     *               false on failure
     */
    function getProfileByServiceID($intServiceID, $bolTimeout = 0)
    {
        if (!preg_match('/^[0-9]+$/',$intServiceID)) {
            return false;
        }

        $objConnectionProfile = false;

        $dbhConnection = get_named_connection_with_db('product');

        $strQuery = 'SELECT
                           cp.intConnectionProfileID
                     FROM
                           userdata.tblProvisionedService ps
                           INNER JOIN userdata.services s ON s.service_id = ps.intServiceID
                           INNER JOIN products.adsl_product ap ON ap.service_definition_id = s.type
                           INNER JOIN products.tblConnProfileGroupProfile cpgp ON cpgp.intSupplierProductID = ps.intSupplierProductID
                           INNER JOIN products.tblSupplierProduct sp ON sp.intSupplierProductID = cpgp.intSupplierProductID
                           INNER JOIN products.tblConnProfileGroup cpg ON (cpg.intProfileGroupID = cpgp.intProfileGroupID AND ap.intDefaultConnectionProfileGroupId = cpg.intProfileGroupID)
                           INNER JOIN products.tblConnectionProfile cp ON cp.intConnectionProfileID = cpgp.intConnectionProfileID
                     WHERE
                           s.service_id = '.$intServiceID.'
                           AND ps.dtmEnd IS NULL
                           AND cp.intMaxDownstream = sp.intRealMaxDownstream
                           AND cp.bolTimeout = '.$bolTimeout.'
                           AND cp.dtmValidTo IS NULL';

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

        $intConnectionProfileID = PrimitivesResultGet($resResult, 'intConnectionProfileID');

        if ($intConnectionProfileID)
        {
            $objConnectionProfile = new CConnectionProfile($intConnectionProfileID);
        }

        return $objConnectionProfile;
    }

} /* end of class CConnectionProfile */

?>
