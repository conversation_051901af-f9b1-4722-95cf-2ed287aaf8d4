<?php
/**
 * C_Core_Service.php
 *
 * Operates on data stored in <i>userdata.services</i> table
 *
 * @package	Core
 *
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 */

require_once PRODUCT_ACCESS_LIBRARY;
require_once '/local/data/mis/database/database_libraries/adsl-access.inc';

class C_Core_Service
{
    const ELLACOYA_NOT_PRESENT         = 0; //user is not in ellacoya
    const ELLACOYA_BILLING_NOT_IN_SYNC = 1; //user billing day is not in sync
    const ELLACOYA_BILLING_IN_SYNC     = 2; //user billing day is in sync

    /**
     * @access private
     * @var int Service ID
     */
    var $_intServiceID = 0;

    /**
     * @access private
     * @var int User ID
     */
    var $_intUserID = 0;

    /**
     * @access private
     * @var string CLI number
     */
    var $_strCLI = '';

    /**
     * @access private
     * @var string Email address
     */
    var $_strUserEmail = '';

    /**
     * @access private
     * @var string Username
     */
    var $_strUsername = '';

    /**
     * @access private
     * @var string Fornames
     */
    var $_strUserForenames = '';

    /**
     * @access private
     * @var string Surname
     */
    var $_strUserSurname = '';

    /**
     * @access private
     * @var int Service Definition ID
     */
    var $_intServiceDefinitionID = 0;

    /**
     * @access private
     * @var string Salutation
     */
    var $_strUserSalutation = '';

    /**
     * @access private
     * @var string Product Type
     */
    var $_strProductType = '';

    /**
     * @access private
     * @var string Invoice period
     */
    var $strInvoicePeriod;

    /**
     * @access private
     * @var string Next Invoice Date
     */
    var $strNextInvoiceDate;

    /**
     * @access private
     * @var int Invoice Day
     */
    var $intInvoiceDay;

    /**
     * @access private
     * @var string Status
     */
    var $strStatus;

    /**
     * @access private
     * @var float Balance
     */
    var $floBalance;

    /**
     * @access private
     * @var int Account ID
     */
    var $intAccountID;

    /**
     * @access private
     * @var int Customer ID
     */
    var $intCustomerID;

    /**
     * @access private
     * @var int Address ID
     */
    var $intAddressID;

    /**
     * @access private
     * @var int CreditDetails ID
     */
    var $intCreditDetailsID;

    /**
     * @access private
     * @var string ISP
     */
    var $strIsp;

    /**
     * @var string ReportSector
     */
    var $strReportSector;

    /**
     * @access private
     * @var string CardType
     */
    var $strCardType;

    /**
     * Constructor
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * <AUTHOR> Marek, <<EMAIL>>
     *
     * @uses userdata_service_get()
     * @uses userdata_user_get()
     * @uses product_get_service()
     *
     * @param int $intServiceID
     */
    function C_Core_Service($intServiceID = false)
    {
        if (false === $intServiceID)
        {
            return;
        }

        if ($arrServiceData = userdata_service_get($intServiceID) and isset($arrServiceData['service_id']))
        {
            $this->setServiceID($arrServiceData['service_id']);
            $this->setCLI($arrServiceData['cli_number']);
            $this->setUserID($arrServiceData['user_id']);
            $this->setUsername($arrServiceData['username']);
            $this->setServiceDefinitionID($arrServiceData['type']);
            $this->setInvoicePeriod($arrServiceData['invoice_period']);
            $this->setNextInvoiceDate($arrServiceData['next_invoice']);
            $this->setInvoiceDay($arrServiceData['invoice_day']);
            $this->setStatus($arrServiceData['status']);
            $this->setIsp($arrServiceData['isp']);
            $this->setReportsector($arrServiceData['strReportSector']);

            if ($arrUserData = userdata_user_get($arrServiceData['user_id']))
            {
                $this->setUserForenames($arrUserData['forenames']);
                $this->setUserSurname($arrUserData['surname']);
                $this->setUserEmail($arrUserData['email']);
                $this->setUserSalutation($arrUserData['salutation']);
            }

            if($arrProductData = product_get_service($arrServiceData['type']))
            {
                $this->setProductType($arrProductData['type']);
            }
        }
    }

    /**
     * Returns instance of self
     * @param $intServiceId
     * @return self
     */
    public static function get($intServiceId)
    {
        return new self($intServiceId);
    }

    /**
     * Not complete method, not removed in case it is called for some reason
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     */
    function setProduct($intServiceDefinitionID)
    {
    }

    /**
     * Returns Service ID
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @return int
     */
    function getServiceID()
    {
        return $this->_intServiceID;
    }

    /**
     * Sets Service ID
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @param int
     * @return void
     */
    function setServiceID($intServiceID)
    {
          $this->_intServiceID = $intServiceID;
    }

    /**
     * Retrieves the user's email address
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @return string
     */
    function getUserEmail()
    {
        $returnValue = $this->_strUserEmail;

        return (string) $returnValue;
    }

    /**
     * Sets UserEmail
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @param string
     * @return void
     */
    function setUserEmail($strUserEmail)
    {
          $this->_strUserEmail = $strUserEmail;
    }

    /**
     * Returns CLI number
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @return string
     */
    function getCLI()
    {
        $returnValue = $this->_strCLI;

        return (string) $returnValue;
    }

    /**
     * Sets CLI number
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @param string
     * @return void
     */
    function setCLI($strCLI)
    {
          $this->_strCLI = $strCLI;
    }

    /**
     * Returns Username
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @return string
     */
    function getUsername()
    {
        $returnValue = $this->_strUsername;

        return (string) $returnValue;
    }

    /**
     * Sets Username
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @param string
     * @return void
     */
    function setUsername($strUsername)
    {
        $this->_strUsername = $strUsername;
    }

    /**
     * Returns UserForenames
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @return string
     */
    function getUserForenames()
    {
        $returnValue = $this->_strUserForenames;

        return (string) $returnValue;
    }

    /**
     * Sets UserForenames
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @param string
     * @return void
     */
    function setUserForenames($strForenames)
    {
        $this->_strUserForenames = $strForenames;
    }

    /**
     * Returns UserSurname
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @return string
     */
    function getUserSurname()
    {
        $returnValue = $this->_strUserSurname;

        return (string) $returnValue;
    }

    /**
     * Sets UserSurname
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @param string
     * @return void
     */
    function setUserSurname($strSurname)
    {
        $this->_strUserSurname = $strSurname;
    }

    /**
     * Gets ServiceID by CLI number
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @param string
     * @return int
     */
    function getServiceIDbyCLI($strCLI)
    {
        $returnValue = UserdataGetServiceIDByCLI($strCLI);

        return (int) $returnValue;
    }

    /**
     * Returns ServiceDefinitionID
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @return int
     */
    function getServiceDefinitionID()
    {
        $returnValue = $this->_intServiceDefinitionID;

        return (int) $returnValue;
    }

    /**
     * Sets ServiceDefinitionID
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @param int
     * @return void
     */
    function setServiceDefinitionID($intSDI)
    {
        $this->_intServiceDefinitionID = $intSDI;
    }

    /**
     * Returns UserSalutation
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @return string
     */
    function getUserSalutation()
    {
        $returnValue = $this->_strUserSalutation;

        return (string) $returnValue;
    }

    /**
     * Sets UserSalutation
     *
     * @access public
     * <AUTHOR> Szulc, <<EMAIL>>
     * @param string
     * @return void
     */
    function setUserSalutation($strSalutation)
    {
        $this->_strUserSalutation = $strSalutation;
    }

    /**
     * Returns ProductType
     *
     * @access public
     * <AUTHOR> Spencer, <<EMAIL>>
     * @return string
     */
    function getProductType()
    {
        return $this->_strProductType;
    }

    /**
     * Sets ProductType
     *
     * @access public
     * <AUTHOR> Spencer, <<EMAIL>>
     * @param string
     * @return void
     */
    function setProductType($strProductType)
    {
        $this->_strProductType = $strProductType;
    }

    /**
     * Returns InvoicePeriod
     *
     * @access public
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @return string
     */
    function getInvoicePeriod()
    {
        return strtolower($this->strInvoicePeriod);
    }

    /**
     * Sets InvoicePeriod
     *
     * @access public
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @param string $strInvoicePeriod ie. monthly, quarterly, yearly, never, half-yearly
     */
    function setInvoicePeriod($strInvoicePeriod)
    {
        $this->strInvoicePeriod = $strInvoicePeriod;
    }

    /**
     * Returns NextInvoiceDate
     *
     * @access public
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @return string
     */
    function getNextInvoiceDate()
    {
        return $this->strNextInvoiceDate;
    }

    /**
     * Sets InvoiceDay
     *
     * @access public
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @param int $intInvoiceDay Day number
     */
    function setInvoiceDay($intInvoiceDay)
    {
        $this->intInvoiceDay = $intInvoiceDay;
    }

    /**
     * Returns InvoiceDay
     *
     * @access public
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @return int Invoice Day
     */
    function getInvoiceDay()
    {
        return $this->intInvoiceDay;
    }

    /**
     * Sets NextInvoiceDate
     *
     * @access public
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @param string $strNextInvoiceDate Date
     */
    function setNextInvoiceDate($strNextInvoiceDate)
    {
        $this->strNextInvoiceDate = $strNextInvoiceDate;
    }

    /**
     * Returns Status
     *
     * @access public
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @return string Status
     */
    function getStatus()
    {
        return $this->strStatus;
    }

    /**
     * Sets Status
     *
     * @access public
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @param string $strStatus Status ie. queued-activate,queued-reactivate,active,
     *                                     queued-deactivate,deactive,queued-destroy,
     *                                     destroyed,unconfigured,invalid,presignup
     */
    function setStatus($strStatus)
    {
        $this->strStatus = $strStatus;
    }

    /**
     * Returns Balance
     *
     * @access public
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @return float Balance
     */
    function getBalance()
    {
        return $this->floBalance;
    }

    /**
     * Sets Balance
     *
     * @access public
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @param float Balance
     */
    function setBalance($floBalance)
    {
        $this->floBalance = $floBalance;
    }

    /**
     * Returns UserID
     *
     * @access public
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @return int User ID
     */
    function getUserID()
    {
        return $this->_intUserID;
    }

    /**
     * Sets UserID
     *
     * @access public
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @param int User ID
     */
    function setUserID($intUserID)
    {
        $this->_intUserID = $intUserID;
    }

    /**
     * Returns AccountID
     *
     * @access public
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @return int Account ID
     */
    function getAccountID()
    {
        return $this->intAccountID;
    }

    /**
     * Sets AccountID
     *
     * @access public
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @param int Account ID
     */
    function setAccountID($intAccountID)
    {
        $this->intAccountID = $intAccountID;
    }

    /**
     * Returns CustomerID
     *
     * @access public
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @return int Customer ID
     */
    function getCustomerID()
    {
        return $this->intCustomerID;
    }

    /**
     * Sets CustomerID
     *
     * @access public
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @param int Customer ID
     */
    function setCustomerID($intCustomerID)
    {
        $this->intCustomerID = $intCustomerID;
    }

    /**
     * Returns AddressID
     *
     * @access public
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @return int Address ID
     */
    function getAddressID()
    {
        return $this->intAddressID;
    }

    /**
     * Sets AddressID
     *
     * @access public
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @param int Address ID
     */
    function setAddressID($intAddressID)
    {
        $this->intAddressID = $intAddressID;
    }

    /**
     * Returns CreditDetailsID
     *
     * @access public
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @return int CreditDetails ID
     */
    function getCreditDetailsID()
    {
        return $this->intCreditDetailsID;
    }

    /**
     * Sets CreditDetailsID
     *
     * @access public
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @param int CreditDetails ID
     */
    function setCreditDetailsID($intCreditDetailsID)
    {
        $this->intCreditDetailsID = $intCreditDetailsID;
    }

    /**
     * Returns CardType
     *
     * @access public
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @return string Card Type
     */
    function getCardType()
    {
        return $this->strCardType;
    }

    /**
     * Sets CardType
     *
     * @access public
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @param string $strCardType CardType
     */
    function setCardType($strCardType)
    {
        $this->strCardType = $strCardType;
    }

    /**
     * Returns ISP name
     *
     * @access public
     * <AUTHOR> Marek, <<EMAIL>>
     *
     * @return string
     */
    function getIsp()
    {
        return $this->strIsp;
    }

    /**
     * Sets ISP name
     *
     * @access public
     * <AUTHOR> Marek, <<EMAIL>>
     *
     * @param string
     */
    function setIsp($strIsp)
    {
          $this->strIsp = $strIsp;
    }

    /**
     * Returns the Report Sector
     *
     * @return string
     */
    function getReportSector()
    {
        return $this->strReportSector;
    }

    /**
     * Sets the report sector
     *
     * @param string $strReportSector The report sector to set
     *
     * @return void
     */
    function setReportSector($strReportSector)
    {
        $this->strReportSector = $strReportSector;
    }

    /**
     * Checks if account is deactivated
     *
     * @access public
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @return boolean
     */
    function isDeactive()
    {
          return ($this->strStatus == 'deactive');
    }

    /**
     * Checks if account is in presignup stage
     *
     * @access public
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @return boolean
     */
    function isPresignupAccount()
    {
        return ($this->strStatus == 'presignup');
    }

    /**
     * Checks if account is a metronet
     *
     * @access public
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @return boolean
     */
    function isMetronetAccount()
    {
        return ($this->strIsp == 'metronet');
    }

    /**
     * Adds service event to <i>userdata.service_events</i> table
     *
     * Replaces {@link userdata_service_event_add()} function
     *
     * @access public
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @param int    $intEventTypeID   Service Event Type ID
     * @param string $strFromType      ServiceDefinitionID
     * @param string $strToType        ServiceDefinitionID
     * @param string $strNextInvoice   Next Invoice Date
     * @param int    $intInvoiceID
     * @param string $strScheduledDate
     * @param string $strEventDate
     * @param int    $intOtherEventID
     * @param int    $intComponentID
     * @param int    $intMyID           php_lib user ID
     *
     * @return mixed Number of affected rows on success
     *               FALSE on failure
     */
    function addServiceEvent($intEventTypeID, $strFromType = '', $strToType = '',
                             $strNextInvoice = '', $intInvoiceID = '', $strScheduledDate = '',
                             $strEventDate = '', $intOtherEventID = '', $intComponentID = '',
                             $intMyID = '')
    {
        $mixReturn = false;
        $intEventTypeID = intval($intEventTypeID);
        $strEventDate   = addslashes($strEventDate);

        // Default the logging date.
        if ($strEventDate == '')
        {
            $strEventDate = "'".date('Y-m-d H:i:s')."'";
        }
        $intMyID = '".$intMyID."';

        // These parameters should be set to NULL if not passed:
        $strFromType      = ($strFromType      == '') ? 'NULL' : "'" . addslashes($strFromType)      . "'";
        $strToType        = ($strToType        == '') ? 'NULL' : "'" . addslashes($strToType)        . "'";
        $strNextInvoice   = ($strNextInvoice   == '') ? 'NULL' : "'" . addslashes($strNextInvoice)   . "'";
        $intInvoiceID     = ($intInvoiceID     == '') ? 'NULL' : "'" . intval($intInvoiceID)         . "'";
        $strScheduledDate = ($strScheduledDate == '') ? 'NULL' : "'" . addslashes($strScheduledDate) . "'";
        $intOtherEventID  = ($intOtherEventID  == '') ? 'NULL' : "'" . intval($intOtherEventID)      . "'";
        $intComponentID   = ($intComponentID   == '') ? 'NULL' : "'" . intval($intComponentID)       . "'";

        $dbhConnection = get_named_connection_with_db('userdata');

        $strQuery = 'INSERT INTO service_events
                           (service_id, event_type_id, from_type_id,
                            to_type_id, next_invoice, invoice_id,
                            event_date, scheduled_date, user_id,
                            other_event_id , component_id)
                     VALUES ('.$this->getServiceID().','.$intEventTypeID.','.$this->getServiceDefinitionID().','.
                              $strToType.','.$strNextInvoice.','.$intInvoiceID.','.
                              $strEventDate.','.$strScheduledDate.','.$intMyID.','.
                              $intOtherEventID.','.$intComponentID.')';

        $resResults = PrimitivesQueryOrExit($strQuery, $dbhConnection, "[ERROR] C_Core_Service::addServiceEvent() - query $strQuery failed", false, true);
        if ($resResults)
        {
            $mixReturn = PrimitivesAffectedRowsGet($dbhConnection);
        }

        return $mixReturn;
    }

    /**
     * Returns Due date based on Invoice period and next invoice date
     *
     * @access public
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @uses C_Core_Service::getInvoicePeriod()
     * @uses C_Core_Service::getNextInvoiceDate()
     *
     * @return string Date in Unix Timestamp format
     */
    function getDueDate()
    {
        switch($this->getInvoicePeriod())
        {
            case 'monthly':
                $intMonthsToSubtract = 1;
            break;
            case 'quarterly':
                $intMonthsToSubtract = 3;
            break;
            case 'yearly':
                $intMonthsToSubtract = 12;
            break;
            case 'half-yearly':
                $intMonthsToSubtract = 6;
            break;
            default:
                $intMonthsToSubtract = 1;
        }

        $uxtNextInvoiceDate = strtotime($this->getNextInvoiceDate());

        $uxtPaymentDueDate = mktime(0, 0, 0,
                                    date('n', $uxtNextInvoiceDate) - $intMonthsToSubtract,
                                    date('j', $uxtNextInvoiceDate),
                                    date('Y', $uxtNextInvoiceDate));

        return $uxtPaymentDueDate;
    }

    /**
     * Changes status of an account
     *
     * @access public
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @param string $strStatusHandle Status Handle
     *
     * @return mixed Number of affected accounts on success
     *               False on failure
     */
    function changeStatus($strStatusHandle)
    {
        $mixReturn = false;

        if (empty($strStatusHandle))
        {
            return $mixReturn;
        }

        $strStatusHandle = addslashes($strStatusHandle);

        $dbhConnection = get_named_connection_with_db('userdata');

        $strQuery = 'UPDATE services
                     SET status = "'.$strStatusHandle.'"
                     WHERE service_id = '. $this->getServiceID();

        $resResults = PrimitivesQueryOrExit($strQuery, $dbhConnection, "[ERROR] C_Core_Service::changeStatus() - query $strQuery failed", false, true);
        if ($resResults)
        {
            $mixReturn = PrimitivesAffectedRowsGet($dbhConnection);
        }

        return $mixReturn;
    }

    /**
     * Changes next invoice date of an account
     *
     * @access public
     * <AUTHOR> Tothova" <<EMAIL>>
     *
     * @param string  $strStatusHandle  Status Handle
     * @param boolean $changeInvoiceDay Are we also wanting to change invoice day
     *
     * @return mixed Number of affected accounts on success
     *               False on failure
     */
    function changeNextInvoice($dteNextInvoice, $changeInvoiceDay = true)
    {
        $mixReturn = false;

        if (($dteNextInvoice != '0000-00-00') && ($dteNextInvoice != '9999-09-09')) {
            // This 'if' statement checks if the date is valid P51187
            list($intYear, $intMonth, $intDay) = explode('-',$dteNextInvoice);

            if ($intMonth < date('n', mktime(0, 0, 0, $intMonth, $intDay, $intYear))) {
                $dteNextInvoice = $intYear.'-'.$intMonth.'-'.date('t', mktime(0, 0, 0, $intMonth, 1, $intYear));
            }
        }

        $invoiceDate = new DateTime($dteNextInvoice);
        $intInvoiceDay = $invoiceDate->format('j');

        $dteNextInvoice = addslashes($dteNextInvoice);

        $dbhConnection = get_named_connection_with_db('userdata');

        $strQuery = "UPDATE services
                        SET authorised_switch_payment = 'immediate',
                            next_invoice = '$dteNextInvoice' ";

        if ($changeInvoiceDay) {
            $strQuery .= ", invoice_day = '$intInvoiceDay' ";
        }

        $strQuery .= "WHERE service_id = '" . $this->getServiceID() . "'";

        $resResults = PrimitivesQueryOrExit(
            $strQuery,
            $dbhConnection,
            "[ERROR] C_Core_Service::changeNextInvoice() - query $strQuery failed",
            false,
            true
        );

        if ($resResults) {
            $mixReturn = PrimitivesAffectedRowsGet($dbhConnection);
        }

        if (self::ELLACOYA_BILLING_NOT_IN_SYNC == $this->ellacoyaBillingDayInSync($intInvoiceDay)) {
            adslUpdateEllacoya(
                $this->getServiceID(),
                'Next invoice date changed by C_Core_Service::changeNextInvoice',
                'billing_day',
                $intInvoiceDay
            );
        }

        return $mixReturn;
    }

    /**
     * Checks if billing day in ellacoya is up to date
     * @param $intBillingDay
     * @return int
     */
    public function ellacoyaBillingDayInSync($intBillingDay)
    {
        $strQuery = "SELECT intBillingDay FROM vwServiceIDProductSegment WHERE intServiceID=".$this->getServiceID();

        $resConn = get_named_connection_with_db('vmbu');
        $arrRes = PrimitivesResultsAsArrayGet(PrimitivesQueryOrExit($strQuery, $resConn));
        if (count($arrRes) === 0)
        {
            return self::ELLACOYA_NOT_PRESENT;
        }
        elseif ($intBillingDay == $arrRes[0]['intBillingDay'])
        {
            return self::ELLACOYA_BILLING_IN_SYNC;
        }
        else
        {
            return self::ELLACOYA_BILLING_NOT_IN_SYNC;
        }
    }

    /**
     * Checks if customer has got a deferred product
     *
     * @access public
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @return boolean
     */
    function isServiceDeferred()
    {
        $mixReturn = array('description' => 'Unknown error occured - probably related with DB connection');

        $dbhConnection = get_named_connection_with_db('product');

        $strQuery = 'SELECT
                           ct.vchContractTypeHandle AS vchContractTypeHandle
                      FROM
                            products.tblContractType AS ct
                            INNER JOIN products.tblContractDefinition AS cd ON ct.intContractTypeID = cd.intContractTypeID
                            INNER JOIN products.tblProductContractDefinition AS pdc ON cd.intContractDefinitionID = pdc.intContractDefinitionID
                      WHERE
                            pdc.intServiceDefinitionID = '.$this->getServiceDefinitionID().'
                            AND ct.vchContractTypeHandle IN ("DEFERRED_ACTIVATION", "PROFIT_FOREGONE_SERVICES")';

        $resResults = PrimitivesQueryOrExit($strQuery, $dbhConnection, "[ERROR] C_Core_Service::isServiceDeferred() - query $strQuery failed", false, true);
        if ($resResults)
        {
            $mixReturn = PrimitivesResultGet($resResults);

            if (count($mixReturn))
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        return $mixReturn;
    }

    /**
     * Fetches data about accounts details for current Service ID
     * from <i>userdata.accounts</i> table
     *
     * @access public
     * <AUTHOR> Marek" <<EMAIL>>
     *
     * @uses C_Core_Error
     *
     * @see DbgLegacy
     *
     * @return mixed List of account data on success
     *               false on failure
     */
    function fetchAccountDetails()
    {
        $mixReturn = false;

        $dbhConnection = get_named_connection_with_db('userdata');

        $intQueryUserId = $this->getUserID();
        $strQuery = "SELECT
                           a.account_id,
                           a.customer_id,
                           a.address_id,
                           c.credit_details_id,
                           a.balance,
                           c.card_type
                     FROM accounts a
                          LEFT JOIN dbCreditDetails.credit_details c
                              ON a.account_id = c.intAccountId AND c.intCreditDetailsStatusId = (
                                  SELECT intCreditDetailsStatusId FROM dbCreditDetails.tblCreditDetailsStatus WHERE vchHandle = 'ACTIVE'
                              )
                          INNER JOIN addresses ad
                              ON ad.address_id = a.address_id
                          INNER JOIN users u
                                ON a.customer_id = u.customer_id
                     WHERE
                          u.user_id = $intQueryUserId";


        $resResults = PrimitivesQueryOrExit($strQuery, $dbhConnection, "[ERROR] C_Core_Service::fetchAccountDetails() - query $strQuery failed", false, true);
        if ($resResults)
        {
            $mixReturn = PrimitivesResultGet($resResults);

            if (!is_array($mixReturn) || empty($mixReturn))
            {
                $strQuery = "SELECT
                                   a.account_id,
                                   a.customer_id,
                                   a.address_id,
                                   c.credit_details_id,
                                   a.balance,
                                   c.card_type
                             FROM accounts a
                                LEFT JOIN dbCreditDetails.credit_details c ON c.intAccountId = a.account_id AND
                                c.intCreditDetailsStatusId = (SELECT intCreditDetailsStatusId FROM dbCreditDetails.tblCreditDetailsStatus WHERE vchHandle = 'ACTIVE')
                                INNER JOIN users u ON a.customer_id = u.customer_id
                             WHERE
                                  u.user_id = $intQueryUserId";

                $resResults = PrimitivesQueryOrExit($strQuery, $dbhConnection, "[ERROR] C_Core_Service::fetchAccountDetails() - query $strQuery failed", false, true);
                if ($resResults)
                {
                    $mixReturn = PrimitivesResultGet($resResults);
                }
            }

            if (is_array($mixReturn) && !empty($mixReturn))
            {
                $this->setAccountID($mixReturn['account_id']);
                $this->setCustomerID($mixReturn['customer_id']);
                $this->setAddressID($mixReturn['address_id']);
                $this->setCreditDetailsID($mixReturn['credit_details_id']);
                $this->setBalance($mixReturn['balance']);
                $this->setCardType($mixReturn['card_type']);
            }
        }

        return $mixReturn;
    }
}
