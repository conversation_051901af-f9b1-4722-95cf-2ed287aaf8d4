<?php
/**
 * Container for hardware-specific functionality
 *
 * @package LegacyCodebase
 * <AUTHOR> <<EMAIL>>
 *
 */

require_once('/local/data/mis/database/application_apis/hardware-bundle-classes.inc');

$array_hardware_configurators = GetHardwareBundleComponentTypes();
foreach ($array_hardware_configurators as $configurator) {
    $global_component_configurators[$configurator] = 'configure_hardware_bundle_configurator';
}

// Hack to insert the component configurator array into PHP5's global scope if it's not already there
if (!isset($GLOBALS['global_component_configurators'])) {
    $GLOBALS['global_component_configurators'] = $global_component_configurators;
} else {
    foreach ($global_component_configurators as $intIndex => $strConfigurator) {
        $GLOBALS['global_component_configurators'][$intIndex] = $strConfigurator;
    }
}

unset($array_hardware_configurators);

// FIXME - hardcoded URL
if (defined("EBUYER_HARDWARE_ORDERING_LIVE") && EBUYER_HARDWARE_ORDERING_LIVE == 'yes') {
    $ebuyer_user_pass = 'account_uid=118469&password=118469';
} else {
    $ebuyer_user_pass = 'account_uid=117155&password=117155';
}

/**
 * Simple function to pull back a list of hardware bundle component ids.  Results (approx. 110) are cached
 * NOTE; the array returned has been flattened (i.e. $array[n] = X, rather than $array[n] = array(X))
 *
 * @return array
 */
function GetHardwareBundleComponentTypes()
{
    static $dataCache = array();

    if (empty($dataCache)) {
        $conn      = get_named_connection_with_db('unprivileged_reporting');
        $query     = 'SELECT service_component_id FROM products.component_hardware_bundle_config';
        $resResult = PrimitivesQueryOrExit($query, $conn);
        $dataCache = PrimitivesResultsAsListGet($resResult);
    }

    return $dataCache;
}

/**
 * Adds an entry into tblHardwareBundleManualOrderReason
 *
 * @param int $intActorID      the customer's actor id
 * @param str $strReasonHandle the reason handle
 * @param int $intComponentID  the component associated with the hardware bundle
 *
 * @return undefined
 */
function hardware_order_reason_add($intActorID, $strReasonHandle, $intComponentID)
{
    static $dataCache = array();

    $intActorID     = (int) $intActorID;
    $intComponentID = (int) $intComponentID;

    if (empty($dataCache)) {
        $conn      = get_named_connection_with_db('common_adsl_reporting');
        $strQuery  = "SELECT vchHandle, intManualOrderReasonId from tblManualOrderReason";
        $resResult = PrimitivesQueryOrExit($strQuery, $conn);

        $tmpCache = PrimitivesResultsAsArrayGet($resResult);
        foreach ($tmpCache as $reason) {
            $strHandle = $reason['vchHandle'];
            $intId     = $reason['intManualOrderReasonId'];
            $dataCache[$strHandle] = $intId;
        }
    }

    // DJM2014: the existing code doesn't error/abort if the reason is invalid, so we just flag this in the error logs!
    $intManualOrderReasonId = (isset($dataCache[$strReasonHandle]) ? $dataCache[$strReasonHandle] : 0);
    if ($intManualOrderReasonId == 0) {
        error_log(
            __FUNCTION__ .
            "() WARNING: unable to find reason handle $strReasonHandle for actor/component $intActorID/$intComponentID"
        );
    }

    //Get Hardware Bundle Id
    $objHardwareBundle   = component_hardware_bundle_get_object($intComponentID);
    $intHardwareBundleID = $objHardwareBundle->GetHardwareBundleID();

    //Add Order Reason
    $strQuery = <<<EOQ
INSERT INTO tblHardwareBundleManualOrderReason (
    intPartnerId,
    intConfigHardwareBundleId,
    intManualOrderReasonId,
    intActorId
) VALUES (
    0,
    $intHardwareBundleID,
    $intManualOrderReasonId,
    $intActorID
)
EOQ;

    $conn      = get_named_connection_with_db('common_adsl');
    $resResult = PrimitivesQueryOrExit($strQuery, $conn);
}

/**
 * Function to fetch a list of manual order reasons where dteEnd is null.
 * Data is cached - this information is assumed to be static for the duration of the current session
 *
 * @return array
 */
function hardware_order_reason_fetch()
{
    static $dataCache = array();

    if (empty($dataCache)) {
        $conn      = get_named_connection_with_db('common_adsl_reporting');
        $strQuery  = "SELECT vchHandle, vchDescription from tblManualOrderReason WHERE dteEnd IS NULL";
        $resResult = PrimitivesQueryOrExit($strQuery, $conn);
        $dataCache = PrimitivesResultsAsArrayGet($resResult);
    }

    return $dataCache;
}

/**
 * Adds a service event to the given component
 *
 * @param int $intComponentID    the component ID
 * @param int $intServiceEventID the service event ID
 *
 * @return undefined
 */
function component_hardware_bundle_add_service_event($intComponentID, $intServiceEventID)
{
    $objHardwareBundle = component_hardware_bundle_get_object($intComponentID);
    $objHardwareBundle->AddServiceEvent($intServiceEventID);
}

/**
 * Wrapper to HardwareBundleSupplier::GetHardwareBundleByComponent()
 *
 * @param int $intComponentID the component ID
 * @param int $intRMA         actually a boolean: if set, RMAs can be actioned
 *
 * @return HardwareBundle
 */
function component_hardware_bundle_get_object($intComponentID, $intRMA = 0)
{
    return HardwareBundleSupplier::GetHardwareBundleByComponent($intComponentID, $intRMA);
}

/**
 * Destroys the given hardware component
 *
 * @param int $intComponentID the component ID
 *
 * @return bool
 */
function component_hardware_bundle_auto_destroy($intComponentID)
{
    $rComponent = userdata_component_get($intComponentID);

    $objBusinessActor = Auth_BusinessActor::getActorByExternalUserId(SCRIPT_USER);
    Tr069_Object::setBusinessActor($objBusinessActor);

    $objHardwareClient = BusTier_BusTier::getClient('hardware');
    $objTr069DeviceArray = $objHardwareClient->getTr069HardwareForComponentId(new Int($intComponentID));

    foreach ($objTr069DeviceArray as $objTr069Device) {
        if (!empty($objTr069Device) && $objTr069Device instanceof Tr069_Device) {
            //Remove the Tr069 device from ACS
            $objTr069Device->delete();
        }
    }

    switch($rComponent['status']) {
        case 'unconfigured':
        case 'active':
        case 'queued-destroy':
            userdata_component_set_status($intComponentID, 'destroyed');
            break;
        case 'queued-activate':
            $HardwareBundle = component_hardware_bundle_get_object($intComponentID);
            $HardwareBundle->CancelOrder();
            break;
        default:
            return false;
    }

    return true;
}

/**
 * Auto-configure the given component
 *
 * @param int $intComponentID the component ID
 *
 * @return bool
 */
function component_hardware_bundle_auto_configure($intComponentID)
{
    if (!preg_match('/^[0-9]+$/', $intComponentID)) {
        return false;
    }

    // Get the component object
    $HardwareBundle = component_hardware_bundle_get_object($intComponentID);

    // If the bundle is already setup, nothing to configure
    if ($HardwareBundle->GetByComponentID($intComponentID)) {
        return true;
    }

    // Initialise the component
    $HardwareBundle->CreateForComponent($intComponentID);

    // Write the data to the database
    $HardwareBundle->Commit();

    // Update the component
    $intHardwareBundleID = $HardwareBundle->GetHardwareBundleID();
    userdata_component_set_configuration($intComponentID, $intHardwareBundleID, '');
    userdata_component_set_status($intComponentID, 'queued-activate');

    return false;
}

/**
 * Adds hardware config data for the given component.  Returns the ID of the new record
 *
 * @param int $component_id the component id
 *
 * @return int
 */
function config_hardware_bundle_add($component_id)
{
    $connection = get_named_connection('userdata');
    $query      = 'INSERT INTO config_hardware (component_id, status) VALUES ("'.$component_id.'", "Not Ordered")';
    $result     = mysql_query($query, $connection) or report_error(__FILE__, __LINE__, mysql_error($connection));
    $insert_id  = mysql_insert_id($connection);

    if ($insert_id != 0) {
        userdata_component_set_status($component_id, 'queued-activate');
        userdata_component_set_config_id($component_id, $insert_id);
    } else {
        report_error(__FILE__, __LINE__, "Failed add Hardware configuration for component_id '$component_id'");
    }

    return $insert_id;
}

/**
 * Auto-configurator for hardware bundles
 *
 * @param int $component_id the component id
 * @param str $action       the configuration action
 *
 * @return undefined
 */
function configure_hardware_bundle_configurator($component_id, $action)
{
    switch($action) {
        case 'auto_configure':
            component_hardware_bundle_auto_configure($component_id);
            break;
        case 'auto_destroy':
            component_hardware_bundle_auto_destroy($component_id);
            break;
        case 'auto_disable':
        case 'auto_enable':
        case 'auto_refresh':
        default:
            // do nothing
            break;
    }
}

/**
 * Simple helper to manage logging output.  Messages are written to STDOUT
 *
 * @param str $strType    the message type (e.g. INFO, WARNING, ERROR)
 * @param str $strMessage the message to be logged
 *
 * @return undefined
 */
function hardwareLogOutput($strType, $strMessage)
{
    print csi_generate_log_line($strType, $strMessage) . "\n";
}
