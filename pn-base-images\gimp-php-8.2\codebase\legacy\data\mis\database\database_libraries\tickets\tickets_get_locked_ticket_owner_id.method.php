<?php

////////////////////////////////////////////////////////////////////////////////////
// Name:      split_tickets_get_locked_ticket_owner_id
// Purpose:   To get the owner id of a Question being worked on
// Arguments: $ticket_id (the id of the ticket)
// Returns:   $owner_id (the owner id of the ticket).
//            false if the Question is not being worked on
/////////////////////////////////////////////////////////////////////////////////////

function split_tickets_get_locked_ticket_owner_id($ticket_id)
{
    // Variable declarations

    $ticket_id  = addslashes($ticket_id);
    $connection = get_named_connection('tickets');

    // Select the 'owner_id's and 'ticket_id's for Question contacts that are being worked on

    $query = "SELECT SQL_NO_CACHE tbwo.owner_id AS owner_id
              FROM tickets AS t
              INNER JOIN tickets_being_worked_on AS tbwo ON tbwo.ticket_id = t.ticket_id
              WHERE tbwo.when_done IS NULL AND t.ticket_id = '{$ticket_id}'";

    $result = mysql_query($query, $connection)
        or report_error(__FILE__, __LINE__, mysql_error($connection));

    $number_found = mysql_num_rows($result);

    // If there are no tickets, then the Question is not being worked on

    if ($number_found == 0) {

        $owner_id = false;
    }
    elseif ($number_found == 1) {

        $owner_id = mysql_result($result, 0, 0);
    }

    mysql_free_result($result);
    return $owner_id;

} // end split_tickets_get_locked_ticket_owner_id

