<?php
	///////////////////////////////////////////////////////////////////////////
	// Configurator for use with all domain type components
	// $Header: /share/git/cvsMigration/legacy_cvs/LegacyCodebase/data/mis/database/database_libraries/components/config-domain-access.inc,v 1.4 2005-11-24 09:03:30 fzaki Exp $

	$global_component_configurators['12']  = 'configure_domain_configurator';
	$global_component_configurators['38']  = 'configure_domain_configurator';
	$global_component_configurators['62']  = 'configure_domain_configurator';
	$global_component_configurators['64']  = 'configure_domain_configurator';
	$global_component_configurators['65']  = 'configure_domain_configurator';
	$global_component_configurators['175'] = 'configure_domain_configurator';
	
	// Hack to insert the component configurator array into PHP5's global scope
	// if it's not already there
	if(!isset($GLOBALS['global_component_configurators']))
	{
		$GLOBALS['global_component_configurators'] = $global_component_configurators;
	}
	else
	{
		foreach($global_component_configurators as $intIndex => $strConfigurator)
		{
			$GLOBALS['global_component_configurators'][$intIndex] = $strConfigurator;
		}
	}

	//
	// auto_destroy handler for Domain Configurator
	//
	function domain_auto_destroy($component_id)
	{
		$component = userdata_component_get($component_id);

		switch($component['status'])
		{
			case 'unconfigured':
				userdata_component_set_status($component_id, 'destroyed');
				break;

			case 'destroyed':
			case 'queued-destroy':
				break;

			case 'queued-activate':
				break;

			case 'active':
				break;
		}
	}


	//
	// Domain Component Configurator
	//
	function configure_domain_configurator($component_id, $action)
	{
		switch($action)
		{
			case 'auto_configure':
				// Do nothing
				break;

			case 'auto_disable':
				break;

			case 'auto_enable':
				break;

			case 'auto_destroy':
				domain_auto_destroy($component_id);
				break;

			case 'auto_refresh':
				break;

			default:
				break;
		}
	}

?>
