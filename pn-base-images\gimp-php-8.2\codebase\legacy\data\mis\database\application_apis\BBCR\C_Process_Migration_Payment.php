<?php
/**
 * C_Process_Migration_Payment
 * Introduced by HTT Ph3 project
 * Class responsible for processing payments for Migrations out and Ceases
 * Used in BBCR Cease and BBCR MigrationOut processors
 *
 * @package LegacyCodebase
 * <AUTHOR> <l<PERSON><PERSON><PERSON><PERSON>@plus.net>
 * @link    link
 */

require_once '/local/data/mis/database/standard_include.inc';
require_once '/local/data/mis/database/database_libraries/sql_primitives.inc';

require_once '/local/data/mis/database/database_libraries/adsl-access.inc';
require_once '/local/data/mis/database/database_libraries/product-access.inc';
require_once '/local/data/mis/database/database_libraries/tickets-access.inc';
require_once '/local/data/mis/database/database_libraries/userdata-access.inc';
require_once '/local/data/mis/database/database_libraries/financial-access.inc';
require_once '/local/data/mis/database/database_libraries/payment-limit-access.inc';
require_once '/local/data/mis/database/database_libraries/components/config-cbc-flex-access.inc';
require_once '/local/data/mis/database/database_libraries/components/config-cbc-payg-access.inc';
require_once '/local/data/mis/database/database_libraries/components/config-cbc-extra-bandwidth-access.inc';
require_once '/local/data/mis/database/database_libraries/components/config-dialup-access.inc';
require_once '/local/data/mis/common_library_functions/common_application_apis/common-date-api.inc';
require_once '/local/data/mis/database/database_libraries/CoreObjects/Financial/CFinancialHelper.inc';
require_once CBC_ACCESS_LIBRARY;

require_once '/local/data/mis/database/database_libraries/components/CProduct.inc';
require_once '/local/data/mis/database/database_libraries/components/CProductComponent.inc';
require_once '/local/data/mis/database/database_libraries/components/CInternetConnectionProduct.inc';
require_once '/local/data/mis/database/database_libraries/CoreObjects/Financial/CProductComponentScheduledPayment.inc';
require_once '/local/data/mis/common_library_functions/class_libraries/Retention/DiscountManager.class.php';
require_once '/local/data/mis/common_library_functions/class_libraries/C_Core_Actions_Products_Contract.inc';
require_once '/local/data/mis/common_library_functions/class_libraries/C_Core_Entities_Products_ContractsAll.inc';

require_once '/local/data/mis/database/class_libraries/CancellationCeaseType.class.php';
require_once '/local/data/mis/database/class_libraries/CancellationPayment.class.php';
require_once '/local/data/mis/database/class_libraries/CancellationHelper.class.php';

require_once '/local/data/mis/database/database_libraries/financial/DirectDebitTransactionCode.class.php';

require_once '/local/data/mis/database/database_libraries/CoreObjects/Financial/COneOffPaymentScheduler.inc';

require_once '/local/data/mis/database/class_libraries/CancellationPaymentHelper.class.php';

/**
 * C_Process_Migration_Payment
 *
 * @package LegacyCodebase
 * <AUTHOR> Chudzinski <<EMAIL>>
 * @link    link
 */
class C_Process_Migration_Payment
{
    /**
     * Service Id
     * @var int
     */
    private $_intServiceId;

    /**
     * Completion date as unix timestamp
     * @var int
     */
    private $_uxtCompletionDate;

    /**
     * CancellationHelper object
     * @var CancellationHelper
     */
    private $_objCancellationHelper = null;

    /**
     * CancellationCeaseType object
     * @var CancellationCeaseType
     */
    private $_objCancellationCeaseType = null;

    /**
     * Construct
     *
     * @param int                   $intServiceId             Service Id
     * @param CancellationCeaseType $objCancellationCeaseType Cancellation type MigrationOut or BB Cease
     * @param int                   $uxtCompletionDate        Date of notification about Cease or
     *                                                        MigrationOut from BBCR
     *
     * @return void
     */
    public function __construct($intServiceId, CancellationCeaseType $objCancellationCeaseType, $uxtCompletionDate)
    {
        $this->_intServiceId = $intServiceId;
        $this->_objCancellationCeaseType =  $objCancellationCeaseType;
        $this->_objCancellationHelper = new CancellationHelper($intServiceId);
        $this->_uxtCompletionDate = $uxtCompletionDate;
    }

    /**
     * Executes cancellation payment actions
     *
     * @param boolean $bolExcludeCessationFee Flag to exclude cassation fee from outstanding fees caclulation
     *
     * @return array
     */
    public function processCancellation($bolExcludeCessationFee = false)
    {
        $statuses = array(
            'queued-destroy',
            'destroyed'
        );

        $componentId = userdataGetComponentId((int) $this->_intServiceId, 'INTERNET_CONNECTION', $statuses, true);

        $changePlan = $this->getChangePlan($componentId);

        $arrReturn = array();

        $objCancellationHelper = $this->_objCancellationHelper;
        $objCoreService = $objCancellationHelper->getCoreService();
        $objServiceDefinition  = new Core_ServiceDefinition($objCoreService->getType());
        $intInvoiceId = null;

        //Do not process Metronet accounts - raise ticket to CSC to check outstanding charges
        if ('metronet' == $objServiceDefinition->getIsp()) {

            $intTeamId = phplibGetTeamIdByHandle('CSC_BILLING');
            $strTicketText = implode(
                '',
                file(BBCR_LOCAL_LIBRARY_ROOT .'ticket_templates/metronet_check_os_charges.tpl')
            );

            $intTicketId = $this->addTicket(
                'Internal',
                $this->_intServiceId,
                0,
                0,
                'Open',
                SCRIPT_USER,
                $strTicketText,
                0,
                $intTeamId
            );

            $strCeaseTypeHandle = $this->_objCancellationCeaseType->getHandle();

            if (CancellationCeaseType::MIGRATION_OUT == $strCeaseTypeHandle
                || CancellationCeaseType::MIGRATION_OUT_WITHOUT_MAC == $strCeaseTypeHandle
            ) {
                //Record migration out service event
                userdata_service_event_add(
                    $this->_intServiceId, SERVICE_EVENT_MIGRATION_OUT,
                    $objCoreService->getType(), $objCoreService->getType()
                );
            }

            $arrReturn['intErrorCode'] = 0;
            $arrReturn['strErrorMessage'] = "Service Id: {$this->_intServiceId} - ".
            "Metronet - ticket {$intTicketId} raised to process account manually\n\n";

            return $arrReturn;
        }

        $objCompletionDate = I18n_Date::fromTimestamp($this->_uxtCompletionDate);

        $objCancellationRequestDate = $this->getNoticePeriodStartDate(
            $this->_intServiceId,
            $this->_objCancellationCeaseType, $objCompletionDate
        );

        if (empty($objCancellationRequestDate)) {
            $arrReturn['intErrorCode'] = 0;
            $arrReturn['strErrorMessage'] = "ServiceId: {$this->_intServiceId} - ".
                "Unable to get notice period start date for outstanding fees calculation.";

            return $arrReturn;
        }

        if (false === $this->_isNextInvoiceDateValidForCalculations()) {

            $intTeamId = phplibGetTeamIdByHandle('CSC_BILLING_NON_AUTOMATED_CEASES');

            $strTicketText = "INTERNAL: This customer has migrated to another provider or ceased line.\n\n".
                "The system cannot calculate outstanding charges because the next invoice date is invalid.\n".
                "Please check the account and inform the customer if there are any outstanding fees.";

            $intTicketId = $this->addTicket(
                'Internal', $this->_intServiceId,
                0, 0, 'Open', 0, $strTicketText, 0, $intTeamId
            );

            $arrReturn['intErrorCode'] = 0;
            $arrReturn['strErrorMessage'] = "Service Id: {$this->_intServiceId} - ".
                "Unable to calculate outstanding fees due to invalid next invoice date ".
                "- ticket {$intTicketId} raised to process account manually\n\n";

            return $arrReturn;
        }

        $productFamily = \ProductFamily_Factory::getFamily($objServiceDefinition->getServiceDefinitionId());

        $productCharge = $productFamily->hasAutoContracts() ? 'profit_foregone' : 'deferred';

        $chargeTypes = array($productCharge,'arrears','first_year','CBC','cessation','subscription');

        $arrOutstandingCharges = $objCancellationHelper->getOutstandingCharges(
            $objCompletionDate,
            $objCancellationRequestDate,
            $chargeTypes,
            $bolExcludeCessationFee
        );

        //Cancell all outstanding scheduled payments for BB subscription component
        $this->_cancelScheduledPayments($this->_intServiceId);

        if (!empty($arrOutstandingCharges)) {
            //Check for outstanding charges
            if (0 == $arrOutstandingCharges['floTotalOutstandingCharges']) {
                $this->_processAccountWithoutCharges();

                $arrReturn['intErrorCode'] = 0;
                $arrReturn['strErrorMessage'] = "ServiceId: {$this->_intServiceId} - ".
                "Processing account without outstanding charges";
            } elseif ($arrOutstandingCharges['floTotalOutstandingCharges'] > 0) {
                $intInvoiceId = $this->_takeCancellationPayment($arrOutstandingCharges);

                $arrReturn['intErrorCode'] = 0;
                $arrReturn['strErrorMessage'] = "ServiceId: {$this->_intServiceId} - ".
                "Taking payment for outstanding charges";
            } else {
                $intInvoiceId = $this->_processRefund($arrOutstandingCharges['floTotalOutstandingCharges']);

                $arrReturn['intErrorCode'] = 0;
                $arrReturn['strErrorMessage'] = "ServiceId: {$this->_intServiceId} - Processing refund";
            }

            if (isset($arrOutstandingCharges['error'])) {
                $arrReturn['intErrorCode'] = 0;

                $strErrors = '';

                foreach ($arrOutstandingCharges['error'] as $arrErrors) {

                    $strErrors = $strErrors."Service ID: {$this->_intServiceId} - ".print_r($arrErrors, 1);
                }

                $arrReturn['strErrorMessage'] .= $strErrors;
            }

            //Nullify deferred contractas, cancel cassation fee and pending discounts

            // Flag here, affects how the service ticket wording will be generated for migrating out
            $strCeaseTypeHandle = $this->_objCancellationCeaseType->getHandle();
            $bolMigrationOutCancellation
                = CancellationCeaseType::MIGRATION_OUT == $strCeaseTypeHandle ||
                CancellationCeaseType::MIGRATION_OUT_WITHOUT_MAC == $strCeaseTypeHandle;

            $this->_cancelCessationCharge($this->_intServiceId);

            if ($productFamily->hasAutoContracts()) {

                $result = $this->executeChangePlan($changePlan, $componentId, $intInvoiceId);

                if (!$result) {

                    $arrReturn['strErrorMessage'] .= "Service Id: {$this->_intServiceId} "
                        . "- Failed to write-off existing broadband contract.";
                }

            } else {

                if (!$this->withdrawExistingContracts($this->_intServiceId, $bolMigrationOutCancellation)) {

                    $arrReturn['strErrorMessage'] .= "Service Id: {$this->_intServiceId} "
                        . "- Failed to withdraw existing contracts.";
                }
            }

            $this->_cancelDiscounts($this->_intServiceId);

            //Add service event to mark account as processed by HTT automation
            $this->_addHttServiceEvent($intInvoiceId);

            //Deactivate prov record and remove Ellacoya Profile from system
            adslDeactivateProvRecord($this->_intServiceId, $objCoreService->getUsername(), $objCoreService->getIsp());

        } else {
            $arrReturn['intErrorCode'] = 12;
            $arrReturn['strErrorMessage'] = "ServiceId: {$this->_intServiceId} - Failed to get outstanding charges\n";
        }

        return $arrReturn;
    }

    /**
     * Returns notice period start date base on cancellation type
     * For Ceases it's completion date
     * For MigrationsOut checks for mac key reqest and generation dates
     *
     * @param int                   $intServiceId             Service id
     * @param CancellationCeaseType $objCancellationCeaseType CancellationCeaseType object
     * @param I18n_Date             $objCompletionDate        Completion date
     *
     * @return I18n_Date Notice period start date
     */
    public function getNoticePeriodStartDate(
        $intServiceId,
        CancellationCeaseType $objCancellationCeaseType,
        I18n_Date $objCompletionDate
    ) {
        $objNoticePeriodStart = null;
        $objMacKeyRequestDate = null;
        $objMacKeyGenrationDate = null;

        if (CancellationCeaseType::BB_CEASE == $objCancellationCeaseType->getHandle()) {
            return $objCompletionDate;
        }

        $objMacKeyRequestDate   = $this->getMacKeyRequestDate($intServiceId);
        $objMacKeyGenrationDate = $this->_objCancellationHelper->getMacKeyGenerationDate();

        if (!empty($objMacKeyRequestDate)) {
            $objNoticePeriodStart = $objMacKeyRequestDate;
        } elseif (!empty($objMacKeyGenrationDate)) {
            $objNoticePeriodStart = $objMacKeyGenrationDate;
        } elseif (CancellationCeaseType::MIGRATION_OUT_WITHOUT_MAC == $objCancellationCeaseType->getHandle()) {
            $objNoticePeriodStart = $objCompletionDate;
        }

        return $objNoticePeriodStart;
    }

    /**
     * Returns date when latest retention offer was offered to customer reqesting MAC key
     *
     * @param int $intServiceId Service id
     *
     * @return I18n_Date Date object if retnetion offer exists otherwise null
     */
    public function getMacKeyRequestDate($intServiceId)
    {
        $objRetentionOfferDate = null;

        $dbhConnection = get_named_connection_with_db('tickets');

        $intServiceId = PrimitivesRealEscapeString($intServiceId, $dbhConnection);

        //Get latest not acceppted and not cancelled retention offer
        //that was created for customer requesting migration out (intRetentionOfferResultId = 3 is Migration)
        $strQuery = "SELECT
                       UNIX_TIMESTAMP(ro.dtmOffered) AS uxtOffered
                     FROM
                       tickets.tblRetentionOffer ro
                     INNER JOIN tickets.tickets t ON t.ticket_id = ro.intTicketId
                     WHERE
                       ro.bolOfferCancelled = false
                     AND
                       ro.dtmAccepted IS null
                     AND
                       ro.intRetentionOfferResultId = 3
                     AND
                       t.service_id = {$intServiceId}
                     ORDER BY ro.intRetentionOfferId DESC
                     LIMIT 1";

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

        $arrResult = PrimitivesResultGet($resResult);

        if (!empty($arrResult['uxtOffered'])) {
            $objRetentionOfferDate = I18n_Date::fromTimestamp($arrResult['uxtOffered']);
        }

        return $objRetentionOfferDate;
    }

    /**
     * Process account with refund.
     *
     * @param float $floAmountToRefund Amount to refund
     *
     * @return int Invoice Id of invoice selected to refunded agains or null for Partner accounts
     */
    private function _processRefund($floAmountToRefund)
    {
        $objCancellationHelper = $this->_objCancellationHelper;
        $objCoreService        = $objCancellationHelper->getCoreService();
        $objServiceDefinition  = new Core_ServiceDefinition($objCoreService->getType());
        $intInvoiceId = null;

        //Make refund amount positive value
        $floAmountToRefund = (-1 * $floAmountToRefund);

        if ($objCoreService->isPartnerVisp($objCoreService->getIsp())) {
            $objCancellationHelper->closeAccount();

            return $intInvoiceId;
        }

        //Downgrade to genreic services product
        $objCancellationHelper->downgradeToGenericServices();

        //Check for chargeable components on account
        $arrChargableComponents = $objCancellationHelper->getAccountChargeableComponents();

        //No refunds are applicable to business customers
        if (!$objServiceDefinition->isBusiness()) {

            //Find latest invoice paid by customer with correct value to refund against
            $intInvoiceId = $objCancellationHelper->getLatestInvoiceToRefund($floAmountToRefund);

            $bolRefunded = false;

            if (!empty($intInvoiceId) && $intInvoiceId > 0) {
                $bolRefunded = $objCancellationHelper->refundCustomer($floAmountToRefund, $intInvoiceId);
            }

            if (false === $bolRefunded) {
                $this->_sendPaymentRefundTicket();
            } elseif (!CeaseCommunications_Registration::getInstance()->canHandleCease($this->intServiceId)) {
                EmailHandler::sendEmail(
                    $this->_intServiceId,
                    'migrating_refund',
                    array('floRefundAmount' => number_format($floAmountToRefund, 2, '.', ''))
                );
            }
        }

        if (empty($arrChargableComponents)) {
            $objCancellationHelper->deactivateAccount();
            $objCancellationHelper->scheduleAccountCancellation();
        }

        return $intInvoiceId;
    }

    /** Handles the sending of a  payment refund ticket
     *
     * @return integer - the ticket id (or boolean false if raising the ticket failed).
     */
    private function _sendPaymentRefundTicket()
    {
        // Send a ticket to CSC_BILLING
        $strTicketPoolHandle = 'CSC_BILLING';
        $strMigrationDate = date('d/m/Y', $this->_uxtCompletionDate);
        $strTicketBody = "The migration date for this account has been confirmed as: {$strMigrationDate}\n\n "
        . "Please action a refund for any overpayment on the account. \n ";

        $intTeamId = phplibGetTeamIdByHandle($strTicketPoolHandle);
        $intTicketId = tickets_ticket_add(
            'Script',
            $this->_intServiceId,
            0,
            0,
            'Open',
            SCRIPT_USER,
            $strTicketBody,
            0,
            $intTeamId
        );

        return $intTicketId;
    }

    /**
     * Process account with outstanding charges.
     * Take payment and infom customer
     *
     * @param array $arrOutstandingCharges Array with otstanding charges
     *
     * @return int Cancellation invoice Id or null for Partner accounts
     */
    private function _takeCancellationPayment($arrOutstandingCharges)
    {
        $objCancellationHelper = $this->_objCancellationHelper;
        $objCoreService = $objCancellationHelper->getCoreService();
        $floTotalOutstandingCharges = $arrOutstandingCharges['floTotalOutstandingCharges'];
        $intInvoiceId = null;

        $arrCharges = $arrOutstandingCharges;
        unset($arrCharges['floTotalOutstandingCharges']);

        foreach ($arrCharges as &$arrData) {
            $arrData['floCost'] = number_format($arrData['floCost'], 2, '.', '');
        }

        $arrEmailData = array(
                              'arrEmailData' =>  $arrCharges,
                              'floTotalOutstandingCharges' => number_format($floTotalOutstandingCharges, 2, '.', ''),
                              'intNoticePeriod' => $objCancellationHelper->getNoticePeriod()
                             );

        if ($objCoreService->isPartnerVisp($objCoreService->getIsp())) {

            $objCustomerHelper = Reseller_CustomerHelper::getByServiceId(new Int($this->_intServiceId));
            $intPartnerId = $objCustomerHelper->getPartnerServiceId();
            $intPartnerServiceId = $intPartnerId->getValue();

            $arrBillingService = userdata_service_get($intPartnerServiceId);
            $uxtDueDate = strtotime($arrBillingService['next_invoice']);

            $objTagHelper = ResellerTag_TagHelper::getByUsername(new String($objCoreService->getUsername()));

            $objPaymentScheduler = new COneOffPaymentScheduler($intPartnerServiceId, SCRIPT_USER);

            $strUsername = $objCoreService->getUsername();

            //add each cancellation charge as its own line item.
            foreach ($arrOutstandingCharges as $arrCharge) {

                if (is_array($arrCharge)) {

                    $intScheduledPaymentId = $objPaymentScheduler->addIncVatAmount(
                        $arrCharge['floCost'] * 100,
                        $uxtDueDate,
                        $arrCharge['strName'] . " ({$strUsername}) " .
                        $objTagHelper->getInvoiceItemDescriptionModifier()
                    );
                }
            }

            if (!CeaseCommunications_Registration::getInstance()->canHandleCease($this->intServiceId)) {
                EmailHandler::sendEmail($this->_intServiceId, 'migrating_os_fee', $arrEmailData);
            }

            $objCancellationHelper->closeAccount();

            return $intInvoiceId;
        }

        $objCancellationPayment = new CancellationPayment($this->_intServiceId);

        $intInvoiceId = $objCancellationHelper->raisePendingCancellationInvoice($arrOutstandingCharges);
        $objCancellationPayment->insertBbcrCancellationInvoice($intInvoiceId);

        $objCancellationHelper->downgradeToGenericServices();

        $arrChargeableComponents = $objCancellationHelper->getAccountChargeableComponents();

        $bolFinalPayment = false;

        if (empty($arrChargeableComponents)) {
            $bolFinalPayment = true;
        }

        //Take payment and update status of cancellation payment
        $cancellationPaymentHelper = new CancellationPaymentHelper();
        $objCancellationPayment->takePayment(
            $intInvoiceId, $arrEmailData, $bolFinalPayment, $cancellationPaymentHelper
        );

        if (empty($arrChargeableComponents)) {
            $objCancellationHelper->deactivateAccount();
            $objCancellationHelper->scheduleAccountCancellation();
        }

        return $intInvoiceId;
    }

    /**
     * Process account without outstanding charges and refunds
     *
     * @return bool
     */
    private function _processAccountWithoutCharges()
    {
        $objCancellationHelper = $this->_objCancellationHelper;
        $objCoreService        = $objCancellationHelper->getCoreService();
        $objServiceDefinition  = new Core_ServiceDefinition($objCoreService->getType());

        if (!CeaseCommunications_Registration::getInstance()->canHandleCease($this->intServiceId)) {
            if ($objServiceDefinition->isBusiness()) {
                $strEmailTemplate = 'migrating_without_fee_business';
            } else {
                $strEmailTemplate = 'migrating_without_fee';
            }

            EmailHandler::sendEmail($this->_intServiceId, $strEmailTemplate);
        }

        //If partner destroy account
        if ($objCoreService->isPartnerVisp($objCoreService->getIsp())) {
            $objCancellationHelper->closeAccount();

            return true;
        } else {
            //Downgarde account to generic services product
            $objCancellationHelper->downgradeToGenericServices();

            //Check for chargeable component on account
            $arrChargeableComponents = $objCancellationHelper->getAccountChargeableComponents();

            if (empty($arrChargeableComponents)) {
                //No chargeable components on account
                $objCancellationHelper->deactivateAccount();
                $objCancellationHelper->scheduleAccountCancellation();
            }

            return true;
        }
    }

    /**
     * Marks as cancelled all scheduled payments for BB subscription for specific service id
     *
     * @param int $intServiceId Service Id
     *
     * @return void
     */
    private function _cancelScheduledPayments($intServiceId)
    {
        //Get Internet connection subscription product id
        $intSubscriptionProductComponentInstanceId = CProductComponent::getProductComponentInstance(
            $intServiceId,
            'SUBSCRIPTION',
            CInternetConnectionProduct::getActiveStatusesForProductComponent('SUBSCRIPTION'),
            'INTERNET_CONNECTION'
        );

        if (!empty($intSubscriptionProductComponentInstanceId)) {
            $objPaymentScheduler = new CProductComponentPaymentScheduler(
                $intSubscriptionProductComponentInstanceId, ''
            );
            $objPaymentScheduler->cancelAllOutstandingPayments();
        }
    }

    /**
     * Add ticket for cancellation of the account
     *
     * @param string $strSource        Ticket source
     * @param int    $intAdslServiceId Adsl service id
     * @param string $strErrorClass    Error class
     * @param string $strErrorSubClass Error sub class
     * @param string $strStatus        Ticket status
     * @param int    $intActionerId    Actioner id
     * @param string $strTicketBody    Ticket body
     * @param int    $intReturnToPool  If return to pool
     * @param int    $intTeamId        Team id
     *
     * @return boolean
     */
    protected function addTicket(
        $strSource,
        $intAdslServiceId,
        $strErrorClass,
        $strErrorSubClass,
        $strStatus,
        $intActionerId,
        $strTicketBody,
        $intReturnToPool = '0',
        $intTeamId = 0
    ) {
        return tickets_ticket_add(
            $strSource, $intAdslServiceId, $strErrorClass, $strErrorSubClass,
            $strStatus, $intActionerId, $strTicketBody, $intReturnToPool, $intTeamId
        );
    }

    /**
     * Withdraws all existing contracts for the service after payment has been taken.
     *
     * @param int  $serviceId       Service Id
     * @param bool $wasCancellation Flag to indicate whether it was a cancellation or not
     *
     * @return bool
     */
    public function withdrawExistingContracts($serviceId, $wasCancellation)
    {
        try {

            $serviceDetails = userdata_service_get($serviceId);
            $forename = '';
            $surname = '';

            if (!empty($serviceDetails['user_id'])) {

                $userDetails = userdata_user_get($serviceDetails['user_id']);

                $forename = $userDetails['forenames'];
                $surname = $userDetails['surname'];
            }

            $contractsClient = BusTier_BusTier::getClient('contracts')
                ->setServiceId((int) $serviceId)
                ->setRaiseServiceNotice(false);

            $criteria = array(
                'status' => 'ACTIVE'
            );

            $activeContracts = $contractsClient->getContracts($criteria);

            foreach ($activeContracts as $contract) {

                if ($contractsClient->withdrawContract($contract, 'BBCR Migration Out')) {

                    // if we are cancelling we need to indicate that no futher payment take,
                    // rather than no payment taken at all
                    if ($wasCancellation) {

                        $message = "Dear " . $forename . " " . $surname
                            . ", <br><br>This question is confirmation that your "
                            . $contract->getName() . "  has been withdrawn. "
                            . "No further payment will be taken and no further action is required by you.";

                    } else {

                        $message = "Dear " . $forename . " " . $surname
                            . ", <br><br>This question is confirmation that your "
                            . $contract->getName() . "  has been withdrawn. "
                            . "No payment will be taken and no further action is required by you.";
                    }

                    $this->addTicket(
                        'Script', $serviceId, 0, 0, 'Closed', '', $message, 0
                    );
                }
            }

            return true;

        } catch (Exception $exception) {

            error_log('Exception thrown while withdrawing contracts.\n Message : '.$exception->getMessage());

            return false;
        }

    }

    /**
     * Method to obtain ChangePlan from ProductChangePlanClient
     *
     * @param int $componentId Component id
     *
     * @return \Plusnet\ProductChangePlanClient\Entity\ChangePlan
     */
    public function getChangePlan($componentId)
    {
        $changePlan = null;

        if (!empty($componentId)) {

            try {

                $productChangePlanClient = BusTier_BusTier::getClient('productChangePlan');

                $changePlan = $productChangePlanClient->removeService(
                    \Plusnet\ProductChangePlanClient\ChangeChannels::BROADBAND_MIGRATION_AWAY_COMPLETE,
                    (int) $this->_intServiceId,
                    (int) $componentId
                );

            } catch (Exception $exception) {

                error_log('Exception thrown while obtaing Change Plan.\n Message : '.$exception->getMessage());
            }
        }

        return $changePlan;
    }

    /**
     * Method to execute ProductChangePlan to write-off / cancell broadband contract (for new products)
     *
     * @param obj $changePlan  \Plusnet\ProductChangePlanClient\Entity\ChangePlan
     * @param int $componentId Broadband component id
     * @param int $invoiceId   Cancellation invoice Id
     *
     * @return bool
     */
    public function executeChangePlan($changePlan, $componentId, $invoiceId)
    {
        $result = false;

        if (!empty($changePlan)) {

            try {
                $options = array();

                if (!empty($invoiceId)) {

                    $invoiceItems = financial_sales_invoice_items_get($invoiceId);

                    if (is_array($invoiceItems)) {

                        foreach ($invoiceItems as $invoiceItem) {

                            if ($invoiceItem['intComponentId'] == $componentId) {

                                $options['invoiceItemId'] = $invoiceItem['sales_invoice_item_id'];
                                $splitVat = CFinancialHelper::splitVat($invoiceItem['value'] * 100);
                                $options['preTaxValue']   = $splitVat['exvat'];

                                break;
                            }
                        }
                    }
                }

                // execute the plan
                $changePlan->execute($options);

                $result = true;

            } catch (Exception $exception) {

                error_log('Exception thrown while executing Change Plan.\n Message : '.$exception->getMessage());
            }
        }

        return $result;
    }

    /**
     * Cancels cassation charge
     *
     * @param int $intServiceId Service id
     *
     * @return void
     */
    private function _cancelCessationCharge($intServiceId)
    {
        //cancel cessation charge
        $objCessationChargeHelper = new Financial_CessationChargeHelper($intServiceId);

        $intCancellationCharge = $objCessationChargeHelper->getCessationCharge();
        $floCancellationChargeExcVat = $intCancellationCharge / 100;
        $arrCancellationChargeIncVat = CFinancialHelper::calcVat($floCancellationChargeExcVat);
        $floCancellationCharge = $arrCancellationChargeIncVat['incvat'];

        $objCessationChargeHelper->cancel(SCRIPT_USER);

        if ($floCancellationCharge > 0) {
            $strTicketText = "&pound;".number_format($floCancellationCharge, 2)
                             ." taken for written off cessation charge";
            $this->addTicket('Internal', $intServiceId, 0, 0, 'Closed', SCRIPT_USER, $strTicketText);
        }
    }

    /**
     * Cancels all pending discounts on account
     *
     * @param int $intServiceId Service id
     *
     * @return void
     */
    private function _cancelDiscounts($intServiceId)
    {
        $objDiscountManager = Retention_DiscountManager::getInstance();
        $objDiscountsArray = $objDiscountManager->getServiceDiscounts($intServiceId, 'pending');

        // $objDiscountsArray implements AccessArray
        if ( count($objDiscountsArray) > 0 ) {
            $objDiscountManager->saveDiscountArray(
                $objDiscountsArray->cancelAll('migration', SCRIPT_USER, 'Cancelled from: ' . __FILE__)
            );
        }
    }

    /**
     * Checks that next invoice date on account is a valid date for outstanding fees calculations.
     * It shouldn't be more than 31 days in the future and less than 31 days in the past.
     *
     * @return boolean
     */
    private function _isNextInvoiceDateValidForCalculations()
    {
        $objCoreService = $this->_objCancellationHelper->getCoreService();

        try {

            $objNextInvoiceDate = $objCoreService->getNextInvoiceDate();
        } catch (InvalidArgumentException $objException) {
            //Next invoice date stored in DB for this account
            //is not valid date to create I18n_Date object (i.e '9999-09-09' or '0000-00-00')
            return false;
        }

        $objNextInvoiceDate->resetDay();
        $uxtNextInvoiceDate = $objNextInvoiceDate->getTimestamp();

        $objNow = I18n_Date::now();
        $objNow->resetDay();
        $objMinNextInvoiceDate = $objNow->getModified('-31', I18n_Date::DAYS);
        $objMaxNextInvoiceDate = $objNow->getModified('+31', I18n_Date::DAYS);

        $objMinNextInvoiceDate->resetDay();
        $objMaxNextInvoiceDate->resetDay();

        $uxtMinNextInvoiceDate = $objMinNextInvoiceDate->getTimestamp();
        $uxtMaxNextInvoiceDate = $objMaxNextInvoiceDate->getTimestamp();

        if ($uxtNextInvoiceDate >= $uxtMinNextInvoiceDate
            && $uxtNextInvoiceDate <= $uxtMaxNextInvoiceDate
        ) {
            return true;
        }

        return false;
    }

    /**
     * Adds service event to mark account as processed by HTT automation
     *
     * @param int $intInvoiceId ID of cancelation invoice or invoice selected for automated refund
     *
     * @return int Service event id
     */
    private function _addHttServiceEvent($intInvoiceId = null)
    {
        $intInvoiceId = empty($intInvoiceId) ? '' : $intInvoiceId;

        $intServiceEventId = userdata_service_event_add(
            $this->_intServiceId,
            SERVICE_EVENT_HTT_AUTOMATION,
            '',
            '',
            '',
            $intInvoiceId
        );

        return $intServiceEventId;
    }
}
