<?php
require_once (SECURE_TRANSACTION_ACCESS_LIBRARY);
require_once '/local/data/mis/database/database_libraries/components/CProductComponent.inc';
require_once COMMON_LIBRARY_ROOT . 'class_libraries/Retention/DiscountManager.class.php';
require_once CLASS_ACCESS_LIBRARY;   // safeguard; required for contract stuff

/**
 * C_Process_Migration
 * Class responsible for processing migrated out customers
 * At the point of creation, used in BBCR script
 * (C_BBCR_MigrationOut.php) and Tiscali migration
 * script (DatabaseAdmin/scripts/migration/migrate_out_tiscali_customers.php)
 *
 * @version $id$
 * <AUTHOR> <mjagie<PERSON><EMAIL>>
 */
class C_Process_Migration {

    /**
     * intServiceID
     *
     * @var int
     * @access private
     */
    var $intServiceID;

    /**
     * dteCompletionDate
     * completion date of migration out
     *
     * @var date
     * @access private
     */
    var $dteCompletionDate;

    /**
     * arrGlobalComponentConfigurators
     *
     * @var array
     * @access private
     */
    var $arrGlobalComponentConfigurators;

    /**
     * arrWorklog
     * Holds messages generated during the cancellation process
     *
     * @var array
     * @access private
     */
    var    $arrWorklog = array();

    /**
     * intDestinationAccountType
     *
     * @var int
     * @access private
     */
    var $intDestinationAccountType;

    /**
     * $intDestinationHomePhone
     * @var int
     */
    private $intDestinationHomePhone = null;

    /**
     * strMyID
     *
     * @var int
     * @access private
     */
    var $strMyID;

    /**
     * C_Process_Migration
     * Class constructor
     *
     * @param int $intServiceID
     * @param date $dteCompletionDate
     * @param array $arrGlobalComponentConfigurators
     * @access public
     * @return void
     */
    function C_Process_Migration($intServiceID, $dteCompletionDate, $arrGlobalComponentConfigurators)
    {
        global $my_id;

        if(empty($intServiceID) || !is_numeric($intServiceID))
        {
            return false;
        }

        if(empty($dteCompletionDate))
        {
            $dteCompletionDate = date('Y-m-d');
        }

        $this->setServiceID($intServiceID);
        $this->setCompletionDate($dteCompletionDate);
        $this->setGlobalComponentConfigurators($arrGlobalComponentConfigurators);
        $this->setMyID($my_id);
    }

    /**
     * setMyID
     *
     * @param string $strMyID
     * @access public
     * @return void
     */
    function setMyID($strMyID)
    {
        $this->strMyID = $strMyID;
    }

    /**
     * getMyID
     *
     * @access public
     * @return string
     */
    function getMyID()
    {
        return $this->strMyID;
    }

    /**
     * setServiceID
     *
     * @param int $intServiceID
     * @access public
     * @return void
     */
    function setServiceID($intServiceID)
    {
        if(empty($intServiceID) || !is_numeric($intServiceID))
        {
            return false;
        }

        $this->intServiceID = $intServiceID;
        return true;
    }

    /**
     * getServiceID
     *
     * @access public
     * @return int
     */
    function getServiceID()
    {
        return $this->intServiceID;
    }

    /**
     * setCompletionDate
     *
     * @param date $dteCompletionDate
     * @access public
     * @return void
     */
    function setCompletionDate($dteCompletionDate)
    {
        if(empty($dteCompletionDate) || !(preg_match('/[0-9]{2}\/[0-9]{2}\/[0-9]{4}/', $dteCompletionDate)))
        {
            return false;
        }

        $this->dteCompletionDate = $dteCompletionDate;
        return true;
    }

    /**
     * getCompletionDate
     *
     * @access public
     * @return date
     */
    function getCompletionDate()
    {
        return $this->dteCompletionDate;
    }

    /**
     * setGlobalComponentConfigurators
     *
     * @param array $arrGlobalComponentConfigurators
     * @access public
     * @return void
     */
    function setGlobalComponentConfigurators($arrGlobalComponentConfigurators)
    {
        $this->arrGlobalComponentConfigurators = $arrGlobalComponentConfigurators;
    }

    /**
     * getGlobalComponentConfigurators
     *
     * @access public
     * @return array
     */
    function getGlobalComponentConfigurators()
    {
        return $this->arrGlobalComponentConfigurators;
    }

    /**
     * configCBCComponent
     *
     * @param int $intServiceID
     * @access public
     * @return int
     */
    function configCBCComponent($intServiceID)
    {
        $intCBCFlexID = false;

        $intCBCFlexComponentID = GetCBCFlexActiveComponentID($intServiceID);

        if ($intCBCFlexComponentID > 0)
        {
            $intCBCFlexID = ConfigFlexGetCBCFlexID($intCBCFlexComponentID);
        }
        return $intCBCFlexID;
    }

    /**
     * checkProduct
     *
     * @param array $arrCurrentProduct
     * @param array $arrService
     * @access public
     * @return array
     */
    function checkProduct($arrCurrentProduct, $arrService)
    {
        $arrReturn['intErrorCode'] = '0';

        if($arrCurrentProduct['bolSDSL'])
        {
            // This is an SDSL account, and shouldn't be migrated out
            $arrReturn['strErrorMessage'] = get_class($this) . " not interested in this record, the account is SDSL. Skipping.\n";
            $arrReturn['intErrorCode'] = '2';

        }
        elseif(!$arrCurrentProduct || $arrService['status'] != 'active')
        {
            // The user doesn't have an ADSL product or is inactive, so they must have been ceased already
            $arrReturn['strErrorMessage'] = get_class($this) . " not interested in this record, not ADSL or not active. Skipping.\n";
            $arrReturn['intErrorCode'] = '2';

        }

        return $arrReturn;

    } //end function checkProduct($arrCurrentProduct, $arrService)

    /**
     * Select an account type to switch to based on the components.
     *
     * @param array $arrService Array of service
     */
    function selectDestinationAccountType($arrService)
    {
        $arrCurrentProduct = product_get_service($arrService['type']);
        $objWlrProduct = CWlrProduct::getWlrProductFromServiceId($this->intServiceID);
        $intGenericServiceDefinitionId = (int) self::getGenericServicesSdi($arrCurrentProduct);

        if ($intGenericServiceDefinitionId == 0) {
            throw new Exception('No generic service definition id for visp '.$arrCurrentProduct['isp']);
        }

        if ($objWlrProduct instanceof CWlrProduct) {

            // If we've got a wlr product, and the isp in question has an account designed for just phone,
            // then we can use this one instead of generic services..
            $intServiceDefinitionId = $this->getWlrOnlySdi($arrCurrentProduct);

            if (empty($intServiceDefinitionId)) {

                $this->checkWlrComponentAndSetDestinationHomePhoneIfNeeded($objWlrProduct,
                                                                         $intGenericServiceDefinitionId,
                                                                         $arrService['isp']);
            } else {

                $this->checkWlrComponentAndSetDestinationHomePhoneIfNeeded($objWlrProduct,
                                                                   $intServiceDefinitionId,
                                                                   $arrService['isp']);

                $this->intDestinationAccountType = $intServiceDefinitionId;
                $this->arrWorkLog[] = 'Account to be converted to a WLR only product';
                return;
            }

        }

        $this->intDestinationAccountType = $intGenericServiceDefinitionId;
        $this->arrWorkLog[] = 'Account to be converted to a generic services account';
    }

    /**
     * check if the current wlr component is compatible with new product if not
     * we will set them to a deafult value
     *
     * @param CProduct $objWlrProduct
     * @param int      $intServiceDefinitionId
     * @param string   $strIsp
     */
    private function checkWlrComponentAndSetDestinationHomePhoneIfNeeded($objWlrProduct, $intServiceDefinitionId, $strIsp)
    {
        $bolWlrComponent = products_check_product_has_component($intServiceDefinitionId,
                                    $objWlrProduct->getComponentID());
        if (!$bolWlrComponent) {

            // If we're unbundling a Greenbee account with wlr, there's an unbundled version of the phone component that
            // we need to change to.
            // If we've got 'home phone essential', then we should change to the current evenings and weekends product.
            if ('greenbee' == $strIsp) {

                $this->intDestinationHomePhone = WLR_GREENBEE_PHONE_COMPONENT_ID;
                $this->arrWorkLog[] = 'Changing greenbee phone product to the unbundled version';
            } elseif ( COMPONENT_WLR_ESSENTIAL == $objWlrProduct->getComponentTypeID()) {

                $this->intDestinationHomePhone = COMPONENT_WLR_PN_TALK_EVENING_WEEKEND;
                $this->arrWorkLog[] = 'Home Phone to be converted to evening and weekends';
            }
        }
    }

    /**
     * Get generic servives sdi for an ISP
     * @param $arrCurrentProduct
     * @return int
     */
    public static function getGenericServicesSdi($arrCurrentProduct)
    {
        $db = get_named_connection_with_db('product_reporting');

        $strQuery = "
SELECT sd.service_definition_id
FROM products.service_definitions sd
INNER JOIN products.tblServiceDefinitionReportGroup sdrg
    ON sd.service_definition_id=sdrg.intServiceDefinitionID
INNER JOIN products.tblServiceDefinitionReportSector sdrs
    ON sdrs.intReportSectorID = sdrg.intReportSectorID
WHERE sd.isp = '{$arrCurrentProduct['isp']}'
AND IF (sd.isp = 'johnlewis', sd.name LIKE '%Basic%', sd.name LIKE '% Generic Services Product')
AND sdrs.vchReportSector = '{$arrCurrentProduct['type']}'
";

        return PrimitivesResultGet(PrimitivesQueryOrExit($strQuery, $db), 'service_definition_id');
    }

    /**
     * Get a WLR-only SDI for the given ISP
     *
     * This only applies to Greenbee at the moment, as it's the only isp that has a product specifically for
     * phone only, but this function will still work if we add more in the future.
     *
     * May need updating in future to differentiate between res/bus products.
     *
     * @param array $arrCurrentProduct  The current product details
     *
     * @return integer - the service definition of the wlr only product, otherwise empty
     */
    function getWlrOnlySdi($arrCurrentProduct)
    {
        $db = get_named_connection_with_db('product_reporting');

        $strQuery = "SELECT sd.service_definition_id
                     FROM products.service_definitions sd
                     INNER JOIN products.service_component_config scc USING(service_definition_id)
                     INNER JOIN products.tblServiceComponentProduct scp
                       ON scp.intServiceComponentID = scc.service_component_id
                     INNER JOIN products.tblServiceComponentProductType scpt
                       USING(intServiceComponentProductTypeID)
                     LEFT JOIN products.adsl_product ap ON ap.service_definition_id = sd.service_definition_id
                     WHERE sd.isp = '" . $arrCurrentProduct['isp'] . "'
                       AND sd.minimum_charge = 0 AND sd.end_date IS NULL
                       AND scc.default_quantity = 1 AND scpt.vchHandle = 'WLR'
                       AND ap.service_definition_id IS NULL";

        return PrimitivesResultGet(PrimitivesQueryOrExit($strQuery, $db), 'service_definition_id');
    }

    function getFreeAccountSdi($arrCurrentProduct)
    {
        $db = get_named_connection_with_db('product_reporting');

        $strNotBusiness = ($arrCurrentProduct['type'] == 'business') ? '' : 'NOT';
        $strNotPnuk = (strpos($arrCurrentProduct['name'], 'Plus.Net.Uk') !== FALSE) ? '' : 'NOT';
        $strSql = "SELECT sd.service_definition_id
                   FROM service_definitions sd
                   WHERE sd.type = 'free'
                   AND sd.signup_via_portal = 'Y'
                   AND sd.end_date IS NULL
                   AND sd.name NOT LIKE '%Service%'
                   AND sd.name $strNotBusiness LIKE '%Business%'
                   AND sd.name $strNotPnuk LIKE '%Plus.Net.Uk'
                   AND sd.minimum_charge = 0
                   AND sd.isp = '" . mysql_real_escape_string($arrCurrentProduct['isp']) . "'";
        return PrimitivesResultGet(PrimitivesQueryOrExit($strSql, $db), 'service_definition_id');
    }

    /**
     * findErrorsInWorklog
     * determines in worklog contains any errors and higlights them
     *
     * @access public
     * @return bool
     */
    function findErrorsInWorklog()
    {
        $bolError = false;

        foreach($this->arrWorklog as $intIdx => $strMessage)
        {
            if(strstr($strMessage, 'ERROR:'))
            {
                $this->arrWorklog[$intIdx] = "<font color='red'>".str_replace('ERROR:', '', $strMessage)."</font>";
                $bolError = true;
            }
        }

        return $bolError;
    }

    /**
     * processWorklog
     *
     * @param string $strAppendCustomerNote
     * @access public
     * @return array
     */
    function processWorklog($strAppendCustomerNote)
    {
        $bolError = $this->findErrorsInWorklog();
        $strErrors = implode("<br>\n", $this->arrWorklog);
        $arrReturn['worklog'] = $this->arrWorklog;

        // Ticket the account
        if($bolError)
        {
            // Something went wrong - alert the outbound migrations team
            // TODO: find out valid and unused error code (if 10 is wrong)
            $arrReturn['intErrorCode'] = 10;
            tickets_ticket_add('Script', $this->getServiceID(), 0, 0, 'Open', 0, $strErrors, '', PHPLibTeamIdGetByName('CSC - Customer Services'));
        }
        else
        {
            // Everything was OK - just ticket the account
            tickets_ticket_add('Internal', $this->getServiceID(), 0, 0, 'Closed', SCRIPT_USER, $strErrors);
            //add customer note
            $strBody = 'The Customer migrated out on '.$this->getCompletionDate();

            if (strlen($strAppendCustomerNote) > 0)
            {
                $strBody .= ". $strAppendCustomerNote";
            }

            customer_notes_add_note($this->getServiceID(), $strBody, $this->strMyID);
        }

        return $arrReturn;
    }

    /**
     * activateNewComponents
     *
     * @param $arrAddComponents
     * @access public
     * @return void
     */
    function activateNewComponents($arrAddComponents)
    {
        $strComma = '';
        $strAddedComponents = '';

        foreach($arrAddComponents as $intAddServiceComponentID)
        {
            $intComponentID = userdata_component_add ($this->getServiceID(), $intAddServiceComponentID, -1, '', 'unconfigured');

            $arrComponentDefinition = product_get_component($intAddServiceComponentID);
            $strAddedComponents    .= $strComma.$arrComponentDefinition['name'];
            $strComma = ',';
            if (isset($this->arrGlobalComponentConfigurators[$intAddServiceComponentID])
                && $this->arrGlobalComponentConfigurators[$intAddServiceComponentID])
            {
                $this->arrGlobalComponentConfigurators[$intAddServiceComponentID]($intComponentID, "auto_configure");
            }
        }

        $this->arrWorklog[] = "Added the following components: $strAddedComponents";
    }

    /**
     * changeDestinationAccountType
     *
     * @param array $arrService
     * @param int $intTypeToCheckForHardware
     * @param int $intCBCFlexID
     * @access public
     * @return array
     */
    function changeDestinationAccountType($arrService, $intTypeToCheckForHardware, $intCBCFlexID)
    {
        $arrSwapComponents = array();
        //if there is still an old WLR and we want change it to a new one
        if (is_object(CWlrProduct::getWlrProductFromServiceId($this->getServiceID())) && !is_null($this->intDestinationHomePhone))
        {
            $arrSwapComponents = array('add' => array($this->intDestinationHomePhone));
        }
        // PID70872,  do not process changes on BT
        $run_conditions = true; // Was an optional parameter before PID70872 changes done. Set to previous default
        $service_event_id=''; // Was an optional parameter before PID70872 changes done. Set to previous default
        userdata_service_account_type_change($this->intServiceID, $this->intDestinationAccountType, $arrSwapComponents, $service_event_id, $run_conditions, array('placeOrderToBT' => false));
        userdata_service_log_type_change($this->intServiceID, $arrService['type'], $this->intDestinationAccountType);

        // Destroy the lost components
        C_BBCR_MigrationOut::CancellationDestroyGroupwareComponents($this->getServiceID());
        C_BBCR_MigrationOut::CancellationDestroyAssociatedADSLUpgradeAccount($arrService['type'],
                                                                             $intTypeToCheckForHardware,
                                                                             $this->getServiceID());
        C_BBCR_MigrationOut::CancellationReturnADSLStock($intTypeToCheckForHardware);
    }
}
