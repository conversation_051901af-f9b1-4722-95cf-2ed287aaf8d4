<?php
/**
 * Finds an account record for a service's user
 *
 * @param int     $user_id            User Id
 * @param boolean $get_new            Flag to indicate to use cache or not (default false)
 * @param string  $strTransactionName Db Transaction name
 *
 * @return array Account details
 */
function split_userdata_account_get_by_user($user_id, $get_new=false, $strTransactionName = 'TRANSACTION_ACCOUNT_GET_BY_USER')
{
    static $arrCachedAccountsByUser = array();

    $intMaxRecords = 20000;
    $intCachedRecords = count($arrCachedAccountsByUser);

    if (isset($arrCachedAccountsByUser[$user_id]) AND $get_new == false) {

        return $arrCachedAccountsByUser[$user_id];
    }

    try {

        $intAccountId = Core_Account::getAccountIdByUserId($user_id, $strTransactionName);
        $objAdaptor = Db_Manager::getAdaptor('Core', $strTransactionName);
        $arrAccountDetails = $objAdaptor->getAccountDetails($intAccountId);

        if ($arrAccountDetails['expiry_date'] instanceof I18n_Date) {

            $arrAccountDetails['expiry_date'] = $arrAccountDetails['expiry_date']->toMysql();
        }

    } catch (Exception $objException) {

        error_log(
            "split_userdata_account_get_by_user: Getting account details for UserID: $user_id failed - "
            . $objException->getMessage()
        );

        $arrAccountDetails = array();
    }

    try {

        Db_Manager::commit($strTransactionName);

    } catch (Exception $objException) {

        error_log("split_userdata_account_get_by_user: failed to commit transaction - " . $objException->getMessage());
    }

    if (!isset($arrCachedAccountsByUser[$user_id]) AND $intCachedRecords >= $intMaxRecords) {

        array_shift($arrCachedAccountsByUser);
    }

    $arrCachedAccountsByUser[$user_id] = $arrAccountDetails;

    return $arrAccountDetails;
}