<?php
require_once('/local/codebase2005/modules/Framework/Libraries/bootstrap.inc.php');

configDefineIfMissing('GROUP_ENHANCED_CARE', 'BOT - Enhanced Care');

function tickets_sendDesktopClientCustomerActionNeededAlert($intServiceId, $intTicketId, $bolNewTicket)
{
    require_once('/local/data/mis/database/application_apis/PnDesktop/SoapClient.class.php');

    $arrService = userdata_service_get($intServiceId);

    if (empty($arrService)) {
        return false;
    }

    if ('plus.net' != $arrService['isp']) {
        return true;
    }
    $strAuthRealm = userdataGetFrameworkAuthRealm(
        $intServiceId,
        $arrService['username']
    );

    if (empty($strAuthRealm)) {
        return false;
    }

    $strSender = PnDesktop_SoapClient::PND_DEFAULT_SENDER;
    $strRecipient = $arrService['username'].'@'.$strAuthRealm;

    if ($bolNewTicket) {
        $strTitle = "New ticket #{$intTicketId} is awaiting your response";
        $strBody  = "Ticket #{$intTicketId} has been sent to you and is awaiting your response. ".
            "Please visit our portal to review the ticket.";
    } else {
        $strTitle = "Ticket #{$intTicketId} is awaiting your response";
        $strBody  = "Ticket #{$intTicketId} has been returned to you and is awaiting your response. ".
            "Please visit our portal to review the ticket.";
    }
    $bolMustSendImmediate = false;

    $objDesktopClent = new PnDesktop_SoapClient(
        $arrService['username'],
        $strAuthRealm
    );
    $uxtNow = time();
    $uxt24hours = $uxtNow + (24*60*60);
    $strTicketLink= "https://www.plus.net/my.html?action=questions&helpheader=questions#".md5($intTicketId);
    $intNotificationType = PnDesktop_SoapClient::PND_TYPE_INFO;

    $strNotificationXml = $objDesktopClent->generate_send_notification_xml(
        $strBody,
        $strTitle,
        $bolMustSendImmediate,
        $uxtNow,
        $uxt24hours,
        $strTitle,
        $intNotificationType,
        $strTicketLink
    );

    $objDesktopClent->send_notification($strRecipient, $strNotificationXml);

    return true;
}

 /**
 * Add an entry into the ticket contact table
 *
 * @param int    $ticket_id            Ticket id associated with the contact
 * @param string $owner_id             ID of the user being assigned to ('0' if not)
 * @param string $body                 Text associated with the contact
 * @param int    $error_class          ID of the error class
 * @param int    $error_subclass       ID of the error subclass
 * @param int    $team_id              ID of the team being assigned to (0 if not, -1 if customer)
 * @param string $status               New status of the ticket ('' if no change)
 * @param string $actioner_id          ID of the staff member updating the ticket (0 if customer)
 * @param string $datestamp            Optional MySQL formatted date string, defaults to now
 * @param bool   $bolTakeOffHold       TRUE if the ticket is to come off hold with the addition of this contact.
 * @param string $strCauseHandle       Optional handle of the cause of the ticket, defaults to 'Other'
 * @param string $autoComplaintClosure Auto complaint closure
 * @param bool   $isSms                Is Sms reply
 * @param int    $complaintResolvedReasonId Complaint resolved Id
 * @param bool   $isInternalOnly       Internal comment (do not display to customer)
 *
 * @throws Exception
 *
 * @return int Identifier for the contact
 */
function split_tickets_ticket_contacts_add(
    $ticket_id,
    $owner_id,
    $body,
    $error_class,
    $error_subclass,
    $team_id,
    $status,
    $actioner_id,
    $datestamp = null,
    $bolTakeOffHold = true,
    $strCauseHandle = '',
    $autoComplaintClosure = '',
    $isSms = false,
    $complaintResolvedReasonId = null,
    $isInternalOnly = false
) {
    global $global_db_src, $my_id;
    $PHP_SELF = $_SERVER['PHP_SELF'];

    // Get the current state of the ticket
    $ticket = tickets_ticket_get($ticket_id);
    $contact_history = tickets_ticket_history_get($ticket_id);
    $ticket_id   = addslashes($ticket_id);
    $owner_id    = addslashes($owner_id);
    $body        = addslashes($body);
    $actioner_id = addslashes($actioner_id);
    $status      = addslashes($status);
    $team_id     = addslashes($team_id);

    $service_id = $ticket['service_id'];

    $service = userdata_service_get($service_id);
    $account = userdata_account_get_by_user($service['user_id']);

    $internal_only = 0;
    if( $isInternalOnly){
        $internal_only = 1;
    }

    // Check for empty owner_id and actioner_id
    if (empty($actioner_id)) {
        $actioner_id = 0;
    }

    if (empty($owner_id)) {
        $owner_id = 0;
    }

    // Make the contact stand out if the customer's in debt at the time.
    if ($account['balance'] > 0.0) {
        $body = '<SPAN CLASS="stand-out">' . $body . '</SPAN>';
    }

    if ($datestamp == null) {
        $datestamp = date('Y-m-d H:i:s');
    }

    // If some passed parameters are 0,
    // they should be set to the same value as the previous ticket_contact
    if ($error_class == 0) {
        $error_class = $ticket['error_class'];
    }
    if ($error_subclass == 0) {
        $error_subclass = $ticket['error_subclass'];
    }

    //Save status for Logging functionality
    $strLogStatus = $status;
    $strOrigStatus = $status;

    // If status if blank, no change.
    if (strlen($status) == 0) {
        $status = $ticket['status'];

        //If Question action = 'Actioned' then change to Ammend for logging
        if ($status == 'Actioned') {
            $strLogStatus = 'Ammend';
        } else {
            $strLogStatus = $status;
        }
    }
    // If the team *and* owner are 0, keep them the same as the previous contact
    if ($owner_id == '0' && intval($team_id) == 0) {
        $owner_id = $ticket['owner_id'];
        $team_id  = $ticket['team_id'];
    }
    //set a flag to hold if the Question was repoened
    $strReopenedTicket = 'FALSE';

    $boolCustomer  = TicketClient_TicketClient::checkIfActionerIsEndUser(
        $actioner_id
    );
    $bolRedirectToJeopardy = false;
    $bolTicketRaisedByCustomer = false;
    if ($ticket['ticket_source'] == 'Portal') {
        $bolTicketRaisedByCustomer = true ;
    }
    $bolAllowedIsps = false;
    $arrAllowedIsps = array('plus.net','freeonline','force9','madasafish','metronet','waitrose','greenbee','vodafone');
    if (in_array($service['isp'], $arrAllowedIsps)) {
        $bolAllowedIsps = true;
    }
    // Should ticket go to jeopardy pool
    if ($bolTicketRaisedByCustomer && $boolCustomer && $status != 'Closed' && $bolAllowedIsps) {
        $objServiceDefinition = new Core_ServiceDefinition($service['type']);
        $bolBusiness = $objServiceDefinition->isBusiness();

        if (!$bolBusiness) {
                $bolRedirectToJeopardy = TicketClient_TicketJeopardyAction::shouldSendToJeopardy($team_id, $contact_history);
            if ($bolRedirectToJeopardy) {
                $team_id = phplibGetTeamIdByHandle('CSC_JEOPARDY');
            }
        }
    }
    if ($boolCustomer && $ticket['status'] == 'Actioned' &&
        $status != 'Closed' && $owner_id == 0 && $bolRedirectToJeopardy == false) {
        //Fetch the return team id.
        $team_id = PHPLibReturnTeamIdGetByTeamId($team_id);

        //set the flag to true
        $strReopenedTicket = 'TRUE';
    }

        // The team ID can never be set to 0, so work out what it should be
    if (intval($team_id) == 0) {
        // Use the new owner ID to figure it out.
        if ($owner_id == '0') {
            // The Question is in Limbo! (i.e. it's going to end up not being owned by anyone).
            // Send it to the Call Centre.
            $team_id = 1;
        } else {
            if ($owner_id == $ticket['service_id']) {
                // The Question is owned by the Customer. The customers pool is -1.
                $team_id = -1;
            } else {
                // Set the team ID to the owner's team.
                $employee = phplib_employee_profile_get($owner_id);

                // team_id really can never be 0, so make sure this isn't
                if (intval($employee['team_id']) == 0) {
                    $team_id = 1;
                } else {
                    $team_id = $employee['team_id'];
                }
            }
        }
    } elseif (intval($team_id) == -1) {
        $owner_id = $service_id;
    }

        /**
         * PnDesktop is on hold
         */
        /*
        if (-1 != intval($ticket['team_id']) && intval($team_id) == -1 && strtolower($status) != 'closed') {
        tickets_sendDesktopClientCustomerActionNeededAlert($service_id, $ticket_id, FALSE);

        }
        */

        // ALPHA_DEV 1737 Faults Automation : Owain Michael Davies : Thu 8th Sept 2005
        // P53214 rewriting this part as it is not working (or even "never worked")
    if ('closed' == strtolower($status)) {
        //we are using here BusinessTierSession
        require_once(COMMON_LIBRARY_ROOT . '/common_application_apis/BusinessTier.php');
        require_once(COMMON_API_ROOT.'FaultsFriacoComponentSchRemoveManager.php');

        //is this ticket related to faults?
        $bolIsFaultTicket = false;

        $objBTSession = new BusinessTierSession('PLUSNET');
        $arrTickets=array($ticket_id);
        $arrFaultStatus = $objBTSession->getFaultStatusByTicketID($arrTickets);

        if (!empty($autoComplaintClosure)) {
            // If ticket is being closed, resolve open complaints
            if (!class_exists('Auth_BusTierSession')) {
                require_once('/local/data/mis/portal_modules/phplib/BusTierSession.class.php');
            }

            $objTicket = TicketClient_Ticket::get($ticket_id);

            if ($objTicket->isOpenComplaint()) {
                // Add contact to ticket to show auto-resolution of complaint by system user
                ticket_tickets_contacts_add(
                    $ticket_id,
                    0,
                    "Complaint has been autoresolved by $autoComplaintClosure.",
                    0,
                    0,
                    0,
                    "",
                    0
                );

                try {
                    $objBusinessActor = Auth_BusTierSession::getCurrentBusinessActor();
                    $objTicket->resolveComplaint($objBusinessActor);
                } catch (Exception $e) {
                    $strContent = 'Please refer to the error log on the server where exception was thrown';
                    $intProblemId = pt_raise_autoproblem(
                        'AuthObjectNotAvailable',
                        'AuthObject is not availible. Failed to resolve complaint on ticket closure',
                        $strContent
                    );

                    throw new Exception('File:'.__FILE__.':'.__LINE__.": Failed to resolve open complaint on closure of ticket $ticket_id. Raised P2: $intProblemId.");
                }
            }
        }

        if (!empty($arrFaultStatus)) {
            $bolIsFaultTicket = true;
        }

        if (true === $bolIsFaultTicket) {
            //send message to java to close fault too.
            $objBTSession->closeFaultByTicketID($service_id, $ticket_id);
            $strError = $objBTSession->getError();
            if (!empty($strError)) {
                 throw new Exception("File:".__FILE__.":".__LINE__.": Failed to close fault when closing ticket $ticket_id: $strError\n");
            }
            //this part was added earlier with ALPHA_DEV 1737 Faults Automation
            $objFriaco = new FaultsFriacoComponentSchRemoveManager($service_id);
            $objFriaco->scheduleRemovalOfFriacoComponent();
        }
    }

        // PROBLEM #3590 - CSC teams made obsolescent
        // this is a list of team IDs that are unused and their mappings to other teams
        // add more if necessary.  this should have been done properly to start with.
        // UPDATE: some teams /were/ wanted by CSC, reinstating them all.  (#3627)
        /*
        $team_mapping_array = array (
        11 => 1, // STC -> CSC
        14 => 1, // TL -> CSC
        13 => 1, // TL-coords -> CSC
        12 => 1, // Bizsupp -> CSC
        22 => 1, // CSC Care -> CSC
        );
        if (isset($team_mapping_array[$team_id])) // don't use in_array to check keys. -.-
        $team_id = $team_mapping_array[$team_id];
        */

        //$connection = get_named_connection('tickets'); REMOVED BY rharvey - Useless code!

/*
        // This section deals with contact sequences. These aren't in the
       database yet, so the code is commented out until the database can support it.

        // Get the next Sequence Position.
        $sequence_query = "SELECT count(*) + 1 FROM ticket_contacts WHERE ticket_id='$ticket_id'";

        $result = mysql_query($sequence_query, $connection);

        $sequence_position = mysql_result($result, 0, 0);

        $query = "INSERT INTO ticket_contacts (
                          ticket_id, body,        date_raised,
                          owner_id,  error_class, error_subclass,
                          team_id,   status,      actioner_id, sequence_position)
                   VALUES (
                          '$ticket_id', '$body',        '$datestamp',
                          '$owner_id',  '$error_class', '$error_subclass',
                          '$team_id',   '$status',      '$actioner_id', '1')";
*/

        // If a Question that is currently on hold is appended,
        // then it comes off hold and returns to its original pool
        // unless the calling function explictly requests it to remain on hold.
        // A message is appended to the update stating this has been done.

        $original_team_id = tickets_original_team_id_get($ticket_id);

    if ($original_team_id > 0 && $bolTakeOffHold) {
        $release_ticket = tickets_ticket_release($ticket_id);

        // If the Question is released, send it back to its original team
        // and append the contact message.

        if ($release_ticket == true) {
            $original_team_name = phplib_team_name_get($original_team_id);
            if ($bolTakeOffHold === 'OVERRIDE_ON_HOLD_TEAM') {
                if ($team_id == WORKPLACE_ON_HOLD_TEAM_ID) {
                    // About to release a Question back to the on hold pool..fix it!
                    $team_id = $original_team_id;
                    $release_info = "The Question $ticket_id has been released from hold " .
                        "and sent to $original_team_name\n\n";
                } else {
                    $strTeamName = phplib_team_name_get($team_id);
                    $release_info = "The Question $ticket_id has been released from hold " .
                            "in the $original_team_name pool and forwarded to $strTeamName\n\n";
                }
            } elseif (-1 == $team_id) { // override on hold team anyway
                $release_info = "The Question $ticket_id has been released from hold " .
                        "in the $original_team_name pool and returned to the customer\n\n";
            } else {
                $team_id = $original_team_id;
                $release_info = "The Question $ticket_id has been released from hold " .
                        "and sent back to $original_team_name\n\n";
            }

            // QW123-160
            // Log release info as an "internal only" comment
            $connection = get_named_connection('tickets');

            $query = "INSERT INTO ticket_contacts (
                              ticket_id, 
                              body,        
                              date_raised,
                              owner_id,  
                              error_class, 
                              error_subclass,
                              team_id,   
                              status,      
                              actioner_id )
                       VALUES (
                              '$ticket_id', 
                              '$release_info',
                              '$datestamp',
                              '$owner_id',  
                              '$error_class', 
                              '$error_subclass',
                              " . WORKPLACE_ON_HOLD_TEAM_ID . ",
                              '$status',      
                              '$actioner_id')";

            $result = mysql_query($query, $connection)
            or report_error(__FILE__, __LINE__, mysql_error($connection));
        }
    } else {
        // Question should be left on hold, if it currently is.
        // Check if it's on hold and change the contact team Id if it is
        $strQuery="SELECT SQL_NO_CACHE original_team_id ".
                  "FROM tickets.ticket_contacts tc ".
                  "INNER JOIN tickets.tickets_on_hold th ON tc.contact_id = th.contact_id ".
                  " AND when_done is null ".
                  "WHERE tc.ticket_id ='$ticket_id' ".
                  "ORDER BY tc.contact_id DESC ".
                  "LIMIT 1";

        $connection = get_named_connection('tickets');

        $result = mysql_query($strQuery, $connection)
        or report_error(__FILE__, __LINE__, mysql_error($connection));
        $arrExistingOnHold = mysql_fetch_array($result, MYSQL_ASSOC);
        mysql_free_result($result);

        if (is_array($arrExistingOnHold) && count($arrExistingOnHold) > 0 && isset($arrExistingOnHold['original_team_id'])
            && $arrExistingOnHold['original_team_id'] > 0) {
                $team_id = WORKPLACE_ON_HOLD_TEAM_ID;
        }
    }
        // P1 52357
        // if ticket is with customer and was actioned by customer -
        // Or
        // P90550 if this is an SMS response that has caused a race condition
        // which may make it impossible to determine the previous team
        // never send it to 'In Progress' again, send it to general CSC pool instead
    if ((-1 == $ticket['team_id']) && $boolCustomer && (WORKPLACE_ON_HOLD_TEAM_ID == $team_id)
        || (WORKPLACE_ON_HOLD_TEAM_ID == $team_id && $isSms)) {
            //send it to Customer Support Centre pool
            $team_id = 1;
    }

        // work out if we need to add Question sla summary info
        $log_summary_info = false;



        // we want to add sla summary info if this contact is the first
        // contact to be added by an 'internal' source, after a change
        // of team for the ticket

    if ($boolCustomer) {
        // no sla summary logging if the customer is adding the contact
        if (strtolower($status) == 'closed') {
            TicketsUpdateMetrics($ticket_id, 0, $status, $team_id, date('Y-m-d h:i:s'), $actioner_id);
        }
    } else {
        // Question Metrics
        TicketsUpdateMetrics($ticket_id, 0, $status, $team_id, date('Y-m-d h:i:s'), $actioner_id);

        $num_contacts = count($contact_history);

        if ($num_contacts == 0) {
            // this is the first contact on the ticket
            // so we'll not log anything for sla summaries
        } else {
            $position_of_last_team_change = -1;

            // find the last change of ownership
            for ($i = $num_contacts - 1; $i > -1; $i--) {
                if (isset($contact_history[$i-1]) &&
                    $contact_history[$i]['team_id'] != $contact_history[$i-1]['team_id']) {
                    $position_of_last_team_change = $i;
                    break;
                }
            }

            if ($position_of_last_team_change == -1) {
                // there has been no change of ownership at all, we'll assume that
                // creating the Question constitutes, a change of ownership from
                // 'no owner' to 'an owner'
                $position_of_last_team_change = 0;
            }

            $position_of_last_internal_contact = -1;

            // find the last internally raised contact where the contact was made
            // by someone in the same team as the Question is currently with
            for ($i=$num_contacts - 1; $i > -1; $i--) {
                if ($contact_history[$i]['actioner_id'] != '0') {
                    $actioner_of_contact = $contact_history[$i]['actioner_id'];

                    $that_employee = phplib_employee_profile_get($actioner_of_contact);

                    if ($that_employee['team_id'] == $ticket['team_id']) {
                        $position_of_last_internal_contact = $i;
                        break;
                    }
                }
            }

            if ($position_of_last_internal_contact == -1) {
                // there have been no internal contacts yet
            }

            if ($position_of_last_internal_contact > $position_of_last_team_change) {
                // there has been an internal contact since the last change of team
                // we don't need to log any summary info
            } else {
                // this is the first internal contact since a change of team
                // we need to log summary information - but only if the actioner of this new contact,
                // is in the same team as the Question is currently with
                $this_employee = phplib_employee_profile_get($actioner_id);

                if ($ticket['team_id'] == $this_employee['team_id']) {
                    $log_summary_info = true;
                    $date_of_start_of_sla_period = $contact_history[$position_of_last_team_change]['date_raised'];
                    $sla_team_id = $this_employee['team_id'];
                } else {
                    // this is the first internal contact since a change of team,
                    // but the person making this contact is not in the same team
                    // as the Question is currently with - therfore we can't
                    // class this contact as a responce to an escalation
                }
            }
        }
    }

        $connection = get_named_connection('tickets');

        $query = "INSERT INTO ticket_contacts (
                              ticket_id, 
                              body,        
                              date_raised,
                              owner_id,  
                              error_class, 
                              error_subclass,
                              team_id,   
                              status,      
                              actioner_id)
                       VALUES (
                              '$ticket_id', 
                              '$body',        
                              '$datestamp',
                              '$owner_id',  
                              '$error_class', 
                              '$error_subclass',
                              '$team_id',   
                              '$status',      
                              '$actioner_id')";

        $result = mysql_query($query, $connection)
        or report_error(__FILE__, __LINE__, mysql_error($connection));

        $contact_id = mysql_insert_id($connection);

        // If contact was created via Workplace then log into Question actions table
    if (isset($my_id) && (trim($my_id) != '')) {
        $intCharacterCount = strlen($body);
        TicketsTicketActionLog($ticket_id, 0, $contact_id, $my_id, $strLogStatus, $intCharacterCount, $team_id, $owner_id);
    } else {// raised via script or customer so log against tblTicketActionLogCustScript table
        //similer to code from custdetails screen function render_contact_list
        switch ($ticket['ticket_source']) {
            case 'Internal':
            case 'Script':
                $strRaisedBy = 'script';
                break;
            default:
                $strRaisedBy = 'portaluser';
                break;
        }

        TicketsTicketActionLogCustScript($ticket_id, 0, $contact_id, $status, $team_id, $owner_id, $strReopenedTicket, $strRaisedBy);
    }


        // add the Question sla summary info if it's required
        // pass to the logging function:
        //        ticket_id
        //        ticket_contact_id
        //        time of this contact
        //        time of contact to refer to in sla
        //      id of team sla is applicable for
    if ($log_summary_info) {
        tickets_sla_summary_add($ticket_id, $contact_id, $date_of_start_of_sla_period, $datestamp, $sla_team_id);
    }

        $update = "UPDATE tickets " .
                 "SET error_class    = '$error_class', " .
                     "error_subclass = '$error_subclass', " .
                     "owner_id       = '$owner_id', " .
                     "team_id        = '$team_id', " .
                     "status         = '$status', " .
                     "actioner_id    = '$actioner_id', " .
                     "contact_count  = contact_count + 1 " .
               "WHERE ticket_id = '$ticket_id'";

    if (($team_id == 112) && ($ticket['team_id'] != 112) && ($strOrigStatus != '')) {
        //need to alert TEAM BOT that another 'Simulataneous Provides' Questions is coming their way.
        //NADS 747: Alert the 'BOT - Simultaneous Provides' team whenever a Question is raised to their pool.
        //WARINING!!! The ID of the BOT - Simultaneous Provides' team is HARD CODEDED!!!
        //71 is the team id of 'TEAM BOT': SELECT * FROM php_lib.group_details where group_id = 71
        //112 is the team id of 'BOT - Simultaneous Provides'.  You're confused... WTF about me??!?
        //NOTE: The ($status != '') bit excludes alerts being show for Questions that are released from hold

        global $whoami;
        $strAlertDescription = 'A TICKET HAS BEEN ESCALATED IN TO THE SIMULTANIOUS PROVIDE POOL';
        $arrTmp = phplib_user_get_from_group(71);
        $arrSimulTeamMembers = array();
        foreach ($arrTmp as $arrDbRow) {
            foreach ($arrDbRow as $strFieldName => $strFieldVal) {
                if ($strFieldName == 'user_id') {
                    array_push($arrSimulTeamMembers, $strFieldVal);
                }
            }
        }
        alert_alert_add(
            1,
            'now()',
            $strAlertDescription,
            'http://workplace.plus.net/tickets/my_tickets_push.html',
            $whoami,
            $arrSimulTeamMembers,
            array(),
            'no',
            'Ticket escalated to Simultanious Provide pool'
        );
        mail('<EMAIL>', 'A TICKET HAS BEEN PLACED IN TO THE SIMULTANIOUS PROVIDE POOL', "ID: $ticket_id");
    }

// ENHANCED CARE ALERT
        require_once '/local/data/mis/database/database_libraries/internalgroups/internal_groups_get_id_of_group.method.php';

        $strGetECPoolsSql = "SELECT team_id FROM teams WHERE name LIKE '%enhanced care%'";

        $arrDbConnection  = get_named_connection_with_db('phplib');
        $resResult       = PrimitivesQueryOrExit($strGetECPoolsSql, $arrDbConnection, '', false);
        $arrECTeamsRaw       = PrimitivesResultsAsArrayGet($resResult);

    foreach ($arrECTeamsRaw as $miaw => $woof) {
        $arrECTeams[] = $woof['team_id'];
    }

    if (in_array($team_id, $arrECTeams) && (!in_array($ticket['team_id'], $arrECTeams) ||
        (count($contact_history) == 0))) {
        global $whoami;
        $strAlertDescription     = 'Fault Ticket '.$ticket_id.' has been raised into an Enhanced Care pool. '.
            'Please ensure this is actioned as soon as possible. SLA for first touch is 1 hour from raising';
        $strAlertTitle        = 'A TICKET HAS BEEN ADDED TO THE ENHANCED CARE POOL';
        $arrTmp         = phplib_user_get_from_group(split_internal_groups_get_id_of_group(GROUP_ENHANCED_CARE));
        $arrECTeamMembers     = array();

        foreach ($arrTmp as $arrDbRow) {
            foreach ($arrDbRow as $strFieldName => $strFieldVal) {
                if ('user_id' == $strFieldName) {
                    array_push($arrECTeamMembers, $strFieldVal);
                }
            }
        }

        require_once '/local/data/mis/database/database_libraries/alert-access.inc';

        alert_alert_add(
            1,
            'now()',
            $strAlertDescription,
            'http://workplace.plus.net/tickets/ticket_show.html?ticket_id='.$ticket_id,
            $whoami,
            $arrECTeamMembers,
            array(),
            'no',
            $strAlertTitle
        );
    }

// ENHANCED CARE ALERT END


//PERSONAL TICKET UPDATE ALERT

        $arrEmployee = phplib_employee_profile_get($ticket['owner_id']);
        $userId = is_null($my_id) ? '' : $my_id;

        //Check that ticket is assigned to agent not team (personal ticket)
    if (isset($arrEmployee['user_id']) && !empty($arrEmployee['user_id']) && $ticket['owner_id'] != $userId) {
        $strSystemUserId = 'zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz';
        $strAlertDescription = 'A ticket that was in your ownership has been updated.';
        $strAlertTitle       = 'Ticket Updated';

        require_once '/local/data/mis/database/database_libraries/alert-access.inc';

        alert_alert_add(
            1,
            time(),
            $strAlertDescription,
            'https://workplace.plus.net/tickets/ticket_show.html?ticket_id='.$ticket_id,
            $strSystemUserId,
            array($ticket['owner_id']),
            array(),
            'no',
            $strAlertTitle
        );
    }
//PERSONAL TICKET UPDATE ALERT END

        $connection = get_named_connection('tickets');

        mysql_query($update, $connection)
        or report_error(__FILE__, __LINE__, mysql_error($connection));

        // If a workplace user actions on a Question that is being worked on by that user,
        // work on that Question is stopped

        $locked_ticket_owner_id = tickets_get_locked_ticket_owner_id($ticket_id);

    if (!$boolCustomer &&
        ($locked_ticket_owner_id == $actioner_id)
       ) {
        tickets_ticket_unlock($ticket_id, $actioner_id);
    }

    if (defined('OPEN_TICKETS_SUMMARY_TABLE') && OPEN_TICKETS_SUMMARY_TABLE == 'live') {
        if (strtolower($status) == 'closed') {
            tickets_open_ticket_summary_delete($ticket_id, 0, $complaintResolvedReasonId);
        } else {
            $query = 'UPDATE open_tickets
                             SET error_class    = "'.$error_class.'",
                                 error_subclass = "'.$error_subclass.'",
                                 owner_id       = "'.$owner_id.'",
                                 team_id        = "'.$team_id.'",
                                 status         = "'.$status.'",
                                 actioner_id    = "'.$actioner_id.'",
                                 contact_count  = contact_count + 1
                           WHERE ticket_id      = "'.$ticket_id.'"
                             AND partner_id     = "0"';

            $connection = get_named_connection('common_tickets');

            $result = mysql_query($query, $connection)
                         or report_error(__FILE__, __LINE__, mysql_error($connection));
        }
    }


///////
//
// Question SLG

    if (strtolower($ticket['ticket_source']) == 'portal') {
        $objTicketSLGTimer = CTicketSLGTimer::Create();
        $objTicketSLGTimer->SetTicketID($ticket_id);
        $objTicketSLGTimer->SetTicketContactID($contact_id);

        $objTeamSLGTimer = CTeamSLGTimer::Create();
        $objTeamSLGTimer->SetTicketID($ticket_id);
        $objTeamSLGTimer->SetTicketContactID($contact_id);
        $objTeamSLGTimer->SetTeamID($team_id);

        switch (strtolower($status)) {
            case 'open':
                //Start a new timer
                $objTicketSLGTimer->Start();
                $objTeamSLGTimer->Start();
                break;
            case 'actioned':
                //The Question is being returned to the customer
                $objTicketSLGTimer->Stop();
                $objTeamSLGTimer->Stop();
                break;
            case 'assigned':
            case '':
                if ($boolCustomer) {
                    //returned from customer - restart the Question timer
                    $objTicketSLGTimer->Start();
                    $objTeamSLGTimer->Start();
                } else {
                    //Moved around internally
                    //Start() will not create a new timer if the team has not changed.
                    $objTeamSLGTimer->Start();
                }
                break;
            case 'closed':
                // The Question is being closed by the customer or after x days of inactivity
                // Should have already been stopped when it was returned to the customer...
                // but stop it to be sure
                $objTicketSLGTimer->Stop();
                $objTeamSLGTimer->Stop();
                break;
            default:
                break;
        }
    }

//
//
/////////




    if (strtolower($status) == 'closed') {
        // add data about Question to metrics table
        if (defined('TICKET_METRICS_TABLE')==true && TICKET_METRICS_TABLE == 'live') {
            TicketsUpdateMetrics($ticket_id, 0, $status, $team_id, date('Y-m-d h:i:s'), $actioner_id);
        }
    }

        return $contact_id;
} // end tickets_ticket_contact_add
