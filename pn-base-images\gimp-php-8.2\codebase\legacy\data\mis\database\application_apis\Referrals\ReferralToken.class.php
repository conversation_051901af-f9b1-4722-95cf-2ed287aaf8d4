<?php

/**
 * ReferralToken 
 * 
 * @package 
 * @version $id$
 * @copyright PlusNet
 * <AUTHOR> <<EMAIL>> 
 * @license PHP License Version 3.01 {@link http://www.php.net/license/3_01.txt}
 */
class ReferralToken
{
	/**
	 * intReferralTokenId 
	 * 
	 * @var integer
	 * @access protected
	 */
	protected $intReferralTokenId;

	/**
	 * intReferralValueId 
	 * 
	 * @var integer
	 * @access protected
	 */
	protected $intReferralValueId;

	/**
	 * intReferralTypeId 
	 * 
	 * @var integer
	 * @access protected
	 */
	protected $intReferralTypeId;

	/**
	 * intSpecialOfferReferralValueId 
	 * 
	 * @var integer
	 * @access protected
	 */
	protected $intSpecialOfferReferralValueId = false;

	/**
	 * intReferrerServiceId 
	 * 
	 * @var integer
	 * @access protected
	 */
	protected $intReferrerServiceId;

	/**
	 * intReferralServiceId 
	 * 
	 * @var integer
	 * @access protected
	 */
	protected $intReferralServiceId;

	/**
	 * intProductTypeId
	 * Called this, because it could be a service definition ID or a service component ID,
	 * which is just confusing.
	 * 
	 * @var integer
	 * @access protected
	 */
	protected $intProductTypeId;

	/**
	 * intComponentId 
	 * 
	 * @var integer
	 * @access protected
	 */
	protected $intComponentId;

	/**
	 * intCreatingTransactionId 
	 * 
	 * @var integer
	 * @access protected
	 */
	protected $intCreatingTransactionId;

	/**
	 * strCreatingTransaction 
	 * 
	 * @var string
	 * @access protected
	 */
	protected $strCreatingTransaction;

	/**
	 * dteMonthCreditEarned 
	 * 
	 * @var string
	 * @access protected
	 */
	protected $dteMonthCreditEarned;

	/**
	 * strYearMonth 
	 * 
	 * @var string
	 * @access protected
	 */
	protected $strYearMonth;

	/**
	 * dteTokenCreated 
	 * 
	 * @var mixed
	 * @access protected
	 */
	protected $dteTokenCreated;

	/**
	 * intOriginalValuePence 
	 * 
	 * @var integer
	 * @access protected
	 */
	protected $intOriginalValuePence;

	/**
	 * intUnspentValuePence 
	 * 
	 * @var integer
	 * @access protected
	 */
	protected $intUnspentValuePence;

	/**
	 * strDbSrc - Read Only. Do not update
	 * 
	 * @var string
	 * @access protected
	 */
	protected $strDbSrc;

	/**
	 * stmLastUpdate - Read Only. Do not update
	 * 
	 * @var string
	 * @access protected
	 */
	protected $stmLastUpdate;



	/**
	 * strSource 
	 * 
	 * @var string
	 * @access private
	 */
	private $strSource;

	/**
	 * dbhConnectionWrite - Useful for transactions; we can set this in from the calling script
	 * 
	 * @var mixed
	 * @access private
	 */
	private $dbhConnectionWrite;



	/**
	 * __construct 
	 * 
	 * @param array $arrVars 
	 * @access public
	 * @return void
	 */
	public function __construct($arrVars = array())
	{
		if (!empty($arrVars))
		{
			// Assign in vars
			foreach ($arrVars as $strKey => $unkValue)
			{
				$this->$strKey = $unkValue;
			}

			// More logical to call it this when creating a new token
			if (isset($arrVars['intReferralValuePence']))
			{
				$this->intOriginalValuePence = $arrVars['intReferralValuePence'];
			}

			// Ensure the created token validates
			$this->validate();
		}

		// Set up connections
		$this->dbhConnectionWrite = get_named_connection_with_db('financial');
	} // public function __construct($arrVars = array())

	/**
	 * getReferralTokenId 
	 * 
	 * <AUTHOR> Jones <<EMAIL>> 
	 * @access public
	 * @return integer
	 */
	public function getReferralTokenId()
	{
		return (int) $this->intReferralTokenId;
	}

	/**
	 * setReferralTokenId 
	 * 
	 * @param integer $intReferralTokenId 
	 * @access public
	 * @return boolean
	 */
	public function setReferralTokenId($intReferralTokenId)
	{
		if (is_numeric($intReferralTokenId) && intval($intReferralTokenId) > 0)
		{
			$this->intReferralTokenId = (int) $intReferralTokenId;
			return true;
		}

		return false;
	}

	/**
	 * getReferralValueId 
	 * 
	 * <AUTHOR> Jones <<EMAIL>> 
	 * @access public
	 * @return integer
	 */
	public function getReferralValueId()
	{
		return $this->intReferralValueId;
	}

	/**
	 * getReferralTypeId - Returns the referral type id. This is 1 for service_definition_id
	 *    or 2 for service_component_id
	 * 
	 * <AUTHOR> Jones <<EMAIL>> 
	 * @access public
	 * @return integer
	 */
	public function getReferralTypeId()
	{
		return $this->intReferralTypeId;
	}

	/**
	 * getSpecialOfferReferralValueId 
	 * 
	 * <AUTHOR> Jones <<EMAIL>> 
	 * @access public
	 * @return integer
	 */
	public function getSpecialOfferReferralValueId()
	{
		return $this->intSpecialOfferReferralValueId;
	}

	/**
	 * setSpecialOfferReferralValueId 
	 * 
	 * <AUTHOR> Jones <<EMAIL>> 
	 * @param integer $intSpecialOfferReferralValueId 
	 * @access public
	 * @return boolean
	 */
	public function setSpecialOfferReferralValueId($intSpecialOfferReferralValueId)
	{
		if (is_numeric($intSpecialOfferReferralValueId) && intval($intSpecialOfferReferralValueId) > 0)
		{
			$this->intSpecialOfferReferralValueId = (int) $intSpecialOfferReferralValueId;
			return true;
		}

		return false;
	}

	/**
	 * getReferrerServiceId 
	 * 
	 * <AUTHOR> Jones <<EMAIL>> 
	 * @access public
	 * @return integer
	 */
	public function getReferrerServiceId()
	{
		return (int) $this->intReferrerServiceId;
	}

	/**
	 * getReferralServiceId 
	 * 
	 * <AUTHOR> Jones <<EMAIL>> 
	 * @access public
	 * @return integer
	 */
	public function getReferralServiceId()
	{
		return (int) $this->intReferralServiceId;
	}

	/**
	 * getProductTypeId
	 * This can be either a service definition ID or service component ID - because of the
	 * way our referrals system. Use getReferralTypeId to tell the difference (if it's set)
	 * 
	 * <AUTHOR> Jones <<EMAIL>> 
	 * @access public
	 * @return integer
	 */
	public function getProductTypeId()
	{
		return (int) $this->intProductTypeId;
	}

	/**
	 * getCreatingTransactionId 
	 * 
	 * <AUTHOR> Jones <<EMAIL>> 
	 * @access public
	 * @return integer
	 */
	public function getCreatingTransactionId()
	{
		return (int) $this->intCreatingTransactionId;
	}

	/**
	 * getCreatingTransaction 
	 * 
	 * <AUTHOR> Jones <<EMAIL>> 
	 * @access public
	 * @return string
	 */
	public function getCreatingTransaction()
	{
		return $this->strCreatingTransaction;
	}

	/**
	 * getMonthCreditEarned 
	 * 
	 * <AUTHOR> Jones <<EMAIL>> 
	 * @access public
	 * @return string
	 */
	public function getMonthCreditEarned()
	{
		return $this->dteMonthCreditEarned;
	}

	/**
	 * getYearMonth 
	 * 
	 * <AUTHOR> Jones <<EMAIL>> 
	 * @access public
	 * @return string
	 */
	public function getYearMonth()
	{
		return $this->strYearMonth;
	}

	/**
	 * setTokenValuePence 
	 * 
	 * <AUTHOR> Jones <<EMAIL>> 
	 * @param integer $intTokenValuePence 
	 * @access public
	 * @return void
	 */
	public function setReferralValuePence($intReferralValuePence)
	{
		$intSpent = $this->intOriginalValuePence - $this->intUnspentValuePence;
		$this->intOriginalValuePence = $intReferralValuePence;
		$this->intUnspentValuePence  = $intReferralValuePence - $intSpent;
	}

	/**
	 * getOriginalValuePence 
	 * 
	 * <AUTHOR> Jones <<EMAIL>> 
	 * @access public
	 * @return integer
	 */
	public function getOriginalValuePence()
	{
		return (int) $this->intOriginalValuePence;
	}

	/**
	 * getUnspentValuePence 
	 * 
	 * <AUTHOR> Jones <<EMAIL>> 
	 * @access public
	 * @return integer
	 */
	public function getUnspentValuePence()
	{
		return (int) $this->intUnspentValuePence;
	}

	/**
	 * getDateTokenCreated 
	 * 
	 * <AUTHOR> Jones <<EMAIL>> 
	 * @access public
	 * @return string
	 */
	public function getDateTokenCreated()
	{
		return $this->dteTokenCreated;
	}

	/**
	 * getSource - This is the db_src field
	 * 
	 * <AUTHOR> Jones <<EMAIL>> 
	 * @access public
	 * @return string
	 */
	public function getSource()
	{
		return $this->strSource;
	}

	/**
	 * setSource 
	 * 
	 * @param string $strSource
	 * @access public
	 * @return void
	 */
	public function setSource($strSource)
	{
		$this->strSource = $strSource;
	}

	/**
	 * setConnectionWrite 
	 * 
	 * @param mixed $dbhConnection 
	 * @access public
	 * @return void
	 */
	public function setConnectionWrite($dbhConnection)
	{
		$this->dbhConnectionWrite = $dbhConnection;
	}

	/**
	 * validateReferralToken - Ensures populated fields are set as expected
	 *    This is normally called from the constructor.
	 * 
	 * <AUTHOR> Jones <<EMAIL>> 
	 * @access protected
	 * @return boolean
	 */
	protected function validate()
	{
		$arrError = array();

		if (empty($this->intReferrerServiceId) ||
				!is_numeric($this->intReferrerServiceId) ||
				intval($this->intReferrerServiceId) <= 0)
		{
			$arrError[] = 'intReferrerServiceId does not validate';
		}

		if (empty($this->intProductTypeId) ||
				!is_numeric($this->intProductTypeId) ||
				intval($this->intProductTypeId) <= 0)
		{
			$arrError[] = 'intProductTypeId does not validate';
		}

		if (empty($this->intCreatingTransactionId) ||
				!is_numeric($this->intCreatingTransactionId) ||
				intval($this->intCreatingTransactionId) <= 0)
		{
			$arrError[] = 'intCreatingTransactionId does not validate';
		}

		$arrCreatingTransaction = array('invalid','adsl_prepaid_subscription','account_type_change','regular_subscription','manual_ticketed_correction','wifi_signup','wifi_subscription_payment','chargeable_component');
		if (empty($this->strCreatingTransaction) ||
				!in_array($this->strCreatingTransaction, $arrCreatingTransaction))
		{
			$arrError[] = 'strCreatingTransaction does not validate';
		}

		// These following preg matches for date and yearnmonth aren't great
		if (empty($this->dteMonthCreditEarned) ||
				!preg_match('/[0-9]{4}\-[0-1][0-9]\-[0-3][0-9]/', $this->dteMonthCreditEarned))
		{
			$arrError[] = 'dteMonthCreditEarned does not validate';
		}

		if (empty($this->strYearMonth) ||
				!preg_match('/[0-9]{4}[0-1][0-9]/', $this->strYearMonth))
		{
			$arrError[] = 'strYearMonth does not validate';
		}

		if (empty($this->intOriginalValuePence) ||
				!is_numeric($this->intOriginalValuePence) ||
				intval($this->intOriginalValuePence) <= 0)
		{
			$arrError[] = 'intOriginalValuePence (intReferralValuePence) does not validate';
		}

		// If this isn't set then we set it to original value in pence
		if (is_null($this->intUnspentValuePence) || '' === $this->intUnspentValuePence)
		{
			$this->intUnspentValuePence = $this->intOriginalValuePence;
		}
		else
		{
			// Otherwise, validate it - the unspent credit can be zero
			if (!is_numeric($this->intUnspentValuePence) ||
					$this->intUnspentValuePence < 0 ||
					$this->intUnspentValuePence > $this->intOriginalValuePence)
			{
				$arrError[] = 'intUnspentValuePence does not validate';
			}
		}

		if (!empty($arrError))
		{
			// All errors together, so user isn't fixing one problem at a time.
			$strError = implode(', ', $arrError);
			throw new Exception('Referral Token is invalid: ' . $strError);
		}

		// We appear to have made it to the end.
		return true;
	} // protected function validateReferralToken()

	/**
	 * isValid 
	 * 
	 * <AUTHOR> Jones <<EMAIL>> 
	 * @access public
	 * @return boolean
	 */
	public function isValid()
	{
		try
		{
			$this->validate();
		}
		catch (Exception $excException)
		{
			return false;
		}

		return true;
	}

	/**
	 * create - Create a referral token (and automagically apply boni
	 * 
	 * <AUTHOR> Jones <<EMAIL>> 
	 * @access public
	 * @return boolean
	 */
	public function create()
	{
		$objReferrals = new Referrals();
		return $objReferrals->createReferralToken($this);
	} // public function create()

	/**
	 * Make a redemption against the referral token
	 * This should probably be moved into the Referrals class, so that ReferralToken is
	 * simply a ReferralToken entity which we then do things with in business logic
	 * functionality.
	 * 
	 * @param integer $intCreditNoteId 
	 * @param integer $intOriginalInvoiceId 
	 * @param integer $intRedeemPence 
	 * @access public
	 * @return boolean
	 */
	public function redeem($intCreditNoteId, $intOriginalInvoiceId, $intRedeemPence)
	{
		// Set this flag in the script that calls setConnectionWrite to cascade transactions
		if (!isset($this->dbhConnectionWrite['bolTransaction']) || 
				false === $this->dbhConnectionWrite['bolTransaction'])
		{
			if (false === PrimitivesQueryOrExit('BEGIN', 
			                                    $this->dbhConnectionWrite,
			                                    'ReferralToken::processReferralCreditTokens::BEGIN',
			                                    false))
			{
				return false;
			}
		}

		// Ideally, the revision should be set from whereever this method is called from, as
		// it will give a better idea of where the action was performed.
		$strSource = (!is_null($this->strSource)) ? $this->strSource
		             :  basename(__FILE__) . ':' . __LINE__ . ':$Id: ReferralToken.class.php,v 1.3 2008-09-30 12:09:04 rjones Exp $';

		$intReferralTokenId   = PrimitivesRealEscapeString($this->intReferralTokenId, $this->dbhConnectionWrite);
		$strSource            = PrimitivesRealEscapeString($strSource,                $this->dbhConnectionWrite);
		$intRedeemPence       = PrimitivesRealEscapeString($intRedeemPence,           $this->dbhConnectionWrite);
		$intCreditNoteId      = PrimitivesRealEscapeString($intCreditNoteId,          $this->dbhConnectionWrite);
		$intOriginalInvoiceId = PrimitivesRealEscapeString($intOriginalInvoiceId,     $this->dbhConnectionWrite);

		$strQuery = "
INSERT INTO referral_token_redemptions
        SET referral_token_id             = '{$intReferralTokenId}', 
            date_redeemed                 =  NOW(),
            value_redeemed_in_pence       = '{$intRedeemPence}',
            credit_note_id                = '{$intCreditNoteId}',
            invoice_reduced_by_redemption = '{$intOriginalInvoiceId}',
            db_src                        = '{$strSource}'";

		if (false === PrimitivesQueryOrExit($strQuery, 
		                                    $this->dbhConnectionWrite, 
		                                    'ReferralToken::processReferralCreditTokens::referral_token_redemptions',
		                                    false))
		{
			// Attempt to rollback the transaction. Doesn't matter if this fails because the 
			// transaction should implicitly rollback after the script ends anyway.
			PrimitivesQueryOrExit('ROLLBACK', 
			                      $this->dbhConnectionWrite, 
			                      'ReferralToken::processReferralCreditTokens::ROLLBACK',
			                      false);
			return false;
		}

		$strQuery = "
UPDATE referral_tokens
   SET unspent_credit_in_pence = (unspent_credit_in_pence - {$intRedeemPence}) 
 WHERE referral_token_id = {$intReferralTokenId}";
		if (false === PrimitivesQueryOrExit($strQuery, 
		                                    $this->dbhConnectionWrite,
		                                    'Referrals::processReferralCreditTokens::referral_tokens',
		                                    false))
		{
			// Attempt to rollback the transaction. Doesn't matter if this fails because the 
			// transaction should implicitly rollback after the script ends anyway.
			PrimitivesQueryOrExit('ROLLBACK', 
			                      $this->dbhConnectionWrite, 
			                      'ReferralToken::processReferralCreditTokens::ROLLBACK',
			                      false);
			return false;
		}

		// Only commit if this transaction is not set up in the calling script
		if (!isset($this->dbhConnectionWrite['bolTransaction']) || 
				false === $this->dbhConnectionWrite['bolTransaction'])
		{
			if (false === (PrimitivesQueryOrExit('COMMIT', 
			                                     $this->dbhConnectionWrite, 
			                                     'ReferralToken::processReferralCreditTokens::COMMIT', 
			                                     false)))
			{
				return false;
			}
		}

		return true;
	} // public function redeem($intCreditNoteId, $intOriginalInvoiceId, $intRedeemPence)

	//
	// Static methods
	//
	
	/**
	 * getReferralTokenById 
	 * 
	 * @param integer $intReferralTokenId 
	 * @access public
	 * @return ReferralToken
	 */
	public static function getReferralTokenById($intReferralTokenId)
	{
		if (!is_numeric($intReferralTokenId) || $intReferralTokenId < 1)
		{
			return false;
		}

		$dbhConnection      = get_named_connection_with_db('financial_reporting');
		$intReferralTokenId = PrimitivesRealEscapeString($intReferralTokenId, $dbhConnection);

		$strQuery = "
SELECT referral_token_id                   AS intReferralTokenId,
       referrer_service_id                 AS intReferrerServiceId,
       referral_service_id                 AS intReferralServiceId,
       component_id                        AS intComponentId,
       creating_transaction_id             AS intCreatingTransactionId,
       creating_transaction                AS strCreatingTransaction,
       month_credit_earned                 AS dteMonthCreditEarned,
       year_n_month                        AS strYearMonth,
       date_token_created                  AS dteTokenCreated,
       referrals_account_type_when_created AS intProductTypeId,
       original_value_in_pence             AS intOriginalValuePence,
       unspent_credit_in_pence             AS intUnspentValuePence,
       db_src                              AS strDbSrc,
       timestamp                           AS stmLastUpdate
  FROM referral_tokens
 WHERE referral_token_id = {$intReferralTokenId}";

		$resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection, 'ReferralToken::getReferralTokenById', false);
		if (false === $resResult)
		{
			return false;
		}

		if (PrimitivesNumRowsGet($resResult) == 0)
		{
			return false;
		}

		$arrReferralToken = PrimitivesResultGet($resResult);
		$objReferralToken = new ReferralToken($arrReferralToken);
		return $objReferralToken;
	} // function getReferralTokenById($intReferralTokenId)
} // class ReferralToken

