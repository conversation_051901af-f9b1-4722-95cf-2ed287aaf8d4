<?php
/**
 * userdata-access.inc
 *
 * Access Library for Userdata
 *
 * @package    LegacyCodebase
 * @subPackage Database Libraries
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright  2012 Plusnet
 */
require_once '/local/data/mis/database/crypt_config.inc';

require_once '/local/data/mis/database/database_libraries/Util_LibrarySplitter.class.php';
require_once '/local/data/mis/database/database_libraries/userdata/Userdata.class.php';

require_once '/local/data/mis/common_library_functions/configDefineIfMissing.inc';

configDefineIfMissing('SERVICE_EVENT_SIGNUP', 1);
configDefineIfMissing('SERVICE_EVENT_CANCELLATION', 2);
configDefineIfMissing('SERVICE_EVENT_SCHEDULED_CANCELLATION', 3);
configDefineIfMissing('SERVICE_EVENT_REACTIVATION', 4);
configDefineIfMissing('SERVICE_EVENT_DISABLE', 5);
configDefineIfMissing('SERVICE_EVENT_ENABLE', 6);
configDefineIfMissing('SERVICE_EVENT_TYPE_CHANGE', 7);
configDefineIfMissing('SERVICE_EVENT_SCHEDULED_TYPE_CHANGE', 8);
configDefineIfMissing('SERVICE_EVENT_MANUAL_NEXTINVOICE_CHANGE', 9);
configDefineIfMissing('SERVICE_EVENT_ACTIONED_SCHEDULED_CANCEL', 10);
configDefineIfMissing('SERVICE_EVENT_REMOVED_SCHEDULED_CANCEL', 11);
configDefineIfMissing('SERVICE_EVENT_SCHEDULED_CANCELLATION_DEL', 12);
configDefineIfMissing('SERVICE_EVENT_ADSL_UPGRADE', 13);
configDefineIfMissing('SERVICE_EVENT_ADSL_ACTIVATED', 14);
configDefineIfMissing('SERVICE_EVENT_ADSL_PREREGISTER', 15);
configDefineIfMissing('SERVICE_EVENT_REMOVED_SCHEDULED_TYPE_CHANGE', 16);
configDefineIfMissing('SERVICE_EVENT_SCHEDULED_TYPE_CHANGE_DEL', 17);
configDefineIfMissing('SERVICE_EVENT_ACTIONED_SCHEDULED_TYPE_CHANGE', 18);
configDefineIfMissing('SERVICE_EVENT_3MONTH_DEACTIVATED', 19);
configDefineIfMissing('SERVICE_EVENT_ADSL_PREREGISTER_CANCELLED', 20);
configDefineIfMissing('SERVICE_EVENT_TYPE_MIGRATION', 21);
configDefineIfMissing('SERVICE_EVENT_TYPE_CREATED_GROUP', 22);
configDefineIfMissing('SERVICE_EVENT_TYPE_JOINED_GROUP', 23);
configDefineIfMissing('SERVICE_EVENT_TYPE_DELETED_GROUP', 24);
configDefineIfMissing('SERVICE_EVENT_TYPE_LEFT_GROUP', 25);
configDefineIfMissing('SERVICE_EVENT_TYPE_RESCINDED_OWNERSHIP_OF_GROUP', 26);
configDefineIfMissing('SERVICE_EVENT_TYPE_TOOK_OWNERSHIP_OF_GROUP', 27);
configDefineIfMissing('SERVICE_EVENT_ADSL_PREUPGRADE', 28);
configDefineIfMissing('SERVICE_EVENT_COMPONENT_CLEANUP', 30);
configDefineIfMissing('SERVICE_EVENT_CBC_FLEX_CHANGE', 38);
configDefineIfMissing('SERVICE_EVENT_CBC_EXTRA_BANDWIDTH_CHANGE', 39);
configDefineIfMissing('SERVICE_EVENT_FAILED_BILLING_STATE_ENTERED', 32);
configDefineIfMissing('SERVICE_EVENT_FAILED_BILLING_RESTRICTIONS_REMOVED_BY_PAYMENT', 33);
configDefineIfMissing('SERVICE_EVENT_FAILED_BILLING_RESTRICTIONS_REMOVED_BY_CSC', 34);
configDefineIfMissing('SERVICE_EVENT_FAILED_BILLING_DEACTIVATION', 35);
configDefineIfMissing('SERVICE_EVENT_FAILED_BILLING_REACTIVATED_BY_PAYMENT', 36);
configDefineIfMissing('SERVICE_EVENT_FAILED_BILLING_REACTIVATED_BY_CSC', 37);
// SERVICE_EVENT_FAILED_BILLING_WILL_ENTER_AFTER_GRACE_PERIOD -
//   currently for Metronet SOHO customers paying by Cheque/BACS
//   - they have 14 days grace period before entering TPAR,
//   after failed billing
//   and all other cheque paying customers that have a 7 day grace period
configDefineIfMissing('SERVICE_EVENT_FAILED_BILLING_WILL_ENTER_AFTER_GRACE_PERIOD', 40);
configDefineIfMissing('SERVICE_EVENT_FAILED_BILLING_WILL_ENTER_AFTER_GRACE_PERIOD_WITH_EMAIL', 42);
configDefineIfMissing('SERVICE_EVENT_FAILED_BILLING_WILL_ENTER_AFTER_GRACE_PERIOD_EMAIL_SENT', 43);
configDefineIfMissing('SERVICE_EVENT_FAILED_BILLING_GRACE_PERIOD_ENDED', 132);
configDefineIfMissing('SERVICE_EVENT_USAGE_LIMIT_RESTRICTION_ADDED', 47);
configDefineIfMissing('SERVICE_EVENT_USAGE_LIMIT_RESTRICTION_REMOVED', 48);
configDefineIfMissing('SERVICE_EVENT_HARDWARE_PURCHASE_SIGNUP', 49);
configDefineIfMissing('SERVICE_EVENT_HARDWARE_PURCHASE_UPGRADE', 50);
configDefineIfMissing('SERVICE_EVENT_HARDWARE_PURCHASE_STANDALONE', 51);
configDefineIfMissing('SERVICE_EVENT_HARDWARE_PURCHASE_SIGNUP', 49);
configDefineIfMissing('SERVICE_EVENT_HARDWARE_PURCHASE_UPGRADE', 50);
configDefineIfMissing('SERVICE_EVENT_HARDWARE_PURCHASE_STANDALONE', 51);
configDefineIfMissing('SERVICE_EVENT_CONTRACT_ACTIVATED', 66);
configDefineIfMissing('SERVICE_EVENT_CONTRACT_EXPIRED', 67);
configDefineIfMissing('SERVICE_EVENT_CONTRACT_CANCELLED', 68);
configDefineIfMissing('SERVICE_EVENT_CONTRACTED_SERVICE_CANCELLED', 133);
configDefineIfMissing('SERVICE_EVENT_CONTRACT_REACTIVATED', 69);
configDefineIfMissing('SERVICE_EVENT_CONTRACT_NULLIFIED', 70);
configDefineIfMissing('SERVICE_EVENT_ACCOUNT_CONVERSION_ON_CANCELATION', 78);
configDefineIfMissing('SERVICE_EVENT_MARKETING_PRODUCT_CHANGE', 79);
configDefineIfMissing('SERVICE_EVENT_CONTRACT_WRITTEN_OFF', 80);
configDefineIfMissing('MIGRATION_PROMOTION_SIGNUP', 81);
configDefineIfMissing('MIGRATION_PROMOTION_REFUND_FAILED', 82);
configDefineIfMissing('MIGRATION_PROMOTION_REFUND_SUCCEED', 83);
configDefineIfMissing('SERVICE_EVENT_MIGRATION_OUT', 84);
configDefineIfMissing('SERVICE_EVENT_METRONET_MAILBOX_ENTERED_FAILED_BILLING', 100);
configDefineIfMissing('SERVICE_EVENT_METRONET_MAILBOX_RESTRICTED', 101);
configDefineIfMissing('SERVICE_EVENT_METRONET_MAILBOX_LEFT_FAILED_BILLING_CUSTOMER', 102);
configDefineIfMissing('SERVICE_EVENT_METRONET_MAILBOX_LEFT_FAILED_BILLING_WORKPLACE', 103);
configDefineIfMissing('SERVICE_EVENT_CHANGE_PRODUCT_FOR_PRODUCT_REFRESH_2006', 93);

//30 day trial project
configDefineIfMissing('SERVICE_EVENT_30DAY_TRIAL_REQUEST', 107);
configDefineIfMissing('SERVICE_EVENT_30DAY_TRIAL_ACTIVATION', 108);

configDefineIfMissing('SERVICE_EVENT_FAULTS_AUTOMATION_ADDED_FRIACO', 109);
configDefineIfMissing('SERVICE_EVENT_FAULTS_AUTOMATION_SCHEDULED_REMOVE_FRIACO', 110);
configDefineIfMissing('SERVICE_EVENT_FAULTS_AUTOMATION_REMOVED_FRIACO', 111);
configDefineIfMissing('SERVICE_EVENT_SENT_TO_DEBT_RECOVERY', 112);
configDefineIfMissing('SERVICE_EVENT_HOUSE_MOVE_TRIAL_REQUESTED', 113);

configDefineIfMissing('SERVICE_EVENT_PENDING_CONTRACT_CREATED', 116);

// VISTA HARDWARE
configDefineIfMissing('SERVICE_EVENT_VISTA_HARDWARE_ORDERED', 115);
configDefineIfMissing('SERVICE_EVENT_HOME_PHONE_RETENTION_OFFER', 122);

configDefineIfMissing('SERVICE_EVENT_SIGNUP_IMPORTED', 123);
configDefineIfMissing('SERVICE_EVENT_NO_CLI', 124);
configDefineIfMissing('SERVICE_EVENT_MANUAL_WLR_PROVIDE', 125);

// CONTRACT CHANGES
configDefineIfMissing('SERVICE_EVENT_12MONTH_CONTRACT_START_ADSL', 126);
configDefineIfMissing('SERVICE_EVENT_12MONTH_CONTRACT_START_WLR', 127);

// RESELLER ENDUSER RESTRICTIONS
configDefineIfMissing('SERVICE_EVENT_RESELLER_BROADBAND_RESTRICTION_ADDED', 128);
configDefineIfMissing('SERVICE_EVENT_RESELLER_BROADBAND_RESTRICTION_REMOVED', 129);

configDefineIfMissing('SERVICE_EVENT_HTT_AUTOMATION', 131);

//Register an ARUDDS event
configDefineIfMissing('SERVICE_EVENT_ARUDDS_EVENT_REGISTER', 134);

// Free Staff Home Phone Line Rental service_component_id
configDefineIfMissing('FREE_STAFF_SERVICE_DEFINITION_ID', 6359);
configDefineIfMissing('FREE_STAFF_LINE_RENTAL_COMPONENT_TYPE_ID', 855);


/**
 * Userdata Service Get
 *
 * @param int  $service_id Service Id
 * @param bool $get_new    Get New
 *
 * @return unknown
 */
function userdata_service_get($service_id, $get_new = false)
{
    $objUserdata = Lib_Userdata::singleton();
    $data = $objUserdata->userdata_service_get($service_id, $get_new);

    $data['restrictedUser'] = 0; // user is NOT restricted

    if (isset($data['enddate']) && date("Y-m-d 00:00:00") >= $data['enddate']) {
        // the user has left, and will now have limited access to their accounts
        // they must be able to change their passwords, edit their details and
        // view their bills and transactions
        $data['restrictedUser'] = 1;
    }

    return $data;
}

/**
 * Userdata Service Set Status
 *
 * @param unknown $service_id Service Id
 * @param unknown $status     Status
 *
 * @return unknown
 */
function userdata_service_set_status($service_id, $status)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_set_status($service_id, $status);
}

/**
 * Userdata Service Log Signup
 *
 * @param unknown $service_id            Service Id
 * @param unknown $service_definition_id Service Definition Id
 * @param unknown $now                   Now
 *
 * @return unknown
 */
function userdata_service_log_signup($service_id, $service_definition_id = '', $now = '')
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_log_signup($service_id, $service_definition_id, $now);
}

/**
 * Userdata Component Add
 *
 * @param unknown $service_id        Service Id
 * @param unknown $component_type_id Component Type Id
 * @param unknown $config_id         Config Id
 * @param unknown $description       Description
 * @param unknown $status            Status
 *
 * @return unknown
 */
function userdata_component_add($service_id, $component_type_id, $config_id, $description, $status)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_component_add($service_id, $component_type_id, $config_id, $description, $status);
}

/**
 * Userdata Service Is Reseller
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_service_is_reseller($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_is_reseller($service_id);
}

/**
 * Userdata User Get
 *
 * @param unknown $user_id    User Id
 * @param unknown $dont_cache Dont Cache
 *
 * @return unknown
 */
function userdata_user_get($user_id, $dont_cache = false)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_user_get($user_id, $dont_cache);
}

/**
 * Userdata Get Prices
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_get_prices($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_get_prices($service_id);
}

/**
 * Userdata Component Find
 *
 * @param unknown $criteria Criteria
 * @param unknown $start    Start
 * @param unknown $limit    Limit
 *
 * @return unknown
 */
function userdata_component_find($criteria, $start = '0', $limit = '0')
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_component_find($criteria, $start, $limit);
}

/**
 * Userdata User Find By Invoice
 *
 * @param unknown $criteria Criteria
 * @param unknown $start    Start
 * @param unknown $limit    Limit
 *
 * @return unknown
 */
function userdata_user_find_by_invoice($criteria, $start, $limit)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_user_find_by_invoice($criteria, $start, $limit);
}

/**
 * Userdata Get Details By Sales Receipt
 *
 * @param unknown $sales_receipt_id Sales Receipt Id
 *
 * @return unknown
 */
function userdata_get_details_by_sales_receipt($sales_receipt_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_get_details_by_sales_receipt($sales_receipt_id);
}

/**
 * Userdata User Find
 *
 * @param unknown $criteria Criteria
 * @param unknown $start    Start
 * @param unknown $limit    Limit
 * @param bool    $bolCount Count
 *
 * @return unknown
 */
function userdata_user_find($criteria, $start = 0, $limit = 0, $bolCount = false)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_user_find($criteria, $start, $limit, $bolCount);
}

/**
 * Userdata User Find By Cli
 *
 * @param unknown $cli_number    Cli Number
 * @param unknown $start         Start
 * @param unknown $limit         Limit
 * @param bool    $bolUseArchive Use Archive
 *
 * @return unknown
 */
function userdata_user_find_by_cli($cli_number, $start = '', $limit = '', $bolUseArchive = false)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_user_find_by_cli($cli_number, $start, $limit, $bolUseArchive);
}

/**
 * Userdata User Find By VSID
 *
 * @param unknown $vsid          VSID
 * @param bool    $bolUseArchive Use Archive
 *
 * @return unknown
 */
function userdata_user_find_by_vsid($vsid, $bolUseArchive = false)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_user_find_by_vsid($vsid, $bolUseArchive);
}

/**
 * Userdata User Find By Bt Circuit Number
 *
 * @param array $array_criteria Criteria
 *
 * @return unknown
 */
function userdata_user_find_by_bt_circuit_number($array_criteria)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_user_find_by_bt_circuit_number($array_criteria);
}

/**
 * Userdata Is Service Adsl Upgrade
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_is_service_adsl_upgrade($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_is_service_adsl_upgrade($service_id);
}

/**
 * Userdata Service Schedule Get
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_service_schedule_get($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_schedule_get($service_id);
}

/**
 * Userdata Service Is Resold
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_service_is_resold($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_is_resold($service_id);
}

/**
 * Userdata Service Note Find By Value
 *
 * @param unknown $service_id Service Id
 * @param unknown $value      Value
 * @param unknown $start      Start
 * @param unknown $limit      Limit
 *
 * @return unknown
 */
function userdata_service_note_find_by_value($service_id, $value, $start, $limit)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_note_find_by_value($service_id, $value, $start, $limit);
}

/**
 * Userdata Service Is Associated
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_service_is_associated($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_is_associated($service_id);
}

/**
 * Userdata Service Is Associate
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_service_is_associate($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_is_associate($service_id);
}

/**
 * Userdata Get Adsl Upgrade Services
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_get_adsl_upgrade_services($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_get_adsl_upgrade_services($service_id);
}

/**
 * Userdata Get Adsl Upgrade Main Service
 *
 * @param unknown $adsl_service_id Adsl Service Id
 *
 * @return unknown
 */
function userdata_get_adsl_upgrade_main_service($adsl_service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_get_adsl_upgrade_main_service($adsl_service_id);
}

/**
 * Userdata User Get Cli
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_user_get_cli($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_user_get_cli($service_id);
}

/**
 * Userdata Service Cost Get
 *
 * @param unknown $service_id Service Id
 * @param unknown $date       Date
 *
 * @return unknown
 */
function userdata_service_cost_get($service_id, $date = '')
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_cost_get($service_id, $date);
}

/**
 * Userdata Subscription Billing Component Charges Get
 *
 * @param unknown $service_id Service Id
 * @param bool    $bolMonthly Monthly
 *
 * @return unknown
 */
function userdata_subscription_billing_component_charges_get($service_id, $bolMonthly = false)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_subscription_billing_component_charges_get($service_id, $bolMonthly);
}

/**
 * Userdata Referrer Get
 *
 * @param unknown $referral_service_id Referral Service Id
 *
 * @return unknown
 */
function userdata_referrer_get($referral_service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_referrer_get($referral_service_id);
}

/**
 * Userdata Get Referrer Details
 *
 * @param int $intReferredId Referred Id
 *
 * @return unknown
 */
function userdataGetReferrerDetails($intReferredId)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataGetReferrerDetails($intReferredId);
}

/**
 * Userdata Referrals Get
 *
 * @param unknown $referral_from_sid                 Referral From Sid
 * @param unknown $suppress_old_emails_for_signed_up Suppress Old Emails For Signed Up
 *
 * @return unknown
 */
function userdata_referrals_get($referral_from_sid, $suppress_old_emails_for_signed_up = true)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_referrals_get($referral_from_sid, $suppress_old_emails_for_signed_up);
}

/**
 * Userdata Referer Get By Service Note
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_referer_get_by_service_note($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_referer_get_by_service_note($service_id);
}

/**
 * Userdata Component Get By Service
 *
 * @param unknown $service_id Service Id
 * @param bool    $bolForce   Force
 *
 * @return unknown
 */
function userdata_component_get_by_service($service_id, $bolForce = false)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_component_get_by_service($service_id, $bolForce);
}

/**
 * Userdata Pound Domain Decision
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_pound_domain_decision($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_pound_domain_decision($service_id);
}

/**
 * Userdata Service Has Encrypted Password
 *
 * @param int $intServiceID Service ID
 *
 * @return unknown
 */
function userdata_service_has_encrypted_password($intServiceID)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_has_encrypted_password($intServiceID);
}

/**
 * Userdata User Get Full
 *
 * @param unknown $user_id User Id
 * @param unknown $get_new Get New
 *
 * @return unknown
 */
function userdata_user_get_full($user_id, $get_new = false)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_user_get_full($user_id, $get_new);
}

/**
 * Userdata Account Get By User
 *
 * @param unknown $user_id            User Id
 * @param unknown $get_new            Get New
 * @param string  $strTransactionName Transaction Name
 *
 * @return unknown
 */
function userdata_account_get_by_user(
    $user_id,
    $get_new = false,
    $strTransactionName = 'TRANSACTION_ACCOUNT_GET_BY_USER'
) {

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_account_get_by_user($user_id, $get_new, $strTransactionName);
}

/**
 * Userdata Service Get Old Service Id
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_service_get_old_service_id($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_get_old_service_id($service_id);
}

/**
 * Userdata Account Is Special Offer Upgrade
 *
 * @param int $intServiceID Service ID
 *
 * @return unknown
 */
function UserdataAccountIsSpecialOfferUpgrade($intServiceID)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataAccountIsSpecialOfferUpgrade($intServiceID);
}

/**
 * Userdata Customer Has Opted Out Of Email
 *
 * @param int $intServiceID Service ID
 *
 * @return unknown
 */
function UserdataCustomerHasOptedOutOfEmail($intServiceID)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataCustomerHasOptedOutOfEmail($intServiceID);
}

/**
 * Userdata Service Is Free
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_service_is_free($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_is_free($service_id);
}

/**
 * Userdata Service Events Find
 *
 * @param array   $array_criteria Criteria
 * @param unknown $orderby        Order by
 * @param unknown $start          Start
 * @param unknown $limit          Limit
 *
 * @return unknown
 */
function userdata_service_events_find($array_criteria, $orderby = 'event_date', $start = 0, $limit = 0)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_events_find($array_criteria, $orderby, $start, $limit);
}

/**
 * Get Automated Cancellations End Date
 *
 * @param array $service a service array
 *
 * @return unknown
 */
function get_automated_cancellations_end_date($service)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->get_automated_cancellations_end_date($service);
}

/**
 * Userdata Get Framework Auth Realm
 *
 * @param int    $intServiceID Service ID
 * @param string $strUsername  Username
 * @param bool   $bolIsActive  Is the auth realm active
 *
 * @return unknown
 */
function userdataGetFrameworkAuthRealm($intServiceID, $strUsername, $bolIsActive = true)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataGetFrameworkAuthRealm($intServiceID, $strUsername, $bolIsActive);
}

/**
 * Is In 30day Trial
 *
 * @param int $intServiceId Service Id
 *
 * @return unknown
 */
function isIn30dayTrial($intServiceId)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->isIn30dayTrial($intServiceId);
}

/**
 * Is In 30day Trial By Cli
 *
 * @param string $strCliNumber Cli Number
 *
 * @return unknown
 */
function isIn30dayTrialByCli($strCliNumber)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->isIn30dayTrialByCli($strCliNumber);
}

/**
 * Userdata Pound Domain Add
 *
 * @param unknown $data Data
 *
 * @return unknown
 */
function userdata_pound_domain_add($data)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_pound_domain_add($data);
}

/**
 * Userdata Switch Authorisation Add
 *
 * @param unknown $service_id Service Id
 * @param unknown $type       Type
 *
 * @return unknown
 */
function userdata_switch_authorisation_add($service_id, $type = 'subscription')
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_switch_authorisation_add($service_id, $type);
}

/**
 * Userdata Customer Add
 *
 * @param unknown $primary_address_id Primary Address Id
 * @param unknown $primary_user_id    Primary User Id
 * @param unknown $company_name       Company Name
 *
 * @return unknown
 */
function userdata_customer_add($primary_address_id, $primary_user_id, $company_name)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_customer_add($primary_address_id, $primary_user_id, $company_name);
}

/**
 * Userdata Address Add
 *
 * @param unknown $customer_id Customer Id
 * @param unknown $house       House
 * @param string  $street      Eet
 * @param unknown $town        Town
 * @param unknown $county      County
 * @param unknown $postcode    Postcode
 * @param unknown $country     Country
 *
 * @return unknown
 */
function userdata_address_add($customer_id, $house, $street, $town, $county, $postcode, $country)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_address_add($customer_id, $house, $street, $town, $county, $postcode, $country);
}

/**
 * Userdata Address Destroy
 *
 * @param unknown $address_id Address Id
 *
 * @return unknown
 */
function userdata_address_destroy($address_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_address_destroy($address_id);
}

/**
 * Userdata User Add
 *
 * @param unknown $address_id         Address Id
 * @param unknown $customer_id        Customer Id
 * @param unknown $telephone          Telephone
 * @param unknown $fax                Fax
 * @param unknown $mobile             Mobile
 * @param unknown $email              Email
 * @param unknown $position           Position
 * @param unknown $salutation         Salutation
 * @param unknown $forenames          Forenames
 * @param unknown $surname            Surname
 * @param unknown $hint_question      Hint Question
 * @param unknown $hint_answer        Hint Answer
 * @param string  $strEveningPhone    Evening Phone
 * @param string  $strTransactionName Transaction Name
 *
 * @return unknown
 */
function userdata_user_add(
    $address_id,
    $customer_id,
    $telephone,
    $fax,
    $mobile,
    $email,
    $position,
    $salutation,
    $forenames,
    $surname,
    $hint_question,
    $hint_answer,
    $strEveningPhone,
    $strTransactionName = 'SPLIT_USERDATA_USER_ADD'
) {

    return Lib_Userdata::singleton()->userdata_user_add(
        $address_id,
        $customer_id,
        $telephone,
        $fax,
        $mobile,
        $email,
        $position,
        $salutation,
        $forenames,
        $surname,
        $hint_question,
        $hint_answer,
        $strEveningPhone,
        $strTransactionName
    );
}

/**
 * Attach a service to a user
 *
 * @param int    $user_id              Id of user record
 * @param int    $isp                  Tag of isp
 * @param string $username             Service's username
 * @param string $password             Service's password
 * @param string $cli_number           User's CLI number
 * @param int    $type                 Type of service (service definition id)
 * @param int    $first_invoice_offset Number of days from now for the first invoice date
 * @param string $invoice_period       Payment interval
 * @param bool   $add_signup_event     Whether to add a signup event to the new
 *
 * @return int New service id
 */
function userdata_service_add(
    $user_id,
    $isp,
    $username,
    $password,
    $cli_number,
    $type,
    $first_invoice_offset,
    $invoice_period,
    $add_signup_event = true
) {

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_add(
        $user_id,
        $isp,
        $username,
        $password,
        $cli_number,
        $type,
        $first_invoice_offset,
        $invoice_period,
        $add_signup_event,
        $marketId,
        $exchangeId
    );
}

/**
 * Attach exchange id and market id to a service
 *
 * @param int $serviceId  Service id
 * @param int $marketId   The ofcom market id (pk from dbExchange.tblMarket)
 * @param int $exchangeId Id of the exchange (pk from dbExchange.tblExchangeExchange)
 *
 * @return int New customer exhange id
 **/
function userdataCustomerExchangeAdd(
    $serviceId,
    $marketId,
    $exchangeId
) {

    $connection = get_named_connection('userdata');

    $exchangeId = !empty($exchangeId) ? $exchangeId : 'NULL';

    $query = "INSERT INTO tblCustomerExchange (intServiceId, intExchangeId, intMarketId) VALUES " .
        "($serviceId, $exchangeId, $marketId)";

    mysql_query($query, $connection)
    or report_error(__FILE__, __LINE__, mysql_error($connection));

    return mysql_insert_id($connection);
}


/**
 * Userdata Account Add
 *
 * @param unknown $customer_id          Customer Id
 * @param unknown $address_id           Address Id
 * @param bool    $bolIsBusinessAccount Is Business Account
 *
 * @return unknown
 */
function userdata_account_add($customer_id, $address_id, $bolIsBusinessAccount = false)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_account_add($customer_id, $address_id, $bolIsBusinessAccount);
}

/**
 * Userdata Outstanding Transaction Add
 *
 * @param unknown $invoice_id     Invoice Id
 * @param unknown $account_id     Account Id
 * @param unknown $transaction_id Transaction Id
 *
 * @return unknown
 */
function userdata_outstanding_transaction_add($invoice_id, $account_id, $transaction_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_outstanding_transaction_add($invoice_id, $account_id, $transaction_id);
}

/**
 * Userdata Service Note Add
 *
 * @param unknown $service_id Service Id
 * @param unknown $note_type  Note Type
 * @param unknown $note       Note
 * @param unknown $entered_by Entered By
 *
 * @return unknown
 */
function userdata_service_note_add($service_id, $note_type, $note, $entered_by)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_note_add($service_id, $note_type, $note, $entered_by);
}

/**
 * Userdata Application Credit Account Add
 *
 * @param unknown $customer_id               Customer Id
 * @param unknown $address_id                Address Id
 * @param unknown $company_registration_no   Company Registration No
 * @param unknown $company_name              Company Name
 * @param unknown $company_postcode          Company Postcode
 * @param unknown $telephone                 Telephone
 * @param unknown $fax                       Fax
 * @param unknown $company_email             Company Email
 * @param unknown $no_of_pcs                 No Of Pcs
 * @param unknown $no_of_employees           No Of Employees
 * @param unknown $account_contact           Account Contact
 * @param unknown $director_name_1           Director Name 1
 * @param unknown $director_address_1        Director Address 1
 * @param unknown $director_position_1       Director Position 1
 * @param unknown $director_name_2           Director Name 2
 * @param unknown $director_address_2        Director Address 2
 * @param unknown $director_position_2       Director Position 2
 * @param unknown $director_name_3           Director Name 3
 * @param unknown $director_address_3        Director Address 3
 * @param unknown $director_position_3       Director Position 3
 * @param unknown $director_name_4           Director Name 4
 * @param unknown $director_address_4        Director Address 4
 * @param unknown $director_position_4       Director Position 4
 * @param unknown $trade_reference_name_1    Trade Reference Name 1
 * @param unknown $trade_reference_address_1 Trade Reference Address 1
 * @param unknown $trade_reference_tel_fax_1 Trade Reference Tel Fax 1
 * @param unknown $trade_reference_name_2    Trade Reference Name 2
 * @param unknown $trade_reference_address_2 Trade Reference Address 2
 * @param unknown $trade_reference_tel_fax_2 Trade Reference Tel Fax 2
 * @param unknown $company_street_no         Company Street No
 * @param unknown $company_street_name       Company Street Name
 * @param unknown $company_town              Company Town
 * @param unknown $company_county            Company County
 * @param unknown $std_phone                 Std Phone
 * @param unknown $std_fax                   Std Fax
 *
 * @return unknown
 */
function userdata_application_credit_account_add(
    $customer_id,
    $address_id,
    $company_registration_no,
    $company_name,
    $company_postcode,
    $telephone,
    $fax,
    $company_email,
    $no_of_pcs,
    $no_of_employees,
    $account_contact,
    $director_name_1,
    $director_address_1,
    $director_position_1,
    $director_name_2,
    $director_address_2,
    $director_position_2,
    $director_name_3,
    $director_address_3,
    $director_position_3,
    $director_name_4,
    $director_address_4,
    $director_position_4,
    $trade_reference_name_1,
    $trade_reference_address_1,
    $trade_reference_tel_fax_1,
    $trade_reference_name_2,
    $trade_reference_address_2,
    $trade_reference_tel_fax_2,
    $company_street_no,
    $company_street_name,
    $company_town,
    $company_county,
    $std_phone,
    $std_fax
) {

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_application_credit_account_add(
        $customer_id,
        $address_id,
        $company_registration_no,
        $company_name,
        $company_postcode,
        $telephone,
        $fax,
        $company_email,
        $no_of_pcs,
        $no_of_employees,
        $account_contact,
        $director_name_1,
        $director_address_1,
        $director_position_1,
        $director_name_2,
        $director_address_2,
        $director_position_2,
        $director_name_3,
        $director_address_3,
        $director_position_3,
        $director_name_4,
        $director_address_4,
        $director_position_4,
        $trade_reference_name_1,
        $trade_reference_address_1,
        $trade_reference_tel_fax_1,
        $trade_reference_name_2,
        $trade_reference_address_2,
        $trade_reference_tel_fax_2,
        $company_street_no,
        $company_street_name,
        $company_town,
        $company_county,
        $std_phone,
        $std_fax
    );
}

/**
 * Userdata User Profile Add
 *
 * @param unknown $user_id           User Id
 * @param unknown $gender            Gender
 * @param unknown $maritalstatus     Maritalstatus
 * @param unknown $birthday          Birthday
 * @param unknown $occupation        Occupation
 * @param unknown $income            Income
 * @param int     $interests         Erests
 * @param unknown $currentconnection Currentconnection
 * @param unknown $pctype            Pctype
 * @param unknown $educationlevel    Educationlevel
 * @param unknown $numberofusers     Numberofusers
 * @param unknown $usagetype         Usagetype
 * @param unknown $referrer          Referrer
 * @param int     $internaloptout    Ernaloptout
 * @param unknown $externaloptout    Externaloptout
 *
 * @return unknown
 */
function userdata_user_profile_add(
    $user_id,
    $gender,
    $maritalstatus,
    $birthday,
    $occupation,
    $income,
    $interests,
    $currentconnection,
    $pctype,
    $educationlevel,
    $numberofusers,
    $usagetype,
    $referrer,
    $internaloptout,
    $externaloptout
) {

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_user_profile_add(
        $user_id,
        $gender,
        $maritalstatus,
        $birthday,
        $occupation,
        $income,
        $interests,
        $currentconnection,
        $pctype,
        $educationlevel,
        $numberofusers,
        $usagetype,
        $referrer,
        $internaloptout,
        $externaloptout
    );
}

/**
 * Userdata Service Associate
 *
 * @param unknown $service_id            Service Id
 * @param unknown $associated_service_id Associated Service Id
 *
 * @return unknown
 */
function userdata_service_associate($service_id, $associated_service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_associate($service_id, $associated_service_id);
}

/**
 * Userdata Retrofit Component
 *
 * @param unknown $service_definition_id Service Definition Id
 * @param unknown $component_type_id     Component Type Id
 *
 * @return unknown
 */
function userdata_retrofit_component($service_definition_id, $component_type_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_retrofit_component($service_definition_id, $component_type_id);
}

/**
 * Userdata Adsl Upgrade Service Associate
 *
 * @param unknown $service_id      Service Id
 * @param unknown $adsl_service_id Adsl Service Id
 *
 * @return unknown
 */
function userdata_adsl_upgrade_service_associate($service_id, $adsl_service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_adsl_upgrade_service_associate($service_id, $adsl_service_id);
}

/**
 * Userdata Write Leased Line Component
 *
 * @param unknown $data   Data
 * @param unknown $update Update
 *
 * @return unknown
 */
function userdata_write_leased_line_component($data, $update = 'false')
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_write_leased_line_component($data, $update);
}

/**
 * Userdata Create Signup Referral
 *
 * @param int    $intReferrerServiceID Referrer Service ID
 * @param int    $intReferralServiceID Referral Service ID
 * @param string $strReferredEmail     Referred Email
 * @param string $strAccountType       Account Type
 *
 * @return unknown
 */
function UserdataCreateSignupReferral(
    $intReferrerServiceID,
    $intReferralServiceID = 0,
    $strReferredEmail = '',
    $strAccountType = ''
) {
    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataCreateSignupReferral(
        $intReferrerServiceID,
        $intReferralServiceID,
        $strReferredEmail,
        $strAccountType
    );
}

/**
 * Userdata Referral Add
 *
 * @param int    $intReferralServiceID Referral Service ID
 * @param int    $intReferrerServiceID Referrer Service ID
 * @param string $strLocation          Location
 *
 * @return unknown
 */
function userdata_referral_add($intReferralServiceID, $intReferrerServiceID, $strLocation = 'new_signup')
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_referral_add($intReferralServiceID, $intReferrerServiceID, $strLocation);
}

/**
 * Userdata Generate Invoice Date
 *
 * @param int $intServiceId Service Id
 *
 * @return unknown
 */
function userdata_generate_invoice_date($intServiceId)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_generate_invoice_date($intServiceId);
}

/**
 * Userdata Get Next Subscription Payment
 *
 * @param int $intServiceId Service Id
 *
 * @return unknown
 */
function userdataGetNextSubscriptionPayment($intServiceId)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataGetNextSubscriptionPayment($intServiceId);
}

/**
 * Userdata Referral New Banner Ad
 *
 * @param int    $intReferrerServiceID Referrer Service ID
 * @param string $strAccountType       Account Type
 *
 * @return unknown
 */
function UserdataReferralNewBannerAd($intReferrerServiceID, $strAccountType)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataReferralNewBannerAd($intReferrerServiceID, $strAccountType);
}

/**
 * Userdata Referral New Webmail
 *
 * @param int    $intReferrerServiceID Referrer Service ID
 * @param string $strAccountType       Account Type
 *
 * @return unknown
 */
function UserdataReferralNewWebmail($intReferrerServiceID, $strAccountType)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataReferralNewWebmail($intReferrerServiceID, $strAccountType);
}

/**
 * Userdata Referral New Email
 *
 * @param int    $intReferrerServiceID Referrer Service ID
 * @param string $strReferredEmail     Referred Email
 * @param string $strAccountType       Account Type
 *
 * @return unknown
 */
function UserdataReferralNewEmail($intReferrerServiceID, $strReferredEmail, $strAccountType)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataReferralNewEmail($intReferrerServiceID, $strReferredEmail, $strAccountType);
}

/**
 * Userdata Referral Limit Exceeded
 *
 * @param int $intReferrerServiceID Referrer Service ID
 * @param int $intLimit             Limit
 *
 * @return unknown
 */
function UserdataReferralLimitExceeded($intReferrerServiceID, $intLimit)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataReferralLimitExceeded($intReferrerServiceID, $intLimit);
}

/**
 * Userdata Account Is A Referral
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_account_is_a_referral($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_account_is_a_referral($service_id);
}

/**
 * Userdata Referral Set
 *
 * @param unknown $signup_sid   Signup Sid
 * @param unknown $referrer_sid Referrer Sid
 * @param unknown $referral_uid Referral Uid
 *
 * @return unknown
 */
function userdata_referral_set($signup_sid, $referrer_sid, $referral_uid)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_referral_set($signup_sid, $referrer_sid, $referral_uid);
}

/**
 * Userdata Component Set Config Id
 *
 * @param unknown $component_id Component Id
 * @param unknown $config_id    Config Id
 *
 * @return unknown
 */
function userdata_component_set_config_id($component_id, $config_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_component_set_config_id($component_id, $config_id);
}

/**
 * Userdata Component Change Type Id
 *
 * @param unknown $component_id          Component Id
 * @param unknown $new_component_type_id New Component Type Id
 *
 * @return unknown
 */
function userdata_component_change_type_id($component_id, $new_component_type_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_component_change_type_id($component_id, $new_component_type_id);
}

/**
 * Userdata Service Type Change Log Add
 *
 * @param unknown $service_id       Service Id
 * @param unknown $from_type_id     From Type Id
 * @param unknown $to_type_id       To Type Id
 * @param unknown $days_free_trial  Days Free Trial
 * @param unknown $old_next_invoice Old Next Invoice
 * @param unknown $new_next_invoice New Next Invoice
 *
 * @return unknown
 */
function userdata_service_type_change_log_add(
    $service_id,
    $from_type_id,
    $to_type_id,
    $days_free_trial = 0,
    $old_next_invoice = '0000-00-00',
    $new_next_invoice = '0000-00-00'
) {

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_type_change_log_add(
        $service_id,
        $from_type_id,
        $to_type_id,
        $days_free_trial,
        $old_next_invoice,
        $new_next_invoice
    );
}

/**
 * Userdata Connect Policing Event Add
 *
 * @param unknown $service_id            Service Id
 * @param unknown $service_definition_id Service Definition Id
 * @param unknown $dialup_group_id       Dialup Group Id
 * @param unknown $seconds_allowed       Seconds Allowed
 * @param unknown $seconds_used          Seconds Used
 * @param unknown $breach_date_start     Breach Date Start
 * @param unknown $breach_date_end       Breach Date End
 *
 * @return unknown
 */
function userdata_connect_policing_event_add(
    $service_id,
    $service_definition_id,
    $dialup_group_id,
    $seconds_allowed,
    $seconds_used,
    $breach_date_start,
    $breach_date_end
) {

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_connect_policing_event_add(
        $service_id,
        $service_definition_id,
        $dialup_group_id,
        $seconds_allowed,
        $seconds_used,
        $breach_date_start,
        $breach_date_end
    );
}

/**
 * Userdata Connect Policing Dialup Cancellation Add
 *
 * @param unknown $service_id              Service Id
 * @param unknown $account_type_id         Account Type Id
 * @param unknown $num_used_free_domains   Num Used Free Domains
 * @param unknown $num_unused_free_domains Num Unused Free Domains
 *
 * @return unknown
 */
function userdata_connect_policing_dialup_cancellation_add(
    $service_id,
    $account_type_id,
    $num_used_free_domains,
    $num_unused_free_domains
) {

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_connect_policing_dialup_cancellation_add(
        $service_id,
        $account_type_id,
        $num_used_free_domains,
        $num_unused_free_domains
    );
}

/**
 * Userdata Start Service Last Used
 *
 * @param unknown $service_id Service Id
 * @param unknown $time       Time
 *
 * @return unknown
 */
function userdata_start_service_last_used($service_id, $time)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_start_service_last_used($service_id, $time);
}

/**
 * Userdata Service Event Add
 *
 * @param unknown $service_id     Service Id
 * @param unknown $event_type_id  Event Type Id
 * @param unknown $from_type      From Type
 * @param unknown $to_type        To Type
 * @param unknown $next_invoice   Next Invoice
 * @param unknown $invoice_id     Invoice Id
 * @param unknown $scheduled_date Scheduled Date
 * @param unknown $event_date     Event Date
 * @param unknown $other_event_id Other Event Id
 * @param unknown $component_id   Component Id
 * @param string  $strUserId      User Id
 *
 * @return unknown
 */
function userdata_service_event_add(
    $service_id,
    $event_type_id,
    $from_type = '',
    $to_type = '',
    $next_invoice = '',
    $invoice_id = '',
    $scheduled_date = '',
    $event_date = '',
    $other_event_id = '',
    $component_id = '',
    $strUserId = ''
) {

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_event_add(
        $service_id,
        $event_type_id,
        $from_type,
        $to_type,
        $next_invoice,
        $invoice_id,
        $scheduled_date,
        $event_date,
        $other_event_id,
        $component_id,
        $strUserId
    );
}

/**
 * Userdata Service Log Preregister
 *
 * @param unknown $service_id            Service Id
 * @param unknown $service_definition_id Service Definition Id
 * @param unknown $now                   Now
 *
 * @return unknown
 */
function userdata_service_log_preregister($service_id, $service_definition_id = '', $now = '')
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_log_preregister($service_id, $service_definition_id, $now);
}

/**
 * Userdata Service Log Cancelled Preregister
 *
 * @param unknown $service_id            Service Id
 * @param unknown $service_definition_id Service Definition Id
 * @param unknown $now                   Now
 *
 * @return unknown
 */
function userdata_service_log_cancelled_preregister($service_id, $service_definition_id = '', $now = '')
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_log_cancelled_preregister($service_id, $service_definition_id, $now);
}

/**
 * Userdata Service Log Scheduled Cancellation
 *
 * @param unknown $service_id            Service Id
 * @param unknown $scheduled_date        Scheduled Date
 * @param unknown $service_definition_id Service Definition Id
 * @param unknown $now                   Now
 *
 * @return unknown
 */
function userdata_service_log_scheduled_cancellation(
    $service_id,
    $scheduled_date,
    $service_definition_id = '',
    $now = ''
) {

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_log_scheduled_cancellation(
        $service_id,
        $scheduled_date,
        $service_definition_id,
        $now
    );
}

/**
 * Userdata Service Log Account Conversion On Cancellation
 *
 * @param int $intServiceID                     Service ID
 * @param int $intCancelledServiceDefinitionID  Cancelled Service Definition ID
 * @param int $intConversionServiceDefinitionID Conversion Service Definition ID
 * @param int $intConvertingComponentID         Converting Component ID
 *
 * @return unknown
 */
function userdata_service_log_account_conversion_on_cancellation(
    $intServiceID,
    $intCancelledServiceDefinitionID,
    $intConversionServiceDefinitionID,
    $intConvertingComponentID
) {

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_log_account_conversion_on_cancellation(
        $intServiceID,
        $intCancelledServiceDefinitionID,
        $intConversionServiceDefinitionID,
        $intConvertingComponentID
    );
}

/**
 * Userdata Service Log Cancellation
 *
 * @param unknown $service_id            Service Id
 * @param unknown $service_definition_id Service Definition Id
 * @param unknown $now                   Now
 *
 * @return unknown
 */
function userdata_service_log_cancellation($service_id, $service_definition_id = '', $now = '')
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_log_cancellation($service_id, $service_definition_id, $now);
}

/**
 * Userdata Service Log Actioned Scheduled Cancellation
 *
 * @param unknown $service_id            Service Id
 * @param unknown $service_definition_id Service Definition Id
 * @param unknown $now                   Now
 *
 * @return unknown
 */
function userdata_service_log_actioned_scheduled_cancellation($service_id, $service_definition_id = '', $now = '')
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_log_actioned_scheduled_cancellation(
        $service_id,
        $service_definition_id,
        $now
    );
}

/**
 * Userdata Get Latest Service Event
 *
 * @param int    $intServiceID      Service ID
 * @param string $strCurrentProduct Current Product
 *
 * @return unknown
 */
function UserdataGetLatestServiceEvent($intServiceID, $strCurrentProduct)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataGetLatestServiceEvent($intServiceID, $strCurrentProduct);
}

/**
 * Userdata Get Next Monthly Invoice Date
 *
 * @param int $intServiceId Service Id
 *
 * @return unknown
 */
function UserdataGetNextMonthlyInvoiceDate($intServiceId)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataGetNextMonthlyInvoiceDate($intServiceId);
}

/**
 * Userdata Get Monthly Invoice Period
 *
 * @param int $intServiceId Service Id
 * @param int $uxtDate      Date
 *
 * @return unknown
 */
function UserdataGetMonthlyInvoicePeriod($intServiceId, $uxtDate = null)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataGetMonthlyInvoicePeriod($intServiceId, $uxtDate);
}

/**
 * Userdata Service Get Next Next Invoice Date
 *
 * @param int $intServiceId Service Id
 *
 * @return unknown
 */
function UserdataServiceGetNextNextInvoiceDate($intServiceId)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataServiceGetNextNextInvoiceDate($intServiceId);
}

/**
 * Userdata Service Get Scheduled Change Date
 *
 * @param int $intServiceId Service Id
 *
 * @return unknown
 */
function UserdataServiceGetScheduledChangeDate($intServiceId)
{
    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataServiceGetScheduledChangeDate($intServiceId);
}

/**
 * Userdata Service Log Scheduled Type Change
 *
 * @param unknown $service_id     Service Id
 * @param unknown $scheduled_date Scheduled Date
 * @param unknown $to_type_id     To Type Id
 * @param unknown $from_type_id   From Type Id
 * @param unknown $now            Now
 * @param string  $strUserId      User Id
 *
 * @return unknown
 */
function userdata_service_log_scheduled_type_change(
    $service_id,
    $scheduled_date,
    $to_type_id,
    $from_type_id = '',
    $now = '',
    $strUserId = ''
) {

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_log_scheduled_type_change(
        $service_id,
        $scheduled_date,
        $to_type_id,
        $from_type_id,
        $now,
        $strUserId
    );
}

/**
 * Userdata Service Log Type Change
 *
 * @param unknown $service_id       Service Id
 * @param unknown $old_type         Old Type
 * @param unknown $new_type         New Type
 * @param unknown $new_next_invoice New Next Invoice
 * @param unknown $now              Now
 * @param string  $strUserId        User Id
 *
 * @return unknown
 */
function userdata_service_log_type_change(
    $service_id,
    $old_type,
    $new_type,
    $new_next_invoice = '',
    $now = '',
    $strUserId = ''
) {

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_log_type_change(
        $service_id,
        $old_type,
        $new_type,
        $new_next_invoice,
        $now,
        $strUserId
    );
}

/**
 * Userdata Service Log Actioned Scheduled Type Change
 *
 * @param unknown $service_id   Service Id
 * @param unknown $from_type_id From Type Id
 * @param unknown $to_type_id   To Type Id
 * @param unknown $now          Now
 *
 * @return unknown
 */
function userdata_service_log_actioned_scheduled_type_change($service_id, $from_type_id, $to_type_id, $now = '')
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_log_actioned_scheduled_type_change(
        $service_id,
        $from_type_id,
        $to_type_id,
        $now
    );
}

/**
 * Userdata Log Removed Scheduled Type Change
 *
 * @param unknown $service_id   Service Id
 * @param unknown $from_type_id From Type Id
 * @param unknown $to_type_id   To Type Id
 * @param unknown $now          Now
 *
 * @return unknown
 */
function userdata_log_removed_scheduled_type_change($service_id, $from_type_id, $to_type_id, $now = '')
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_log_removed_scheduled_type_change($service_id, $from_type_id, $to_type_id, $now);
}

/**
 * Userdata Service Log Reactivation
 *
 * @param unknown $service_id            Service Id
 * @param unknown $service_definition_id Service Definition Id
 * @param unknown $now                   Now
 *
 * @return unknown
 */
function userdata_service_log_reactivation($service_id, $service_definition_id = '', $now = '')
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_log_reactivation($service_id, $service_definition_id, $now);
}

/**
 * Userdata Service Log Disable
 *
 * @param unknown $service_id            Service Id
 * @param unknown $service_definition_id Service Definition Id
 * @param unknown $now                   Now
 *
 * @return unknown
 */
function userdata_service_log_disable($service_id, $service_definition_id = '', $now = '')
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_log_disable($service_id, $service_definition_id, $now);
}

/**
 * Userdata Service Log Enable
 *
 * @param unknown $service_id            Service Id
 * @param unknown $service_definition_id Service Definition Id
 * @param unknown $now                   Now
 *
 * @return unknown
 */
function userdata_service_log_enable($service_id, $service_definition_id = '', $now = '')
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_log_enable($service_id, $service_definition_id, $now);
}

/**
 * Userdata Log Removed Scheduled Cancellation
 *
 * @param unknown $service_id            Service Id
 * @param unknown $service_definition_id Service Definition Id
 * @param unknown $now                   Now
 *
 * @return unknown
 */
function userdata_log_removed_scheduled_cancellation($service_id, $service_definition_id = '', $now = '')
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_log_removed_scheduled_cancellation($service_id, $service_definition_id, $now);
}

/**
 * Userdata Log Adsl Upgrade
 *
 * @param unknown $service_id  Service Id
 * @param unknown $new_type_id New Type Id
 * @param unknown $old_type_id Old Type Id
 * @param unknown $now         Now
 *
 * @return unknown
 */
function userdata_log_adsl_upgrade($service_id, $new_type_id, $old_type_id = '', $now = '')
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_log_adsl_upgrade($service_id, $new_type_id, $old_type_id, $now);
}

/**
 * Userdata Log Adsl Preupgrade
 *
 * @param unknown $service_id  Service Id
 * @param unknown $new_type_id New Type Id
 * @param unknown $old_type_id Old Type Id
 * @param unknown $now         Now
 *
 * @return unknown
 */
function userdata_log_adsl_preupgrade($service_id, $new_type_id, $old_type_id = '', $now = '')
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_log_adsl_preupgrade($service_id, $new_type_id, $old_type_id, $now);
}

/**
 * Userdata Log Deferred Adsl Activation
 *
 * @param int $intServiceID       Service ID
 * @param int $intServiceDefId    Service Def Id
 * @param int $intServiceDefIdNew Service Def Id New
 *
 * @return unknown
 */
function UserdataLogDeferredAdslActivation($intServiceID, $intServiceDefId, $intServiceDefIdNew = '')
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataLogDeferredAdslActivation($intServiceID, $intServiceDefId, $intServiceDefIdNew);
}

/**
 * Userdata Log Adsl Activated
 *
 * @param unknown $service_id     Service Id
 * @param unknown $service_def_id Service Def Id
 * @param unknown $now            Now
 *
 * @return unknown
 */
function userdata_log_adsl_activated($service_id, $service_def_id = '', $now = '')
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_log_adsl_activated($service_id, $service_def_id, $now);
}

/**
 * Userdata Log Type Migration
 *
 * @param unknown $service_id Service Id
 * @param unknown $from_type  From Type
 * @param unknown $to_type    To Type
 * @param unknown $now        Now
 *
 * @return unknown
 */
function userdata_log_type_migration($service_id, $from_type, $to_type, $now = '')
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_log_type_migration($service_id, $from_type, $to_type, $now);
}

/**
 * Userdata Log Type Group Created
 *
 * @param unknown $service_id Service Id
 * @param unknown $group_id   Group Id
 *
 * @return unknown
 */
function userdata_log_type_group_created($service_id, $group_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_log_type_group_created($service_id, $group_id);
}

/**
 * Userdata Log Type Joined Group
 *
 * @param unknown $service_id Service Id
 * @param unknown $group_id   Group Id
 *
 * @return unknown
 */
function userdata_log_type_joined_group($service_id, $group_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_log_type_joined_group($service_id, $group_id);
}

/**
 * Userdata Log Type Deleted Group
 *
 * @param unknown $service_id Service Id
 * @param unknown $group_id   Group Id
 *
 * @return unknown
 */
function userdata_log_type_deleted_group($service_id, $group_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_log_type_deleted_group($service_id, $group_id);
}

/**
 * Userdata Log Type Left Group
 *
 * @param unknown $service_id Service Id
 * @param unknown $group_id   Group Id
 *
 * @return unknown
 */
function userdata_log_type_left_group($service_id, $group_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_log_type_left_group($service_id, $group_id);
}

/**
 * Userdata Log Type Rescinded Ownership Of Group
 *
 * @param unknown $service_id              Service Id
 * @param unknown $group_id                Group Id
 * @param unknown $service_id_of_new_owner Service Id Of New Owner
 *
 * @return unknown
 */
function userdata_log_type_rescinded_ownership_of_group($service_id, $group_id, $service_id_of_new_owner)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_log_type_rescinded_ownership_of_group(
        $service_id,
        $group_id,
        $service_id_of_new_owner
    );
}

/**
 * Userdata Log Type Took Ownership Of Group
 *
 * @param unknown $service_id              Service Id
 * @param unknown $group_id                Group Id
 * @param unknown $service_id_of_old_owner Service Id Of Old Owner
 *
 * @return unknown
 */
function userdata_log_type_took_ownership_of_group($service_id, $group_id, $service_id_of_old_owner)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_log_type_took_ownership_of_group($service_id, $group_id, $service_id_of_old_owner);
}

/**
 * Userdata Demographics Add
 *
 * @param unknown $service_id                      Service Id
 * @param int     $internet_usage                  Ernet Usage
 * @param unknown $number_of_pcs                   Number Of Pcs
 * @param unknown $number_of_satellite_offices     Number Of Satellite Offices
 * @param unknown $number_of_home_workers          Number Of Home Workers
 * @param unknown $os_used                         Os Used
 * @param int     $intention_to_upgrade_net_access Ention To Upgrade Net Access
 * @param unknown $download_large_files            Download Large Files
 *
 * @return unknown
 */
function userdata_demographics_add(
    $service_id,
    $internet_usage,
    $number_of_pcs,
    $number_of_satellite_offices,
    $number_of_home_workers,
    $os_used,
    $intention_to_upgrade_net_access,
    $download_large_files
) {

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_demographics_add(
        $service_id,
        $internet_usage,
        $number_of_pcs,
        $number_of_satellite_offices,
        $number_of_home_workers,
        $os_used,
        $intention_to_upgrade_net_access,
        $download_large_files
    );
}

/**
 * Userdata Demographics Alter
 *
 * @param unknown $service_id                      Service Id
 * @param int     $internet_usage                  Ernet Usage
 * @param unknown $number_of_pcs                   Number Of Pcs
 * @param unknown $number_of_satellite_offices     Number Of Satellite Offices
 * @param unknown $number_of_home_workers          Number Of Home Workers
 * @param unknown $os_used                         Os Used
 * @param int     $intention_to_upgrade_net_access Ention To Upgrade Net Access
 * @param unknown $download_large_files            Download Large Files
 *
 * @return unknown
 */
function userdata_demographics_alter(
    $service_id,
    $internet_usage,
    $number_of_pcs,
    $number_of_satellite_offices,
    $number_of_home_workers,
    $os_used,
    $intention_to_upgrade_net_access,
    $download_large_files
) {
    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_demographics_alter(
        $service_id,
        $internet_usage,
        $number_of_pcs,
        $number_of_satellite_offices,
        $number_of_home_workers,
        $os_used,
        $intention_to_upgrade_net_access,
        $download_large_files
    );
}

/**
 * Userdata Signup Via Landingpage Report
 *
 * @param unknown $HitLandingPageBeginDate Hit Landing Page Begin Date
 * @param unknown $HitLandingPageEndDate   Hit Landing Page End Date
 *
 * @return unknown
 */
function userdata_signup_via_landingpage_report($HitLandingPageBeginDate, $HitLandingPageEndDate)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_signup_via_landingpage_report($HitLandingPageBeginDate, $HitLandingPageEndDate);
}

/**
 * Userdata Signup Via Landingpage
 *
 * @param int    $service_id             Service Id
 * @param string $landing_page           Landing Page
 * @param int    $when_visited_timestamp When Visited Timestamp
 * @param string $error_message          Error Message
 *
 * @return unknown
 */
function userdata_signup_via_landingpage($service_id, $landing_page, $when_visited_timestamp, &$error_message)
{
    $FunctionCreatedUnixTimestamp = 1034872384;

    $error_message = '';
    if (!preg_match('/^[0-9]+$/', $service_id)) {
        $error_message = "service_id '$service_id' is invalid.";

        return (false);
    }

    if ($landing_page == '') {
        $error_message = "landing_page '$landing_page' is invalid.";

        return (false);
    }

    if ((!preg_match('/^[0-9]+$/', $when_visited_timestamp))
        || ($when_visited_timestamp < $FunctionCreatedUnixTimestamp)
    ) {
        $error_message = "unix timestamp of landing page visit  '$FunctionCreatedUnixTimestamp' is invalid.";

        return (false);
    }

    $landing_page = addslashes($landing_page);

    $connection = get_named_connection('userdata');

    $query = 'INSERT INTO signup_via_landingpage ' .
        '(landing_page,service_id,date_landing_page_visited,time_landing_page_visited,date_signedup,time_signedup) ' .
        "VALUES ('$landing_page','$service_id',from_unixtime($when_visited_timestamp), " .
        "from_unixtime($when_visited_timestamp),now(), now()) ";

    mysql_query($query, $connection) or error_log(__FILE__, __LINE__, mysql_error($connection));
    if (mysql_errno($connection)) {
        error_log(
            __FILE__ . __LINE__ . " Failed to add signup_via_landingpage \n \t '$query' "
            . mysql_error($connection)
        );

        return (false);
    }

    $InsertID = mysql_insert_id($connection);

    return ($InsertID);
}

/**
 * Userdata Make Reseller
 *
 * @param int $intServiceID Service Id
 *
 * @return unknown
 */
function userdata_make_reseller($intServiceID)
{
    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_make_reseller($intServiceID);
}

/**
 * Userdata Make Resold
 *
 * @param int $intResellerServiceID Reseller Service ID
 * @param int $intResoldServiceID   Resold Service ID
 *
 * @return unknown
 */
function userdata_make_resold($intResellerServiceID, $intResoldServiceID)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_make_resold($intResellerServiceID, $intResoldServiceID);
}

/**
 * Userdata Insert IPBlock Request
 *
 * @param int $intServiceID Service ID
 * @param int $intBlockSize Block Size
 *
 * @return unknown
 */
function userdataInsertIPBlockRequest($intServiceID, $intBlockSize)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataInsertIPBlockRequest($intServiceID, $intBlockSize);
}

/**
 * Userdata Is Prereg Account
 *
 * @param int $intServiceId Service Id
 *
 * @return unknown
 */
function userdata_is_prereg_account($intServiceId)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_is_prereg_account($intServiceId);
}

/**
 * Userdata Is Temp Adsl Account
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_is_temp_adsl_account($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_is_temp_adsl_account($service_id);
}

/**
 * Userdata Is Switch Account
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_is_switch_account($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_is_switch_account($service_id);
}

/**
 * Userdata Get Outstanding Switch Authorisation Request
 *
 * @param unknown $service_id        Service Id
 * @param unknown $next_invoice_date Next Invoice Date
 * @param unknown $transaction_type  Transaction Type
 *
 * @return unknown
 */
function userdata_get_outstanding_switch_authorisation_request(
    $service_id,
    $next_invoice_date = '',
    $transaction_type = 'subscription'
) {

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_get_outstanding_switch_authorisation_request(
        $service_id,
        $next_invoice_date,
        $transaction_type
    );
}

/**
 * Userdata Get Event Id
 *
 * @param string $strEventName Event Name
 *
 * @return unknown
 */
function UserdataGetEventId($strEventName)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataGetEventId($strEventName);
}

/**
 * Userdata Moa User Get
 *
 * @param unknown $isp_or_config_isp_id Isp Or Config Isp Id
 * @param unknown $moa_user_id          Moa User Id
 *
 * @return unknown
 */
function userdata_moa_user_get($isp_or_config_isp_id, $moa_user_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_moa_user_get($isp_or_config_isp_id, $moa_user_id);
}

/**
 * Userdata Bandwidth Abuse Records Get
 *
 * @param array $array_criteria Criteria
 *
 * @return unknown
 */
function userdata_bandwidth_abuse_records_get($array_criteria)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_bandwidth_abuse_records_get($array_criteria);
}

/**
 * Userdata Get Service IDBy CLI
 *
 * @param int $intCLINumber CLINumber
 *
 * @return unknown
 */
function UserdataGetServiceIDByCLI($intCLINumber)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataGetServiceIDByCLI($intCLINumber);
}

/**
 * Find Recycled User
 *
 * @param string $strUsername     Username
 * @param string $strDBConnection DBConnection
 *
 * @return unknown
 */
function FindRecycledUser($strUsername, $strDBConnection = 'userdata')
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->FindRecycledUser($strUsername, $strDBConnection);
}

/**
 * Userdata Customer Get
 *
 * @param unknown $customer_id Customer Id
 * @param unknown $dont_cache  Dont Cache
 *
 * @return unknown
 */
function userdata_customer_get($customer_id, $dont_cache = false)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_customer_get($customer_id, $dont_cache);
}

/**
 * Userdata Customer Get Primary User
 *
 * @param unknown $customer_id Customer Id
 *
 * @return unknown
 */
function userdata_customer_get_primary_user($customer_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_customer_get_primary_user($customer_id);
}

/**
 * Userdata Address Get
 *
 * @param unknown $address_id Address Id
 * @param unknown $cache      Cache
 *
 * @return unknown
 */
function userdata_address_get($address_id, $cache = true)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_address_get($address_id, $cache);
}

/**
 * Userdata Address List Get
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_address_list_get($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_address_list_get($service_id);
}

/**
 * Userdata Service Get All
 *
 * @param unknown $user_id User Id
 * @param unknown $get_new Get New
 *
 * @return unknown
 */
function userdata_service_get_all($user_id, $get_new = false)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_get_all($user_id, $get_new);
}

/**
 * Userdata Service Get By Account
 *
 * @param unknown $account_id Account Id
 *
 * @return unknown
 */
function userdata_service_get_by_account($account_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_get_by_account($account_id);
}

/**
 * Userdata Service Find Due Payment
 *
 * @param unknown $date        Date
 * @param array   $arrServices Services
 *
 * @return unknown
 */
function userdata_service_find_due_payment($date = '', $arrServices = array())
{
    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_find_due_payment($date, $arrServices);
}

/**
 * Userdata Service Find Due Payment Today
 *
 * @param unknown $date Date
 *
 * @return unknown
 */
function userdata_service_find_due_payment_today($date = '')
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_find_due_payment_today($date);
}

/**
 * Userdata Component Get
 *
 * @param unknown $component_id Component Id
 *
 * @return unknown
 */
function userdata_component_get($component_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_component_get($component_id);
}

/**
 * Userdata Account Get
 *
 * @param unknown $account_id Account Id
 *
 * @return unknown
 */
function userdata_account_get($account_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_account_get($account_id);
}

/**
 * Userdata Friaco Get By User
 *
 * @param unknown $user_id User Id
 *
 * @return unknown
 */
function userdata_friaco_get_by_user($user_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_friaco_get_by_user($user_id);
}

/**
 * Userdata Outstanding Transaction Get All
 *
 * @return unknown
 */
function userdata_outstanding_transaction_get_all()
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_outstanding_transaction_get_all();
}

/**
 * Userdata Outstanding Transaction Get By Account
 *
 * @param unknown $account_id Account Id
 *
 * @return unknown
 */
function userdata_outstanding_transaction_get_by_account($account_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_outstanding_transaction_get_by_account($account_id);
}

/**
 * Userdata Service Note Get
 *
 * @param unknown $service_note_id Service Note Id
 *
 * @return unknown
 */
function userdata_service_note_get($service_note_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_note_get($service_note_id);
}

/**
 * Userdata Service Note Find
 *
 * @param unknown $service_id Service Id
 * @param unknown $start      Start
 * @param unknown $limit      Limit
 *
 * @return unknown
 */
function userdata_service_note_find($service_id, $start, $limit)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_note_find($service_id, $start, $limit);
}

/**
 * Userdata User Profile Get
 *
 * @param unknown $user_id User Id
 *
 * @return unknown
 */
function userdata_user_profile_get($user_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_user_profile_get($user_id);
}

/**
 * Userdata Find Expiring Services
 *
 * @param unknown $days Days
 *
 * @return unknown
 */
function userdata_find_expiring_services($days)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_find_expiring_services($days);
}

/**
 * Userdata Days Cc Invalid
 *
 * @param unknown $days           Days
 * @param unknown $include_switch Include Switch
 *
 * @return unknown
 */
function userdata_days_cc_invalid($days, $include_switch = true)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_days_cc_invalid($days, $include_switch);
}

/**
 * Userdata Service Bt Accountnumber Get
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_service_bt_accountnumber_get($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_bt_accountnumber_get($service_id);
}

/**
 * Userdata Surftime Application Details Get
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_surftime_application_details_get($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_surftime_application_details_get($service_id);
}

/**
 * Userdata Retrieve Name
 *
 * @param unknown $customer_id Customer Id
 *
 * @return unknown
 */
function userdata_retrieve_name($customer_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_retrieve_name($customer_id);
}

/**
 * Userdata Get Customer Id From Service Id
 *
 * @param int $intServiceId Service Id
 *
 * @return unknown
 */
function UserdataGetCustomerIdFromServiceId($intServiceId)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataGetCustomerIdFromServiceId($intServiceId);
}

/**
 * Userdata Get User Email From Service Id
 *
 * @param int $intServiceId Service Id
 *
 * @return unknown
 */
function UserdataGetUserEmailFromServiceId($intServiceId)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataGetUserEmailFromServiceId($intServiceId);
}

/**
 * Userdata User Has Credit Details
 *
 * @param unknown $user_id                          User Id
 * @param unknown $expires_before_next_invoice_date Expires Before Next Invoice Date
 * @param bool    $bolExcludeCheques                Exclude Cheques
 *
 * @return unknown
 */
function userdata_user_has_credit_details(
    $user_id,
    $expires_before_next_invoice_date = false,
    $bolExcludeCheques = false
) {

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_user_has_credit_details(
        $user_id,
        $expires_before_next_invoice_date,
        $bolExcludeCheques
    );
}

/**
 * Userdata Get Credit Details Expiry Date
 *
 * @param unknown $user_id User Id
 *
 * @return unknown
 */
function userdata_get_credit_details_expiry_date($user_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_get_credit_details_expiry_date($user_id);
}

/**
 * Userdata User Has Cheque Payment Method
 *
 * @param unknown $user_id User Id
 *
 * @return unknown
 */
function userdata_user_has_cheque_payment_method($user_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_user_has_cheque_payment_method($user_id);
}

/**
 * Userdata Get Credit Check Id
 *
 * @param unknown $customer_id Customer Id
 * @param unknown $address_id  Address Id
 *
 * @return unknown
 */
function userdata_get_credit_check_id($customer_id, $address_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_get_credit_check_id($customer_id, $address_id);
}

/**
 * Userdata Get Cancel Scheduled Services
 *
 * @param bool $bolDate Date
 *
 * @return unknown
 */
function userdata_get_cancel_scheduled_services($bolDate = false)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_get_cancel_scheduled_services($bolDate);
}

/**
 * Userdata Service Associates Get
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_service_associates_get($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_associates_get($service_id);
}

/**
 * Userdata Service Is Staff
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_service_is_staff($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_is_staff($service_id);
}

/**
 * Userdata Service Resold Year End
 *
 * @param unknown $date_clause Date Clause
 *
 * @return unknown
 */
function userdata_service_resold_year_end($date_clause)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_resold_year_end($date_clause);
}

/**
 * Userdata Get Resold Services
 *
 * @param unknown $the_reseller_service_id The Reseller Service Id
 * @param unknown $status                  Status
 *
 * @return unknown
 */
function userdata_get_resold_services($the_reseller_service_id, $status = "")
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_get_resold_services($the_reseller_service_id, $status);
}

/**
 * Userdata Account Get By Resold Service
 *
 * @param unknown $resold_service_id Resold Service Id
 *
 * @return unknown
 */
function userdata_account_get_by_resold_service($resold_service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_account_get_by_resold_service($resold_service_id);
}

/**
 * Userdata Get Associated Services
 *
 * @param unknown $service_id Service Id
 * @param unknown $status     Status
 *
 * @return unknown
 */
function userdata_get_associated_services($service_id, $status = '')
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_get_associated_services($service_id, $status);
}

/**
 * Userdata Get Service Id From Credit Details Id
 *
 * @param array $array_credit_details_id Credit Details Id
 *
 * @return unknown
 */
function userdata_get_service_id_from_credit_details_id($array_credit_details_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_get_service_id_from_credit_details_id($array_credit_details_id);
}

/**
 * Userdata Email Delivery Method Get
 *
 * @param unknown $component_id Component Id
 *
 * @return unknown
 */
function userdata_email_delivery_method_get($component_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_email_delivery_method_get($component_id);
}

/**
 * Userdata Keyed Service Note Get
 *
 * @param unknown $service_id Service Id
 * @param unknown $key        Key
 *
 * @return unknown
 */
function userdata_keyed_service_note_get($service_id, $key)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_keyed_service_note_get($service_id, $key);
}

/**
 * Userdata Keyed Service Note Get All
 *
 * @param unknown $key           Key
 * @param bool    $bolReturnDate Return Date
 *
 * @return unknown
 */
function userdata_keyed_service_note_get_all($key, $bolReturnDate = false)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_keyed_service_note_get_all($key, $bolReturnDate);
}

/**
 * Userdata Service Get Account Status
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_service_get_account_status($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_get_account_status($service_id);
}

/**
 * Userdata Weekly Bandwidth Weeks Get
 *
 * @param unknown $week_id Week Id
 *
 * @return unknown
 */
function userdata_weekly_bandwidth_weeks_get($week_id = '')
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_weekly_bandwidth_weeks_get($week_id);
}

/**
 * Userdata Weekly Bandwidth Stats Get
 *
 * @param unknown $week_id            Week Id
 * @param array   $array_account_type Account Type
 * @param unknown $on_peak_hours      On Peak Hours
 * @param unknown $off_peak_hours     Off Peak Hours
 * @param unknown $weekend_hours      Weekend Hours
 * @param unknown $report_type        Report Type
 * @param unknown $dialup_group_id    Dialup Group Id
 * @param unknown $service_id         Service Id
 *
 * @return unknown
 */
function userdata_weekly_bandwidth_stats_get(
    $week_id,
    $array_account_type = array(),
    $on_peak_hours = '',
    $off_peak_hours = '',
    $weekend_hours = '',
    $report_type = '',
    $dialup_group_id = '',
    $service_id = ''
) {

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_weekly_bandwidth_stats_get(
        $week_id,
        $array_account_type,
        $on_peak_hours,
        $off_peak_hours,
        $weekend_hours,
        $report_type,
        $dialup_group_id,
        $service_id
    );
}

/**
 * Userdata Weekly Suggested Product Totals
 *
 * @param unknown $week_id Week Id
 *
 * @return unknown
 */
function userdata_weekly_suggested_product_totals($week_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_weekly_suggested_product_totals($week_id);
}

/**
 * Userdata Weekly Bandwidth Suggested Friaco Product Get
 *
 * @return unknown
 */
function userdata_weekly_bandwidth_suggested_friaco_product_get()
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_weekly_bandwidth_suggested_friaco_product_get();
}

/**
 * Userdata Bandwidth Report Revenue Data Get
 *
 * @param unknown $isp           Isp
 * @param unknown $start         Start
 * @param unknown $length        Length
 * @param array   $array_filters Filters
 * @param unknown $order_by      Order By
 *
 * @return unknown
 */
function userdata_bandwidth_report_revenue_data_get($isp = 'all', $start, $length, $array_filters = array(), $order_by)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_bandwidth_report_revenue_data_get($isp, $start, $length, $array_filters, $order_by);
}

/**
 * Userdata Bandwidth Report Revenue Count Get
 *
 * @param unknown $isp           Isp
 * @param array   $array_filters Filters
 *
 * @return unknown
 */
function userdata_bandwidth_report_revenue_count_get($isp = 'all', $array_filters = array())
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_bandwidth_report_revenue_count_get($isp, $array_filters);
}

/**
 * Userdata Bandwidth Report Construct Whereclause
 *
 * @param array   $array_filters Filters
 * @param unknown $modified      Modified
 *
 * @return unknown
 */
function userdata_bandwidth_report_construct_whereclause($array_filters, $modified = false)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_bandwidth_report_construct_whereclause($array_filters, $modified);
}

/**
 * Userdata Bandwidth Report Revenue Sum Get
 *
 * @param unknown $isp Isp
 *
 * @return unknown
 */
function userdata_bandwidth_report_revenue_sum_get($isp = 'all')
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_bandwidth_report_revenue_sum_get($isp);
}

/**
 * Userdata Bandwidth Report Filter Values Get
 *
 * @return unknown
 */
function userdata_bandwidth_report_filter_values_get()
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_bandwidth_report_filter_values_get();
}

/**
 * Userdata Service Schedule Add
 *
 * @param unknown $service_id              Service Id
 * @param unknown $new_definition_id       New Definition Id
 * @param unknown $change_date             Change Date
 * @param unknown $owner_id                Owner Id
 * @param int     $intToCBCFlexID          To CBCFlex ID
 * @param bool    $bolCbcFix               Cbc Fix
 * @param string  $strContractLengthHandle Contract Length Handle
 * @param int     $intTariffID             Tariff ID
 * @param int     $promotionCodeId         Promotion Code Id
 *
 * @return unknown
 */
function userdata_service_schedule_add(
    $service_id,
    $new_definition_id,
    $change_date,
    $owner_id,
    $intToCBCFlexID = 0,
    $bolCbcFix = '',
    $strContractLengthHandle = '',
    $intTariffID = 'null',
    $promotionCodeId = null
) {

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_schedule_add(
        $service_id,
        $new_definition_id,
        $change_date,
        $owner_id,
        $intToCBCFlexID,
        $bolCbcFix,
        $strContractLengthHandle,
        $intTariffID,
        $promotionCodeId
    );
}

/**
 * Userdata Service Schedule Complete
 *
 * @param unknown $service_id Service Id
 * @param unknown $type       Type
 *
 * @return unknown
 */
function userdata_service_schedule_complete($service_id, $type)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_schedule_complete($service_id, $type);
}

/**
 * Userdata Service Schedules Get
 *
 * @return unknown
 */
function userdata_service_schedules_get()
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_schedules_get();
}

/**
 * Userdata Service Schedule Cancel
 *
 * @param unknown $service_id Service Id
 * @param int     $intOldType Old Type
 * @param int     $intNewType New Type
 *
 * @return unknown
 */
function userdata_service_schedule_cancel($service_id, $intOldType = 0, $intNewType = 0)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_schedule_cancel($service_id, $intOldType, $intNewType);
}

/**
 * Userdata Get All Credit Details
 *
 * @param unknown $card_number Card Number
 *
 * @return unknown
 */
function userdata_get_all_credit_details($card_number)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_get_all_credit_details($card_number);
}

/**
 * Userdata Get Credit Details By Credit Id
 *
 * @param unknown $credit_id Credit Id
 *
 * @return unknown
 */
function userdata_get_credit_details_by_credit_id($credit_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_get_credit_details_by_credit_id($credit_id);
}

/**
 * Get Accounts Info Using Credit Card Details Id
 *
 * @param unknown $credit_details_id Credit Details Id
 *
 * @return unknown
 */
function get_accounts_info_using_credit_card_details_id($credit_details_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->get_accounts_info_using_credit_card_details_id($credit_details_id);
}

/**
 * Userdata Retrieve All User Info
 *
 * @param unknown $customer_id Customer Id
 *
 * @return unknown
 */
function userdata_retrieve_all_user_info($customer_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_retrieve_all_user_info($customer_id);
}

/**
 * Userdata Account Get By Service
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_account_get_by_service($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_account_get_by_service($service_id);
}

/**
 * Userdata Service Def Component Summary Get
 *
 * @param unknown $service_definition_id Service Definition Id
 *
 * @return unknown
 */
function userdata_service_def_component_summary_get($service_definition_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_def_component_summary_get($service_definition_id);
}

/**
 * Userdata Get Adsl Upgrade In Progress
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_get_adsl_upgrade_in_progress($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_get_adsl_upgrade_in_progress($service_id);
}

/**
 * Userdata Get Mrtg Component
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_get_mrtg_component($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_get_mrtg_component($service_id);
}

/**
 * Userdata Get Free Trial Component
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_get_free_trial_component($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_get_free_trial_component($service_id);
}

/**
 * Userdata Get All Leased Line Component
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_get_all_leased_line_component($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_get_all_leased_line_component($service_id);
}

/**
 * Userdata Service Type Change Get Last
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_service_type_change_get_last($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_type_change_get_last($service_id);
}

/**
 * Userdata Get Total Multiple Cli
 *
 * @param unknown $isp      Isp
 * @param unknown $services Services
 *
 * @return unknown
 */
function userdata_get_total_multiple_cli($isp, $services)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_get_total_multiple_cli($isp, $services);
}

/**
 * Userdata Get Stat Multiple Cli Product
 *
 * @param unknown $type     Type
 * @param unknown $services Services
 *
 * @return unknown
 */
function userdata_get_stat_multiple_cli_product($type, $services)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_get_stat_multiple_cli_product($type, $services);
}

/**
 * Userdata Get Stat Multiple Cli
 *
 * @param unknown $isp      Isp
 * @param unknown $services Services
 *
 * @return unknown
 */
function userdata_get_stat_multiple_cli($isp, $services)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_get_stat_multiple_cli($isp, $services);
}

/**
 * Userdata Get Total Cli By Service
 *
 * @param unknown $isp                   Isp
 * @param unknown $service_definition_id Service Definition Id
 * @param unknown $services              Services
 *
 * @return unknown
 */
function userdata_get_total_cli_by_service($isp, $service_definition_id, $services)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_get_total_cli_by_service($isp, $service_definition_id, $services);
}

/**
 * Userdata Service Event Types Get All
 *
 * @return unknown
 */
function userdata_service_event_types_get_all()
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_event_types_get_all();
}

/**
 * Userdata Pound Domain Choice Needed Upgrade
 *
 * @param unknown $service_id       Service Id
 * @param unknown $new_service_def  New Service Def
 * @param unknown $free_domain_comp Free Domain Comp
 *
 * @return unknown
 */
function userdata_pound_domain_choice_needed_upgrade($service_id, $new_service_def, $free_domain_comp)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_pound_domain_choice_needed_upgrade($service_id, $new_service_def, $free_domain_comp);
}

/**
 * Userdata Pound Domain Choice Needed
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_pound_domain_choice_needed($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_pound_domain_choice_needed($service_id);
}

/**
 * Userdata All Pound Domain Choice Needed
 *
 * @return unknown
 */
function userdata_all_pound_domain_choice_needed()
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_all_pound_domain_choice_needed();
}

/**
 * Userdata Get Webstats Component Id
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_get_webstats_component_id($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_get_webstats_component_id($service_id);
}

/**
 * Userdata Service Has Hardware Component
 *
 * @param unknown $service_id          Service Id
 * @param bool    $bolReturnComponents Return Components
 *
 * @return unknown
 */
function userdata_service_has_hardware_component($service_id, $bolReturnComponents = false)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_has_hardware_component($service_id, $bolReturnComponents);
}

/**
 * Userdata Webspace Usage
 *
 * @param int $intServiceId Service Id
 *
 * @return unknown
 */
function UserdataWebspaceUsage($intServiceId)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataWebspaceUsage($intServiceId);
}

/**
 * Userdata User Has Outstanding Balance
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_user_has_outstanding_balance($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_user_has_outstanding_balance($service_id);
}

/**
 * Userdata User Has Outstanding Balance
 *
 * @param int $intServiceId Service Id
 *
 * @return unknown
 */
function UserdataUserHasOutstandingBalance($intServiceId)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataUserHasOutstandingBalance($intServiceId);
}

/**
 * Userdata Service Has Taken Special Offer Upgrade
 *
 * @param int    $intServiceID             Service ID
 * @param int    $intSpecialOfferUpgradeID Special Offer Upgrade ID
 * @param string $strStartDate             Start Date
 * @param string $strEndDate               End Date
 *
 * @return unknown
 */
function UserdataServiceHasTakenSpecialOfferUpgrade(
    $intServiceID,
    $intSpecialOfferUpgradeID,
    $strStartDate = '',
    $strEndDate = ''
) {

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataServiceHasTakenSpecialOfferUpgrade(
        $intServiceID,
        $intSpecialOfferUpgradeID,
        $strStartDate,
        $strEndDate
    );
}

/**
 * Userdata Get IPBlock Request
 *
 * @param int $intServiceID Service ID
 *
 * @return unknown
 */
function userdataGetIPBlockRequest($intServiceID)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataGetIPBlockRequest($intServiceID);
}

/**
 * Userdata Service Product Get
 *
 * @param int $intServiceID Service ID
 *
 * @return unknown
 */
function userdataServiceProductGet($intServiceID)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataServiceProductGet($intServiceID);
}

/**
 * Userdata Service Product Changes Since
 *
 * @param int $intServiceID Service ID
 *
 * @return unknown
 */
function userdataServiceIsProductChanging($intServiceID)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataServiceIsProductChanging($intServiceID);
}

/**
 * Userdata Service Product Changes Since
 *
 * @param int     $intServiceID Service ID
 * @param unknown $utxDate      Utx Date
 *
 * @return unknown
 */
function userdataServiceProductChangesSince($intServiceID, $utxDate)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataServiceProductChangesSince($intServiceID, $utxDate);
}

/**
 * Userdata Has Post Master Email
 *
 * @param int $intServiceID Service ID
 *
 * @return unknown
 */
function userdataHasPostMasterEmail($intServiceID)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataHasPostMasterEmail($intServiceID);
}

/**
 * Userdata Has Nine Nine Nine Bundle
 *
 * @param int $intServiceID Service ID
 *
 * @return unknown
 */
function userdataHasNineNineNineBundle($intServiceID)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataHasNineNineNineBundle($intServiceID);
}

/**
 * Userdata Switch Authorisation Price Expire
 *
 * @param unknown $auth_uid Auth Uid
 *
 * @return unknown
 */
function userdata_switch_authorisation_price_expire($auth_uid)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_switch_authorisation_price_expire($auth_uid);
}

/**
 * Userdata Switch Authorisation Date Expire
 *
 * @param unknown $auth_uid Auth Uid
 *
 * @return unknown
 */
function userdata_switch_authorisation_date_expire($auth_uid)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_switch_authorisation_date_expire($auth_uid);
}

/**
 * Userdata Switch Expire All User Subscriptions
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_switch_expire_all_user_subscriptions($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_switch_expire_all_user_subscriptions($service_id);
}

/**
 * Userdata Get Default Email
 *
 * @param array   $array_input Input
 * @param unknown $get_new     Get New
 *
 * @return unknown
 */
function userdata_get_default_email($array_input = array(), $get_new = false)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_get_default_email($array_input, $get_new);
}

/**
 * Userdata Switch Authorisation Date Change
 *
 * @param unknown $auth_uid          Auth Uid
 * @param unknown $next_invoice_date Next Invoice Date
 *
 * @return unknown
 */
function userdata_switch_authorisation_date_change($auth_uid, $next_invoice_date)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_switch_authorisation_date_change($auth_uid, $next_invoice_date);
}

/**
 * Userdata Customer Alter
 *
 * @param unknown $customer_id        Customer Id
 * @param unknown $primary_address_id Primary Address Id
 * @param unknown $primary_user_id    Primary User Id
 * @param unknown $company_name       Company Name
 * @param unknown $company_nature     Company Nature
 * @param unknown $company_size       Company Size
 *
 * @return unknown
 */
function userdata_customer_alter(
    $customer_id,
    $primary_address_id,
    $primary_user_id,
    $company_name = 'not set',
    $company_nature = -1,
    $company_size = -1
) {

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_customer_alter(
        $customer_id,
        $primary_address_id,
        $primary_user_id,
        $company_name,
        $company_nature,
        $company_size
    );
}

/**
 * Userdata Address Alter
 *
 * @param unknown $address_id Address Id
 * @param unknown $house      House
 * @param string  $street     Eet
 * @param unknown $town       Town
 * @param unknown $county     County
 * @param unknown $postcode   Postcode
 * @param unknown $country    Country
 *
 * @return unknown
 */
function userdata_address_alter($address_id, $house, $street, $town, $county, $postcode, $country)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_address_alter($address_id, $house, $street, $town, $county, $postcode, $country);
}

/**
 * Userdata User Alter
 *
 * @param unknown $user_id            User Id
 * @param unknown $address_id         Address Id
 * @param unknown $telephone          Telephone
 * @param unknown $fax                Fax
 * @param unknown $mobile             Mobile
 * @param unknown $email              Email
 * @param unknown $position           Position
 * @param unknown $salutation         Salutation
 * @param unknown $forenames          Forenames
 * @param unknown $surname            Surname
 * @param unknown $hint_question      Hint Question
 * @param unknown $hint_answer        Hint Answer
 * @param string  $strEveningPhone    Evening Phone
 * @param string  $strTransactionName Transaction Name
 *
 * @return unknown
 */
function userdata_user_alter(
    $user_id,
    $address_id,
    $telephone,
    $fax,
    $mobile,
    $email,
    $position,
    $salutation,
    $forenames,
    $surname,
    $hint_question,
    $hint_answer,
    $strEveningPhone,
    $strTransactionName = 'SPLIT_USERDATA_USER_ALTER'
) {

    return Lib_Userdata::singleton()->userdata_user_alter(
        $user_id,
        $address_id,
        $telephone,
        $fax,
        $mobile,
        $email,
        $position,
        $salutation,
        $forenames,
        $surname,
        $hint_question,
        $hint_answer,
        $strEveningPhone,
        $strTransactionName
    );
}

/**
 * Alter customer Id
 *
 * @param integer $intUserId          User Id
 * @param integer $intCustomerId      Customer Id
 * @param string  $strTransactionName Transaction name
 *
 * @return bool
 */
function userdata_user_alter_customer_id(
    $intUserId,
    $intCustomerId,
    $strTransactionName = 'SPLIT_USERDATA_ALTER_CUSTOMER_ID'
) {

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_user_alter_customer_id($intUserId, $intCustomerId, $strTransactionName);
}

/**
 * Userdata User Profile Alter
 *
 * @param unknown $user_id           User Id
 * @param unknown $gender            Gender
 * @param unknown $maritalstatus     Maritalstatus
 * @param unknown $birthday          Birthday
 * @param unknown $occupation        Occupation
 * @param unknown $income            Income
 * @param unknown $currentconnection Currentconnection
 * @param unknown $pctype            Pctype
 * @param unknown $educationlevel    Educationlevel
 * @param unknown $numberofusers     Numberofusers
 * @param unknown $usagetype         Usagetype
 * @param unknown $referrer          Referrer
 * @param unknown $internaloptout    Internaloptout
 * @param unknown $externaloptout    Externaloptout
 * @param unknown $interests         Intrests
 *
 * @return unknown
 */
function userdata_user_profile_alter(
    $user_id,
    $gender,
    $maritalstatus,
    $birthday,
    $occupation,
    $income,
    $currentconnection,
    $pctype,
    $educationlevel,
    $numberofusers,
    $usagetype,
    $referrer,
    $internaloptout,
    $externaloptout,
    $interests
) {

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_user_profile_alter(
        $user_id,
        $gender,
        $maritalstatus,
        $birthday,
        $occupation,
        $income,
        $currentconnection,
        $pctype,
        $educationlevel,
        $numberofusers,
        $usagetype,
        $referrer,
        $internaloptout,
        $externaloptout,
        $interests
    );
}

/**
 * Userdata Service Set Password
 *
 * @param unknown $service_id Service Id
 * @param unknown $password   Password
 *
 * @return unknown
 */
function userdata_service_set_password($service_id, $password)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_set_password($service_id, $password);
}

/**
 * Userdata Service Set Password Expiry
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_service_set_password_expiry($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_set_password_expiry($service_id);
}

/**
 * Userdata Service Set Cli
 *
 * @param unknown $service_id          Service Id
 * @param unknown $cli_number          Cli Number
 * @param bool    $bolUpdateServiceCli Update Service Cli
 *
 * @return unknown
 */
function userdata_service_set_cli($service_id, $cli_number, $bolUpdateServiceCli = false)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_set_cli($service_id, $cli_number, $bolUpdateServiceCli);
}

/**
 * Userdata Service Set Type
 *
 * @param unknown $service_id Service Id
 * @param unknown $type       Type
 *
 * @return unknown
 */
function userdata_service_set_type($service_id, $type)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_set_type($service_id, $type);
}

/**
 * Userdata Service Set Next Invoice
 *
 * @param unknown $service_id       Service Id
 * @param unknown $next_invoice     Next Invoice
 * @param unknown $changeInvoiceDay Change Invoice Day
 *
 * @return unknown
 */
function userdata_service_set_next_invoice($service_id, $next_invoice, $changeInvoiceDay = true)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_set_next_invoice($service_id, $next_invoice, $changeInvoiceDay);
}

/**
 * Userdata Service Set Next Invoice Maaf
 *
 * @param int     $intServiceId       Service Id
 * @param unknown $dteNextInvoiceDate Dte Next Invoice Date
 * @param bool    $bolToBill          To Bill
 *
 * @return unknown
 */
function userdata_service_set_next_invoice_maaf($intServiceId, $dteNextInvoiceDate, $bolToBill = true)
{
    //We put the arguments to an array because this function is used by a script, where it can process more accounts
    $arrArgs[0]['intServiceId'] = $intServiceId;
    $arrArgs[0]['dteNextInvoice'] = $dteNextInvoiceDate;

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_set_next_invoice_maaf($arrArgs, $bolToBill);
}

/**
 * Userdata Service Set Invoice Period
 *
 * @param unknown $service_id     Service Id
 * @param unknown $invoice_period Invoice Period
 *
 * @return unknown
 */
function userdata_service_set_invoice_period($service_id, $invoice_period)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_set_invoice_period($service_id, $invoice_period);
}

/**
 * Userdata Service Set Enddate
 *
 * @param unknown $service_id Service Id
 * @param unknown $end_date   End Date
 *
 * @return unknown
 */
function userdata_service_set_enddate($service_id, $end_date)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_set_enddate($service_id, $end_date);
}

/**
 * Userdata Service Set Enddate Null
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_service_set_enddate_null($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_set_enddate_null($service_id);
}

/**
 * Userdata Service Enddate Remove
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_service_enddate_remove($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_enddate_remove($service_id);
}

/**
 * Userdata Service Auto Update Next Invoice
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_service_auto_update_next_invoice($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_auto_update_next_invoice($service_id);
}

/**
 * Userdata Component Set Configuration
 *
 * @param unknown $component_id Component Id
 * @param unknown $config_id    Config Id
 * @param unknown $description  Description
 *
 * @return unknown
 */
function userdata_component_set_configuration($component_id, $config_id, $description)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_component_set_configuration($component_id, $config_id, $description);
}

/**
 * Userdata Component Set Status
 *
 * @param unknown $component_id Component Id
 * @param unknown $status       Status
 *
 * @return unknown
 */
function userdata_component_set_status($component_id, $status)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_component_set_status($component_id, $status);
}

/**
 * Userdata Component Move
 *
 * @param unknown $component_id    Component Id
 * @param unknown $from_service_id From Service Id
 * @param unknown $to_service_id   To Service Id
 *
 * @return unknown
 */
function userdata_component_move($component_id, $from_service_id, $to_service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_component_move($component_id, $from_service_id, $to_service_id);
}

/**
 * Userdata Account Address Alter
 *
 * @param unknown $account_id Account Id
 * @param unknown $address_id Address Id
 *
 * @return unknown
 */
function userdata_account_address_alter($account_id, $address_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_account_address_alter($account_id, $address_id);
}

/**
 * Userdata Account Balance Add
 *
 * @param unknown $account_id Account Id
 * @param unknown $amount     Amount
 *
 * @return unknown
 */
function userdata_account_balance_add($account_id, $amount)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_account_balance_add($account_id, $amount);
}

/**
 * Userdata Account Balance Sub
 *
 * @param unknown $account_id Account Id
 * @param unknown $amount     Amount
 *
 * @return unknown
 */
function userdata_account_balance_sub($account_id, $amount)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_account_balance_sub($account_id, $amount);
}

/**
 * Userdata Account Set Credit Terms
 *
 * @param unknown $account_id   Account Id
 * @param unknown $credit_terms Credit Terms
 *
 * @return unknown
 */
function userdata_account_set_credit_terms($account_id, $credit_terms)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_account_set_credit_terms($account_id, $credit_terms);
}

/**
 * Userdata Account Set Credit Limit
 *
 * @param unknown $account_id   Account Id
 * @param unknown $credit_limit Credit Limit
 *
 * @return unknown
 */
function userdata_account_set_credit_limit($account_id, $credit_limit)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_account_set_credit_limit($account_id, $credit_limit);
}

/**
 * Userdata Service Bt Accountnumber Set
 *
 * @param unknown $service_id Service Id
 * @param unknown $new_number New Number
 *
 * @return unknown
 */
function userdata_service_bt_accountnumber_set($service_id, $new_number)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_bt_accountnumber_set($service_id, $new_number);
}

/**
 * Userdata Service Next Invoice Tick
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_service_next_invoice_tick($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_next_invoice_tick($service_id);
}

/**
 * Userdata Keyed Service Note Set
 *
 * @param unknown $service_id Service Id
 * @param unknown $key        Key
 * @param unknown $value      Value
 *
 * @return unknown
 */
function userdata_keyed_service_note_set($service_id, $key, $value)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_keyed_service_note_set($service_id, $key, $value);
}

/**
 * Userdata Modify Reseller Status
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_modify_reseller_status($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_modify_reseller_status($service_id);
}

/**
 * Userdata Adsl Upgrade Set Status
 *
 * @param unknown $adsl_upgrade_id Adsl Upgrade Id
 * @param unknown $status          Status
 *
 * @return unknown
 */
function userdata_adsl_upgrade_set_status($adsl_upgrade_id, $status)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_adsl_upgrade_set_status($adsl_upgrade_id, $status);
}

/**
 * Userdata Service Type Change Get Components To Add
 *
 * @param unknown $new_service_def_id       New Service Def Id
 * @param array   $array_current_components Current Components
 *
 * @return unknown
 */
function userdata_service_type_change_get_components_to_add($new_service_def_id, $array_current_components)
{
    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_type_change_get_components_to_add(
        $new_service_def_id,
        $array_current_components
    );
}

/**
 * Type change get components to remove
 *
 * @param unknown $new_service_def_id       New Service Def Id
 * @param array   $array_current_components Current components
 * @param bool    $bolExcludeBoltOn         Exclude bolt on bool
 *
 * @return array
 */
function userdata_service_type_change_get_components_to_remove(
    $new_service_def_id,
    $array_current_components,
    $bolExcludeBoltOn = false
) {

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_type_change_get_components_to_remove(
        $new_service_def_id,
        $array_current_components,
        $bolExcludeBoltOn
    );
}

/**
 * Userdata Service Account Type Change
 *
 * @param int    $service_id            Service Id
 * @param int    $new_service_def_id    New Service Def Id
 * @param array  $array_swap_components Swap Components
 * @param string $service_event_id      Service Event Id
 * @param bool   $run_conditions        Run Conditions
 * @param array  $options               (optional)    BT processing flag for destroying components.
 *
 * @return string
 *
 * PID70872, updated to pass in optional parameter to control updates to BT.
 */
function userdata_service_account_type_change(
    $service_id,
    $new_service_def_id,
    $array_swap_components = array(),
    $service_event_id = '',
    $run_conditions = true,
    $options = array()
) {
    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_account_type_change(
        $service_id,
        $new_service_def_id,
        $array_swap_components,
        $service_event_id,
        $run_conditions,
        $options
    );
}

/**
 * Userdata Component Delete
 *
 * @param int $component_id Component Id
 *
 * @return unknown
 */
function userdata_component_delete($component_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_component_delete($component_id);
}

/**
 * Userdata Outstanding Transaction Delete
 *
 * @param unknown $outstanding_transaction_id Outstanding Transaction Id
 *
 * @return unknown
 */
function userdata_outstanding_transaction_delete($outstanding_transaction_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_outstanding_transaction_delete($outstanding_transaction_id);
}

/**
 * Userdata Service Note Delete
 *
 * @param unknown $service_note_id Service Note Id
 *
 * @return unknown
 */
function userdata_service_note_delete($service_note_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_note_delete($service_note_id);
}

/**
 * Userdata Keyed Service Note Delete
 *
 * @param unknown $service_id Service Id
 * @param unknown $key        Key
 *
 * @return unknown
 */
function userdata_keyed_service_note_delete($service_id, $key)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_keyed_service_note_delete($service_id, $key);
}

/**
 * Userdata Service Disassociate
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_service_disassociate($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_disassociate($service_id);
}

/**
 * Userdata Resold Service Note Delete
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function userdata_resold_service_note_delete($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_resold_service_note_delete($service_id);
}

/**
 * Userdata Resold Service Cost Get
 *
 * @param unknown $resold_service_id   Resold Service Id
 * @param unknown $reseller_service_id Reseller Service Id
 *
 * @return unknown
 */
function userdata_resold_service_cost_get($resold_service_id, $reseller_service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_resold_service_cost_get($resold_service_id, $reseller_service_id);
}

/**
 * Userdata Switch Authorisation Flag Set
 *
 * @param unknown $service_id Service Id
 * @param unknown $flag_value Flag Value
 *
 * @return unknown
 */
function userdata_switch_authorisation_flag_set($service_id, $flag_value)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_switch_authorisation_flag_set($service_id, $flag_value);
}

/**
 * Userdata Log Is Scheduled Cancellation
 *
 * @param unknown $service_id            Service Id
 * @param unknown $service_definition_id Service Definition Id
 * @param unknown $now                   Now
 *
 * @return unknown
 */
function userdata_log_is_scheduled_cancellation($service_id, $service_definition_id = '', $now = '')
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_log_is_scheduled_cancellation($service_id, $service_definition_id, $now);
}

/**
 * Userdata Pound Domain Set Choice
 *
 * @param unknown $data Data
 *
 * @return unknown
 */
function userdata_pound_domain_set_choice($data)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_pound_domain_set_choice($data);
}

/**
 * Userdata Get Subscription Billing Component Names
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function UserdataGetSubscriptionBillingComponentNames($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataGetSubscriptionBillingComponentNames($service_id);
}

/**
 * Userdata User Has Always On Account
 *
 * @param int $intServiceID Service ID
 *
 * @return unknown
 */
function UserdataUserHasAlwaysOnAccount($intServiceID)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataUserHasAlwaysOnAccount($intServiceID);
}

/**
 * Userdata User Has Static IP
 *
 * @param int $intServiceID Service ID
 *
 * @return unknown
 */
function UserdataUserHasStaticIP($intServiceID)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataUserHasStaticIP($intServiceID);
}

/**
 * Userdata User Migrating ISP
 *
 * @param int $intServiceID Service ID
 *
 * @return unknown
 */
function UserdataUserMigratingISP($intServiceID)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataUserMigratingISP($intServiceID);
}

/**
 * Userdata Get Banned Passwords
 *
 * @return unknown
 */
function userdataGetBannedPasswords()
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataGetBannedPasswords();
}

/**
 * Userdata Get Defer Period Progress
 *
 * @param int $intServiceID Service ID
 *
 * @return unknown
 */
function UserdataGetDeferPeriodProgress($intServiceID)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataGetDeferPeriodProgress($intServiceID);
}

/**
 * Userdata Get DDUpgrade Status ID
 *
 * @param string $strStatusTag Status Tag
 *
 * @return unknown
 */
function UserdataGetDDUpgradeStatusID($strStatusTag)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataGetDDUpgradeStatusID($strStatusTag);
}

/**
 * Userdata Log DDActivation
 *
 * @param int $intDDUpgradeID DDUpgrade ID
 *
 * @return unknown
 */
function UserdataLogDDActivation($intDDUpgradeID)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataLogDDActivation($intDDUpgradeID);
}

/**
 * Userdata Log DDFailure
 *
 * @param int $intDDUpgradeID DDUpgrade ID
 *
 * @return unknown
 */
function UserdataLogDDFailure($intDDUpgradeID)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataLogDDFailure($intDDUpgradeID);
}

/**
 * Userdata Update Status
 *
 * @param string $strstatusTag   Status Tag
 * @param int    $intDDUpgradeID DDUpgrade ID
 *
 * @return unknown
 */
function UserdataUpdateStatus($strstatusTag, $intDDUpgradeID)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataUpdateStatus($strstatusTag, $intDDUpgradeID);
}

/**
 * Userdata Log DDUpgrade
 *
 * @param array $arrSignupData Signup Data
 *
 * @return unknown
 */
function UserdataLogDDUpgrade($arrSignupData)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataLogDDUpgrade($arrSignupData);
}

/**
 * Userdata Get DDStatus
 *
 * @param int $intServiceID Service ID
 *
 * @return unknown
 */
function UserdataGetDDStatus($intServiceID)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataGetDDStatus($intServiceID);
}

/**
 * Userdata Get All DDStatus
 *
 * @param int $intServiceID Service ID
 *
 * @return unknown
 */
function UserdataGetAllDDStatus($intServiceID)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataGetAllDDStatus($intServiceID);
}

/**
 * Userdata Get Upgrade DDDetails
 *
 * @param int $intServiceID Service ID
 *
 * @return unknown
 */
function UserdataGetUpgradeDDDetails($intServiceID)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataGetUpgradeDDDetails($intServiceID);
}

/**
 * Userdata Is DDUpgrade
 *
 * @param int $intServiceID Service ID
 *
 * @return unknown
 */
function UserdataIsDDUpgrade($intServiceID)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataIsDDUpgrade($intServiceID);
}

/**
 * Userdata Get Upgrade Type
 *
 * @param int $intServiceID Service ID
 *
 * @return unknown
 */
function UserdataGetUpgradeType($intServiceID)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataGetUpgradeType($intServiceID);
}

/**
 * Userdata Get ADSLUpgrade Info
 *
 * @param int $intServiceID Service ID
 *
 * @return unknown
 */
function UserdataGetADSLUpgradeInfo($intServiceID)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataGetADSLUpgradeInfo($intServiceID);
}

/**
 * Userdata Get Upgrade Info
 *
 * @param int $intServiceID Service ID
 *
 * @return unknown
 */
function UserdataGetUpgradeInfo($intServiceID)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataGetUpgradeInfo($intServiceID);
}

/**
 * Userdata Get DDUpgrade ID
 *
 * @param int    $intServiceID Service ID
 * @param string $strStatusTag Status Tag
 *
 * @return unknown
 */
function UserdataGetDDUpgradeID($intServiceID, $strStatusTag)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataGetDDUpgradeID($intServiceID, $strStatusTag);
}

/**
 * Userdata Set Mail Opt Out
 *
 * @param int  $intServiceID Service ID
 * @param bool $bolStatus    Status
 *
 * @return bool
 */
function UserdataSetMailOptOut($intServiceID, $bolStatus)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataSetMailOptOut($intServiceID, $bolStatus);
}

/**
 * Set comms settings
 *
 * @param integer $intServiceID Service Id
 * @param array   $arrStatus    Status
 * @param array   $userId       userId
 * @param string  $strOldEmail  old emailId
 *
 * @return bool
 */
function userDataSetCommsSettings($intServiceID, $arrStatus, $userId = null, $strOldEmail)
{
    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataSetCommsSettings($intServiceID, $arrStatus, $userId, $strOldEmail);
}

/**
 * Setup comms settings
 *
 * @param integer $intServiceID Service Id
 * @param array   $arrStatus    Status
 *
 * @return bool
 */
function userDataSetupCommsSettings($intServiceID, $arrStatus)
{
    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataSetupCommsSettings($intServiceID, $arrStatus);
}

/**
 * Userdata Get Component Status
 *
 * @param int $intComponentID Component ID
 *
 * @return unknown
 */
function UserdataGetComponentStatus($intComponentID)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataGetComponentStatus($intComponentID);
}

/**
 * Userdata Log Change Inv Period Def Account
 *
 * @param int $intServiceID           Service ID
 * @param int $intServiceDefinitionID Service Definition ID
 *
 * @return unknown
 */
function userdataLogChangeInvPeriodDefAccount($intServiceID, $intServiceDefinitionID)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataLogChangeInvPeriodDefAccount($intServiceID, $intServiceDefinitionID);
}

/**
 * Userdata Check Change Inv Period Def Account Log
 *
 * @param int $intServiceID           Service ID
 * @param int $intServiceDefinitionID Service Definition ID
 *
 * @return unknown
 */
function userdataCheckChangeInvPeriodDefAccountLog($intServiceID, $intServiceDefinitionID)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataCheckChangeInvPeriodDefAccountLog($intServiceID, $intServiceDefinitionID);
}

/**
 * Userdata Get Pending ADSLUpgrade
 *
 * @param int $intServiceID Service ID
 *
 * @return unknown
 */
function UserdataGetPendingADSLUpgrade($intServiceID)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataGetPendingADSLUpgrade($intServiceID);
}

/**
 * Get Card Type By Service ID
 *
 * @param int $intServiceID Service ID
 *
 * @return unknown
 */
function GetCardTypeByServiceID($intServiceID)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->GetCardTypeByServiceID($intServiceID);
}

/**
 * Userdata Get Visp Config For Service Id
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function UserdataGetVispConfigForServiceId($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataGetVispConfigForServiceId($service_id);
}

/**
 * Get Credit Card Details By Service Id
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function get_credit_card_details_by_service_id($service_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->get_credit_card_details_by_service_id($service_id);
}

/**
 * Is Metronet Service
 *
 * @param array $arrService Service
 *
 * @return unknown
 */
function IsMetronetService($arrService)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->IsMetronetService($arrService);
}

/**
 * Handle 10 GBSpecial Offer
 *
 * @param int   $intServiceID      Service ID
 * @param array $arrCurrentProduct Current Product
 * @param array $arrNewProduct     New Product
 *
 * @return unknown
 */
function handle10GBSpecialOffer($intServiceID, $arrCurrentProduct, $arrNewProduct)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->handle10GBSpecialOffer($intServiceID, $arrCurrentProduct, $arrNewProduct);
}

/**
 * Metronet Get Child Service IDs
 *
 * @param int $intMasterServiceID Master Service ID
 *
 * @return unknown
 */
function Metronet_GetChildServiceIDs($intMasterServiceID)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->Metronet_GetChildServiceIDs($intMasterServiceID);
}

/**
 * Metronet Set Child Email Addresses To Master Address
 *
 * @param int    $intMasterServiceID Master Service ID
 * @param string $strNewEmail        New Email
 *
 * @return unknown
 */
function Metronet_SetChildEmailAddressesToMasterAddress($intMasterServiceID, $strNewEmail)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->Metronet_SetChildEmailAddressesToMasterAddress($intMasterServiceID, $strNewEmail);
}

/**
 * Metronet Set Mail Opt Out On All Children
 *
 * @param int  $intMasterServiceID Master Service ID
 * @param bool $bolStatus          Status
 *
 * @return unknown
 */
function Metronet_SetMailOptOutOnAllChildren($intMasterServiceID, $bolStatus)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->Metronet_SetMailOptOutOnAllChildren($intMasterServiceID, $bolStatus);
}

/**
 * Business Or Consumer
 *
 * @param unknown $service_definition_id Service Definition Id
 *
 * @return unknown
 */
function business_or_consumer($service_definition_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->business_or_consumer($service_definition_id);
}

/**
 * Userdata Get Guide File Name
 *
 * @param unknown $service_definition_id Service Definition Id
 *
 * @return unknown
 */
function userdataGetGuideFileName($service_definition_id)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataGetGuideFileName($service_definition_id);
}

/**
 * Userdata Change Invoice Day
 *
 * @param int $intServiceID  Service ID
 * @param int $intInvoiceDay Invoice Day
 *
 * @return unknown
 */
function userdataChangeInvoiceDay($intServiceID, $intInvoiceDay)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataChangeInvoiceDay($intServiceID, $intInvoiceDay);
}

/**
 * Get Member Center Broadband Support File
 *
 * @param int    $intServiceID            Service ID
 * @param string $strBroadbandSupportFile Broadband Support File
 *
 * @return unknown
 */
function GetMemberCenterBroadbandSupportFile($intServiceID, $strBroadbandSupportFile)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->GetMemberCenterBroadbandSupportFile($intServiceID, $strBroadbandSupportFile);
}

/**
 * Userdata Service Get Primary Component Id
 *
 * @param int $intServiceId Service Id
 *
 * @return unknown
 */
function userdataServiceGetPrimaryComponentId($intServiceId)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataServiceGetPrimaryComponentId($intServiceId);
}

/**
 * Userdata Get User Details By Customer Id
 *
 * @param int $intCustomerId Customer Id
 *
 * @return unknown
 */
function userdataGetUserDetailsByCustomerId($intCustomerId)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataGetUserDetailsByCustomerId($intCustomerId);
}

/**
 * Userdata Is Service Rin Enabled
 *
 * @param int $intServiceId Service Id
 *
 * @return unknown
 */
function userdataIsServiceRinEnabled($intServiceId)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataIsServiceRinEnabled($intServiceId);
}

/**
 * Userdata Has Referral Tokens For Year Month
 *
 * @param int    $intReferralServiceId Referral Service Id
 * @param string $strYearNMonth        Year NMonth
 *
 * @return unknown
 */
function userdataHasReferralTokensForYearMonth($intReferralServiceId, $strYearNMonth)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataHasReferralTokensForYearMonth($intReferralServiceId, $strYearNMonth);
}

/**
 * Userdata Get IPComponent
 *
 * @param int $intServiceId Service Id
 *
 * @return unknown
 */
function userdataGetIPComponent($intServiceId)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataGetIPComponent($intServiceId);
}

/**
 * Userdata Get Product Group From Service Id
 *
 * @param int $intServiceId Service Id
 *
 * @return unknown
 */
function userdataGetProductGroupFromServiceId($intServiceId)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataGetProductGroupFromServiceId($intServiceId);
}

/**
 * Userdata Has Bbyw Product
 *
 * @param int $intServiceId Service Id
 *
 * @return unknown
 */
function userdataHasBbywProduct($intServiceId)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataHasBbywProduct($intServiceId);
}

/**
 * Userdata Get Service Event Id By Name
 *
 * @param string $strEventName Event Name
 *
 * @return unknown
 */
function userdataGetServiceEventIdByName($strEventName)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataGetServiceEventIdByName($strEventName);
}

/**
 * Userdata Is Account Adsl
 *
 * @param int $intServiceID Service ID
 *
 * @return unknown
 */
function userdataIsAccountAdsl($intServiceID)
{
    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataIsAccountAdsl($intServiceID);
}

/**
 * Userdata Get Default Mailbox Alias
 *
 * @param int $intServiceID Service ID
 *
 * @return unknown
 */
function userdataGetDefaultMailboxAlias($intServiceID)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataGetDefaultMailboxAlias($intServiceID);
}

/**
 * Userdata Has 90 Day Trial
 *
 * @param int $intServiceID Service ID
 *
 * @return unknown
 */
function userdataHas90DayTrial($intServiceID)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataHas90DayTrial($intServiceID);
}

/**
 * Userdata Get Framework Locale
 *
 * @param int    $intServiceID Service ID
 * @param string $strUsername  Username
 *
 * @return unknown
 */
function userdataGetFrameworkLocale($intServiceID, $strUsername)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataGetFrameworkLocale($intServiceID, $strUsername);
}

/**
 * Userdata Service Username Log
 *
 * @param int    $intServiceId Service Id
 * @param string $strUsername  Username
 * @param string $strIsp       Isp
 *
 * @return unknown
 */
function userdata_service_username_log($intServiceId, $strUsername, $strIsp)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_service_username_log($intServiceId, $strUsername, $strIsp);
}

/**
 * Userdata Get Username History
 *
 * @param string $strUsername Username
 *
 * @return unknown
 */
function userdataGetUsernameHistory($strUsername)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataGetUsernameHistory($strUsername);
}

/**
 * Userdata Get Short Brand From Service Id
 *
 * @param int $intServiceId Service Id
 *
 * @return unknown
 */
function userdataGetShortBrandFromServiceId($intServiceId)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataGetShortBrandFromServiceId($intServiceId);
}

/**
 * Userdata Is Pnuk Customer
 *
 * @param int $intServiceId Service Id
 *
 * @return unknown
 */
function userdataIsPnukCustomer($intServiceId)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataIsPnukCustomer($intServiceId);
}

/**
 * Userdata Get Db Extension For Groupware
 *
 * @param int $intServiceId Service Id
 *
 * @return unknown
 */
function userdataGetDbExtensionForGroupware($intServiceId)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataGetDbExtensionForGroupware($intServiceId);
}

/**
 * Userdata Get Brand For Calendar
 *
 * @param int $intServiceId Service Id
 *
 * @return unknown
 */
function userdataGetBrandForCalendar($intServiceId)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataGetBrandForCalendar($intServiceId);
}

/**
 * Userdata Get Credit Card Info
 *
 * @param int    $intCreditDetailsId   Credit Details Id
 * @param string $strDbTransactionName Db Transaction Name
 *
 * @return unknown
 */
function userdataGetCreditCardInfo($intCreditDetailsId, $strDbTransactionName = 'TRANSACTION_GET_CREDIT_CARD_INFO')
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataGetCreditCardInfo($intCreditDetailsId, $strDbTransactionName);
}

/**
 * Userdata User Update Mobile
 *
 * @param int    $intServiceId Service Id
 * @param string $strMobileNo  Mobile No
 *
 * @return unknown
 */
function userdataUserUpdateMobile($intServiceId, $strMobileNo)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataUserUpdateMobile($intServiceId, $strMobileNo);
}

/**
 * Userdata Service Lookup
 *
 * @param string $strCategory Category
 * @param string $strValue    Value
 *
 * @return unknown
 */
function userdata_service_lookup($strCategory, $strValue)
{
    return Lib_Userdata::singleton()->userdata_service_lookup($strCategory, $strValue);
}

/**
 * @param integer $intServiceId Service Id
 * @param string  $strCategory  Category
 *
 * @return string
 */
function userdata_service_lookup_value($intServiceId, $strCategory)
{
    return Lib_Userdata::singleton()->userdata_service_lookup_value($intServiceId, $strCategory);
}

/**
 * Lookup categories
 *
 * @return array
 */
function userdata_service_lookup_categories()
{
    return Lib_Userdata::singleton()->userdata_service_lookup_categories();
}

/**
 * Is Maaf Visp
 *
 * @param string $strVisp Visp
 *
 * @return bool
 */
function userdataIsMaafVisp($strVisp)
{
    return Lib_Userdata::singleton()->userdataIsMaafVisp($strVisp);
}

/**
 * Is Maaf User
 *
 * @param integer $intServiceId   Service Id
 * @param string  $strTransaction Transaction
 * @param bool    $bolUseMaster   Use master bool
 *
 * @return bool
 */
function userdataIsMaafUser($intServiceId, $strTransaction = 'TRANS_SPLIT_USERDATAISMAAFUSER', $bolUseMaster = true)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataIsMaafUser($intServiceId, $strTransaction, $bolUseMaster);
}

/**
 * Userdata Is Bv User
 *
 * @param int $intServiceId Service Id
 *
 * @return unknown
 */
function userdataIsBvUser($intServiceId)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataIsBvUser($intServiceId);
}

/**
 * Userdata Is Greenbee User
 *
 * @param int $intServiceId Service Id
 *
 * @return unknown
 */
function userdataIsGreenbeeUser($intServiceId)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataIsGreenbeeUser($intServiceId);
}

/**
 * Userdata Service Get By Username And Isp
 *
 * @param string $strUsername Username
 * @param string $strIsp      Isp
 *
 * @return unknown
 */
function userdataServiceGetByUsernameAndIsp($strUsername, $strIsp)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataServiceGetByUsernameAndIsp($strUsername, $strIsp);
}

/**
 * Userdata Get Non Direct Debit Surcharge
 *
 * @param int   $intServiceId    Service Id
 * @param array $arrInvoiceItems Invoice Items
 *
 * @return unknown
 */
function userdataGetNonDirectDebitSurcharge($intServiceId, &$arrInvoiceItems)
{
    if (!defined('COMPONENT_NON_DD_SURCHARGE')) {
        return 0;
    }

    if (!is_numeric($intServiceId)) {
        return 0;
    }

    // get details of the reseller account
    $intResellerId = userdata_service_is_resold($intServiceId);

    if (is_numeric($intResellerId) && ($intResellerId > 0)) {
        $intServiceId = $intResellerId;
    }

    // check for direct debit details
    require_once '/local/data/mis/database/database_libraries/direct-debit-access.inc';
    if (direct_debit_has_service_got_active_instruction($intServiceId) > 0) {
        return 0;
    }
    $directDebitDetails = Financial_DirectDebitDetails::getInstructionByServiceAndStatuses(
        $intServiceId,
        array(
            Financial_DirectDebitDetails::DD_AWAITING_SUBMISSION,
            Financial_DirectDebitDetails::DD_INSTRUCTION_SUBMITTED
        )
    );

    if (($directDebitDetails instanceof Financial_DirectDebitDetails) &&
        $directDebitDetails->getDbSource() == 'inserted at signup'
    ) {
        list($submissionDate, $submissionTime) = explode(' ', $directDebitDetails->getSubmissionDate());

        if (!empty($submissionDate) && $submissionDate != '0000-00-00') {
            // Skip charging DD surcharge if DD added within one month
            $ddSubmissionDate = I18n_Date::fromString($submissionDate);
            $minLimitNonChargeableDdSurchargeDate = I18n_Date::now();
            $minLimitNonChargeableDdSurchargeDate->modify(-1, I18n_Date::MONTHS);

            if ($ddSubmissionDate->getTimestamp() >= $minLimitNonChargeableDdSurchargeDate->getTimestamp()) {
                return 0;
            }
        }
    }

    // check for active component
    $arrCriteria = array(
        'service_id' => $intServiceId,
        'type'       => COMPONENT_NON_DD_SURCHARGE,
        'status'     => array('active')
    );

    $arrComponentDetails = userdata_component_find($arrCriteria);

    if (!empty($arrComponentDetails[0]['component_id'])) {
        // find charge
        $floCost = product_component_monthly_charge_get(COMPONENT_NON_DD_SURCHARGE, 'active');

        if ($floCost > 0) {
            $arrInvoiceItems[] = array(
                'description'  => 'Non Direct Debit Processing charge',
                'amount'       => $floCost,
                'gross'        => true,
                'component_id' => $arrComponentDetails[0]['component_id']
            );
        }

        return $floCost;
    }

    // doesn't have component so 0 surcharge
    return 0;
}

/**
 * Userdata User Is Insight Referred
 *
 * @param int $intServiceId Service Id
 *
 * @return unknown
 */
function userdata_user_is_insight_referred($intServiceId)
{
    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_user_is_insight_referred($intServiceId);
}

/**
 * Userdata Insert Service Charity
 *
 * @param int $intServiceID Service ID
 * @param int $intCharityID Charity ID
 *
 * @return unknown
 */
function userdataInsertServiceCharity($intServiceID, $intCharityID)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataInsertServiceCharity($intServiceID, $intCharityID);
}

/**
 * Userdata Change User Preference For Invoice Type
 *
 * @param int    $intAccountId      Account Id
 * @param string $strNewInvoiceType New Invoice Type
 *
 * @return unknown
 */
function UserdataChangeUserPreferenceForInvoiceType($intAccountId, $strNewInvoiceType)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataChangeUserPreferenceForInvoiceType($intAccountId, $strNewInvoiceType);
}

/**
 * Userdata Get User Preference For Invoice Type
 *
 * @param int $intAccountId Account Id
 *
 * @return unknown
 */
function UserdataGetUserPreferenceForInvoiceType($intAccountId)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataGetUserPreferenceForInvoiceType($intAccountId);
}

/**
 * Userdata Get Components By Generic Handle
 *
 * @param int   $intServiceId Service Id
 * @param array $arrHandle    Handle
 *
 * @return unknown
 */
function UserdataGetComponentsByGenericHandle($intServiceId, $arrHandle)
{
    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataGetComponentsByGenericHandle($intServiceId, $arrHandle);
}

/**
 * Userdata Get Components By Product Handle
 *
 * @param int   $intServiceId Service Id
 * @param array $arrHandle    Handle
 *
 * @return array
 */
function UserdataGetComponentsByProductHandle($intServiceId, $arrHandle)
{
    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->UserdataGetComponentsByProductHandle($intServiceId, $arrHandle);
}

/**
 * Userdata Get Primary Service From Account Id
 *
 * @param int $intAccountId Account Id
 *
 * @return unknown
 */
function userdataGetPrimaryServiceFromAccountId($intAccountId)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataGetPrimaryServiceFromAccountId($intAccountId);
}

/**
 * Userdata Get Market For User
 *
 * @param string $strCli             Cli
 * @param string $strTransactionName Transaction Name
 *
 * @return unknown
 */
function userdataGetMarketForUser($strCli, $strTransactionName = Db_Manager::DEFAULT_TRANSACTION)
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataGetMarketForUser($strCli, $strTransactionName = Db_Manager::DEFAULT_TRANSACTION);
}

/**
 * Reseller disabled broadband
 *
 * @param integer $service_id string
 *
 * @return unknown
 */
function userdata_service_reseller_disabled_broadband($service_id)
{
    return userdata_service_event_add(
        $service_id,
        SERVICE_EVENT_RESELLER_BROADBAND_RESTRICTION_ADDED,
        $service_definition_id,
        $service_definition_id,
        '',
        '',
        '',
        $now
    );
}

/**
 * Reseller enabled broadband
 *
 * @param integer $service_id Service Id
 *
 * @return unknown
 */
function userdata_service_reseller_enabled_broadband($service_id)
{

    $strQuery = "SELECT service_event_id as intActiveEventId FROM userdata.service_events WHERE"
        . " service_id='$service_id' AND event_type_id = '" . SERVICE_EVENT_RESELLER_BROADBAND_RESTRICTION_ADDED
        . "' ORDER BY service_event_id DESC LIMIT 1";

    $dbWorkplace = get_named_connection_with_db('userdata');
    $resResult = PrimitivesQueryOrExit($strQuery, $dbWorkplace);
    $intBbRestrictionId = PrimitivesResultGet($resResult, 'intActiveEventId');
    if (empty($intBbRestrictionId)) {
        $intBbRestrictionId = '';
    }

    return userdata_service_event_add(
        $service_id,
        SERVICE_EVENT_RESELLER_BROADBAND_RESTRICTION_REMOVED,
        '',
        '',
        '',
        '',
        '',
        '',
        $intBbRestrictionId
    );
}

/**
 * Has promo
 *
 * @param string  $strPromoCode Promo code
 * @param integer $intServiceId Service Id
 *
 * @return unknown
 */
function userdataHasPromo($strPromoCode, $intServiceId)
{
    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataHasPromo($strPromoCode, $intServiceId);
}

/**
 * Has web space
 *
 * @param integer $serviceId Service Id
 *
 * @return unknown
 */
function userdataHasWebspace($serviceId)
{
    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataHasWebspace($serviceId);
}

/**
 * Returns the pre signup row
 *
 * @param int $intServiceId The service ID
 *
 * @return array
 */
function userdataGetPresignupData($intServiceId)
{
    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataGetPresignupData($intServiceId);
}

/**
 * Links a preSignup with a service
 *
 * @param int $intPresignupDataId The PreSignup Id
 * @param int $intServiceId       The Service Id
 *
 * @return null
 */
function userdataLinkPreSignup($intPresignupDataId, $intServiceId)
{
    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataLinkPreSignup($intPresignupDataId, $intServiceId);
}

/**
 * Gets the postcode of a customer
 *
 * @param int $intServiceId Service Id
 *
 * @return null
 */
function userdataGetPostcodeByServiceId($intServiceId)
{
    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataGetPostcodeByServiceId($intServiceId);
}

/**
 * Gets the Merchant Summary Details
 *
 * @param int $intServiceId Service Id
 *
 * @return unknown
 */
function userdataGetMerchantSummaryDetails($intServiceId)
{
    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataGetMerchantSummaryDetails($intServiceId);
}

/**
 * Set new Merchant ID
 *
 * @param int    $intServiceId     Service Id
 * @param string $strNewMerchantID New Merchand Id
 *
 * @return unknown
 */
function userdataSetMerchantID($intServiceId, $strNewMerchantID)
{
    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataSetMerchantID($intServiceId, $strNewMerchantID);
}

/**
 * Find a user by direct debit reference
 *
 * @param string $ddRef Direct debit reference
 *
 * @return array
 **/
function userdata_user_find_by_direct_debit_reference($ddRef)
{
    $users = array();

    $query = "
        SELECT DISTINCT a.address_id, a.customer_id, a.house, a.street, a.town,
               a.county, a.postcode, a.country, a.status,
               u.user_id, u.telephone, u.vchEveningPhone, u.fax, u.mobile,
               u.email, u.position, u.salutation, u.forenames, u.surname,
               u.hint_question, u.hint_answer, s.service_id, s.isp,
               s.username, s.password, s.cli_number, s.type, s.status,
               s.startdate, s.enddate, s.next_invoice, s.next_invoice_warned,
               s.invoice_day, s.authorised_switch_payment, s.bolMailOptOut
          FROM transactions.direct_debit_instructions as ddi
    INNER JOIN userdata.services as s on s.service_id = ddi.service_id
    INNER JOIN userdata.users as u on u.user_id = s.user_id
    INNER JOIN userdata.addresses as a on a.customer_id = u.customer_id
         WHERE ddi.our_reference = '{$ddRef}'
      GROUP BY s.service_id";

    $connection = get_named_connection('userdata');
    $result = mysql_query($query, $connection)
    or report_error(__FILE__, __LINE__, mysql_error($connection));

    while ($user = mysql_fetch_assoc($result)) {
        $users[] = $user;
    }

    return $users;
}

/**
 * Userdata get componentId by specific service component type handle and status for specific service id
 *
 * @param int    $serviceId                         Service id
 * @param string $serviceComponentProductTypeHandle Service component product type handle
 * @param array  $statuses                          Statuses to filter by
 * @param bool   $excludeStatuses                   If provided statuses are excluded
 *
 * @return int component id / null if $serviceId or $serviceComponentProductTypeHandle are not valid or desired
 *                            component not found
 */
function userdataGetComponentId(
    $serviceId,
    $serviceComponentProductTypeHandle,
    array $statuses = array(),
    $excludeStatuses = true
) {
    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataGetComponentId(
        $serviceId,
        $serviceComponentProductTypeHandle,
        $statuses,
        $excludeStatuses
    );
}

/**
 * acsertain whether a given service belongs to a dealer account
 *
 * @param integer $service_id Service Id
 *
 * @return integer
 */
function userdata_service_is_dealer($service_id)
{
    $connection = get_named_connection("userdata");

    $service_id = addslashes($service_id);

    $query = "SELECT p.intPartnerActorId
              FROM Reseller.tblPartner AS p
              INNER JOIN Reseller.tblPartnerType AS t 
              ON p.intPartnerTypeId = t.intPartnerTypeId
              INNER JOIN PlusnetSession.tblBusinessActorPlusnetService AS s 
              ON p.intPartnerActorId = s.intBusinessActorId
              WHERE s.intServiceId = '{$service_id}' AND t.vchDescription LIKE '%Dealer%';";

    $results = mysql_query($query, $connection);

    if (mysql_num_rows($results) == 0) {
        return 0;
    } else {
        return 1;
    }
}

/**
 * Is service partner
 *
 * @param integer $service_id Service Id
 *
 * @return integer
 */
function userdata_service_is_partner($service_id)
{
    $connection = get_named_connection("userdata");

    $service_id = addslashes($service_id);

    $query = "SELECT p.intPartnerActorId
              FROM Reseller.tblPartner AS p
              INNER JOIN Reseller.tblPartnerType AS t 
              ON p.intPartnerTypeId = t.intPartnerTypeId
              INNER JOIN PlusnetSession.tblBusinessActorPlusnetService AS s 
              ON p.intPartnerActorId = s.intBusinessActorId
              WHERE s.intServiceId = '{$service_id}' AND t.vchDescription LIKE '%Partner%';";

    $results = mysql_query($query, $connection);

    if (mysql_num_rows($results) == 0) {
        return 0;
    } else {
        return 1;
    }
}

/**
 * Retrieve the communications preferences for a service_id
 *
 * @param integer $service_id Service Id
 *
 * @return array
 */
function userdataGetCommunicationPreferences($service_id)
{
    $connection = get_named_connection("userdata");
    $strQuery = 'SELECT mm.vchHandle as strMarketingMethodHandle, cmm.bolOptedIn ' .
        ' FROM tblMarketingMethod mm ' .
        ' INNER JOIN tblCustomerMarketingMethod cmm ON mm.intMarketingMethodId = cmm.intMarketingMethodId ' .
        " WHERE cmm.intServiceId = $service_id";
    $result = mysql_query($strQuery, $connection)
    or report_error(__FILE__, __LINE__, mysql_error($connection));

    while ($setting = mysql_fetch_assoc($result)) {
        $settings[$setting['strMarketingMethodHandle']] = $setting['bolOptedIn'];
    }

    return $settings;
}


/**
 * Retrieve the marketing preferences for a service_id
 *
 * @param integer $service_id Service Id
 *
 * @return array
 */
function userdataGetMarketingPreferences($service_id)
{
    $connection = get_named_connection("userdata");
    $strQuery = 'SELECT ms.vchHandle as strMarketingSourceHandle, cms.bolOptedIn ' .
        ' FROM tblMarketingSource ms ' .
        ' INNER JOIN tblCustomerMarketingSource cms ON ms.intMarketingSourceId = cms.intMarketingSourceId ' .
        " WHERE cms.intServiceId = $service_id";
    $result = mysql_query($strQuery, $connection)
    or report_error(__FILE__, __LINE__, mysql_error($connection));

    while ($setting = mysql_fetch_assoc($result)) {
        $settings[$setting['strMarketingSourceHandle']] = $setting['bolOptedIn'];
    }

    return $settings;
}

/**
 * Calculates the customer's next-invoice date and returns it as a date string in Y-m-d format
 *
 * @param int $intServiceID the customer's service id
 *
 * @return str
 */
function userdata_generate_next_invoice_date_by_service($intServiceID)
{

    $intServiceID = (int)$intServiceID;
    $service = userdata_service_get($intServiceID);

    $strLastInvoice = $service['next_invoice'];
    $intInvoiceDay = (int)$service['invoice_day'];
    $strInvoicePeriod = $service['invoice_period'];

    $strNextInvoice = userdata_generate_next_invoice_date($strLastInvoice, $intInvoiceDay, $strInvoicePeriod);

    if (empty($strNextInvoice)) {
        error_log(
            __FUNCTION__ .
            "[$intServiceID] WARNING: failed to calulate the next-invoice date. Parameters:" .
            " $strLastInvoice/$intInvoiceDay/$strInvoicePeriod"
        );
    }

    return $strNextInvoice;
}

/**
 * Calculates the next invoice date for the given start-date, invoice day and invoicing period
 * Returns a date-string in Y-m-d format
 *
 * @param string  $strLastInvoice   the "last" invoice date in Y-m-d format
 * @param integer $intInvoiceDay    the customer's invoice-day - needed when dealing with "short" months
 * @param string  $strInvoicePeriod the billing period (e.g. monthly, yearly, etc)
 *
 * @return string
 */
function userdata_generate_next_invoice_date($strLastInvoice, $intInvoiceDay, $strInvoicePeriod)
{
    $invalidDateList = array('0000-00-00', '9999-09-09');

    if (in_array($strLastInvoice, $invalidDateList)) {
        return '';
    }

    $sqlInvoicePeriod = '';
    switch ($strInvoicePeriod) {
        case 'monthly':
            $sqlInvoicePeriod = '1 MONTH';
            break;
        case 'quarterly':
            $sqlInvoicePeriod = '3 MONTH';
            break;
        case 'half-yearly':
            $sqlInvoicePeriod = '6 MONTH';
            break;
        case 'yearly':
            $sqlInvoicePeriod = '12 MONTH';
            break;
        case 'never':
            $sqlInvoicePeriod = '1 MONTH';
            break;
        default:
            report_error(__FILE__, __LINE__, "Unrecognised payment period [$strInvoicePeriod]");
            break;
    }

    // A customer's invoice "day" can change when going from a long month to a short month.  E.g. a customer with a
    // default invoice date of the 31st will be billed on January 31st, February 28th and March 30th.
    // We therefore need to "rebuild" the last-invoice date to make sure that it's set to the most appropriate day -
    // otherwise, there's a risk that we'll return a NULL date as MySql's DATE_ADD() will return NULL if the
    // supplied date is invalid (e.g. '2014-02-31').
    $uxtInvoiceDate = strtotime($strLastInvoice);
    $strLastDay = date('t', $uxtInvoiceDate);
    $strInvoiceDay = ($intInvoiceDay <= $strLastDay ? $intInvoiceDay : $strLastDay);

    $strYear = date('Y', $uxtInvoiceDate);
    $strMonth = date('m', $uxtInvoiceDate);
    $strTmpDate = "$strYear-$strMonth-$strInvoiceDay";

    $query = "SELECT DATE_ADD('$strTmpDate', INTERVAL $sqlInvoicePeriod) AS next_invoice";
    $conn = get_named_connection('userdata_reporting');
    $res = mysql_query($query, $conn) or report_error(__FILE__, __LINE__, mysql_error($conn));

    if (mysql_num_rows($res) != 1) {
        error_log(
            __FUNCTION__ . "() WARNING: failed to calculate next-invoice date for $strTmpDate/$sqlInvoicePeriod"
        );

        return '';
    }

    $rec = mysql_fetch_assoc($res);
    $strNextInvoice = $rec['next_invoice'];

    if (empty($strNextInvoice)) {
        error_log(
            __FUNCTION__ . "(): WARNING: failed to calculate next-invoice date for $strTmpDate/$sqlInvoicePeriod"
        );

        return '';
    }

    return $strNextInvoice;
}

/**
 * Check whether to display provisioning notice
 *
 * @param integer $serviceId Service Id
 *
 * @return string|null
 */
function userdataGetFibreProvisioningNotice($serviceId)
{
    $arrUserData = userdata_service_get($serviceId);

    if (adslGetServiceDefinition($arrUserData['type']) == 'residential') {
        if ($arrUserData['status'] != 'active') {
            $lineResult = LineCheck_Result::getLatestResultByServiceId($serviceId);
            $supplierRules = new Product_SupplierProductRules(
                $lineResult,
                new Int($arrUserData['type']),
                new Int($serviceId)
            );
            $supplierProduct = $supplierRules->getSupplierProduct();

            if ($supplierProduct->getProductCode() == "FTTC") {
                if (($supplierProduct->getMax()->__toString() == '80000' &&
                    $supplierProduct->getMaxUpStream()->__toString() == "20Mbit/s" &&
                    $supplierProduct->getInternalMaxDownstream() == null)
                ) {
                    $strFttcProvisioningNotice = "This is an Unlimited Fibre order and should be provisioned on the " .
                        "80Mbps download and 20Mbps upload BT Wholesale product";
                } elseif (($supplierProduct->getMax()->__toString() == '40000' &&
                    $supplierProduct->getMaxUpStream()->__toString() == "10Mbit/s")
                ) {
                    $strFttcProvisioningNotice = "This is an Unlimited Fibre order and should be provisioned on the " .
                        "40Mbps download and 10Mbps upload BT Wholesale product";
                }
            }
        }

        $arrProductChangeDetails = userdataServiceIsProductChanging($serviceId);

        if (!empty($arrProductChangeDetails) &&
            isset($arrProductChangeDetails['intNewSdi']) &&
            $arrProductChangeDetails['strChangeComplete'] == 'no'
        ) {
            if ($arrProductChangeDetails['intNewSdi'] == '6867' ||
                $arrProductChangeDetails['intNewSdi'] == '6863'
            ) {
                $strFttcProvisioningNotice = "This is an Unlimited Fibre order and should be provisioned on the " .
                    "80Mbps download and 20Mbps upload BT Wholesale product";
            } elseif ($arrProductChangeDetails['intNewSdi'] == '6868' ||
                $arrProductChangeDetails['intNewSdi'] == '6864'
            ) {
                $strFttcProvisioningNotice = "This is an Unlimited Fibre order and should be provisioned on the " .
                    "40Mbps download and 10Mbps upload BT Wholesale product";
            }
        }
    }

    return isset($strFttcProvisioningNotice) ? $strFttcProvisioningNotice : null;
}

/**
 * Userdata Add Restricted Access
 *
 * @param int $intCustomerId Customer Id
 *
 * @return void
 */
function userdataMarkAsRestrictedAccess($intCustomerId)
{
    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataMarkAsRestrictedAccess($intCustomerId);
}

/**
 * Userdata Service Get Scheduled details
 *
 * @param int $intServiceId Service Id
 *
 * @return unknown
 */
function userdataServiceGetScheduledDetails($intServiceId)
{
    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataServiceGetScheduledDetails($intServiceId);
}

/**
 * Userdata Service Update Scheduled details
 *
 * @param int $scheduleId Scheduled Id
 *
 * @return unknown
 */
function userdataServiceUpdateScheduledChangeStatus($scheduleId)
{
    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataServiceUpdateScheduledChangeStatus($scheduleId);
}

/**
 * Userdata Get Login Component
 *
 * @param int $serviceId Service id
 *
 * @return login componentId
 */
function userdata_get_login_component_by_serviceid($serviceId)
{
    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_get_login_component_by_serviceid($serviceId);
}

/**
 * Userdata Get Termination Data
 *
 * @param int $serviceId Service id
 *
 * @return termination data object
 */
function userdata_get_terminationdata_by_serviceid($serviceId)
{
    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_get_terminationdata_by_serviceid($serviceId);
}

/**
 * Userdata Get Component Instances
 *
 * @param int $componentId Component id
 *
 * @return Component Instances
 */
function userdata_get_component_instances_by_component_id($componentId)
{
    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_get_component_instances_by_component_id($componentId);
}

/**
 * Userdata Set Component Instance Status
 *
 * @param int $componentId Component id
 * @param int $statusId    Status Id
 *
 * @return void
 */
function userdata_update_component_instance_status($componentId, $statusId)
{
    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_update_component_instance_status($componentId, $statusId);
}

/**
 * Userdata service update scheduled changeDate
 *
 * @param int    $scheduleId    Schedule id
 * @param string $scheduledDate Scheduled date
 *
 */
function userdataServiceUpdateScheduledChangeDate($scheduleId, $scheduledDate)
{
    AccountChange_AccountChangeApi::updateProductScheduledChangeDate($scheduledDate, $scheduleId);
}

/**
 * @param Array $terminationData termination Data
 *
 * @return array
 */
function userdata_termination_quotes_add($terminationData)
{
    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_termination_quotes_add(
        $terminationData['DPID'],
        $terminationData['terminationDate'],
        isset($terminationData['ceaseType']) ? $terminationData['ceaseType'] : '',
        isset($terminationData['ceaseReason']) ? $terminationData['ceaseReason'] : '',
        isset($terminationData['productInstance']) ? $terminationData['productInstance'] : '',
        isset($terminationData['isTransferOrder']) ? $terminationData['isTransferOrder'] : 0,
        isset($terminationData['isRetainDomainEmail']) ? $terminationData['isRetainDomainEmail'] : 0,
        isset($terminationData['componentId']) ? $terminationData['componentId'] : 0,
        isset($terminationData['terminationDateWithNoticePeriod']) ?
            $terminationData['terminationDateWithNoticePeriod'] : null,
        isset($terminationData['requestedDate']) ? $terminationData['requestedDate'] : '',
        isset($terminationData['lastUserId']) ? $terminationData['lastUserId'] : ''
    );
}

/**
 * Cancel Account Closure
 *
 * @param int $intServiceId Service Id
 *
 * @return void
 */
function userdata_termination_quote_remove($intServiceId)
{
    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_termination_quote_remove($intServiceId);
}

/**
 * Get Termination quote by user
 *
 * @param int $intServiceId Service Id
 *
 * @return int
 */
function userdata_get_termination_quote($intServiceId)
{
    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_get_termination_quote($intServiceId);
}

/**
 * Userdata update service type
 *
 * @param int $serviceId           Service id
 * @param int $serviceDefinitionId Service Definition Id
 *
 * @return void
 */
function userdataUpdateServiceType($serviceId, $serviceDefinitionId)
{
    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdataUpdateServiceType($serviceId, $serviceDefinitionId);
}

/**
 * Use product service definition to determine if ISP is supported by RBM
 *
 * @param int $intServiceId Service id
 *
 * @return bool
 */
function userdataIsIspSupportedByRBM($intServiceId)
{

    $connection = get_named_connection("userdata");

    $service_id = addslashes($service_id);

    $query = "SELECT sd.isp
              FROM products.service_definitions AS sd, userdata.services AS us
              WHERE sd.service_definition_id = us.type
              AND us.service_id = '$intServiceId'
              ";

    $executeQuery = mysql_query($query, $connection)
    or report_error(__FILE__, __LINE__, mysql_error($connection));

    $results = mysql_fetch_assoc($executeQuery);

    switch ($results['isp']) {
        case 'madasafish':
        case 'metronet':
        case 'greenbee':
        case 'waitrose':
            return false;
            break;
        default:
            return true;
    }
}

/**
 * Userdata fetch dummy cli number
 *
 * @return array
 */
function userdata_fetch_dummycli()
{

    $objUserdata = Lib_Userdata::singleton();

    return $objUserdata->userdata_fetch_dummycli();
}

/**
 * Shall add/display Free Staff Line Rental Component during Legacy Account Change journey - this shall return true
 * when customer is switching to Free Staff BB product and also either:
 * - have WLR and is not removing it during the process
 * - have no WLR but is adding it during the process
 *
 * @param int   $newServiceDefinitionId New ServiceDefinition Id
 * @param array $currentComponents      Array of current components
 * @param array $componentsToAdd        Array of components that will be added during the process
 * @param array $componentsToRemove     Array of components that will be removed during the process
 *
 * @return bool
 */
function userdataShallAddFreeStaffLineRentalComponent(
    $newServiceDefinitionId,
    $currentComponents,
    $componentsToAdd,
    $componentsToRemove
) {
    $result = false;

    if ($newServiceDefinitionId == FREE_STAFF_SERVICE_DEFINITION_ID) {
        $phoneComponentTypes = array_column(GetWlrComponentTypes(), 'service_component_id');

        $currentPhoneComponentId = null;
        $willAddPhone = false;
        $willRemovePhone = false;

        foreach ($currentComponents as $component) {
            if ((in_array($component['component_type_id'], $phoneComponentTypes)
                && $component['status'] == 'active')
            ) {
                $currentPhoneComponentId = $component['component_id'];
                break;
            }
        }

        if (!empty($currentPhoneComponentId)) {
            $willRemovePhone = in_array($currentPhoneComponentId, $componentsToRemove);
        } else {
            foreach ($componentsToAdd as $component_type_id) {
                if (in_array($component_type_id, $phoneComponentTypes)) {
                    $willAddPhone = true;
                    break;
                }
            }
        }

        $result = ((!empty($currentPhoneComponentId) && !$willRemovePhone) || $willAddPhone);
    }

    return $result;
}
