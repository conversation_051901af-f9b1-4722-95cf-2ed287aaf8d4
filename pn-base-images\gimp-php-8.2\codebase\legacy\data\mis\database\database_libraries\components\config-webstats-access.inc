<?php

	/////////////////////////////////////////////////////////////////////
	// File:     config-webstats-access.inc
	// Purpose:  Access mini-library for config_webstats
	/////////////////////////////////////////////////////////////////////

	/////////////////////////////////////////////////////////////////////
	// Functions
	//
	// Write functions
	// ---------------
	//
	// config_webstats_add
	//
	// Read functions
	// --------------
	//
	// config_webstats_get
	// config_webstats_by_component_status
	//
	// Delete functions
	// ----------------
	//
	// config_webstats_delete
	//
	/////////////////////////////////////////////////////////////////////

	/////////////////////////////////////////////////////////////////////
	// Data

	$global_component_configurators["23"] = "config_webstats_configurator";
	$global_component_configurators["24"] = "config_webstats_configurator";
	
	//
	/////////////////////////////////////////////////////////////////////
	// Hack to insert the component configurator array into PHP5's global scope
	// if it's not already there
	if(!isset($GLOBALS['global_component_configurators']))
	{
		$GLOBALS['global_component_configurators'] = $global_component_configurators;
	}
	else
	{
		foreach($global_component_configurators as $intIndex => $strConfigurator)
		{
			$GLOBALS['global_component_configurators'][$intIndex] = $strConfigurator;
		}
	}

	/////////////////////////////////////////////////////////////////////
	// Library functions

	/////////////////////////////////////////////////////////////
	// Function:  config_webstats_add
	// Purpose:   Create a webstats configuration for a component
	// Arguments: $component_id (id of corresponding component)
	// Returns:   ID of the webstats configuration record
	/////////////////////////////////////////////////////////////

	function config_webstats_add ($component_id) {

		global $global_db_src;
                $connection = get_named_connection ("userdata");

		$component_id = addslashes ($component_id);

		mysql_query ("INSERT INTO config_webstats (component_id)
						   VALUES ('$component_id')", $connection);

		return mysql_insert_id();

	}


	/////////////////////////////////////////////////////////////
	// Function:  config_webstats_get
	// Purpose:   Get a component and webstats configuration
	//            record
	// Arguments: $component_id
	// Returns:   Union of component and webstats configuration
	/////////////////////////////////////////////////////////////

	function config_webstats_get ($component_id) {

		global $global_db_src;
                $connection = get_named_connection ("userdata");

		$component_id = addslashes ($component_id);

		$result = mysql_query ("SELECT * FROM components LEFT JOIN config_webstats
						 ON components.config_id = config_webstats.webstats_id
						 WHERE components.component_id = '$component_id'", $connection);

		$union = mysql_fetch_array ($result, MYSQL_ASSOC);

		mysql_free_result ($result);

		return $union;
	}


	/////////////////////////////////////////////////////////////
	// Function:  config_webstats_get_by_component_status
	// Purpose:   Retrieve all webstats with a particular
	//              component status
	// Arguments: $status (desired status)
	// Returns:   Array of component-webstats unions
	/////////////////////////////////////////////////////////////

	function config_webstats_get_by_component_status ($status) {

		global $global_db_src;
                $connection = get_named_connection ("userdata");

		$status = addslashes ($status);

		$result = mysql_query ("SELECT * FROM components,config_webstats
						 WHERE components.component_id = config_webstats.component_id
						 AND components.status = '$status'", $connection);

		$unions = array();
                while ($union = mysql_fetch_array ($result, MYSQL_ASSOC)) {
                        $unions[] = $union;
                }

		mysql_free_result ($result);

		return $unions;
	}


	/////////////////////////////////////////////////////////////
	// Function:  config_webstats_destroy
	// Purpose:   Delete a webstats configuration
	// Arguments: $webstats_id
	/////////////////////////////////////////////////////////////

	function config_webstats_destroy($component_id)
	{
		$component = userdata_component_get($component_id);

		$webstats_id = $component['config_id'];

		config_webstats_delete($webstats_id);

		userdata_component_set_status($component_id, 'destroyed');
	}


	/////////////////////////////////////////////////////////////
	// Function:  config_webstats_delete
	// Purpose:   Delete a webstats configuration
	// Arguments: $webstats_id
	/////////////////////////////////////////////////////////////

	function config_webstats_delete ($webstats_id) {

		global $global_db_src;

		$connection = get_named_connection ("userdata");

		$component_id = addslashes ($webstats_id);

		mysql_query ("DELETE FROM config_webstats
				     WHERE webstats_id = '$webstats_id'", $connection);
	}


	// Library functions
	/////////////////////////////////////////////////////////////////////
	
	function config_webstats_configurator ($component_id, $action)
	{
	
		switch ($action)
		{
			case "auto_configure":
				// Nothing to do here
				break;

			case "auto_disable":
				
				break;

			case "auto_enable":
				
				break;

			case "auto_refresh":
				// Nothing to do here
				break;

			case "auto_destroy":
				config_webstats_destroy ($component_id);
				break;
				
			case 'queued_activate':
				// P12995 - this forces a script to alter the passwords
				userdata_component_set_status($component_id, 'queued-activate');
				break;

			default:
				break;
		}
	}
?>
