<?php

    require_once('/local/data/mis/database/database_libraries/sql_primitives.inc');
    /**
     * Wrapper for sql_primitives to save some typing,
     * throw exceptions, reuse handles and help with transactions
     * Also alows to inject mock object for unittesting
     */
    final class Mail_Db {

        /**
         * @var Mail_Db Singleton, class instance
         */
        private static $_objInstance = null;

        /**
         * @var array Valid handles loaded from $global_named_connections_config
         */
        private static $_arrValidHandles = null;

        /**
         * @var boolean Whether to log queries or not
         */
        private static $_bolLog = false;

        /**
         * The last connection used by the query method
         *
         * @var resource
         **/
        private $_lastConnection = null;

        /**
         * Constructor loads valid handles from $global_named_connections_config
         */
        private function __construct()
        {
            self::$_arrValidHandles = array_keys($GLOBALS['global_named_connections_config']);
        }

        /**
         * Singleton pattern
         *
         * @return Mail_Db
         */
        public static function getInstance()
        {
            if(!isset(self::$_objInstance)) {
                self::$_objInstance = new self();
            }
            return self::$_objInstance;
        }

        /**
         * Allows to replace the db object with mock object at any time during testing
         *
         * @param MockMail_Db $mock
         */
        public static function injectMockObject(MockMail_Db $objMock)
        {
            self::$_objInstance = $objMock;
        }

        /**
         * Turns on/off query logging to catch slow and repeated queries
         * use Mail_Db::getInstance()->logging(true); to enable logging
         * you can enable it for any part of the script and disable by passing false
         *
         * @param boolean flag
         */
        public static function logging($flag)
        {
            self::$_bolLog = (bool)$flag;
        }

        /**
         * Wraps around PrimitivesQueryOrExit, throws exception on failure
         *
         * @param string query
         * @param string handle
         * @return mysql_result
         * @throws Exception
         */
        public function query($strQuery, $strHandle)
        {
            if(self::$_bolLog) {
                $uxtStart = microtime(true);
            }

            if(!in_array($strHandle, self::$_arrValidHandles)) {
                throw new Mail_Exception("Invalid database handle: $strHandle");
            }

            $this->_lastConnection = get_named_connection_with_db($strHandle);

            $resResult = PrimitivesQueryOrExit($strQuery, $this->_lastConnection,'',false);

            if(self::$_bolLog) {
                $uxtEnd = microtime(true);
                $floTime = round($uxtEnd - $uxtStart, 5);
                error_log('Debug query:' . $strQuery . PHP_EOL . "time: {$floTime}s");
            }

            if(!$resResult) {
                throw new Mail_Exception("Query failed: $strQuery\n" .mysql_error());
            } else {
                return $resResult;
            }
        }

        /**
         * Calls self::query and returns insert id
         *
         * @param string query
         * @param string handle
         * @return integer
         */
        public function insert($strQuery, $strHandle)
        {
            $resResult = $this->query($strQuery, $strHandle);
            return $this->getInsertId($strHandle);
        }

        /**
         * Calls self::query and PrimitivesResultGet to return a table row
         *
         * @param string query
         * @param string handle
         * @return array
         */
        public function getSingleRow($strQuery, $strHandle)
        {
            $resResult = $this->query($strQuery, $strHandle);
            if(PrimitivesNumRowsGet($resResult) == 1) {
                return PrimitivesResultGet($resResult);
            }
        }

        /**
         * Calls self::query and PrimitivesResultGet to return a single value
         *
         * @param string query
         * @param string field
         * @param string handle
         * @return mixed
         */
        public function getSingleValue($strQuery, $strField, $strHandle)
        {
            $resResult = $this->query($strQuery, $strHandle);
            if(PrimitivesNumRowsGet($resResult) == 1) {
                return PrimitivesResultGet($resResult, $strField);
            }
        }

        /**
         * Calls self::query and PrimitivesResultGet to return multiple table rows
         *
         * @param string query
         * @param string handle
         * @return array
         */
        public function getArray($strQuery, $strHandle)
        {
            $resResult = $this->query($strQuery, $strHandle);
            if(PrimitivesNumRowsGet($resResult) > 0) {
                return PrimitivesResultsAsArrayGet($resResult);
            }
        }

        /**
         * Calls self::query and PrimitivesResultsAsListGet to return a list of values
         *
         * @param string query
         * @param string handle
         * @return array
         */
        public function getList($strQuery, $strHandle)
        {
            $resResult = $this->query($strQuery, $strHandle);
            if(PrimitivesNumRowsGet($resResult) > 0) {
                return PrimitivesResultsAsListGet($resResult);
            }
        }

        /**
         * Returns insert id
         *
         * @param string connection
         * @return integer
         */
        public function getInsertId($strHandle)
        {

            // Use the same connection that we did the insert on if 
            // it's available..
            $connection = $this->_lastConnection;

            // We'll have to connect again if it isn't
            if (!is_resource($connection)) {
                $connection = get_named_connection_with_db($strHandle);
            } 

            $intId = PrimitivesInsertIdGet($connection);
            if(is_numeric($intId)) {
                return $intId;
            } else {
                throw new Mail_Exception('PrimitivesInsertIdGet faild to return numeric insert id');
            }
        }

        /**
         * Overloaded call to begin, commit, rollback
         * Allows to perform transaction on multiple connection
         */
        public function __call($strName, $arrArgs)
        {
            if(in_array(strtolower($strName), array('begin', 'commit', 'rollback'))) {
                if(!empty($arrArgs)) {
                    foreach ($arrArgs as $strHandle) {
                        $this->query($strName, $strHandle);
                    }
                } else {
                    throw new Mail_Exception('Database connection handle not specified');
                }
            } else {
                throw new Mail_Exception('Invalid method call');
            }
        }
    }
