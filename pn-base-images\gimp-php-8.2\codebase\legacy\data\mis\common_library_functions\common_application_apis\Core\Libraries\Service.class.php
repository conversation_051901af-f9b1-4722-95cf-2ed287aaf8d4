<?php

use Plusnet\BillingApiClient\Service\ServiceManager as BillingServiceManager;
use Plusnet\BillingApiClient\Facade\BillingApiFacade;
use Plusnet\Feature\FeatureToggleManager;

require_once '/local/data/mis/audit_logging/AuditLogger.php';

class Core_Service
{
    /**
     * The maximum number of Service objects to cache.
     * userdata_service_get has this at 20,000, but I don't think we use as many of these objects
     */
    const SERVICE_CACHE_THRESHOLD = 1000;

    /**
     * @var array
     */
    private static $_serviceCache;

    /**
     * Limit the size of the cache
     */
    private static $_cacheSize = self::SERVICE_CACHE_THRESHOLD;

    /**
     * @var Core_ServiceDao
     */
    private $objDao = null;

    /**
     * @var Core_Account
     */
    private $objCoreAccount = null;

    /**
     * @var Core_VispConfig
     */
    private $objVispConfig = null;

    /**
     * @var Core_User
     */
    private $objUser = null;

    /**
     * @var I18n_Date
     */
    private $nextInvoiceDate = null;

    /**
     *
     * @var array
     */
    protected $arrAdslProductDetails = null;

    private static $arrEssentialSdi = array(6704,6705,6708,6709,6710,6711);


    /**
     * Reset the service cache
     *
     * @return void
     */
    public static function reset()
    {
        self::$_serviceCache = array();
    }

    /**
     * getUserDetailsForServiceId
     *
     * @param integer $intServiceId       Service ID
     * @param string  $strTransactionName Database transaction name
     *
     * @return Core_User
     */
    public static function getUserDetailsForServiceId(
        $intServiceId,
        $strTransactionName = Db_Manager::DEFAULT_TRANSACTION
    ) {
        return Core_User::getUserByServiceId($intServiceId, $strTransactionName);
    }

    /**
     * getServiceByUsernameRealm
     *
     * @param string $strUsername        Username
     * @param string $strRealm           Realm
     * @param string $strTransactionName Database transaction name
     *
     * @return Core_Service|bool
     *
     * @throws Core_Exception
     */
    public static function getServiceByUsernameRealm(
        $strUsername,
        $strRealm,
        $strTransactionName = Db_Manager::DEFAULT_TRANSACTION
    ) {
        if ('' == trim($strUsername) || '' == trim($strRealm)) {
            return false;
        }

        try {
            $objAdaptor = Db_Manager::getAdaptor('Core', $strTransactionName);
            $intServiceId = $objAdaptor->getServiceByUsernameRealm($strUsername, $strRealm);

            if ($intServiceId) {
                return new Core_Service($intServiceId, $strTransactionName);
            }
        } catch (Exception $objException) {
            throw new Core_Exception($objException->getMessage());
        }

        return false;
    }

    /**
     * isJlpVisp
     *
     * @param string $strVisp the VISP
     *
     * @return boolean
     */
    public static function isJlpVisp($strVisp)
    {
        // WARNING
        // DO NOT ADD 'johnlewis' TO THIS LIST
        // IT IS *NOT* A JLP VISP - ONLY GB and WAITROSE ARE
        return in_array($strVisp, array('greenbee', 'waitrose'));
    }

    /**
     * isEssentialSdi
     *
     * @param integer $intSdi the service definition ID
     *
     * @return bool
     */
    public static function isEssentialSdi($intSdi)
    {
        return in_array((int)$intSdi, self::$arrEssentialSdi);
    }

    /**
     * Check whether the isp is partner
     *
     * @param string $strIsp the ISP
     *
     * @return bool
     */
    public static function isPartnerVisp($strIsp)
    {
        return ($strIsp == 'partner');
    }

    /**
     * Retrieve the object by the Service Id
     *
     * @param integer $serviceId Service ID to retrieve
     * @param boolean $refresh   Fetch the fresh copy from the DB
     *
     * @return Core_Service
     */
    public static function fetchByServiceId($serviceId, $refresh = false)
    {
        if (!empty(self::$_serviceCache[$serviceId]) && !$refresh) {
            $service = self::$_serviceCache[$serviceId];
        } else {
            $service = self::cacheService(new Core_Service($serviceId));
        }

        return $service;
    }

    /**
     * setServiceCacheSize
     *
     * @param mixed $size the size
     *
     * @return void
     */
    public static function setServiceCacheSize($size)
    {
        if (is_numeric($size)) {
            self::$_cacheSize = $size;
        }
    }

    /**
     * Cache a service accout, assuming caching is enabled
     *
     * @param Core_Service $service Service to cache
     *
     * @return Core_Service
     */
    public static function cacheService(Core_Service $service)
    {
        // This constant may be set to prevent caching of services. We should obey it here.
        if (!defined('USERDATA_DO_NOT_CACHE_SERVICES') || USERDATA_DO_NOT_CACHE_SERVICES == false) {
            if (count(self::$_serviceCache) >= self::$_cacheSize) {
                // Threshold exceeded. Shift the oldest entry off the array
                array_shift(self::$_serviceCache);
            }

            self::$_serviceCache[$service->getServiceId()] = $service;
        }

        return $service;
    }

    /**
     * Get service data from <i>userdata.services</i> table based on given <i>$intAccountId</i> value
     *
     * @uses Core_ServiceDao
     * @uses Core_Service
     *
     * @param int    $intAccountId         the account ID
     * @param string $strDbTransactionName the transaction name
     * @param bool   $bolUseMaster         whether to run on master
     *
     * @return Core_Service
     */
    public static function getServiceByAccountId(
        $intAccountId,
        $strDbTransactionName = 'TRANSACTION_GET_SERVICE_BY_ACCOUNTID',
        $bolUseMaster = false
    ) {
        $objAdaptor = Db_Manager::getAdaptor('Core', $strDbTransactionName, $bolUseMaster);

        /* @var $objAdaptor Db_Adaptor */
        $arrDetails = $objAdaptor->getServiceByAccountId($intAccountId);

        $objServiceDao = new Core_ServiceDao($strDbTransactionName, true);

        if (is_array($arrDetails) && !empty($arrDetails[0])) {
            $objServiceDao->setVars($arrDetails[0]);
        }

        $objCoreService = new Core_Service();
        $objCoreService->setDao($objServiceDao);

        Db_Manager::commit($strDbTransactionName);

        return $objCoreService;
    }

    /**
     * Enter description here...
     *
     * @param int    $intCreditDetailsId   the credit details ID
     * @param string $strDbTransactionName the transaction name
     * @param bool   $bolRunOnMaster       whether to run on master
     *
     * @throws Exception
     * @return Core_Service
     */
    public static function getServiceByCreditDetailsId(
        $intCreditDetailsId,
        $strDbTransactionName,
        $bolRunOnMaster = false
    ) {
        Dbg_Dbg::write("Looking for service data with $intCreditDetailsId credit details ID", 'Core');

        if (false == is_numeric($intCreditDetailsId) || 0 == $intCreditDetailsId) {
            $strMessage = __CLASS__ . __METHOD__ . ' incorrect credit details ID passed';
            throw new Exception($strMessage);
        }

        $objAdaptor = Db_Manager::getAdaptor('Core', $strDbTransactionName, $bolRunOnMaster);

        /* @var $objAdaptor Db_Adaptor */
        $arrDetails = $objAdaptor->getServiceByCreditDetailsId($intCreditDetailsId);

        $objServiceDao = new Core_ServiceDao($strDbTransactionName, true);

        if (is_array($arrDetails) && !empty($arrDetails[0])) {
            Dbg_Dbg::write("Service ID {$arrDetails[0]['service_id']} found", 'Core');
            $objServiceDao->setVars($arrDetails[0]);
        }

        $objCoreService = new Core_Service();
        $objCoreService->setDao($objServiceDao);

        Db_Manager::commit($strDbTransactionName);

        return $objCoreService;
    }

    /**
     * <p>Constructor</p>
     * <p>When <i>$intServiceId</i> provided it initialize
     * {@link Core_Service::$objDao} DAO
     * </p>
     *
     * @uses Core_ServiceDao::get()
     *
     * @param int    $intServiceId       the service ID
     * @param string $strTransactionName the transaction name
     *
     * @throws Core_Exception
     */
    public function __construct($intServiceId = null, $strTransactionName = Db_Manager::DEFAULT_TRANSACTION)
    {
        if (isset($intServiceId)) {
            // Use type casting to avoid exceptions on zero-filled values
            $intServiceId = (int) $intServiceId;

            try {
                $this->objDao = Core_ServiceDao::get($intServiceId, $strTransactionName);
            } catch (Db_Exception $objException) {
                $strMessage = "Core_Service failed to get service details for service [$intServiceId]" .
                    " on transaction [$strTransactionName]." .
                    " Exception message: " . $objException->getMessage() . "\n" .
                    " Exception stacktrace: " . $objException->getTraceAsString();

                error_log(__METHOD__ . ': ' . $strMessage);
                throw new Core_Exception($strMessage);
            }
        }
    }

    /**
     * Takes care of all calls to setters and getters of DAO object
     *
     * @access public
     *
     * @uses Core_Service::$objDao
     *
     * <AUTHOR> Marek" <<EMAIL>>
     * <AUTHOR> Borodaenko" <<EMAIL>>
     *
     * @param string $strMethod Method name
     * @param array  $arrParams Params passed to method called
     *
     * @throws Exception
     * @return mixed
     */
    public function __call($strMethod, $arrParams)
    {
        if (preg_match('/^(get|set)/', $strMethod)) {
            return call_user_func_array(array($this->objDao, $strMethod), $arrParams);
        }

        throw new Exception("Method does not exist: ".get_class($this).'::'.$strMethod);
    }

    /**
     * Return a string of the data access object
     *
     * @return string
     */
    public function __toString()
    {
        if (isset($this->objDao)) {
            return $this->objDao->__toString();
        } else {
            return '';
        }
    }

    /**
     * Calls write method of DAO object
     *
     * @uses Core_Service::$objDao
     *
     * @return bool
     */
    public function write()
    {
        return $this->objDao->write();
    }

    /**
     * Sets {@link Core_Service::$objDao} DAO
     *
     * @param Core_IServiceDao $objCoreServiceDao the service
     *
     * @return void
     */
    public function setDao(Core_IServiceDao $objCoreServiceDao)
    {
        $this->objDao = $objCoreServiceDao;
    } // public function setDao(Core_IServiceDao $objCoreServiceDao)

    /**
     * getDao
     *
     * @return Core_ServiceDao
     */
    protected function getDao()
    {
        return $this->objDao;
    }

    /**
     * Get a billing date object
     *
     * @return Core_BillingDate
     */
    public function getBillingDate()
    {
        try {
            $billingDate = Core_BillingDate::getBillingDateFromDateAndPeriod(
                $this->getNextInvoiceDate()->toMySql(),
                $this->getInvoicePeriod(),
                $this->getInvoiceDay(),
                $this->isRbmMigrationComplete()
            );
        } catch (Exception $e) {
            $billingDate = null;
        }

        return $billingDate;
    }

    /**
     * Get the next invoice date for an account, from RBM or coredb
     *
     * @return I18n_Date NextInvoiceDate
     */
    public function getNextInvoiceDate()
    {
        if (!empty($this->nextInvoiceDate)) {
            return $this->nextInvoiceDate;
        }

        $serviceId = $this->getServiceId();

        if (FeatureToggleManager::isOnFiltered("RBM_MIGRATION_COMPLETE", null, null, null, $this->getServiceId())) {
            // Shiny new - RBM will get the invoice date of the top-level partner
            $this->nextInvoiceDate = $this->getInvoiceDateFromRBM($serviceId);
        } else {
            // Legacy - need to walk up to the top level partner ourselves
            $helper = Reseller_CustomerHelper::getByServiceId(new Int($serviceId));
            if ($helper instanceof Reseller_CustomerHelper && $helper->isPartner() && !$helper->isEndUser()) {
                try {
                    $partner = new Core_Service($helper->getPartnerServiceId()->getValue());
                    $this->nextInvoiceDate = $partner->getNextInvoiceDate();
                } catch (InvalidArgumentException $e) {
                    $this->nextInvoiceDate = null;
                }
            } else {
                try {
                    $this->nextInvoiceDate = $this->getDao()->getNextInvoiceDate();
                } catch (InvalidArgumentException $e) {
                    $this->nextInvoiceDate = null;
                }
            }
        }

        return $this->nextInvoiceDate;
    }



    /**
     * Get the invoice date after the next one.
     *
     * @return I18n_Date NextInvoiceDate
     */
    public function getNextNextInvoiceDate()
    {
        $invoiceDate = $this->getNextInvoiceDate();
        $increment_value = 1;
        $increment_unit = I18n_Date::MONTHS;

        switch ($this->getInvoicePeriod())
        {
            case 'quarterly':
                $increment_value = 3;
                break;
            case 'half-yearly':
                $increment_value = 6;
                break;
            case 'yearly':
                $increment_value = 12;
                break;
            case 'monthly':
            case 'never':
                break;
            default:
                report_error (__FILE__, __LINE__, "impossible payment period");
                break;
        }

        $invoiceDate->modify($increment_value, $increment_unit);
        return $invoiceDate;
    }



    /**
     * Get the invoice date for a service from RBM
     *
     * Logs an error if there's an exception
     *
     * @param int $serviceId Service ID to look up
     *
     * @return I18n_Date
     */
    private static function getInvoiceDateFromRBM($serviceId)
    {
        AuditLogger::functionEntry(__METHOD__);

        /** @var BillingApiFacade $billingApiFacade */
        $billingApiFacade = BillingServiceManager::getService('BillingApiFacade');
        $billingData = $billingApiFacade->getBillingAccountLite($serviceId);

        if (isset($billingData['nextInvoiceDate'])) {
            // XML data - will be in string format
            AuditLogger::functionExit(__METHOD__);
            return I18n_Date::fromString($billingData['nextInvoiceDate']);
        }

        AuditLogger::functionExit(__METHOD__);
        return null;
    }

    /**
     * static method to invoke the RBM API to read the next invoice date
     *
     * @param array $userService userService Object
     *
     * @return bool isBillingToday
     */
    public static function isBillingTodayFromService($userService)
    {
        $nextInvoice = null;

        if (!isset($userService)) {
            return false;
        }

        $serviceId = $userService['service_id'];

        if ($userService['isp'] == 'partner') {
            $helper = Reseller_CustomerHelper::getByServiceId(new Int($serviceId));
            if (!is_null($helper)) {
                $partnerChild = $helper->getPartner();
                if (is_null($partnerChild) || ($partnerChild->getParentResellerId() != 0)) {
                    //partner child (end user)
                    return false;
                }
            }
        }

        if (FeatureToggleManager::isOnFiltered("RBM_MIGRATION_COMPLETE", null, null, null, $serviceId)) {
            // Get data from RBM
            $nextInvoice = self::getInvoiceDateFromRBM($serviceId);
        } elseif (isset($userService['next_invoice'])) {
            // Old system - look in coredb
            $nextInvoice = $userService['next_invoice'];
        }

        return self::checkDateToday($nextInvoice, $userService['invoice_period']);
    }

    /**
     * Check if this account is due to run billing today
     *
     * @return boolean isBillingToday
     */
    public function isBillingToday()
    {
        return self::checkDateToday($this->getNextInvoiceDate(), $this->objDao->getInvoicePeriod());
    }

    /**
     * stati method to check the logic for next invoice is today
     *
     * @param DateTime|I18n_Date|string $nextInvoiceBillDate next invoice date
     * @param string                    $nextCycle           frequency of payment
     *
     * @return bool isBillingToday
     */
    public static function checkDateToday($nextInvoiceBillDate, $nextCycle)
    {
        $isToday=false;
        $invoiceDate=null;

        if ($nextInvoiceBillDate instanceof I18n_Date) {
            $invoiceDate=$nextInvoiceBillDate->toDateTime();
        } elseif ($nextInvoiceBillDate instanceof DateTime) {
            $invoiceDate=$nextInvoiceBillDate;
        } elseif (gettype($nextInvoiceBillDate) == 'string') {
            $invoiceDate=DateTime::createFromFormat('Y-m-d', $nextInvoiceBillDate);
        } else {
            return false; // No parseable date, or null date
        }

        $todayDate = date('d');

        switch ($nextCycle) {
            case 'quarterly':
                $monthAdd = 3;
                break;
            case 'half-yearly':
                $monthAdd = 6;
                break;
            case 'yearly':
                $monthAdd = 12;
                break;
            default://monthly & never
                $monthAdd = 1;
                break;
        }

        $year = date('Y');

        $month = date('n');
        $month += $monthAdd;

        if ($month>12) {
            $year++;
            $month -= 12;
        }

        $expectedInvoiceDate = "$year-".($month<10?"0":"")."$month-".$todayDate;

        $nextActualDate = date('Y-m-d', strtotime($expectedInvoiceDate));

        if (( $nextActualDate == $invoiceDate->format('Y-m-d')) || ( date('Y-m-d') ==  $invoiceDate->format('Y-m-d'))) {
            $isToday = true;
        }

        return $isToday;
    }

    /**
     * getUser
     *
     * @return Core_User
     */
    public function getUser()
    {
        return $this->objUser;
    }

    /**
     * fetchUserDetails
     *
     * @return void
     */
    public function fetchUserDetails()
    {
        $this->objUser = self::getUserDetailsForServiceId($this->getServiceId(), $this->objDao->getTransaction());
    }

    /**
     * Returns how many months of discount left on their account
     *
     * <AUTHOR> <<EMAIL>>
     *
     * @return integer
     */
    public function getDiscountLength()
    {
        $arrDiscountDetails = $this->getDiscountDetails();
        $bolIsValueFamily = Core_ServiceDefinition::instance($this->getType())->isValueFamilyProduct();

        return ($bolIsValueFamily) ? count($arrDiscountDetails) : Core_ServiceDefinition::DEFAULT_DISCOUNT_LENGTH;
    }

    /**
     * Retrieves details about broadband product for given customer
     *
     * Returns an array with the following structure:
     * <pre>
     * array(
     *   [service_definition_id] => INT
     *   [name] => STRING
     *   [isp] => STRING
     *   [minimum_charge] => FLOAT
     *   [initial_charge] => FLOAT
     *   [type] => STRING
     *   [password_visible_to_support] => STRING
     *   [requires] => STRING
     *   [date_created] => STRING
     *   [end_date] => STRING
     *   [signup_via_portal] => STRING
     *   [blurb] => STRING
     *   [vchContract] => STRING
     *   [strComponentName] => STRING
     * )
     * </pre>
     *
     * If it is service component based product it gets <i>minimum_charge</i>
     * and <i>vchContract</i> out of <i>tblTariff</i> table and populates <i>strComponentName</i>.
     * Otherwise all the information comes from <i>service_definitions</i> table -
     * <i>strComponentName</i> is not available in that case.
     *
     * <AUTHOR> Marek <<EMAIL>>
     *
     * @see getServiceComponentDetailsForService
     * @see getServiceDefinitionDetailsForService
     *
     * @param boolean $bolFlush flush cached info
     *
     * @return array
     */
    public function getAdslDetails($bolFlush = false)
    {
        if (null == $this->arrAdslProductDetails || $bolFlush) {
            $objDatabase = Db_Manager::getAdaptor('Core');
            $this->arrAdslProductDetails = $objDatabase->getServiceComponentDetailsForService($this->getServiceId());

            if (empty($this->arrAdslProductDetails)) {
                $this->arrAdslProductDetails =
                    $objDatabase->getServiceDefinitionDetailsForService($this->getServiceId());
            }

            // In order to deal with the legacy and framework code locking each other
            // if was decided to commit the default transaction everytime we called the database
            Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);
        }

        return $this->arrAdslProductDetails;
    }

    /**
     * Method to return ADSL component name
     *
     * @return string
     */
    public function getAdslComponentName()
    {
        $this->getAdslDetails();
        if (isset($this->arrAdslProductDetails['strComponentName'])) {
            return $this->arrAdslProductDetails['strComponentName'];
        } else {
            return '';
        }
    }

    /**
     * Function to return current minimum charge
     *
     * @return int
     */
    public function getMinimumCharge()
    {
        $this->getAdslDetails();
        if (isset($this->arrAdslProductDetails['minimum_charge'])) {
            return $this->arrAdslProductDetails['minimum_charge'];
        } else {
            return 0;
        }
    }

    /**
     * Function to return current contract name
     *
     * @return string
     */
    public function getBroadbandContract()
    {
        $this->getAdslDetails();
        if (isset($this->arrAdslProductDetails['vchContract'])) {
            return $this->arrAdslProductDetails['vchContract'];
        } else {
            return '';
        }
    }

    /**
     * Function to return service component ID
     *
     * @return string
     */
    public function getServiceComponentId()
    {
        $this->getAdslDetails();
        if (isset($this->arrAdslProductDetails['service_component_id'])) {
            return $this->arrAdslProductDetails['service_component_id'];
        } else {
            return '';
        }
    }

    /**
     * Retrieves details about discounts for ValueFamily product
     *
     * Returns an array with the following structure:
     * <pre>
     * array(
     *   [decValue] => DECIMAL
     * )
     * </pre>
     *
     * <AUTHOR> Marek <<EMAIL>>
     *
     * @see getActiveDiscountDetails
     *
     * @return array
     */
    public function getDiscountDetails()
    {
        $arrDiscountDetails = array();
        $strComponentName = $this->getAdslComponentName();

        // Not ValueFamily product - no discount information
        if (empty($strComponentName)) {
            return $arrDiscountDetails;
        }

        // To make it work both Market information in InternetConnection component and discount component name
        // MUST be the same
        $strMarket = trim(substr($strComponentName, strpos($strComponentName, 'Market')));

        $objDatabase = Db_Manager::getAdaptor('ProductFamily');
        $arrDiscountDetails = $objDatabase->getActiveDiscountDetails($this->getServiceId(), "%$strMarket%");

        // In order to deal with the legacy and framework code locking each other
        // if was decided to commit the default transaction everytime we called the database
        Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);

        return $arrDiscountDetails;
    }

    /**
     * Fetches the report sector for this core service
     *
     * @return string
     */
    public function getReportSector()
    {
        if (!isset($this->_reportSector)) {
            $this->_reportSector = Db_Manager::getAdaptor('Core')
                ->getReportSectorForService($this->getServiceId());
        }

        return $this->_reportSector;
    }

    /**
     * Returns {@link Core_VispConfig} object
     *
     * <AUTHOR> Chudzinski <<EMAIL>>
     * @return Core_VispConfig
     */
    public function getVispConfig()
    {
        if (!$this->objVispConfig instanceof Core_VispConfig) {
            $this->objVispConfig = new Core_VispConfig($this->getIsp(), $this->objDao->getTransaction());
        }

        return $this->objVispConfig;
    }

    /**
     * isMaafUser
     *
     * @throws Core_Exception
     * @return bool
     */
    public function isMaafUser()
    {
        $objVispConfig = $this->getVispConfig();

        $strShortBrand = $objVispConfig->getShortBrand();

        if (empty($strShortBrand)) {
            $strMessage = 'Cannot identify short brand - got empty string';
            throw new Core_Exception($strMessage, Core_Exception::ERR_EMPTY_SHORT_BRAND);
        }

        return ($strShortBrand == 'ma');
    }

    /**
     * isBvUser
     *
     * @throws Core_Exception
     * @return bool
     */
    public function isBvUser()
    {
        $objVispConfig = $this->getVispConfig();
        $strShortBrand = $objVispConfig->getShortBrand();

        // ST-16: Accounts which have migrated from Greenbee/Waitrose are
        // still being picked up as Brightview accounts due to this method.
        $arrAdslDetails = $this->getAdslDetails();
        if ('johnlewis' == $arrAdslDetails['isp']) {
            return false;
        }

        if (empty($strShortBrand)) {
            $strMessage = 'Cannot identify short brand - got empty string';
            throw new Core_Exception($strMessage, Core_Exception::ERR_EMPTY_SHORT_BRAND);
        }

        return ($strShortBrand == 'ma' || $strShortBrand == 'gb' || $strShortBrand == 'wr');
    }

    /**
     * isJlpUser
     *
     * @return boolean
     */
    public function isJlpUser()
    {
        return self::isJlpVisp($this->getIsp());
    }

    /**
     * isGbUser
     *
     * @return boolean
     */
    public function isGbUser()
    {
        return ($this->getIsp() == 'greenbee');
    }

    /**
     * isPlusnetUser
     *
     * @return boolean
     */
    public function isPlusnetUser()
    {
        return ($this->getIsp() == 'plus.net');
    }

    /**
     * isPartnerUser
     *
     * @return boolean
     */
    public function isPartnerUser()
    {
        return ($this->getIsp() == 'partner');
    }

    /**
     * isJohnLewisUser
     *
     * @return boolean
     */
    public function isJohnLewisUser()
    {
        return ($this->getIsp() == 'johnlewis');
    }

    /**
     * Is the customer a John Lewis VISP customer (JLBB, Waitrose, Greenbee)
     *
     * @return bool
     */
    public function isJohnLewisVispCustomer()
    {
        return in_array($this->getIsp(), array(
            'johnlewis', 'greenbee', 'waitrose',
        ));
    }

    /**
     * Method to check whether the account is destroyed.
     *
     * @return bool
     */
    public function isDestroyedAccount()
    {
        if ('destroyed' == $this->objDao->getStatus()) {
            return true;
        }

        return false;
    }

    /**
     * Get service status.
     *
     * @return string
     */
    public function getStatus()
    {
        return $this->objDao->getStatus();
    }

    /**
     * Has home phone
     *
     * @return bool
     */
    public function hasHomePhone()
    {
        return ($this->getHomePhone() != null);
    }

    /**
     * Get home phone
     *
     * @return HomePhone
     */
    public function getHomePhone()
    {
        try {
            $this->requireHomePhone();
            $homePhone = new HomePhone($this->getServiceId());
        } catch (Exception $exception) {
            $homePhone = null;
        }

        return $homePhone;
    }

    /**
     * Require home phone
     *
     * @return void
     */
    private function requireHomePhone()
    {
        require_once '/local/data/mis/database/database_libraries/components/HomePhone.class.php';
    }

    /**
     * Checks whether the RBM migration has completed
     *
     * @return bool
     */
    protected function isRbmMigrationComplete()
    {
        return FeatureToggleManager::isOnFiltered('RBM_MIGRATION_COMPLETE', null, null, null, $this->getServiceId());
    }
}
