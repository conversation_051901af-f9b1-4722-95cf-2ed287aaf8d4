<?php
/**
 * HardwareRmaProcess.class.php
 *
 * @package    Application_Apis
 * @subpackage HardwareBundle
 * <AUTHOR> <<EMAIL>>
 * @link       link
 */

/**
 * HardwareRmaProcess Class
 *
 * @package    Application_Apis
 * @subpackage HardwareBundle
 * <AUTHOR> <<EMAIL>>
 * @link       link
 */
class HardwareRmaProcess extends HardwareBundleBase
{
    //
    // Private Members
    //

    var $m_objOriginalBundle = null; // Reference to the bundle that this RMA originated from
    var $m_intOriginalBundleID = 0;
    var $m_objReplacementBundle = null; // Reference to the bundle that contains replacement hardware
    var $m_intReplacementBundleID = 0;
    var $m_usiReasonID = 0;
    var $m_strReasonHandle = '';
    var $m_strReasonDisplayName = '';
    var $m_strReasonCode = '';
    var $m_strComment = '';
    var $m_usiTypeID = 0;
    var $m_strTypeHandle = '';
    var $m_strTypeDisplayName = '';
    var $m_usiStatusID = 0;
    var $m_strStatusHandle = 0;
    var $m_strStatusDisplayName = 0;
    var $m_arrOptions = array();
    var $m_recCalculatedItemChanges = false;
    var $m_arrRemovedItems = array();
    var $m_arrAddedItems = array();
    var $m_strRmaStart = '';
    var $m_strRmaEnd = '';
    var $m_arrRmaItems = array();
    var $m_intManualRma = 0;
    //
    // HardwareRmaProcess :: Get Functions
    //

    /**
     * GetByOriginalBundle
     *
     * @param Object &$objOriginalBundle OriginalBundle
     *
     * @return Ambigous <boolean, unknown>
     */
    function GetByOriginalBundle(&$objOriginalBundle)
    {
        $intOriginalBundleID = $objOriginalBundle->GetBundleID();

        $strRmaClassName = $objOriginalBundle->GetClassPrefix() . 'HardwareRmaProcess';

        $dbhConnection = get_named_connection('userdata');

        //Get the lates RMA process
        $strQuery = 'SELECT MAX(usiHardwareRmaProcessID) ' .
            'FROM tblHardwareRmaProcess ' .
            "WHERE usiConfigHardwareBundleID='$intOriginalBundleID'";

        $resResult = mysql_query($strQuery, $dbhConnection)
        or report_error(__FILE__, __LINE__, mysql_error($dbhConnection));

        $intRmaID = mysql_result($resResult, 0, 0);

        if (!isset($intRmaID) || $intRmaID == '' || $intRmaID == 0) {
            $unkRetVal = false;
        } else {
            //BTHardwareBundle
            $unkRetVal = new $strRmaClassName();

            $unkRetVal->SetOriginalBundle($objOriginalBundle);

            $unkRetVal->GetByID($intRmaID);
        }

        mysql_free_result($resResult);

        return $unkRetVal;

    } // function GetByOriginalBundle


    /**
     * GetByID
     *
     * @param int $intRmaID RmaID
     *
     * @return void
     */
    function GetByID($intRmaID)
    {
        // Make sure we have enough to work with
        $intSupplierID = $this->GetSupplierID();

        if ($intSupplierID == false) {
            return false;
        }

        $dbhConnection = get_named_connection('userdata');

        // Get the Rma itself
        $strQuery
            = 'SELECT hrp.usiConfigHardwareBundleID,' .
            ' hrp.usiReplacementHardwareBundleID,' .
            ' hrp.usiHardwareRmaReasonID,' .
            ' hrp.usiConfigHardwareBundleID,' .
            ' hrr.vchHandle      AS strReasonHandle,' .
            ' hrr.vchDisplayName AS strReasonDisplayName,' .
            ' hsrr.vchCode       AS strReasonCode,' .
            ' hrp.usiHardwareRmaTypeID,' .
            ' hrt.vchHandle      AS strTypeHandle,' .
            ' hrt.vchDisplayName AS strTypeDisplayName,' .
            ' hsrt.vchCode       AS strTypeCode,' .
            ' hrp.usiHardwareOrderStatusID,' .
            ' hos.tag            AS strStatusTag,' .
            ' hos.display_name   AS strStatusDisplayName,' .
            ' hrp.txtComment     AS strComment,' .
            ' hrp.dtmStart       AS strStart,' .
            ' hrp.dtmEnd         AS strEnd' .
            ' FROM tblHardwareRmaProcess AS hrp' .
            ' INNER JOIN products.tblHardwareRmaReason AS hrr' .
            ' ON hrp.usiHardwareRmaReasonID=hrr.usiHardwareRmaReasonID' .
            ' INNER JOIN products.tblHardwareSupplierRmaReason AS hsrr' .
            ' ON hrr.usiHardwareRmaReasonID=hsrr.usiHardwareRmaReasonID' .
            ' INNER JOIN products.vblHardwareRmaType AS hrt' .
            ' ON hrp.usiHardwareRmaTypeID=hrt.usiHardwareRmaTypeID' .
            ' INNER JOIN products.tblHardwSuppRmaType AS hsrt' .
            ' ON hrt.usiHardwareRmaTypeID=hsrt.usiHardwareRmaTypeID' .
            ' INNER JOIN products.hardware_order_statuses AS hos' .
            ' ON hrp.usiHardwareOrderStatusID=hos.hardware_order_status_id' .
            ' WHERE usiHardwareRmaProcessID=' . $intRmaID .
            ' AND hsrt.usiHardwareSupplierID=' . $intSupplierID .
            ' AND hsrr.usiHardwareSupplierID=' . $intSupplierID;

        $resResult = mysql_query($strQuery, $dbhConnection)
        or report_error(__FILE__, __LINE__, mysql_error($dbhConnection));

        if (mysql_num_rows($resResult) == 0) {
            return false;
        }

        $recRmaData = mysql_fetch_assoc($resResult);

        // Populate variables
        $this->m_usiReasonID = $recRmaData['usiHardwareRmaReasonID'];
        $this->m_strReasonHandle = $recRmaData['strReasonHandle'];
        $this->m_strReasonDisplayName = $recRmaData['strReasonDisplayName'];
        $this->m_strReasonCode = $recRmaData['strReasonCode'];
        $this->m_strComment = $recRmaData['strComment'];
        $this->m_usiTypeID = $recRmaData['usiHardwareRmaTypeID'];
        $this->m_strTypeHandle = $recRmaData['strTypeHandle'];
        $this->m_strTypeDisplayName = $recRmaData['strTypeDisplayName'];
        $this->m_usiStatusID = $recRmaData['usiHardwareOrderStatusID'];
        $this->m_strStatusHandle = $recRmaData['strStatusTag'];
        $this->m_strStatusDisplayName = $recRmaData['strStatusDisplayName'];
        $this->m_strRmaStart = $recRmaData['strStart'];
        $this->m_strRmaEnd = $recRmaData['strEnd'];
        $this->m_intOriginalBundleID = $recRmaData['usiConfigHardwareBundleID'];
        $this->m_intReplacementBundleID = $recRmaData['usiReplacementHardwareBundleID'];

        mysql_free_result($resResult);

        // Options
        $strQuery
            = 'SELECT usiHardwRmaProcessOptionID,' .
            ' hro.usiHardwareRmaOptionID,' .
            ' hrot.usiHardwareRmaOptionTypeID,' .
            ' hro.vchHandle       AS strOptionHandle,' .
            ' hro.vchDisplayName  AS strOptionDisplayName,' .
            ' hrot.vchHandle      AS strOptionTypeHandle,' .
            ' hrot.vchDisplayName AS strOptionTypeDisplayName' .
            ' FROM tblHardwRmaProcessOption AS hrpo' .
            ' INNER JOIN products.tblHardwareRmaOption AS hro' .
            ' ON hrpo.usiHardwareRmaOptionID=hro.usiHardwareRmaOptionID' .
            ' INNER JOIN products.vblHardwareRmaOptionType AS hrot' .
            ' ON hro.usiHardwareRmaOptionTypeID=hrot.usiHardwareRmaOptionTypeID' .
            ' WHERE usiHardwareRmaProcessID=' . $intRmaID;

        $resResult = mysql_query($strQuery, $dbhConnection)
        or report_error(__FILE__, __LINE__, mysql_error($connection));

        while ($recOption = mysql_fetch_assoc($resResult)) {
            $this->m_arrOptions[] = $recOption;
        }

        mysql_free_result($resResult);

        // Item changes

        // Added Items
        if (count($this->m_arrRmaItems) > 0) {
            $strQuery
                = 'SELECT hbi.hardware_bundle_item_id AS intItemID,' .
                ' hsp.display_name                 AS strDisplayName,' .
                ' hbi.quantity                     AS intQuantity,' .
                ' hbi.order_number                 AS strOrderCode,' .
                ' hsp.hardware_supplier_product_id AS intHardwareSupplierProductID,' .
                ' hpc.is_valuable                  AS intIsValuable,' .
                ' hpc.hardware_product_class_id    AS intHardwareProductClassID,' .
                ' hsp.curCostToCustomerIncVatPence AS curCostToCustomerIncVatPence,' .
                ' hrod.vchHandle                   AS strDirectionHandle' .
                ' FROM hardware_bundle_items               AS hbi' .
                ' INNER JOIN products.hardware_supplier_products AS hsp' .
                ' ON hbi.hardware_supplier_product_id=hsp.hardware_supplier_product_id' .
                ' INNER JOIN products.hardware_product_classes   AS hpc' .
                ' ON hsp.hardware_product_class_id=hpc.hardware_product_class_id' .
                ' INNER JOIN tblHardwRmaProcessItem              AS hrpi' .
                ' ON hrpi.usiHardwareBundleItemID=hbi.hardware_bundle_item_id' .
                ' INNER JOIN tblHardwareRmaProcess               AS hrp' .
                ' ON hrpi.usiHardwareRmaProcessID=hrp.usiHardwareRmaProcessID' .
                ' INNER JOIN products.vblHardwRmaOptDirection    AS hrod' .
                ' ON hrpi.usiHardwRmaOptDirectionID=hrod.usiHardwRmaOptDirectionID' .
                ' WHERE hsp.product_code IN (' . $this->m_arrRmaItems . ')';
        } else {
            $strQuery
                = 'SELECT hbi.hardware_bundle_item_id      AS intItemID,' .
                ' hsp.display_name                 AS strDisplayName,' .
                ' hbi.quantity                     AS intQuantity,' .
                ' hbi.order_number                 AS strOrderCode,' .
                ' hsp.hardware_supplier_product_id AS intHardwareSupplierProductID,' .
                ' hpc.is_valuable                  AS intIsValuable,' .
                ' hpc.hardware_product_class_id    AS intHardwareProductClassID,' .
                ' hsp.curCostToCustomerIncVatPence AS curCostToCustomerIncVatPence,' .
                ' hrod.vchHandle                   AS strDirectionHandle' .
                ' FROM hardware_bundle_items               AS hbi' .
                ' INNER JOIN products.hardware_supplier_products AS hsp' .
                ' ON hbi.hardware_supplier_product_id=hsp.hardware_supplier_product_id' .
                ' INNER JOIN products.hardware_product_classes   AS hpc' .
                ' ON hsp.hardware_product_class_id=hpc.hardware_product_class_id' .
                ' INNER JOIN tblHardwRmaProcessItem              AS hrpi' .
                ' ON hrpi.usiHardwareBundleItemID=hbi.hardware_bundle_item_id' .
                ' INNER JOIN tblHardwareRmaProcess               AS hrp' .
                ' ON hrpi.usiHardwareRmaProcessID=hrp.usiHardwareRmaProcessID' .
                ' INNER JOIN products.vblHardwRmaOptDirection    AS hrod' .
                ' ON hrpi.usiHardwRmaOptDirectionID=hrod.usiHardwRmaOptDirectionID' .
                ' WHERE hrp.usiHardwareRmaProcessID=' . $intRmaID;
        }

        $resResult = mysql_query($strQuery, $dbhConnection)
        or report_error(__FILE__, __LINE__, mysql_error($connection));

        $this->m_recCalculatedItemChanges = array(
            'Add'    => array(),
            'Remove' => array()
        );
        $this->m_arrRemovedItems = array();
        $this->m_arrAddedItems = array();

        while ($recRow = mysql_fetch_assoc($resResult)) {
            $this->m_recCalculatedItemChanges[$recRow['strDirectionHandle']] = $recRow;

            switch ($recRow['strDirectionHandle']) {
                case 'Add':
                    $this->m_arrAddedItems[] = $recRow['intItemID'];
                    break;

                case 'Remove':
                    $this->m_arrRemovedItems[] = $recRow['intItemID'];
                    break;
            }
        }

        $this->m_intID = $intRmaID;

        $this->MarkUpToDate();

        $this->SetValid();

        return $this;

    } // function GetByID

    /**
     * Get the options available for RMA on the bundle. Returned data is
     * ready for use by a template. Second Parameter is the permissions
     * object of the person trying to start an RMA process. Pass false if
     * there is no permissions object available
     *
     * @param Object &$objBundle Bundle
     * @param Object $objPerm    objPerm
     *
     * @return multitype:Ambigous <multitype:multitype: , string> number
     */
    function GetRmaOptions(&$objBundle, $objPerm = false)
    {
        $intSupplierID = $objBundle->GetSupplierID();
        $arrComponent = $objBundle->GetComponent();

        $dbConnection = get_named_connection_with_db('product_reporting');

        $intContractID = null;

        // Find out if this hardware bundle is associated with a free hardware contract
        $contractClient = BusTier_BusTier::getClient('contracts')
            ->setServiceId($arrComponent['service_id'])
            ->setRaiseServiceNotice(false);

        $criteria = array(
            'componentId' => $intComponentID
        );

        $contract = $contractClient->getContract($criteria);

        if (!empty($contract)) {

            $intContractID = $contract->getId();
        }

        //
        //Do we need to strip out any options for the BT RMA process
        //

        $intServiceComponentID = $arrComponent['component_type_id'];

        $strQuery = 'SELECT intHardwareRmaOptionID ' .
            ' FROM tblHardwServCompRmaOpt ' .
            " WHERE intServiceComponentID = '$intServiceComponentID' ";

        $resResult = PrimitivesQueryOrExit($strQuery, $dbConnection);
        $arrHardwareRmaOptionIDs = PrimitivesResultsAsListGet($resResult);
        $strINHardwareRmaOptionID = implode("','", $arrHardwareRmaOptionIDs);

        //
        // Construct a query to get the possible options for this hardware bundle
        //

        $dbhConnection = get_named_connection('product');

        $strQuery
            = 'SELECT hro.usiHardwareRmaOptionID,' .
            ' hrot.usiHardwareRmaOptionTypeID,' .
            ' hrot.vchHandle AS strOptionTypeHandle,' .
            ' hro.vchHandle AS strHardwareOptionHandle,' .
            ' hro.vchDisplayName AS strHardwareOptionDisplayName' .
            ' FROM products.tblHardwSuppRmaOption AS hsro' .
            ' INNER JOIN products.tblHardwareRmaOption AS hro' .
            ' ON hsro.usiHardwareRmaOptionID=hro.usiHardwareRmaOptionID' .
            ' INNER JOIN products.vblHardwareRmaOptionType AS hrot' .
            ' ON hro.usiHardwareRmaOptionTypeID=hrot.usiHardwareRmaOptionTypeID' .
            ' WHERE hro.dtmWhenRemoved IS NULL' .
            ' AND hsro.usiHardwareSupplierID=' . $intSupplierID;

        //If there are particular RMA Options that are the only ones available then add this to the query
        if ($strINHardwareRmaOptionID != '') {
            $strQuery .= ' AND hro.usiHardwareRmaOptionID IN (' . $strINHardwareRmaOptionID . ')';
        }

        $strQuery .= ' ORDER BY hrot.usiHardwareRmaOptionTypeID';

        $resResult = mysql_query($strQuery, $dbhConnection)
        or report_error(__FILE__, __LINE__, mysql_error($dbhConnection));

        $arrOptions = array();

        // Create the structure
        while ($recRow = mysql_fetch_assoc($resResult)) {

            if (!isset($arrOptions[$recRow['strOptionTypeHandle']])) {

                $arrOptions[$recRow['strOptionTypeHandle']] = array();
            }

            if ($intContractID && $recRow['strHardwareOptionHandle'] == 'NoReplacementHardware') {

                $recRow['strHardwareOptionDisplayName'] .= ' (nullify free hardware contract)';
            }

            $arrOptions[$recRow['strOptionTypeHandle']][] = $recRow;
        }

        mysql_free_result($resResult);

        // Find out which option should be the default for this bundle.
        $intBundleID = $objBundle->GetBundleID();

        $strQuery
            = 'SELECT hro.usiHardwareRmaOptionID' .
            ' FROM userdata.hardware_bundle_items AS hbi' .
            ' INNER JOIN products.hardware_supplier_products AS hsp' .
            ' ON hbi.hardware_supplier_product_id=hsp.hardware_supplier_product_id' .
            ' INNER JOIN products.hardware_product_classes AS hpc' .
            ' ON hsp.hardware_product_class_id=hpc.hardware_product_class_id' .
            ' INNER JOIN products.hardware_product_classes_lookup AS hpcl' .
            ' ON (hpc.hardware_product_class_id=hpcl.ancestor_id' .
            ' OR  (hpc.hardware_product_class_id=hpcl.hardware_product_class_id' .
            ' AND   hpcl.ancestor_id=0))' .
            ' INNER JOIN products.tblHardwRmaOptItemClass AS hroic' .
            ' ON hpcl.hardware_product_class_id=hroic.usiHardwareProductClassID' .
            ' INNER JOIN products.tblHardwareRmaOption AS hro' .
            ' ON hroic.usiHardwareRmaOptionID=hro.usiHardwareRmaOptionID' .
            ' INNER JOIN products.vblHardwRmaOptDirection AS hrod' .
            ' ON hroic.usiHardwRmaOptDirectionID=hrod.usiHardwRmaOptDirectionID' .
            ' INNER JOIN products.tblHardwSuppRmaOption AS hsro' .
            ' ON hro.usiHardwareRmaOptionID=hsro.usiHardwareRmaOptionID' .
            ' WHERE hsp.when_removed IS NULL' .
            ' AND hpc.when_removed IS NULL' .
            ' AND hro.dtmWhenRemoved IS NULL' .
            ' AND hbi.config_hardware_bundle_id=' . $intBundleID .
            ' AND hpc.is_valuable=1';

        $resResult = mysql_query($strQuery, $dbhConnection)
        or report_error(__FILE__, __LINE__, mysql_error($dbhConnection));

        if (mysql_num_rows($resResult) == 0) {
            $intDefaultOption = -1;
        } else {
            // There shouldn't be multiple, but if there is just pick the
            // first. It won't cause any harm.
            $intDefaultOption = mysql_result($resResult, 0, 0);
        }

        mysql_free_result($resResult);

        // Do we need to strip out options?
        // NOTE: This part isn't particularly generic, but it's just a
        // small part..

        if ($intDefaultOption !== -1 && $objPerm !== false && $objPerm->have_perm('manager') == false) {
            // How long since the hardware was ordered?
            $strWhenOrdered = $objBundle->GetWhenOrdered();

            $intDaysSinceOrder = (time() - strtotime($strWhenOrdered)) / SECONDS_IN_DAY;

            if ($intDaysSinceOrder >= 10) {
                // Unset all but the default option
                foreach ($arrOptions['Radio'] as $intIndex => $recOption) {
                    if ($recOption['usiHardwareRmaOptionID'] != $intDefaultOption) {
                        unset($arrOptions['Radio'][$intIndex]);
                    }
                }
            }
        }

        return array(
            'arrOptions'       => $arrOptions,
            'intDefaultOption' => $intDefaultOption
        );

    } // function GetRmaOptions


    /**
     * GetRmaReasons
     *
     * @param int $intSupplierID SupplierID
     *
     * @return multitype:unknown
     */
    function GetRmaReasons($intSupplierID)
    {
        $dbhConnection = get_named_connection('product');

        $strQuery
            = 'SELECT hrr.usiHardwareRmaReasonID,' .
            ' hrr.vchHandle     AS strHardwareRmaReasonHandle,' .
            ' hrr.vchDisplayName AS strHardwareRmaReasonDisplayName' .
            ' FROM products.tblHardwareRmaReason         AS hrr' .
            ' INNER JOIN products.tblHardwareSupplierRmaReason AS hsrr' .
            ' ON hrr.usiHardwareRmaReasonID=hsrr.usiHardwareRmaReasonID' .
            ' WHERE hrr.dtmWhenRemoved IS NULL' .
            ' AND hsrr.usiHardwareSupplierID=' . $intSupplierID;

        $resResult = mysql_query($strQuery, $dbhConnection)
        or report_error(__FILE__, __LINE__, mysql_error($dbhConnection));

        $arrResons = array();

        while ($recRow = mysql_fetch_assoc($resResult)) {
            $arrResons[] = $recRow;
        }

        return $arrResons;

    } // function GetRmaReasons

    /**
     * Function _GetRMAItemDetails
     * Uses objects comma separated strings of items to send and receive.
     * Function then gets details for each item and returns array of items
     * to RMA (expect from customer) and new items to send to customer
     * If no items are found in the object to RMA
     * (this should never occur), the function returns false
     *
     * <AUTHOR>
     *
     * @return boolean|number
     */
    function _GetRMAItemDetails()
    {
        switch ($this->m_arrOptions[0]) {
            case 1: //    RMA WITH DEFERRED CONTRACT CANCELLATION OR FOR REFUND
                if (count($this->m_objOriginalBundle->m_arrItems) > 0) {
                    foreach ($this->m_objOriginalBundle->m_arrItems as $item) {
                        $itemIDs[] = $item['intID'];
                    }
                    $strRemoveItemIDs = implode(",", $itemIDs);
                    $strAddItemIDs = "";
                    $strReturnsBag = 'credit';
                } else {
                    return (false);
                }
                break;
            case 2:    //    REPLACE ALL ITEMS
                if (count($this->m_objOriginalBundle->m_arrItems) > 0) {
                    foreach ($this->m_objOriginalBundle->m_arrItems as $item) {
                        $itemIDs[] = $item['intID'];
                    }
                    $strRemoveItemIDs = implode(",", $itemIDs);
                    $strAddItemIDs = &$strRemoveItemIDs;
                    $strReturnsBag = '';
                } else {
                    return (false);
                }
                break;
            case 3: //if any individual items were selected and passed to this screen
                if (count($this->m_arrRmaItems) > 0) {
                    $strRemoveItemIDs = "";
                    foreach ($this->m_arrRmaItems AS $item => $qty) {
                        $strRemoveItemIDs .= $item . ",";
                    }
                    $strRemoveItemIDs = substr_replace($strRemoveItemIDs, '', -1, 1);
                    $strAddItemIDs = &$strRemoveItemIDs;
                    $strReturnsBag = '';
                } else {
                    return (false);
                }
                break;
        }

        $dbhConnection = get_named_connection('product_reporting');

        //process items to REMOVE
        $strQuery
            = 'SELECT hbi.hardware_bundle_item_id AS intItemToRemoveID,' .
            ' hsp.display_name                 AS strDisplayName,' .
            ' hbi.order_number                 AS strOrderCode,' .
            ' hsp.hardware_supplier_product_id AS intHardwareSupplierProductID,' .
            ' hpc.is_valuable                  AS intIsValuable,' .
            ' hbi.quantity                     AS intQuantity,' .
            ' hpc.hardware_product_class_id    AS intHardwareProductClassID,' .
            ' hsp.curCostToCustomerIncVatPence AS curCostToCustomerIncVatPence' .
            ' FROM userdata.hardware_bundle_items  AS hbi' .
            ' INNER JOIN hardware_supplier_products      AS hsp' .
            ' ON hsp.hardware_supplier_product_id=hbi.hardware_supplier_product_id' .
            ' INNER JOIN hardware_product_classes        AS hpc' .
            ' ON  hsp.hardware_product_class_id=hpc.hardware_product_class_id' .
            ' WHERE hbi.hardware_bundle_item_id IN (' . $strRemoveItemIDs . ')';

        $resResult = mysql_query($strQuery, $dbhConnection)
        or report_error(__FILE__, __LINE__, mysql_error($dbhConnection));
        while ($recRow = mysql_fetch_assoc($resResult)) {
            $currentItemId = $recRow['intItemToRemoveID'];
            if (isset($this->m_arrRmaItems[$currentItemId])) {
                $recRow['intQuantity'] = $this->m_arrRmaItems[$currentItemId];
            }
            $arrRemove[] = $recRow;
        }

        mysql_free_result($resResult);

        //If a list of items to add were received, add them to add order
        if ($strAddItemIDs != '') {
            //process items to ADD
            $strQuery
                = 'SELECT hbi.hardware_bundle_item_id AS intItemToAddID,' .
                ' hsp.hardware_supplier_product_id AS intHardwareSupplierProductID,' .
                ' hsp.display_name                 AS strDisplayName,' .
                ' hpc.is_valuable                  AS intIsValuable,' .
                ' hpc.hardware_product_class_id    AS intHardwareProductClassID,' .
                ' hsp.curCostToCustomerIncVatPence AS curCostToCustomerIncVatPence,' .
                ' hbi.quantity                     AS intQuantity' .
                ' FROM userdata.hardware_bundle_items  AS hbi' .
                ' INNER JOIN hardware_supplier_products      AS hsp' .
                ' ON hsp.hardware_supplier_product_id=hbi.hardware_supplier_product_id' .
                ' INNER JOIN hardware_product_classes        AS hpc' .
                ' ON hsp.hardware_product_class_id=hpc.hardware_product_class_id' .
                ' WHERE hbi.hardware_bundle_item_id IN (' . $strAddItemIDs . ')';

            $resResult = mysql_query($strQuery, $dbhConnection)
            or report_error(__FILE__, __LINE__, mysql_error($dbhConnection));

            $objHardwareClient = BusTier_BusTier::getClient('hardware');
            $substitutionHelper = $objHardwareClient->getHardwareSubstitutionHelper();

            while ($recRow = mysql_fetch_assoc($resResult)) {
                $currentHardwareSupplierProductId = $recRow['intHardwareSupplierProductID'];
                if ($substitutionHelper->doesHardwareHaveSubstitution($currentHardwareSupplierProductId)) {
                    $substituteHardware = $objHardwareClient->getHardwareDetailsByHardwareSupplierProductId(
                        $substitutionHelper->getHardwareSubstituteId($currentHardwareSupplierProductId)
                    );
                    $recRow['intHardwareSupplierProductID'] = $substituteHardware->getHardwareSupplierProductId();
                    $recRow['strDisplayName'] = $substituteHardware->getDisplayName();
                    $recRow['substitutionItem'] = true;
                }

                $currentItemId = $recRow['intItemToAddID'];
                if (isset($this->m_arrRmaItems[$currentItemId])) {
                    $recRow['intQuantity'] = $this->m_arrRmaItems[$currentItemId];
                }
                //$recRow = array_slice($recRow,1);
                $arrAdd[] = $recRow;
            }
            mysql_free_result($resResult);
        }

        foreach ($arrRemove as $element) {
            $correct_bags = $this->get_correct_returns_bag($element['intHardwareSupplierProductID']);

            // If we find bag we do not look for it anymore
            if (!empty($correct_bags)) {
                break;
            }
        }

        //if credit, add credit bag

        if ($strReturnsBag == 'credit') {
            $returnsBag = $correct_bags[0][0];

            $strQuery
                = 'SELECT hsp.hardware_supplier_product_id AS intHardwareSupplierProductID,' .
                ' hsp.display_name                 AS strDisplayName,' .
                ' hpc.is_valuable                  AS intIsValuable,' .
                ' hpc.hardware_product_class_id    AS intHardwareProductClassID,' .
                ' hsp.curCostToCustomerIncVatPence AS curCostToCustomerIncVatPence' .
                ' FROM hardware_supplier_products      AS hsp' .
                ' INNER JOIN hardware_product_classes        AS hpc' .
                ' ON hsp.hardware_product_class_id=hpc.hardware_product_class_id' .
                ' WHERE hsp.tag = \'' . $returnsBag . '\'';

            $resResult = mysql_query($strQuery, $dbhConnection)
            or report_error(__FILE__, __LINE__, mysql_error($dbhConnection));

            while ($recRow = mysql_fetch_assoc($resResult)) {
                $recRow['intQuantity'] = 1;
                $arrAdd[] = $recRow;
            }

            mysql_free_result($resResult);
        } else {
            //else, add a return bag
            $returnsBag = $correct_bags[1][0];

            $strQuery
                = 'SELECT hsp.hardware_supplier_product_id AS intHardwareSupplierProductID,' .
                ' hsp.display_name                 AS strDisplayName,' .
                ' hpc.is_valuable                  AS intIsValuable,' .
                ' hpc.hardware_product_class_id    AS intHardwareProductClassID,' .
                ' hsp.curCostToCustomerIncVatPence AS curCostToCustomerIncVatPence' .
                ' FROM hardware_supplier_products      AS hsp' .
                ' INNER JOIN hardware_product_classes        AS hpc' .
                ' ON hsp.hardware_product_class_id=hpc.hardware_product_class_id' .
                ' WHERE hsp.tag = \'' . $returnsBag . '\'';


            $resResult = mysql_query($strQuery, $dbhConnection)
            or report_error(__FILE__, __LINE__, mysql_error($dbhConnection));

            while ($recRow = mysql_fetch_assoc($resResult)) {
                $recRow['intQuantity'] = 1;
                $arrAdd[] = $recRow;
            }

            mysql_free_result($resResult);

        }

        $arrRemove = array_filter($arrRemove, function ($item) {
            return false === strpos($item['strDisplayName'], 'Set Top Box');
        });

        $arrAdd = array_filter($arrAdd, function ($item) {
            return false === strpos($item['strDisplayName'], 'Set Top Box');
        });

        $recReturn['Remove'] = $arrRemove;
        $recReturn['Add'] = $arrAdd;
        $this->m_recCalculatedItemChanges = $recReturn;

        return $recReturn;
    }//function _GetRMAItemDetails

    /**
     * Work out a list of items that need to be:
     * a) Sent back by the customer and
     * b) Sent out to the customer.
     * This information is used all over the place in this system and is
     * pretty core to the whole thing. Whatever you do don't break it!
     *
     * @return boolean
     */
    function CalculateItemChanges()
    {
        //parse RMAitem's in query string and build SQL IN string

        if ($this->m_recCalculatedItemChanges !== false) {
            return $this->m_recCalculatedItemChanges;
        }

        $dbhConnection = get_named_connection('product');

        $intSupplierID = $this->GetSupplierID();

        if (count($this->m_arrOptions) == 0) {
            return false;
        }

        $intBundleID = $this->m_objOriginalBundle->GetBundleID();

        $recReturn = array(
            'Add'    => array(),
            'Remove' => array()
        );

        $arrRemove = array();
        $arrAdd = array();
        // For each option...

        /**
         * New Code for more dynamic RMA tool that enables RMA'ing of individual items rather than complete bundles
         * Checks if object was initiated by the Manual RMA process and if so checks the options and processes
         * Calls $this->_GetRMAItemDetails for most of the processing
         * <AUTHOR>
         * @date   080705
         **/

        //If any individual RMA items have been set, RMA only them, otherwise run as normal
        if ($this->m_intManualRma == 1) {
            if ($recReturn = $this->_GetRMAItemDetails()) {
                return $recReturn;
            } else {
                echo "An error occured and no items can currently be RMA'd. " .
                    "PLease select a Hardware item to be RMA'd";
                error_log(
                    "\n\n********* File: rma-hardware-bundle-classes.inc line "
                    . __LINE__ .
                    " could not complete successfully as No items were found in the object to RMA." .
                    "No items will be displayed in the screen 'Confirm RMA Submission' " .
                    "(hardware_bundle_rma_wizard_confirm.html). This is a very unlikely error and if it" .
                    " does occur, review all RMA related object files, starting at file " .
                    "rma-hardware-bundle-classes.inc function CalculateItemChanges. ds()" .
                    " the object and ensure that there are items in " .
                    "this->m_objOriginalBundle->m_arrItems and or this->m_arrRmaItems **********\n\n"
                );

                return (false);
            }
        } else {
            //else, if this RMA process is entered via a different method that the RMA screen, run this
            foreach ($this->m_arrOptions as $intOptionID) {
                // Find out what item should be removed, including how many.

                $strQuery
                    = 'SELECT hbi.hardware_bundle_item_id      AS intItemToRemoveID,' .
                    ' hsp.display_name                 AS strDisplayName,' .
                    ' hbi.quantity                     AS intQuantity,' .
                    ' hbi.order_number                 AS strOrderCode,' .
                    ' hsp.hardware_supplier_product_id AS intHardwareSupplierProductID,' .
                    ' hpc.is_valuable                  AS intIsValuable,' .
                    ' hpc.hardware_product_class_id    AS intHardwareProductClassID,' .
                    ' hsp.curCostToCustomerIncVatPence AS curCostToCustomerIncVatPence' .
                    ' FROM tblHardwareRmaOption            AS hro' .
                    ' INNER JOIN tblHardwRmaOptItemClass         AS hroc' .
                    ' ON hro.usiHardwareRmaOptionID=hroc.usiHardwareRmaOptionID' .
                    ' INNER JOIN vblHardwRmaOptDirection         AS hrod' .
                    ' ON hroc.usiHardwRmaOptDirectionID=hrod.usiHardwRmaOptDirectionID' .
                    ' INNER JOIN hardware_product_classes_lookup AS hpcl' .
                    ' ON (hroc.usiHardwareProductClassID=hpcl.ancestor_id' .
                    ' OR  (hroc.usiHardwareProductClassID=hpcl.hardware_product_class_id' .
                    ' AND   hpcl.ancestor_id=0))' .
                    ' INNER JOIN hardware_product_classes        AS hpc' .
                    ' ON hpcl.hardware_product_class_id=hpc.hardware_product_class_id' .
                    ' INNER JOIN hardware_supplier_products      AS hsp' .
                    ' ON hsp.hardware_product_class_id=hpc.hardware_product_class_id' .
                    ' INNER JOIN userdata.hardware_bundle_items  AS hbi' .
                    ' ON hsp.hardware_supplier_product_id=hbi.hardware_supplier_product_id' .
                    ' INNER JOIN hardware_order_statuses         AS hos' .
                    ' ON hbi.hardware_order_status_id=hos.hardware_order_status_id' .
                    ' WHERE hrod.vchHandle="Remove"' .
                    ' AND hos.tag IN ("dispatched","delivered","rma_complete")' .
                    ' AND hsp.hardware_supplier_id=' . $intSupplierID .
                    ' AND hro.usiHardwareRmaOptionID=' . $intOptionID .
                    ' AND hbi.config_hardware_bundle_id=' . $intBundleID;

                $resResult = mysql_query($strQuery, $dbhConnection)
                or report_error(__FILE__, __LINE__, mysql_error($dbhConnection));

                while ($recRow = mysql_fetch_assoc($resResult)) {
                    $arrRemove[] = $recRow;
                }

                mysql_free_result($resResult);

                // Find out what item should be added, using the number that was removed
                $strQuery
                    = 'SELECT hsp.hardware_supplier_product_id AS intHardwareSupplierProductID,' .
                    ' hsp.display_name                 AS strDisplayName,' .
                    ' hpc.is_valuable                  AS intIsValuable,' .
                    ' hpc.hardware_product_class_id    AS intHardwareProductClassID,' .
                    ' hsp.curCostToCustomerIncVatPence AS curCostToCustomerIncVatPence' .
                    ' FROM tblHardwareRmaOption            AS hro' .
                    ' INNER JOIN tblHardwRmaOptItemClass         AS hroc' .
                    ' ON hro.usiHardwareRmaOptionID=hroc.usiHardwareRmaOptionID' .
                    ' INNER JOIN vblHardwRmaOptDirection         AS hrod' .
                    ' ON hroc.usiHardwRmaOptDirectionID=hrod.usiHardwRmaOptDirectionID' .
                    ' INNER JOIN hardware_product_classes_lookup AS hpcl' .
                    ' ON (hroc.usiHardwareProductClassID=hpcl.ancestor_id' .
                    ' OR  (hroc.usiHardwareProductClassID=hpcl.hardware_product_class_id' .
                    ' AND   hpcl.ancestor_id=0))' .
                    ' INNER JOIN hardware_product_classes        AS hpc' .
                    ' ON hpcl.hardware_product_class_id=hpc.hardware_product_class_id' .
                    ' INNER JOIN hardware_supplier_products      AS hsp' .
                    ' ON hsp.hardware_product_class_id=hpc.hardware_product_class_id' .
                    ' WHERE hrod.vchHandle="Add"' .
                    ' AND hsp.when_removed IS NULL' .
                    ' AND hsp.hardware_supplier_id=' . $intSupplierID .
                    ' AND hro.usiHardwareRmaOptionID=' . $intOptionID;

                $resResult = mysql_query($strQuery, $dbhConnection)
                or report_error(__FILE__, __LINE__, mysql_error($dbhConnection));

                while ($recRow = mysql_fetch_assoc($resResult)) {
                    $arrAdd[] = $recRow;
                }

                mysql_free_result($resResult);

                // Collate quantities
                if (count($arrAdd) == count($arrRemove)) {
                    foreach ($arrRemove as $intIndex => $recRemoveItem) {
                        $arrAdd[$intIndex]['intQuantity'] = $recRemoveItem['intQuantity'];
                    }
                } else {
                    // Just default them all to 1. Shouldn't happen but we need
                    // to account for it anyway.
                    foreach ($arrAdd as $intIndex => $recDummy) {
                        $arrAdd[$intIndex]['intQuantity'] = 1;
                    }
                }

                $recReturn['Remove'] = $arrRemove;
                $recReturn['Add'] = $arrAdd;
            }

            $this->m_recCalculatedItemChanges = $recReturn;

            return $recReturn;
        }

    } // function CalculateItemChanges

    /**
     * CalculatePriceChanges
     *
     * @return multitype:number
     */
    function CalculatePriceChanges()
    {
        $recItemChanges = $this->CalculateItemChanges();

        // Collate value figures
        $curNetChange = 0;

        foreach ($recItemChanges['Add'] as $recItem) {
            $curNetChange += $recItem['curCostToCustomerIncVatPence'];
        }

        foreach ($recItemChanges['Remove'] as $recItem) {
            $curNetChange -= $recItem['curCostToCustomerIncVatPence'];
        }

        // Is the hardware payment being deferred?
        $recService = $this->m_objOriginalBundle->GetService();

        $recAdslProduct = adsl_get_product_details($recService['type']);

        $recProduct = product_get_service($recService['type']);

        // Get deferrment details on the account
        $recDeferData = UserdataGetDeferPeriodProgress($this->m_objOriginalBundle->GetServiceID());

        if ($recDeferData['bolHardwareDeferred'] === true) {
            // Do deferred calculations
            $curInstantCharge = ($curNetChange / 12) * $recDeferData['intMonthsIntoDeferPeriod'];

            $curSubscriptionChange = $curInstantCharge / $recDeferData['intMonthsIntoDeferPeriod'];

            switch ($recService['invoice_period']) {
                case 'quarterly':
                    $curSubscriptionChange *= 3;
                    break;

                case 'half-yearly':
                    $curSubscriptionChange *= 6;
                    break;
            }
        } else {
            // No deferring to deal with
            $curInstantCharge = $curNetChange;
            $curSubscriptionChange = 0;
        }

        return array(
            'curInstantCharge'      => $curInstantCharge / 100,
            'curSubscriptionChange' => $curSubscriptionChange / 100
        );

    } // function CalculatePriceChanges

    /**
     * Figure out what account type the account needs to be changed to to complete this RMA
     *
     * @return multitype:number string
     */
    function CalculateAccountTypeChange()
    {

        if ($this->IsLikeForLike() === true || $this->m_arrOptions[0] == 3) {
            return array(
                'intID'          => 0,
                'strDisplayName' => 'No change'
            );
        }

        // Find out the class of the items we're moving to
        $arrItemClasses = array();

        $arrCalculatedChanges = $this->CalculateItemChanges();
        foreach ($arrCalculatedChanges['Add'] as $recItem) {
            if ($recItem['intIsValuable'] == 1) {
                $arrItemClasses[] = $recItem['intHardwareProductClassID'];
            }
        }
        $intItemCount = count($arrItemClasses);

        if ($intItemCount > 0) {
            // Get the current account type
            $recService = $this->m_objOriginalBundle->GetService();

            $intCurrentTypeID = $recService['type'];

            $dbhConnection = get_named_connection('product');

            // Ensure that the temporary table doesn't already exist
            $strDrop = 'DROP TABLE IF EXISTS tblPossibleTypes';

            mysql_query($strDrop, $dbhConnection)
            or report_error(__FILE__, __LINE__, mysql_error($dbhConnection));

            // Time for the most monsterous query of all time! (Ulp!)
            $strQuery
                = 'CREATE TEMPORARY TABLE tblPossibleTypes' .
                ' SELECT DISTINCT sd2.service_definition_id AS intID,' .
                ' sd2.name AS strDisplayName' .
                ' FROM' .
                ' service_definitions AS sd1' .
                ' INNER JOIN adsl_product AS ap1' .
                ' ON ap1.service_definition_id = sd1.service_definition_id' .
                ' INNER JOIN service_component_config AS sip_comp1' .
                ' ON sip_comp1.service_definition_id = sd1.service_definition_id' .
                ' INNER JOIN service_component_config AS dial_comp1' .
                ' ON dial_comp1. service_definition_id = sd1. service_definition_id' .
                ' INNER JOIN component_ipzone_config AS sip1' .
                ' ON sip1.service_component_id = sip_comp1.service_component_id' .
                ' INNER JOIN component_radius_config AS dialup1' .
                ' ON dial_comp1.service_component_id = dialup1.service_component_id' .
                ' INNER JOIN service_definitions AS sd2' .
                ' ON sd2.service_definition_id != sd1.service_definition_id' .
                ' INNER JOIN adsl_product AS ap2' .
                ' ON ap2.service_definition_id = sd2.service_definition_id' .
                ' INNER JOIN service_component_config AS sip_comp2' .
                ' ON sip_comp2.service_definition_id = sd2.service_definition_id' .
                ' INNER JOIN service_component_config AS dial_comp2' .
                ' ON dial_comp2.service_definition_id = sd2. service_definition_id' .
                ' INNER JOIN component_ipzone_config AS sip2' .
                ' ON sip2.service_component_id = sip_comp2.service_component_id' .
                ' INNER JOIN component_radius_config AS dialup2' .
                ' ON dial_comp2.service_component_id = dialup2.service_component_id' .
                ' LEFT JOIN tblHardwareOnlyBundles AS hob1' .
                ' ON sd1.service_definition_id = hob1.intServiceDefinitionID' .
                ' LEFT JOIN tblHardwareOnlyBundles AS hob2' .
                ' ON sd2.service_definition_id = hob2.intServiceDefinitionID' .
                ' WHERE sd1.service_definition_id=ap1.service_definition_id' .
                ' AND ap1.service_definition_id=sip_comp1.service_definition_id' .
                ' AND ap1.service_definition_id=dial_comp1.service_definition_id' .
                ' AND sip_comp1.service_component_id=sip1.service_component_id' .
                ' AND dial_comp1.service_component_id=dialup1.service_component_id' .
                ' AND sd2.service_definition_id=ap2.service_definition_id' .
                ' AND ap2.service_definition_id=sip_comp2.service_definition_id' .
                ' AND ap2.service_definition_id=dial_comp2.service_definition_id' .
                ' AND sip_comp2.service_component_id=sip2.service_component_id' .
                ' AND dial_comp2.service_component_id=dialup2.service_component_id' .
                ' AND dialup1.subscription_required="Y"' .
                ' AND dialup1.requires_active_account="Y"' .
                ' AND dial_comp1.default_quantity>0' .
                ' AND sd1.isp=sd2.isp' .
                ' AND sd1.requires=sd2.requires' .
                ' AND sd1.type=sd2.type' .
                ' AND IF(sd1.end_date IS NULL, sd2.end_date IS NULL, sd2.end_date IS NOT NULL)' .
                ' AND sd1.signup_via_portal=sd2.signup_via_portal' .
                ' AND ap1.nat=ap2.nat' .
                ' AND ap1.deferred_install_fee=ap2.deferred_install_fee' .
                ' AND ap1.ip_stream=ap2.ip_stream' .
                ' AND ap1.speed=ap2.speed' .
                ' AND sip1.intZoneId=sip2.intZoneId' .
                ' AND dialup1.dialup_group_id=dialup2.dialup_group_id' .
                ' AND dialup1.subscription_required=dialup2.subscription_required' .
                ' AND dialup1.quota_id=dialup2.quota_id' .
                ' AND dialup1.requires_active_account=dialup2.requires_active_account' .
                ' AND dialup1.can_be_disabled=dialup2.can_be_disabled' .
                ' AND dialup1.user_viewable=dialup2.user_viewable' .
                ' AND dialup1.staticip_allowed=dialup2.staticip_allowed' .
                ' AND IF (sd1.name LIKE "%(Annual Contract)%", sd2.name LIKE "%(Annual Contract)%", 1)' .
                ' AND IF (sd1.name LIKE "%(Monthly Contract)%", sd2.name LIKE "%(Monthly Contract)%", 1)' .
                ' AND IF (hob1.intHardwareBundleID IS NULL,' .
                ' hob2.intHardwareBundleID IS NULL, hob2.intHardwareBundleID IS NOT NULL)' .
                ' AND sd1.service_definition_id=' . $intCurrentTypeID;

            mysql_query($strQuery, $dbhConnection)
            or report_error(__FILE__, __LINE__, mysql_error($dbhConnection));
            // The temporary table now contains a list of account types
            // with the same characteristics as the current account type.
            // We now need tio search this list of find the one that
            // contains the hardware we're moving to.

            $strClasses = implode(',', $arrItemClasses);

            $strQuery
                = 'SELECT pt.intID, pt.strDisplayName,' .
                ' ABS(pt.intID-667) AS intInterval,' .
                ' COUNT(*) AS intNum' .
                ' FROM tblPossibleTypes AS pt' .
                ' INNER JOIN service_component_config AS sip_hardw' .
                ' ON pt.intID=sip_hardw.service_definition_id' .
                ' INNER JOIN component_hardware_bundle_config AS hardw_comp' .
                ' ON sip_hardw.service_component_id=hardw_comp.service_component_id' .
                ' INNER JOIN hardware_bundle_config_items AS hardw_item' .
                ' ON hardw_comp.component_hardware_bundle_config_id=hardw_item.component_hardware_bundle_config_id' .
                ' INNER JOIN hardware_supplier_products AS hsp' .
                ' ON hardw_item.hardware_supplier_product_id=hsp.hardware_supplier_product_id' .
                ' WHERE hsp.hardware_product_class_id IN (' . $strClasses . ')' .
                ' GROUP BY pt.intID, pt.strDisplayName ' .
                ' HAVING intNum=' . $intItemCount .
                ' ORDER BY intInterval';

            $resResult = mysql_query($strQuery, $dbhConnection)
            or report_error(__FILE__, __LINE__, mysql_error($dbhConnection));

            $intCount = mysql_num_rows($resResult);

            switch ($intCount) {
                case 1:
                    $arrRetVal = mysql_fetch_assoc($resResult);

                    break;

                case 0:
                    // Can't find an equivalent account type!
                    error_log(
                        "HardareRmaProcess::CalculateAccountTypeChange - " .
                        "Can't find a suitable account type to change to for type '$intCurrentTypeID', " .
                        "classes '$strClasses'"
                    );

                    $arrRetVal = array(
                        'intID'          => 0,
                        'strDisplayName' => 'No change'
                    );

                    break;

                default:
                    // Found multiple account types
                    // Hacky, but I see no other way... Look for the type
                    // whose service definition ID is closest to the
                    // account's current service_definition_id.
                    // This is already fetched as the first ro as a result
                    // of the intInterval field returned in the second
                    // query.
                    $arrRetVal = mysql_fetch_assoc($resResult);

                    error_log(
                        "HardareRmaProcess::CalculateAccountTypeChange - " .
                        "Found multiple account types to change to for type '$intCurrentTypeID', " .
                        "classes '$strClasses'. Assuming type '{$arrRetVal['intID']}'."
                    );

                    break;
            }

            mysql_free_result($resResult);

            $strDrop = 'DROP TABLE tblPossibleTypes';

            mysql_query($strDrop, $dbhConnection)
            or report_error(__FILE__, __LINE__, mysql_error($dbhConnection));
        } else {
            $arrRetVal = array(
                'intID'          => 0,
                'strDisplayName' => 'No change'
            );
        }

        return $arrRetVal;

    } // function CalculateAccountTypeChange


    /**
     * Returns true of the RMA is like for like (i.e. all valuable items are of the same class)
     *
     * @return boolean
     */
    function IsLikeForLike()
    {
        // So, is the customer sending everything valuable back?
        $recItemChanges = $this->CalculateItemChanges();
        $arrSendBackItems = $recItemChanges['Remove'];
        $objOriginalBundle = $this->GetOriginalBundle();
        $arrActualItems = $objOriginalBundle->GetItems();
        $arrAddItems = $recItemChanges['Add'];

        $bolAreSendingEverythingBack = true;

        $arrValuableSendBacks = array();

        foreach ($arrActualItems as $recItem) {

            if ($recItem['intIsValuable'] == false) {
                // Ignore it as it's not important
                continue;
            }
            if ('tv_box' === $recItem['intHardwareProductClassTag']) {
                continue;
            }
            // Are we sending it back?
            $intClassID = $recItem['intHardwareProductClassID'];

            $bolAreSendingBack = false;

            foreach ($arrSendBackItems as $intIndex => $recSendBackItem) {
                if ($recSendBackItem['intHardwareProductClassID'] == $intClassID) {
                    unset($arrSendBackItems[$intIndex]);

                    $arrValuableSendBacks[] = $recSendBackItem;

                    $bolAreSendingBack = true;

                    break;
                }
            }
            if ($bolAreSendingBack == false) {
                $bolAreSendingEverythingBack = false;

                break;
            }
        }

        if ($bolAreSendingEverythingBack === true) {
            // Is the customer getting the same as he's sending back?
            // Go through the things he's getting
            $bolIsGettingSameAsSendingBack = true;

            foreach ($arrAddItems as $recAddItem) {
                if ($recAddItem['intIsValuable'] == false) {
                    // Ignore it as it's not important
                    continue;
                }

                $intClassID = $recAddItem['intHardwareProductClassID'];

                $bolItemMatches = false;

                // Look at what he's sending back
                foreach ($arrValuableSendBacks as $intIndex => $recValuableSendBack) {
                    if ($recValuableSendBack['intHardwareProductClassID'] == $intClassID) {
                        unset($arrValuableSendBacks[$intIndex]);

                        $bolItemMatches = true;

                        break;
                    }
                }

                if ($bolItemMatches == false) {
                    $bolIsGettingSameAsSendingBack = false;

                    break;
                }
            }

            // Do we have any sent-back valuable items that haven't been
            // accounted for in the items that are being re-ordered?
            if (count($arrValuableSendBacks) > 0) {
                $bolIsGettingSameAsSendingBack = false;
            }

            if ($bolIsGettingSameAsSendingBack == true) {
                $bolIsLikeForLike = true;
            } else {
                $bolIsLikeForLike = false;
            }
        } else {
            $bolIsLikeForLike = false;
        }

        return $bolIsLikeForLike;

    } // function IsLikeForLike


    /**
     * Get the type of the RMA. Pretty complicated calculation as it happens!
     *
     * @return array
     */
    function GetRmaType()
    {
        if ($this->IsLikeForLike() === true || ($this->m_intManualRma == 1 && count($this->m_arrRmaItems) > 0)) {
            $strTypeHandle = 'Exchange';
        } else {
            $strTypeHandle = 'Credit';
        }

        // Get the record from the database
        $dbhConnection = get_named_connection('product');

        $intSupplierID = $this->GetSupplierID();

        $strQuery
            = 'SELECT hrt.usiHardwareRmaTypeID,' .
            ' hrt.vchHandle       AS strHandle,' .
            ' hrt.vchDisplayName  AS strDisplayName,' .
            ' hsrt.vchCode        AS strCode' .
            ' FROM vblHardwareRmaType  AS hrt' .
            ' INNER JOIN tblHardwSuppRmaType AS hsrt' .
            ' ON hrt.usiHardwareRmaTypeID=hsrt.usiHardwareRmaTypeID' .
            ' WHERE hrt.vchHandle=\'' . $strTypeHandle . '\'' .
            ' AND usiHardwareSupplierID = ' . $intSupplierID;

        $resResult = mysql_query($strQuery, $dbhConnection)
        or report_error(__FILE__, __LINE__, mysql_error($dbhConnection));

        $recReturn = mysql_fetch_assoc($resResult);

        mysql_free_result($resResult);

        return $recReturn;

    } // function GetRmaType

    /**
     * GetReasonCode
     *
     * @return void
     */
    function GetReasonCode()
    {
        if ($this->m_strReasonCode == '') {
            $dbhConnection = get_named_connection('product');

            $intSuplierID = $this->GetSupplierID();

            $strQuery = 'SELECT vchCode AS strCode ' .
                'FROM tblHardwareSupplierRmaReason ' .
                "WHERE usiHardwareRmaReasonID='{$this->m_usiReasonID}' " .
                "AND usiHardwareSupplierID='$intSuplierID'";

            $resResult = mysql_query($strQuery, $dbhConnection)
            or report_error(__FILE__, __LINE__, mysql_error($dbhConnection));

            $this->m_strReasonCode = mysql_result($resResult, 0, 0);
        }

        return $this->m_strReasonCode;
    }


    /**
     * GetOriginalOrderCode
     *
     * @return void
     */
    function GetOriginalOrderCode()
    {
        $objBundle = &$this->GetOriginalBundle();

        return $objBundle->GetOrderNumber();
    }


    /**
     * GetRmaCount
     *
     * @return number
     */
    function GetRmaCount()
    {
        $intRmaCount = 1;

        $dbhConnection = get_named_connection('userdata');

        $intBundleID = $this->m_objOriginalBundle->GetBundleID();

        // Keep looping back along the chain until we reach the end.
        while ($intBundleID !== false) {
            $strSelect = 'SELECT usiConfigHardwareBundleID ' .
                'FROM tblHardwareRmaProcess ' .
                "WHERE usiReplacementHardwareBundleID='$intBundleID'";

            $resResult = mysql_query($strSelect, $dbhConnection)
            or report_error(__FILE__, __LINE__, mysql_error($dbhConnection));

            if (mysql_num_rows($resResult) > 0) {
                ++$intRmaCount;

                $intBundleID = mysql_result($resResult, 0, 0);
            } else {
                $intBundleID = false;
            }

            mysql_free_result($resResult);
        }

        return $intRmaCount;

    } // function GetRmaCount


    /**
     * GetOptionNames
     *
     * @return multitype:|multitype:unknown
     */
    function GetOptionNames()
    {
        if (count($this->m_arrOptions) == 0) {
            return array();
        }

        // Get the names of the options applied to this RMA
        $dbhConnection = get_named_connection('product');

        $strOptions = implode(',', $this->m_arrOptions);

        $strQuery = 'SELECT vchDisplayName AS strDisplayName ' .
            'FROM tblHardwareRmaOption ' .
            "WHERE usiHardwareRmaOptionID IN ($strOptions)";

        $resResult = mysql_query($strQuery, $dbhConnection)
        or report_error(__FILE__, __LINE__, mysql_error($dbhConnection));

        $arrOptionNames = array();

        while ($recRecord = mysql_fetch_assoc($resResult)) {
            $arrOptionNames[] = $recRecord;
        }

        return $arrOptionNames;

    } // function GetOptionNames


    /**
     * GetDirectionID
     *
     * @param string $strHandle Handle
     *
     * @return int
     */
    function GetDirectionID($strHandle)
    {
        $strHandle = addslashes($strHandle);

        $dbhConnection = get_named_connection('product');

        $strQuery = 'SELECT usiHardwRmaOptDirectionID ' .
            'FROM vblHardwRmaOptDirection ' .
            "WHERE vchHandle='$strHandle'";

        $resResult = mysql_query($strQuery, $dbhConnection)
        or report_error(__FILE__, __LINE__, mysql_error($dbhConnection));

        return mysql_result($resResult, 0, 0);
    }

    /**
     * GetSummaryData
     *
     * @return multitype:string multitype: number
     */
    function GetSummaryData()
    {
        return array(
            'strRmaStart'             => $this->m_strRmaStart,
            'strRmaEnd'               => $this->m_strRmaEnd,
            'arrOptions'              => $this->m_arrOptions,
            'strRmaTypeDisplayName'   => $this->m_strTypeDisplayName,
            'strRmaReasonDisplayName' => $this->m_strReasonDisplayName,
            'strRmaStatusDisplayName' => $this->m_strStatusDisplayName
        );
    }

    /**
     * Returns true if the RMA should be returned to the supplier, false if
     * it should just come back to Plusnet.
     *
     * @return boolean
     */
    function IsReturnToSupplier()
    {
        $arrItemChanges = $this->CalculateItemChanges();

        $bolReturnToSupplier = false;

        foreach ($arrItemChanges['Remove'] as $recItem) {
            if ($recItem['intIsValuable'] == true) {
                $bolReturnToSupplier = true;
            }
        }

        return $bolReturnToSupplier;
    }

    /**
     * Get the address that the RMA should be returned to
     *
     * @return void|string
     */
    function GetReturnsAddress()
    {
        // OK, I'm doing this in a slightly (ok very) hacky way, but I'm
        // running out of time. Also, this is a class method so it can be
        // made nicer at a later date without client code being affected as
        // long as the funcitonality of the function doesn't change.

        // First check to see that we're not in a situation where the
        // hardware needs to be returned to us instead of the supplier.

        if ($this->IsReturnToSupplier() === true) {
            return $this->GetSupplierReturnsAddress();
        } else {
            // HACK!
            return "Plusnet plc\n" .
                "The Balance\n" .
                "2 Pinfold Street\n" .
                "Sheffield\n" .
                "South Yorkshire\n" .
                "S1 2GU";
        }
    }


    /**
     * GetSupplierReturnsAddress
     *
     * @return void
     */
    function GetSupplierReturnsAddress()
    {
        report_error(__FILE__, __LINE__, 'HardwareRmaProcess::GetSupplierReturnsAddress must be overridden!');
    }


    /**
     * GetComment
     *
     * @return string
     */
    function GetComment()
    {
        return $this->m_strComment;
    }

    /**
     * Get a list of actual item changes that have been applied as a result
     * of this RMA.
     *
     * @return multitype:multitype:
     */
    function GetActualItemChanges()
    {
        return array(
            'Removed' => $this->m_arrRemovedItems,
            'Added'   => $this->m_arrAddedItems
        );
    }


    /**
     * GetOriginalBundle
     *
     * @return boolean|null
     */
    function GetOriginalBundle()
    {
        if ($this->m_objOriginalBundle == null) {
            if ($this->m_intOriginalBundleID == 0) {
                return false;
            } else {
                $strClassName = $this->GetClassPrefix() . 'HardwareBundle';

                $this->m_objOriginalBundle = new $strClassName();

                $this->m_objOriginalBundle->GetByHardwareID($this->m_intOriginalBundleID);
            }
        }

        return $this->m_objOriginalBundle;
    }


    /**
     * GetReplacementBundle
     *
     * @return boolean|NULL
     */
    function GetReplacementBundle()
    {
        if ($this->m_objReplacementBundle == null) {
            if ($this->m_intReplacementBundleID == 0) {
                return false;
            } else {
                $strClassName = $this->GetClassPrefix() . 'HardwareBundle';

                $this->m_objReplacementBundle = new $strClassName();

                $this->m_objReplacementBundle->GetByHardwareID($this->m_intReplacementBundleID);
            }
        }

        return $this->m_objReplacementBundle;
    }


    /**
     * GetWhenReturned
     *
     * @return string
     */
    function GetWhenReturned()
    {
        return $this->m_strRmaEnd;
    }


    //
    // HardwareRmaProcess :: Set Functions
    //
    /**
     * SetOriginalBundle
     *
     * @param object &$objBundle Bundle
     *
     * @return void
     */
    function SetOriginalBundle(&$objBundle)
    {
        $this->m_objOriginalBundle = &$objBundle;
    }

    /**
     * AddOption
     *
     * @param int $intOptionID OptionID
     *
     * @return void
     */
    function AddOption($intOptionID)
    {
        $this->m_arrOptions[] = addslashes($intOptionID);

        $this->MarkModified();
    }

    /**
     * SetReason
     *
     * @param int $intReasonID ReasonID
     *
     * @return void
     */
    function SetReason($intReasonID)
    {
        $this->m_usiReasonID = addslashes($intReasonID);

        $this->MarkModified();
    }


    /**
     * SetComment
     *
     * @param string $strComment Comment
     *
     * @return void
     */
    function SetComment($strComment)
    {
        $this->m_strComment = addslashes($strComment);

        $this->MarkModified();
    }

    /**
     * SetWhenCompleted
     *
     * @param string $strWhenCompletedDate WhenCompletedDate
     *
     * @return void
     */
    function SetWhenCompleted($strWhenCompletedDate)
    {
        $this->m_strRmaEnd = $strWhenCompletedDate;

        $this->MarkModified();
    }

    /**
     * SetStatusTag
     *
     * @param string $strNewStatusTag NewStatusTag
     *
     * @return void
     */
    function SetStatusTag($strNewStatusTag)
    {
        if ($this->m_strStatusHandle != $strNewStatusTag) {
            $this->m_strStatusHandle = $strNewStatusTag;

            $this->MarkModified();
        }
    }

    /**
     * SetRMAItems
     *
     * @param array $arrRMAItems RMAItems
     *
     * @return void
     */
    function SetRMAItems($arrRMAItems)
    {
        foreach ($arrRMAItems as $item) {
            $this->m_arrRmaItems[$item]++;
        }
    }

    /**
     * SetManualRma
     *
     * @param int $m_intManualRma ManualRma
     *
     * @return void
     */
    function SetManualRma($m_intManualRma)
    {
        $this->m_intManualRma = $m_intManualRma;
    }

    //
    // HardwareRmaProcess :: Action Functions
    //

    /**
     * Commit the RMA object to the database
     *
     * @return void
     */
    function Commit()
    {
        // First see if the object needs committing
        if ($this->IsModified() == false) {
            // This isn't an error: the object is already in sync!
            return true;
        }

        // First, is this object tied to a configuration already in the database?
        if ($this->m_intID == 0) {
            // No, we need to add a new configuration
            // Start by inserting the RMA Process entry
            $intBundleID = $this->m_objOriginalBundle->GetBundleID();
            $recType = $this->GetRmaType();
            $intTypeID = $recType['usiHardwareRmaTypeID'];
            $recStatus = HardwareBundleControl::GetStatus('rma_in_progress');
            $intStatusID = $recStatus['intStatus'];
            //actionerId added to record user when manually ordering RMA
            $actionerId = isset($GLOBALS['my_id']) ? $GLOBALS['my_id'] : SCRIPT_USER;

            // problem fix 38057 - dbargiel
            if (intval($intTypeID) < 1) {
                ob_start();
                $strErrorMessage = ob_get_clean();
                ob_end_clean();
                report_error(
                    __FILE__, __LINE__,
                    'Please see problem 38057 for details. Record details from HardwareRmaProcess::GetRmaType() ' .
                    $strErrorMessage
                );
            }
            // end problem fix 38057

            $strInsert = 'INSERT INTO tblHardwareRmaProcess ( ' .
                ' usiConfigHardwareBundleID, usiReplacementHardwareBundleID, ' .
                ' usiHardwareRmaReasonID, usiHardwareRmaTypeID, ' .
                ' usiHardwareOrderStatusID, txtComment, dtmStart, vchActionerId) ' .
                'VALUES ( ' .
                " '$intBundleID', '0', " .
                " '{$this->m_usiReasonID}', '$intTypeID', " .
                " '$intStatusID', '{$this->m_strComment}', NOW(), '$actionerId' )";


            $dbhConnection = get_named_connection('userdata');
            mysql_query($strInsert, $dbhConnection)
            or report_error(__FILE__, __LINE__, mysql_error($dbhConnection));

            $intID = mysql_insert_id($dbhConnection);

            //
            // Items
            //
            $recItemChanges = $this->CalculateItemChanges();
            $intRemoveDirectionID = $this->GetDirectionID('Remove');
            $intAddDirectionID = $this->GetDirectionID('Add');

            $arrItemsBeforeRemoving = $this->m_objOriginalBundle->GetItems();
            $intCurBundleItemCount = count($arrItemsBeforeRemoving);
            foreach ($recItemChanges['Remove'] as $recItem) {
                // Go through the items the bundle had before any were added
                for ($intCurItem = 0; $intCurItem < $intCurBundleItemCount; ++$intCurItem) {
                    if ($arrItemsBeforeRemoving[$intCurItem]['intID'] == $recItem['intItemToRemoveID']) {
                        $this->m_objOriginalBundle->SetStatusTag($intCurItem, 'rma_in_progress');

                        $dbhConnection = get_named_connection('userdata');

                        $intRemovedItemID = $arrItemsBeforeRemoving[$intCurItem]['intID'];

                        $arrRemovedItems[] = $arrItemsBeforeRemoving[$intCurItem];
                        $pos = count($arrRemovedItems) - 1;
                        $arrRemovedItems[$pos]['intQuantity'] = $recItem['intQuantity'];

                        $strInsert = 'INSERT INTO tblHardwRmaProcessItem ( ' .
                            ' usiHardwareRmaProcessID, ' .
                            ' usiHardwRmaOptDirectionID, ' .
                            ' usiHardwareBundleItemID ) ' .
                            'VALUES ( ' .
                            " '$intID', " .
                            " '$intRemoveDirectionID', " .
                            " '$intRemovedItemID' )";
                        mysql_query($strInsert, $dbhConnection)
                        or report_error(__FILE__, __LINE__, mysql_error($dbhConnection));
                    }
                }
            }

            //ensure that the items removed has the correct Quantity
            $this->m_arrRemovedItems = $arrRemovedItems;

            // Do we need to add a new bundle?
            $recRmaType = $this->GetRmaType();
            $arrItemsBeforeAdding = $this->m_objOriginalBundle->GetItems();
            $arrAddedItems = array();
            foreach ($recItemChanges['Add'] as $intIndex => $recAddedItem) {
                // returns bag doesn't have an id at this stage, it gets added later anyway so skip it.
                if (isset($recAddedItem['intItemToAddID'])) {
                    foreach ($arrItemsBeforeAdding as $existingItem) {
                        if ($existingItem['intID'] == $recAddedItem['intItemToAddID']) {
                            $itemToAdd = $existingItem;
                            if (isset($recAddedItem['substitutionItem']) && $recAddedItem['substitutionItem'])  {
                                $itemToAdd['intSupplierProductID'] = $recAddedItem['intHardwareSupplierProductID'];
                                $itemToAdd['strDisplayName'] = $recAddedItem['strDisplayName'];
                            }

                            $arrAddedItems[] = $itemToAdd;
                        }
                    }
                }
            }

            $this->m_arrAddedItems = $arrAddedItems;

            //
            // Options
            //
            $dbhConnection = get_named_connection('userdata');

            foreach ($this->m_arrOptions as $intOptionID) {
                $strInsert = 'INSERT INTO tblHardwRmaProcessOption ( ' .
                    ' usiHardwareRmaProcessID, usiHardwareRmaOptionID ) ' .
                    'VALUES ( ' .
                    " '$intID', '{$intOptionID}' )";
                mysql_query($strInsert, $dbhConnection)
                or report_error(__FILE__, __LINE__, mysql_error($dbhConnection));
            }

            // Change to overall status of the original bundle
            $this->m_objOriginalBundle->SetOverallStatusTag('rma_in_progress', false);

            // Commit changes to the original bundle
            // Now sends array of items to change to ensure that only these item status details are updated
            //If this is a credit RMA, do not pass items for RMA to initiate ordering of credit bag
            if ($this->m_arrOptions[0] == 1) {
                $this->m_objOriginalBundle->Commit();
            } else {
                //Else, pass items that require RMAing
                $this->m_objOriginalBundle->Commit($this->m_arrAddedItems);
            }

            // Set up the object
            $this->m_intID = $intID;
            // Mark the object as being back in sync
            $this->MarkUpToDate();
        } else {
            // Update the RMA Process
            $recStatus = HardwareBundleControl::GetStatus($this->m_strStatusHandle);
            $dbhConnection = get_named_connection('userdata');

            $strComment = addslashes($this->m_strComment);

            $strUpdate = 'UPDATE tblHardwareRmaProcess ' .
                "SET usiConfigHardwareBundleID='{$this->m_intOriginalBundleID}', " .
                " usiReplacementHardwareBundleID='{$this->m_intReplacementBundleID}', " .
                " usiHardwareRmaReasonID='{$this->m_usiReasonID}', " .
                " usiHardwareRmaTypeID='{$this->m_usiTypeID}', " .
                " usiHardwareOrderStatusID='{$recStatus['intStatus']}', " .
                " txtComment='{$strComment}', " .
                " dtmStart='{$this->m_strRmaStart}', " .
                " dtmEnd='{$this->m_strRmaEnd}' " .
                "WHERE usiHardwareRmaProcessID='{$this->m_intID}'";
            mysql_query($strUpdate, $dbhConnection)
            or report_error(__FILE__, __LINE__, mysql_error($dbhConnection));
            // Mark the object as being back in sync
            $this->MarkUpToDate();
        }

        return true;

    } // function Commit()


    /**
     * InitiateRma
     *
     * @param int $intComponentID ComponentID
     *
     * @return void
     */
    function InitiateRma($intComponentID = 0)
    {
        // Get info
        $recService = $this->m_objOriginalBundle->GetService();
        $intServiceID = $recService['service_id'];
        $intCurTypeID = $recService['type'];
        $strNextInvoice = $recService['next_invoice'];
        $strInvoicePeriod = $recService['invoice_period'];
        $recUser = userdata_user_get($recService['user_id']);
        $strCustomerEmail = $recUser['email'];
        $recVispConfig = product_visp_config_get($recService['isp']);
        $strPortalUrl = $recVispConfig['portal_main_page_url'];

        // Add a contact to the account
        $strBody = "Dear Customer,\n" .
            "\n" .
            "Our suppliers have now generated the RMA (Return Merchandise Authorization) code which you " .
            "require in order to return your hardware.\n\n" .
            'Your RMA Number is: RMA ' . $this->GetRmaCode() . "\n" .
            "In order to return your hardware, you should mark this number clearly on the outside of all " .
            "packaging (Care should be taken to prevent " .
            "defacing the original packaging). " .
            "The item should then be returned to the following address :\n" .
            "\n" . $this->GetReturnsAddress() . "\n" .
            "Important Notes:\n\n" .
            "* Your RMA code should be written clearly on the side of the parcel but not on the original box " .
            "that the hardware was delivered in. " .
            "Failure to do so will delay the processing of your return.\n\n" .
            "* You must include all original items when returning your hardware, " .
            "including manuals, CDs power supplies etc. Microfilters do not need to be included except where " .
            "the equipment is being returned for a credit (Only availble within 7 days of delivery).\n\n" .
            "* Once the warehouse receive your RMA package, " .
            "they will prioritise the dispatch of your replacement equipment.\n\n" .
            'With Regards, ' . $strPortalUrl . "\n";

        $this->m_objOriginalBundle->AddContact($strBody);

        // Change account type
        $recNewTypeData = $this->CalculateAccountTypeChange();

        $intNewTypeID = $recNewTypeData['intID'];

        if ($intNewTypeID != 0) {
            userdata_service_set_type($intServiceID, $intNewTypeID);

            // Log the type change
            userdata_service_log_type_change($intServiceID, $intCurTypeID, $intNewTypeID, $strNextInvoice);
        }

        // Email the customer information on the starter pack section of the portal
        $objEmailTemplate = new CTemplateEngine();

        $objEmailTemplate->pubSetFile(
            '/local/data/mis/database/application_apis/templates/' .
            'emails/hardware_bundle_insight_rma_initiate_inform_customer_email.pnt'
        );
        //don't use the postmaster@... address for metronet users!
        if ($recService['isp'] != 'metronet') {
            $strEmailAddress = str_replace(
                'anythingyoulike', 'postmaster', $recVispConfig['default_customer_email_address']
            );
            $strEmailAddress = str_replace('username', $recService['username'], $strEmailAddress);
            $strEmailAddress = "$strEmailAddress,$strCustomerEmail";
        } else {
            $strEmailAddress = $recUser['email'];
        }

        //Get the date the hardware was dispatched
        $objHardwareBundle = component_hardware_bundle_get_object($intComponentID);
        $intTimeSinceDispatch = $objHardwareBundle->GetTimeSinceDispatch();
        $dtmCurrentTime = mktime();
        $dtmTimeOfDispatch = $dtmCurrentTime - $intTimeSinceDispatch;

        //Find out 11 months after this period
        $arrHardwareShippedDate = getdate($dtmTimeOfDispatch);

        $intYear = $arrHardwareShippedDate['year'];
        $intMonth = $arrHardwareShippedDate['mon'] + 11;
        $intDay = $arrHardwareShippedDate['mday'];

        $strGuaranteeDate = "$intYear-$intMonth-$intDay 00:00:00";
        $dtmGuaranteeDate = strtotime($strGuaranteeDate);

        $strGuaranteeDateDisplay = date('jS F Y', $dtmGuaranteeDate);

        //If the hardware was shipped over 11 months ago, add the extra text regarding the guarantee period
        if ($dtmGuaranteeDate < $dtmCurrentTime) {
            $bolGuarenteePeriod = true;
        } else {
            $bolGuarenteePeriod = false;
        }

        $recTemplateData = array(
            'strPortalUrl'       => $strPortalUrl,
            'strUsername'        => $recService['username'],
            'strFullName'        => "{$recUser['forenames']} {$recUser['surname']}",
            'strVispName'        => $recVispConfig['full_name'],
            'bolGuarenteePeriod' => $bolGuarenteePeriod,
            'strGuarenteePeriod' => $strGuaranteeDateDisplay
        );

        $objEmailTemplate->pubSetData($recTemplateData);

        $strMailBody = $objEmailTemplate->pubRun();

        // Send the mail to Insight's customer support department

        mail(
            $strEmailAddress,
            'Confirmation of ADSL Starter Pack RMA', $strMailBody,
            "From: <EMAIL>\nReturn-path: <EMAIL>"
        );

        // Actually raise the RMA with the suppler if we need to
        if ($this->IsReturnToSupplier() === true) {
            $this->SendRmaRequest();
        }

        //  We need to add the hardware back to our stock list
        $arrAccountHardware = adslGetAccountHardware($intCurTypeID);
        if (is_array($arrAccountHardware) && isset($arrAccountHardware['service_component_id'])) {
            adslHardwareStockLevelAlter($arrAccountHardware['service_component_id'], '+');
        }

    } // function Initiate

    /**
     * PollForUpdate
     *
     * @return boolean
     */
    function PollForUpdate()
    {
        $strClassName = $this->m_strClassPrefix . 'HardwareBundleSupplier';

        // Find out what the current status of the RMA is from the supplier
        $Supplier = new $strClassName();

        $arrStatus = $Supplier->FetchLatestRmaStatus($this);
        $strWhenCompleted = isset($arrStatus['rma_complete_date']) ? $arrStatus['rma_complete_date'] : false;

        if ($strWhenCompleted === false) {
            // We shouldn't be here. All replies from the supplier should be recognisable, so we return false. Other
            // code will handle this as though the supplier just didn't reply.
            // DJM: 16/10/2012: error logging removed as it was generating too much output in the error log
            // As per above, this error condition will be handled elsewhere in the code

            return false;
        } elseif (strlen($strWhenCompleted) > 0) {
            // Update the object
            $this->SetStatusTag('dispatched');

            // Update the original bundle
            $objOriginalBundle = &$this->GetOriginalBundle();

            $objOriginalBundle->SetOverallStatusTag('dispatched', false);

            // Need to set the individual items
            foreach ($objOriginalBundle->GetItems() as $intItemIndex => $recItem) {
                if ($recItem['strStatusTag'] == 'rma_in_progress'
                    || $recItem['strStatusTag'] == 'awaiting_processing'
                    || $recItem['strStatusTag'] == 'dispatched' || $recItem['strStatusTag'] == 'delivered'
                ) {
                    $objOriginalBundle->SetStatusTag($intItemIndex, 'dispatched');

                }
            }//End of RMA - update item status

            $this->SetWhenCompleted($strWhenCompleted);

            // Save the new info
            $binResult = $this->Commit();

            $objOriginalBundle->Commit();

            $objOriginalBundle->updateTr069Details($arrStatus);

            $objOriginalBundle->AddContact("RMA Received.");

            // Is there a new order to complete? If so kick it off.
            $objReplacementBundle = $this->GetReplacementBundle();

            if ($objReplacementBundle !== false) {
                // Kick off order
                $objReplacementBundle->PlaceOrder();
            }
        } else {
            // We were unable to get a reply from the supplier. Not
            // unexpected, so just return false silently.
            return false;
        }

    } // function PollForUpdate

    /**
     * get_correct_returns_bag
     *
     * @param int $product_id product id
     *
     * @return array
     */
    function get_correct_returns_bag($product_id)
    {
        // Look the product bags up in the hardware_product_returns_link table
        $sql = "select hprl.credit_bag,
                   hprl.return_bag
            from hardware_product_returns_link hprl
            join hardware_supplier_products hsp
                on hsp.hardware_supplier_product_id = hprl.hardware_supplier_product_id
            where hsp.hardware_supplier_product_id = {$product_id}";
        $dbh = get_named_connection('product');
        $res = mysql_query($sql, $dbh);
        $row = mysql_fetch_row($res);
        $bags = array();
        if ($row) {
            // convert into words
            foreach ($row as $bag) {
                $sql = "select tag from hardware_supplier_products
                    where product_code = $bag";
                $res = mysql_query($sql, $dbh);
                $bags[] = mysql_fetch_row($res);
            }
        }

        return $bags;
    }

} // class HardwareRmaProcess
