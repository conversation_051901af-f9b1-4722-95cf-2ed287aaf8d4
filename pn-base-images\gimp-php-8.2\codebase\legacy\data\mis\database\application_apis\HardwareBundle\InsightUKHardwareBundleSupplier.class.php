<?php
	class InsightUKHardwareBundleSupplier extends HardwareBundleSupplier
	{
		// Private member variables
		var $m_strClassPrefix         = 'InsightUK';
		var $m_strSupplierTag         = 'insightuk';
		var $m_strEDISegmentDelimiter = "\n";
		var $m_strEDIElementDelimiter = '~';

		var $m_strFTPServerName       = '';
		var $m_strFTPUsername         = '';
		var $m_strFTPPassword         = '';
		var $m_strFTPInDirectory      = '';
		var $m_strFTPOutDirectory     = '';
		var $m_strWDDXSiteURL         = '';

		// Constructor
		function InsightUKHardwareBundleSupplier()
		{
			$this->HardwareBundleSupplier();

			// Populate the server connection variables
			$this->m_strFTPServerName       = HARDWARE_BUNDLE_INSIGHTUK_FTP_SERVER;
			$this->m_strFTPUsername         = HARDWARE_BUNDLE_INSIGHTUK_FTP_USERNAME;
			$this->m_strFTPPassword         = HARDWARE_BUNDLE_INSIGHTUK_FTP_PASSWORD;
			$this->m_strFTPInDirectory      = HARDWARE_BUNDLE_INSIGHTUK_FTP_INDIR;
			$this->m_strFTPOutDirectory     = HARDWARE_BUNDLE_INSIGHTUK_FTP_OUTDIR;
			$this->m_strWDDXSiteURL         = HARDWARE_BUNDLE_INSIGHTUK_WDDX_URL;

			// Just check that we're set up
			if ($this->m_strFTPServerName == '')
			{
				report_error(__FILE__, __LINE__, 'Server configuration details for Insight Hardware aren\'t defined!!!!');
			}
		}


		// Send bulk orders to Insight
		function SendOrders($arrHardwareBundles)
		{
			// Generate the order strings
			$arrOrderStrings = array();

			foreach ($arrHardwareBundles as $HardwareBundle)
			{
				$arrOrderStrings[] = $HardwareBundle->GenerateOrderString();
			}

			// Collate individual strings into one string
			$strOrderString = implode("\n", $arrOrderStrings);

			// Place the order
			if ($this->PlaceOrder($strOrderString) == true)
			{
				// Update the bundles
				foreach ($arrHardwareBundles as $HardwareBundle)
				{
					$HardwareBundle->SetWhenOrdered();
					$HardwareBundle->SetStatusTag(-1, 'awaiting_processing');

					// Commit to the database
					if ($HardwareBundle->Commit() == true)
					{
						$HardwareBundle->AddContact('Order placed for hardware bundle');
					}
					else
					{
						error_log("InsightUKHardwareBundleSupplier::SendOrders - The following Hardware Bundle object did not Commit() for some reason:\n " . dump_structure_to_string($HardwareBundle));
					}
				}

				return true;
			}
			else
			{
				return false;
			}

		} // function SendOrders


		// Place a hardware order
		function PlaceOrder($strOrderString)
		{
			// Create a temporary file
			$strTempFileName        = tempnam('/tmp', 'insight_hardware') . '.csv';
			$strDestinationFileName = basename($strTempFileName);

			$hTempFile = fopen($strTempFileName, 'w');

			// Put the order string into the temp file
			fputs($hTempFile, $strOrderString);

			// Close the temp file (We're done with putting things in it).
			fclose($hTempFile);

			// Connect to the FTP server.
			$hFTPServer = ftp_connect($this->m_strFTPServerName)
				or report_error(__FILE__, __LINE__, "Couldn't connect to Insight ftp server to order hardware");

			// Login to the FTP server
			$hLoginResult = ftp_login($hFTPServer, $this->m_strFTPUsername, $this->m_strFTPPassword)
				or report_error(__FILE__, __LINE__, "Couldn't log in to Insight ftp server to order hardware");

			// Turn passive mode off
			ftp_pasv($hFTPServer, true)
				or report_error(__FILE__, __LINE__, "Couldn't change to passive mode on Insight ftp server to order hardware");

			// Move to the destination directory
			ftp_chdir($hFTPServer, $this->m_strFTPInDirectory)
				or report_error(__FILE__, __LINE__, "Couldn't change directory on Insight ftp server to order hardware");

			// Upload the file
			$binUploaded = ftp_put($hFTPServer, $strDestinationFileName, $strTempFileName, FTP_ASCII)
				or report_error(__FILE__, __LINE__, "Couldn't upload file to Insight ftp server to order hardware");

			// Close the FTP connection
			ftp_quit($hFTPServer);

			// Delete the temp file
			unlink($strTempFileName);

			// Replace the pipes with newlines because mail clients appeared
			// not to like the format of the string
			mail('<EMAIL>', 'Hardware Order Placed', str_replace('|', "\n", $strOrderString));

			return true;

		} // function PlaceOrder($strOrderString)


		function PollForOrderAcknowledgements()
		{
			// Look in the shared directory containing 'split' files created by
			// hardware_bundles_poll_for_insight_order_acknowledgements.php
			// belonging to this partner.

			// Create directory object
			$intPartnerID = $this->GetPartnerID();

			$strDirname = HARDWARE_BUNDLE_INSIGHTUK_SPLIT_DIR . "/$intPartnerID/";

			$Dir = dir($strDirname);

			while (($strFilename = $Dir->read()) !== false)
			{
				$strFullFilename = $strDirname . $strFilename;

				// Ignore the directory pointers.
				if ($strFilename[0] == '.')
				{
					continue;
				}

				$recEDIData = $this->DecodeEDI($this->ReadInFile($strFullFilename));

				//If there any errors return them
				if(isset($recEDIData['arrErrors']) && count($recEDIData['arrErrors']) > 0)
				{
					//Add the filename in question
					$recEDIData['strFileName'] = $strFullFilename;

					return $recEDIData;
				}


				if (count($recEDIData) == 0)
				{
					// Something has gone wrong, so don't do any more processing
					// on this
					$arrErrors[] = 'Count recEDIData == 0';

					//Add the filename in question
					$recEDIData['strFileName'] = $strFullFilename;

					return $arrErrors;
				}

				// Get the hardware bundle
				$strClassName = $this->m_strClassPrefix . 'HardwareBundle';

				// How many records are included in this file?
				foreach ($recEDIData['arrRecords'] as $rEDIRecord)
				{
					$Bundle = new $strClassName();

					$Bundle->GetByHardwareID($rEDIRecord['intHardwareID']);

					// Make sure we got a valid bundle
					if ($Bundle->IsValid() == false)
					{
						error_log("InsightUKHardwareBundleSupplier::PollForOrderAcknowledgements - Couldn't get a hardware bundle object for Hardware ID '{$rEDIRecord['intHardwareID']}'");

						// Go to the next record
						continue;
					}

					// Are we looking at an acknowledgement or rejection?
					switch ($rEDIRecord['strAckType'])
					{
						case 'AK':
						case 'AD':
						case 'AT':
							// Acknowledged
							// Set new items in the object
							$Bundle->SetOrderNumber(-1, $rEDIRecord['strOrderNumber']);
							$Bundle->SetStatusTag(-1, 'awaiting_dispatch');

							// Save the data to the database
							if ($Bundle->Commit() == true)
							{
								// Log a ticket
								$Bundle->AddContact('Hardware order acknowledged');
							}

							break;

						case 'RJ':
						case 'RD':
						case 'RT':
							// Rejected

							// Only change the status to 'rejected' if the
							// component's current status is 'awaiting_processing'
							if ($Bundle->GetStatusTag() == 'awaiting_processing')
							{
								$Bundle->SetStatusTag(-1, 'rejected');

								if ($Bundle->Commit() == true)
								{
									// Log a ticket
									$Bundle->AddContact('Hardware order rejected');
								}
							}
							else
							{
								// Ignore it. The order has already been
								// accepted and doing anything with this will
								// only cause tears.
							}

							break;

						default:
							error_log("InsightUKHardwareBundleSupplier::PollForOrderAcknowledgements - Unknown acknowledgement type '{$rEDIRecord['strAckType']}'");

							break;
					}

					// Clean up memory
					unset($Bundle);
				}

				// Delete the EDI file
				unlink($strFullFilename);

			} // foreach ($arrFiles as $strServerFilename)

			return $arrErrors;

		} // function PollForOrderAcknowledgements


		// Fetch the latest status for a hardware bundle - and the tracking order number
		function FetchLatestOrderStatus($HardwareBundle)
		{
			// Get the order number
			$strOrderNumber = $HardwareBundle->GetOrderNumber();

			// Set up the call
			$strSite = $this->m_strWDDXSiteURL . "orderstatus.2.php?orderno=$strOrderNumber";

			//$strCommand = "/usr/local/bin/curl $strSite";
			$strCommand = "/usr/bin/curl $strSite";

			$strOutput = exec($strCommand);

			// Make sure we got something back
			if ($strOutput == '')
			{
				return false;
			}

			$recResponse = wddx_deserialize($strOutput);

			$strResponseString = $recResponse['status'];

			//Get the hardware order tracking number - if set
			if(isset($recResponse['tracking']))
			{
				$strTrackingNumber = $recResponse['tracking'];
			}
			else
			{
				$strTrackingNumber = '';
			}

			$strStatusTag = $this->DecodeOrderStatusResponse($strResponseString);

			$recRetVal = array('strStatusTag' => $this->DecodeOrderStatusResponse($recResponse['status']));

			// Look for an invoice ID
			if(preg_match('`Invoice #:[\s]*([0-9]+)`', $strResponseString, $arrRegs) == true)
			{

				$recRetVal['strInvoiceNumber'] = $arrRegs[1];
			}

			//Remember to return the tracking number
			$recRetVal['strTrackingNumber'] = $strTrackingNumber;

			return $recRetVal;

		} // function FetchLatestOrderStatus



		/**
		 * Get the status of an RMA from Insight.
		 *
		 * @param HardwareRmaProcess $objRma
		 * @return array
		 */
		function FetchLatestRmaStatus($objRma)
		{
			// Get the order number
			$strRmaCode = $objRma->GetRmaCode();

			// Set up the call
			$strSite = $this->m_strWDDXSiteURL . "rmastatus.php?pono=$strRmaCode";

			$strCommand = "/usr/local/bin/curl $strSite";

			$strOutput = exec($strCommand);

			// Make sure we got something back
			if ($strOutput == '')
			{
				return false;
			}

			$recResponse = wddx_deserialize($strOutput);

			switch ($recResponse['status'])
			{
				case 'Error':
					// Nothing to report.
					$strRetVal = '';
					break;

				case 'OK':
					$strRetVal = $recResponse['invdate'];
					break;

				default:
					// Write an error to the error log, but this will only
					// happen if we don't have an RMA number to send.
					$strRetVal = false;

					error_log("InsightUKHardwareBundleSupplier::FetchLatestRmaStatus - Returned an error when trying to get an update on RMA $strRmaCode");
			}

			return array('rma_complete_date' => $strRetVal);

		} // function FetchLatestRmaStatus



	// Decode an EDI document
	function DecodeEDI($strDocument)
	{
		// Inform developer of the file we're looking at
		mail('<EMAIL>', 'Downloaded Hardware Ack file', $strDocument);

		//Default return
		$recEDIData = array('arrRecords' => array(),
		                    'arrErrors'  => array());

		// Explode on the segment delimiter
		$arrSegmentStrings = explode($this->m_strEDISegmentDelimiter, $strDocument);

		// Create a tree of the segments and elements

		$arrSegments = array();

		// While we're creating the array, we're going to find the document type
		$strDocType   = '';

		foreach ($arrSegmentStrings as $strSegment)
		{
			$arrElements = explode($this->m_strEDIElementDelimiter, $strSegment);

			$arrSegments[] = $arrElements;

			if ($strDocType == '')
			{
				foreach ($arrElements as $intIndex => $strElement)
				{
					if ($strElement == 'ST')
					{
						$strDocType = $arrElements[$intIndex + 1];
					}
				}
			}
		}

		// Now to do something with the document
		switch ($strDocType)
		{
			case '855':
				return $this->DecodeEDI855($arrSegments);
				break;

			default:
				error_log("Unknown EDI document type '$strDocType'");

				$recEDIData['arrErrors'][] =  "Unknown EDI document type '$strDocType'";
				return $recEDIData;
		}

	} // function DecodeEDI($strDocument)


	// Decode an 855 Document
	function DecodeEDI855($arrSegments)
	{
		$recEDIData = array('arrRecords' => array(),
		                    'arrErrors'  => array());

		$intCurRecord = 0;

		foreach ($arrSegments as $intSegmentIndex => $arrElements)
		{
			$strSegmentHeader = $arrElements[0];

			switch ($strSegmentHeader)
			{
				case 'ST':
					// Start of a section
					$intCurRecord = $arrElements[2];

					// Initialise the section
					$recEDIData['arrRecords'][$intCurRecord] = array();

					break;

				case 'SE':
					// End of a section
					$intCurRecord = 0;

					break;

				case 'ISA':
					// Are we in test mode?
					$recEDIData['strMode'] = $arrElements[15];

					// Date of the interchange
					$recEDIData['strDocDate'] = $arrElements[9];

					break;

				case 'BAK':
					// Check that we're inside a record section
					if ($intCurRecord == 0)
					{
						error_log("HardwareBundleSupplier::DecodeEDI855 - Reached BAK line while not in a record");

						$recEDIData['arrErrors'][] = 'HardwareBundleSupplier::DecodeEDI855 - Reached BAK line while not in a record';

						return $recEDIData;
					}

					// Get the PlusNET service ID
					$recEDIData['arrRecords'][$intCurRecord]['intHardwareID'] = $arrElements[3];

					// Get the order number
					$recEDIData['arrRecords'][$intCurRecord]['strOrderNumber'] = $arrElements[8];

					// Get the Acknowledgement Type
					$recEDIData['arrRecords'][$intCurRecord]['strAckType'] = $arrElements[2];

					break;

				case 'GE':
					// Store the number of records that should have been found
					$recEDIData['intRecordCount'] = $arrElements[1];

					break;

				default:
					// Not interested, do nothing
			}
		}

		// Check that we've found the correct number of records
		if (count($recEDIData['arrRecords']) != $recEDIData['intRecordCount'])
		{
			error_log("HardwareBundleSupplier::DecodeEDI855 - Record Count mismatch");

			$recEDIData['arrErrors'][] = 'HardwareBundleSupplier::DecodeEDI855 - Record Count mismatch';

			return $recEDIData;
		}

		return $recEDIData;

	} // function DecodeEDI855($arrSegments)

	} // InsightUKHardwareBundleSupplier
