<?php
    /**
    * This file declares the CComponentEvent class
    *
    * @package    Core
    * @subpackage Event Logging
    * <AUTHOR> gfraser
    * @version    $Id: CComponentEvent.inc,v 1.4 2006-07-04 04:52:34 fzaki Exp $
    * @filesource
    */

    require_once('/local/data/mis/database/database_libraries/CoreObjects/EventLogging/CEvent.inc');

    class CComponentEvent extends CEvent
    {

        /**
        * Component ID (userdata.components.component_id)
        *
        * @var integer
        * @access private
        */
        var $m_intComponentID = 0;


        /**
        * Constructor for the CComponentEvent class
        * Load a component instance
        *
        * @param int The component ID
        * <AUTHOR>
        * @return boolean success
        */

        function CComponentEvent($intComponentID)
        {
            //Set the member variable m_intComponentID
            $this->m_intComponentID = $intComponentID;

            return true;
        }


        //////////////
        // Accessors
        //////////////

        /**
        * Returns the component ID
        *
        * @access public
        * <AUTHOR>
        * @return integer component ID
        */
        function getComponentID()
        {
            if ($this->m_intComponentID < 1)
            {
                return false;
            }

            return $this->m_intComponentID;
        }


        //////////////////
        // Public Methods
        //////////////////

        function getEvents($strEventTypeHandle = null)
        {
            $dbhConn  = get_named_connection_with_db('componentEventLogging_reporting');

            $strQuery = "SELECT e.intComponentEventId, e.dtmEventDate,
                         et.vchHandle vchEventTypeHandle, et.vchDisplayName vchEventTypeName
                         FROM tblComponentEvent e
                         INNER JOIN tblComponentEventType et USING(intComponentEventTypeID)
                         WHERE e.intComponentID = {$this->m_intComponentID}\n";

            if ($strEventTypeHandle) {

                $intComponentEventTypeID = $this->prvGetEventTypeID($strEventTypeHandle);

                if (!$intComponentEventTypeID) return false;

                $strQuery .= "AND e.intComponentEventTypeID = $intComponentEventTypeID\n";
            }

            $strQuery .= "ORDER BY e.dtmEventDate";

            $res = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Get component events', false);

            if (!$res) return false;


            return PrimitivesResultsAsArrayGet($res);
        }



        /**
        * Log the creation event for the component
        *
        * @access public
        * <AUTHOR>
        * @return boolean true
        */
        function logCreation($strServiceComponentProductHandle='')
        {
            switch ($strServiceComponentProductHandle)
            {
                case 'PLUSTALK' :

                    $intComponentEventTypeID = $this->prvGetEventTypeID('PlusTalkCreation');

                    break;
                case 'WLR' :

                    $intComponentEventTypeID = $this->prvGetEventTypeID('WlrCreation');

                    break;
                default :

                    // Do nothing
                    break;

            } // end switch

            if (isset($intComponentEventTypeID) && $intComponentEventTypeID > 0)
            {
                $intComponentEventID = $this->logEvent($intComponentEventTypeID);

                if (isset($intComponentEventID) && $intComponentEventID > 0)
                {
                    return true;
                }

            } // end if

            return false;

        } // End of function logCreation

        /**
        * Log the signup event for the component
        *
        * @access public
        * <AUTHOR>
        * @return boolean true
        */
        function logSignup($strServiceComponentProductHandle='')
        {
            switch ($strServiceComponentProductHandle)
            {
                case 'PLUSTALK' :

                    $intComponentEventTypeID = $this->prvGetEventTypeID('PlusTalkSignup');

                    break;
                case 'WLR' :

                    $intComponentEventTypeID = $this->prvGetEventTypeID('WlrSignup');

                    break;

                default :

                    // Do nothing
                    break;

            } // end switch

            if (isset($intComponentEventTypeID) && $intComponentEventTypeID > 0)
            {
                $intComponentEventID = $this->logEvent($intComponentEventTypeID);

                if (isset($intComponentEventID) && $intComponentEventID > 0)
                {
                    return true;
                }

            } // end if

            return false;

        } // End of function logSignup


        /**
         * Log the event identified by the handle.
         *
         * @param string $handle the handle for the event
         *
         * @return boolean
         */
        function logEventByHandle($handle)
        {
            get_named_connection_with_db('componentEventLogging');

            $intComponentEventTypeID = $this->prvGetEventTypeID($handle);

            return $this->logEvent($intComponentEventTypeID);
        }

        /**
        * Log the event PlusTalkCancelation

        * @access public
        * <AUTHOR>
        * @return boolean true/false
        */
        function logPlusTalkCancellation($uxtCancellationDate)
        {
            $dbhConn  = get_named_connection_with_db('componentEventLogging');

            $intComponentEventTypeID = $this->prvGetEventTypeID('PlusTalkCancellation');

            return $this->logEvent($intComponentEventTypeID);
        }

        /**
        * Log the event WlrCancelation logWlrCeaseOrderEffective

        * @access public
        * <AUTHOR>
        * @return boolean true/false
        */
        function logWlrCancellation($uxtCancellationDate)
        {
            $dbhConn  = get_named_connection_with_db('componentEventLogging');

            $intComponentEventTypeID = $this->prvGetEventTypeID('WlrCancellation');

            return $this->logEvent($intComponentEventTypeID);
        }

        /**
        * Log the event PlusTalkCancelation

        * @access public
        * <AUTHOR>
        * @return boolean true/false
        */
        function logPlusTalkCancellationRequest($uxtCancellationDate=0)
        {
            if (0 == $uxtCancellationDate) {
                $uxtCancellationDate = time();
            }

            $dbhConn  = get_named_connection_with_db('componentEventLogging');

            $intComponentEventTypeID = $this->prvGetEventTypeID('PlusTalkCancellationRequest');

            return $this->logEvent($intComponentEventTypeID);
        }

        /**
        * Log the event WlrCancelation

        * @access public
        * <AUTHOR>
        * @return boolean true/false
        */
        function logWlrCancellationRequest($uxtCancellationDate=0)
        {
            if (0 == $uxtCancellationDate) {
                $uxtCancellationDate = time();
            }

            $dbhConn  = get_named_connection_with_db('componentEventLogging');

            $intComponentEventTypeID = $this->prvGetEventTypeID('WlrCancellationRequest');

            return $this->logEvent($intComponentEventTypeID);
        }

        /**
        * logWlrApplyDebtManagementDueToFailedBilling
        *
        * Log the event WlrDebtMgmtAppliedFailedBilling
        *
        * @access public
        * <AUTHOR> Marek" <<EMAIL>>
        * @return boolean true/false
        */
        function logWlrApplyDebtManagementDueToFailedBilling($uxtApplyDate=0)
        {
            if (0 == $uxtApplyDate) {
                $uxtApplyDate = time();
            }

            $dbhConn  = get_named_connection_with_db('componentEventLogging');

            $intComponentEventTypeID = $this->prvGetEventTypeID('WlrDebtMgmtAppliedFailedBilling');

            return $this->logEvent($intComponentEventTypeID);
        }

        /**
        * logWlrApplyDebtManagementDueToCreditLimitReached
        *
        * Log the event WlrDebtMgmtAppliedCreditLimit
        *
        * @access public
        * <AUTHOR> Marek" <<EMAIL>>
        * @return boolean true/false
        */
        function logWlrApplyDebtManagementDueToCreditLimitReached($uxtApplyDate=0)
        {
            if (0 == $uxtApplyDate) {
                $uxtApplyDate = time();
            }

            $dbhConn  = get_named_connection_with_db('componentEventLogging');

            $intComponentEventTypeID = $this->prvGetEventTypeID('WlrDebtMgmtAppliedCreditLimit');

            return $this->logEvent($intComponentEventTypeID);
        }

        /**
        * logWlrRemoveDebtManagementCreditLimit
        *
        * Log the event WlrDebtMgmtRemoveCreditLimit
        *
        * @access public
        * <AUTHOR> Marek" <<EMAIL>>
        * @return boolean true/false
        */
        function logWlrRemoveDebtManagementCreditLimit($uxtRemoveDate=0)
        {
            if (0 == $uxtRemoveDate) {
                $uxtRemoveDate = time();
            }

            $dbhConn  = get_named_connection_with_db('componentEventLogging');

            $intComponentEventTypeID = $this->prvGetEventTypeID('WlrDebtMgmtRemoveCreditLimit');

            return $this->logEvent($intComponentEventTypeID);
        }

        /**
        * logWlrRemoveDebtManagementExitTPAR
        *
        * Log the event WlrDebtMgmtRemoveExitTPAR
        *
        * @access public
        * <AUTHOR> Marek" <<EMAIL>>
        * @return boolean true/false
        */
        function logWlrRemoveDebtManagementExitTPAR($uxtRemoveDate=0)
        {
            if (0 == $uxtRemoveDate) {
                $uxtRemoveDate = time();
            }

            $dbhConn  = get_named_connection_with_db('componentEventLogging');

            $intComponentEventTypeID = $this->prvGetEventTypeID('WlrDebtMgmtRemoveExitTPAR');

            return $this->logEvent($intComponentEventTypeID);
        }

        /**
        * logWlrEventEffective
        *
        * Log the "effective" event for WLR
        *
        * @access public
        * <AUTHOR> Marek" <<EMAIL>>
        * @return boolean true/false
        */
        function logWlrEventEffective($strTypeHandler, $uxtEventDate=0)
        {
            if (0 == $uxtEventDate) {
                $uxtEventDate = time();
            }

            $dbhConn  = get_named_connection_with_db('componentEventLogging');

            $intComponentEventTypeID = $this->prvGetEventTypeID($strTypeHandler);

            return $this->logEvent($intComponentEventTypeID);
        }

        /**
        * Log the event WlrTransferAway.
        *
        * @access public
        * <AUTHOR> Marek" <<EMAIL>>
        * @return boolean true/false
        */
        function logWlrTransferAway($uxtEventDate=0)
        {
            if (0 == $uxtEventDate) {
                $uxtEventDate = time();
            }

            $dbhConn  = get_named_connection_with_db('componentEventLogging');

            $intComponentEventTypeID = $this->prvGetEventTypeID('WlrTransferAway');

            return $this->logEvent($intComponentEventTypeID);
        }

        /**
        * logWlrTransferAwayEffective
        *
        * Log the event WlrTransferAwayEffective
        *
        * @access public
        * <AUTHOR> Marek" <<EMAIL>>
        * @return boolean true/false
        */
        function logWlrTransferAwayEffective($uxtEventDate=0)
        {
            if (0 == $uxtEventDate) {
                $uxtEventDate = time();
            }

            $dbhConn  = get_named_connection_with_db('componentEventLogging');

            $intComponentEventTypeID = $this->prvGetEventTypeID('WlrTransferAwayEffective');

            return $this->logEvent($intComponentEventTypeID);
        }

        /**
        * logWlrRetention
        *
        * Log the event WlrRetention
        *
        * @access public
        * <AUTHOR> Marek" <<EMAIL>>
        * @return boolean true/false
        */
        function logWlrRetention($uxtEventDate=0)
        {
            if (0 == $uxtEventDate) {
                $uxtEventDate = time();
            }

            $dbhConn  = get_named_connection_with_db('componentEventLogging');

            $intComponentEventTypeID = $this->prvGetEventTypeID('WlrRetention');

            return $this->logEvent($intComponentEventTypeID);
        }

        /**
        * logWlrRetentionEffective
        *
        * Log the event WlrRetentionEffective
        *
        * @access public
        * <AUTHOR> Marek" <<EMAIL>>
        * @return boolean true/false
        */
        function logWlrRetentionEffective($uxtEventDate=0)
        {
            if (0 == $uxtEventDate) {
                $uxtEventDate = time();
            }

            $dbhConn  = get_named_connection_with_db('componentEventLogging');

            $intComponentEventTypeID = $this->prvGetEventTypeID('WlrRetentionEffective');

            return $this->logEvent($intComponentEventTypeID);
        }

        /**
        * logWlrTransferOrderEffective
        *
        * Log the event WlrTransferOrderEffective
        *
        * @access public
        * <AUTHOR> Marek" <<EMAIL>>
        * @return boolean true/false
        */
        function logWlrTransferOrderEffective($uxtEventDate=0)
        {
            if (0 == $uxtEventDate) {
                $uxtEventDate = time();
            }

            $dbhConn  = get_named_connection_with_db('componentEventLogging');

            $intComponentEventTypeID = $this->prvGetEventTypeID('WlrTransferOrderEffective');

            return $this->logEvent($intComponentEventTypeID);
        }

        /**
        * logWlrCeaseOrder
        *
        * Log the event WlrCeaseOrderCancellation
        *
        * @access public
        * <AUTHOR> Marek" <<EMAIL>>
        * @return boolean true/false
        */
        function logWlrCeaseOrder($uxtEventDate=0)
        {
            if (0 == $uxtEventDate) {
                $uxtEventDate = time();
            }

            $dbhConn  = get_named_connection_with_db('componentEventLogging');

            $intComponentEventTypeID = $this->prvGetEventTypeID('WlrCeaseOrder');

            return $this->logEvent($intComponentEventTypeID);
        }

        /**
        * logWlrCeaseOrderEffective
        *
        * Log the event WlrCeaseOrderCancellationEffective
        *
        * @access public
        * <AUTHOR> Marek" <<EMAIL>>
        * @return boolean true/false
        */
        function logWlrCeaseOrderEffective($uxtEventDate=0)
        {
            if (0 == $uxtEventDate) {
                $uxtEventDate = time();
            }

            $dbhConn  = get_named_connection_with_db('componentEventLogging');

            $intComponentEventTypeID = $this->prvGetEventTypeID('WlrCeaseOrderEffective');

            return $this->logEvent($intComponentEventTypeID);
        }

        /**
        * Log the event of product change request

        * @access public
        * <AUTHOR>
        * @return boolean true/false
        */
        function logPlusTalkProductChangeRequest($intComponentToID)
        {
                $intEventTypeID = $this->prvGetEventTypeID('PlusTalkProductChangeRequest');

                $dbhConn  = get_named_connection_with_db('componentEventLogging');
                $intComponentEventID = $this->logEvent($intEventTypeID);

                if (isset($intComponentEventID))
                {
                    $strQuery = 'INSERT INTO tblPlusTalkProductChange(intComponentEventID, '.
                                'intComponentToID) VALUES ('.
                                "'$intComponentEventID', '$intComponentToID')";

                    PrimitivesQueryOrExit($strQuery, $dbhConn,'Insert a component event');

                    return true;
                }

                return false;
        }

        /**
        * Log the event of product change request

        * @access public
        * <AUTHOR>
        * @return boolean true/false
        */
        function logWlrProductChangeRequest($intComponentToID)
        {
                $intEventTypeID = $this->prvGetEventTypeID('WlrProductChangeRequest');

                $dbhConn  = get_named_connection_with_db('componentEventLogging');
                $intComponentEventID = $this->logEvent($intEventTypeID);

                if (isset($intComponentEventID))
                {
                    $strQuery = 'INSERT INTO tblWlrProductChange(intComponentEventID, '.
                                'intComponentToID) VALUES ('.
                                "'$intComponentEventID', '$intComponentToID')";

                    PrimitivesQueryOrExit($strQuery, $dbhConn,'Insert a component event');

                    return true;
                }

                return false;
        }

        /**
        * Log the event PlusTalkProductChange

        * @access public
        * <AUTHOR>
        * @return boolean true/false
        */
        function logPlusTalkProductChange($intComponentToID)
        {
            $intEventTypeID = $this->prvGetEventTypeID('PlusTalkProductChange');

            $dbhConn  = get_named_connection_with_db('componentEventLogging');
            $intComponentEventID = $this->logEvent($intEventTypeID);

            if (isset($intComponentEventID))
            {
                $strQuery = 'INSERT INTO tblPlusTalkProductChange(intComponentEventID, '.
                            'intComponentToID) VALUES ('.
                            "'$intComponentEventID', '$intComponentToID')";

                PrimitivesQueryOrExit($strQuery, $dbhConn,'Insert a componnet event');

                return true;
            }

            return false;

        }

        /**
        * Log the event WlrProductChange

        * @access public
        * <AUTHOR>
        * @return boolean true/false
        */
        function logWlrProductChange($intComponentToID)
        {
            $intEventTypeID = $this->prvGetEventTypeID('WlrProductChange');

            $dbhConn  = get_named_connection_with_db('componentEventLogging');
            $intComponentEventID = $this->logEvent($intEventTypeID);

            if (isset($intComponentEventID))
            {
                $strQuery = 'INSERT INTO tblWlrProductChange(intComponentEventID, '.
                            'intComponentToID) VALUES ('.
                            "'$intComponentEventID', '$intComponentToID')";

                PrimitivesQueryOrExit($strQuery, $dbhConn,'Insert a component event');

                return true;
            }

            return false;

        }


        /**
        * Log the event for component status change

        * @access public
        * <AUTHOR>
        * @return boolean true/false
        */
        function logStatusChange($strStatusChangeHandle)
        {
            $intComponentEventTypeID = $this->prvGetEventTypeID($strStatusChangeHandle);
            if ($this->logEvent($intComponentEventTypeID))
            {
                return true;
            }
            return false;

        }

        /**
        * Log the event by event handle

        * @access public
        * <AUTHOR>
        * @return boolean true/false
        */
        function logPlusTalkPromotion($intPromotionID)
        {
                $intEventTypeID = $this->prvGetEventTypeID('PlusTalkPromotionActivation');

                $dbhConn  = get_named_connection_with_db('componentEventLogging');
                $intComponentEventID = $this->logEvent($intEventTypeID);

                if (isset($intComponentEventID))
                {
                        $strQuery = 'INSERT INTO tblPlusTalkPromotionActivation(intComponentEventID, '.
                                    'intPromotionID) VALUES ('.
                                    "'$intComponentEventID', '$intPromotionID')";

                        PrimitivesQueryOrExit($strQuery, $dbhConn,'Insert a componnet event');

                        return true;
                }

                return false;
        }

        /**
        * Log the event in for a component

        * @access public
        * <AUTHOR>
        * @return intComponentEventTypeID if successfull otherwise boolean false
        */
        function logEvent($intComponentEventTypeID)
        {
            $dbhConn  = get_named_connection_with_db('componentEventLogging');
            $strQuery = 'INSERT INTO tblComponentEvent (intComponentID, '.
                        "intComponentEventTypeID, dtmEventDate) VALUES ('$this->m_intComponentID', ".
                    "'$intComponentEventTypeID', NOW())";

            if (PrimitivesQueryOrExit($strQuery, $dbhConn,'Insert a component event'))
            {
                return (PrimitivesInsertIdGet($dbhConn));
            }

            return false;

        }

        /**
         * True static method to cache event type IDs - prvGetEventTypeID() is only accessible through an
         * object instance, due to the need to maintain backwards compatibility with the old caching mechanism
         *
         * @param str $strEventTypeHandle the event type handle
         *
         * @return int
         */
        public static function staticGetEventTypeID($strEventTypeHandle)
        {
            static $dataCache = array();

            // As there's ~100 of these, we cache the entire list in a single pass
            if (empty($dataCache)) {
                $conn  = get_named_connection_with_db('componentEventLogging_reporting');
                $query     = 'SELECT intComponentEventTypeID, vchHandle FROM tblComponentEventType';
                $resResult = PrimitivesQueryOrExit($query, $conn, 'Get event type ID');

                // Convert into a simple key => value lookup table
                $tmpList = PrimitivesResultsAsArrayGet($resResult, 'vchHandle');
                foreach ($tmpList as $tmpHandle => $eventType) {
                    $dataCache[$tmpHandle] = $eventType['intComponentEventTypeID'];
                }
            }

            if (!array_key_exists($strEventTypeHandle, $dataCache)) {
                return false;
            }

            return $dataCache[$strEventTypeHandle];
        }

        /**
         * Fetch a component event id, given the event type handle.  Caches data.
         * NOTE: CEvent has it's own legacy caching mechanism, which includes a count() method to check
         * how many handles have been loaded.  For now, we maintain this functionality...
         * Returns boolean false if the data is not found
         *
         * @param str $strEventTypeHandle The component event type id
         *
         * @return int
         */
        public function prvGetEventTypeID($strEventTypeHandle)
        {
            $cetID = CComponentEvent::staticGetEventTypeID($strEventTypeHandle);

            // Add the event type to the "legacy" caching system so that the legacy count() method
            // will still work correctly
            if ($cetID && !array_key_exists($strEventTypeHandle, $this->m_arrEventTypes)) {
                $this->m_arrEventTypes[$strEventTypeHandle] = $cetID;
            }

            return $cetID;
        }

        /**
         * GetLastComponentEventIDsForEventType
         *
         * Gets last intComponentEventTypeID for given ComponentEventType
         * @param  array Ids of the component event types we are interested in
         * 
         * @return array component event ids
         */
        function GetLastComponentEventIDsForEventType($arrComponentEventTypeIDs)
        {

            $dbhConn  = get_named_connection_with_db('componentEventLogging');

            $strComponentEventTypeIDs = implode(',',$arrComponentEventTypeIDs);

            $strQuery = 'SELECT MAX(intComponentEventID) as intComponentEventID,
            intComponentEventTypeID
            FROM
            tblComponentEvent
            WHERE
            intComponentID = '.$this->m_intComponentID.' AND
            intComponentEventTypeID IN('.$strComponentEventTypeIDs.')
            GROUP BY
            intComponentEventTypeID
            ORDER BY intComponentEventID ASC';

            $resResult = PrimitivesQueryOrExit($strQuery, $dbhConn,'Get event type ID');
            $arrComponentEventIDs = PrimitivesResultsAsArrayGet($resResult);

            $arrEvents = array();
            foreach ($arrComponentEventIDs as $arrComponentEventID)
            {
                $intType = $arrComponentEventID['intComponentEventTypeID'];
                $arrEvents[$intType] = $arrComponentEventID['intComponentEventID'];
            }
            return $arrEvents;
        }
    }

?>
