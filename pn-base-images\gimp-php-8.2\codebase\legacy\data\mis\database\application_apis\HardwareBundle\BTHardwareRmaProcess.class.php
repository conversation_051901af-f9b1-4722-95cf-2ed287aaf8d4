<?php
/**
 * BTHardwareRmaProcess class
 *
 * Encapsulates the Suppliers RMA process
 *
 * @package   DatabaseAccessCommon
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2007 PlusNet
 * @since     File available since 2007-05-25
 */

/**
 * BTHardwareRmaProcess
 *
 * Encapsulates the Suppliers RMA process
 *
 * @package   DatabaseAccessCommon
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2007 PlusNet
 */
class BTHardwareRmaProcess extends HardwareRmaProcess
{
    //
    // PRIVATE Members
    //

    /**
    * The Class Prefix
    *
    * A String that is the prefix of this class
    *
    * @access private
    * @var    String
    */
    var $m_strClassPrefix         = 'BT';


    /**
    * The Supplier Tag
    *
    * A String that is this suppliers tag
    *
    * @access private
    * @var    String
    */
    var $m_strSupplierTag         = 'BT';


    //
    // Constructor
    //

    /**
    * BTHardwareRmaProcess constructor
    *
    * The constructor method for this class
    *
    */
    function BTHardwareRmaProcess()
    {
        HardwareRmaProcess::HardwareRmaProcess();
    }//end of function:BTHardwareRmaProcess()


    //
    // Protected
    //

    /**
    * Get the RMA Code
    *
    * For this Hardware bundle get the RMA Code
    *
    * @access protected
    * @return String - The RMA Code
    */
    function GetRmaCode()
    {
        return 0;

        //todo - is this still valid for RMA
        $recRmaType  = $this->GetRmaType();
        $strTypeCode = $recRmaType['strCode'];

        $strReasonCode = $this->GetReasonCode();

        $objOriginalBundle = &$this->GetOriginalBundle();

        $strOriginalInvoiceNumber = str_pad($objOriginalBundle->GetInvoiceNumber(), 8, '0', STR_PAD_LEFT);

        $strRmaCount = sprintf('%03d', $this->GetRmaCount());

        $strRmaCode = "$strTypeCode$strReasonCode" . "00-$strOriginalInvoiceNumber-$strRmaCount";

        return $strRmaCode;
    }//end of function: GetRmaCode()


    /**
    * Get the return address
    *
    * Get the suppliers return address
    *
    * @access protected
    * @return String - The return address
    */

    function GetReturnsAddress()
    {
        //The returns address
        //@todo - Am I still valid for BT

        // BT take care of this. Return generic message.
        $strReturnAddress = 'Not applicable';

        return $strReturnAddress;

    }//end of function:GetSupplierReturnsAddress()

    /**
     * Calculate price changes
     *
     * @return int
     */
    function CalculatePriceChanges()
    {
        // With only USB there is nothing to work out. This will need extending when we provision other hardware.
        return 0;
    }

    /**
     * Get rma option Id from rma option Id
     *
     * @param int $intRMAOptionID rma option Id
     *
     * @return string
     */
    function GetRMATypeString($intRMAOptionID)
    {
        $strSQL = "SELECT vchHandle FROM tblHardwareRmaOption WHERE usiHardwareRmaOptionID = '$intRMAOptionID'";

        $dbhConnection = get_named_connection_with_db('product');

        $resResult = PrimitivesQueryOrExit($strSQL, $dbhConnection);

        $arrResult = PrimitivesResultsAsArrayGet($resResult);

        $strTag = $arrResult[0]['vchHandle'];

        return $strTag;
    }

    /**
     * InitiateRma
     *
     * @param int $intComponentID ComponentID
     *
     * @return void
     */
    function InitiateRma($intComponentID = 0)
    {
        //Calculate what items to add
        $recItemChanges = $this->CalculateItemChanges();
        $intRemoveDirectionID = $this->GetDirectionID('Remove');
        $intAddDirectionID    = $this->GetDirectionID('Add');

        $arrItemsBeforeAdding  = $this->m_objOriginalBundle->GetItems();
        $intCurBundleItemCount = count($arrItemsBeforeAdding);

        $dbhConnection = get_named_connection_with_db('userdata');

        //remove the items we need to get rid off
        foreach ($recItemChanges['Remove'] as $recItem) {

            //first check if the select qty is the same as is currently held on the customer's bundle
            $strQuery = "SELECT quantity from hardware_bundle_items
                where hardware_bundle_item_id = ".$recItem['intItemToRemoveID'];
            $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);
            $arrResults = PrimitivesResultsAsArrayGet($resResult);
            $intQuantity = $arrResults[0]['quantity'];
            //If somehow an incorrect qty has been passed, die and display an error
            if ($recItem['intQuantity'] < 1 || $recItem['intQuantity'] >	$intQuantity) {

                error_log(
                    "\n\n ####### SERVICE ID: " . $intServiceID . " - This is a very unlikely error " .
                    "and has been caused because bt-hardware-bundle-classes.inc has received an invalid " .
                    "quantity of items to RMA in line " . __LINE__ . ". This value must be greater than 0 " .
                    "and must be less than the current quantity in the database for this hardware bundle item. " .
                    "The Quantity received was " . $recItem['intQuantity']." and the quantity in the database is " .
                    $intQuantity . ". ####### \n\n"
                );
                die("An error has occured. Please contact a developer and ask them to review the error logs.");

            } elseif ($recItem['intQuantity'] == $intQuantity) {
                //If selected quantity is equal to current quantity in the database,
                //delete complete item as it will be added again

                $this->m_objOriginalBundle->DestroyBundleItems($recItem['intItemToRemoveID']);

            } else {
                //If selected quantity is less than current quantity in the database,
                //update current qty. the new order will be added

                $intNewQuantity = $intQuantity - $recItem['intQuantity'];
                $strQuery = "UPDATE hardware_bundle_items SET quantity = $intNewQuantity
                    WHERE hardware_bundle_item_id = ".$recItem['intItemToRemoveID'];
                PrimitivesQueryOrExit($strQuery, $dbhConnection);
            }
        }

        // Add the new items to the budles
        foreach ($recItemChanges['Add'] as $recItem) {

            $this->m_objOriginalBundle->AddItem(
                $recItem['intHardwareSupplierProductID'],
                1,
                'not_ordered'
            );
        }
        // Update the object
        $strNewStatus = 'awaiting_rma';
        $this->m_objOriginalBundle->SetStatusTag(-1, $strNewStatus, $recItemChanges['Add']);
        $this->m_objOriginalBundle->SetWhenLastUpdated();
        foreach ($recItemChanges['Remove'] as $item) {

            $bag_type = $this->get_correct_returns_bag($item['intHardwareSupplierProductID']);
            if (!empty($bag_type)) {
                break;
            }
        }
        // Commit the new bundle items
        //If this is a credit RMA, do not pass items for RMA to initiate ordering of credit bag
        if ($this->m_arrOptions[0] == 1) {

            $this->m_objOriginalBundle->Commit();
            $this->m_objOriginalBundle->AddJiffy(14, 11, $bag_type);
        } else {
             //Else, pass items that require RMAing
            $this->m_objOriginalBundle->Commit($this->m_arrAddedItems);
            $this->m_objOriginalBundle->AddJiffy(1, 11, $bag_type);
        }

        //$this->m_objOriginalBundle->Commit();
        // Add links to the new items in the RMA object
        $dbhConnection = get_named_connection('userdata');

        // Get the IDs of the new items
        foreach ($recItemChanges['Add'] as $intIndex => $recItem) {

            // Insert a link
            $recItem = $this->m_objOriginalBundle->GetItem($intIndex);

            $intNewItemID = $recItem['intID'];

            $arrAddedItems[] = $recItem;

            $strInsert = 'INSERT INTO tblHardwRmaProcessItem ( ' .
                                    ' usiHardwareRmaProcessID, ' .
                                    ' usiHardwRmaOptDirectionID, ' .
                                    ' usiHardwareBundleItemID ) ' .
                              'VALUES ( ' .
                                    " '$intID', " .
                                    " '$intAddDirectionID', " .
                                    " '$intNewItemID' )";
            mysql_query($strInsert, $dbhConnection)
                or report_error(__FILE__, __LINE__, mysql_error($dbhConnection));
        }

        //if the status has changed then add a contact
        $contactMessage = "Status of hardware order changed from '$strCurrentStatus' to '$strNewStatus'";

        $this->m_objOriginalBundle->AddContact($contactMessage);
    }//end of function: InitiateRma()


    /**
    * Actually send an RMA request
    *
    * Send the RMA request to the supplier
    *
    * @access protected
    * @return Boolean - Depending on success
    */
    function SendRmaRequest()
    {

        //Send the RMA request
        return true;
    }//end of function:SendRmaRequest()

}//end of class:BTHardwareRmaProcess
?>
