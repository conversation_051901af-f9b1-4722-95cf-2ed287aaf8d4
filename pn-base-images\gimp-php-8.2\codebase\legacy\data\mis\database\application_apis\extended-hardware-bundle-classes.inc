<?php

	///////////////////////////////////////////////////////////////////////////
	// hardware-bundle-classes.inc
	// <PERSON>, October 2002
	// Revised <PERSON>, June 2004
	// $Id: extended-hardware-bundle-classes.inc,v 1.20 2007-06-08 13:15:55 kprzybyszewski Exp $
	//
	// This file contains all extended classes used by the hardware bundle system.
	//
	// IMPORTANT:
	// The database has been designed to allow each item of the order to be
	// handled by a different supplier, thereby having its own status. However,
	// these classes have been written with the assumption that the supplier
	// and status for each item in an order are the same.
	//
	// Functions marked *** STATIC *** don't require an instansiated object to be used.
	// i.e. You can just call classname::functionname()
	//
	// Hungarian notation used:
	//
	// str : String
	// int : Integer
	// arr : Array of items (not a row from a database)
	// r   : Record (associative array, eg a row from the database)
	// bin : Boolean
	// h   : Handle (eg database connection or result, or a file handle)
	// m_  : Class member variable (shouldn't be touched outside the class)
	//
	
	require_once '/local/data/mis/database/application_apis/HardwareBundle/HardwareBundleControl.class.php';
	require_once '/local/data/mis/database/application_apis/HardwareBundle/HardwareBundleType.class.php';
	require_once '/local/data/mis/database/application_apis/HardwareBundle/HardwareBundle.class.php';
	require_once '/local/data/mis/database/application_apis/HardwareBundle/HardwareBundleSupplier.class.php';
?>