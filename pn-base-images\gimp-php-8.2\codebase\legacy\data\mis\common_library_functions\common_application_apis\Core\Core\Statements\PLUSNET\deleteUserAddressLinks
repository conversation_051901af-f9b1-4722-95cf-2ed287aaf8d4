server: coredb
role: master
rows: single
statement:

DELETE 
	userdata.tblUserAddress, 
	userdata.tblAddressHasType
FROM 
	userdata.tblUserAddress  
INNER JOIN userdata.tblAddressHasType ON
	userdata.tblUserAddress.intAddressId = userdata.tblAddressHasType.intAddressId 
INNER JOIN userdata.tblAddressType ON
	userdata.tblAddressHasType.intAddressTypeId = userdata.tblAddressType.intAddressTypeId
WHERE 
	userdata.tblAddressType.vchHandle = :vchHandle 
	AND userdata.tblUserAddress.intUserId = :intUserId
