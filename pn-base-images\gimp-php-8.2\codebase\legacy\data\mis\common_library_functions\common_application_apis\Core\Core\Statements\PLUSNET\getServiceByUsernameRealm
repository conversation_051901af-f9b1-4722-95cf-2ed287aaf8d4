server: coredb
role: slave
rows: single
statement:

    SELECT CAST(s.service_id AS UNSIGNED) AS intServiceId
      FROM PlusnetSession.tblBusinessActor AS ba
INNER JOIN PlusnetSession.tblAuthenticationRealm AS ar ON (ar.authenticationRealmID = ba.authenticationRealmID)
INNER JOIN PlusnetSession.tblUserType AS ut ON (ut.userTypeID = ba.userTypeID)
INNER JOIN userdata.services s ON (s.service_id = ba.externalUserID)
     WHERE ba.username  = :strUsername
       AND ar.realm = :strRealm
