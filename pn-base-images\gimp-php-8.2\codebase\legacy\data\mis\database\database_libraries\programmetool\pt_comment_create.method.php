<?php
/**
 * File header
 *
 * @package LegacyCodebase
 * <AUTHOR> <<EMAIL>>
 *
 */

/**
 * Create a new comment, linked to an existing problem
 *
 * @param array $array_comment associative array of comment info
 *
 * @return int the comment id
 */
function split_pt_comment_create($array_comment)
{
    foreach ($array_comment as $key => $value) {
        $$key = addslashes($value);
    }

    $query =
<<<EOQ
INSERT INTO pt_comment (
    type_id,
    type,
    sub_type_id,
    sub_type,
    comment_body,
    user_id,
    date_created 
) VALUES ( 
    $type_id,
    '$type',
    $sub_type_id,
    '$sub_type',
    '$comment_body',
    '$user_id',
    now() 
)
EOQ;

    switch ($sub_type) {
        case 'task':
            pr_update_task_modified($sub_type_id);
            break;
        case 'job':
            pr_update_job_modified($sub_type_id);
            break;
    }

    $db = get_named_connection('project_tool');
    mysql_query($query, $db);
    return mysql_insert_id($db);
}
