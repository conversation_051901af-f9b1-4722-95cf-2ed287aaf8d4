<?php

/**
 * Access library for Scheduled Payments
 *
 *
 *
 * @package    Core
 * @subpackage Financial
 * @access     public
 * <AUTHOR> <<EMAIL>>
 * @version    $Id: CScheduledPayment.inc,v 1.8 2008-09-22 20:00:01 rmerewood Exp $
 * @filesource
 */


require_once(DATABASE_LIBRARY_ROOT.'sql_primitives.inc');
require_once('/local/data/mis/database/database_libraries/CoreObjects/CObject/CObject.inc');
require_once('/local/data/mis/database/database_libraries/CoreObjects/Utility/CValidator.inc');
require_once('/local/data/mis/database/database_libraries/CoreObjects/Financial/CFinancialHelper.inc');

/**
 * Scheduled Payment object
 *
 *
 *
 * @access     public
 * <AUTHOR> <<EMAIL>>
 */
class CScheduledPayment extends CObject
{
    /**
     * Account ID
     *
     * @var integer
     * @access private
     */
    var $m_intAccountID = 0;

    /**
     * Actioner ID
     *
     * @var integer
     * @access private
     */
    var $m_strActionerID = '';


    /**
     * ID of type of payment
     *
     * @var integer
     * @access private
     */
    var $m_intScheduledPaymentTypeID = 0;


    /**
     * The config id, from different tables depending on the Scheduled Payment Type ID
     *
     * @var integer
     * @access private
     */
    var $m_intConfigID = 0;


    var $m_intScheduledPaymentID = 0;
    var $m_intSalesInvoiceID = 0;
    var $m_intLineItemID = 0;
    var $m_strDescription = 0;
    var $m_intAmountExVatPence = 0;
    var $m_intVatPence = 0;
    var $m_uxtDateDue = 0;
    var $m_bolOnBilling = 0;
    var $m_uxtStartPeriodCovered = 0;
    var $m_uxtEndPeriodCovered = 0;
    var $m_uxtInvoiced = 0;
    var $m_uxtDateExpected = 0;
    var $m_uxtCancelled = 0;
    var $m_uxtFailed = 0;


    ////////////////
    // Constructor
    ////////////////


    /**
     * Contructor for the CScheduledPayment class
     *
     * @access protected
     * <AUTHOR>
     * @param  integer
     * @return boolean success
     */
    function CScheduledPayment($intScheduledPaymentID)
    {
        if($intScheduledPaymentID < 1) {
            return false;
        }

        $dbhConn = get_named_connection_with_db('financial');

        $strQuery = "SELECT intScheduledPaymentID,
        intScheduledPaymentTypeID,
        intAccountID,
        intSalesInvoiceID,
        intLineItemID,
        vchDescription as strDescription,
        vchActionerID as strActionerID,
        intAmountExVatPence,
        intVatPence,
        UNIX_TIMESTAMP(dteDue) as uxtDateDue,
        bolOnBilling,
        UNIX_TIMESTAMP(dteStartPeriodCovered) as uxtStartPeriodCovered,
        UNIX_TIMESTAMP(dteEndPeriodCovered) as uxtEndPeriodCovered,
        UNIX_TIMESTAMP(dtmInvoiced) as uxtInvoiced,
        UNIX_TIMESTAMP(dtmDateExpected) as uxtDateExpected,
        UNIX_TIMESTAMP(dtmCancelled) as uxtCancelled,
        UNIX_TIMESTAMP(dtmFailed) as uxtFailed
        FROM financial.tblScheduledPayment
        WHERE intScheduledPaymentID = '$intScheduledPaymentID'";

        $refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Construct CScheduledPayment');
        $arrConfig = PrimitivesResultsAsArrayGet($refResult);

        if(count($arrConfig) < 1) {
            $this->setError(__FILE__, __LINE__, "Scheduled Payment for intScheduledPaymentID '$intScheduledPaymentID'");
            return false;
        }
        $arrConfig = $arrConfig[0];


        $this->m_intScheduledPaymentID = $intScheduledPaymentID;
        $this->m_intScheduledPaymentTypeID = $arrConfig['intScheduledPaymentTypeID'];
        $this->m_intAccountID = $arrConfig['intAccountID'];
        $this->m_intSalesInvoiceID = $arrConfig['intSalesInvoiceID'];
        $this->m_intLineItemID = $arrConfig['intLineItemID'];
        $this->m_strDescription = $arrConfig['strDescription'];
        $this->m_strActionerID = $arrConfig['strActionerID'];
        $this->m_intAmountExVatPence = $arrConfig['intAmountExVatPence'];
        $this->m_intVatPence = $arrConfig['intVatPence'];
        $this->m_uxtDateDue = $arrConfig['uxtDateDue'];
        $this->m_bolOnBilling = $arrConfig['bolOnBilling'];
        $this->m_uxtStartPeriodCovered = $arrConfig['uxtStartPeriodCovered'];
        $this->m_uxtEndPeriodCovered = $arrConfig['uxtEndPeriodCovered'];
        $this->m_uxtInvoiced = $arrConfig['uxtInvoiced'];
        $this->m_uxtDateExpected = $arrConfig['uxtDateExpected'];
        $this->m_uxtCancelled = $arrConfig['uxtCancelled'];
        $this->m_uxtFailed = $arrConfig['uxtFailed'];

        return true;
    }

    //////////////
    // Accessors
    //////////////

    /**
     * Returns scheduled payment ID
     *
     * @access public
     * <AUTHOR> Marek <<EMAIL>>
     *
     * @return int
     */
    function getScheduledPaymentID()
    {
        return $this->m_intScheduledPaymentID;
    }

    /**
     * Get the scheduled payment type id
     *
     * @return int Scheduled Paymeent Type ID
     */
    function getScheduledPaymentTypeID()
    {
        return $this->m_intScheduledPaymentTypeID;
    }

    /**
     * Get the description of the scheduled payment
     *
     * @return str Description
     */
    function getDescription()
    {
        return $this->m_strDescription;
    }

    /**
     * Get the amount to bill in POUNDS (as required by billing script)
     *
     * @return flo Cost (cost ex vat + vat)
     */
    function getBillingAmount()
    {
        return (($this->m_intAmountExVatPence + $this->m_intVatPence) / 100);
    }

    /**
     * Get the sales invoice id
     *
     * @return int
     */
    function getSalesInvoiceID()
    {
        return $this->m_intSalesInvoiceID;
    }

    /**
     * @return int
     */
    function getStartPeriodCovered()
    {
        return $this->m_uxtStartPeriodCovered;
    }

    /**
     * @return int
     */
    function getEndPeriodCovered()
    {
        return $this->m_uxtEndPeriodCovered;
    }

    /////////////////////
    // Static Methods
    ////////////////////
    /**
    * Mark the schedule payment as been failed
    *
    * @access public
    * <AUTHOR>
    * @param  int Schedule Payment ID
    * @param  int Failed biling report ID
    * @return boolean TRUE/FALSE
    */

    function markAsFailed($intScheduledPaymentID, $intBillingReportID)
    {
        $dbhConnection = get_named_connection_with_db('financial');
        $strQuery = " INSERT INTO
        tblFailedPayment (intScheduledPaymentID,
        intBrID)
        VALUES ('$intScheduledPaymentID',
        '$intBillingReportID') ";

        if(PrimitivesQueryOrExit($strQuery, $dbhConnection , "Add the record in Failed Payment table from CScheduledPayment::markAsFailed "))
        {
            if(CScheduledPayment::markSchedulePaymentAsFailed($intScheduledPaymentID))
            {
                return TRUE;
            }
        }

        return FALSE;
    }

    /**
     * Mark scheduled payment as Failed
     *
     *
     * @access public
     * <AUTHOR>
     * @param  integer The Schedule Payment ID
     * @return boolean true on success, false on failure.
     */
    function markSchedulePaymentAsFailed($intScheduledPaymentID)
    {

        //Create the payment
        $dbhConn = get_named_connection_with_db('financial');

        $strQuery = "UPDATE financial.tblScheduledPayment
        SET dtmFailed = NOW()
        WHERE intScheduledPaymentID = '$intScheduledPaymentID' ";

        if(PrimitivesQueryOrExit($strQuery, $dbhConn, 'Mark a scheduled payment as failed'))
        {
            return TRUE;
        }

        return false;
    }


    /**
     * Mark failed scheduled payment as payed
     *
     *
     * @access public
     * <AUTHOR>
     * @param  integer billing report ID
     * @return boolean true on success, false on failure.
     */

    function markFailedSchedulePaymentAsPaid($intSalesInvoiceID, $intLineInvoiceItemId = null)
    {
        $dbhConn = get_named_connection_with_db('financial');

        $strQuery = "UPDATE tblFailedPayment SET
        dtmPaid = NOW()
        WHERE intScheduledPaymentID = '{$this->m_intScheduledPaymentID}'";

        if(PrimitivesQueryOrExit($strQuery, $dbhConn, 'Update the failed payment row to be paid')) {
            $this->markAsInvoiced($intSalesInvoiceID, $intLineInvoiceItemId);
            return TRUE;
        }


        return FALSE;
    }


    /**
     * Get the failed schedule payment details based on billing report ID
     *
     *
     * @access public
     * <AUTHOR>
     * @param  integer billing report ID
     * @return boolean true on success, false on failure.
     */
    function getFailedSchedulePaymentByBillingReportID($intBillingReportID)
    {
        $dbhConn = get_named_connection_with_db('financial');
        $strQuery = "SELECT intScheduledPaymentID,
        intFailedPaymentID
        FROM tblFailedPayment
        WHERE intBrID = '$intBillingReportID'
        AND dtmPaid IS NULL";

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Get the schedule payments that are failed');
        $arrFailedPayments = PrimitivesResultsAsArrayGet($resResult);

        if(count(arrFailedPayments) > 0)
        {
            return $arrFailedPayments;
        }

        return FALSE;

    }

    /**
     * Returns an instance of the correct concrete type of ScheduledPayment
     *
     * Abstract factory method.
     *
     * @access public
     * @static
     * <AUTHOR>
     * @param  integer Scheduled Payment ID
     * @return object An instance of a concrete scheduled payment subclass
     */
    function createInstance($intScheduledPaymentID)
    {
        $intScheduledPaymentTypeID = CScheduledPayment::getTypeID($intScheduledPaymentID);
        $strType = CScheduledPayment::getTypeHandle($intScheduledPaymentTypeID);

        switch ($strType) {
            case 'PRODUCT_COMPONENT':
                include_once('/local/data/mis/database/database_libraries/CoreObjects/Financial/CProductComponentScheduledPayment.inc');
                return new CProductComponentScheduledPayment($intScheduledPaymentID);
                break;
            case 'ONE_OFF':
                return new self($intScheduledPaymentID);
            case 'COMPONENT':
            case 'HARDWARE_BUNDLE':
            default:
                break;
        }

        //Type implementation not found
        return new CError(__FILE__, __LINE__, "Unrecognised Scheduled Payment Type.  No implementation found.");
    }


    /**
     * Fetch scheduled payment type id
     *
     * @access private
     * <AUTHOR>
     * @param  integer Scheduled Payment ID
     * @return integer Scheduled Payment Type ID
     */
    function getTypeID($intScheduledPaymentID)
    {
        $dbhConn  = get_named_connection_with_db('financial');

        $strQuery = "SELECT intScheduledPaymentTypeID
        FROM tblScheduledPayment
        WHERE intScheduledPaymentID = '$intScheduledPaymentID'";

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConn,'Get scheduled payment type ID');
        return PrimitivesResultGet($resResult, 'intScheduledPaymentTypeID');
    }

    /**
     * Private helper to get/cache scheduled payment type details - there's only 4 of them!
     *
     * @return array
     */
    public static function getAllScheduledPaymentTypes()
    {
        static $dataCache = false;

        if (empty($dataCache)) {
            $query = "SELECT intScheduledPaymentTypeID, vchHandle AS strHandle FROM tblScheduledPaymentType";

            $conn      = get_named_connection_with_db('financial_reporting');
            $resResult = PrimitivesQueryOrExit($query, $conn, 'Get scheduled payment type ID by handle');
            $dataCache = PrimitivesResultsAsArrayGet($resResult);
        }

        return $dataCache;
    }

    /**
     * Fetch scheduled payment type id by handle
     *
     * @param str $strHandle The payment type handle
     *
     * @return int
     */
    public static function getTypeIDByHandle($strHandle)
    {
        // As there's only 4 types, it's not worth caching...
        $typeList = CScheduledPayment::getAllScheduledPaymentTypes();
        foreach ($typeList as $type) {
            if ($type['strHandle'] == $strHandle) {
                return $type['intScheduledPaymentTypeID'];
            }
        }

        return false;
    }

    /**
     * Fetch scheduled payment type handle by type id
     *
     * @param int $intScheduledPaymentTypeID The payment type id
     *
     * @return str
     */
    public static function getTypeHandle($intScheduledPaymentTypeID)
    {
        // As there's only 4 types, it's not worth caching...
        $typeList = CScheduledPayment::getAllScheduledPaymentTypes();
        foreach ($typeList as $type) {
            if ($type['intScheduledPaymentTypeID'] == $intScheduledPaymentTypeID) {
                return $type['strHandle'];
            }
        }

        return false;
    }

    /**
     * Get outstanding scheduled payments for a particular account id for billing
     *
     * @access public
     * <AUTHOR>
     * @param  int Account ID
     * @param  uxtDueDate
     * @return arr Scheduled Payment IDs
     */
    public static function getBillingOutstandingScheduledPaymentIDs($intAccountID, $uxtDueDate=0)
    {
        if (!(isset($uxtDueDate) && $uxtDueDate > 0))
        {
            $uxtDueDate = mktime(0, 0, 0, date('m'), date('d'), date('Y'));
        }

        $dbhConnection = get_named_connection_with_db('financial');

        $strQuery = 'SELECT intScheduledPaymentID ' .
                    ' FROM tblScheduledPayment ' .
                    " WHERE " .
                    " intAccountID = '$intAccountID' " .
                    " AND dteDue <= FROM_UNIXTIME('$uxtDueDate') " .
                    ' AND dtmInvoiced IS NULL ' .
                    ' AND bolOnBilling = 1 ' .
                    ' AND dtmCancelled IS NULL ' .
                    ' AND dtmFailed IS NULL '.
                    ' ORDER BY dteDue, dteStartPeriodCovered';

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection , "Get oustanding scheduled payment IDs for Service ID $intServiceID");

        return PrimitivesResultsAsListGet($resResult);

    } // End of function getBillingOutstandingScheduledPaymentIDs


    /**
     * We can only take 3 months of payments due to OFCOM's general condition 11.
     * So cancell call charges over 3 months old
     *
     * @access public
     * <AUTHOR>
     * @param  int Account ID
     * @param  uxtCancelled
     * @return boolean success
     */
    public static function cancelCallCharges($intAccountID, $uxtCancelled=0)
    {
        if(!isset($intAccountID) || !is_numeric($intAccountID)
            || !isset($uxtCancelled) || !is_numeric($uxtCancelled)) {

            return;
        }

        if($uxtCancelled == 0) {
            $uxtCancelled = time();
        }

        $dbhConnection = get_named_connection_with_db('financial');

        $strQuery = "UPDATE tblScheduledPayment
                    SET dtmCancelled = FROM_UNIXTIME($uxtCancelled)
                    WHERE intAccountID = '$intAccountID'
                    AND dtmInvoiced IS NULL
                    AND bolOnBilling = 1
                    AND dtmCancelled IS NULL
                    AND dtmFailed IS NULL
                    AND dteDue <= DATE_SUB(FROM_UNIXTIME($uxtCancelled), INTERVAL 3 MONTH)
                    AND vchDescription like 'Call charge for the period to%'";

        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection , "Mark Scheduled Payment for call charges as cancelled");

        return true;

    } // End of function cancelCallCharges


    //////////////////////////////
    // Static Validator Methods
    //////////////////////////////


    /**
     * Validates the format of a scheduled payment ID
     *
     *
     * @access public
     * @static
     * <AUTHOR>
     * @param  integer Scheduled Payment ID
     * @return boolean true if matches, false if not
     */
    function isValidScheduledPaymentIDFormat($intScheduledPaymentID)
    {
        return ((is_int($intScheduledPaymentID) && $intScheduledPaymentID >= 0) || ctype_digit($intScheduledPaymentID));
    }


    /**
     * Validates a scheduled payment ID
     *
     * Expensive, as it requires a database hit. Only use if really required,
     * such as for financial actions.
     * Otherwise, try isValidScheduledPaymentIDFormat
     *
     * @access public
     * @static
     * <AUTHOR>
     * @param  integer Scheduled Payment ID
     * @return boolean true if matches, false if not
     */
    function isValidScheduledPaymentID($intScheduledPaymentID)
    {
        if(!CScheduledPayment::isValidScheduledPaymentIDFormat($intScheduledPaymentID)) {
            return false;
        }


        $dbhConn = get_named_connection_with_db('financial');

        return CValidator::idExistsInTable($dbhConn,
        $intScheduledPaymentID,
                                           'financial',
                                           'tblScheduledPayment',
                                           'intScheduledPaymentID');
    }


    ////////////////////////
    // Public Methods
    ////////////////////////


    /**
     * Check whether scheduled payment has been invoiced
     *
     * @access public
     * <AUTHOR>
     * @param  integer The ID of the Scheduled Payment
     * @return boolean true or false
     */
    function hasBeenInvoiced()
    {
        $dbhConn = get_named_connection_with_db('financial');

        $strQuery = "SELECT IF((intSalesInvoiceID IS NOT NULL AND intSalesInvoiceID != 0), 1, 0) as bolHasBeenInvoiced
        FROM financial.tblScheduledPayment
        WHERE intScheduledPaymentID = '{$this->m_intScheduledPaymentID}'";

        $refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Check if scheduled payment has been invoiced');

        return (boolean) PrimitivesResultGet($refResult,'bolHasBeenInvoiced');
    }

    /**
     * Mark scheduled payment as invoiced
     *
     * @access public
     * <AUTHOR>
     *
     * @param  integer The invoice ID this payment was invoiced on
     * @param  integer The line invoice ID this payment was invoiced on
     * @param  integer Unix Timestamp of the date this was invoiced (defaults to now)
     * @param  bool bolUnFail mark as non failed previously failed billing?
     *
     * @return boolean true on success, false on failure.
     */
    function markAsInvoiced($intSalesInvoiceID, $intLineInvoiceItemId = null, $uxtInvoiced=0, $bolUnFail=FALSE)
    {
        if($this->hasBeenInvoiced()) {
            $this->setError(__FILE__, __LINE__, "Cannot mark scheduled payment ID '{$this->m_intScheduledPaymentID}' as invoiced.  All ready invoiced.");
            return false;
        }

        //Default Date Invoiced to now
        if($uxtInvoiced == 0) {
            $uxtInvoiced = time();
        }

        //Create the payment
        $dbhConn = get_named_connection_with_db('financial');

        $strFailedSql      = ($bolUnFail) ? ' dtmFailed = NULL, ' : '';
        $strInvoiceItemSql = (!empty($intLineInvoiceItemId) && is_numeric($intLineInvoiceItemId)) ? " intLineItemID = $intLineInvoiceItemId, " : '';

        $strQuery = "UPDATE financial.tblScheduledPayment
                     SET intSalesInvoiceID = '$intSalesInvoiceID',
                         $strFailedSql
                         $strInvoiceItemSql
                         dtmInvoiced = FROM_UNIXTIME($uxtInvoiced)
                     WHERE intScheduledPaymentID = '{$this->m_intScheduledPaymentID}'
                    ";

        PrimitivesQueryOrExit($strQuery, $dbhConn, 'Mark a scheduled payment as invoiced');

        //Should have updated exactly 1 row.
        $intNumAffectedRows = PrimitivesAffectedRowsGet($dbhConn);

        if($intNumAffectedRows != 1) {
            $this->setError(__FILE__, __LINE__,
                            "Error marking Scheduled Payment as invoiced. Affected rows = '$intNumAffectedRows'.  " .
                            "Expected '1' for Payment ID '{$this->m_intScheduledPaymentID}'");
            return false;
        }

        $this->m_intSalesInvoiceID = $intSalesInvoiceID;
        $this->m_intLineItemID     = $intLineInvoiceItemId;
        $this->m_uxtInvoiced       = $uxtInvoiced;

        if ($bolUnFail)
        {
            $this->m_uxtFailed = null;
        }
        return true;
    }

    /**
     * You MUST override this function with the correct method for the scheduled payment type
     *
     * @public
     * <AUTHOR>
     * @return bool
     */
    function renew()
    {
        return true;
    }

    /**
     * Cancel this scheduled payment
     *
     *
     * @access public
     * <AUTHOR>
     * @return boolean success
     */
    function cancel($uxtCancelled=0)
    {
        if($uxtCancelled == 0) {
            $uxtCancelled = time();
        }

        $dbhConn = get_named_connection_with_db('financial');

        $strQuery = "UPDATE financial.tblScheduledPayment
        SET dtmCancelled = FROM_UNIXTIME($uxtCancelled)
        WHERE intScheduledPaymentID = '{$this->m_intScheduledPaymentID}'";

        $refResult = PrimitivesQueryOrExit($strQuery, $dbhConn, 'Mark a Scheduled Payment as cancelled');

        //Should have updated exactly 1 row.
        $intNumAffectedRows = PrimitivesAffectedRowsGet($dbhConn);

        if($intNumAffectedRows != 1) {
            $this->setError(__FILE__, __LINE__,
                            "Error marking Scheduled Payment as Cancelled. Affected rows = '$intNumAffectedRows'.  " .
                            "Expected '1' for Payment ID '{$this->m_intScheduledPaymentID}'");
            return false;
        }

        return true;
    }



    function getComponentId()
    {
        $db = get_named_connection_with_db('financial_reporting');

        $strQuery = "SELECT intComponentID FROM tblConfigComponent
        WHERE intScheduledPaymentID = {$this->m_intScheduledPaymentID}";

        $res = PrimitivesQueryOrExit($strQuery, $db, 'CScheduledPayment::getComponentId', FALSE);

        if(!$res) return FALSE;


        return PrimitivesResultGet($res, 'intComponentID');
    }

    function getSchedulePaymentIdByInvoiceId($intInvoiceId)
    {
        $db = get_named_connection_with_db('financial_reporting');

        if(empty($intInvoiceId))
        {
            return false;
        }

        $strQuery = "SELECT intScheduledPaymentID FROM tblScheduledPayment WHERE intSalesInvoiceID = '$intInvoiceId'";

        $res = PrimitivesQueryOrExit($strQuery, $db, 'CScheduledPayment::isSchedulePayment', FALSE);

        if(!$res) return FALSE;


        $intScheduledPaymentID = PrimitivesResultGet($res, 'intScheduledPaymentID');

        if($intScheduledPaymentID > 0)
        {
            return $intScheduledPaymentID;
        }

        return false;

    }

    /**
     * Set the payment period covered by this scheduled payment. If bolSafe is set to true (default)
     * then the period will only be set if the current values are 'no'
     *
     * @param integer $uxtPeriodStart start date of the period
     * @param integer $uxtPeriodEnd   end date of the period
     * @param boolean $bolSafe        only update the period if it is currently not set
     *
     * @return boolean
     */
    public function setPaymentPeriodCovered($uxtPeriodStart, $uxtPeriodEnd, $bolSafe = true)
    {
        // invalid / negative start

        if (!ctype_digit($uxtPeriodStart) && (!is_int($uxtPeriodStart) || $uxtPeriodStart < 0)) {

            $this->setError(
                __FILE__,
                __LINE__,
                'Start period is invalid. Unable to set new payment period on SPID ' .
                    "#{$this->m_intScheduledPaymentID}"
            );

            return false;
        }


        // invalid / negative end

        if (!ctype_digit($uxtPeriodEnd) && (!is_int($uxtPeriodEnd) || $uxtPeriodEnd < 0)) {

            $this->setError(
                __FILE__,
                __LINE__,
                'End period is invalid. Unable to set new payment period on SPID ' .
                    "#{$this->m_intScheduledPaymentID}"
            );

            return false;
        }


        // start and end is back-to-front or on same second

        if ($uxtPeriodStart >= $uxtPeriodEnd) {

            $this->setError(
                __FILE__,
                __LINE__,
                "Start period ({$uxtPeriodStart}) is ahead (or same as) of end period ({$uxtPeriodEnd}). Unable to set new payment period on SPID " .
                    "#{$this->m_intScheduledPaymentID}"
            );

            return false;
        }


        if ($bolSafe && ($this->m_uxtStartPeriodCovered > 0 || $this->m_uxtEndPeriodCovered > 0)) {

            $this->setError(
                __FILE__,
                __LINE__,
                'Payment period already defined. Unable to set new payment period on SPID ' .
                    "#{$this->m_intScheduledPaymentID}"
            );

            return false;
        }

        $db = get_named_connection_with_db('financial');

        // Already sanity checked, but prevent stupidity on the DB side too
        $strSafeClause = '';
        if ($bolSafe) {
            $strSafeClause = 'dteStartPeriodCovered IS NULL AND dteEndPeriodCovered IS NULL AND ';
        }

        $strQuery = "
 UPDATE financial.tblScheduledPayment
    SET dteStartPeriodCovered = FROM_UNIXTIME({$uxtPeriodStart}),
        dteEndPeriodCovered   = FROM_UNIXTIME({$uxtPeriodEnd})
  WHERE {$strSafeClause}intScheduledPaymentId = {$this->m_intScheduledPaymentID}";

        $res = PrimitivesQueryOrExit($strQuery, $db, 'CScheduledPayment::setPaymentPeriodCovered', FALSE);

        $affectedRows = PrimitivesAffectedRowsGet($db);
        if ($affectedRows != 1) {

            $this->setError(
                __FILE__,
                __LINE__,
                "Failed to update the payment period for SPID #{$this->m_intScheduledPaymentID}. " .
                    "Expected to update 1 row, updated {$affectedRows} instead."
            );

            return false;
        }

        $this->m_uxtStartPeriodCovered = $uxtPeriodStart;
        $this->m_uxtEndPeriodCovered   = $uxtPeriodEnd;

        return true;
    }



    ////////////////////////
    // Private Methods
    ////////////////////////


}
