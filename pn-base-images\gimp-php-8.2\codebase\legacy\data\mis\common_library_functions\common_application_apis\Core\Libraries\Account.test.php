<?php
	if (! defined('SIMPLE_TEST'))
	{
		define('SIMPLE_TEST', '/local/data/mis/scripts/SimpleTests/simpletest/');
	}

	require_once(SIMPLE_TEST . 'unit_tester.php');
	require_once(SIMPLE_TEST . 'reporter.php');

	require_once ('/local/data/mis/database/standard_include.inc');
	require_once ('/local/www/database-admin/config/config.inc');

	class Test_Core_Account extends UnitTestCase
	{
		public function Test_getAccountDaoFailure()
		{
			$objAccount = new Core_Account();
			
			$bolExceptionThrown = false;
			try {

				$objAccount->getAccountDao();

			} catch (Exception $objException) {

				$this->assertEqual($objException->getCode(), Core_Account::ERR_ACCOUNT_DAO_EMPTY);
				$bolExceptionThrown = true;
			}

			$this->assertTrue($bolExceptionThrown);
		} // public function Test_getAccountDaoFailure()
		
		public function getAccountIdByUserIdUsingAddress()
		{
			$objAdaptor = Mock::createMock($this, 'Db_Adaptor');
			Db_Manager::setAdaptor('Core', $objAdaptor);

			$intUserId = 95970;
			$intReturnAccountId = 101;
			$objAdaptor->getAccountIdUsingAddress($intUserId)->andReturn($intReturnAccountId);

			Mock::replay($objAdaptor);

			$intAccountId = Core_Account::getAccountIdByUserId($intUserId);

			$this->assertEqual($intAccountId, $intReturnAccountId);

			Mock::tally($objAdaptor);
		} // public function Test_getAccountIdByUserIdUsingAddress()

		public function Test_getAccountIdByUserIdWithoutAddress()
		{
			$objAdaptor = Mock::createMock($this, 'Db_Adaptor');
			Db_Manager::setAdaptor('Core', $objAdaptor);

			$intUserId = 95970;
			$intReturnAccountId = null;
			$objAdaptor->getAccountIdUsingAddress($intUserId)->andReturn($intReturnAccountId);
			$intReturnAccountId = 101;
			$objAdaptor->getAccountIdWithoutAddress($intUserId)->andReturn($intReturnAccountId);

			Mock::replay($objAdaptor);

			$intAccountId = Core_Account::getAccountIdByUserId($intUserId);

			$this->assertEqual($intAccountId, $intReturnAccountId);

			Mock::tally($objAdaptor);
		} // public function Test_getAccountIdByUserIdWithoutAddress()

		public function Test_getAccountIdByUserIdFailure()
		{
			$objAdaptor = Mock::createMock($this, 'Db_Adaptor');
			Db_Manager::setAdaptor('Core', $objAdaptor);

			$intUserId = 95970;
			$intReturnAccountId = null;
			$objAdaptor->getAccountIdUsingAddress($intUserId)->andReturn($intReturnAccountId);
			$objAdaptor->getAccountIdWithoutAddress($intUserId)->andReturn($intReturnAccountId);

			Mock::replay($objAdaptor);

			$bolExceptionThrown = false;
			try {

				$intAccountId = Core_Account::getAccountIdByUserId($intUserId);

			} catch (Exception $objException) {

				$bolExceptionThrown = true;
				$this->assertEqual($objException->getCode(), Core_Account::ERR_ACCOUNTID_NOT_FOUND);
			}
			

			$this->assertTrue($bolExceptionThrown);

			Mock::tally($objAdaptor);
		} // public function Test_getAccountIdByUserIdFailure()
		
	} // class Test_Core_Account extends UnitTestCase

?>
