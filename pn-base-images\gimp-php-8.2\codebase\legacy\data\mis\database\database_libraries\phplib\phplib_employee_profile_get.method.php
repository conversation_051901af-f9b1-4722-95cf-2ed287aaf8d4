<?php
/**
 * phpcs DOCBLOCK
 *
 * @package LegacyCodebase
 * <AUTHOR> <<EMAIL>>
 */

/**
 * Retrieves the profile of an employee, including their team information.  Returns an empty array if no data found
 *
 * @param str $user_id The ID of the employee
 *
 * @return array
 */
function split_phplib_employee_profile_get($user_id)
{
    static $dataCache = array();

    if (!array_key_exists($user_id, $dataCache)) {
        $tmpUserId = addslashes($user_id);
        $query     = "SELECT * FROM employee_profile WHERE user_id='$tmpUserId'";

        $connection = get_named_connection('phplib_reporting');
        $result     = mysql_query($query, $connection) or report_error(__FILE__, __LINE__, mysql_error($connection));
        $userdata   = mysql_fetch_array($result, MYSQL_ASSOC);
        mysql_free_result($result);

        // If the lookup fails, we fallback to the master DB.  If this fails, the user really doesn't exist!
        if (empty($userdata)) {
            $connection = get_named_connection('phplib');
            $result     = mysql_query($query, $connection)
                or report_error(__FILE__, __LINE__, mysql_error($connection));
            $userdata   = mysql_fetch_array($result, MYSQL_ASSOC);
            mysql_free_result($result);
        }

        $userdata = (!empty($userdata) ? $userdata : array());
        $dataCache[$user_id] = $userdata;
    }

    $userdata = $dataCache[$user_id];
    
    if (!empty($userdata)) {
        // phplib_team_get() also caches data, so we can reduce memory overheads by calling this function on demand
        // to populate $userdata['team'], rather than storing a copy of the team data against each individual employee!
        $userdata['team'] = phplib_team_get($userdata['team_id']);
    }

    return $userdata;
}
