<?php
/**
 * Core BillingDate Facade
 *
 * @package   Core
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2010 PlusNet
 * @version   git
 * @since     File available since 2010-04-30
 */

/**
 * Core BillingDate Facade
 *
 * We use this class to 'safely' get an instance of Core_BillingDate - particularly, if there
 * is a disparity between next_invoice and invoice_day it will catch the exception that Core_BillingDate
 * throws and raise an auto-problem instead. It will then 'guess' the invoice day as being the
 * month day from the next invoice field. This should only occur where there is a genuine disparity
 * between the invoice_date and invoice_day.
 *
 * @package   Core
 * <AUTHOR> <rjon<PERSON>@plus.net>
 *
 * @copyright 2010 PlusNet
 */
class Core_BillingDate_Facade
{
    /**
     * The actor ID of the script user
     *
     * @var integer
     */
    const SCRIPT_USER_ACTOR_ID = 5;

    /**
     * Do we want to suppress the auto problem
     *
     * @var boolean
     */
    protected static $_suppress = false;

    /**
     * Get a Core_BillingDate object passing in the data directly
     *
     * @param UnsignedInt $serviceId     Service ID
     * @param I18n_Date   $billingDate   Billing date
     * @param UnsignedInt $billingPeriod Billing period
     * @param UnsignedInt $billingDay    Billing day
     *
     * @return Core_BillingDate
     */
    public static function getBillingDate(
        UnsignedInt $serviceId,
        I18n_Date   $billingDate   = null,
        UnsignedInt $billingPeriod = null,
        UnsignedInt $billingDay    = null
    )
    {
        try {
            $billingDate = new Core_BillingDate($billingDate, $billingPeriod, $billingDay);
        } catch (Core_Exception $exc) {
            $billingDate =
                self::raiseAutoproblemAndGuessBillingDay($serviceId, $billingDate, $billingPeriod, $billingDay);
        }

        return $billingDate;
    }

    /**
     * Allows us to get a BillingDate object based on the details we get straight out of the userdata.services table
     *
     * @param integer $serviceId Service ID
     * @param string  $date      Billing Date
     * @param string  $period    Billing Period Handle
     * @param integer $day       Billing Day
     *
     * @return Core_BillingDate
     */
    public static function getBillingDateFromDateAndPeriod($serviceId, $date, $period, $day)
    {
        try {
            $billingDate = Core_BillingDate::getBillingDateFromDateAndPeriod($date, $period, $day);
        } catch (Core_Exception $exc) {
            $billingDate = self::raiseAutoproblemAndGuessBillingDay(
                new UnsignedInt($serviceId),
                I18n_Date::fromString($date),
                Core_BillingDate::getBillingPeriodFromHandle($period),
                new UnsignedInt($day)
            );
        }

        return $billingDate;
    }

    /**
     * Suppress the auto problem from being raised
     *
     * @param bool $suppress if true, an auto-problem will not be raised in the event of date mismatches
     *
     * @return bool
     */
    public static function suppressAutoProblem($suppress = true)
    {
        $oldValue = self::$_suppress;

        self::$_suppress = $suppress;

        return $oldValue;
    }

    /**
     * If the Core_BillingDate class throws an exception we assume that the billing date is correct
     * and the billing day is wrong, so we get the billing day from the day of the invoice date. We'll
     * raise an auto-problem to highlight that we've done this. If we don't return some kind of valid
     * Core_BillingDate object we'll probably stop the higher-level script from operating, and will
     * probably cause bad things to happen
     *
     * @param UnsignedInt $serviceId     Service ID
     * @param I18n_Date   $billingDate   Billing date
     * @param UnsignedInt $billingPeriod Billing period
     * @param UnsignedInt $billingDay    Billing day
     *
     * @return Core_BillingDate
     */
    protected static function raiseAutoproblemAndGuessBillingDay(
        UnsignedInt $serviceId,
        I18n_Date   $billingDate,
        UnsignedInt $billingPeriod,
        UnsignedInt $billingDay
    ) {
        $guessedDay = new UnsignedInt(substr($billingDate->toMySql(), 8, 2));

        if (!self::$_suppress) {
            $autoProblemClient = BusTier_BusTier::getClient('autoproblem');
            $autoProblem = $autoProblemClient->prepareAutoProblem(
                'invalidBillingDateAndDay',
                array(
                    'serviceId'     => $serviceId->getValue(),
                    'billingDate'   => $billingDate->toMySql(),
                    'billingPeriod' => $billingPeriod->getValue(),
                    'billingDay'    => $billingDay->getValue(),
                    'guessedDay'    => $guessedDay->getValue()
                ),
                Auth_BusinessActor::get(self::SCRIPT_USER_ACTOR_ID)
            );
            $autoProblemId = $autoProblem->raiseProblem();
        }

        return new Core_BillingDate($billingDate, $billingPeriod, $guessedDay);
    }
}
