<?php
/**
 * Core ServicePeriod DayIterator
 *
 * @package    Core
 * @subpackage ServicePeriod
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2010 PlusNet
 * @version    git
 * @link       Projects/Core/PDD
 * @since      File available since 2010-06-21
 */

/**
 * Core ServicePeriod DayIterator
 *
 * Take a Core_ServicePeriod object and iterate over it, incrementing a day each time
 * until we reach the last day. The last day will be the end time of the service period
 * end date.
 *
 * For example:
 *              $spIterator = Core_ServicePeriod_DayIterator::fromStartAndEnd(
 *                  I18n_Date::fromString('2010-01-15 04:26:13'),
 *                  I18n_Date::fromString('2010-01-19 04:23:42')
 *              );
 *
 * This will produce the dates (as I18n_Date objects):
 *
 *              '2010-01-15 04:26:13',
 *              '2010-01-16 00:00:00',
 *              '2010-01-17 00:00:00',
 *              '2010-01-18 00:00:00',
 *              '2010-01-19 04:23:42'
 *
 * N.B.: DO NOT change this behaviour. WorkOutDistinctInAndOutDailyUsage depends on
 *       it being this way. If you really need it to produce output differently then
 *       do something like add class constants for the iteration mode and set
 *       self::AT_MIDNIGHT as the default
 *
 * @package    Core
 * @subpackage ServicePeriod
 * <AUTHOR> Jones <<EMAIL>>
 *
 * @copyright  2010 PlusNet
 * @link       Projects/Core/PDD
 */
class Core_ServicePeriod_DayIterator implements Iterator
{
    /**
     * Service period object representing start and end time
     *
     * @var Core_ServicePeriod
     */
    protected $_servicePeriod;

    /**
     * Dates that we've produced. We store them in here so if we
     * rewind or need to get ->current or some such then we don't
     * need to generate it again
     *
     * @var array
     */
    private $_dates = array();

    /**
     * The last day the period is for. We record this so we don't generate
     * it every time we get a date, since we need to check the current date
     * isn't the last date
     *
     * @var string
     */
    private $_last;

    /**
     * Internal pointer as to which record we're at - i.e. which day of the
     * service period (beginning at 0 of course
     *
     * @var integer
     */
    private $_pointer;

    /**
     * Creates an instance of the Core_SerivcePeriod_DayIterator
     *
     * @param Core_ServicePeriod $servicePeriod Representing the start and end time
     *
     * @return void
     */
    public function __construct(Core_ServicePeriod $servicePeriod)
    {
        $this->_servicePeriod = $servicePeriod;
        $this->_dates[0]      = $servicePeriod->getStart();
        $this->_last          = date('Y-m-d', $servicePeriod->getEnd()->getTimestamp());
        $this->_pointer       = 0;
    }

    /**
     * Return the current date
     *
     * @return I18n_Date
     */
    public function current()
    {
        return $this->getDate($this->_pointer);
    }

    /**
     * Return the next date
     *
     * @return I18n_Date
     */
    public function next()
    {
        return $this->getDate(++$this->_pointer);
    }

    /**
     * Rewind back to the beginning
     *
     * @return void
     */
    public function rewind()
    {
        $this->_pointer = 0;
    }

    /**
     * Determines whether the current record is valid
     *
     * @return boolean
     */
    public function valid()
    {
        return ($this->current() !== false);
    }

    /**
     * Return the key for the current record. In this case it's numeric
     *
     * @return integer|boolean
     */
    public function key()
    {
        return ($this->valid())
            ? $this->_pointer
            : false;
    }

    /**
     * Flags whether this is the last record.
     *
     * @return boolean
     */
    public function isLast()
    {
        return (!$this->valid() || $this->getDate($this->_pointer + 1) === false);
    }

    /**
     * Does the hard work of figuring out what the date should be for the given
     * offset.
     *
     * @param integer $offset Get day from offset
     *
     * @return I18n_Date|false
     */
    protected function getDate($offset)
    {
        if (!isset($this->_dates[$offset])) {
            $timeToMake = strtotime($this->getDate($offset - 1));
            $newTime    = mktime(0, 0, 0, date('m', $timeToMake), date('d', $timeToMake) + 1, date('Y', $timeToMake));

            // If the new time we're constructing is before the last day use that time
            if ($newTime < strtotime($this->_last)) {
                $this->_dates[$offset] = I18n_Date::fromTimestamp($newTime);
            }

            // If the new time is the last day then use the service period end
            if ($newTime == strtotime($this->_last)) {
                $this->_dates[$offset] = $this->_servicePeriod->getEnd();
            }

            if ($newTime > strtotime($this->_last)) {
                $this->_dates[$offset] = false;
            }
        }

        return $this->_dates[$offset];
    }
}
