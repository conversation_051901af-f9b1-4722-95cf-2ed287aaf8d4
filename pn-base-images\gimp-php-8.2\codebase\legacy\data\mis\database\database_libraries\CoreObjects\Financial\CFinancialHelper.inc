<?php
/**
* Helper class for financial calculations
*
* This is the only place in the codebase where these calculations should be performed.
* Do not duplicate defined values such as VAT %
*
* @package    Core
* @subpackage Financial
*/

require_once("/local/data/mis/database/database_libraries/CoreObjects/CObject/CObject.inc");
define("VAT_NOW_IN_CFINANCIALHELPER",'1');

/**
* Helper class for financial calculations
*
* @package    Core
* @subpackage Financial
*/
class CFinancialHelper extends CObject
{
	/**
	 * @var Financial_VatRate
	 */
	private static $vatRate;

	/**
	 * getVatRate
	 *
	 * @return void
	 */
	private static function getVatRate()
	{
		if (is_null(self::$vatRate)) {
			$schedule = new Financial_VatRateSchedule();
			self::$vatRate = $schedule->getCurrent();
		}

		return self::$vatRate;
	}

	/**
	 * set vatRate to avoid call Financial_VatRateSchedule::getCurrent in tests
     *
     * @param float $floVatRate
	 *
	 * @return void
	 */
    public function setVatRate($floVatRate)
    {
        self::$vatRate = new Financial_VatRate($floVatRate);
    }

	/**
	 * Calculate the VAT element on a net amount and return the gross amount and the vat element
	 *
	 * Returns an array with 4 elements:
	 *    'exvat', 'incvat' and 'vat', all in pence for display.
	 *    'vatrate' -- current rate of VAT
	 * For total float accuracy, set the bolFloatAccuracy flag to true to return precise values.
	 * Currently uses php round() method for converting float to int accuracy.  This could be improved.
	 *
	 * @param int $intExVatPence ex. VAT amount in pence
	 * @param bool $bolFloatAccuracy Round to nearest number of pence
	 * @return array
	 */
	public static function calcVat($intExVatPence, $bolFloatAccuracy = false)
	{
		$arrReturn = array();

		if($intExVatPence < 0) {
			return new CError(__FILE__, __LINE__, "Cannot calculate VAT element on a negative gross amount.");
		}

		$vatRate = self::getVatRate();

		$arrReturn['exvat']   = $intExVatPence;
		$arrReturn['vat']     = $vatRate->calcVatPence($intExVatPence);
		$arrReturn['incvat']  = $arrReturn['exvat'] + $arrReturn['vat'];
		$arrReturn['vatrate'] = $vatRate->toFloat();

		if (!$bolFloatAccuracy) {
			$arrReturn['vat']    = intval(round($arrReturn['vat']));
			$arrReturn['incvat'] = intval(round($arrReturn['incvat']));
		}

		return $arrReturn;
	}

	/**
	 * Caclulate the VAT element on a gross amount and return the net amount and the vat element
	 *
	 * Returns an array with 4 elements:
	 *    'incvat', 'exvat' and 'vat', all in pence for display.
	 *    'vatrate' -- current rate of VAT
	 * For total float accuracy, set the bolFloatAccuracy flag to true
	 * Currently uses php round() method for converting float to int accuracy.  This could be improved.
	 *
	 * @param int $intIncVatPence inc. VAT amount in pence
	 * @param bool $bolFloatAccuracy round to whole number of pence
	 * @return array
	 */
	public static function splitVat($intIncVatPence, $bolFloatAccuracy = false)
	{
		$vatRate = self::getVatRate();

		$arrReturn = array();
		$arrReturn['incvat']  = $intIncVatPence;
		$arrReturn['vat']     = $vatRate->splitVatPence($intIncVatPence);
		$arrReturn['exvat']   = $intIncVatPence - $arrReturn['vat'];
		$arrReturn['vatrate'] = $vatRate->toFloat();

		if (!$bolFloatAccuracy) {
			$arrReturn['exvat'] = intval(round($arrReturn['exvat']));
			$arrReturn['vat']   = intval(round($arrReturn['vat']));
		}

		return $arrReturn;
	}

	/**
	 * Returns the current VAT rate
	 *
	 * @return float
	 */
	public static function getCurrentVatRate()
	{
		return self::getVatRate()->toFloat();
	}
}
