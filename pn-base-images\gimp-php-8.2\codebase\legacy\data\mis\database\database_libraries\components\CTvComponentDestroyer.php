<?php

/**
 * Contains methods related to auto destroy feature of YouView component.
 *
 * @category LegacyCodebase
 * @package  LegacyCodebase
 * <AUTHOR> <<EMAIL>>
 */
class CTvComponentDestroyer
{
    private $cTvComponent;

    /**
     * CTvComponentDestroyer constructor.
     * @param int $componentId Component ID
     */
    public function __construct($componentId)
    {
        $this->cTvComponent = CComponent::createInstance($componentId);
    }

    /**
     * Destroys the current TV Component
     *
     * @return boolean
     */
    public function destroyCComponent()
    {
        if (in_array(
            $this->cTvComponent->getStatus(),
            array(CComponent::STATUS_UNCONFIGURED, CComponent::STATUS_ACTIVE, CComponent::STATUS_DEACTIVE)
        )) {
            return $this->cTvComponent->destroy();
        }
        return false;
    }

    /**
     * @param CComponent $cTvComponent Component ID
     * @return void
     */
    public function setCTvComponent($cTvComponent)
    {
        $this->cTvComponent = $cTvComponent;
    }
}
