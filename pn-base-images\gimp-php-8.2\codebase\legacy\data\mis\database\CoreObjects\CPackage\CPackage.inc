<?php
/**
* Class for common functionality between generic packages
*/
class CPackage
{
	var $m_strPackage = 'CPackage Core';
	
	/**
	* Event Handler properties
	*/
	var $m_arrEvent = array();
	var $m_arrEventPointer = array('intGetPointer' => 0,
	                               'intRaisePointer' => 0);
	
	/**
	* Event Handler
	*
	* Functionality for handling events. eventRaise can be over-ridden in the sub-class
	*/
	
	/**
	* eventAdd - Add an event to the object
	*
	* Add an event to the handler. Pass an array of arguments to be attached to the
	* event. The array can also contain key arguments, which will be assigned in as
	* special keys to the event array. These are as follows:
	* 	strMessage: A descriptive message of the event
	*
	* <AUTHOR> <<EMAIL>>
	* @modifier "<PERSON><PERSON>" <<EMAIL>>
	* @access	public
	* @param	array   	Array of arguments to attach to the event
	* @param	string  	File where the event took place (referenced by __FILE__)
	* @param	integer 	Line where the event took place (referenced by __LINE__)
	* @return	boolean
	*/
	function eventAdd($arrArgs, $strFile = NULL, $intLine = NULL)
	{
		// Get the index of the new event
		$intEventIndex = count($this->m_arrEvent);
		
		//setting key field 
		if(in_array('strMessage', array_keys($arrArgs))) {

			//if this variable is set, the message passed in it will populate 
			//"Event description" field in an autoproblem raised
			$this->m_arrEvent[$intEventIndex]['strMessage'] = $arrArgs['strMessage'];

			//unsetting message, so it will not appear for the second time with all other data
			//passed in the array 
			unset($arrArgs['strMessage']);
		}
		
		$this->m_arrEvent[$intEventIndex]['arrArgs'] = $arrArgs;
		$this->m_arrEvent[$intEventIndex]['strFile'] = $strFile;
		$this->m_arrEvent[$intEventIndex]['intLine'] = $intLine;
		$this->m_arrEvent[$intEventIndex]['intEventIndex'] = $intEventIndex;

		return TRUE;
	} // function eventAdd($arrArgs, $strFile = NULL, $intLine = NULL)

	/**
	* eventCheck - Check if an event has occurred
	*
	* Checks to see if an event has occurred at all. This is not necessarily caused by
	* the last operation that was performed. Any operation may have caused it within the
	* scope of the Object.
	*
	* <AUTHOR> Jones" <<EMAIL>>
	* @access	public
	* @return	boolean 	TRUE if an event occurred
	*/
	function eventCheck()
	{
		if (! empty($this->m_arrEvent))
		{
			return TRUE;
		}
		else
		{
			return FALSE;
		}
	} // function eventCheck()
	
	/**
	* eventGet - Retrieve an event
	*
	* Retrieves an event specified by the event number supplied, or FALSE if the number
	* is out-of-range. If no event number is supplied, then an internal pointer will be 
	* used to cycle through the events, so eventGet can be called repeatedly until a
	* FALSE is returned, denoting there are no more events.
	* 
	* <AUTHOR> Jones" <<EMAIL>>
	* @access	public
	* @param	integer		The number of the event to retrieve
	* @return	array   	An associative array of the event parameters
	*/
	function eventGet($intEvent = NULL)
	{
		$intTotalEvents = count($this->m_arrEvent);
		
		// User provided event number. Check it's valid and return the event
		if (isset($intEvent))
		{
			if ($intEvent >= 0 && $intEvent <= $intTotalEvents - 1)
			{
				return $this->m_arrEvent[$intEvent];
			}
			else
			{
				return FALSE;
			}
		} // if (isset($intEvent))

		// Return the event by pointer
		return $this->m_arrEvent[$this->_nextEvent('GET')];
	} // function eventGet($intEvent = NULL)

	/**
	* eventGetAll - Retrieve all events
	*
	* Returns an array of associative arrays containing each event's parameters.
	* Basically, it's just the member variable that is populated by the eventAdd
	* function.
	*
	* <AUTHOR> Jones" <<EMAIL>>
	* @access	public
	* @return	array   	Array of associative arrays of the event parameters
	*/
	function eventGetAll()
	{
		return $this->m_arrEvent;
	}

	/**
	* eventRaise - Raise an event
	*
	* Raises an event specified by an event number, returning FALSE if the event number
	* is out of range. Otherwise it will use an internal pointer to cycle through events
	* like eventGet (though the pointer is independent of eventGet).
	* You can override this function in any class extended from CPackage to use tailored
	* event handling for each sub-class. Might be useful to, say, store the parameters in
	* a database and try to do the operation again later via a cron script (depending on
	* the nature of the event, of course).
	*
	* <AUTHOR> Jones" <<EMAIL>>
	* @access	public
	* @param	array   	An associative array of extra arguments
	* @param	integer 	The event number to raise
	* @return	boolean  	TRUE if the event was raised successfully
	*/
	function eventRaise($arrArgsExtra = NULL, $intEvent = NULL)
	{		
		$intTotalEvents = count($this->m_arrEvent);
		
		if (isset($intEvent))
		{
			// User supplied event number. Check valid
			if ($intEvent >= 0 && $intEvent <= $intTotalEvents - 1)
			{
				$intEventIndex = $intEvent;
			}
			else
			{
				return FALSE;
			}
		} // if (isset($intEvent))
		else
		{
			// Use internal pointer
			if (! ($intEventIndex = $this->_nextEvent('RAISE')))
			{
				return FALSE;
			}
		} // !(if (isset($intEvent)))

		$strProblemTitle = 'Package Error - ' . $this->m_strPackage;
		$strDescription  = 'The CPackage Event Handler was raised the following event:\n\n';
		
		$arrEvent = $this->eventGet($intEventIndex);
		
		$strVerbose  = "Library Data:\n";
		
		foreach ($arrEvent['arrArgs'] as $strKey => $mxdValue)
		{
			$strVerbose .= "$strKey: $mxdValue\n";
		}

		$strVerbose .= "Event Description:\n" . $arrEvent['strMessage'] . "\n" .
		               'Library: ' . $arrEvent['strFile'] . "\n" .
		               'Line: ' . $arrEvent['intLine'] . "\n";
		
		if (count($arrArgsExtra) > 0)
		{
			$strVerbose .= "\nExecutor Data:\n";
		
			foreach ($arrArgsExtra as $strKey => $mxdValue)
			{
				switch ($strKey)
				{
					case 'strFile':
						$strFile = $mxdValue;
						break;
	
					case 'intLine':
						$intLine = $mxdValue;
						break;

					case 'strProblemGroup':
						$strProblemGroup = $mxdValue;
						break;
					
					default:
						$strVerbose .= "$strKey: $mxdValue\n";
						break;
				} // switch ($strKey)
			} // foreach ($arrArgs as $strKey => $mxdValue)
		
			if (isset($strFile)) { $strVerbose .= "File: $strFile\n"; }
			if (isset($intLine)) { $strVerbose .= "Line: $intLine\n"; }
		} // if ($count($arrArgsExtra) > 0)
		
		// No problem group override.
		if (! isset($strProblemGroup) || $strProblemGroup == '')
		{
			$strProblemGroup = 'undefined';
		}
		
		pt_raise_autoproblem($strProblemGroup, 'AUTO-PROBLEM: ' . $strProblemTitle, $strDescription, $strVerbose);

		return TRUE;
	} // function eventRaise($intEvent = NULL)

	/**
	* eventRaiseAll - Raise all events
	*
	* Raise all events in the stack to the handler. Be sure this is really what you
	* want to do, as often events are generated on the same operation, and it is really
	* only the first, or last, event you want to raise.
	*
	* <AUTHOR> Jones" <<EMAIL>>
	* @access	public
	* @param	array   	Array of additional arguments to supply to the handler
	* @return	boolean 	TRUE if all events were raised successfully
	*/
	function eventRaiseAll($arrArgsExtra = array())
	{
		$bolFailures = FALSE;
		
		foreach ($this->m_arrEvent as $arrEvent)
		{
			if (! $this->eventRaise($arrArgsExtra))
			{
				$bolFailures = TRUE;
			}
		}

		if ($bolFailures == FALSE)
		{
			return TRUE;
		}
		else
		{
			return FALSE;
		}
	} // function eventRaiseAll($arrArgsExtra = array())

	/**
	* eventClear - Clear an event
	*
	* Clears an event from the stack. WARNING!!! This will cause all events later in the
	* stack to move up one place. If no number is specified, it will clear the earliest
	* event. Event pointers will be adjusted if necessary
	*
	* <AUTHOR> Jones" <<EMAIL>>
	* @access	public
	* @param	integer 	Event number to clear
	* @return	boolean  	TRUE if the event was successfully cleared
	*/
	function eventClear($intEvent = NULL)
	{
		$intTotalEvents = count($this->m_arrEvent);

		if (isset($intEvent))
		{
			if ($intEvent >= 0 && $intEvent <= $intTotalEvents - 1)
			{
				return FALSE;
			}
		}
		else
		{
			$intEvent = 0;
		}

		if ($this->m_arrEvent = array_splice($this->m_arrEvent, $intEvent))
		{
			// Correct the pointers, or eventGet and eventRaise could skip events
			foreach ($this->m_arrEventPointer as $strPointer => $intPointerEvent)
			{
				if ($intEvent < $intPointerEvent)
				{
					$this->m_arrEventPointer[$strPointer]--;
				}
			}
		}
		else
		{
			return FALSE;
		}
		
		return TRUE;
	} // function eventClear($intEvent = NULL)

	/**
	* eventClearAll - Clear all events
	*
	* Clear all events (by blanking the event array)
	*
	* <AUTHOR> Jones" <<EMAIL>>
	* @access	public
	* @return	boolean 	TRUE on success (this can't fail, really)
	*/
	function eventClearAll()
	{
		$this->m_arrEvent = array();

		// Better reset the pointers too!
		foreach ($this->m_arrEventPointer as $strPointer => $intPointerEvent)
		{
			$this->m_arrEventPointer[$strPointer] = 0;
		}
			
		return TRUE;
	}

	/**
	* nextEvent - Get the event number of the next event indicated by the pointer
	*
	* <AUTHOR> Jones" <<EMAIL>>
	* @access	private
	* @param	string  	Pointer handle either GET or RAISE
	* @return	boolean  	
	*/
	function _nextEvent($strPointer)
	{
		switch ($strPointer)
		{
			case 'GET':
				$strPointerHandle = 'intGetPointer';
				break;

			case 'RAISE':
				$strPointerHandle = 'intRaisePointer';
				break;

			default:
				return FALSE;
				break;
		} // switch ($strPointer)

		$intTotalEvents = count($this->m_arrEvent);

		if ($this->m_arrEventPointer[$strPointerHandle] < $intTotalEvents)
		{
			$intEventIndex = $this->m_arrEventPointer[$strPointerHandle];
			$this->m_arrEventPointer[$strPointerHandle]++;

			return $intEventIndex;
		}
		else
		{
			return FALSE;
		}
	}
} // class CPackage
?>
