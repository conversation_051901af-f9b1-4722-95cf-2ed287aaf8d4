<?php
/**
 * EllacoyaManager
 *
 * Class for adding and maintaining Ellacoya profiles on the Ellacoyas.
 *
 * @package  SoapSession
 * @uses     SoapSession
 * <AUTHOR> <<EMAIL>>
 * @filesource
 */
configDefineIfMissing(
    'ELLACOYA_MANAGER_WSDL',
    'http://ellacoya.businesstier.plus.net:8080/EllacoyaSubscriberSynchroniserWsdl/SubscriberService.wsdl'
);
configDefineIfMissing('ELLACOYA_COMM_LOG', '/share/admin/log/EllacoyaSubscriber.log');

class EllacoyaManager extends SoapSession
{
    /**
     * @access private
     * @var    string
     */
    var $m_strPackage = 'Ellacoya Subscriber Manager';

    /**
     * @access private
     * @var    string
     */
    var $m_strProblemGroup = 'EllacoyaSubscriberError';

    /**
     * @access private
     * @var    string
     */
    var $m_fphLog = 0;

    /**
     * addSubscriber
     *
     * Add a new subscriber to the Ellacoya system
     *
     * <AUTHOR> <<EMAIL>>
     * @access public
     * @param  string
     * @param  integer
     * @param  integer
     * @param  integer
     * @return boolean
     */
    function addSubscriber($strUsername, $strProfile, $intServiceID, $intSDI, $intBillingDay)
    {
        $strUsername = $this->convertSuffixedUsernameRealm($strUsername);

        $this->_strAction = 'AddSubscriber';

        // Make sure that the ServiceID passed is original (not teporary) ServiceID.
        // If it is temporary ServiceID then set intServiceID to original one.
        $dbhConnection=get_named_connection_with_db ("userdata");
        $strQuery="SELECT service_id FROM adsl_upgrades WHERE holding_adsl_service_id='$intServiceID' ORDER BY adsl_upgrade_id DESC LIMIT 1";
        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);
        $mixResult = PrimitivesResultGet($resResult, 'service_id');
        $strMoreInfo = '';
        if ($mixResult) {
            $intServiceID = $mixResult;
            $strMoreInfo = '(temporary account detected, fetched parent: '. $intServiceID. ')';
        }
        settype($intServiceID, 'integer');

        if (empty($strUsername) || empty($intSDI) || empty($intBillingDay) || empty($intServiceID) || empty($intBillingDay))
        {
            $this->eventAdd(array('strMessage' => "addSubscriber is missing data to perform the operation ". $strMoreInfo,
                                  'strUsername' => $strUsername,
                                  'strEllacoyaProfile' => $strProfile,
                                  'intServiceID' => $intServiceID,
                                  'intSDI' => $intSDI,
                                  'intBillingDay' => $intBillingDay),
            __FILE__, __LINE__);

            return false;
        } // if ($strUsername == '' || $intSDI == '' || $intBillingDay == '' || $intServiceID == '' || $intBillingDay == '')


        if ($strProfile == '')
        {
            // None-set Profile should not be a fail condition because it's not important
            $strProfile = 'Generic';
        }

        if (! $objSubscriberClient = $this->getSoapClient(ELLACOYA_MANAGER_WSDL))
        {
            // Event carried from getSoapClient
            return false;
        }

        $this->_ellacoyaLog('addSubscriber', array($strUsername, $strProfile, $intServiceID, $intSDI, $intBillingDay));
        if (! $bolResult = $this->callSoapClient('addSubscriber',
        array($this->_arrSession, $strUsername, $strProfile, $intServiceID, $intSDI, $intBillingDay),
        $objSubscriberClient))
        {

            // Event carried from callSoapClient
            return false;
        }

        return $bolResult;
    }

    /**
     * updateSubscriber
     *
     * Update an Ellacoya subscriber with new details
     *
     * <AUTHOR> Jones" <<EMAIL>>
     * @return boolean
     */
    function updateSubscriber($strUsername, $strEllacoyaProfileName, $intServiceID, $intSDI, $intBillingDay)
    {
        $strUsername = $this->convertSuffixedUsernameRealm($strUsername);

        $this->_strAction = 'UpdateSubscriber';

        // Make sure that the ServiceID passed is original (not teporary) ServiceID.
        // If it is temporary ServiceID then set intServiceID to original one.
        $dbhConnection=get_named_connection_with_db ("userdata");
        $strQuery="SELECT service_id FROM adsl_upgrades WHERE holding_adsl_service_id='$intServiceID' ORDER BY adsl_upgrade_id DESC LIMIT 1";
        $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);
        $arrResult = PrimitivesResultsAsArrayGet($resResult);
        $strMoreInfo = '';
        if (count($arrResult)) {
            $intServiceID = $arrResult['service_id'];
            $strMoreInfo = '(temporary account detected, fetched parent: '. $intServiceID. ')';
        }
        settype($intServiceID, 'integer');

        if ($strUsername == '' || $intServiceID == 0 || $intSDI == '' || empty($intBillingDay))
        {
            $bolNullBD = is_null($intBillingDay);

            $this->eventAdd(array('strMessage' => "updateSubscriber is missing data to perform the operation".
            ($bolNullBD? ", passing NULL billing day is DEPRECATED!" : ""). $strMoreInfo,
                                  'strUsername' => $strUsername,
                                  'strEllacoyaProfileName' => $strEllacoyaProfileName,
                                  'intServiceID' => $intServiceID,
                                  'intSDI' => $intSDI,
                                  'intBillingDay' => $intBillingDay),
            __FILE__, __LINE__);

            return false;
        } // if ($strUsername == '' || $intServiceID == '' || $intSDI == '' || empty($intBillingDay))

        // Make sure Service ID is valid and has no leading zeros
        $intServiceID = intval($intServiceID);

        $bolNumericServiceID = is_numeric($intServiceID);
        $bolNumericSDI = is_numeric($intSDI);
        $bolValidBillingDay = is_numeric($intBillingDay) && (0 < $intBillingDay) && (32 > $intBillingDay);
        if (!$bolNumericServiceID || !$bolNumericSDI || !$bolValidBillingDay) {
            $this->eventAdd(array('strMessage' => "invalid parameters passed ".
            ($bolNumericServiceID? "" : " non numeric serviceId").
            ($bolNumericSDI? "" : " non numeric SDI").
            ($bolValidBillingDay? "" : " invalid billing day"),
                                  'strUsername' => $strUsername,
                                  'strEllacoyaProfileName' => $strEllacoyaProfileName,
                                  'intServiceID' => $intServiceID,
                                  'intSDI' => $intSDI,
                                  'intBillingDay' => $intBillingDay),
            __FILE__, __LINE__);

            return false;
        } // if (!$bolNumericServiceID || !$bolNumericSDI || !$bolValidBillingDay)

        if ($strEllacoyaProfileName == '')
        {
            // None-set Profile should not be a fail condition because it's not important
            $strEllacoyaProfileName = 'Generic';
        }

        if (! $objSubscriberClient = $this->getSoapClient(ELLACOYA_MANAGER_WSDL))
        {
            return false;
        }

        $this->_ellacoyaLog('updateSubscriberWithBillingDay', array($strUsername, $strEllacoyaProfileName, $intServiceID, $intSDI, $intBillingDay));
        if (! $bolResult = $this->callSoapClient('updateSubscriberWithBillingDay',
        array($this->_arrSession, $strUsername, $strEllacoyaProfileName, $intServiceID, $intSDI, $intBillingDay),
        $objSubscriberClient))
        {
            return false;
        }

        if(!cbcMarkAsUnsquashed($intServiceID)) {

            $this->eventAdd(array(
                'strMessage'   => 'Failed to mark service as unsquashed',
                'intServiceID' => $intServiceID
            ));

            return FALSE;
        }

        return $bolResult;
    }

    /**
     * applyFbSoftRestriction
     *
     * @param int $intServiceId Customer's service id
     *
     * @return boolean
     */
    function applyFbSoftRestriction($intServiceId)
    {
        $service = userdata_service_get($intServiceId);
        $product = product_get_service($service['type']);
        $visp = product_visp_config_get($product['isp']);
        $brand = strtoupper($visp['short_brand']);

        $profile = 'Redirect_FB_PN_Res_1';
        if ($brand == 'JL') {
            $profile = 'Redirect_FB_' . $brand . '_Res_1';
        }

        $userRealm = adslGetUserRealm($intServiceId);
        $intServiceId = intval($intServiceId);

        if (!$objSubscriberClient = $this->getSoapClient(ELLACOYA_MANAGER_WSDL)) {
            return false;
        }

        $this->_ellacoyaLog(
            'applyFbSoftRestriction',
            array($intServiceId, $userRealm, $profile)
        );

        if (! $bolResult = $this->callSoapClient(
            'applyProfileToSubscriberBatch',
            array($this->_arrSession, $this->_getServiceIdBatch(array($intServiceId)), $profile),
            $objSubscriberClient
        )) {
            return false;
        }

        return $bolResult;
    }

    /**
     * applyFbHardRestriction
     *
     * @param integer $intServiceId Customer's service id
     * @param string  $sector       The sector the customer is currently in
     *
     * @return boolean
     */
    function applyFbHardRestriction($intServiceId, $sector)
    {
        $service = userdata_service_get($intServiceId);
        $product = product_get_service($service['type']);
        $visp = product_visp_config_get($product['isp']);
        $brand = strtoupper($visp['short_brand']);

        $profile = 'Redirect_FB_PN_';
        if ($brand == 'JL') {
            $profile = 'Redirect_FB_' . $brand . '_';
        }

        switch($sector) {
            case 'business':
                $profile .= 'Biz_2';
                break;
            case 'residential':
            default:
                $profile .= 'Res_2';
                break;
        }

        $userRealm = adslGetUserRealm($intServiceId);
        $intServiceId = intval($intServiceId);

        if (! $objSubscriberClient = $this->getSoapClient(ELLACOYA_MANAGER_WSDL)) {
            return false;
        }

        $this->_ellacoyaLog(
            'applyFbHardRestriction',
            array($intServiceId, $userRealm, $profile)
        );

        if (! $bolResult = $this->callSoapClient(
            'applyProfileToSubscriberBatch',
            array($this->_arrSession, $this->_getServiceIdBatch(array($intServiceId)), $profile),
            $objSubscriberClient
        )) {
            return false;
        }

        return $bolResult;
    }

    /**
     * Method changes billing day
     *
     * @access public
     * <AUTHOR> Bargiel <<EMAIL>>
     *
     * @param int $intServiceID Service Id
     * @param int $intBillingDay New billing day
     *
     * @return boolean
     */
    function changeBillingDay($intServiceID, $intBillingDay)
    {
        $this->_strAction = 'changeBillingDay';

        if ($intServiceID == '' || $intBillingDay == '')
        {
            $this->eventAdd(array('strMessage' => "changeBillingDay is missing data to perform the operation",
                                  'intServiceID' => $intServiceID,
                                  'intBillingDay' => $intBillingDay),
            __FILE__, __LINE__);

            return false;
        } // if ($intServiceID == '' || $intBillingDay == '')

        // Make sure Service ID is valid and has no leading zeros
        $intServiceID = intval($intServiceID);

        if (!$objSubscriberClient = $this->getSoapClient(ELLACOYA_MANAGER_WSDL))
        {
            return false;
        }
        $this->_ellacoyaLog('changeBillingDay', array($intServiceID, $intBillingDay));
        if (!$bolResult = $this->callSoapClient('changeBillingDay',
        array($this->_arrSession, $intServiceID, $intBillingDay),
        $objSubscriberClient))
        {
            return false;
        }

        // Everything is OK
        return true;

    } // End of method changeBillingDay()

    /**
     * updateRealm
     *
     * Update an Ellacoya subscriber with new details
     *
     * <AUTHOR> Jones" <<EMAIL>>
     * @access public
     * @param  string  $strOldUsername Old User name
     * @param  string  $strNewUsername New user name
     * @param  integer $intServiceID Customer service id
     *
     * @return boolean
     */
    function updateRealm($strOldUsername, $strNewUsername, $intServiceID)
    {
        $strOldUsername = $this->convertSuffixedUsernameRealm($strOldUsername);
        $strNewUsername = $this->convertSuffixedUsernameRealm($strNewUsername);

        $this->_strAction = 'UpdateRealm';

        if ($strOldUsername == '' || $strNewUsername == '' || $intServiceID == '')
        {
            $this->eventAdd(array('strMessage' => "updateRealm is missing data to perform the operation",
                                  'strOldUsername' => $strOldUsername,
                                  'strNewUsername' => $strNewUsername,
                                  'intServiceID' => $intServiceID),
            __FILE__, __LINE__);

            return false;
        } // if ($strOldUsername == '' || $strNewUsername == '' || $intServiceID == '')

        // Make sure Service ID is valid and has no leading zeros
        $intServiceID = intval($intServiceID);

        if (! $objSubscriberClient = $this->getSoapClient(ELLACOYA_MANAGER_WSDL))
        {
            return false;
        }

        $this->_ellacoyaLog('updateRealm', array($strOldUsername, $strNewUsername, $intServiceID));
        if (! $bolResult = $this->callSoapClient('updateRealm',
        array($this->_arrSession, $strOldUsername, $strNewUsername, $intServiceID),
        $objSubscriberClient))
        {
            return false;
        }
    }

    /**
     * addIpBlock
     *
     * Add an IP block to the Ellacoya subscriber
     *
     * By default this removes any existing blocks,
     * setting $bolMultiple to TRUE will allow multiple blocks
     *
     * <AUTHOR> Jones" <<EMAIL>>
     * @access public
     * @param	string
     * @param	string
     * @param	integer
     * @return	boolean
     */
    function addIpBlock($strUsername, $strIPAddress,
    $intBlockSize, $bolMultiple = FALSE)
    {
        $strUsername = $this->convertSuffixedUsernameRealm($strUsername);

        $this->_strAction = 'AddIPBlock';

        if ($strUsername == '' || $strIPAddress == '' || $intBlockSize == '')
        {
            $this->eventAdd(array('strMessage' => "addIpBlock is missing data to perform the operation",
                                  'strUsername' => $strUsername,
                                  'strIPAddress' => $strIPAddress,
                                  'intBlockSize' => $intBlockSize),
            __FILE__, __LINE__);

            return false;
        } // if ($strUsername == '' || $strIPAddress == '' || $intBlockSize == '')

        if ($intBlockSize == 1)
        {
            // Shouldn't do anything for single IP addresses
            return TRUE;
        }

        $subscriberClient = $this->getSoapClient(ELLACOYA_MANAGER_WSDL);

        $this->_ellacoyaLog('addIpBlock', array($strUsername, $strIPAddress, $intBlockSize));

        if($bolMultiple) {
            $blocks = array('ipBlocks' => array(
            array(
                            'ip' => $strIPAddress,
                            'blockSize' => $intBlockSize
            )
            )
            );

            return $this->callSoapClient('addIpBlocks',
            array($this->_arrSession,
            $strUsername,
            $blocks),
            $subscriberClient);
        } else {
            return $this->callSoapClient('addIpBlock',
            array($this->_arrSession,
            $strUsername, $strIPAddress,
            $intBlockSize),
            $subscriberClient);
        }
    }

    /**
     * Remove an IP block from the ellacoya
     *
     * @return boolean
     */
    public function removeIpBlock($strUsername, $strIpAddress, $intBlockSize)
    {
        $strUsername = $this->convertSuffixedUsernameRealm($strUsername);

        $this->_strAction = __FUNCTION__;

        if(!$strUsername || !$strIpAddress || !$intBlockSize) {
            $this->eventAdd(
            array(
                    'strMessage' => "removeIpBlock is missing data " .
                                    "to perform the operation",
                    'strUsername' => $strUsername,
                    'strIPAddress' => $strIpAddress,
                    'intBlockSize' => $intBlockSize
            ),
            __FILE__, __LINE__
            );

            return FALSE;
        }

        $subscriberClient = $this->getSoapClient(ELLACOYA_MANAGER_WSDL);

        $this->_ellacoyaLog('removeIpBlock',
        array($strUsername, $strIpAddress, $intBlockSize));

        return $this->callSoapClient('removeIpBlock',
        array($this->_arrSession, $strUsername,
        $strIpAddress, $intBlockSize),
        $subscriberClient);
    }

    /**
     * removeSubscriber
     *
     * <AUTHOR> Jones" <<EMAIL>>
     * @access	public
     * @param	string
     * @return	boolean
     */
    function removeSubscriber($strUsername)
    {
        $strUsername = $this->convertSuffixedUsernameRealm($strUsername);

        $this->_strAction = 'RemoveSubscriber';

        if ($strUsername == '')
        {
            $this->eventAdd(array('strMessage' => "removeSubscriber is missing username and realm",
                                  'strUsername' => $strUsername),
            __FILE__, __LINE__);

            return false;
        }

        if (! $objSubscriberClient = $this->getSoapClient(ELLACOYA_MANAGER_WSDL))
        {
            // Event from getSoapClient
            return false;
        }

        $this->_ellacoyaLog('removeSubscriber', array($strUsername));
        if (! $bolResult = $this->callSoapClient('removeSubscriber',
        array($this->_arrSession, $strUsername),
        $objSubscriberClient))
        {
            // Event from callSoapClient
            return false;
        }

        return $bolResult;
    }

    /**
     * New fun methods for BBYW project
     *
     * <AUTHOR> Jones" <<EMAIL>>
     * @access	public
     * @param	array	Array of Service IDs
     * @return	bool	true on success
     *                   or false if exception value on exception received from java
     *                   OR not all customers were processed
     */
    function squashSubscriberBatch($arrServiceId)
    {
        $this->_strAction = 'squashSubscriberBatch';

        if (empty($arrServiceId) || !is_array($arrServiceId)) {

            $this->eventAdd(array('strMessage' => "squashSubscriberBatch was passed an " .
                                                  "invalid service ID array",
                                  'arrServiceId' => print_r($arrServiceId, 1)),
            __FILE__, __LINE__);

            return false;
        }

        foreach($arrServiceId as $intServiceId) {

            if(!$intServiceId || !preg_match('/\d+/', $intServiceId)) {

                $this->eventAdd(array('strMessage' => "squashSubscriberBatch was passed an " .
                                                      "invalid service ID",
                                      'intServiceId' => $intServiceId),
                __FILE__, __LINE__);
                return false;
            }
        }


        if (! $objSubscriberClient = $this->getSoapClient(ELLACOYA_MANAGER_WSDL))
        {
            // Event from getSoapClient
            return false;
        }

        $this->_ellacoyaLog('squashSubscriberBatch', array($arrServiceId));

        if (! $unkResult = $this->callSoapClient('squashSubscriberBatch',
        array($this->_arrSession, $this->_getServiceIdBatch($arrServiceId)),
        $objSubscriberClient))
        {
            // Event from callSoapClient
            return false;
        }

        if(isset($unkResult['message']) && !empty($unkResult['message'])) {

            //java tier returned exception
            $this->eventAdd(array('strMessage' => 'Exception was returned from Soap client: '.$unkResult['message']));
            return FALSE;
        }


        $arrServiceIdsSucceeded = array();
        $arrServiceIdsFailed = array();

        //squashed service ids are stored in $unkResult['results'], in format serviceid=>result
        foreach($unkResult['results'] as $intServiceId => $bolResult) {

            if($bolResult) {

                $arrServiceIdsSucceeded[] = $intServiceId;

            } else {

                $arrServiceIdsFailed[] = $intServiceId;
            }
        }


        // Mark services as squashed in DB

        $db = get_named_connection_with_db('userdata');

        $intBatchSize = 100;
        for($intOffset = 0; $intOffset < count($arrServiceIdsSucceeded); $intOffset += $intBatchSize) {

            $arrServiceIdBatch = array_slice($arrServiceIdsSucceeded, $intOffset, $intBatchSize);
            $arrRows = array();

            foreach($arrServiceIdBatch as $intServiceId) {

                $arrRows[] = "($intServiceId, 1)";
            }

            $strQuery = "REPLACE INTO tblSquashedBandwidth(intServiceId, bolCurrentlySquashed)
                         VALUES " . implode(',', $arrRows);

            $bolSuccess = PrimitivesQueryOrExit($strQuery, $db, 'squashSubscriberBatch', FALSE);

            if(!$bolSuccess) {

                $this->eventAdd(array('strMessage' => 'Query failed',
                                      'strQuery' => $strQuery,
                                      'strError' => mysql_error($db['handle'])),
                __FILE__, __LINE__);
            }

        }

        //some of Service IDs weren't squashed
        if(count($arrServiceIdsFailed) > 0) {

            $this->eventAdd(array('strMessage' => 'Following Service IDs failed to be squashed: '.implode(',', $arrServiceIdsFailed)));

            // Don't return false here, as even though some service IDs have failed to be squashed,
            // we could still have some successes. Calls to eventGetAll() by the client will be able
            // determine this error event, as well as the successful/failed squashes from the
            // $unkResult return result.
        }


        // Handle unkResult, though I've no idea what shape it will be in

        return $unkResult;
    }

    /**
     * restoreDefaultProfile
     *
     * @param integer $intServiceId
     *
     * @return boolean
     */
    function restoreDefaultProfile($intServiceId)
    {
        $this->_strAction = 'restoreDefaultProfile';
        $this->_ellacoyaLog($this->_strAction, array($intServiceId));

        if (empty($intServiceId) || !is_numeric($intServiceId)) {
            $this->eventAdd(
            array(
                    'strMessage' => $this->_strAction . " is missing Service ID, or the provided Service ID was not a numeric",
                    'intServiceId' => $intServiceId
            ),
            __FILE__,
            __LINE__
            );

            return false;
        }

        return $this->callUnsquashSubscriber($intServiceId);
    }

    /**
     * New fun methods for BBYW project
     *
     * <AUTHOR> Jones" <<EMAIL>>
     * @access	public
     * @param	integer	Service ID
     * @return	boolean	True on success
     */
    function unsquashSubscriber($intServiceId)
    {
        $this->_strAction = 'unsquashSubscriber';

        if (empty($intServiceId) || !is_numeric($intServiceId)) {
            $this->eventAdd(
            array(
                    'strMessage' => $this->_strAction . " is missing Service ID, or the provided Service ID was not a numeric",
                    'intServiceId' => $intServiceId
            ),
            __FILE__,
            __LINE__
            );

            return false;
        }

        //Already unsquashed?
        if(!cbcIsUserSquashed($intServiceId))
        {
            //Account is already unsquashed
            return true;
        }

        $unkResult = $this->callUnsquashSubscriber($intServiceId);

        // If an op failed in callUnsquashSubscriber we will have added an event which means eventCheck will
        // return true. If it adds no events the check returns false and we go on to try to mark unsquashed
        if (!$this->eventCheck() && !cbcMarkAsUnsquashed($intServiceId)) {

            $this->eventAdd(array(
                'strMessage'   => 'Failed to mark service as unsquashed',
                'intServiceId' => $intServiceId
            ));

            return $unkResult;
        }

        return $unkResult;
    }

    /**
     * callUnsquashSubscriber
     *
     * @param integer $intServiceId
     * @return void
     */
    protected function callUnsquashSubscriber($intServiceId)
    {
        //Do java stuff
        if (!$objSubscriberClient = $this->getSoapClient(ELLACOYA_MANAGER_WSDL)) {
            // Event from getSoapClient
            return false;
        }

        $this->_strAction = 'unSquashSubscriber';
        $this->_ellacoyaLog($this->_strAction, array($intServiceId));

        if (! $unkResult =
        $this->callSoapClientWithRetry(
        $this->_strAction,
        array($this->_arrSession, $intServiceId),
        3,   // retry 3 times
        10,  // wait 10 secs between retries
        $objSubscriberClient
        )
        ) {
            // Event from callSoapClientWithRetry
            return $unkResult;
        }

        //unsquash unsuccesful
        if (isset($unkResult['result']) && $unkResult['result'] != TRUE) {
            $this->eventAdd(array('strMessage' => 'Unsquash of Service ID ' . $intServiceId . ' was unsuccesful.'));
            return $unkResult;
        }

        //exception was returned from soapclient
        if (isset($unkResult['message']) && !empty($unkResult['message'])) {
            $this->eventAdd(array('strMessage' => 'Exception was returned from Soap client: '.$unkResult['message']));
            return $unkResult;
        }

        return $unkResult;
    }

    /**
     * Prepare complexType for SOAP call
     *
     * @param	array	$arrServiceId
     * @return	array
     */
    function _getServiceIdBatch($arrServiceId)
    {
        return array('serviceIds' => $arrServiceId);
    }

    /**
     * _ellacoyaLog
     *
     * @param	string	$strAction
     * @param	array	$arrArgs
     * @return	void
     */
    function _ellacoyaLog($strAction, $arrArgs)
    {
        // No logging
        if (false === ELLACOYA_COMM_LOG) {
            return true;
        }

        if (! is_resource($this->m_fphLog)) {
            if (file_exists(ELLACOYA_COMM_LOG) && is_writable(ELLACOYA_COMM_LOG)) {
                if (false === ($this->m_fphLog = fopen(ELLACOYA_COMM_LOG, 'a+'))) {
                    error_log('Unable to write to Ellacoya Log [' . ELLACOYA_COMM_LOG . ']');
                    return false;
                }
            } else {
                error_log('Unable to write to Ellacoya Log [' . ELLACOYA_COMM_LOG . ']');
                return false;
            }
        }

        fwrite($this->m_fphLog, '[' . date('Y-m-d H:i:s', time()) . "] [$strAction] ");

        $bolFirst = 1;
        foreach ($arrArgs as $strKey => $mxdValue) {
            if (is_array($mxdValue)) {
                $mxdValue = implode(',', $mxdValue);
            }

            if ($bolFirst == 0) {
                fwrite($this->m_fphLog, ",$mxdValue");
            } else {
                fwrite($this->m_fphLog, $mxdValue);
                $bolFirst = 0;
            }
        }

        fwrite($this->m_fphLog, "\n");
    }

    /**
     * BV customers has their ADSL username suffixed by isp, eg <EMAIL>,
     * Elacoya need to have - replaced with @
     *
     * @param string $strUsername
     *
     * @return string
     */
    public function convertSuffixedUsernameRealm($strUsername)
    {
        // Username don't contain realm, do nothing
        if (false === strpos($strUsername, '@')) {

            return $strUsername;
        }

        // Get username and realm
        $arrUsername = explode('@', $strUsername);

        $strUser = $arrUsername[0];
        $strRealm = $arrUsername[1];

        // Now check is username is suffixed, if not, do nothong
        if (false === strpos($strUser, '-')) {

            return $strUsername;
        }

        // Check is there valid suffix, hypen should be on third place from end
        $strSuffix = strrev($strUser);
        $strSuffix = $strSuffix[2];

        if ('-' <> $strSuffix) {

            return $strUsername;
        }

        // Finally replace - with @
        return (str_replace('-', '@', $strUser) . '@' . $strRealm);
    }

    /**
     * Send an update to Ellacoya to provide the Pro AddOn
     * traffic profile for the customer
     *
     * @param int $serviceId
     * @param string $strOfferName
     *
     * @return bool
     */
    public function setPreferredProfile($serviceId, $strOfferName)
    {
        $subscriberClient = $this->getSoapClient(ELLACOYA_MANAGER_WSDL);

        $this->_strAction = 'setPreferredProfile';
        $this->_ellacoyaLog($this->_strAction, array($serviceId, $strOfferName));

        $bolResult = $this->callSoapClient(
            $this->_strAction,
            array($this->_arrSession, $serviceId, $strOfferName),
            $subscriberClient
        );
        return $bolResult;
    }

    /**
     * Get the current customer profile.
     *
     * @param int $serviceId
     *
     * @return SubscriberProfileDetails
     */
    public function getProfileDetails($serviceId)
    {
        require_once
            '/local/data/mis/common_library_functions/common_application_apis/SubscriberProfileDetails.class.php';
        $subscriberClient = new SoapClient(
            ELLACOYA_MANAGER_WSDL,
            array(
                'classmap' => array(
                    'SubscriberProfileDetails' => "SubscriberProfileDetails"
                )
            )
        );

        $this->_strAction = 'getProfileDetails';
        $this->_ellacoyaLog($this->_strAction, array($serviceId));

        $result = $subscriberClient->getProfileDetails(
            $this->_arrSession, $serviceId
        );
        return $result;
    }

    /**
     * Remove the Pro Add On traffic profile in Ellacoya and
     * return the customer to the relevant product profile
     *
     * @param int $serviceId
     *
     * @return bool
     */
    public function resetToDefaultProfile($serviceId)
    {
        $subscriberClient = $this->getSoapClient(ELLACOYA_MANAGER_WSDL);

        $this->_strAction = 'restoreDefaultProfile';
        $this->_ellacoyaLog($this->_strAction, array($serviceId));

        $bolResult = $this->callSoapClient(
            $this->_strAction,
            array($this->_arrSession, $serviceId),
            $subscriberClient
        );
        return $bolResult;
    }
}
