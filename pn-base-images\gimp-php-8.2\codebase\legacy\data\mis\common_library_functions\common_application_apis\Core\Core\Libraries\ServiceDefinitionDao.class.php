<?php
/**
 * ServiceDefinition DAO class
 * 
 * @package    Core
 * <AUTHOR> <kp<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@plus.net>
 * 
 * @copyright  2009 PlusNet
 * @version    CVS: $Id: ServiceDefinitionDao.class.php,v 1.2 2009-03-26 17:43:02 kp<PERSON><PERSON><PERSON><PERSON><PERSON>ki Exp $
 * @since      File available since 16/03/2009
 */

/**
 * ServiceDefinition DAO class
 * 
 * @package    Core
 * <AUTHOR> <kp<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@plus.net> 
 * @copyright  2009 PlusNet
 */

class Core_ServiceDefinitionDao extends Db_Object
{
	/**
	 * @access protected
	 * @var string Primary key name
	 */
	protected $strPrimaryKey = 'service_definition_id';

	/**
	 * service definition id
	 * @var int
	 */
	protected $service_definition_id = null;
	
	/**
	 * product variant id
	 * @var int
	 */
	protected $intProductVariantId = null;
	
	/**
	 * product name
	 * @var string
	 */
	protected $name = null;
	
	/**
	 * ISP product is on
	 * @var string
	 */
	protected $isp = null;
	
	/**
	 * Minimum charge
	 * @var int
	 */
	protected $minimum_charge = null;
	
	/**
	 * Date when product was created
	 * @var string
	 */
	protected $date_created = null;
	
	/**
	 * 
	 * Product requirements
	 * Current possible values:
	 * | surftime        |
	 * |                 |
	 * | adsl            |
	 * | adsl,not_yearly |
	 * | friaco          |
	 * | adsl,monthly    |
	 * 
	 * @var unknown_type
	 */
	protected $requires = null;
	
	/**
	 * Initial charge
	 * @var int
	 */
	protected $initial_charge = null;
	
	/**
	 * Type
	 * One of:
	 * 'other','free','residential','business','colease','non-corporate','hostmaster'
	 * @var string
	 */
	protected $type = null;
	/**
	 * Is password visible to support ?
	 * Y/N
	 * @var string
	 */
	protected $password_visible_to_support = null;
	
	/**
	 * Date when product was no longer available
	 * @var string
	 */
	protected $end_date = null;
	
	/**
	 * Is product available to signup via portal ?
	 * Y/N
	 * @var string
	 */
	protected $signup_via_portal = null;
	
	/**
	 * Id of BT product
	 * @var int
	 */
	protected $bt_product_id = null;

	/**
	 * True if service definition is an ADSL product
	 * 
	 * @var boolean
	 */
	protected $bolAdsl = null;

	/**
	 * products.tblProductVariant.vchHandle
	 * 
	 * @var string
	 */
	protected $strProductVariant = null;

	/**
	 * products.tblProductFamily.vchHandle
	 * 
	 * @var string
	 */
	protected $strProductFamily = null;

	/**
	 * Getter function 
	 * @return int
	 */
	public function getServiceDefinitionId()
	{
		return $this->service_definition_id;
	}

	/**
	 * Getter function 
	 * @return int
	 */
	public function getProductVariantId()
	{
		return $this->intProductVariantId;
	}

	/**
	 * Getter function 
	 * @return int
	 */
	public function getName()
	{
		return $this->name;
	}

	/**
	 * Getter function 
	 * @return int
	 */
	public function getIsp()
	{
		return $this->isp;
	}

	/**
	 * Getter function 
	 * @return int
	 */
	public function getMinimumCharge()
	{
		return $this->minimum_charge;
	}

	/**
	 * Getter function 
	 * @return int
	 */
	public function getDateCreated()
	{
		return $this->date_created;
	}

	/**
	 * Getter function 
	 * @return int
	 */
	public function getRequires()
	{
		return $this->requires;
	}

	/**
	 * Getter function 
	 * @return int
	 */
	public function getInitialCharge()
	{
		return $this->initial_charge;
	}
	/**
	 * Getter function 
	 * @return int
	 */
	public function getType()
	{
		return $this->type;
	}

	/**
	 * Getter function 
	 * @return int
	 */
	public function getPasswordVisibleToSupport()
	{
		return $this->password_visible_to_support;
	}

	/**
	 * Getter function 
	 * @return int
	 */
	public function getEndDate()
	{
		return $this->end_date;
	}
	
	/**
	 * Getter function 
	 * @return int
	 */
	public function getSignupViaPortal()
	{
		return $this->signup_via_portal;
	}
	
	/**
	 * Getter function 
	 * @return int
	 */
	public function getBtProductId()
	{
		return $this->bt_product_id;
	}
	
	/**
	 * Getter function 
	 * @return int
	 */
	public function getProductVariant()
	{
		return $this->strProductVariant;
	}
	
	/**
	 * Getter function 
	 * @return int
	 */
	public function getProductFamily()
	{
		return $this->strProductFamily;
	}

	/**
	 * Getter function
	 * @return bool
	 */
	public function getBolAdsl()
	{
		return $this->bolAdsl;
	}

	/**
	 * Fetch a single row from <i>products.service_definitions</i> table
	 * 
	 * @access public
	 * @static
	 *  
	 * @uses Db_Object::getObject()
	 * 
	 * <AUTHOR> <<EMAIL>>
	 * 
	 * @param int    $intServiceDefinitionId
	 * @param string $strTransaction
	 * 
	 * @return Core_UserDao
	 */
	public static function get($intServiceDefinitionId, $strTransaction = Db_Manager::DEFAULT_TRANSACTION)
	{
		Dbg_Dbg::write(__CLASS__ . ' ' . __METHOD__.": Fetching service definition for Id $intServiceDefinitionId", 'Core');

		return parent::getObject(get_class(), $intServiceDefinitionId, $strTransaction);
	}
}