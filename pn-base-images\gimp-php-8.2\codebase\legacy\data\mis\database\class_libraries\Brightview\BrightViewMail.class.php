<?php

require_once '/local/data/mis/database/class_libraries/Brightview/BrightViewPasswd.class.php';
require_once '/local/data/mis/database/class_libraries/Brightview/BrightViewMailBox.class.php';
require_once '/local/data/mis/database/crypt_config.inc';
/**
 * class BrightViewMail
 * 
 * This class does NOT represent a single mailbox, it is a container for mailboxes (catchals, forwards, etc).
 * The 'real' mailbox class is BrightViewMailBox - there you have milbox name, password, etc
 *  
 *
 */

class BrightViewMail extends BrightViewPasswd
{
    private $arrMailBoxes = array();
    private $intMailQuota;

    private function __construct()
    {
    }
    
    public function setMailQuota($intMailQuota) 
    {
        $this->intMailQuota = $intMailQuota;
    }
    
    protected static function fromArray($arr)
    {
        $ret = new self();
        $ret->setVars($arr);
        $ret->setMailQuota($arr['mail_quota']);
        return $ret;
    }

    /**
     * function read
     * 
     * A method to iterate over all mailboxes on the account
     * For examples of use see end of BrightViewMailBox.class.php file
     * 
     * @return mailbox object
     */
    public function read()
    {
        if (null == $this->arrMailBoxes) $this->fetchMailBoxes();
        $ret = each($this->arrMailBoxes);
        return $ret[1];
    }
    
    /**
     * Function to work in conjunction with $this->read(); it resets
     * the mailboxes array pointer to the start of array.
     *
     * <AUTHOR> Sourkova <<EMAIL>>
     *
     */
    public function reset()
    {
        if ($this->arrMailBoxes) reset($this->arrMailBoxes);
    }

    /**
     * function getMailBoxes     *
     * 
     * @return an array of mailboxes
     */
    public function getMailBoxes()
    {
        if (null == $this->arrMailBoxes) $this->fetchMailBoxes();
        return $this->arrMailBoxes;
    }

    private function fetchMailBoxes()
    {
        //this query will pick both - pop3 mailboxes and catch-all definitions
        $stmt = BVDB::db()->prepare(
            'SELECT * 
             FROM passwd pw 
             INNER JOIN multipop mp 
               ON mp.mailid = pw.mailid 
             WHERE parentid =  ?'
        );
        $stmt->execute(array($this->getMailId()));

        foreach ($stmt->fetchAll(PDO::FETCH_ASSOC) as $arrMailBox) {
            $this->inToArrMailBoxes(BrightViewMailBox::fromResult($arrMailBox));
        }
    }
    
    private function inToArrMailBoxes(BrightViewMailBox $obj)
    {
        if (is_object($this->arrMailBoxes[$obj->getMailId()])) {
            //catchall was first
            if ($this->arrMailBoxes[$obj->getMailId()]->isCatchAll()) {
                $obj->setCatchAllMultiPopId($this->arrMailBoxes[$obj->getMailId()]->getMultiPopId());
                $this->arrMailBoxes[$obj->getMailId()] = $obj;
                //mailbox was first - set catchall id
            } elseif ($obj->isCatchAll()) {
                $this->arrMailBoxes[$obj->getMailId()]->setCatchAllMultiPopId($obj->getMultiPopId());
                unset($obj);
            } else {
                throw new Exception("Too many records in mulipop: ".print_r($obj, true));
            }
        } else {
            $this->arrMailBoxes[$obj->getMailId()] = $obj;
        }
    }
    
    /**
     * function createNewForward
     * 
     * This function will create a new forward without creating pop3 mailbox
     * To apply forward to existing mailbox use forwardTo method
     *
     * @param string $strAlias
     * @param string $strDomain
     * @param string $strForwardTo
     * @return boolean
     */
    
    public static function createNewForward($strAlias, $strDomain, $strForwardTo)
    {
        $strForwardTo = filter_var($strForwardTo, FILTER_VALIDATE_EMAIL);
        
        if (false === $strForwardTo) {
            throw new Exception('Incorrect email');
        }
        
        if (self::doesEmailExist($strAlias, $strDomain)) throw new Exception("Email $strAlias@$strDomain already exist");
        
        //create virtual relationship
        $stmt = BVDB::db()->prepare('INSERT INTO multipop (mailid, domain, alias, external, status) VALUES (?,?,?,?,?)');
        if (!$stmt->execute(array(0, $strDomain, $strAlias, $strForwardTo, BrightViewMailBox::STATUS_EXTERNAL))) {
            throw new Exception('Failed to create virtual relationship: '.print_r($stmt->errorInfo(), true));
        }
        //all done
        return true;
    }
    
    /**
     * function isDomainActive
     *
     * @param string $strDomain
     * @return boolean
     */
    
    public static function isDomainActive($strDomain)
    {
        //check if there is an active domain
        $stmt = BVDB::db()->prepare('SELECT COUNT(p.username) as intNumberOfDomains FROM cashew.domain d JOIN passwd p ON d.mailid = p.mailid WHERE d.domain = ? AND d.status=1');
        $stmt->execute(array($strDomain));
        return 1 == count($stmt->fetchAll(PDO::FETCH_ASSOC));
    }
    /**
     * Is there a email $strAlias@$strDomain
     *
     * @param string $strAlias
     * @param string $strDomain
     * @return boolean
     */
    public static function doesEmailExist($strAlias, $strDomain)
    {
        $stmt = BVDB::db()->prepare('SELECT mailid FROM multipop WHERE alias = ? AND domain = ?');
        $stmt->execute(array($strAlias, $strDomain));
        
        return (1 == count($stmt->fetchAll(PDO::FETCH_ASSOC)));
    }

    /**
     * function createNewMailbox
     * 
     * Function to create new POP3 mailbox. It hides all the quirks of MAAF mailbox creation
     *
     * @param string $strAlias - mailbox name
     * @param string $strDomain - domain name
     * @param string $strPassswd - password
     * @return mialbox object
     */
    
    public function createNewMailbox($strAlias, $strDomain, $strPassswd)
    {
        if (!self::isDomainActive($strDomain)) throw new Exception('No active domain for mailbox');

        if (self::doesEmailExist($strAlias, $strDomain)) throw new Exception('Email already exist');

        $strPassswd = Crypt_Crypt::encrypt($strPassswd, 'maafDes');

        //create the user
        $strMailBoxPrefix = self::getMailBoxPrefix($this->getUserName(), $this->getMailId());
        
        $intNextMailBoxNumber = self::getNextMailBoxNumber($strMailBoxPrefix);
                
        $strSql  = 'INSERT INTO passwd (username, realm, fullname, passwd, status, options, mail_quota, parentid) VALUES ';
        $strSql .= ' (:username, :realm, :fullname, :passwd, :status, :options, :mail_quota, :parentid)';
        
        $stmt = BVDB::db()->prepare($strSql);
        
        $stmt->bindValue(':username', $strMailBoxPrefix.'-'.$intNextMailBoxNumber.'-', PDO::PARAM_STR);
        $stmt->bindValue(':realm', $this->getRealm(), PDO::PARAM_STR);
        $stmt->bindValue(':fullname', $this->getFullName(), PDO::PARAM_STR);
        $stmt->bindValue(':passwd', $strPassswd, PDO::PARAM_STR);
        $stmt->bindValue(':options', 'dialup,mail,pop3,imap,webmail,smtp', PDO::PARAM_STR);
        $stmt->bindValue(':status', BrightViewPasswd::STATUS_ACTIVE, PDO::PARAM_STR);
        $stmt->bindValue(':mail_quota', 20, PDO::PARAM_INT);
        $stmt->bindValue(':parentid', $this->getMailId(), PDO::PARAM_INT);

        if (!$stmt->execute()) {
            throw new Exception('Failed to create an account: '.print_r($stmt->errorInfo(), true));
        }

        $inNewMailId = (int) BVDB::db()->lastInsertId();

        //create virtual relationship
        $stmt = BVDB::db()->prepare('INSERT INTO multipop (mailid, domain, alias, status) VALUES (?,?,?,?)');
        if (!$stmt->execute(array($inNewMailId, $strDomain, $strAlias, BrightViewMailBox::STATUS_ACTIVE))) {
            throw new Exception('Failed to create virtual relationship: '.print_r($stmt->errorInfo(), true));
        }

        //all done 
        return BrightViewMailBox::fromAliasAndDomain($strAlias, $strDomain);
    }

    /**
     * Is there a catchall on domain ?
     *
     * @param string $strDomain
     * @return boolean
     */
    
    public static function checkCatchAllForDomain($strDomain)
    {
        $stmt = BVDB::db()->prepare('SELECT * FROM multipop WHERE domain = ? AND alias = "*"');
        if (!$stmt->execute(array($strDomain))) {
            throw new Exception('Failed catchall check:'.print_r($stmt->errorInfo(), true));
        }

        return 0 == count($stmt->fetchAll());
    }
    
    /**
     * A function to create new mialbox and make it catchall in one go 
     *
     * @param string $strAlias
     * @param string $strDomain
     * @return boolean
     */

    public function createNewCatchAllMailBox($strAlias, $strDomain)
    {
        if (!self::checkCatchAllForDomain($strDomain)) {
            throw new Exception('There is already a catch all for domain'.$strDomain);
        }
        
        $objMailBox = $this->createNewMailbox($strAlias, $strDomain);
        
        if (null == $objMailBox) throw new Exception('Failed to create mailbox');
        
        $stmt = BVDB::db()->prepare('INSERT INTO multipop (mailid, domain, alias, status) VALUES (?,?,?,?)');
        if (!$stmt->execute(array($objMailBox->getMailId(), $strDomain, '*', BrightViewMailBox::STATUS_ACTIVE))) {
            throw new Exception('Failed to create catch-all entry:'.print_r($stmt->errorInfo(), true));
        }
        
        return BrightViewMailBox::fromAliasAndDomain($strAlias, $strDomain);
    }

    //intMailId - mailid of parent
    private static function getMailBoxPrefix($strUserName, $intMailId)
    {
        return substr($strUserName, 0, 2) . $intMailId;
    }
    
    private static function getNextMailBoxNumber($strMailBoxPrefix)
    {
        $stmt = BVDB::db()->prepare("SELECT username FROM passwd WHERE parentid = ? ORDER BY CAST(substring_index(username, '-', -2) AS UNSIGNED) DESC LIMIT 1");

        $stmt->execute(array(substr($strMailBoxPrefix, 2)));
        
        $strUserName = $stmt->fetchColumn();

        if (empty($strUserName)) return 1;
        
        $arrMatches = array();
        preg_match('/^'.$strMailBoxPrefix.'\-(\d+)\-/', $strUserName, $arrMatches);
        
        if (empty($arrMatches)) return 1; //or Exception should be thrown ?
        
        if (!is_numeric($arrMatches[1])) return 1; // or Execption ?
        
        return ((int) $arrMatches[1]) + 1;
    }
    
    /**
     * Method gets mailbox based on multipopid
     * 
     * <AUTHOR> Bargiel <<EMAIL>>
     * 
     * @throws Exception
     * 
     * @return BrightViewMailBox
     */
    public static function getMailBoxDetails($intMultipopId)
    {
        $stmt = BVDB::db()->prepare('SELECT * FROM passwd p INNER JOIN multipop m ON (p.mailid = m.mailid) WHERE multipopid =  ?');
        $stmt->execute(array($intMultipopId));
        if ($arrMailBox = $stmt->fetchAll(PDO::FETCH_ASSOC)) {
            return BrightViewMailBox::fromResult($arrMailBox[0]);
        }   
        
    } // end of method getMailBoxDetails
    

    /**
     * Sets catch all on a master mailbox
     *
     * @param string $strDomain - mastermailbox domain
     * @return booelan
     */
    public static function setCatchAllOnMaster($strDomain)
    {
        //according to Ricky Chan such delete will do the trick
        $stmt = BVDB::db()->prepare('DELETE FROM multipop WHERE domain = ? AND alias ="*"');
        if (!$stmt->execute(array($strDomain))) throw new Exception('Setting catchall on master failed');
        return true;
    }
    
    /**
     * Method disables catch all on given domain
     * 
     * <AUTHOR> Bargiel <<EMAIL>> 
     *
     * @param string $strDomain
     * 
     * @throws Exception
     * 
     * @return void
     */
    public function disableCatchAll($strDomain)
    {
        $stmt = BVDB::db()->prepare('INSERT INTO multipop SET mailid=0, domain=?, alias="*"');
        if (!$stmt->execute(array($strDomain))) throw new Exception('Disabling catchall failed');
    } // end of method disableCatchAll()
    
    /**
     * Method checks if catch all is disabled
     * 
     * <AUTHOR> Bargiel <<EMAIL>>
     *
     * @param string $strDomainName
     * 
     * @return boolean
     */
    public function isCatchAllDisabled($strDomainName)
    {
        $stmt = BVDB::db()->prepare('SELECT count(multipopid) FROM multipop WHERE mailid=0 AND domain = ?');
        $stmt->execute(array($strDomainName));
        $intRows = $stmt->fetchColumn();
        $stmt->closeCursor();
        return 1 == $intRows;
    }
    
    /**
     * get catchall mailid (setup to work with MAAF_CProduct_Webmail->setDefaulMailbox)
     *
     * @param string $strDomainName
     * 
     * @return integer
     */
    public function getCatchallMultipopId($strDomainName) 
    {
        if ($strDomainName == '') {
            $strDomainName = $this->getRealm();
        }
        $stmt = BVDB::db()->prepare('SELECT mailid, multipopid FROM multipop WHERE domain = ? and alias = "*"');
        $stmt->execute(array($strDomainName));

        $arrData = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $stmt->closeCursor();
        
        if (!isset($arrData[0])) {
            // master is catchall
            return 0;
        }
        if ($arrData[0]['mailid'] == 0) {
            // catchall is disabled
            return -1;
        }
        // an additional mailbox is the catchall
        return $arrData[0]['multipopid'];
    }

    /**
     * funtion used to set a catchall on particular $strDomain, all emails will go to $strAlias 
     *
     * @param unknown_type $strDomain
     * @param unknown_type $strAlias
     * 
     */
    public static function setCatchAll($strDomain, $strAlias)
    {
        $str ='INSERT INTO multipop (domain, alias, mailid) SELECT ?,"*",mailid FROM multipop WHERE alias = ? AND domain = ?';
        $stmt = BVDB::db()->prepare($str);
        $stmt->execute(array($strDomain, $strAlias, $strDomain));
    }
    
    /**
     * delete a mailbox with given mailid
     *
     * @param integer $intMultiPopId
     * @return boolean
     */
    public static function deleteMailbox($intMultiPopId)
    {
        BVDB::db()->beginTransaction();

        try
        {
            $stmt = BVDB::db()->prepare('SELECT mailid FROM multipop WHERE multipopid = ?');
            $stmt->execute(array($intMultiPopId));
            
            $intMailId = $stmt->fetchColumn();
            $stmt->closeCursor();
    
            $stmt = BVDB::db()->prepare('DELETE FROM multipop WHERE mailid = ?');
            $stmt->execute(array($intMailId));
            
            //AND parentid > 0 - additional protection to not delete main passwd entry for customer
            $stmt = BVDB::db()->prepare('DELETE FROM passwd WHERE mailid = ? AND parentid > 0');
            $stmt->execute(array($intMailId));
        }
        catch (Exception $e)
        {
            BVDB::db()->rollBack();
            return false;
        }

        BVDB::db()->commit();
        return true;
    }
    
    /**
     * Method disables master mailbox
     * 
     * <AUTHOR> Bargiel <<EMAIL>>
     * 
     * @throws Exception
     * 
     * @return void
     */
    public function disableMailbox()
    {
        $objPDOStatement = BVDB::db()->prepare('UPDATE passwd SET status="poisoned", options=options & ~? WHERE mailid = ?');
        $objPDOStatement->execute(array(self::OPT_SMTP + self::OPT_FTP + self::OPT_POP3 + self::OPT_IMAP + self::OPT_WEBMAIL, $this->intMailId));
    } // end of method disableMailbox()
    
    /**
     * Method enables master mailbox
     * 
     * <AUTHOR> Bargiel <<EMAIL>>
     * 
     * @throws Exception
     * 
     * @return void
     */
    public function enableMailbox()
    {
        $objPDOStatement = BVDB::db()->prepare('UPDATE passwd SET status="active", options=options | ? WHERE mailid = ?');
        $objPDOStatement->execute(array(self::OPT_SMTP + self::OPT_FTP + self::OPT_POP3 + self::OPT_IMAP + self::OPT_WEBMAIL, $this->intMailId));
    } // end of method enableMailbox()
    
    /**
     * Method archives master mailbox
     * 
     * <AUTHOR> Bargiel <<EMAIL>>
     * 
     * @throws Exception
     * 
     * @return void
     */
    public function archiveMailbox()
    {
        $objPDOStatement = BVDB::db()->prepare('UPDATE passwd SET status="archive" WHERE mailid = ?');
        $objPDOStatement->execute(array($this->intMailId));
    } // end of method enableMailbox()
    
} // end of class BrightViewMail
