<?php
/**
 * tickets-access.inc
 *
 * Access Library for Tickets
 *
 * @package    LegacyCodebase
 * @subPackage Database Libraries
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2012 Plusnet
 */

// DJM2015: Ticket rendering is memory intensive, and is even more so, now that we're caching Class/Cause data
// We therefore need to bump up the memory limit when this library is loaded!
// The new caching mechanism appears to use between 5 - 30 mb of extra ram, so we add an extra 50mb...

// Loosely adapted from the example on http://php.net/manual/en/function.ini-get.php
// Note that a memory limit of "-1" means that the script is already set to use unlimited memory!
$strMemoryLimit = ini_get('memory_limit');
if (!empty($strMemoryLimit) && $strMemoryLimit != -1) {
    $strMemoryLimit = trim($strMemoryLimit);
    $strPostfix     = strtolower(substr($strMemoryLimit, -1, 1));
    $intMemoryLimit = (int) $strMemoryLimit;

    switch ($strPostfix) {
        case 'g':
            $intMemoryLimit *= 1024;
        case 'm':
            $intMemoryLimit *= 1024;
        case 'k':
            $intMemoryLimit *= 1024;
    }

    $intMemoryLimit += 50 * (1024 * 1024);

    foreach (array('K', 'M','G') as $strPostfix) {
        $intMemoryLimit /= 1024;
        if ($intMemoryLimit > 1024) {
            continue;
        }

        $strMemoryLimit = $intMemoryLimit . $strPostfix;
        break;
    }

    if (ini_set('memory_limit', $strMemoryLimit) === false) {
        error_log(__FILE__ . ": WARNING: failed to set new memory limit for <{$_SERVER['SCRIPT_NAME']}>");
    }
}

require_once '/local/data/mis/database/database_libraries/tickets-notify_user_functions.inc';
require_once '/local/data/mis/database/database_libraries/sql_primitives.inc';
require_once COMMON_LIBRARY_ROOT . 'class_libraries/Tickets/CClass.inc';
require_once COMMON_LIBRARY_ROOT . 'class_libraries/Tickets/CTicketClass.inc';
require_once '/local/data/mis/database/database_libraries/CServiceNotice.inc';
require_once '/local/data/mis/database/database_libraries/CServiceNoticeType.inc';
require_once '/local/data/mis/database/database_libraries/CTicketSLGTimer.inc';
require_once '/local/data/mis/database/database_libraries/CTeamSLGTimer.inc';
require_once '/local/www/database-admin/config/tickets_config.inc';

require_once COMMON_API_ROOT . '/BusinessTier.php';
require_once COMMON_API_ROOT . '/xmlparser.class.php';

require_once '/local/data/mis/database/database_libraries/Util_LibrarySplitter.class.php';
require_once '/local/data/mis/database/database_libraries/tickets/Tickets.class.php';

if (isset($WORKPLACE_FRONT_PAGE)) {
    require_once('/local/data/mis/database/database_libraries/alert-access.inc');
}

$global_ticket_cache         = array();
$global_ticket_contact_cache = array();

/**
 * Local Tickets Check For Fault Tickets
 *
 * @param array &$arrTickets Tickets
 *
 * @return unknown
 */
function localTicketsCheckForFaultTickets(&$arrTickets)
{
    require_once '/local/data/mis/common_library_functions/common_application_apis/BusinessTier.php';

    $objSession = new BusinessTierSession('PLUSNET');

    $arrTicketIDs = array();

    foreach (array_keys($arrTickets) as $intKey) {
        if ((preg_match('/^[0-9]+$/', $arrTickets[$intKey]['ticket_id'])) && ($arrTickets[$intKey]['ticket_id'] > 0)) {
            $arrTicketIDs[] = $arrTickets[$intKey]['ticket_id'];
        }
    }

    if (count($arrTicketIDs) == 0) {
        return;
    }

    $dbhConnection = get_named_connection_with_db('plusnet_faults_reporting');

    $strQuery = 'SELECT f.faultID, f.controllingTicketID, f.supplierId, fs.handle ' .
                'FROM tblFault f ' .
                'INNER JOIN tblFaultStateHistory fsh ON f.faultID = fsh.faultID AND left_2 IS NULL ' .
                'INNER JOIN tblFaultState fs ON fsh.faultStateID = fs.faultStateID ' .
                'WHERE controllingTicketID IN (' . implode(', ', $arrTicketIDs) . ')';
    unset($arrTicketIDs);

    $resResult = PrimitivesQueryOrExit($strQuery, $dbhConnection);

    $arrFaults = PrimitivesResultsAsArrayGet($resResult);

    foreach (array_keys($arrFaults) as $intFaultKey) {
        foreach (array_keys($arrTickets) as $intTicketKey) {
            if ($arrTickets[$intTicketKey]['ticket_id'] == $arrFaults[$intFaultKey]['controllingTicketID']) {
                require_once '/local/data/mis/common_library_functions/common_application_apis/xmlparser.class.php';

                $arrQuestions = array();

                $arrTickets[$intTicketKey]['fault_id'] = $arrFaults[$intFaultKey]['faultID'];
                $arrTickets[$intTicketKey]['type'] = 'fault';
                $arrTickets[$intTicketKey]['intSupplierID'] = $arrFaults[$intFaultKey]['supplierId'];
                $arrTickets[$intTicketKey]['fault_status'] = $arrFaults[$intFaultKey]['handle'];

                $arrFaultDetails = $objSession->getFaultDetails(array('faultID' => $arrFaults[$intFaultKey]['faultID']));

                list($arrIndices, $arrValues) = XMLParser::parseIntoArray($arrFaultDetails['diagnosticQestionResults']);

                if (count($arrIndices) > 0) {
                    foreach ($arrIndices['ANSWER'] as $intIndex) {
                        $strQuestion = $arrValues[$intIndex]['attributes']['PROMPT'];
                        $strAnswer = $arrValues[$intIndex]['attributes']['VALUE'];
                        $arrQuestions[$strQuestion] = $strAnswer;
                    }

                    $arrTickets[$intTicketKey]['fault_questions'] = $arrQuestions;
                }
            }
        }
    }
}

/**
 * Tickets User Ticket Stats Get
 *
 * @param string $user_id User Id
 * @param int    $team_id Team Id
 *
 * @return array
 */
function tickets_user_ticket_stats_get($user_id, $team_id)
{
    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_user_ticket_stats_get($user_id, $team_id);
}

/**
 * Tickets Tickets Find
 *
 * @param unknown $criteria           Criteria
 * @param unknown $orderby            Order By
 * @param unknown $limit              Limit
 * @param int     $number             Number
 * @param bool    $bolUseReporting    Use Reporting
 * @param string  $strContactOrderBy  Contact Order By
 * @param bool    $bolIncludeContacts Include Contacts
 *
 * @return unknown
 */
function tickets_tickets_find(
    $criteria,
    $orderby = '',
    $limit = 0,
    $number = 0,
    $bolUseReporting = false,
    $strContactOrderBy = 'contact_id',
    $bolIncludeContacts = true
) {
    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_tickets_find($criteria, $orderby, $limit, $number, $bolUseReporting, $strContactOrderBy, $bolIncludeContacts);
}

/**
 * Tickets Process Criteria
 *
 * @param array $array_criteria Criteria
 *
 * @return unknown
 */
function tickets_process_criteria($array_criteria)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_process_criteria($array_criteria);
}

/**
 * Tickets Contacts Get Indexed
 *
 * @param unknown $ticket_ids   Ticket Ids
 * @param unknown $orderby      Orderby
 * @param unknown $table_source Table Source
 *
 * @return unknown
 */
function tickets_contacts_get_indexed($ticket_ids, $orderby = 'contact_id', $table_source = '')
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_contacts_get_indexed($ticket_ids, $orderby, $table_source);
}

/**
 * Check Manual House Move In Progress
 *
 * @param int $intServiceID Service ID
 *
 * @return unknown
 */
function checkManualHouseMoveInProgress($intServiceID)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->checkManualHouseMoveInProgress($intServiceID);
}

/**
 * Tickets Tickets Count
 *
 * @param unknown $criteria Criteria
 *
 * @return unknown
 */
function tickets_tickets_count($criteria)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_tickets_count($criteria);
}

/**
 * Ticket Sort
 *
 * @param unknown $a A
 * @param unknown $b B
 *
 * @return unknown
 */
function ticket_sort($a, $b)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->ticket_sort($a, $b);
}

/**
 * Tickets Original Team Id Get
 *
 * @param unknown $ticket_id Ticket Id
 *
 * @return unknown
 */
function tickets_original_team_id_get($ticket_id)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_original_team_id_get($ticket_id);
}

/**
 * Tickets Ticket Add
 *
 * @param unknown $source               Source
 * @param unknown $service_id           Service Id
 * @param unknown $error_class          Error Class
 * @param unknown $error_subclass       Error Subclass
 * @param unknown $status               Status
 * @param unknown $actioner_id          Actioner Id
 * @param unknown $body                 Body
 * @param unknown $return_to            Return To
 * @param unknown $team_id              Team Id
 * @param unknown $owner_id             Owner Id
 * @param string  $strCauseHandle       Cause Handle
 * @param string  $strQuestionTopicName Question Topic Name
 *
 * @return unknown
 */
function tickets_ticket_add($source, $service_id, $error_class, $error_subclass, $status, $actioner_id, $body, $return_to = '0', $team_id = 0, $owner_id = '0', $strCauseHandle = '', $strQuestionTopicName = '')
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_ticket_add($source, $service_id, $error_class, $error_subclass, $status, $actioner_id, $body, $return_to, $team_id, $owner_id, $strCauseHandle, $strQuestionTopicName);
}

/**
 * Tickets Ticket Question Topic Add
 *
 * @param int $intTicketID              Ticket ID
 * @param int $intTicketQuestionTopicID Ticket Question Topic ID
 *
 * @return unknown
 */
function tickets_ticket_question_topic_add($intTicketID, $intTicketQuestionTopicID)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_ticket_question_topic_add($intTicketID, $intTicketQuestionTopicID);
}

/**
 * Bol In Legacy Adaptor Call
 *
 * @return unknown
 */
function bolInLegacyAdaptorCall()
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->bolInLegacyAdaptorCall();
}

/**
 * Tickets Ticket Contacts Add
 *
 * @param unknown $ticket_id            Ticket Id
 * @param unknown $owner_id             Owner Id
 * @param unknown $body                 Body
 * @param unknown $error_class          Error Class
 * @param unknown $error_subclass       Error Subclass
 * @param unknown $team_id              Team Id
 * @param unknown $status               Status
 * @param unknown $actioner_id          Actioner Id
 * @param unknown $datestamp            Datestamp
 * @param bool    $bolTakeOffHold       Take Off Hold
 * @param string  $strCauseHandle       Cause Handle
 * @param string  $autoComplaintClosure Complaint Closure
 * @param bool    $isSms                SMS response
 * @param bool    $isInternalOnly       Internal comment (do not show to customer)
 *
 * @return unknown
 */
function tickets_ticket_contacts_add(
    $ticket_id,
    $owner_id,
    $body,
    $error_class,
    $error_subclass,
    $team_id,
    $status,
    $actioner_id,
    $datestamp = null,
    $bolTakeOffHold = true,
    $strCauseHandle = '',
    $autoComplaintClosure = '',
    $isSms = false,
    $complaintResolvedReasonId = null,
    $isInternalOnly = false
) {
    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_ticket_contacts_add(
        $ticket_id,
        $owner_id,
        $body,
        $error_class,
        $error_subclass,
        $team_id,
        $status,
        $actioner_id,
        $datestamp,
        $bolTakeOffHold,
        $strCauseHandle,
        $autoComplaintClosure,
        $isSms,
        $complaintResolvedReasonId,
        $isInternalOnly
    );
}

/**
 * Tickets Ticket Reminder Add
 *
 * @param unknown $ticket_id   Ticket Id
 * @param unknown $user_id     User Id
 * @param unknown $remind_time Remind Time
 * @param unknown $body        Body
 *
 * @return unknown
 */
function tickets_ticket_reminder_add($ticket_id, $user_id, $remind_time, $body)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_ticket_reminder_add($ticket_id, $user_id, $remind_time, $body);
}

/**
 * Tickets Ticket File Check Ext
 *
 * @param unknown $filename Filename
 *
 * @return unknown
 */
function tickets_ticket_file_check_ext($filename)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_ticket_file_check_ext($filename);
}

/**
 * Tickets Ticket File Check Mime
 *
 * @param unknown $mime_type Mime Type
 *
 * @return unknown
 */
function tickets_ticket_file_check_mime($mime_type)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_ticket_file_check_mime($mime_type);
}

/**
 * Tickets Ticket File Add
 *
 * @param unknown $ticket_id                Ticket Id
 * @param unknown $file_location            File Location
 * @param unknown $actioner_id              Actioner Id
 * @param unknown $filename                 Filename
 * @param unknown $mime_type                Mime Type
 * @param bool    $bolTakeAttachmentOffHold Take Attachment Off Hold
 *
 * @return unknown
 */
function tickets_ticket_file_add($ticket_id, $file_location, $actioner_id, $filename, $mime_type, $bolTakeAttachmentOffHold = true)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_ticket_file_add($ticket_id, $file_location, $actioner_id, $filename, $mime_type, $bolTakeAttachmentOffHold);
}

/**
 * Tickets New Error Tag
 *
 * @param unknown $tag         Tag
 * @param unknown $description Description
 *
 * @return unknown
 */
function tickets_new_error_tag($tag, $description)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_new_error_tag($tag, $description);
}

/**
 * Tickets Callback Add
 *
 * @param unknown $service_id        Service Id
 * @param unknown $creator_id        Creator Id
 * @param unknown $caller_id         Caller Id
 * @param unknown $date_calling_back Date Calling Back
 *
 * @return unknown
 */
function tickets_callback_add($service_id, $creator_id, $caller_id, $date_calling_back)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_callback_add($service_id, $creator_id, $caller_id, $date_calling_back);
}

/**
 * Tickets Contact Sales Add
 *
 * @param unknown $contact_details Contact Details
 *
 * @return unknown
 */
function tickets_contact_sales_add($contact_details)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_contact_sales_add($contact_details);
}

/**
 * Tickets Sla Summary Add
 *
 * @param unknown $ticket_id                Ticket Id
 * @param unknown $ticket_contact_id        Ticket Contact Id
 * @param unknown $date_of_sla_period_start Date Of Sla Period Start
 * @param unknown $date_of_sla_period_end   Date Of Sla Period End
 * @param unknown $team_id                  Team Id
 *
 * @return unknown
 */
function tickets_sla_summary_add($ticket_id, $ticket_contact_id, $date_of_sla_period_start, $date_of_sla_period_end, $team_id)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_sla_summary_add($ticket_id, $ticket_contact_id, $date_of_sla_period_start, $date_of_sla_period_end, $team_id);
}

/**
 * Tickets Performance Summary Add
 *
 * @param unknown $start_ts                      Start Ts
 * @param unknown $end_ts                        End Ts
 * @param unknown $avg_seconds_to_first_response Avg Seconds To First Response
 * @param unknown $avg_seconds_to_closure        Avg Seconds To Closure
 * @param unknown $team_id                       Team Id
 *
 * @return unknown
 */
function tickets_performance_summary_add($start_ts, $end_ts, $avg_seconds_to_first_response, $avg_seconds_to_closure, $team_id)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_performance_summary_add($start_ts, $end_ts, $avg_seconds_to_first_response, $avg_seconds_to_closure, $team_id);
}

/**
 * Tickets Manual Response Time Add
 *
 * @param int    $intAverageClosureSeconds Average Closure Seconds
 * @param string $strWorkplaceUser         Workplace User
 * @param int    $intTargetReplyTimeHours  Target Reply Time Hours
 *
 * @return unknown
 */
function tickets_manual_response_time_add($intAverageClosureSeconds, $strWorkplaceUser, $intTargetReplyTimeHours = false)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_manual_response_time_add($intAverageClosureSeconds, $strWorkplaceUser, $intTargetReplyTimeHours);
}

/**
 * Tickets Archive Run Add
 *
 * @return unknown
 */
function tickets_archive_run_add()
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_archive_run_add();
}

/**
 * Tickets Archived Service Add
 *
 * @param unknown $archive_run_id Archive Run Id
 * @param unknown $service_id     Service Id
 * @param unknown $ticket_count   Ticket Count
 *
 * @return unknown
 */
function tickets_archived_service_add($archive_run_id, $service_id, $ticket_count)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_archived_service_add($archive_run_id, $service_id, $ticket_count);
}

/**
 * Tickets Esclate To Problem
 *
 * @param unknown $ticket_id  Ticket Id
 * @param unknown $problem_id Problem Id
 * @param unknown $user_id    User Id
 *
 * @return unknown
 */
function tickets_esclate_to_problem($ticket_id, $problem_id, $user_id)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_esclate_to_problem($ticket_id, $problem_id, $user_id);
}

/**
 * Tickets Ticket Snooze
 *
 * @param unknown $contact_id       Contact Id
 * @param unknown $actioner_id      Actioner Id
 * @param unknown $original_team_id Original Team Id
 * @param unknown $release_date     Release Date
 *
 * @return unknown
 */
function tickets_ticket_snooze($contact_id, $actioner_id, $original_team_id, $release_date)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_ticket_snooze($contact_id, $actioner_id, $original_team_id, $release_date);
}

/**
 * Tickets Ticket Lock
 *
 * @param unknown $ticket_id             Ticket Id
 * @param unknown $owner_id              Owner Id
 * @param unknown $start_time            Start Time
 * @param unknown $expected_release_time Expected Release Time
 *
 * @return unknown
 */
function tickets_ticket_lock($ticket_id, $owner_id, $start_time, $expected_release_time)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_ticket_lock($ticket_id, $owner_id, $start_time, $expected_release_time);
}

/**
 * Tickets Open Ticket Summary Add
 *
 * @param unknown $ticket_id      Ticket Id
 * @param unknown $partner_id     Partner Id
 * @param unknown $source         Source
 * @param unknown $service_id     Service Id
 * @param unknown $error_class    Error Class
 * @param unknown $error_subclass Error Subclass
 * @param unknown $status         Status
 * @param unknown $actioner_id    Actioner Id
 * @param unknown $return_to      Return To
 * @param unknown $team_id        Team Id
 * @param unknown $owner_id       Owner Id
 * @param unknown $time           Time
 * @param string  $strTransaction Transaction
 *
 * @return unknown
 */
function tickets_open_ticket_summary_add($ticket_id, $partner_id, $source, $service_id, $error_class, $error_subclass, $status, $actioner_id, $return_to, $team_id, $owner_id, $time, $strTransaction = 'TRANS_SPLIT_TICKETSOPENTICKETSUMMARYADD')
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_open_ticket_summary_add($ticket_id, $partner_id, $source, $service_id, $error_class, $error_subclass, $status, $actioner_id, $return_to, $team_id, $owner_id, $time, $strTransaction);
}

/**
 * Tickets Ticket Action Log
 *
 * @param int    $intTicketId       Ticket Id
 * @param int    $intPartnerId      Partner Id
 * @param int    $intContactId      Contact Id
 * @param string $strUserId         User Id
 * @param string $strAction         Action
 * @param int    $intCharacterCount Character Count
 * @param int    $intTeamId         Team Id
 * @param string $strOwnerId        Owner Id
 *
 * @return unknown
 */
function TicketsTicketActionLog($intTicketId, $intPartnerId, $intContactId, $strUserId, $strAction, $intCharacterCount, $intTeamId, $strOwnerId)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->TicketsTicketActionLog($intTicketId, $intPartnerId, $intContactId, $strUserId, $strAction, $intCharacterCount, $intTeamId, $strOwnerId);
}

/**
 * Tickets Ticket Action Log Cust Script
 *
 * @param int    $intTicketId       Ticket Id
 * @param int    $intPartnerId      Partner Id
 * @param int    $intContactId      Contact Id
 * @param string $strAction         Action
 * @param int    $intTeamId         Team Id
 * @param string $strOwnerId        Owner Id
 * @param string $strReopenedTicket Reopened Ticket
 * @param string $strRaisedBy       Raised By
 *
 * @return unknown
 */
function TicketsTicketActionLogCustScript($intTicketId, $intPartnerId, $intContactId, $strAction, $intTeamId, $strOwnerId, $strReopenedTicket, $strRaisedBy)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->TicketsTicketActionLogCustScript($intTicketId, $intPartnerId, $intContactId, $strAction, $intTeamId, $strOwnerId, $strReopenedTicket, $strRaisedBy);
}

/**
 * Tickets Update Metrics
 *
 * @param int     $intTicketId   Ticket Id
 * @param int     $intPartnerId  Partner Id
 * @param string  $strStatus     Status
 * @param int     $intTeamId     Team Id
 * @param unknown $dtmStatusTime Dtm Status Time
 * @param int     $intActionerId Actioner Id
 *
 * @return unknown
 */
function TicketsUpdateMetrics($intTicketId, $intPartnerId, $strStatus, $intTeamId, $dtmStatusTime, $intActionerId = null)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->TicketsUpdateMetrics($intTicketId, $intPartnerId, $strStatus, $intTeamId, $dtmStatusTime, $intActionerId);
}

/**
 * Tickets Customer Question Get All
 *
 * @return unknown
 */
function TicketsCustomerQuestionGetAll()
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->TicketsCustomerQuestionGetAll();
}

/**
 * Tickets Customer Question Get Answers
 *
 * @param int $intQuestionId Question Id
 *
 * @return unknown
 */
function TicketsCustomerQuestionGetAnswers($intQuestionId)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->TicketsCustomerQuestionGetAnswers($intQuestionId);
}

/**
 * Ticket Customer Question Already Answered
 *
 * @param int $intQuestionId      Question Id
 * @param int $intTicketContactId Ticket Contact Id
 *
 * @return unknown
 */
function TicketCustomerQuestionAlreadyAnswered($intQuestionId, $intTicketContactId)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->TicketCustomerQuestionAlreadyAnswered($intQuestionId, $intTicketContactId);
}

/**
 * Tickets Customer Queston Get Child Question
 *
 * @param int $intQuestionId Question Id
 *
 * @return unknown
 */
function TicketsCustomerQuestonGetChildQuestion($intQuestionId)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->TicketsCustomerQuestonGetChildQuestion($intQuestionId);
}

/**
 * Tickets Customer Answer Get
 *
 * @param int $intQuestionId Question Id
 *
 * @return unknown
 */
function TicketsCustomerAnswerGet($intQuestionId)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->TicketsCustomerAnswerGet($intQuestionId);
}

/**
 * Tickets Customer Response Metrics
 *
 * @param unknown $dtmStartDate  Dtm Start Date
 * @param unknown $dtmEndDate    Dtm End Date
 * @param int     $intQuestionId Question Id
 * @param int     $intGroupId    Group Id
 *
 * @return unknown
 */
function TicketsCustomerResponseMetrics($dtmStartDate, $dtmEndDate, $intQuestionId = 0, $intGroupId = 0)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->TicketsCustomerResponseMetrics($dtmStartDate, $dtmEndDate, $intQuestionId, $intGroupId);
}

/**
 * Tickets Touched Metrics
 *
 * @param unknown $dtmStartDate Dtm Start Date
 * @param unknown $dtmEndDate   Dtm End Date
 * @param string  $strUserId    User Id
 *
 * @return unknown
 */
function TicketsTouchedMetrics($dtmStartDate, $dtmEndDate, $strUserId)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->TicketsTouchedMetrics($dtmStartDate, $dtmEndDate, $strUserId);
}

/**
 * Tickets Escalated Metrics
 *
 * @param unknown $dtmStartDate Dtm Start Date
 * @param unknown $dtmEndDate   Dtm End Date
 * @param int     $intGroupId   Group Id
 *
 * @return unknown
 */
function TicketsEscalatedMetrics($dtmStartDate, $dtmEndDate, $intGroupId = 0)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->TicketsEscalatedMetrics($dtmStartDate, $dtmEndDate, $intGroupId);
}

/**
 * Tickets Get Metrics
 *
 * @param unknown $date      Date
 * @param int     $intTeamId Team Id
 *
 * @return unknown
 */
function TicketsGetMetrics($date, $intTeamId)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->TicketsGetMetrics($date, $intTeamId);
}

/**
 * Tickets Get User Ticket Action Metrics
 *
 * @param string $strStartTime  Start Time
 * @param string $strEndTime    End Time
 * @param string $strUserId     User Id
 * @param bool   $bolGetDetails Get Details
 *
 * @return unknown
 */
function TicketsGetUserTicketActionMetrics($strStartTime, $strEndTime, $strUserId, $bolGetDetails = false)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->TicketsGetUserTicketActionMetrics($strStartTime, $strEndTime, $strUserId, $bolGetDetails);
}

/**
 * Tickets Ticket History Get
 *
 * @param unknown $ticket_id Ticket Id
 *
 * @return unknown
 */
function tickets_ticket_history_get($ticket_id)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_ticket_history_get($ticket_id);
}

/**
 * Tickets Ticket Get
 *
 * @param unknown $ticket_id     Ticket Id
 * @param unknown $dont_cache    Dont Cache
 * @param bool    $bolUseArchive Use Archive
 *
 * @return unknown
 */
function tickets_ticket_get($ticket_id, $dont_cache = false, $bolUseArchive = false)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_ticket_get($ticket_id, $dont_cache, $bolUseArchive);
}

/**
 * Tickets Ticket Question Topic Get
 *
 * @param int $intTicketID Ticket ID
 *
 * @return unknown
 */
function tickets_ticket_question_topic_get($intTicketID)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_ticket_question_topic_get($intTicketID);
}

/**
 * Tickets Ticket Contact Get
 *
 * @param unknown $ticket_contact_id Ticket Contact Id
 *
 * @return unknown
 */
function tickets_ticket_contact_get($ticket_contact_id)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_ticket_contact_get($ticket_contact_id);
}

/**
 * Tickets Ticket Status Get
 *
 * @param unknown $ticket_id Ticket Id
 *
 * @return unknown
 */
function tickets_ticket_status_get($ticket_id)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_ticket_status_get($ticket_id);
}

/**
 * Tickets Error Class Get
 *
 * @param unknown $error_class_id Error Class Id
 *
 * @return unknown
 */
function tickets_error_class_get($error_class_id)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_error_class_get($error_class_id);
}

/**
 * Tickets Error Classes Get
 *
 * @return unknown
 */
function tickets_error_classes_get()
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_error_classes_get();
}

/**
 * Tickets Error Classes Get All
 *
 * @return unknown
 */
function tickets_error_classes_get_all()
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_error_classes_get_all();
}

/**
 * Tickets Error Class Names Get
 *
 * @return unknown
 */
function tickets_error_class_names_get()
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_error_class_names_get();
}

/**
 * Tickets Error Subclasses Get
 *
 * @param unknown $parent_class Parent Class
 *
 * @return unknown
 */
function tickets_error_subclasses_get($parent_class)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_error_subclasses_get($parent_class);
}

/**
 * Tickets Error Subclass Name Get
 *
 * @param unknown $error_class Error Class
 *
 * @return unknown
 */
function tickets_error_subclass_name_get($error_class)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_error_subclass_name_get($error_class);
}

/**
 * Tickets Tickets Get
 *
 * @param unknown $whereclause     Whereclause
 * @param unknown $orderby         Orderby
 * @param unknown $limit           Limit
 * @param bool    $bolUseReporting Use Reporting
 *
 * @return unknown
 */
function tickets_tickets_get($whereclause = '', $orderby = 'ticket_id', $limit = null, $bolUseReporting = false)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_tickets_get($whereclause, $orderby, $limit, $bolUseReporting);
}

/**
 * Tickets Tickets Get All
 *
 * @param unknown $whereclause Whereclause
 * @param unknown $orderby     Orderby
 * @param unknown $limit       Limit
 * @param unknown $limit2      Limit 2
 *
 * @return unknown
 */
function tickets_tickets_get_all($whereclause = '', $orderby = 'ticket_id', $limit = 0, $limit2 = 0)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_tickets_get_all($whereclause, $orderby, $limit, $limit2);
}

/**
 * Tickets Contacts Get All
 *
 * @param unknown $ticket_id Ticket Id
 * @param unknown $orderby   Orderby
 *
 * @return unknown
 */
function tickets_contacts_get_all($ticket_id, $orderby = 'contact_id')
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_contacts_get_all($ticket_id, $orderby);
}

/**
 * Tickets Personal Tickets Count
 *
 * @param unknown $user_id     User Id
 * @param unknown $whereclause Whereclause
 *
 * @return unknown
 */
function tickets_personal_tickets_count($user_id, $whereclause = '')
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_personal_tickets_count($user_id, $whereclause);
}

/**
 * Tickets Personal Ex Tickets Count
 *
 * @param unknown $user_id User Id
 * @param unknown $team_id Team Id
 *
 * @return unknown
 */
function tickets_personal_ex_tickets_count($user_id, $team_id)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_personal_ex_tickets_count($user_id, $team_id);
}

/**
 * Tickets Team Tickets Count
 *
 * @param unknown $team_id     Team Id
 * @param unknown $whereclause Whereclause
 *
 * @return unknown
 */
function tickets_team_tickets_count($team_id, $whereclause = "")
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_team_tickets_count($team_id, $whereclause);
}

/**
 * Tickets Team Ex Tickets Count
 *
 * @param unknown $team_id Team Id
 *
 * @return unknown
 */
function tickets_team_ex_tickets_count($team_id)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_team_ex_tickets_count($team_id);
}

/**
 * Tickets Cust Ticket Count
 *
 * @param unknown $service_id  Service Id
 * @param unknown $whereclause Whereclause
 *
 * @return unknown
 */
function tickets_cust_ticket_count($service_id, $whereclause = "")
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_cust_ticket_count($service_id, $whereclause);
}

/**
 * Tickets Open Portal Count
 *
 * @param unknown $service_id Service Id
 *
 * @return unknown
 */
function tickets_open_portal_count($service_id)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_open_portal_count($service_id);
}

/**
 * Tickets Open Portal Count By Sub Class
 *
 * @param unknown $service_id     Service Id
 * @param unknown $error_subclass Error Subclass
 *
 * @return unknown
 */
function tickets_open_portal_count_by_sub_class($service_id, $error_subclass)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_open_portal_count_by_sub_class($service_id, $error_subclass);
}

/**
 * Tickets Users Last Ticket By Origin
 *
 * @param unknown $service_id     Service Id
 * @param unknown $origin         Origin
 * @param unknown $error_subclass Error Subclass
 *
 * @return unknown
 */
function tickets_users_last_ticket_by_origin($service_id, $origin, $error_subclass)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_users_last_ticket_by_origin($service_id, $origin, $error_subclass);
}

/**
 * Tickets External Tickets Find
 *
 * @param unknown $criteria Criteria
 * @param unknown $orderby  Orderby
 * @param unknown $limit    Limit
 * @param unknown $number   Number
 *
 * @return unknown
 */
function tickets_external_tickets_find($criteria, $orderby, $limit = 0, $number = 0)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_external_tickets_find($criteria, $orderby, $limit, $number);
}

/**
 * Tickets Tickets Count Including Partners
 *
 * @param unknown $criteria Criteria
 *
 * @return unknown
 */
function TicketsTicketsCountIncludingPartners($criteria)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->TicketsTicketsCountIncludingPartners($criteria);
}

/**
 * Tickets Tickets Find Including Partners
 *
 * @param unknown $criteria Criteria
 * @param unknown $orderby  Orderby
 * @param unknown $limit    Limit
 * @param unknown $number   Number
 *
 * @return unknown
 */
function TicketsTicketsFindIncludingPartners($criteria, $orderby = '', $limit = 0, $number = 0)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->TicketsTicketsFindIncludingPartners($criteria, $orderby, $limit, $number);
}

/**
 * Tickets Get Tickets Being Worked On Data
 *
 * @param unknown $ticket_id  Ticket Id
 * @param unknown $partner_id Partner Id
 *
 * @return unknown
 */
function TicketsGetTicketsBeingWorkedOnData($ticket_id, $partner_id)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->TicketsGetTicketsBeingWorkedOnData($ticket_id, $partner_id);
}

/**
 * Tickets Ticket Contacts Get
 *
 * @param unknown $ticket_id    Ticket Id
 * @param unknown $partner_id   Partner Id
 * @param unknown &$partner_url Partner Url
 *
 * @return unknown
 */
function TicketsTicketContactsGet($ticket_id, $partner_id, &$partner_url)
{
    $contacts = array();
    $ticket_id  = addslashes($ticket_id);
    $partner_id = addslashes($partner_id);

    if ($partner_id == 0) {
        $database_name = 'tickets';
    } else {
        $connection = get_named_connection('partners');
        $query = 'SELECT database_name, url
                    FROM partners
                   WHERE partner_id = "'.$partner_id.'"';

        $result = mysql_query($query, $connection)
                  or report_error(__FILE__, __LINE__, mysql_error($connection));
        $row = mysql_fetch_assoc($result);

        mysql_free_result($result);

        $database_name = $row['database_name'];
        $partner_url = $row['url'];
    }

    if ($database_name != '') {
        $connection = get_named_connection($database_name);
        $query = 'SELECT contact_id,
                         ticket_id,
                         status,
                         owner_id,
                         actioner_id,
                         error_class,
                         error_subclass,
                         team_id,
                         body,
                         date_raised,
                         db_src,
                         UNIX_TIMESTAMP(date_raised) AS unix_date
                    FROM ticket_contacts
                   WHERE ticket_id="'.$ticket_id.'"
                ORDER BY contact_id';
        $result = mysql_query($query, $connection)
                  or report_error(__FILE__, __LINE__, mysql_error($connection));

        while ($contact = mysql_fetch_array($result, MYSQL_ASSOC)) {
            $contact['body'] = stripslashes($contact['body']);

            $conn = get_named_connection('partners');

            $team_query = "SELECT team_id
                             FROM partner_team_mappings
                            WHERE partner_team_id = ".$contact['team_id']."
                              AND partner_id = $partner_id";

            $team_result = mysql_query($team_query, $conn)
                           or report_error(__FILE__, __LINE__, mysql_error($conn));

            $contact['team_id'] = @mysql_result($team_result, 0, 'team_id');

            mysql_free_result($team_result);
            $contacts[] = $contact;
        }

        mysql_free_result($result);
    }

    return $contacts;
}

/**
 * Ticket Get Reminders To Send
 *
 * @return unknown
 */
function ticket_get_reminders_to_send()
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->ticket_get_reminders_to_send();
}

/**
 * Tickets Get Next Id
 *
 * @return unknown
 */
function tickets_get_next_id()
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_get_next_id();
}

/**
 * Ticket Get All Phrases
 *
 * @return unknown
 */
function ticket_get_all_phrases()
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->ticket_get_all_phrases();
}

/**
 * Tickets Phrase Get
 *
 * @param unknown $ticket_phrase_id Ticket Phrase Id
 *
 * @return unknown
 */
function tickets_phrase_get($ticket_phrase_id)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_phrase_get($ticket_phrase_id);
}

/**
 * Tickets Ticket File Get
 *
 * @param unknown $contact_id Contact Id
 *
 * @return unknown
 */
function tickets_ticket_file_get($contact_id)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_ticket_file_get($contact_id);
}

/**
 * Tickets First Response Sla Data Get
 *
 * @param unknown $start_ts Start Ts
 * @param unknown $end_ts   End Ts
 * @param unknown $team_id  Team Id
 *
 * @return unknown
 */
function tickets_first_response_sla_data_get($start_ts, $end_ts, $team_id)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_first_response_sla_data_get($start_ts, $end_ts, $team_id);
}

/**
 * Tickets Closure Sla Data Get
 *
 * @param unknown $start_ts Start Ts
 * @param unknown $end_ts   End Ts
 * @param unknown $team_id  Team Id
 *
 * @return unknown
 */
function tickets_closure_sla_data_get($start_ts, $end_ts, $team_id)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_closure_sla_data_get($start_ts, $end_ts, $team_id);
}

/**
 * Tickets Performance Summary Get Latest
 *
 * @param unknown $team_id Team Id
 *
 * @return unknown
 */
function tickets_performance_summary_get_latest($team_id)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_performance_summary_get_latest($team_id);
}

/**
 * Tickets Manual Response Time Get Latest
 *
 * @return unknown
 */
function tickets_manual_response_time_get_latest()
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_manual_response_time_get_latest();
}

/**
 * Tickets Contact Sales Count Open
 *
 * @param array $arrRestrictToBrands Restrict To Brands
 *
 * @return unknown
 */
function tickets_contact_sales_count_open($arrRestrictToBrands = array())
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_contact_sales_count_open($arrRestrictToBrands);
}

/**
 * Tickets Contact Sales Get Ticket
 *
 * @param unknown $ticket_id Ticket Id
 *
 * @return unknown
 */
function tickets_contact_sales_get_ticket($ticket_id)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_contact_sales_get_ticket($ticket_id);
}

/**
 * Tickets Contact Sales Get Brand Info
 *
 * @param unknown $brand Brand
 *
 * @return unknown
 */
function tickets_contact_sales_get_brand_info($brand)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_contact_sales_get_brand_info($brand);
}

/**
 * Tickets Contact Sales Log Event
 *
 * @param unknown $contact_sale_id Contact Sale Id
 * @param unknown $reply           Reply
 * @param unknown $reply_length    Reply Length
 * @param unknown $time_to_reply   Time To Reply
 * @param unknown $actioner_id     Actioner Id
 *
 * @return unknown
 */
function tickets_contact_sales_log_event($contact_sale_id, $reply, $reply_length, $time_to_reply, $actioner_id)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_contact_sales_log_event($contact_sale_id, $reply, $reply_length, $time_to_reply, $actioner_id);
}

/**
 * Tickets Get Out Standing Contact Sales Report By Date
 *
 * @param unknown $dtmStartDate Dtm Start Date
 *
 * @return unknown
 */
function ticketsGetOutStandingContactSalesReportByDate($dtmStartDate = '00-00-00 00:00:00')
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->ticketsGetOutStandingContactSalesReportByDate($dtmStartDate);
}

/**
 * Tickets Get Out Standing Contact Sales Report Latest
 *
 * @return unknown
 */
function ticketsGetOutStandingContactSalesReportLatest()
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->ticketsGetOutStandingContactSalesReportLatest();
}

/**
 * Tickets Get Out Standing Contact Sales For Report
 *
 * @param unknown $dteStartDate   Dte Start Date
 * @param unknown $dteEndDate     Dte End Date
 * @param string  $strDatePattern Date Pattern
 *
 * @return unknown
 */
function ticketsGetOutStandingContactSalesForReport($dteStartDate, $dteEndDate, $strDatePattern)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->ticketsGetOutStandingContactSalesForReport($dteStartDate, $dteEndDate, $strDatePattern);
}

/**
 * Tickets Contact Sales Get All Open
 *
 * @param unknown $from_limit          From Limit
 * @param unknown $to_limit            To Limit
 * @param array   $arrRestrictToBrands Restrict To Brands
 *
 * @return unknown
 */
function tickets_contact_sales_get_all_open($from_limit = false, $to_limit = false, $arrRestrictToBrands = array())
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_contact_sales_get_all_open($from_limit, $to_limit, $arrRestrictToBrands);
}

/**
 * Tickets Get Scheduled Bulk Move Rules
 *
 * @return unknown
 */
function tickets_get_scheduled_bulk_move_rules()
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_get_scheduled_bulk_move_rules();
}

/**
 * Tickets Get User Created Tickets
 *
 * @param unknown $user_id     User Id
 * @param unknown $whereclause Whereclause
 *
 * @return unknown
 */
function tickets_get_user_created_tickets($user_id, $whereclause = '')
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_get_user_created_tickets($user_id, $whereclause);
}

/**
 * Tickets Get User Closed Tickets
 *
 * @param unknown $user_id     User Id
 * @param unknown $whereclause Whereclause
 *
 * @return unknown
 */
function tickets_get_user_closed_tickets($user_id, $whereclause = '')
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_get_user_closed_tickets($user_id, $whereclause);
}

/**
 * Tickets Get Department Closed Tickts
 *
 * @param unknown $team_id     Team Id
 * @param unknown $whereclause Whereclause
 *
 * @return unknown
 */
function tickets_get_department_closed_tickts($team_id, $whereclause = '')
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_get_department_closed_tickts($team_id, $whereclause);
}

/**
 * Tickets Archivable Tickets Get
 *
 * @return unknown
 */
function tickets_archivable_tickets_get()
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_archivable_tickets_get();
}

/**
 * Tickets Get Locked Ticket Owner Id
 *
 * @param unknown $ticket_id Ticket Id
 *
 * @return unknown
 */
function tickets_get_locked_ticket_owner_id($ticket_id)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_get_locked_ticket_owner_id($ticket_id);
}

/**
 * Tickets Get Locked Ticket Default Expiry Length
 *
 * @param unknown $owner_id Owner Id
 *
 * @return unknown
 */
function tickets_get_locked_ticket_default_expiry_length($owner_id)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_get_locked_ticket_default_expiry_length($owner_id);
}

/**
 * Tickets Get Unlockable Tickets
 *
 * @return unknown
 */
function tickets_get_unlockable_tickets()
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_get_unlockable_tickets();
}

/**
 * Tickets Get Locked Ticket Details
 *
 * @param unknown $ticket_id Ticket Id
 *
 * @return unknown
 */
function tickets_get_locked_ticket_details($ticket_id)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_get_locked_ticket_details($ticket_id);
}

/**
 * Tickets Open Tickets Fields Get Array
 *
 * @return unknown
 */
function tickets_open_tickets_fields_get_array()
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_open_tickets_fields_get_array();
}

/**
 * Tickets Count By Contact ID
 *
 * @param int $intContactSaleID Contact Sale ID
 *
 * @return unknown
 */
function TicketsCountByContactID($intContactSaleID)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->TicketsCountByContactID($intContactSaleID);
}

/**
 * Tickets Get All Tickets By Contact ID
 *
 * @param int $intContactSaleID Contact Sale ID
 *
 * @return unknown
 */
function TicketsGetAllTicketsByContactID($intContactSaleID)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->TicketsGetAllTicketsByContactID($intContactSaleID);
}

/**
 * Tickets Get Ticket Being Worked On
 *
 * @param string $strUserID       User ID
 * @param int    $intPartnerID    Partner ID
 * @param string $strDatabaseName Database Name
 *
 * @return unknown
 */
function TicketsGetTicketBeingWorkedOn($strUserID, $intPartnerID, $strDatabaseName)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->TicketsGetTicketBeingWorkedOn($strUserID, $intPartnerID, $strDatabaseName);
}

/**
 * Arr Tickets Push Get Next Ticket
 *
 * @param string $strUserID       User ID
 * @param int    $intTeamID       Team ID
 * @param string $strCallCentre   Call Centre
 * @param string $strCustomerType Customer Type
 *
 * @return unknown
 */
function arrTicketsPushGetNextTicket($strUserID, $intTeamID, $strCallCentre, $strCustomerType = 'residential')
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->arrTicketsPushGetNextTicket($strUserID, $intTeamID, $strCallCentre, $strCustomerType);
}

/**
 * Bol Tickets Push User Has Passed
 *
 * @param string $strUserID    User ID
 * @param int    $intTicketID  Ticket ID
 * @param int    $intPartnerID Partner ID
 *
 * @return unknown
 */
function bolTicketsPushUserHasPassed($strUserID, $intTicketID, $intPartnerID)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->bolTicketsPushUserHasPassed($strUserID, $intTicketID, $intPartnerID);
}

/**
 * Bol Tickets Push Currently Worked On
 *
 * @param int $intTicketID  Ticket ID
 * @param int $intPartnerId Partner Id
 *
 * @return unknown
 */
function bolTicketsPushCurrentlyWorkedOn($intTicketID, $intPartnerId)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->bolTicketsPushCurrentlyWorkedOn($intTicketID, $intPartnerId);
}

/**
 * Arr Tickets Get User Actioned Tickets By Date
 *
 * @param string  $strUserID        User ID
 * @param unknown $dtmStartDateTime Dtm Start Date Time
 * @param unknown $dtmEndDateTime   Dtm End Date Time
 *
 * @return unknown
 */
function arrTicketsGetUserActionedTicketsByDate($strUserID, $dtmStartDateTime, $dtmEndDateTime)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->arrTicketsGetUserActionedTicketsByDate($strUserID, $dtmStartDateTime, $dtmEndDateTime);
}

/**
 * Arr Tickets Get User Contact Tickets By Date
 *
 * @param string  $strUserID        User ID
 * @param unknown $dtmStartDateTime Dtm Start Date Time
 * @param unknown $dtmEndDateTime   Dtm End Date Time
 *
 * @return unknown
 */
function arrTicketsGetUserContactTicketsByDate($strUserID, $dtmStartDateTime, $dtmEndDateTime)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->arrTicketsGetUserContactTicketsByDate($strUserID, $dtmStartDateTime, $dtmEndDateTime);
}

/**
 * Arr Tickets Get User Passed Tickets By Date
 *
 * @param string  $strUserID        User ID
 * @param unknown $dtmStartDateTime Dtm Start Date Time
 * @param unknown $dtmEndDateTime   Dtm End Date Time
 *
 * @return unknown
 */
function arrTicketsGetUserPassedTicketsByDate($strUserID, $dtmStartDateTime, $dtmEndDateTime)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->arrTicketsGetUserPassedTicketsByDate($strUserID, $dtmStartDateTime, $dtmEndDateTime);
}

/**
 * Arr Tickets Get User Touched Tickets By Date
 *
 * @param string  $strUserID        User ID
 * @param unknown $dtmStartDateTime Dtm Start Date Time
 * @param unknown $dtmEndDateTime   Dtm End Date Time
 *
 * @return unknown
 */
function arrTicketsGetUserTouchedTicketsByDate($strUserID, $dtmStartDateTime, $dtmEndDateTime)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->arrTicketsGetUserTouchedTicketsByDate($strUserID, $dtmStartDateTime, $dtmEndDateTime);
}

/**
 * Get Ticket Action Log Id
 *
 * @param string $strAction Action
 *
 * @return unknown
 */
function getTicketActionLogId($strAction)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->getTicketActionLogId($strAction);
}

/**
 * Get Employee Time On Ticket
 *
 * @param string $strEmployeeId Employee Id
 * @param int    $intTicketId   Ticket Id
 * @param int    $intPartnerId  Partner Id
 *
 * @return unknown
 */
function getEmployeeTimeOnTicket($strEmployeeId, $intTicketId, $intPartnerId)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->getEmployeeTimeOnTicket($strEmployeeId, $intTicketId, $intPartnerId);
}

/**
 * Get Average Employee Time On Tickets Over Period
 *
 * @param string  $strEmployeeId  Employee Id
 * @param unknown $dtmStartDate   Dtm Start Date
 * @param unknown $dtmEndDate     Dtm End Date
 * @param array   $arrValidStatus Valid Status
 *
 * @return unknown
 */
function getAverageEmployeeTimeOnTicketsOverPeriod($strEmployeeId, $dtmStartDate, $dtmEndDate, $arrValidStatus = array())
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->getAverageEmployeeTimeOnTicketsOverPeriod($strEmployeeId, $dtmStartDate, $dtmEndDate, $arrValidStatus);
}

/**
 * Tickets Check For Fault Tickets
 *
 * @param array &$arrTickets Tickets
 *
 * @return unknown
 */
function ticketsCheckForFaultTickets(&$arrTickets)
{
    $session = new BusinessTierSession('PLUSNET');

    $arrTicketIDs = array();

    foreach ($arrTickets as $arrTicket) {
        if (isset($arrTicket['ticket_id']) && $arrTicket['ticket_id']) {
            $arrTicketIDs[] = $arrTicket['ticket_id'];
        }
    }

    $arrFaultStatuses = $session->getFaultStatusByTicketID($arrTicketIDs);
    if (!$arrFaultStatuses) {
        return;
    }

    $arrFaults = array();

    foreach ($arrFaultStatuses as $arrFaultStatus) {
        foreach ($arrFaultStatus['faultStatusValueObjects'] as $arrFault) {
            $arrFaults[$arrFault['ticketID']] = $arrFault;
        }
    }

    foreach ($arrTickets as $i => $arrTicket) {
        if (isset($arrFaults[$arrTicket['ticket_id']])) {
            $arrFault = $arrFaults[$arrTicket['ticket_id']];

            $arrFaultDetails = $session->getFaultDetails($arrFault['faultID']);
            if (!$arrFaultDetails) {
                continue;
            }

            list($arrIndices, $arrValues)
                = XMLParser::parseIntoArray($arrFaultDetails['diagnosticQestionResults']);

            $arrQuestions = array();
            if (isset($arrIndices['ANSWER']) && is_array($arrIndices['ANSWER'])) {
                foreach ($arrIndices['ANSWER'] as $intIndex) {
                    $strQuestion = $arrValues[$intIndex]['attributes']['PROMPT'];
                    $strAnswer = $arrValues[$intIndex]['attributes']['VALUE'];

                    $arrQuestions[$strQuestion] = $strAnswer;
                }
            }
            $arrTickets[$i]['fault_id'] = $arrFault['faultID']['faultID'];
            $arrTickets[$i]['fault_status'] = $arrFault['faultStatusHandle'];

            if (count($arrQuestions)) {
                $arrTickets[$i]['fault_questions'] = $arrQuestions;
            }
        }
    }
}

/**
 * Tickets Check Pushing Bypass Permissions
 *
 * @param string $strUserID User ID
 *
 * @return unknown
 */
function tickets_checkPushingBypassPermissions($strUserId)
{
        $objTickets = Lib_Tickets::singleton();
        return $objTickets->tickets_checkPushingBypassPermissions($strUserId);
}

/**
 * Get Active Priority Config
 *
 * @param string $strUserID              User ID
 * @param bool   $bolIgnorePassedTickets Ignore Passed Tickets
 *
 * @return unknown
 */
function GetActivePriorityConfig($strUserID, $bolIgnorePassedTickets = true)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->GetActivePriorityConfig($strUserID, $bolIgnorePassedTickets);
}

/**
 * Get Tickets To Ignore
 *
 * @param string $strUserID User ID
 *
 * @return unknown
 */
function getTicketsToIgnore($strUserID)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->getTicketsToIgnore($strUserID);
}

/**
 * Get Open Tickets By Class ID
 *
 * @param int $intServiceID Service ID
 * @param int $intClassID   Class ID
 *
 * @return unknown
 */
function getOpenTicketsByClassID($intServiceID, $intClassID)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->getOpenTicketsByClassID($intServiceID, $intClassID);
}

/**
 * Tickets Ticket Update
 *
 * @param unknown $ticket_id      Ticket Id
 * @param unknown $owner_id       Owner Id
 * @param unknown $error_class    Error Class
 * @param unknown $error_subclass Error Subclass
 * @param unknown $team_id        Team Id
 * @param unknown $status         Status
 * @param unknown $actioner_id    Actioner Id
 * @param unknown $contact_count  Contact Count
 *
 * @return unknown
 */
function tickets_ticket_update($ticket_id, $owner_id, $error_class, $error_subclass, $team_id, $status, $actioner_id, $contact_count)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_ticket_update($ticket_id, $owner_id, $error_class, $error_subclass, $team_id, $status, $actioner_id, $contact_count);
}

/**
 * Tickets Clear Return To
 *
 * @param unknown $return_to Return To
 *
 * @return unknown
 */
function tickets_clear_return_to($return_to)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_clear_return_to($return_to);
}

/**
 * Tickets Contact Sales Update Actioner Id
 *
 * @param unknown $sales_contact_id Sales Contact Id
 * @param unknown $action_id        Action Id
 *
 * @return unknown
 */
function tickets_contact_sales_update_actioner_id($sales_contact_id, $action_id)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_contact_sales_update_actioner_id($sales_contact_id, $action_id);
}

/**
 * Tickets Contact Sales Update Status
 *
 * @param unknown $sales_contact_id Sales Contact Id
 * @param unknown $status           Status
 *
 * @return unknown
 */
function tickets_contact_sales_update_status($sales_contact_id, $status)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_contact_sales_update_status($sales_contact_id, $status);
}

/**
 * Tickets Update Error Class
 *
 * @param unknown $error_class_id Error Class Id
 * @param unknown $name           Name
 *
 * @return unknown
 */
function tickets_update_error_class($error_class_id, $name)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_update_error_class($error_class_id, $name);
}

/**
 * Tickets Update Error Class Description
 *
 * @param unknown $error_class_id Error Class Id
 * @param unknown $description    Description
 *
 * @return unknown
 */
function tickets_update_error_class_description($error_class_id, $description)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_update_error_class_description($error_class_id, $description);
}

/**
 * Tickets Ticket Archive
 *
 * @param unknown $ticket_id             Ticket Id
 * @param unknown $ticket_archive_run_id Ticket Archive Run Id
 *
 * @return unknown
 */
function tickets_ticket_archive($ticket_id, $ticket_archive_run_id)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_ticket_archive($ticket_id, $ticket_archive_run_id);
}

/**
 * Tickets Archive Run Complete
 *
 * @param unknown $ticket_archive_run_id Ticket Archive Run Id
 * @param unknown $num_services_archived Num Services Archived
 * @param unknown $num_tickets_archived  Num Tickets Archived
 *
 * @return unknown
 */
function tickets_archive_run_complete($ticket_archive_run_id, $num_services_archived, $num_tickets_archived)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_archive_run_complete($ticket_archive_run_id, $num_services_archived, $num_tickets_archived);
}

/**
 * Tickets Ticket Release
 *
 * @param unknown $ticket_id Ticket Id
 *
 * @return unknown
 */
function tickets_ticket_release($ticket_id)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_ticket_release($ticket_id);
}

/**
 * Tickets Ticket Unlock
 *
 * @param unknown $ticket_id   Ticket Id
 * @param unknown $releaser_id Releaser Id
 *
 * @return unknown
 */
function tickets_ticket_unlock($ticket_id, $releaser_id)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_ticket_unlock($ticket_id, $releaser_id);
}

/**
 * Log Employee On Ticket Start Time
 *
 * @param string $strEmployeeId Employee Id
 * @param int    $intTicketId   Ticket Id
 * @param int    $intPartnerId  Partner Id
 *
 * @return unknown
 */
function LogEmployeeOnTicketStartTime($strEmployeeId, $intTicketId, $intPartnerId)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->LogEmployeeOnTicketStartTime($strEmployeeId, $intTicketId, $intPartnerId);
}

/**
 * Log Employee On Ticket Stop Time
 *
 * @param string $strEmployeeId       Employee Id
 * @param int    $intTicketId         Ticket Id
 * @param int    $intPartnerId        Partner Id
 * @param string $strLastTicketAction Last Ticket Action
 * @param int    $intLogId            Log Id
 *
 * @return unknown
 */
function LogEmployeeOnTicketStopTime($strEmployeeId, $intTicketId, $intPartnerId, $strLastTicketAction, $intLogId)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->LogEmployeeOnTicketStopTime($strEmployeeId, $intTicketId, $intPartnerId, $strLastTicketAction, $intLogId);
}

/**
 * Tickets Update Out Standing Contact Sales
 *
 * @param int $intContactSalesID Contact Sales ID
 *
 * @return unknown
 */
function ticketsUpdateOutStandingContactSales($intContactSalesID = 0)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->ticketsUpdateOutStandingContactSales($intContactSalesID);
}

/**
 * Tickets Set Rating
 *
 * @param int $intTicketID Ticket ID
 * @param int $intRating   Rating
 *
 * @return unknown
 */
function ticketsSetRating($intTicketID, $intRating)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->ticketsSetRating($intTicketID, $intRating);
}

/**
 * Ticket Reminder Delete
 *
 * @param unknown $ticket_reminder_id Ticket Reminder Id
 *
 * @return unknown
 */
function ticket_reminder_delete($ticket_reminder_id)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->ticket_reminder_delete($ticket_reminder_id);
}

/**
 * Tickets Delete Error Class
 *
 * @param unknown $error_class_id Error Class Id
 *
 * @return unknown
 */
function tickets_delete_error_class($error_class_id)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_delete_error_class($error_class_id);
}

/**
 * Tickets Open Ticket Summary Delete
 *
 * @param unknown $ticket_id  Ticket Id
 * @param unknown $partner_id Partner Id
 *
 * @return unknown
 */
function tickets_open_ticket_summary_delete($ticket_id, $partner_id, $complaintResolvedReasonId=null)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_open_ticket_summary_delete($ticket_id, $partner_id, $complaintResolvedReasonId);
}

/**
 * Tickets Ticket Get Service Id
 *
 * @param unknown $ticket_id  Ticket Id
 * @param unknown $dont_cache Dont Cache
 *
 * @return unknown
 */
function tickets_ticket_get_service_id($ticket_id, $dont_cache = false)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_ticket_get_service_id($ticket_id, $dont_cache);
}

/**
 * Get Tickets With Topics
 *
 * @param int $intServiceID       Service ID
 * @param int $intQuestionTopicID Question Topic ID
 *
 * @return unknown
 */
function getTicketsWithTopics($intServiceID, $intQuestionTopicID = null)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->getTicketsWithTopics($intServiceID, $intQuestionTopicID);
}

/**
 * Get First Ticket Contact Details
 *
 * @param int $intTicketId Ticket Id
 *
 * @return unknown
 */
function getFirstTicketContactDetails($intTicketId)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->getFirstTicketContactDetails($intTicketId);
}

/**
 * Tickets Ticket Set Mobile Number
 *
 * @param int    $intTicketId     Ticket Id
 * @param string $strMobileNumber Mobile Number
 *
 * @return unknown
 */
function tickets_ticket_set_mobile_number($intTicketId, $strMobileNumber)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_ticket_set_mobile_number($intTicketId, $strMobileNumber);
}

/**
 * Tickets Ticket Set Sms Optin
 *
 * @param int  $intTicketId Ticket Id
 * @param bool $bolOptin    Optin
 *
 * @return unknown
 */
function tickets_ticket_set_sms_optin($intTicketId, $bolOptin = 0)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->tickets_ticket_set_sms_optin($intTicketId, $bolOptin);
}

/**
 * Add Retention Ticket
 *
 * @param int $intTicketId  Ticket Id
 * @param int $intContactId Contact Id
 *
 * @return unknown
 */
function addRetentionTicket($intTicketId, $intContactId)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->addRetentionTicket($intTicketId, $intContactId);
}

/**
 * Get Retention Ticket
 *
 * @param int $intTicketId Ticket Id
 *
 * @return unknown
 */
function getRetentionTicket($intTicketId)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->getRetentionTicket($intTicketId);
}

/**
 * Update Retention Ticket
 *
 * @param int  $intTicketId      Ticket Id
 * @param int  $intContactId     Contact Id
 * @param bool $bolOfferAccepted Offer Accepted
 *
 * @return unknown
 */
function updateRetentionTicket($intTicketId, $intContactId, $bolOfferAccepted)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->updateRetentionTicket($intTicketId, $intContactId, $bolOfferAccepted);
}

/**
 * Get Retention Tickets To Action
 *
 * @return unknown
 */
function getRetentionTicketsToAction()
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->getRetentionTicketsToAction();
}

/**
 * Tickets Request Ticket Reservation With Owner
 *
 * @param int    $intTicketId      Ticket Id
 * @param string $strOwnerId       Owner Id
 * @param bool   $bolExitOnFailure Exit On Failure
 *
 * @return unknown
 */
function ticketsRequestTicketReservationWithOwner($intTicketId, $strOwnerId, $bolExitOnFailure = true)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->ticketsRequestTicketReservationWithOwner($intTicketId, $strOwnerId, $bolExitOnFailure);
}

/**
 * Tickets Get Customer Comment Count
 *
 * @param int $intTicketId Ticket Id
 *
 * @return unknown
 */
function ticketsGetCustomerCommentCount($intTicketId)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->ticketsGetCustomerCommentCount($intTicketId);
}

/**
 * Tickets Get Fault Ticket Id
 *
 * @param unknown $serviceId Service Id
 *
 * @return unknown
 */
function ticketsGetFaultTicketId($serviceId)
{

    $objTickets = Lib_Tickets::singleton();
    return $objTickets->ticketsGetFaultTicketId($serviceId);
}

/**
 * Get Billing Complaints Reasons
 *
 * @return array
 */
function getBillingComplaintReasons()
{
    $objTickets = Lib_Tickets::singleton();
    return $objTickets->getBillingComplaintReasons();
}

/**
 * Get Complaints Resolved Reasons
 *
 * @return array
 */
function getComplaintResolvedReasons()
{
    $objTickets = Lib_Tickets::singleton();
    return $objTickets->getComplaintResolvedReasons();
}

/**
 * Get Complaints Channels
 *
 * @return array
 */
function getComplaintChannels()
{
    $objTickets = Lib_Tickets::singleton();
    return $objTickets->getComplaintChannels();
}

/**
 * Get Complaints Raised By
 *
 * @return array
 */
function getComplaintRaisedBy()
{
    $objTickets = Lib_Tickets::singleton();
    return $objTickets->getComplaintRaisedBy();
}

/**
 * Get Open ticket Complaints Count
 *
 * @return array
 */
function getOpenTicketComplaintsByServiceId($serviceId)
{
    $objTickets = Lib_Tickets::singleton();
    return $objTickets->getOpenTicketComplaintsByServiceId($serviceId);
}

/**
 * Get Closed ticket Complaints Count
 *
 * @return array
 */
function getClosedWithin56DaysTicketComplaintsByServiceId($serviceId)
{
    $objTickets = Lib_Tickets::singleton();
    return $objTickets->getClosedWithin56DaysTicketComplaintsByServiceId($serviceId);
}
