<?php
require_once '/local/data/mis/database/database_libraries/components/MAAF/Webspace.class.php';
$global_component_configurators[MAAF_Component_Webspace::COMPONENT_TYPE_ID] = 'config_MAAFWebspace_configurator';

function config_MAAFWebspace_configurator($intComponentID, $strAction)
{
	try {
		
		MAAF_Component_Webspace::configureComponent($intComponentID, $strAction);
		
	} catch (Exception $objException) {
		return false;
	}
	
	return true;
}
