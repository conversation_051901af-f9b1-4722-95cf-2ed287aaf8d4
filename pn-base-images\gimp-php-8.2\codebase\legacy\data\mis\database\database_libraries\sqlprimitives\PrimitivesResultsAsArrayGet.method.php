<?php
/**
 * Bare-bones cleanup - these changes will be superceded by PLATOPT-37
 */

	//////////////////////////////////////////////////////////
	// Function  : split_PrimitivesResultsAsArrayGet
	//           : (wrapper function to get_mysql_results_as_array)
	//
	// Purpose   : to get the results from a query as an array
	// Arguments : $resResult,  (the MySQL result)
	//           : $strIndexOn, (optional field to use as the
	//             array's index)
	// Returns   : array of each entry in mysql result set
	//////////////////////////////////////////////////////////

function split_PrimitivesResultsAsArrayGet($resResult, $strIndexOn = '')
{
    $result_array = array();

    while ($this_row = mysql_fetch_array($resResult, MYSQL_ASSOC)) {
        if ($strIndexOn == '' || (!isset($this_row[$strIndexOn]))) {
            $result_array[] = $this_row;
        } else {
            $result_array[$this_row[$strIndexOn]] = $this_row;
        }
    }

    mysql_free_result($resResult);

    return $result_array;
}
