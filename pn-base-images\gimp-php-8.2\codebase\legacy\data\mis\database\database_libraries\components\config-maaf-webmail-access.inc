<?php

if (!defined('COMPONENT_MAAF_WEBMAIL')) {

    require_once '/local/data/mis/database/database_libraries/components/component-defines.inc';
}

global $global_component_configurators;

$global_component_configurators[COMPONENT_MAAF_WEBMAIL] = 'config_webmail_configurator';

function config_webmail_configurator($intComponentID, $strAction)
{
    try {

        MAAF_CProduct_Webmail::configure($intComponentID, $strAction);

    } catch (Exception $objException) {

        error_log(basename(__FILE__)  . ':' . $objException->getMessage());
        return false;
    }

    return true;
}