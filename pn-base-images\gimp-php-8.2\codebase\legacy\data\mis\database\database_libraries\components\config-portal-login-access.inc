<?php
/////////////////////////////////////////////////////////////////////
// File:     config-portal-login-access.inc
// Purpose:  Access mini-library for config_portal_logins
/////////////////////////////////////////////////////////////////////

require_once (COMMON_API_ROOT . 'BusinessTier.php');
require_once (COMMON_API_ROOT . 'Framework.php');
require_once '/local/data/mis/database/crypt_config.inc';
require_once USERDATA_ACCESS_LIBRARY;
require_once '/local/data/mis/database/class_libraries/Brightview/Library.inc';

/////////////////////////////////////////////////////////////////////
// Data
$global_component_configurators[COMPONENT_PLUS_PORTAL_LOGIN]               = 'configure_portal_login_plusnet_configurator';
$global_component_configurators[COMPONENT_FOL_PORTAL_LOGIN]                = 'configure_portal_login_freeonline_configurator';
$global_component_configurators[COMPONENT_F9_PORTAL_LOGIN]                 = 'configure_portal_login_force9_configurator';
$global_component_configurators[COMPONENT_PNUK_PORTAL_LOGIN]               = 'configure_portal_login_plusnetuk_configurator';
$global_component_configurators[COMPONENT_SEARCHPRO_PORTAL_LOGIN]          = 'configure_portal_login_searchpro_configurator';
$global_component_configurators[COMPONENT_DABSOL_PORTAL_LOGIN]             = 'configure_portal_login_dabsol_configurator';
$global_component_configurators[COMPONENT_PNUK_RESELLER_PORTAL_LOGIN]      = 'configure_portal_login_pnukreseller_configurator';
$global_component_configurators[COMPONENT_SEARCHPRO_RESELLER_PORTAL_LOGIN] = 'configure_portal_login_spreseller_configurator';
$global_component_configurators[COMPONENT_METRONET_PORTAL_LOGIN]           = 'configure_portal_login_metronet_configurator';
$global_component_configurators[COMPONENT_MAAF_PORTAL_LOGIN]               = 'configure_portal_login_madasafish_configurator';
$global_component_configurators[COMPONENT_WAITROSE_PORTAL_LOGIN]           = 'configure_portal_login_waitrose_configurator';
$global_component_configurators[COMPONENT_GREENBEE_PORTAL_LOGIN]           = 'configure_portal_login_greenbee_configurator';
$global_component_configurators[COMPONENT_PARTNER_PORTAL_LOGIN]            = 'configure_portal_login_partner_configurator';
$global_component_configurators[COMPONENT_PARTNER_ADMIN_LOGIN]             = 'configure_portal_login_partner_configurator';
$global_component_configurators[COMPONENT_PARTNER_ENDUSER]                 = 'configure_partner_enduser_configurator';
$global_component_configurators[COMPONENT_PARTNER_ADMIN]                   = 'configure_partner_admin_configurator';
$global_component_configurators[COMPONENT_JOHNLEWIS_PORTAL_LOGIN]          = 'configure_portal_login_johnlewis_configurator';
$global_component_configurators[COMPONENT_VODAFONE_PORTAL_LOGIN]           = 'configure_portal_login_vodafone_configurator';
// Data
/////////////////////////////////////////////////////////////////////

// Hack to insert the component configurator array into PHP5's global scope
// if it's not already there
if (!isset($GLOBALS['global_component_configurators'])) {
    $GLOBALS['global_component_configurators'] = $global_component_configurators;
}
else {
    foreach($global_component_configurators as $intIndex => $strConfigurator) {
        $GLOBALS['global_component_configurators'][$intIndex] = $strConfigurator;
    }
}

/////////////////////////////////////////////////////////////////////
// Library functions
/////////////////////////////////////////////////////////////

/**
 * Add a user to the auth_user table for a portal
 *
 * @param integer $component_id ID of component to configure
 * @param string $portalname name of the portal to configure
 * @param string $username user's username
 * @param string $password user's password
 * @param string $user_type user type
 * @param string $strPartnerBrand Partner brand CHESS/PLUSNET
 * @return boolean false on failure, void on success (obviously)
 */
function config_portal_login_physical_add($component_id, $portalname, $username, $password, $user_type = null, $strPartnerBrand = null)
{
    $strEncryptedPassword = Crypt_Crypt::encrypt($password, 'tblBusinessActor');
    $username             = mysql_real_escape_string($username);
    $password             = mysql_real_escape_string($password);
    $arrComponent         = userdata_component_get($component_id);
    $service_id           = $arrComponent['service_id'];

    // Update the Java system
    $strRealm = translatePortalNameToAuthenticationRealm($portalname);

    if ($strRealm) {
        $sess = new BusinessTierSession('PLUSNET');

        if('PLUSNET_PARTNER_ADMIN_USER' == $user_type) {
            $sess->createUser('PLUSNET_PARTNER_ADMIN_USER', $strRealm, $service_id, $username, $strEncryptedPassword);
        } else {
            $sess->createUser('PLUSNET_ENDUSER', $strRealm, $service_id, $username, $strEncryptedPassword);
        }

        $arrServiceData  = userdata_service_get($service_id);
        $arrUserData     = userdata_user_get($arrServiceData['user_id']);
        $arrCustomerData = userdata_customer_get($arrUserData['customer_id']);

        // add user to framework visibility layers
        $intBusinessActorId = $sess->getBusinessActorId($username, $password, $strRealm);
        $intParentBusinessActorID = $intBusinessActorId;
        $objSessManager = new BusinessTierSession('PLUSNET');

        if (!$intBusinessActorId = $objSessManager->getBusinessActorId($username, $strRealm)) {
            $objSessManager->createUser('PLUSNET_ENDUSER', $strRealm, $service_id, $username, $strEncryptedPassword);

            if (FALSE == ($intBusinessActorId = $objSessManager->getBusinessActorId($username, $strRealm))) {
                error_log("Adding Business actor failed on retrieve 'PLUSNET_ENDUSER', $strRealm, $service_id, $username, $strEncryptedPassword");
                return false;
            }
        }
        else {
            $objSessManager->changeUserPassword($username, $strRealm, $strEncryptedPassword);
        }

        // Update the framework's visiblity and permissions.
        $arrServiceData = userdata_service_get($service_id);
        $arrUserData = userdata_user_get($arrServiceData['user_id']);
        $arrCustomerData = userdata_customer_get($arrUserData['customer_id']);
        $arrProductData = product_get_service($arrServiceData['type']);

        // create a proper foreign key dependency between actor and service
        if (false == Core_ActorServiceRelation::existsForActorId(new Int($intBusinessActorId))->getValue()) {
            Core_ActorServiceRelation::atomicCreation(new Int($intBusinessActorId) , new Int($service_id));
        }

        $strCompanyDisplayName = $arrCustomerData['company_name'];
        $strUserDisplayName = $username . '@' . $arrCustomerData['company_name'];

        if (trim($arrCustomerData['company_name']) == '' || trim($arrCustomerData['company_name']) == 'N/A') {
            $strCompanyDisplayName = $username;
            $strUserDisplayName = $username;
        }

        $intCustomerId = $arrUserData['customer_id'];
        $arrActors = array();
        $arrActors[] = array(
            'intBusinessActorId'          => $intBusinessActorId,
            'intJobTemplateId'            => null,
            'arrSupplementaryRoleHandles' => array(),
        );
        $objTreeAdaptor = new Legacy_FrameworkTreeAdaptor('PLUSNET', 'WORKPLACE_ISP_ADMIN');
        $arrMapOfTheWorldLevels = $objTreeAdaptor->getMotwBaseByIsp($arrProductData['isp']);

        // ew, this hurts - but if you don't like it feel free to refactor properly
        if ('partner' == $arrServiceData['isp']) {

            if (isPartnerEndUser($username)) {
                $arrMapOfTheWorldLevels = addPartnerEuMotwLevels(
                $arrMapOfTheWorldLevels, $arrActors,
                $username, $intBusinessActorId, $strUserDisplayName);
            }
            else {
                $arrMapOfTheWorldLevels = addPartnerMotwLevels(
                $arrMapOfTheWorldLevels, $arrActors,
                $intCustomerId, $strCompanyDisplayName, $strPartnerBrand);
            }
        }
        else {
            $arrMapOfTheWorldLevels = addDefaultMotwLevels(
            $arrMapOfTheWorldLevels, $arrActors,
            $intCustomerId, $strCompanyDisplayName, $intBusinessActorId, $strUserDisplayName);
        }

        if ($arrProductData['isp'] === 'johnlewis' && in_array($arrServiceData['isp'], array('greenbee', 'waitrose'))) {
            // we are migrating from a GB/WR -> JLP, then force overwritting of ENDUSER Map of the World Key!
            foreach ($arrMapOfTheWorldLevels as $strLevel => $arrLevel) {

                /*
                 * Explanation of the check below and use of the Overwrite flag
                 *
                 * Basically when a product is migrated from Greenbee or Waitrose and onto John Lewis the Map of the
                 * World was incorrect. The map basically needed to be remapped to use John Lewis ISP which was not
                 * happening correctly.
                 *
                 * An example after migrated a Greenbee account... (note, ISP is still GREENBEE_ISP)
                 * +----------------+---------------+-------------------+----------------------+
                 * | intTreeLevelId | intTreeBandId | strTreeBandHandle | strTreeLevelHandle   |
                 * +----------------+---------------+-------------------+----------------------+
                 * |              1 |             1 | VISBAND_ROOT      | WORKPLACE_ROOT       |
                 * |              2 |             2 | VISBAND_PARTNER   | PLUSNET_PARTNER_ROOT |
                 * |        1989329 |             3 | VISBAND_ISP       | GREENBEE_ISP         |
                 * |        2307280 |             7 | VISBAND_ENDUSER   | ENDUSER:710966       |
                 * +----------------+---------------+-------------------+----------------------+
                 *
                 * To correct this any element in the Map of the World with ":" in the Key is remapped to JLP by using
                 * the overwrite flag which the framework now will use to remap.
                 *
                 * Assumption here that: keys after the ISP in the format- "KEY : ID", so we check for the colon
                 *
                 * So before the change:
                 * +----------------+---------------+----------------------+----------------------------------------------+---------------------+---------------------+
                 * | intTreeLevelId | intTreeBandId | vchHandle            | vchFullPath                                  | vchDisplayName      | stmLastUpdate       |
                 * +----------------+---------------+----------------------+----------------------------------------------+---------------------+---------------------+
                 * |              1 |             1 | WORKPLACE_ROOT       | /1                                           | Workplace Root      | 2007-10-16 16:36:00 |
                 * |              2 |             2 | PLUSNET_PARTNER_ROOT | /1/2                                         | Plusnet Partner     | 2007-10-16 16:36:00 |
                 * |        1989329 |             3 | GREENBEE_ISP         | /1/2/1989329                                 | Greenbee ISP        | 2008-05-18 22:58:35 |
                 * |        2297628 |             5 | CUSTOMER:1269547     | /1/2/1989329/2297628                         | adamwaugh           | 2012-02-10 13:33:16 |
                 * |        2297629 |             6 | ADMINS:1269547:1     | /1/2/1989329/2297628/2297629                 | Top Level Admins    | 2012-02-10 13:33:16 |
                 * |        2297630 |             6 | COSTCENTRE:1269547:1 | /1/2/1989329/2297628/2297629/2297630         | Default Cost Centre | 2012-02-10 13:33:16 |
                 * |        2297631 |             7 | ENDUSER:708559       | /1/2/1989329/2297628/2297629/2297630/2297631 | adamwaugh           | 2008-07-10 14:27:00 |
                 * +----------------+---------------+----------------------+----------------------------------------------+---------------------+---------------------+
                 *
                 * And after:
                 * +----------------+---------------+----------------------+----------------------------------------------+---------------------+---------------------+
                 * | intTreeLevelId | intTreeBandId | vchHandle            | vchFullPath                                  | vchDisplayName      | stmLastUpdate       |
                 * +----------------+---------------+----------------------+----------------------------------------------+---------------------+---------------------+
                 * |              1 |             1 | WORKPLACE_ROOT       | /1                                           | Workplace Root      | 2007-10-16 16:36:00 |
                 * |              2 |             2 | PLUSNET_PARTNER_ROOT | /1/2                                         | Plusnet Partner     | 2007-10-16 16:36:00 |
                 * |        4124102 |             3 | JOHN_LEWIS_ISP       | /1/2/4124102                                 | John Lewis ISP      | 2011-09-02 13:11:30 |
                 * |        2297628 |             5 | CUSTOMER:1269547     | /1/2/4124102/2297628                         | adamwaugh           | 2012-02-10 13:36:24 |
                 * |        2297629 |             6 | ADMINS:1269547:1     | /1/2/4124102/2297628/2297629                 | Top Level Admins    | 2012-02-10 13:36:24 |
                 * |        2297630 |             6 | COSTCENTRE:1269547:1 | /1/2/4124102/2297628/2297629/2297630         | Default Cost Centre | 2012-02-10 13:36:24 |
                 * |        2297631 |             7 | ENDUSER:708559       | /1/2/4124102/2297628/2297629/2297630/2297631 | adamwaugh           | 2012-02-10 13:36:24 |
                 * +----------------+---------------+----------------------+----------------------------------------------+---------------------+---------------------+
                 *
                 * For more information see JLPR-1716 (http://jira.internal.plus.net/browse/JLPR-1716)
                 */
                if (strpos($strLevel, ":") > 0) {
                // this is one of the keys which are non-default e.g. unique per actor/tree
                    $arrMapOfTheWorldLevels[$strLevel]['overwrite'] = true;
                }

            }
        }

        if (true !== $objTreeAdaptor->addOrUpdateLevels($arrMapOfTheWorldLevels)) {
            // set component to queued-activate to show failure
            userdata_component_set_status($component_id, 'queued-activate');

            // log to error log so people can find it
            $message = "ERROR: Failed to add Map Of The World entries for portal "
            . "login component '{$component_id}', service_id '{$service_id}', "
            . "business actor ID '{$intBusinessActorId}'. The user won't be "
            . "able to login to their portal, or recieve emails via the email "
            . "handler. The levels to be added were "
            . print_r($arrMapOfTheWorldLevels,true);
            error_log($message);

            // don't do anything else
            return false;
        }

        // Add the account to the SoulStone database
        if (userdataIsMaafUser($service_id) || $portalname == 'waitrose' || $portalname == 'greenbee') {

            try {
                $objCurrentAccount = Brightview_Account::findByServiceId($service_id);

                // Account exist - update data
                if (false != $objCurrentAccount) {
                    $objCurrentAccount->setPassword(Crypt_Crypt::encrypt($password, 'maafDes'));
                    $objCurrentAccount->setFullName($arrUserData['salutation'] . ' ' . $arrUserData['forenames'] . ' ' . $arrUserData['surname']);
                    $objCurrentAccount->setRealm(userdataGetFrameworkAuthRealm($service_id, $username));
                    $objCurrentAccount->setStatus('active');
                    $objCurrentAccount->save();
                }
                // There is no entry in Soulstone
                else {
                    $arrDefaults = array(
                        'parentid' => 0,
                        'username' => $username,
                        'realm'    => userdataGetFrameworkAuthRealm($service_id, $username) ,
                        'fullname' => $arrUserData['salutation'] . ' ' . $arrUserData['forenames'] . ' ' . $arrUserData['surname'],
                        'passwd'   => Crypt_Crypt::encrypt($password, 'maafDes') ,
                        'status'   => 'active',
                        'options'  => array('dialup', 'mail'),
                    );
                    // Create Soulstone config
                    $objAccount = new Brightview_Account($arrDefaults);
                    $objAccount->save();
                    // Save mailid on core DB
                    $objConfigSoulstone = new Brightview_ConfigSoulstone();
                    $objConfigSoulstone->setServiceId($service_id);
                    $objConfigSoulstone->setMailId($objAccount->getMailId());
                    $objConfigSoulstone->save();
                }
            }
            catch(Exception $e) {
                // set the component to queued-activate
                userdata_component_set_status($component_id, 'queued-activate');
                error_log("Exception: portal-login-component $component_id {$e->getMessage() }");
            }

            // Set component to active when configured
            userdata_component_set_status($component_id, 'active');
        }
    }
    else {
        //do the debug here for problem: 59334
        Dbg_Dbg::write(__CLASS__ . ' ' . __METHOD__ . " : Debugging for null Realm: Service Id: " .
        $service_id. "\n" . Logging::getCallStackAsString(),
                   'config-portal-login-access.inc');
    }
}

/**
 * Is the actor on the Partner Programme
 *
 * @param string $strUsername
 * @return boolean
 */
function isPartnerEndUser($strUsername)
{
    // check signup details to see if pending account
    $actor = Reseller_SignupHelper::findPendingActor(new Reseller_EndUserUsername($strUsername));
    if ($actor instanceof Auth_BusinessActor) {

        return true;
    }

    // check active users
    if(TRUE === Reseller_EndUser::isPartnerEndUser($strUsername)) {

        return true;
    }

    return false;
}

/**
 * Map of the world levels for a Partner
 *
 * @param array $arrMapOfTheWorldLevels
 * @param array $arrActors
 * @param integer $intCustomerId
 * @param string $strCompanyDisplayName
 * @param string $strPartnerBrand
 * @return array
 */
function addPartnerMotwLevels(array $arrMapOfTheWorldLevels, array $arrActors, $intCustomerId, $strCompanyDisplayName, $strPartnerBrand)
{
    $arrMapOfTheWorldLevels[$strPartnerBrand] = array(
        'strDisplayName' => $strPartnerBrand,
        'strBand'        => 'VISBAND_RESELLER'
        );

        $arrMapOfTheWorldLevels["CUSTOMER:$intCustomerId"] = array(
        'strDisplayName' => $strCompanyDisplayName,
        'strBand'        => 'VISBAND_CUSTOMER',
        'arrActors'      => $arrActors,
        );
        $arrMapOfTheWorldLevels["ADMINS:$intCustomerId:1"] = array(
        'strDisplayName' => 'Top Level Admins',
        'strBand'        => 'VISBAND_ADMIN',
        );
        $arrMapOfTheWorldLevels["COSTCENTRE:$intCustomerId:1"] = array(
        'strDisplayName' => 'Default Cost Centre',
        'strBand'        => 'VISBAND_ADMIN',
        );
        return $arrMapOfTheWorldLevels;
}

/**
 * Map of the world levels for a Partner End Users
 *
 * @param array $arrMapOfTheWorldLevels
 * @param array $arrActors
 * @param string $strUsername
 * @param integer $intBusinessActorId
 * @param string $strUserDisplayName
 * @return array
 */
function addPartnerEuMotwLevels(array $arrMapOfTheWorldLevels, array $arrActors, $strUsername, $intBusinessActorId, $strUserDisplayName)
{
    if (Reseller_SignupHelper::findPendingActor(new Reseller_EndUserUsername($strUsername)) instanceof Auth_BusinessActor) {
        $helper = Reseller_SignupHelper::retrieve(new Val_Username($strUsername));
        $partner = $helper->getPartnerActor();
    } else {
        // this is an active Partner Enduser account
        $partner = Auth_BusinessActor::getActorByAuthDetails($strUsername, 'partner.plus.net');
    }
    $arrPartnerService = userdata_service_get($partner->getExternalUserId());
    $arrPartnerUser = userdata_user_get($arrPartnerService['user_id']);
    $arrPartnerCustomer = userdata_customer_get($arrPartnerUser['customer_id']);
    $intPartnerCustomerId = $arrPartnerUser['customer_id'];

    if (trim($arrPartnerCustomer['company_name']) == '' || trim($arrPartnerCustomer['company_name']) == 'N/A') {
        $strPartnerCompany = $partner->getUsername();
    }

    unset($arrPartnerService, $arrPartnerUser, $arrPartnerCustomer, $helper, $partner);
    // EndUser
    $arrMapOfTheWorldLevels["CUSTOMER:$intPartnerCustomerId"] = array(
        'strDisplayName' => $strPartnerCompany,
        'strBand'        => 'VISBAND_CUSTOMER'
        );
        $arrMapOfTheWorldLevels["ADMINS:$intPartnerCustomerId:1"] = array(
        'strDisplayName' => 'Top Level Admins',
        'strBand'        => 'VISBAND_ADMIN'
        );
        $arrMapOfTheWorldLevels["COSTCENTRE:$intPartnerCustomerId:1"] = array(
        'strDisplayName' => 'Default Cost Centre',
        'strBand'        => 'VISBAND_ADMIN'
        );
        $arrMapOfTheWorldLevels["ENDUSER:$intBusinessActorId"] = array(
        'strDisplayName' => $strUserDisplayName,
        'strBand'        => 'VISBAND_ENDUSER',
        'arrActors'      => $arrActors
        );
        return $arrMapOfTheWorldLevels;
}

/**
 * Map of the world levels for all other vISPs
 *
 * @param array $arrMapOfTheWorldLevels
 * @param array $arrActors
 * @param integer $intCustomerId
 * @param string $strCompanyDisplayName
 * @param integer $intBusinessActorId
 * @param string $strUserDisplayName
 * @return array
 */
function addDefaultMotwLevels(array $arrMapOfTheWorldLevels, array $arrActors, $intCustomerId, $strCompanyDisplayName, $intBusinessActorId, $strUserDisplayName)
{
    $arrMapOfTheWorldLevels["CUSTOMER:$intCustomerId"] = array(
        'strDisplayName' => $strCompanyDisplayName,
        'strBand'        => 'VISBAND_CUSTOMER'
        );
        $arrMapOfTheWorldLevels["ADMINS:$intCustomerId:1"] = array(
        'strDisplayName' => 'Top Level Admins',
        'strBand'        => 'VISBAND_ADMIN'
        );
        $arrMapOfTheWorldLevels["COSTCENTRE:$intCustomerId:1"] = array(
        'strDisplayName' => 'Default Cost Centre',
        'strBand'        => 'VISBAND_ADMIN'
        );
        $arrMapOfTheWorldLevels["ENDUSER:$intBusinessActorId"] = array(
        'strDisplayName' => $strUserDisplayName,
        'strBand'        => 'VISBAND_ENDUSER',
        'arrActors'      => $arrActors
        );
        return $arrMapOfTheWorldLevels;
}

/**
 * Set password for a portal login in auth_user
 *
 * @param string $portalname name of the portal
 * @param string $username user's username
 * @param string $password user's new password
 * @return true on success, void on failure (just to keep things consistent)
 */
function config_portal_login_physical_set_password($portalname, $username, $password)
{
    // Update the Java system
    $strEncryptedPassword = Crypt_Crypt::encrypt($password, 'tblBusinessActor');
    $strRealm = translatePortalNameToAuthenticationRealm($portalname);

    if ($strRealm) {
        $sess = new BusinessTierSession('PLUSNET');
        $sess->changeUserPassword($username, $strRealm, $strEncryptedPassword);
    }

    return true;
}

/**
 * Delete an entry from auth_user
 *
 * @param string $portalname portal to remove from
 * @param string $username user to remove access from
 */
function config_portal_login_physical_delete($portalname, $username)
{
    // Update the Java system
    $strRealm = translatePortalNameToAuthenticationRealm($portalname);

    if ($strRealm) {
        $sess = new BusinessTierSession('PLUSNET');
        $sess->destroyUser($username, $strRealm);
    }
}

/**
 * Create a portal login configuration for a component
 *
 * @param integer $component_id id of corresponding component
 * @param string $portalname name of the portal to configure
 * @param string $user_type user type
 * @param string $strPartnerBrand
 * @return integer component ID
 */
function config_portal_login_add($component_id, $portalname, $user_type = null, $strPartnerBrand = null)
{
    global $global_db_src;

    $connection = get_named_connection('userdata');
    $component_id = mysql_real_escape_string($component_id);
    $portalname = mysql_real_escape_string($portalname);

    // First delete any login athat already exists for this component
    $delete = "DELETE FROM config_portal_logins WHERE component_id='$component_id'";
    mysql_query($delete, $connection);

    $component = userdata_component_get($component_id);
    $service = userdata_service_get($component['service_id']);
    $username = $service['username'];
    $password = $service['password'];
    $strEncryptedPassword = Crypt_Crypt::encrypt($password, 'config_portal_logins');

    // Create the config record
    $insert =
        'INSERT INTO config_portal_logins (component_id, portalname, username, password) ' .
        "VALUES ('$component_id', '$portalname', '$username', '$strEncryptedPassword')";
    mysql_query($insert, $connection) or report_error(__FILE__, __LINE__, mysql_error($connection));
    $portal_login_id = mysql_insert_id($connection);

    // Danger Will Robinson: This will probably steal $connection
    // Create the portal login
    config_portal_login_physical_add($component_id, $portalname, $username, $password, $user_type, $strPartnerBrand);
    return $portal_login_id;
}

/**
 * Get a component and portal configuration record
 *
 * @param integer $component_id
 * @return array
 */
function config_portal_login_get($component_id)
{
    global $global_db_src;

    $connection = get_named_connection('userdata');
    $component_id = mysql_real_escape_string($component_id);
    $select =
        'SELECT * FROM components ' .
        'LEFT JOIN config_portal_logins ON components.config_id = config_portal_logins.portal_login_id ' .
        "WHERE components.component_id = '$component_id'";
    $result = mysql_query($select, $connection) or report_error(__FILE__, __LINE__, mysql_error($connection));
    $union = mysql_fetch_array($result, MYSQL_ASSOC);
    mysql_free_result($result);

    return $union;
}

/**
 * Delete a portal login config
 *
 * @param integer $portal_login_id
 * @param integer $intServiceId
 */
function config_portal_login_delete($portal_login_id, $intServiceId)
{
    $connection = get_named_connection('userdata');
    $portal_login_id = mysql_real_escape_string($portal_login_id);

    // Find portal name and username
    $query =
        'SELECT cpl.portalname, s.username ' .
        'FROM config_portal_logins cpl ' .
        'INNER JOIN components c ON cpl.component_id = c.component_id ' .
        'INNER JOIN services s ON c.service_id = s.service_id ' .
        'WHERE cpl.portal_login_id = ' . $portal_login_id;
    $result = mysql_query($query, $connection) or report_error(__FILE__, __LINE__, mysql_error($connection));
    $row = mysql_fetch_array($result, MYSQL_ASSOC);
    mysql_free_result($result);

    $portalname = $row['portalname'];
    $username   = $row['username'];

    // Delete config
    $delete =
        'DELETE FROM config_portal_logins ' .
        "WHERE portal_login_id = '$portal_login_id'";

    mysql_query($delete, $connection) or report_error(__FILE__, __LINE__, mysql_error($connection));

    // Delete login
    if (strlen($portalname) > 0 && strlen($username) > 0) {
        config_portal_login_physical_delete($portalname, $username);
    }

    // Poison soulstone entry (if any)
    $objBvAccount = Brightview_Account::findByServiceId($intServiceId);

    if ($objBvAccount) {
        $objBvAccount->setStatus('poisoned');
        $objBvAccount->save();
    }
}

/**
 * Set a user's portal login password
 *
 * @param integer $component_id associated component id
 * @param string $portalname portal that the user should be allowed access to
 * @param array $portal_login portal login information record
 * @param string $password user's new password
 */
function config_portal_login_set_password($component_id, $portalname, $portal_login, $password)
{
    $connection = get_named_connection('userdata');

    $component = userdata_component_get($component_id);

    $service_id           = $component['service_id'];
    $strEncryptedPassword = Crypt_Crypt::encrypt($password, 'config_portal_logins');
    $password             = mysql_real_escape_string($password);
    $username             = $portal_login['username'];
    $portal_login_id      = $portal_login['portal_login_id'];

    $update =
        'UPDATE config_portal_logins ' .
        "SET password = '$strEncryptedPassword' " .
        "WHERE portal_login_id = '$portal_login_id'";

    mysql_query($update, $connection) or report_error(__FILE__, __LINE__, mysql_error($connection));

    // Update soulstone if needed
    if (userdataIsMaafUser($service_id)) {
        try {
            $objAccount = Brightview_Account::findByServiceId($service_id);
            $objAccount->setPassword(Crypt_Crypt::encrypt($password, 'maafDes'));
            $objAccount->save();
        }
        catch(Exception $e) {
            // set the component to queued-activate
            userdata_component_set_status($component_id, 'queued-activate');
            error_log("Exception: portal-login-component $component_id {$e->getMessage() }");
        }
    }

    if (!config_portal_login_physical_set_password($portalname, $username, $password)) {
        $service = userdata_service_get($service_id);
        config_portal_login_physical_add($component_id, $portalname, $username, $password);
    }
    else {
        //do the debug here for problem: 59334
        Dbg_Dbg::write(__CLASS__ . ' ' . __METHOD__ . " : Debugging for auto_enable action: Service Id: " .
        $service_id. "\n" . Logging::getCallStackAsString(),
                   'config-portal-login-access.inc');
    }
}

///////////////////////////////////////////////////////////////////
//
// Functions for population the Java busness layer with portal logins
//
//////////////////////////////////////////////////////////////////


/**
 * Translate portal name to authentication realm
 *
 * @param string $strPortalName
 * @return string
 */
function translatePortalNameToAuthenticationRealm($strPortalName)
{
    $arrPortalName = array(
        'force9'     => 'portal.f9.net.uk',
        'freeonline' => 'portal.free-online.net',
        'plus'       => 'portal.plus.net',
        'metronet'   => 'portal.metronet.co.uk',
        'madasafish' => 'madasafish.com',
        'care4free'  => 'care4free.net',
        'dialstart'  => 'dialstart.net',
        'easily'     => 'easilybroadband.co.uk',
        'fnn'        => 'freenetname.co.uk',
        'global'     => 'globalnet.co.uk',
        'totalise'   => 'totalise.co.uk',
        'callnetuk'  => 'callnetuk.com',
        'icscotland' => 'icscotland.net',
        'totalserve' => 'totalserve.co.uk',
        'ic24'       => 'ic24.net',
        'waitrose'   => 'waitrose.com',
        'greenbee'   => 'greenbee.net',
        'partner'    => 'partner.plus.net',
        'johnlewis'  => 'johnlewisbroadband.com',
        'vodafone'   => 'vodafoneathome.co.uk'
    );

    if (!isset($arrPortalName[$strPortalName])) {
        return false;
    }

    return $arrPortalName[$strPortalName];
}

// Library functions
/////////////////////////////////////////////////////////////////////

/////////////////////////////////////////////////////////////////////
// Configurator functions

/**
 * @param integer $component_id
 * @param string $portalname
 * @param string $user_type
 */
function config_portal_login_auto_configure($component_id, $portalname, $user_type = null)
{
    global $strPartnerBrand;

    $component = userdata_component_get($component_id);

    switch ($component['status']) {
        case 'unconfigured':
            // Needs configuring
            $portal_login_id = config_portal_login_add($component_id, $portalname, $user_type, $strPartnerBrand);

            userdata_component_set_configuration($component_id, $portal_login_id, '');
            userdata_component_set_status($component_id, 'active');
            break;

        case 'active':
        case 'deactive':
        case 'queued-reactivate':
        case 'queued-deactivate':
        case 'queued-activate':
        case 'queued-deconfigure':
        case 'queued-destroy':
        case 'destroyed':
            // These aren't the droids you're looking for. Move along.
            break;

        default:
            // The sky is falling!
            break;
    }
}

/**
 * @param integer $component_id
 * @param string $portalname
 */
function config_portal_login_auto_destroy($component_id, $portalname)
{
    $component = userdata_component_get($component_id);
    switch ($component['status']) {
        case 'unconfigured':
        case 'active':
        case 'deactive':
        case 'queued-reactivate':
        case 'queued-deactivate':
        case 'queued-activate':
        case 'queued-deconfigure':
        case 'queued-destroy':
            config_portal_login_delete($component['config_id'], $component['service_id']);
            userdata_component_set_status($component_id, 'destroyed');
            break;
        case 'destroyed':
            break;
        default:
            break;
    }
}

/**
 * @param integer $component_id
 * @param string $portalname
 * @param string $user_type
 */
function config_portal_login_auto_refresh($component_id, $portalname, $user_type = null)
{
    $component = userdata_component_get($component_id);

    switch ($component['status']) {
        case 'unconfigured':
        case 'destroyed':
            // Nothing to do here
            break;

        case 'active':
        case 'deactive':
        case 'queued-reactivate':
        case 'queued-activate':
            // Have or may have configuration
            $component['config_id'] = config_portal_login_add($component_id, $portalname);
            userdata_component_set_configuration($component_id, $component['config_id'], '');
            $component = userdata_component_get($component_id);

            if ($component['config_id'] == - 1) {
                $component['config_id'] = config_portal_login_add($component_id, $portalname, $user_type);
                userdata_component_set_configuration($component_id, $component['config_id'], '');
            }

            $portal_login = config_portal_login_get($component['component_id']);
            $service = userdata_service_get($component['service_id'], true);
            config_portal_login_set_password($component_id, $portalname, $portal_login, $service['password']);
            break;

        case 'queued-deconfigure':
        case 'queued-deactivate':
        case 'queued-destroy':
            // Shouldn't be here
            break;

        default:
            // The sky is falling!
            break;
    }
}


/**
 * @param integer $component_id
 * @param string $portalname
 */
function config_portal_login_auto_disable($component_id, $portalname)
{
    $component = userdata_component_get($component_id);

    switch ($component['status']) {
        case 'unconfigured':
        case 'destroyed':
        case 'deactive':
            // Nothing to do here
            break;

        case 'active':
            // Have or may have configuration
            //           $component     = userdata_component_get ($component_id);
            //            $portal_login = config_portal_login_get($component_id);
            //           $service       = userdata_service_get   ($component['service_id']);
            //
            //           config_portal_login_set_password($component_id, $portalname, $portal_login, '_SUSPENDED_');
            //           userdata_component_set_status   ($component_id, 'deactive');
            break;


        case 'queued-deconfigure':
        case 'queued-reactivate':
        case 'queued-deactivate':
        case 'queued-activate':
        case 'queued-destroy':
            // Shouldn't be here
            break;

        default:
            // The sky is falling!
            break;
    }
}

/**
 * @param integer $component_id
 * @param string $portalname
 */
function config_portal_login_auto_enable($component_id, $portalname)
{
    $component = userdata_component_get($component_id);

    switch ($component['status']) {
        case 'unconfigured':
        case 'destroyed':
        case 'active':
            // Nothing to do here
            break;

        case 'deactive':
            // Have or may have configuration
            $component = userdata_component_get($component_id);
            $portal_login = config_portal_login_get($component_id);
            $service = userdata_service_get($component['service_id']);
            config_portal_login_set_password($component_id, $portalname, $portal_login, $service['password']);
            userdata_component_set_status($component_id, 'active');
            break;

        case 'queued-deconfigure':
        case 'queued-reactivate':
        case 'queued-deactivate':
        case 'queued-activate':
        case 'queued-destroy':
            // Shouldn't be here
            break;

        default:
            // The sky is falling!
            break;
    }
}

function configure_portal_login_plusnet_configurator($component_id, $action)
{
    configure_portal_login_common_configurator_internal($component_id, $action, 'plus');
}

function configure_portal_login_freeonline_configurator($component_id, $action)
{
    configure_portal_login_common_configurator_internal($component_id, $action, 'freeonline');
}

function configure_portal_login_force9_configurator($component_id, $action)
{
    configure_portal_login_common_configurator_internal($component_id, $action, 'force9');
}

function configure_portal_login_plusnetuk_configurator($component_id, $action)
{
    configure_portal_login_common_configurator_internal($component_id, $action, 'plusnetuk');
}

function configure_portal_login_searchpro_configurator($component_id, $action)
{
    configure_portal_login_common_configurator_internal($component_id, $action, 'searchpro');
}

function configure_portal_login_dabsol_configurator($component_id, $action)
{
    configure_portal_login_common_configurator_internal($component_id, $action, 'dabsol');
}

function configure_portal_login_pnukreseller_configurator($component_id, $action)
{
    configure_portal_login_common_configurator_internal($component_id, $action, 'pnukreseller');
}

function configure_portal_login_spreseller_configurator($component_id, $action)
{
    configure_portal_login_common_configurator_internal($component_id, $action, 'spreseller');
}

function configure_portal_login_metronet_configurator($component_id, $action)
{
    configure_portal_login_common_configurator_internal($component_id, $action, 'metronet');
}

function configure_portal_login_madasafish_configurator($component_id, $action)
{
    $arrComponent = userdata_component_get($component_id);
    $arrService = userdata_service_get($arrComponent['service_id']);
    $strPortalName = $arrService['isp'];

    configure_portal_login_common_configurator_internal($component_id, $action, $strPortalName);
}

function configure_portal_login_waitrose_configurator($component_id, $action)
{
    configure_portal_login_common_configurator_internal($component_id, $action, 'waitrose');
}

function configure_portal_login_greenbee_configurator($component_id, $action)
{
    configure_portal_login_common_configurator_internal($component_id, $action, 'greenbee');
}
function configure_portal_login_partner_configurator($component_id, $action)
{
    $arrComponent = userdata_component_get($component_id);
    $arrService = userdata_service_get($arrComponent['service_id']);
    $strPortalName = $arrService['isp'];

    configure_portal_login_common_configurator_internal($component_id, $action, $strPortalName);
}

function configure_partner_enduser_configurator($component_id, $action)
{
    configure_portal_login_common_configurator_internal($component_id, $action, 'partner');
}

function configure_partner_admin_configurator($component_id, $action)
{
    switch ($action) {
        case 'auto_configure':

            config_portal_login_auto_configure($component_id, 'partner', 'PLUSNET_PARTNER_ADMIN_USER');
            break;

        case 'auto_disable':
            config_portal_login_auto_disable($component_id, 'partner');
            break;

        case 'auto_enable':
            config_portal_login_auto_enable($component_id, 'partner');
            break;

        case 'auto_destroy':
            config_portal_login_auto_destroy($component_id, 'partner');
            break;

        case 'auto_refresh':
            config_portal_login_auto_refresh($component_id, 'partner', 'PLUSNET_PARTNER_ADMIN_USER');
            break;

        default:
            break;
    }
}

function configure_portal_login_johnlewis_configurator($component_id, $action)
{
    $component = userdata_component_get($component_id);
    $service = userdata_service_get($component['service_id']);

    configure_portal_login_common_configurator_internal($component_id, $action, $service['isp']);
}

function configure_portal_login_vodafone_configurator($component_id, $action)
{
    configure_portal_login_common_configurator_internal($component_id, $action, 'vodafone');
}


/**
 * The following function should only be used by this file and shouldn't
 * be called from external code.
 **/

/* static */ function configure_portal_login_common_configurator_internal($component_id, $action, $portalName)
{
    switch ($action) {
        case 'auto_configure':
            config_portal_login_auto_configure($component_id, $portalName);
            break;

        case 'auto_disable':
            config_portal_login_auto_disable($component_id, $portalName);
            break;

        case 'auto_enable':
            config_portal_login_auto_enable($component_id, $portalName);
            break;

        case 'auto_destroy':
            config_portal_login_auto_destroy($component_id, $portalName);
            break;

        case 'auto_refresh':
            config_portal_login_auto_refresh($component_id, $portalName);
            break;

        default:
            break;
    }
}
// Configurator functions
/////////////////////////////////////////////////////////////////////
