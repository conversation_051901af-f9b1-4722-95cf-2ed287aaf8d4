server: itw
role: slave
rows: multiple
statement:

SELECT
		a.address_id,
		a.customer_id,
		a.house,
		a.street,
		a.town,
		a.county,
		a.postcode,
		a.country,
		a.status,
		at.vchHandle as strType<PERSON>andle,
		at.vchDisplayName as strTypeName
FROM
		dbRemoteInternet.addresses a
		LEFT JOIN dbRemoteInternet.tblAddressHasType aht
			ON (aht.intAddressId = a.address_id)
		LEFT JOIN dbRemoteInternet.tblAddressType at
			ON (at.intAddressTypeId = aht.intAddressTypeId)
WHERE
		address_id in (:arrAddressIds)
