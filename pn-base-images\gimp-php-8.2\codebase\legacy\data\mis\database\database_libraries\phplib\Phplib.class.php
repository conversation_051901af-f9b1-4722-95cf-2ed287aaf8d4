<?php
	class Lib_Phplib extends Util_LibrarySplitter
	{
		protected static $objInstance;

		protected function __construct()
		{
			$this->myName = __CLASS__;
			$this->myFile = __FILE__;
		}

		public static function singleton()
		{
			if (!isset(self::$objInstance))
			{
				$strClass = __CLASS__;
				self::$objInstance = new $strClass;
			}

			return self::$objInstance;
		} // public static function singleton()

		/**
		 * setInstance - Allow over-riding of the instance so we can mock it
		 *
		 * <AUTHOR> <<EMAIL>>
		 * @param Lib_SqlPrimitives $objInstance
		 * @static
		 * @access public
		 * @return void
		 */
		public static function setInstance(Lib_Phplib $objInstance)
		{
			self::$objInstance = $objInstance;
		}

		/**
		 * resetInstance - reset the instance - usually called from tearDown
		 *
		 * <AUTHOR> <<EMAIL>>
		 * @static
		 * @access public
		 * @return void
		 */
		public static function resetInstance()
		{
			self::$objInstance = null;
		}
	} // class Lib_Phplib extends Util_LibrarySplitter

?>
